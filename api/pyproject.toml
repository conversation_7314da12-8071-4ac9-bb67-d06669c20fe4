[tool.ruff]
# https://github.com/charliermarsh/ruff
fix = true
line-length = 120

[tool.ruff.lint]
select = [
	# Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I001",
    "B006",
    # flake8-tidy-imports (TID) banned-api
    "TID251",
    # flake8-logging-format (G) - migrated from flake8
    "G001",  # logging-string-format
    "G002",  # logging-percent-format
    "G003",  # logging-string-concat
    "G004",  # logging-f-string
    "G010",  # logging-warn
]
unfixable = [
    # Never try to fix "assigned to but never used"
    "F841"
]
ignore = [
    # Ignore the same rules that flake8 was ignoring
    "E203",  # whitespace before ':'
    "E231",  # missing whitespace after ','
    "E501",  # line too long (handled by formatter)
]
[tool.ruff.lint.flake8-tidy-imports.banned-api]
"django.contrib.postgres.fields.ArrayField".msg = "Avoid ArrayField; prefer model relationship instead. Arrays of scalar types (like strings) provide no foreign key integrity constraints and produce APIs that are difficult to extend without breaking backwards compatibility."
"django.core.management.base.BaseCommand".msg = "Use firefly.modules.firefly_django.management.commands.base.FireflyBaseCommand."
"django.db.models.CharField".msg = "Use django.db.models.TextField unless there is a known hard external constraint on character length. VARCHAR(n) has no performance advantage over TEXT in Postgres: https://www.postgresql.org/docs/current/datatype-character.html."
"django.db.models.DO_NOTHING".msg = "Avoid DO_NOTHING; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.SET".msg = "Avoid SET; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.SET_DEFAULT".msg = "Avoid SET_DEFAULT; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.SET_NULL".msg = "Avoid SET_NULL; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.deletion.DO_NOTHING".msg = "Avoid DO_NOTHING; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.deletion.SET".msg = "Avoid SET; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.deletion.SET_DEFAULT".msg = "Avoid SET_DEFAULT; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.deletion.SET_NULL".msg = "Avoid SET_NULL; prefer CASCADE, PROTECT, or RESTRICT instead."
"django.db.models.signals".msg = "Avoid Django model signals; prefer SaveHandlersMixin instead. See ADR 0006 for more information."
"django.db.transaction.atomic".msg = "Avoid atomic transactions, which can result in deadlocks and poor database performance. Consult with the broader Engineering team if you think you have a rare valid use case."
"fieldsignals".msg = "Avoid Django model signals; prefer SaveHandlersMixin instead. See ADR 0006 for more information."
"safedelete.signals".msg = "Avoid Django model signals; prefer SaveHandlersMixin instead. See ADR 0006 for more information."
[tool.ruff.lint.per-file-ignores]
# Ignore star imports from base settings
"**/firefly/settings/*" = ["F405"]
# Switching to isinstance causes tests to fail; do not make changes to this core logic
"**/firefly/modules/firefly_django/context.py" = ["E721"]
# forms/logic.py is deprecated and scary to touch
"**/firefly/modules/forms/logic.py" = ["E721"]
# Django generates migrations with long SQL command strings that are nontrivial to split.
# Generated migrations also cannot control whether banned APIs are used.
"**/migrations/*.py" = ["E501", "TID251"]
[tool.poetry]
name = "api"
version = "0.1.0"
description = ""
authors = ["Firefly Health"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
aiohttp = "3.11.18" # not directly required, pinned by Synk to avoid a vulnerability
amazon-textract-response-parser = "0.1.32"
authy = "2.2.1"
boto3 = "1.38.21"
boto3-stubs = {version = "1.34.136", extras = ["s3", "secretsmanager"]}
boxsdk = ">=3.3.0"
cbor2 = ">=5.6.2" # indirectly required by pubnub, pinned by Snyk to avoid a vulnerability
certifi = ">=2024.6.2" # has a min version requirement so that untrusted CAs can be removed from the chain
cryptography = "44.0.3"
datadog = "0.51.0"
ddtrace = "2.19.2"
django-cachalot = "2.6.2"
django-cors-headers = "3.14.0"
django-deprecate-fields = "0.1.0"
django-dirtyfields = "1.9.7"
django-dramatiq = "0.13.0"
django-extensions = "3.2.3"
django-fieldsignals = "0.7.0"
django-filter = "23.5"
django-imagekit = "5.0.0"
django-model-utils = "4.5.1"
django-pghistory = "3.2.0"
django-pgtrigger = "4.15.2"
django-redis = "5.4.0"
django-rest-elasticsearch = "0.4.1"
django-safedelete = "1.4.0"
django-s3-storage = "0.15.0"
django-waffle = "4.2.0"
django = "4.2.20"
djangorestframework = "3.15.2"
dnspython = "2.6.1" # used to find out the issuer of a request to infer the tenant
dramatiq = {version = "1.17.1", extras = ["rabbitmq"]}
drf-extra-fields = "3.7.0"
drf-jwt = "1.19.2"
drf-spectacular = "0.27.1"
elasticsearch-dsl = ">=6.0.0,<7.0.0"
elasticsearch = ">=6.0.0,<7.0.0"
geopy = "2.4.1"
google-api-python-client = "2.116.0"
google-auth-httplib2 = "0.2.0"
google-auth-oauthlib = "1.2.2"
html-sanitizer = "2.5.0"
importlib-metadata = "5.2.0"
jinja2 = "3.1.6"
json-log-formatter = "0.5.1"
jsonschema = "3.2.0"
looker-sdk = "22.20.0"
lxml = "5.4.0"
mistune = ">=2.0.1" # not directly required, pinned by Synk to avoid a vulnerability
mozilla-django-oidc = "4.0.1"
numpy = "1.26.4" # required by pandas. pin upper version (since Pandas does not)
oauthlib = "3.2.2"
openai = "1.16.1"
openpyxl = "3.1.3" # required by pandas to handle excel files
pandas = "1.5.3"
paramiko = "3.5.1"
pika = "1.3.2"
pillow = "10.3.0"
pubnub = "7.4.1"
pycryptodomex = "3.20.0"
pyjwt = "2.4.0"
python-crontab = "2.5.1"
python-dateutil = "2.9.0.post0"
python-gnupg = "0.5.4"
pyzipper = "0.3.6"
requests-oauthlib = "1.3.1"
requests = "2.32.2"
rest-framework-generic-relations = "2.2.0"
setproctitle = "1.3.2" # https://docs.datadoghq.com/integrations/gunicorn/#installation
shortuuid = "0.5.0"
snowflake-connector-python = "3.15.0"
transitions = "0.7.2"
twilio = "6.15.1"
whitenoise = "5.3.0" # Django doesn't support serving static files unless you're using the development server. We use whitenoise to host static files in deployments
xlrd = "2.0.1" # Required by pandas to handle old-style Excel files (.xls)
zeep = "3.4.0"
psycopg2-binary = "2.9.10"
rapidfuzz = "2.15.2"
legacy-drf-jwt = {path = "wheels/legacy_drf_jwt-1.19.2-py2.py3-none-any.whl"}

[tool.poetry.group.dev.dependencies]
vulture = "^2.7"
coverage = "5.0.3"
diff-cover = "2.6.0"
django-fake-model = "0.1.4"
django-migration-linter = "5.2.0"
factory-boy = "3.2.1"
freezegun = "0.3.15"
mock = "5.2.0"
pre-commit = "2.21.0"
pytest = "8.3.5"
pytest-django = "4.11.1"
pytest-socket = "0.7.0"
pytest-timeout = "2.3.1"
ruff = "0.11.11"
# Note: This version of django-stubs is compatible with a future version of
# Django (v5), despite us being on v4. This is to take advantage of some bug
# fixes in later versions. Be aware that some types may differ."
django-stubs = {version = "5.1.3", extras = ["compatible-mypy"]}
djangorestframework-stubs = "1.9.1"
types-mock = "5.2.0.20250516"
types-paramiko = "3.4.0.20240120"
types-python-dateutil = "2.9.0.20241206"
types-requests = "*********"
ipython = "8.11.0" # no directly required, pinned by Snyk to avoid a vulnerability
callee = "0.3.1"
setuptools = "70.3.0"
packaging = "22.0"
pygithub = "1.47"
werkzeug = "3.0.6"
watchdog = "3.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
