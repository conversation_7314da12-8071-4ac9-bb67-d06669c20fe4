# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield  # noqa: TID251
from django.db import models
from django_deprecate_fields import deprecate_field

from firefly.core.alias.models import AliasMapping
from firefly.core.user.models import Person
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.insurance.models import EmployerGroup
from firefly.modules.practice.models import ClaimProvider


# DEPRECATED: Moved to analytics pipeline
class Claim(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over Cha<PERSON><PERSON>ield
    block_of_business_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    group = deprecate_field(
        models.ForeignKey(
            EmployerGroup,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
            related_name="claims",
            null=True,
            blank=True,
        )
    )
    member = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(Person, on_delete=models.SET_NULL, related_name="claims", null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True, unique=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    secondary_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_status = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_suffix = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    adjudication_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    admission_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    paid_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    discharge_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    void_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    clean_claim_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    claim_created_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    deductible_year = deprecate_field(models.IntegerField(null=True, blank=True))
    plan_year = deprecate_field(models.IntegerField(null=True, blank=True))
    received_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharge_status = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_name = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    hra_amount = deprecate_field(models.FloatField(null=True, blank=True))
    provider = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(ClaimProvider, on_delete=models.SET_NULL, related_name="claims", null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    physician_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product_name = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    inpatient_outpatient_identifier = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_contract_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_name = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    waiver_id = deprecate_field(models.IntegerField(null=True, blank=True))


# DEPRECATED: Moved to analytics pipeline
class ClaimLine(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    block_of_business_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    group = deprecate_field(
        models.ForeignKey(
            EmployerGroup,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
            related_name="claimlines",
            null=True,
            blank=True,
        )
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    division_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    member = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(Person, on_delete=models.SET_NULL, related_name="claimlines", null=True, blank=True)  # noqa: TID251
    )
    # Once claim is no longer a FK, change this name to claim_id)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    clean_claim_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    secondary_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    claim_suffix = deprecate_field(models.IntegerField(null=True, blank=True))
    provider = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(ClaimProvider, on_delete=models.SET_NULL, related_name="claimlines", null=True, blank=True)  # noqa: TID251
    )
    adjudication_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    admission_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    discharge_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharge_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    physician_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_specialty_key = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type_code = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    charge_type_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    charge_type_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product = deprecate_field(models.CharField(max_length=10, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_area = deprecate_field(models.CharField(max_length=10, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_area_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    inpatient_outpatient_identifier = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_contract_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_id = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    form_type = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    change_time = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    physician_first_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    physician_last_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    physician_npi = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_address = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_city = deprecate_field(models.CharField(max_length=50, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_state = deprecate_field(models.CharField(max_length=5, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_zip = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_tin = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    clean_claim_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    void_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    fill_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    written_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    waiver_id = deprecate_field(models.IntegerField(null=True, blank=True))
    cycle_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rx_claim_id = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    rx_quantity = deprecate_field(models.FloatField(null=True, blank=True))
    rx_total_fills = deprecate_field(models.IntegerField(null=True, blank=True))
    rx_fill_number = deprecate_field(models.IntegerField(null=True, blank=True))
    rx_days_supply = deprecate_field(models.IntegerField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    daw_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    mail_order = deprecate_field(models.BooleanField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    brand_or_generic_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    formulary_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    specialty_rx = deprecate_field(models.BooleanField(null=True, blank=True))
    plan_year = deprecate_field(models.IntegerField(null=True, blank=True))
    deductible_year = deprecate_field(models.IntegerField(null=True, blank=True))
    vendor_created_date = deprecate_field(models.DateTimeField(null=True, blank=True))

    adjustment = deprecate_field(models.FloatField(null=True, blank=True))
    allowed = deprecate_field(models.FloatField(null=True, blank=True))
    disallowed = deprecate_field(models.FloatField(null=True, blank=True))
    hra_amount = deprecate_field(models.FloatField(null=True, blank=True))
    out_of_pocket = deprecate_field(models.FloatField(null=True, blank=True))
    paid_to_patient = deprecate_field(models.FloatField(null=True, blank=True))
    paid_to_provider = deprecate_field(models.FloatField(null=True, blank=True))
    patient_responsibility = deprecate_field(models.FloatField(null=True, blank=True))
    claim = deprecate_field(models.ForeignKey(Claim, on_delete=models.CASCADE, related_name="lines"))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    line_number = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    service_date_begin = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    service_date_end = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    length_of_stay = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    total_charge = deprecate_field(models.FloatField(null=True, blank=True))
    ineligible_amount = deprecate_field(models.FloatField(null=True, blank=True))
    discount_amount = deprecate_field(models.FloatField(null=True, blank=True))
    cob_savings = deprecate_field(models.FloatField(null=True, blank=True))
    copay = deprecate_field(models.FloatField(null=True, blank=True))
    deductible = deprecate_field(models.FloatField(null=True, blank=True))
    coinsurance = deprecate_field(models.FloatField(null=True, blank=True))
    plan_paid = deprecate_field(models.FloatField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    diagnosis_codes_primary = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    diagnosis_codes = deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, null=True, blank=True), null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    diagnosis_code_identifier = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_1 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_1 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    ineligible_description_text_1 = deprecate_field(models.TextField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_2 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_2 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    ineligible_description_text_2 = deprecate_field(models.TextField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_3 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_3 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    ineligible_description_text_3 = deprecate_field(models.TextField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_4 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_4 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    ineligible_description_text_4 = deprecate_field(models.TextField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_5 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_5 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    ineligible_description_text_5 = deprecate_field(models.TextField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    procedure_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    procedure_modifiers = deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, null=True, blank=True), null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    other_procedure_identifiers = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    revenue_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    place_of_service_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rx_ndc = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    received_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    paid_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))


# DEPRECATED: Moved to Analytics pipeline
class ClaimLineV2(BaseModelV3):
    # Variable names are Firefly generic names
    # Comments show what they map to in Vitori Claim.csv
    # Firefly generic name --> Vitori Claim.csv name

    adjudication_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # adjustment_amount --> total_adjustment_amount
    adjustment_amount = deprecate_field(models.FloatField(null=True, blank=True))
    admission_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # allowed_amount --> total_allowed
    allowed_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # billed_amount --> total_charge
    billed_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # bill_type --> bill_type_code
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bill_type_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    block_of_business_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    brand_or_generic_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # claim_line_number --> line_number
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_line_number = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # claim_number --> claim_id
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    claim_number = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # claim_paid_date --> paid_date
    claim_paid_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    claim_suffix = deprecate_field(models.IntegerField(null=True, blank=True))
    # claim_received_date --> received_date
    claim_received_date = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    # claim_synced_timestamp --> change_time
    claim_synced_timestamp = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    charge_type_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    charge_type_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    clean_claim_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # cob_amount --> cob_savings
    cob_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # coinsurance_amount --> coinsurance
    coinsurance_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # copay_amount --> copay
    copay_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_area = deprecate_field(models.CharField(max_length=10, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_area_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    cycle_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # date_from --> service_date_begin
    date_from = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    # date_to --> service_date_end
    date_to = deprecate_field(models.DateTimeField(max_length=255, null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    daw_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # deductible_amount --> deductible
    deductible_amount = deprecate_field(models.FloatField(null=True, blank=True))
    deductible_year = deprecate_field(models.IntegerField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    diagnosis_code_identifier = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    diagnosis_codes = deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, null=True, blank=True), null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    diagnosis_codes_primary = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # disallowed_amount --> disallowed
    disallowed_amount = deprecate_field(models.FloatField(null=True, blank=True))
    discharge_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharge_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    discount_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    division_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # drg --> drg_code
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    drg_description = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_address = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_city = deprecate_field(models.CharField(max_length=50, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_state = deprecate_field(models.CharField(max_length=5, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_tin = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # facility_zip_code --> facility_zip
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_zip_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    fill_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    form_type = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    formulary_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    group = deprecate_field(
        models.ForeignKey(
            EmployerGroup,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
            related_name="claim_lines",
            null=True,
            blank=True,
        )
    )
    hra_amount = deprecate_field(models.FloatField(null=True, blank=True))
    ineligible_amount = deprecate_field(models.FloatField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_1 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_2 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_3 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_4 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_code_5 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_1 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_2 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_3 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_4 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ineligible_description_5 = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    inpatient_outpatient_identifier = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    length_of_stay = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    mail_order = deprecate_field(models.BooleanField(null=True, blank=True))
    member = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(Person, on_delete=models.SET_NULL, related_name="claim_lines", null=True, blank=True)  # noqa: TID251
    )
    # modifier --> procedure_modifiers
    modifiers = deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, null=True, blank=True), null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_contract_code = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    other_procedure_identifiers = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # out_of_pocket_amount --> out_of_pocket
    out_of_pocket_amount = deprecate_field(models.FloatField(null=True, blank=True))
    paid_to_patient = deprecate_field(models.FloatField(null=True, blank=True))
    paid_to_provider = deprecate_field(models.FloatField(null=True, blank=True))
    patient_responsibility = deprecate_field(models.FloatField(null=True, blank=True))
    # payment_status --> claim_status
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payment_status = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # place_of_service --> place_of_service_code
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    place_of_service = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_id = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    plan_paid = deprecate_field(models.FloatField(null=True, blank=True))
    plan_year = deprecate_field(models.IntegerField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product = deprecate_field(models.CharField(max_length=10, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    product_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    provider = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(ClaimProvider, on_delete=models.SET_NULL, related_name="claim_lines", null=True, blank=True)  # noqa: TID251
    )
    # rendering_first_name --> physician_first_name
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rendering_first_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # rendering_id --> physician_id
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rendering_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # rendering_last_name --> physician_last_name
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rendering_last_name = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # rendering_npi --> physician_npi
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rendering_npi = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    # rendering_specialty --> provider_specialty_key
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rendering_specialty = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    revenue_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rx_claim_id = deprecate_field(models.CharField(max_length=25, null=True, blank=True))  # noqa: TID251
    rx_days_supply = deprecate_field(models.IntegerField(null=True, blank=True))
    rx_fill_number = deprecate_field(models.IntegerField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rx_ndc = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    rx_quantity = deprecate_field(models.FloatField(null=True, blank=True))
    rx_total_fills = deprecate_field(models.IntegerField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    secondary_id = deprecate_field(models.CharField(max_length=100, null=True, blank=True))  # noqa: TID251
    # service_code --> procedure_code
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    service_code = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251
    specialty_rx = deprecate_field(models.BooleanField(null=True, blank=True))
    # vendor_created_date --> created_date
    vendor_created_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    void_date = deprecate_field(models.DateTimeField(null=True, blank=True))
    # vendor_waiver_id --> waiver_id
    vendor_waiver_id = deprecate_field(models.IntegerField(null=True, blank=True))
    # Source of where the claim line came from
    vendor_source = deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=50, blank=False, null=False, choices=AliasMapping.ALIAS_NAME_CHOICES)  # noqa: TID251
    )
    written_date = deprecate_field(models.DateTimeField(null=True, blank=True))
