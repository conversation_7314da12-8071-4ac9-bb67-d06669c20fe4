from django.contrib.auth.models import Group
from django.contrib.postgres.fields.array import ArrayField
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models.fields.json import J<PERSON>NField
from django.utils import timezone
from django_fake_model import models as fake_model
from mock import patch
from pandas.tseries.offsets import BDay

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP, NP_ROLE
from firefly.core.user.factories import ProviderDetailFactory
from firefly.core.user.models import Person
from firefly.modules.statemachines.contants import StateMachineCustomFields, StateMachineSystemAction
from firefly.modules.statemachines.factories import (
    TestStateMachineModelFactory,
    TestWorkUnitStateMachineModelFactory,
    TestWorkUnitStateMachineWithContentModelFactory,
)
from firefly.modules.statemachines.mixin import StateMachineMixin, WorkUnitStateMachineMixin
from firefly.modules.statemachines.serializers import TestStateMachineSerializer, TestStateMachineWorkUnitSerializer
from firefly.modules.statemachines.utils import convert_date_to_end_of_working_est
from firefly.modules.statemachines.validate import validate_state_machine_content


class TestStateMachineMixin(FireflyTestCase):
    class MyFakeModelWithStateMachineMixin(fake_model.FakeModel, StateMachineMixin):
        states = ArrayField(
            JSONField(null=True, blank=False),
            blank=True,
            null=False,
            default=list,
        )

        state_transitions = ArrayField(
            JSONField(null=True, blank=False),
            blank=True,
            null=False,
            default=list,
        )

        # DO NOT COPY-PASTE: Prefer TextField over CharField
        initial_state = models.CharField(max_length=25, blank=False, null=False)  # noqa: TID251
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        initial_action = models.CharField(max_length=255, null=True, blank=False)  # noqa: TID251
        trigger_initial_action_manually = models.BooleanField(default=False)

        @property
        def state_machine_states(self):
            return self.states

        @property
        def state_machine_initial_state(self):
            return self.initial_state

        @property
        def state_machine_transitions(self):
            return self.state_transitions

        @property
        def state_machine_initial_action(self):
            return self.initial_action

        @property
        def state_machine_trigger_initial_action_manually(self):
            return self.trigger_initial_action_manually

    # update tests
    @MyFakeModelWithStateMachineMixin.fake_me
    def test_initial_action(self):
        # check if the initial_action method is triggered on create.
        with patch.object(
            self.MyFakeModelWithStateMachineMixin,
            "trigger_method",
            create=True,
        ) as trigger_method:
            self.MyFakeModelWithStateMachineMixin.objects.create(
                states=[
                    {"name": "new"},
                    {"name": "in_progress", "on_exit": ["update_bar_value_to_exit"]},
                    {"name": "complete"},
                    {"name": "deferred"},
                    {"name": "reopened"},
                ],
                state_transitions=[
                    {
                        "trigger": "initial",
                        "source": "new",
                        "dest": "new",
                        "before": "trigger_method",
                    },
                    {"trigger": "start", "source": "new", "dest": "in_progress"},
                    {
                        "trigger": "finish",
                        "source": "in_progress",
                        "dest": "complete",
                    },
                    {"trigger": "skip_to_complete", "source": "new", "dest": "complete"},
                    {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                    {
                        "trigger": "start_again",
                        "source": ["complete", "deferred"],
                        "dest": "reopened",
                        "before": "update_bar_value_to_bar",
                    },
                    {
                        "trigger": "start_again",
                        "source": "reopened",
                        "dest": ["in_progress", "complete", "deferred"],
                    },
                ],
                initial_state="new",
                initial_action="initial",
            )

            trigger_method.assert_called_once()

        # check if the initial_action method is not triggered on create.
        with patch.object(
            self.MyFakeModelWithStateMachineMixin,
            "trigger_method",
            create=True,
        ) as trigger_method:
            state_machine_item = self.MyFakeModelWithStateMachineMixin.objects.create(
                trigger_initial_action_manually=True,
                states=[
                    {"name": "new"},
                    {"name": "in_progress", "on_exit": ["update_bar_value_to_exit"]},
                    {"name": "complete"},
                    {"name": "deferred"},
                    {"name": "reopened"},
                ],
                state_transitions=[
                    {
                        "trigger": "initial",
                        "source": "new",
                        "dest": "new",
                        "before": "trigger_method",
                    },
                    {"trigger": "start", "source": "new", "dest": "in_progress"},
                    {
                        "trigger": "finish",
                        "source": "in_progress",
                        "dest": "complete",
                    },
                    {"trigger": "skip_to_complete", "source": "new", "dest": "complete"},
                    {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                    {
                        "trigger": "start_again",
                        "source": ["complete", "deferred"],
                        "dest": "reopened",
                        "before": "update_bar_value_to_bar",
                    },
                    {
                        "trigger": "start_again",
                        "source": "reopened",
                        "dest": ["in_progress", "complete", "deferred"],
                    },
                ],
                initial_state="new",
                initial_action="initial",
            )

            trigger_method.assert_not_called()

            state_machine_item.state_machine_trigger_initial_action()
            trigger_method.assert_called_once()

    def test_state_transitions(self):
        test_model = TestStateMachineModelFactory.create(
            states=[
                {"name": "new"},
                {"name": "in_progress", "on_exit": ["update_bar_value_to_exit"]},
                {"name": "complete"},
                {"name": "deferred"},
                {"name": "reopened"},
            ],
            state_transitions=[
                {"trigger": "start", "source": "new", "dest": "in_progress"},
                {
                    "trigger": "finish",
                    "source": "in_progress",
                    "dest": "complete",
                },
                {"trigger": "skip_to_complete", "source": "new", "dest": "complete"},
                {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                {
                    "trigger": "start_again",
                    "source": ["complete", "deferred"],
                    "dest": "reopened",
                    "before": "update_bar_value_to_bar",
                },
                {
                    "trigger": "start_again",
                    "source": "reopened",
                    "dest": ["in_progress", "complete", "deferred"],
                },
            ],
            initial_state="new",
        )
        self.assertEqual(test_model.status, "new")

        # Should update the status to in_progress
        test_model.action = "start"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "in_progress")
        self.assertEqual(test_model.action, "start")

        # Should update the status to complete
        test_model.action = "finish"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "complete")
        self.assertEqual(test_model.bar, "exit")
        self.assertEqual(test_model.action, "finish")

        # Pass an action that doesn't exist in the list
        test_model.action = "random"
        self.assertRaisesRegex(
            Exception,
            "Do not know event named 'random'.",
            lambda: test_model.save(),
        )

        # Pass an invalid action
        test_model.action = "start"
        self.assertRaisesRegex(
            Exception,
            "Can't trigger event start from state complete!",
            lambda: test_model.save(),
        )

        # Should update the status to reopened
        test_model.action = "start_again"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "reopened")
        self.assertEqual(test_model.bar, "bar")
        self.assertEqual(test_model.action, "start_again")

    def test_actions(self):
        test_model = TestStateMachineModelFactory.create(
            states=[
                {"name": "new"},
                {"name": "in_progress"},
                {"name": "complete"},
                {"name": "deferred"},
                {"name": "reopened"},
            ],
            state_transitions=[
                {
                    "trigger": "start",
                    "source": "new",
                    "dest": "in_progress",
                    StateMachineCustomFields.DESCRIPTIONS: ["New desc"],
                    "before": "update_bar_value_to_exit",
                },
                {
                    "trigger": "finish",
                    "source": "in_progress",
                    StateMachineCustomFields.DESCRIPTIONS: ["Action 1 desc", "Action 2 desc"],
                    "before": ["action_1", "action_2"],
                    "dest": "complete",
                },
                {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                {
                    "trigger": "start_again",
                    "source": ["complete", "deferred"],
                    "dest": "reopened",
                    "before": "update_bar_value_to_bar",
                },
                {
                    "trigger": "start_again",
                    "source": "reopened",
                    "dest": ["in_progress", "complete", "deferred"],
                },
            ],
            initial_state="new",
        )

        serializer = TestStateMachineSerializer(test_model)
        test_model_data = serializer.data
        actions = test_model_data.get("actions")
        self.assertEqual(len(actions), 1)

        action = actions[0]
        self.assertEqual(action["dest"], "in_progress")
        self.assertEqual(action["trigger"], "start")
        self.assertEqual(action["source"], "new")
        self.assertEqual(action["dest"], "in_progress")
        self.assertEqual(action[StateMachineCustomFields.DESCRIPTIONS], ["New desc"])
        self.assertEqual(action["before"], "update_bar_value_to_exit")

        TestStateMachineSerializer().update(test_model, {"action": "start"})

        serializer = TestStateMachineSerializer(test_model)
        test_model_data = serializer.data

        actions = test_model_data.get("actions")
        self.assertEqual(len(actions), 2)


# When working with a fake model with a person or an owner group,
# we should use the decorator on the class instead of the function
# for effective rollback because of pending triggers
class MyFakeModelWithOwnerWorkUnitStateMachineMixin(fake_model.FakeModel, WorkUnitStateMachineMixin):
    person = models.ForeignKey(
        Person,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
    )

    owner_group = models.ForeignKey(
        "user.AssigneeGroup",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        related_name="+",
    )

    @property
    def subject(self):
        return self.person


@MyFakeModelWithOwnerWorkUnitStateMachineMixin.fake_me
class TestWorkUnitStateMachineMixinWithOwner(FireflyTestCase):
    def test_auto_configure_owner(self):
        from firefly.core.user.models import AssigneeGroup

        billing_owner_group, _ = AssigneeGroup.objects.get_or_create(
            unique_key=ASSIGNEE_GROUP_UNIQUE_KEY_MAP["billing_and_insurance"],
        )
        test_model = MyFakeModelWithOwnerWorkUnitStateMachineMixin.objects.create(
            state_machine_content={
                "state_with_categories": [
                    {
                        "state": {"name": "new"},
                        "category": "not_started",
                        "owner": {"assignee_group_key": "billing_and_insurance"},
                    },
                    {
                        "state": {"name": "outreach 1"},
                        "category": "in_progress",
                        "owner": {"patient": True},
                    },
                    {
                        "state": {"name": "outreach 2"},
                        "category": "in_progress",
                        "owner": {"role": NP_ROLE},
                    },
                    {"state": {"name": "done"}, "category": "complete"},
                    {"state": {"name": "deferred"}, "category": "deferred"},
                ],
                "transitions": [
                    {"trigger": "start", "source": "new", "dest": "outreach 1"},
                    {"trigger": "outreach 2", "source": "outreach 1", "dest": "outreach 2"},
                    {"trigger": "finish", "source": ["outreach 1", "outreach 2"], "dest": "done"},
                    {"trigger": "skip_to_complete", "source": "new", "dest": "done"},
                    {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                ],
                "initial_state": "new",
            },
        )
        test_model.person = self.patient.person
        np_group, _ = Group.objects.get_or_create(name=NP_ROLE)
        np_provider = ProviderDetailFactory()
        np_group.user_set.add(np_provider.user)
        self.patient.person.care_team.set([np_provider.user.id, ProviderDetailFactory().user.id])
        self.assertEqual(test_model.status, "new")
        self.assertEqual(test_model.owner_group, billing_owner_group)

        # Change the status outreach 1, should update the owner to current patient
        test_model.action = "start"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "outreach 1")
        self.assertEqual(test_model.owner_group, self.patient.assignee_group)

        # Change the status outreach 2, should update the owner to billing and insurance
        test_model.action = "outreach 2"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "outreach 2")
        self.assertEqual(test_model.owner_group, np_provider.user.assignee_group)


class TestWorkUnitStateMachineMixin(FireflyTestCase):
    class MyFakeModelWithWorkUnitStateMachineMixin(fake_model.FakeModel, WorkUnitStateMachineMixin):
        def update_bar_value_to_exit(self, *args, **kwargs):
            self.bar = "exit"

        def update_bar_value_to_bar(self, *args, **kwargs):
            self.bar = "bar"

        def update_bar_value_to_hello(self, *args, **kwargs):
            self.bar = "hello"

    def test_state_transitions_wo_state_machine_content(self):
        test_model = TestWorkUnitStateMachineModelFactory.create(
            states_with_categories=[
                {"state": {"name": "new"}, "category": "not_started"},
                {
                    "state": {"name": "in_progress", "on_exit": ["update_bar_value_to_exit"]},
                    "category": "in_progress",
                },
                {"state": {"name": "done"}, "category": "complete"},
                {"state": {"name": "deferred"}, "category": "deferred"},
            ],
            state_transitions=[
                {
                    "trigger": "start",
                    "source": "new",
                    "dest": "in_progress",
                    "after": "update_bar_value_to_bar",
                },
                {"trigger": "finish", "source": "in_progress", "dest": "done"},
                {"trigger": "skip_to_complete", "source": "new", "dest": "done"},
                {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
            ],
            initial_state="new",
        )
        self.assertEqual(test_model.status, "new")
        self.assertEqual(test_model.status_category, "not_started")

        # Should update the status to in_progress
        test_model.action = "start"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "in_progress")
        self.assertEqual(test_model.status_category, "in_progress")
        self.assertEqual(test_model.bar, "bar")

        # Should update the status to done
        test_model.action = "finish"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "done")
        self.assertEqual(test_model.status_category, "complete")
        self.assertEqual(test_model.bar, "exit")

        # Pass an invalid action
        test_model.action = "start"
        self.assertRaisesRegex(
            Exception,
            "Can't trigger event start from state done!",
            lambda: test_model.save(),
        )

    @MyFakeModelWithWorkUnitStateMachineMixin.fake_me
    def test_state_transitions_with_state_machine_content(self):
        test_model = self.MyFakeModelWithWorkUnitStateMachineMixin.objects.create(
            state_machine_content={
                "state_with_categories": [
                    {"state": {"name": "new"}, "category": "not_started"},
                    {
                        "state": {"name": "in_progress", "on_exit": ["update_bar_value_to_exit"]},
                        "category": "in_progress",
                    },
                    {"state": {"name": "done"}, "category": "complete"},
                    {"state": {"name": "deferred"}, "category": "deferred"},
                ],
                "transitions": [
                    {
                        "trigger": "initial",
                        "source": "new",
                        "dest": "new",
                        "after": "update_bar_value_to_hello",
                    },
                    {
                        "trigger": "start",
                        "source": "new",
                        "dest": "in_progress",
                        "after": "update_bar_value_to_bar",
                    },
                    {"trigger": "finish", "source": "in_progress", "dest": "done"},
                    {"trigger": "skip_to_complete", "source": "new", "dest": "done"},
                    {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                ],
                "initial_state": "new",
                "initial_action": "initial",
            }
        )
        self.assertEqual(test_model.status, "new")
        self.assertEqual(test_model.status_category, "not_started")
        self.assertEqual(test_model.bar, "hello")

        # Should update the status to in_progress
        test_model.action = "start"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "in_progress")
        self.assertEqual(test_model.status_category, "in_progress")
        self.assertEqual(test_model.bar, "bar")

        # Should update the status to done
        test_model.action = "finish"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "done")
        self.assertEqual(test_model.status_category, "complete")
        self.assertEqual(test_model.bar, "exit")

        # Pass an invalid action
        test_model.action = "start"
        self.assertRaisesRegex(
            Exception,
            "Can't trigger event start from state done!",
            lambda: test_model.save(),
        )

    class MyFakeWorkUnitModelWithWorkUnitStateMachineMixin(fake_model.FakeModel, WorkUnitStateMachineMixin):
        due_date = models.DateTimeField(null=True, blank=True, db_index=True)

    @MyFakeWorkUnitModelWithWorkUnitStateMachineMixin.fake_me
    def test_auto_calculate_due_date(self):
        test_model = self.MyFakeWorkUnitModelWithWorkUnitStateMachineMixin.objects.create(
            state_machine_content={
                "state_with_categories": [
                    {
                        "state": {"name": "new"},
                        "category": "not_started",
                        "due_date": [{"days": 1}],
                    },
                    {
                        "state": {"name": "outreach 1"},
                        "category": "in_progress",
                        "due_date": [{"days": 10}],
                    },
                    {
                        "state": {"name": "outreach 2"},
                        "category": "in_progress",
                        "due_date": [{"days": 7, "use_business_days": True}],
                    },
                    {"state": {"name": "done"}, "category": "complete"},
                    {"state": {"name": "deferred"}, "category": "deferred"},
                ],
                "transitions": [
                    {"trigger": "start", "source": "new", "dest": "outreach 1"},
                    {"trigger": "outreach 2", "source": "outreach 1", "dest": "outreach 2"},
                    {"trigger": "finish", "source": ["outreach 1", "outreach 2"], "dest": "done"},
                    {"trigger": "skip_to_complete", "source": "new", "dest": "done"},
                    {"trigger": "failed", "source": "in_progress", "dest": "deferred"},
                ],
                "initial_state": "new",
            },
        )
        self.assertEqual(test_model.status, "new")
        self.assertEqual(
            test_model.due_date,
            convert_date_to_end_of_working_est((timezone.now() + timezone.timedelta(days=1)).date()),
        )

        # Change the status outreach 1, should update the due date to T + 10 days
        test_model.action = "start"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "outreach 1")
        self.assertEqual(
            test_model.due_date,
            convert_date_to_end_of_working_est((timezone.now() + timezone.timedelta(days=10)).date()),
        )

        # Change the status outreach 2, should update the due date to T + 7 business days
        test_model.action = "outreach 2"
        test_model.save()
        test_model.refresh_from_db()
        self.assertEqual(test_model.status, "outreach 2")
        self.assertEqual(test_model.due_date, convert_date_to_end_of_working_est((timezone.now() + BDay(7)).date()))

    def test_serializer_system_actions(self):
        test_model = TestWorkUnitStateMachineWithContentModelFactory.create(
            state_machine_content={
                "state_with_categories": [
                    {
                        "state": {"name": "new"},
                        "category": "not_started",
                    },
                    {
                        "state": {"name": "in progress"},
                        "category": "in_progress",
                    },
                    {
                        "state": {"name": "reopened"},
                        "category": "in_progress",
                    },
                    {"state": {"name": "done"}, "category": "complete"},
                ],
                "transitions": [
                    {"trigger": "in progress", "source": "new", "dest": "in progress"},
                    {"trigger": "done", "source": "in progress", "dest": "done"},
                    {"trigger": "reopened", "source": "*", "dest": "reopened"},
                ],
                "initial_state": "new",
            }
        )
        serializer = TestStateMachineWorkUnitSerializer(test_model)
        test_model_data = serializer.data
        actions = test_model_data.get("actions")
        self.assertEqual(len(actions), 2)
        for action in actions:
            self.assertIn(action["trigger"], ["in progress", "reopened"])

        # System actions shouldn't showup as an available action
        test_model.state_machine_content = {
            "state_with_categories": [
                {
                    "state": {"name": "new"},
                    "category": "not_started",
                },
                {
                    "state": {"name": "in progress"},
                    "category": "in_progress",
                },
                {
                    "state": {"name": "reopened"},
                    "category": "in_progress",
                },
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "transitions": [
                {"trigger": "in progress", "source": "new", "dest": "in progress"},
                {"trigger": "done", "source": "in progress", "dest": "done"},
                {
                    "trigger": "reopened",
                    "source": "*",
                    "dest": "reopened",
                    "system_action": StateMachineSystemAction.REOPEN,
                },
            ],
            "initial_state": "new",
        }
        test_model.save()
        serializer = TestStateMachineWorkUnitSerializer(test_model)
        test_model_data = serializer.data
        actions = test_model_data.get("actions")
        self.assertEqual(len(actions), 1)
        action = actions[0]
        self.assertEqual(action["trigger"], "in progress")


class ValidateStateMachineContentTestCase(FireflyTestCase):
    def test_validate_required_fields(self):
        state_machine_content = {}
        self.assertRaisesRegex(
            ValidationError,
            "state_with_categories is required",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
            ]
        }
        self.assertRaisesRegex(
            ValidationError,
            "'initial_state' is required",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
        }
        self.assertRaisesRegex(
            ValidationError,
            "'transitions' is required",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        # There shouldn't be any errors
        validate_state_machine_content(state_machine_content)

    def test_validate_state_with_categories(self):
        state_machine_content = {
            "state_with_categories": [{}],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'state' is required",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [{"state": {}}],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'state' - 'name' is required",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [{"state": {"name": "new"}}],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'category' is required",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [{"state": {"name": "new"}, "category": "not_started1"}],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'category' value of not_started1",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "new"}, "category": "not_started"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "states must be unique. 'new' is used more than once",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "title with more than 25 characters"}, "category": "not_started"},
                {"state": {"name": "new"}, "category": "not_started"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "title with more than 25 characters"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            (
                "'state_with_categories' - 'state' - 'name' value of 'title with more than 25 characters'"
                " must be less than 25 characters"
            ),
            lambda: validate_state_machine_content(state_machine_content),
        )

    def test_validate_state_with_categories_owner(self):
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - Only 1 of the keys",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"garbage": "value"}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - Only 1 of the keys",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"role": "role.NP", "patient": True}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - Only 1 of the keys",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"assignee_group_key": 12}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - 'assignee_group_key' should be of type str",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"assignee_group_key": ""}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - 'assignee_group_key' should not be blank",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"role": 12}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - 'role' should be of type str",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"role": ""}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - 'role' should not be blank",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"patient": 1}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'state_with_categories' - 'owner' - 'patient' should be of type bool",
            lambda: validate_state_machine_content(state_machine_content),
        )
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started", "owner": {"role": "role.NP"}},
                {"state": {"name": "done"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "done", "source": "new", "dest": "done"},
            ],
        }
        # Passes without any issues
        validate_state_machine_content(state_machine_content)

    def test_validate_system_actions(self):
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
                {"state": {"name": "reopen"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "reopen", "source": "done", "dest": "reopen"},
                {"trigger": "done", "source": "new", "dest": "done"},
                {"trigger": "auto_close", "source": "*", "dest": "done", "system_action": "auto_close1"},
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'system_action' - auto_close1 not a valid system action.",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
                {"state": {"name": "reopen"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "reopen", "source": "done", "dest": "reopen"},
                {"trigger": "done", "source": "new", "dest": "done", "system_action": StateMachineSystemAction.CLOSE},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            f"'system_action' - multiple actions cannot have the same"
            f" system_action: '{StateMachineSystemAction.CLOSE}'.",
            lambda: validate_state_machine_content(state_machine_content),
        )

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
                {"state": {"name": "reopen"}, "category": "complete"},
                {"state": {"name": "Will Not Do"}, "category": "deferred"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "reopen", "source": "done", "dest": "reopen"},
                {"trigger": "done", "source": "new", "dest": "done"},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
                {
                    "trigger": "defereed",
                    "source": "*",
                    "dest": "Will Not Do",
                    "system_action": StateMachineSystemAction.DEFERRED,
                },
            ],
        }
        validate_state_machine_content(state_machine_content)

        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "new"}, "category": "not_started"},
                {"state": {"name": "done"}, "category": "complete"},
                {"state": {"name": "reopen"}, "category": "complete"},
            ],
            "initial_state": "new",
            "transitions": [
                {"trigger": "initial", "source": "new", "dest": "new"},
                {"trigger": "reopen", "source": "done", "dest": "reopen"},
                {
                    "trigger": "done",
                    "source": "new",
                    "dest": "done",
                    "system_action": "title with more than 25 characters",
                },
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
            ],
        }
        self.assertRaisesRegex(
            ValidationError,
            "'system_action' value of 'title with more than 25 characters' must be less than 25 characters",
            lambda: validate_state_machine_content(state_machine_content),
        )
