import logging

from django.core.exceptions import ValidationError
from transitions.core import Machine

from firefly.modules.statemachines.contants import STATE_MACHINE_SYSTEM_ACTIONS
from firefly.modules.statemachines.utils import get_transitions_without_custom_fields
from firefly.modules.work_units.constants import STATUS_CATEGORY_CHOICES

logger = logging.getLogger(__name__)


def validate_state_machine_content(state_machine_content):
    if state_machine_content is not None:
        if (
            state_machine_content.get("state_with_categories") is None
            or len(state_machine_content.get("state_with_categories")) == 0
        ):
            raise ValidationError([{"message": "state_with_categories is required"}])

        if state_machine_content.get("initial_state") is None or state_machine_content.get("initial_state") == "":
            raise ValidationError([{"message": "'initial_state' is required"}])

        if state_machine_content.get("transitions") is None or len(state_machine_content.get("transitions")) == 0:
            raise ValidationError([{"message": "'transitions' is required"}])

        state_with_categories = state_machine_content.get("state_with_categories")
        validate_state_with_categories(state_with_categories)

        states = [state_with_category.get("state") for state_with_category in state_with_categories]
        initial_status = state_machine_content.get("initial_state")
        state_names = [state.get("name") for state in states]
        if initial_status not in state_names:
            raise ValidationError(
                [
                    {
                        "message": (
                            f"'initial_state' value of {initial_status} must be in states.Options are {state_names}"
                        )
                    }
                ]
            )

        validate_state_machine_fields(states, state_machine_content.get("transitions"))

        validate_system_actions(state_machine_content.get("transitions"))


def validate_state_with_categories(state_with_categories):
    categories = [category[0] for category in STATUS_CATEGORY_CHOICES]
    states = []
    for state_with_category in state_with_categories:
        if state_with_category.get("state") is None:
            raise ValidationError([{"message": "'state_with_categories' - 'state' is required"}])

        state = state_with_category.get("state")
        if state.get("name") is None or state.get("name") == "":
            raise ValidationError([{"message": "'state_with_categories' - 'state' - 'name' is required"}])
        state_name = state.get("name")
        if len(state_name) > 25:
            raise ValidationError(
                [
                    {
                        "message": (
                            f"'state_with_categories' - 'state' - 'name' value of '{state_name}'"
                            f" must be less than 25 characters"
                        )
                    }
                ]
            )
        if state_name in states:
            state_name = state.get("name")
            raise ValidationError([{"message": f"states must be unique. '{state_name}' is used more than once"}])
        states.append(state_name)

        if state_with_category.get("category") is None:
            raise ValidationError([{"message": "'state_with_categories' - 'category' is required"}])

        category = state_with_category.get("category")

        if category not in categories:
            raise ValidationError(
                [
                    {
                        "message": (
                            f"'state_with_categories' - 'category' value of {category}"
                            f" must be in STATUS_CATEGORY_CHOICES. Options are {categories}"
                        )
                    }
                ]
            )
        due_date = state_with_category.get("due_date")
        if due_date is not None:
            for due_date_meta in due_date:
                validate_state_due_date_meta(due_date_meta)
        owner_meta = state_with_category.get("owner")
        if owner_meta is not None:
            validate_state_owner_meta(owner_meta)


def validate_state_machine_fields(states, transitions):
    # Pytransitions doesn't have any validation functions that we could use.
    # So initiaing the component with states and transitions to get the errors.
    try:
        Machine(states=states, transitions=get_transitions_without_custom_fields(transitions))
    except Exception as error:
        raise ValidationError([{"message": error}])


def validate_state_due_date_meta(due_date_meta):
    days = due_date_meta.get("days")
    if days is None:
        raise ValidationError([{"message": "'state_with_categories' - 'due_date' - 'days' is required"}])

    if not isinstance(days, int):
        raise ValidationError([{"message": "'state_with_categories' - 'due_date' - 'days' should be of type int"}])
    use_business_days = due_date_meta.get("use_business_days")
    if use_business_days is not None and not isinstance(use_business_days, bool):
        raise ValidationError(
            [{"message": "'state_with_categories' - 'due_date' - 'use_business_days' should be of type bool"}]
        )


def validate_state_owner_meta(owner_meta):
    num_keys = len(set(owner_meta.keys()).intersection(set(["assignee_group_key", "role", "patient"])))
    if num_keys != 1:
        raise ValidationError(
            [
                {
                    "message": "'state_with_categories' - 'owner' - Only 1 of the keys"
                    + " ('assignee_group_key', 'role', 'patient') should be present"
                }
            ]
        )

    assignee_group = owner_meta.get("assignee_group_key")
    if assignee_group is not None and not isinstance(assignee_group, str):
        raise ValidationError(
            [{"message": "'state_with_categories' - 'owner' - 'assignee_group_key' should be of type str"}]
        )
    if assignee_group is not None and isinstance(assignee_group, str) and assignee_group == "":
        raise ValidationError(
            [{"message": "'state_with_categories' - 'owner' - 'assignee_group_key' should not be blank"}]
        )
    role = owner_meta.get("role")
    if role is not None and not isinstance(role, str):
        raise ValidationError([{"message": "'state_with_categories' - 'owner' - 'role' should be of type str"}])
    if role is not None and isinstance(role, str) and role == "":
        raise ValidationError([{"message": "'state_with_categories' - 'owner' - 'role' should not be blank"}])
    patient = owner_meta.get("patient")
    if patient is not None and not isinstance(patient, bool):
        raise ValidationError([{"message": "'state_with_categories' - 'owner' - 'patient' should be of type bool"}])


def validate_system_actions(transitions):
    used_system_actions = []
    for transition in transitions:
        if not transition.get("system_action"):
            continue

        system_action = transition.get("system_action")
        if len(system_action) > 25:
            raise ValidationError(
                [{"message": (f"'system_action' value of '{system_action}' must be less than 25 characters")}]
            )
        if system_action not in STATE_MACHINE_SYSTEM_ACTIONS:
            raise ValidationError(
                [
                    {
                        "message": (
                            f"'system_action' - {system_action} not a valid system action."
                            f" Should be one of {STATE_MACHINE_SYSTEM_ACTIONS}"
                        )
                    }
                ]
            )
        if system_action in used_system_actions:
            raise ValidationError(
                [
                    {
                        "message": (
                            f"'system_action' - multiple actions cannot have the same system_action: '{system_action}'."
                        )
                    }
                ]
            )
        used_system_actions.append(system_action)
