import logging

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.core.validators import RegexValidator
from django.db import models

from firefly.core.user.models import Person
from firefly.modules.firefly_django.models import BaseModelV3

logger = logging.getLogger(__name__)

PHONE_CALL_DIRECTION_CHOICES = [
    ("INBOUND", "INBOUND"),
    ("OUTBOUND", "OUTBOUND"),
]


class PhoneCallDisposition(BaseModelV3):
    """
    Represents type of phone call disposition.
    """

    # Display name for the call disposition.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(unique=True, max_length=255)  # noqa: TID251

    # If true, phone calls with this disposition are considered "actionable" and
    # should have an associated Case.
    is_actionable = models.BooleanField(blank=True, null=True)

    def __str__(self):
        return f"CallDisposition({self.id}) - {self.title}"


class PhoneCall(BaseModelV3):
    # INTENT TO DEPRECATE: This field will be replaced by person.
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="phoneCall",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    # Subject of the phone calls, usually the patient, member, or prospect who made or received the call.
    #
    # For now, this will be kept in sync with user. Consumers should continue to use the user field
    # until it is fully deprecated.
    person = models.ForeignKey(
        Person,
        related_name="phoneCall",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        null=True,
        blank=True,
    )

    # Phone call direction (Inbound/Outbound)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    direction = models.CharField(max_length=10, choices=PHONE_CALL_DIRECTION_CHOICES)  # noqa: TID251

    # Datetime of the call
    called_at = models.DateTimeField()

    # phone call disposition.
    disposition = models.ForeignKey(
        PhoneCallDisposition,
        on_delete=models.RESTRICT,
        related_name="phone_calls",
        blank=True,
        null=True,
    )

    # Related cases tied to the phone call.
    case_relations = GenericRelation("cases.CaseRelation")

    # Datetime of the call ended
    call_ended_at = models.DateTimeField(blank=True, null=True)

    # External call Id tied to phone call. Currently captures data specific to Talkdesk.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    external_call_id = models.CharField(max_length=100, blank=True, null=True, unique=True, db_index=True)  # noqa: TID251

    phone_regex = RegexValidator(regex=r"^\+?1?\d{9,15}$", message="Phone number format: '+1115559999'.")
    # phone number - this would be used to link phone calls later for cases where user is not identified
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    contact_phone_number = models.CharField(  # noqa: TID251
        validators=[phone_regex], max_length=17, unique=False, null=True, blank=True
    )

    handled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="handled_phone_calls",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_phone_number = models.CharField(  # noqa: TID251
        validators=[phone_regex], max_length=17, unique=False, null=True, blank=True
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    call_type = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_phone_display_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_user_id = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_user_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_user_email = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    total_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talk_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    wait_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    hold_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    abandon_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    total_ringing_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    notes = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    user_voice_rating = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ring_groups = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ivr_options = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    is_in_business_hours = models.BooleanField(blank=True, null=True)
    is_callback_from_queue = models.BooleanField(blank=True, null=True)
    is_transfer = models.BooleanField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    handling_user_id = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    handling_user_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    handling_user_email = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    recording_url = models.CharField(max_length=200, blank=True, null=True)  # noqa: TID251
    is_external_transfer = models.BooleanField(blank=True, null=True)
    is_if_no_answer = models.BooleanField(blank=True, null=True)
    is_call_forwarding = models.BooleanField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    csat_score = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    csat_survey_time = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    mood = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    is_mood_prompted = models.BooleanField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    team_id = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    team_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rating_reason = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    contacts = models.JSONField(blank=True, null=True)
    ring_attempts = models.JSONField(blank=True, null=True)

    def save(self, *args, **kwargs):
        if self.person:
            person = self.person
            self.user = None
            if person.user is not None:
                self.user = person.user
        else:
            self.user = None

        super(PhoneCall, self).save(*args, **kwargs)


class RingAttempt(BaseModelV3):
    # Interaction ID is through the AliasMapping to PhoneCall
    phone_call = models.ForeignKey(
        PhoneCall,
        related_name="ringAttempt",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_contact_id = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_batch_ring_id = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # Attempt ID is in the AliasMapping to RingAttempt
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_attempt_type = models.CharField(max_length=50, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    started_at_time = models.CharField(max_length=50, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ring_started_at_time = models.CharField(max_length=50, blank=True, null=True)  # noqa: TID251
    ring_attempt_duration = models.IntegerField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_user_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_user_email = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    talkdesk_team_name = models.CharField(max_length=100, blank=True, null=True)  # noqa: TID251
