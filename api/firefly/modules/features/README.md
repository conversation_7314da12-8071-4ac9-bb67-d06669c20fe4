# Features

## Motivation

The features module provides a flexible, rule-based system for controlling application functionality based on tenant configuration and user state. It enables tenant-specific customization, and state-dependent feature access while maintaining a consistent user experience across different contexts.

## Business Context

Feature flags serve as the primary mechanism for controlling application behavior across different tenants and user states. They enable:
- Tenant-specific feature customization for different client contracts
- Progressive feature rollouts based on user onboarding state
- Insurance coverage-dependent functionality
- Program enrollment-based feature access

## Core Concepts

### Feature Access Hierarchy
- **Tenant-Level Features**: Global on/off switches for entire tenant organizations
- **Program-Based Features**: Features enabled based on user's program enrollment
- **State-Dependent Features**: Features that depend on user's current state (onboarding, insurance, etc.)
- **Combined Logic**: Features that require both tenant enablement and user state conditions

### Feature Types
1. **Binary Features**: Simple on/off functionality (e.g., appointment booking)
2. **Conditional Features**: Features with complex state-based rules
3. **Progressive Features**: Features that unlock as users progress through onboarding
4. **Tenant-Specific Features**: Custom functionality for specific client contracts

## Technical Implementation

### Core Components

**Features Enum**: Central registry of all available features
- Defined in `constants.py` with descriptive names
- Used consistently across backend and frontend
- Provides type safety and autocomplete support

**ProgramFeatureCalculator**: Main business logic engine
- Evaluates feature access based on user state and tenant configuration
- Processes rule sets from JSON configuration
- Returns boolean feature access map for users

**Tenant Configuration**: Simple boolean mapping for tenant-level features
- Stored in `Tenant.feature_access_config` JSON field
- Configurable through Django admin interface
- Overrides program-based rules when disabled

### Rule-Based Configuration

**Program Feature Config**: JSON-based rule system in `program_feature_config.json`
- Defines feature access rules for each program
- Supports complex conditional logic based on user state
- Rule order matters - first matching rule determines access

**Rule Structure**:
```json
{
  "onboarding_state": ["member", "enrolled"],
  "insurance_coverage_state": ["active"],
  "value": true
}
```

**Condition Types**:
- `onboarding_state`: User's progress through onboarding process
- `insurance_coverage_state`: Current insurance coverage status
- `value`: Boolean result when conditions are met

### Frontend Integration

**Patient App**: `useFeatureAccess` hook and `BehindPatientFeatureAccessCheck` component
- Real-time feature access evaluation
- Conditional rendering based on feature availability
- Graceful degradation for disabled features

**Lucian (Provider App)**: `useFeatureAccess` hook and `BehindProviderFeatureAccessCheck` component
- Provider-specific feature access control
- Administrative override capabilities
- Tenant-aware feature management

## Business Logic

### Feature Evaluation Process

The `ProgramFeatureCalculator` processes feature access through a multi-step evaluation:

1. **Tenant Check**: Verify if feature is enabled at tenant level
2. **Program Iteration**: Check each program the user is enrolled in
3. **Rule Evaluation**: Process rule sets for each program/feature combination
4. **Result Aggregation**: Combine results (any program enabling = feature enabled)

### Rule Processing Logic

**Rule Order Significance**: Rules are processed sequentially, first match wins
- Early rules can override later conditions
- Allows for exception handling (e.g., disable for churned users)
- Enables complex conditional logic with fallback defaults

**Condition Matching**: Multiple condition types supported
- `onboarding_state`: User's current onboarding progress
- `insurance_coverage_state`: Insurance coverage status
- Future extensibility for additional state types

**Boolean Logic**: Simple true/false evaluation per rule
- Rules return boolean values when conditions match
- No complex boolean operators within single rules
- Simplicity enables easier debugging and maintenance

### Example Rule Sets

**Progressive Feature Enablement**:
```json
[
  { "onboarding_state": ["prospect"], "value": false },
  { "onboarding_state": ["member", "enrolled"], "value": true },
  { "value": false }
]
```

**Insurance-Dependent Features**:
```json
[
  { "insurance_coverage_state": ["active"], "value": true },
  { "onboarding_state": ["enrolled"], "value": false },
  { "value": false }
]
```

**Complex Conditional Logic**:
```json
[
  { "onboarding_state": ["churned"], "value": false },
  {
    "onboarding_state": ["member"],
    "insurance_coverage_state": ["active"],
    "value": true
  },
  { "value": false }
]
```

## Configuration

### Adding New Features

1. **Define Feature Constant**: Add to `Features` enum in `constants.py`
   ```python
   class Features(Enum):
       NEW_FEATURE = "new_feature"
   ```

2. **Configure Tenant Access**: Update tenant configuration in Django admin
   - Navigate to Tenant model
   - Edit `feature_access_config` JSON field
   - Add feature with boolean value

3. **Define Program Rules**: Add rule sets to `program_feature_config.json`
   ```json
   {
     "program_name": {
       "new_feature": [
         { "onboarding_state": ["member"], "value": true },
         { "value": false }
       ]
     }
   }
   ```

### Frontend Implementation

**Patient App Integration**:
```javascript
const featureAccess = useFeatureAccess();
const canAccessFeature = featureAccess.new_feature;

return (
  <BehindPatientFeatureAccessCheck feature="new_feature">
    <FeatureComponent />
  </BehindPatientFeatureAccessCheck>
);
```

**Provider App Integration**:
```javascript
const featureAccess = useFeatureAccess();

return (
  <BehindProviderFeatureAccessCheck feature="new_feature">
    <AdminFeatureComponent />
  </BehindProviderFeatureAccessCheck>
);
```

### Testing and Validation

**Rule Testing**: Validate rule logic with different user states
- Test all possible onboarding states
- Verify insurance coverage combinations
- Confirm rule order precedence

**Tenant Configuration**: Ensure proper tenant-level overrides
- Test feature disabled at tenant level
- Verify program rules are ignored when tenant disables
- Confirm proper fallback behavior
