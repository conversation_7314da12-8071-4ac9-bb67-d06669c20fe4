import logging
from typing import List

import dramatiq
from django.conf import settings
from django.db.models import Q

from firefly.core.services.dramatiq.constants import RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS
from firefly.core.services.dramatiq.utils import get_rate_limiter
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP, DEFAULT_PREFERRED_LANGUAGE
from firefly.core.user.models.models import Assignee<PERSON>roup, User
from firefly.modules.appointment.constants import NEW_PATIENT_APPOINTMENT_TYPES
from firefly.modules.care_plan.constants import CarePlanActions
from firefly.modules.care_plan.models import CarePlan
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation
from firefly.modules.onboarding.constants import (
    CASE_CATEGORY_UNIQUE_KEY_FOR_ONBOARDING_FIRST_APPT_OUTREACH,
    CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH,
)
from firefly.modules.onboarding.models import OnboardingState
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.onboarding.statemachine.utils import (
    is_activated_member,
    send_onboarding_slack_alert,
)
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import person_enrolled_programs_list
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)


def create_onboarding_outreach_case_from_state(patient: User, current_onboarding_state: str, log_prefix: str):
    onboarding_case: Case
    created: bool
    patient_enrolled_programs_list: List[str] = person_enrolled_programs_list(person=patient.person)
    # For benefit members assign case to member_guide group
    if ProgramCodes.BENEFIT in patient_enrolled_programs_list:
        care_coordinator_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["care_coordinator"]
        owner_group = AssigneeGroup.objects.get(unique_key=care_coordinator_unique_key)
        defaults = {
            "owner_group": owner_group,
            "description": "Created from: {}".format(current_onboarding_state),
        }
    else:
        defaults = {
            "description": "Created from: {}".format(current_onboarding_state),
        }
    onboarding_case, created = Case.objects.get_or_create(
        person=patient.person,
        category=CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_ONBOARDING_FIRST_APPT_OUTREACH),
        defaults=defaults,
    )
    if created:
        send_onboarding_slack_alert(
            user_id=patient.pk,
            current_status=current_onboarding_state,
            case_id=onboarding_case.pk,
        )
        onboarding_state: OnboardingState = patient.onboarding_state
        if onboarding_state.onboarding_case_creation_state is None:
            onboarding_state.onboarding_case_creation_state = [current_onboarding_state]
        else:
            onboarding_state.onboarding_case_creation_state.append(current_onboarding_state)
        onboarding_state.save(update_fields=["onboarding_case_creation_state"])
        CaseRelation.objects.create(case=onboarding_case, content_object=onboarding_state)
        logger.info(
            "%s: Created Onboarding outreach Case with id %d for user %d",
            log_prefix,
            onboarding_case.pk,
            patient.pk,
        )
    else:
        logger.info(
            "%s: User %d already has onboarding case with id %d. Skipping any edits",
            log_prefix,
            patient.pk,
            onboarding_case.pk,
        )


@dramatiq.actor(queue_name="onboarding", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def async_create_onboarding_outreach_case_from_initialized_state(patient_id: int):
    # This function can have invocations from multiple workers
    # Ensure that only one worker has the lock required to execute
    rate_limiter_key: str = "onboarding_outreach_case_creation{}".format(patient_id)
    # The async function is only a light wrapper around the synchronous function
    # and is only responsible for integrating with dramatiq
    # and rate limiter functions. Ensuring that we get retries,
    # DLQ functionality and rate limiting.
    # These functionalities are (unit)-tested as part of dramatiq and our rate limiting
    # code. This pattern allows for separation of concerns and allows for easier testing.
    # In tests - we verify that the async function is invoked. Whereas the
    # tests for the synchronous function house assertions for different scenarios.
    with get_rate_limiter(
        key=rate_limiter_key,
        limit=settings.ONBOARDING_CASE_CREATION_RATE_LIMIT_PER_USER,
    ).acquire():
        create_onboarding_outreach_case_from_initialized_state(
            patient_id=patient_id,
        )


def create_onboarding_outreach_case_from_initialized_state(patient_id: int):
    log_prefix: str = "onboarding_outreach_init_to_signup"
    try:
        patient: User = User.objects.get(id=patient_id)
        onboarding_state: OnboardingState = patient.onboarding_state
        current_onboarding_status: str = onboarding_state.status
        if current_onboarding_status == OnboardingStatus.INITIALIZED:
            create_onboarding_outreach_case_from_state(patient, current_onboarding_status, log_prefix)
        else:
            logger.info(
                "%s: User %d has already moved away from initialized state. Not creating onboarding case.",
                log_prefix,
                patient.pk,
            )
    except Exception as e:
        logger.exception(
            "%s: Failed to create Onboarding outreach Case for user %d",
            log_prefix,
            patient_id,
        )
        raise e


@dramatiq.actor(queue_name="onboarding", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def async_create_onboarding_outreach_case_from_signedup_state(patient_id):
    # This function can have invocations from multiple workers
    # Ensure that only one worker has the lock required to execute
    rate_limiter_key: str = "onboarding_outreach_case_creation{}".format(patient_id)
    # The async function is only a light wrapper around the synchronous function
    # and is only responsible for integrating with dramatiq
    # and rate limiter functions. Ensuring that we get retries,
    # DLQ functionality and rate limiting.
    # These functionalities are (unit)-tested as part of dramatiq and our rate limiting
    # code. This pattern allows for separation of concerns and allows for easier testing.
    # In tests - we verify that the async function is invoked. Whereas the
    # tests for the synchronous function house assertions for different scenarios.
    with get_rate_limiter(
        key=rate_limiter_key,
        limit=settings.ONBOARDING_CASE_CREATION_RATE_LIMIT_PER_USER,
    ).acquire():
        create_onboarding_outreach_case_from_signedup_state(
            patient_id=patient_id,
        )


def create_onboarding_outreach_case_from_signedup_state(patient_id):
    log_prefix: str = "onboarding_outreach_init_to_signup"
    try:
        patient: User = User.objects.get(id=patient_id)
        onboarding_state: OnboardingState = patient.onboarding_state
        current_onboarding_status: str = onboarding_state.status
        patient_enrolled_programs_list: List[str] = person_enrolled_programs_list(person=patient.person)
        if current_onboarding_status == OnboardingStatus.SIGNEDUP and (
            len(patient_enrolled_programs_list) == 0 or ProgramCodes.PRIMARY_CARE in patient_enrolled_programs_list
        ):
            create_onboarding_outreach_case_from_state(patient, current_onboarding_status, log_prefix)
        else:
            logger.info(
                "%s: User %d has already moved away from signedup state or is not enrolled in primary care."
                "Not creating onboarding case.",
                log_prefix,
                patient.pk,
            )
    except Exception as e:
        logger.exception(
            "%s: Failed to create Onboarding outreach Case for user %d",
            log_prefix,
            patient_id,
        )
        raise e


@dramatiq.actor(queue_name="onboarding", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def async_create_onboarding_outreach_case_for_scheduling_appointment(patient_id: int, already_delayed: bool = False):
    # This function can have invocations from multiple workers
    # Ensure that only one worker has the lock required to execute
    rate_limiter_key: str = "onboarding_outreach_case_creation{}".format(patient_id)
    # The async function is only a light wrapper around the synchronous function
    # and is only responsible for integrating with dramatiq
    # and rate limiter functions. Ensuring that we get retries,
    # DLQ functionality and rate limiting.
    # These functionalities are (unit)-tested as part of dramatiq and our rate limiting
    # code. This pattern allows for separation of concerns and allows for easier testing.
    # In tests - we verify that the async function is invoked. Whereas the
    # tests for the synchronous function house assertions for different scenarios.
    with get_rate_limiter(
        key=rate_limiter_key,
        limit=settings.ONBOARDING_CASE_CREATION_RATE_LIMIT_PER_USER,
    ).acquire():
        create_onboarding_outreach_case_for_scheduling_appointment(
            patient_id=patient_id,
            already_delayed=already_delayed,
        )


def create_onboarding_outreach_case_for_scheduling_appointment(patient_id: int, already_delayed: bool = False):
    log_prefix: str = "onboarding_outreach_scheduling_appointment"
    try:
        patient = User.objects.get(id=patient_id)
        onboarding_state: OnboardingState = patient.onboarding_state
        current_onboarding_status: str = onboarding_state.status
        patient_enrolled_programs_list: List[str] = person_enrolled_programs_list(person=patient.person)
        if (
            not is_activated_member(patient.person)
            or patient.appointments.filter(reason__in=NEW_PATIENT_APPOINTMENT_TYPES).exists()
        ):
            logger.info(
                "%s: User %d has already moved away from Member state or"
                "has already scheduled a new patient appointment. Not creating onboarding case.",
                log_prefix,
                patient.pk,
            )
        # Delay the creation of appt outreach case if both
        #   - user has selected no thanks on the screen to book first appointment
        #   - this function call is not the delayed one
        elif onboarding_state.book_visit_during_onboarding_yn is False and already_delayed is False:
            async_create_onboarding_outreach_case_for_scheduling_appointment.send_with_options(
                args=(patient_id, True),
                delay=settings.ALLOWED_DELAY_FOR_USER_TO_COMPLETE_SCHEDULING_VISIT_LATER_IN_MILLISECONDS,
            )
            logger.info(
                "%s: Patient has chosen to delay the appointment booking.Creating a delayed task for user %d",
                log_prefix,
                patient_id,
            )
        # Create an appt outreach case case if user is
        #   - already a member
        #   - either is enrolled in primary care program or isnt yet enrolled
        #     in any programs. Since case creation happens async - we want to err
        #     on the side of caution (and rather create cases when they are not
        #     necessary rather than miss cases when they are necessary)
        elif len(patient_enrolled_programs_list) == 0 or ProgramCodes.PRIMARY_CARE in patient_enrolled_programs_list:
            create_onboarding_outreach_case_from_state(patient, current_onboarding_status, log_prefix)
        else:
            logger.info(
                "%s: Patient has not enrolled for care.Skipping creation of Onboarding outreach Case for user %d",
                log_prefix,
                patient_id,
            )
    except Exception as e:
        logger.exception(
            "%s: Failed to create Onboarding outreach Case for user %d",
            log_prefix,
            patient_id,
        )
        raise e


@dramatiq.actor(queue_name="preferred_non_english", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def create_onboarding_case_for_preferred_language_non_english(patient_id):
    # This function can have invocations from multiple workers
    # Ensure that only one worker has the lock required to execute
    rate_limiter_key: str = "create_onboarding_case_for_preferred_language_non_english_{}".format(patient_id)
    with get_rate_limiter(
        key=rate_limiter_key,
        limit=1,
    ).acquire():
        person = User.objects.get(id=patient_id).person

        if person and person.preferred_language and person.preferred_language.name != DEFAULT_PREFERRED_LANGUAGE:
            from firefly.modules.cases.models import Case, CaseCategory

            category = CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH)

            language_chosen = person.preferred_language_other
            if language_chosen is None:
                language_chosen = person.preferred_language.name
            description = "Chosen Language: {}".format(language_chosen)

            Case.objects.get_or_create(
                category=category,
                person=person,
                defaults={
                    "description": description,
                },
            )


@dramatiq.actor(queue_name="auto_close_care_plans", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def auto_close_care_plans_async(users, dry_run_off):
    for user in users:
        care_plans = CarePlan.objects.filter(patient_id=user.id).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        if care_plans.count():
            logger.info("Would be closing %s care plan(s) for user %s", care_plans.count(), user.id)
            if dry_run_off:
                for care_plan in care_plans:
                    try:
                        care_plan.status_category = StatusCategory.COMPLETE
                        care_plan.action = CarePlanActions.INACTIVE
                        care_plan.save()
                    except Exception as e:
                        logger.exception(  # noqa
                            "Failed to close care plan %s for patient %s: %s", care_plan.pk, care_plan.patient.id, e
                        )
