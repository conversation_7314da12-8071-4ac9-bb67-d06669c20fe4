import logging

from django.conf import settings
from django.db import models
from django.utils import timezone
from django_deprecate_fields import deprecate_field
from transitions import Machine

from firefly.core.services.slack.constants import SlackChannel
from firefly.core.services.slack.tasks import send_slack_message
from firefly.core.user.utils import get_user_link
from firefly.modules.appointment.utils import patient_has_established_care
from firefly.modules.insurance.models import InsuranceMemberInfo
from firefly.modules.onboarding.statemachine.constants import (
    ONBOARDING_CHOICES,
    ONBOARDING_VALUES,
    OnboardingStatus,
    OnboardingTransitions,
)
from firefly.modules.onboarding.statemachine.utils import (
    deprecate_onboarding_statemachine_post_transition,
    deprecate_onboarding_statemachine_transition,
)
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import (
    person_is_in_program,
    program_info_for_person_program,
    remove_person_from_program,
)
from firefly.modules.statemachines.coverage.models import PatientTransitionLog
from firefly.modules.tenants.utils import is_person_in_firefly

CURRENT_TIMEZONE = timezone.get_current_timezone()
INSURANCE_TYPES_ELIGIBLE = [
    InsuranceMemberInfo.SOURCE_TYPE_EMPLOYER,
    InsuranceMemberInfo.SOURCE_TYPE_PRIVATE,
]

logger = logging.getLogger(__name__)

# LOTS OF THINGS (INCLUDING ORDER) ARE VERY IMPORTANT HERE, PLEASE TRIPLE CHECK WHEN EDITING
MACHINE_TRANSITIONS = [
    {
        # This will trigger for all members after the first batch of demographics information during registration
        "trigger": OnboardingTransitions.BASIC_INFO_COLLECTED,
        "source": [OnboardingStatus.INITIALIZED],
        "dest": OnboardingStatus.INITIALIZED,
        "before": [],
        "after": [
            "initialize_member_in_braze",
            "create_case_for_onboarding_outreach",
            "update_test_only_flag",
        ],
    },
    {
        # Allow basic info collected from a signed up state as well
        # Since the basic info collected transition and the to sign up
        # transition can happen within a short interval of each other
        # causing a race condition
        "trigger": OnboardingTransitions.BASIC_INFO_COLLECTED,
        "source": [OnboardingStatus.SIGNEDUP],
        "dest": OnboardingStatus.SIGNEDUP,
        "before": [],
        "after": [
            "initialize_member_in_braze",
            "create_case_for_onboarding_outreach",
            "update_test_only_flag",
        ],
    },
    {
        # This will trigger for all Coverage members
        "trigger": OnboardingTransitions.SIGNUP_COMPLETE,
        "source": [OnboardingStatus.INITIALIZED, OnboardingStatus.SIGNEDUP],
        "dest": OnboardingStatus.MEMBER,
        "conditions": "is_in_benefit_program",
        "unless": ["skips_member_onboarding"],
        "after": ["handle_signup_complete"],
    },
    {
        # This will trigger for all members
        "trigger": OnboardingTransitions.SIGNUP_COMPLETE,
        "source": [OnboardingStatus.INITIALIZED, OnboardingStatus.SIGNEDUP],
        "dest": OnboardingStatus.SIGNEDUP,
        "unless": ["skips_member_onboarding"],
        "after": ["handle_signup_complete"],
    },
    {
        "trigger": OnboardingTransitions.SIGNUP_COMPLETE,
        "source": [OnboardingStatus.INITIALIZED, OnboardingStatus.SIGNEDUP],
        "dest": OnboardingStatus.ENROLLED,
        "conditions": "skips_member_onboarding",
        "after": [],
    },
    {
        "trigger": OnboardingTransitions.SIGNUP_COMPLETE,
        "source": [OnboardingStatus.ENROLLED],
        "dest": OnboardingStatus.ENROLLED,
    },
    {
        "trigger": OnboardingTransitions.INSURANCE_COVERED,
        "source": [
            OnboardingStatus.INITIALIZED,
            OnboardingStatus.SIGNEDUP,
            OnboardingStatus.ENROLLED,
            OnboardingStatus.MEMBER,
            OnboardingStatus.DEACTIVATED,
        ],
        "dest": OnboardingStatus.MEMBER,
        "unless": ["is_covered_member"],
    },
    {
        "trigger": OnboardingTransitions.ACCOUNT_REACTIVATED,
        "source": [OnboardingStatus.DEACTIVATED],
        "dest": OnboardingStatus.SIGNEDUP,
        "after": [
            "unblock_auth0_logins",
            "enable_user",
        ],
    },
    {
        "trigger": OnboardingTransitions.ACCOUNT_DEACTIVATED,
        "source": [
            OnboardingStatus.INITIALIZED,
            OnboardingStatus.SIGNEDUP,
            OnboardingStatus.ENROLLED,
            OnboardingStatus.MEMBER,
            OnboardingStatus.DEACTIVATED,
        ],
        "dest": OnboardingStatus.DEACTIVATED,
        "after": [
            "block_auth0_logins",
            "disable_user",
        ],
    },
]


class OnboardingStateMachineMixin(models.Model):
    """Mixin that adds onboarding statemachine. For use on OnboardingState."""

    class Meta:
        abstract = True

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=32,
        choices=ONBOARDING_CHOICES,
        default=OnboardingStatus.INITIALIZED,
        db_index=True,
    )

    initialized_at = models.DateTimeField(null=True, blank=True)
    signedup_at = models.DateTimeField(null=True, blank=True)
    # the enrolled_at will be deprecated, the only usage is in Onboarding tab
    enrolled_at = models.DateTimeField(null=True, blank=True)
    # TODO: Remove member status and track signup completion as part of signed_up
    # and insurance eligibility as part of coverage mixin
    # as a final step, the member_at will be deprecated
    member_at = models.DateTimeField(null=True, blank=True)
    # churned_at is used in update_poc_attribution signal behind the feature flag and in Onboarding tab
    churned_at = deprecate_field(models.DateTimeField(null=True, blank=True))
    # discharged_at is used in update_poc_attribution signal behind the feature flag and in Onboarding tab
    discharged_at = deprecate_field(models.DateTimeField(null=True, blank=True))
    deactivated_at = models.DateTimeField(null=True, blank=True)
    activated_at = models.DateTimeField(null=True, blank=True)

    initialized_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    signedup_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # the enrolled_updated_by can be deprecated, the only usage is in Onboarding tab
    enrolled_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    member_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # the churned_updated_by can be deprecated, the only usage is in Onboarding tab
    churned_updated_by = deprecate_field(
        models.ForeignKey(
            settings.AUTH_USER_MODEL,
            null=True,
            blank=True,
            related_name="+",
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )
    discharged_updated_by = deprecate_field(
        models.ForeignKey(
            settings.AUTH_USER_MODEL,
            null=True,
            blank=True,
            related_name="+",
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )
    deactivated_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    def __init__(self, *args, **kwargs):
        """Set up the state machine."""

        super().__init__(*args, **kwargs)
        self.onboarding_machine = Machine(
            model=self,
            states=ONBOARDING_VALUES,
            initial=self.status,
            transitions=MACHINE_TRANSITIONS,
            auto_transitions=True,
            model_attribute="status",
            after_state_change="_post_transition",
            send_event=True,
        )

    def _post_transition(self, event_data):
        """Create a PatientTransitionLog after every transition."""
        actor = event_data.kwargs.get("actor")
        initial_status = event_data.transition.source
        current_status = event_data.transition.dest
        deprecate_onboarding_statemachine_post_transition(initial_status, current_status)
        # Validation
        if len(event_data.args) > 0:
            raise TypeError("State transitions expect no args. Please pass any metadata as kwargs.")
        # writing to patient transition log when Initialized -> Initialized transition is triggered.
        if initial_status != current_status or (
            initial_status == OnboardingStatus.INITIALIZED and current_status == OnboardingStatus.INITIALIZED
        ):
            updated_at_key = current_status + "_at"
            updated_by_key = current_status + "_updated_by"
            source_timestamp = getattr(self, initial_status + "_at")
            log = PatientTransitionLog.objects.create(
                attribute="onboarding",
                transition_name=f"to_{current_status}"
                if initial_status != current_status
                else f"update_{current_status}",
                source=event_data.transition.source,
                source_ts=source_timestamp,
                destination=current_status,
                person=self.patient.person,
                actor=actor,
            )
            setattr(self, updated_at_key, log.destination_ts.astimezone(CURRENT_TIMEZONE))
            setattr(self, updated_by_key, actor)
            setattr(self, "status", current_status)
            self.save()
        logger.info(
            "[DEBUG_ELIGIBILITY] Complete post transistion log for status - %s",
            current_status,
        )
        logger.info("[DEBUG_ELIGIBILITY] person employer - %s", self.patient.person.employer_id)

        # Send Slack Alert for Onboarding status changes
        if initial_status != current_status and is_person_in_firefly(self.patient.person):
            markdown_text = get_user_link(self.patient.id)
            insurance_info = self.patient.person.insurance_info
            payer = self.patient.person.insurance_info.insurance_payer
            pmpm_eligible = payer.pmpm_eligible if payer else "Unknown"
            payer_name = payer.name if payer else "Unknown"
            markdown_text += (
                f": {initial_status} -> {current_status}! "
                f"PMPM Eligible: {pmpm_eligible}. "
                f"Plan: {insurance_info.state} - {payer_name}. "
                f"Coverage: {self.patient.person.coverage}"
            )

            if current_status == OnboardingStatus.DEACTIVATED:
                send_slack_message.send_with_options(
                    args=(markdown_text, SlackChannel.PROSPECT_ALERTS, [], None, False), delay=5000
                )

    @deprecate_onboarding_statemachine_transition
    def on_enter_initialized(self, event_data):
        pass

    @deprecate_onboarding_statemachine_transition
    def on_enter_enrolled(self, event_data):
        pass

    @deprecate_onboarding_statemachine_transition
    def on_enter_member(self, event_data):
        # The coverage users are directly moved to member status from initialised,
        # hence we are dual writing signedup_at.
        # This should be removed as part of deprecating the member status
        if person_is_in_program(person=self.patient.person, program_uid=ProgramCodes.BENEFIT):
            self.signedup_at = timezone.now()

    @deprecate_onboarding_statemachine_transition
    def on_enter_signedup(self, event_data):
        pass

    def on_enter_deactivated(self, event_data):
        data = event_data.kwargs.get("data")
        pending_discharge_reason: str = ""
        if data is not None:
            if data.get("reason"):
                self.deactivated_reason_app = data["reason"]
                if isinstance(data["reason"], list) and len(data["reason"]) > 0:
                    pending_discharge_reason = data["reason"][0]
                else:
                    pending_discharge_reason = data["reason"]
                self.save()
            if data.get("other_reason_data"):
                self.deactivated_reason_other = data["other_reason_data"]
                self.save()

        self.block_auth0_logins(self.patient)
        self.disable_user(self.patient)
        if patient_has_established_care(self.patient):
            primary_care_info = program_info_for_person_program(
                person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE
            )
            primary_care_info.pending_discharge_reason = pending_discharge_reason
            primary_care_info.save(update_fields=["pending_discharge_reason"])
        else:
            remove_person_from_program(
                person=self.patient.person,
                program_uid=ProgramCodes.PRIMARY_CARE,
                data={"reason": pending_discharge_reason, "is_deactivated": True},
            )
        # Don't send the email again if the account is already deactivated
        if event_data.transition.source != event_data.transition.dest:
            self.send_account_deletion_email(self.patient)

    def on_exit_deactivated(self, event_data):
        self.unblock_auth0_logins(self.patient)
        self.enable_user(self.patient)
