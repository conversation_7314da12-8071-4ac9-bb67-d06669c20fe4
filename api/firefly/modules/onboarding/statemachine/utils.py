import logging
import random

from django.utils import timezone

from firefly.core.services.slack.constants import SlackChannel
from firefly.core.services.slack.tasks import send_slack_message
from firefly.core.user.models.models import User
from firefly.modules.features.constants import Features
from firefly.modules.features.tenant_features import any_tenant_enables_feature
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.programs.models import Program
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import person_is_enrolled_in_program, person_is_in_program
from firefly.modules.statemachines.coverage.constants import CoverageStates
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import Task, TaskCollectionTask

logger = logging.getLogger(__name__)


def choose_weighted_member_guide(member_guide_list):
    """Helper for selecting member guide."""
    if not member_guide_list:
        raise ValueError("Failed to choose member guide from empty list")
    choice = random.sample(list(member_guide_list), 2)
    return choice


def send_onboarding_slack_alert(user_id: int, current_status: str, case_id: int):
    from firefly.modules.tasks.tasks import case_task_link

    user = User.objects.get(id=user_id)
    insurance_info = user.person.insurance_info
    payer = user.person.insurance_info.insurance_payer
    payer_name = payer.name if payer else "Unknown"
    markdown_text = case_task_link(user.person, case_id)
    markdown_text += f": Created from {current_status}! Plan: {insurance_info.state} - {payer_name}. "

    send_slack_message.send_with_options(args=(markdown_text, SlackChannel.ONBOARDING_ALERTS, [], None, False))


def update_onboarding_status(person):
    onboarding_state = person.user.onboarding_state
    # If user completed poc or selected care team move status to Member
    # If user only downloaded app move status to signed up
    # Else move status to initialized
    if onboarding_state.poc_completed_at is not None or onboarding_state.care_team_selection_at is not None:
        onboarding_state.to_member()
    elif onboarding_state.downloaded_app_at is not None:
        onboarding_state.to_signedup()
    else:
        onboarding_state.to_initialized()


def update_insurance_task_to_complete_processing(person):
    insurance_task = TaskCollectionTask.objects.get(uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE)
    tasks = Task.objects.filter(patient=person.user, autocreated_from=insurance_task)
    for task in tasks:
        if task.metadata["is_processing"]:
            task.metadata["is_processing"] = False
            task.save(update_fields=["metadata"])


def set_eligibility_check(user, actor):
    user.onboarding_state.insurance_eligibility_at = timezone.now()
    user.onboarding_state.insurance_eligibility_updated_by = actor
    user.onboarding_state.save(update_fields=["insurance_eligibility_at", "insurance_eligibility_updated_by"])


def deprecate_onboarding_statemachine_post_transition(initial, current):
    logger.warning('[OnboardingStateMachine Refactor] Deprecated transition triggered: "%s to %s"]', initial, current)


def deprecate_onboarding_statemachine_transition(f):
    """
    As part of refactoring the OnboardingStateMachine to only reflect app access and not program enrollment (which is
    being moved to the ProgramEnrollment and/ ProgramInfo models as part of this project), we need to log in Datadog
    when to-be-deprecated state machine transitions are triggered. This will allow us to verify that our refactor is
    working as expected and not missing anything. Note that the `deactivated` status is not being deprecated.
    """

    def wrapper(*args, **kwargs):
        logger.warning('[OnboardingStateMachine Refactor] Deprecated transition triggered: "%s"]', f.__name__)
        return f(*args, **kwargs)

    return wrapper


def deprecate_onboarding_statemachine_side_effect(f):
    """
    As part of refactoring the OnboardingStateMachine to only reflect app access and not program enrollment (which is
    being moved to the ProgramEnrollment and/ ProgramInfo models as part of this project), we need to log in Datadog
    when to-be-deprecated state machine side effects are called. This will allow us to verify that our refactor is
    working as expected and not missing anything. Note that the `deactivated` status is not being deprecated.
    """

    def wrapper(*args, **kwargs):
        logger.warning('[OnboardingStateMachine Refactor] Deprecated side effect called: "%s"]', f.__name__)
        return f(*args, **kwargs)

    return wrapper


def handle_member_activated(user):
    if not user:
        return
    primary_care_program = Program.objects.get(uid=ProgramCodes.PRIMARY_CARE)
    benefit_program = Program.objects.get(uid=ProgramCodes.BENEFIT)
    if person_is_enrolled_in_program(user.person, benefit_program):
        # user.onboarding_state.refresh_from_db()
        # user.onboarding_state.to_member()
        handle_benefit_member_activated(user, is_first_signup_complete_event=True)
    if person_is_enrolled_in_program(user.person, primary_care_program):
        handle_primary_care_member_activated(user)


def handle_primary_care_member_activated(user, is_first_signup_complete_event=False):
    # Skip if member hasn't signed up yet
    if _member_has_not_signed_up(user) or not any_tenant_enables_feature(
        Features.onboarding.value, list(user.person.tenants.all())
    ):
        return
    _handle_primary_care_or_benefit_member_activated(user, is_first_signup_complete_event)


def handle_benefit_member_activated(user, is_first_signup_complete_event=False):
    from firefly.modules.onboarding.statemachine.side_effects import (
        assign_intake_survey_form,
        assign_member_guide,
        create_onboarding_outreach_case_for_coverage_only_members,
    )

    # Skip if member hasn't signed up yet
    if _member_has_not_signed_up(user):
        return
    assign_member_guide(user)
    _handle_primary_care_or_benefit_member_activated(user, is_first_signup_complete_event)
    create_onboarding_outreach_case_for_coverage_only_members(user)
    assign_intake_survey_form(user)


def _handle_primary_care_or_benefit_member_activated(user, is_first_signup_complete_event=False):
    from firefly.modules.onboarding.statemachine.side_effects import (
        create_onboarding_journey_virtual_tasks,
        send_new_patient_sms,
        send_welcome_reason_message,
    )

    primary_care_program = Program.objects.get(uid=ProgramCodes.PRIMARY_CARE)
    benefit_program = Program.objects.get(uid=ProgramCodes.BENEFIT)
    # Skip if member hasn't signed up yet or is enrolled in both programs
    if _member_has_not_signed_up(user) or (
        not is_first_signup_complete_event
        and person_is_enrolled_in_program(user.person, primary_care_program)
        and person_is_enrolled_in_program(user.person, benefit_program)
    ):
        return
    send_welcome_reason_message(user)
    send_new_patient_sms(user)
    create_onboarding_journey_virtual_tasks(user)


def _member_has_not_signed_up(user):
    return (
        not user
        or not hasattr(user, "onboarding_state")
        or user.onboarding_state.status not in (OnboardingStatus.SIGNEDUP, OnboardingStatus.MEMBER)
    )


def is_activated_member(person):
    return (
        # Check that they have completed signup
        is_signedup(person.user.onboarding_state)
        # Check that they have completed the "finish setup" section and have a care team
        and person.provider_detail_patient_persons.exists()
        # Check that their insurance is covered
        and person.coverage == CoverageStates.COVERED_INSURANCE
        # Check that they are enrolled in the primary care program and not churned/discharged
        and person_is_in_program(person, ProgramCodes.PRIMARY_CARE)
    )


def is_signedup(onboarding_state):
    return onboarding_state.status in (OnboardingStatus.SIGNEDUP, OnboardingStatus.MEMBER)
