import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from unittest import mock
from unittest.mock import patch

from django.contrib.auth.models import Group
from django.utils import timezone
from dramatiq.rate_limits import ConcurrentRateLimiter
from dramatiq.rate_limits.backends import StubBackend

from firefly.core.alias.models import get_content_type
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import (
    ASSIGNEE_GROUP_UNIQUE_KEY_MAP,
    MEMBER_GUIDE_COVERAGE_ROLE,
    ONBOARDING_OUTREACH_COVERAGE_ONLY,
)
from firefly.core.user.factories import PersonFactory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models import Person, User
from firefly.core.user.models.models import AssigneeGroup
from firefly.core.user.utils import create_update_assignee_group_from_user
from firefly.modules.appointment.constants import AppointmentReason, AppointmentStatus
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.utils import patient_has_established_care
from firefly.modules.attribution.models import AbstractAttributionData, Attribution
from firefly.modules.cases.constants import INSURANCE_PLAN_NEEDS_REVIEW, DischargeMemberCaseStatuses
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.chat_message.models import ChatMessageTemplate
from firefly.modules.facts.factories import PreferredLanguageFactory
from firefly.modules.features.constants import Features
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.insurance.constants import AttributionRequirementType, ContractAttributionType, ContractPMPMType
from firefly.modules.insurance.models import Contract, Employer, InsuranceMemberInfo, InsurancePayer
from firefly.modules.onboarding.constants import CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus, OnboardingTransitions
from firefly.modules.onboarding.statemachine.utils import (
    handle_benefit_member_activated,
    handle_primary_care_member_activated,
)
from firefly.modules.onboarding.tasks import create_onboarding_case_for_preferred_language_non_english
from firefly.modules.pods.constants import PodUIDs
from firefly.modules.pods.models import Pod
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.models import Program, ProgramEnrollment
from firefly.modules.programs.primary_care.utils import COVERAGE_INTAKE_SURVEY
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, person_is_in_program, remove_person_from_program
from firefly.modules.signup_reasons.models import SignupReasons
from firefly.modules.statemachines.attribution.mixin import AttributionConstants
from firefly.modules.statemachines.coverage.constants import CoverageStates
from firefly.modules.statemachines.coverage.models import PatientTransitionLog
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import SOURCE_TYPES, Task, TaskCollection, TaskCollectionTask
from firefly.modules.tenants.factories import TenantFactory

logger = logging.getLogger(__name__)


class OnboardingStatusTestCase(FireflyTestCase):
    def update_attribution(
        self, person: Person, attribution_requirement_type: str, contract: Optional[Contract] = None
    ):
        if contract and contract.config.get("employer_id"):
            employer = Employer.objects.get(id=contract.config.get("employer_id"))
            person.employer = employer
            person.save(update_fields=["employer"])
            if contract.config.get("is_care_program_enrollment_enabled"):
                add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
            if contract.config.get("is_coverage_program_enrollment_enabled"):
                add_person_to_program(person, ProgramCodes.BENEFIT)
        if contract and contract.config.get("payer_id"):
            add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        self.assertIsNotNone(person.insurance_info)
        if person.insurance_info:
            insurance_info: InsuranceMemberInfo = person.insurance_info
            if (
                hasattr(insurance_info, "insurance_plan")
                and insurance_info.insurance_plan is not None
                and not insurance_info.insurance_plan.firefly_accepted
            ):
                insurance_info.insurance_plan.firefly_accepted = True
                insurance_info.insurance_plan.save(update_fields=["firefly_accepted"])
                insurance_info.refresh_from_db()
            if contract and contract.config.get("allowable_group_ids"):
                insurance_info.group_number = contract.config.get("allowable_group_ids")[0]
            if contract and contract.config.get("payer_id"):
                insurance_info.insurance_payer = InsurancePayer.objects.get(id=contract.config.get("payer_id"))
            insurance_info.save()
        attribution: Attribution = person.attribution
        attribution.contract = contract
        attribution.attribution_requirement_type = attribution_requirement_type
        attribution.save()

    def setUp(self):
        super().setUp()
        self.payer_contract = Contract.objects.create(
            config={
                "payer_id": self.patient.person.insurance_info.insurance_payer.id,
                "plan_description_specific": True,
                "attribution_type": ContractAttributionType.POC_FORM,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
                "is_attribution_tracked_externally": True,
            },
            contracted_entity_content_type=get_content_type(self.patient.person.insurance_info.insurance_payer),
            contracted_entity=self.patient.person.insurance_info.insurance_payer,
        )
        self._discharge_state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
            title="Member Discharge",
            content=self.member_discharge_state_machine_content,
        )

        self.discharge_category, _ = CaseCategory.objects.get_or_create(
            title="Member Discharge",
            unique_key="member_discharge",
            state_machine_definition=self._discharge_state_machine_definition,
            description="default",
        )

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    def test_transitions(self, mock_update_auth0_user):
        self.assertEqual(PatientTransitionLog.objects.filter(person=self.patient.person).count(), 3)
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=ContractAttributionType.POC_FORM,
            contract=self.payer_contract,
        )
        self.patient.onboarding_state.to_enrolled(actor=self.patient)
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.onboarding_state.to_member(actor=self.patient)
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        # There is 3 transition log already from patient creation.
        # 4 Transition log from onboarding state, 1 from attribution(init to contracted)
        # discharge, deactivated, churned triggers POC attribution since the user is not enrolled in PCP
        # and PCP signed at date is null, attribution transistion is short circuited
        self.assertEqual(PatientTransitionLog.objects.filter(person=self.patient.person).count(), 8)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    @patch("firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._unsubscribe_from_zingle_list")
    @patch("firefly.modules.onboarding.statemachine.side_effects.schedule_account_deletion_notification")
    def test_deactivate_user_for_employer_contract(
        self, mock_schedule_account_deletion_notification, mock_unsubscribe_zingle_contact, mock_update_auth0_user
    ):
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=AttributionRequirementType.POC_FORM,
            contract=self.contract,
        )
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(
            self.patient.person.attribution.status,
            AttributionConstants.States.CONTRACTED,
        )
        self.patient.onboarding_state.poc_completed_at = timezone.now()
        self.patient.onboarding_state.save()
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(
            self.patient.person.attribution.status,
            AttributionConstants.States.CONFIRMED,
        )
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        self.patient.refresh_from_db()
        mock_unsubscribe_zingle_contact.assert_called_once()
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": True})
        self.assertEqual(self.patient.userdevices.count(), 0)
        self.assertTrue(self.patient.disabled)
        self.assertTrue(hasattr(self.patient.person, "internal_attribution_log"))
        self.assertIsNotNone(self.patient.person.internal_attribution_log)
        self.assertEqual(
            self.patient.person.internal_attribution_log.status,
            AbstractAttributionData.STATUS_NOT_ATTRIBUTED,
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(
            self.patient.person.attribution.status,
            AttributionConstants.States.CONTRACTED,
        )
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.DEACTIVATED)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    @patch("firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._unsubscribe_from_zingle_list")
    @patch("firefly.modules.onboarding.statemachine.side_effects.schedule_account_deletion_notification")
    def test_deactivate_user_for_payer_contract(
        self, mock_schedule_account_deletion_notification, mock_unsubscribe_zingle_contact, mock_update_auth0_user
    ):
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=AttributionRequirementType.POC_FORM,
            contract=self.payer_contract,
        )
        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        self.patient.onboarding_state.poc_completed_at = timezone.now()
        self.patient.onboarding_state.save()
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.PROVISIONAL)
        remove_person_from_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        self.patient.refresh_from_db()
        mock_unsubscribe_zingle_contact.assert_called_once()
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": True})
        self.assertEqual(self.patient.userdevices.count(), 0)
        self.assertTrue(self.patient.disabled)
        self.assertTrue(hasattr(self.patient.person, "internal_attribution_log"))
        self.assertIsNotNone(self.patient.person.internal_attribution_log)
        self.assertEqual(
            self.patient.person.internal_attribution_log.status, AbstractAttributionData.STATUS_NOT_ATTRIBUTED
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.DEACTIVATED)

        # Ensure sure deletion notification is only sent once
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        mock_schedule_account_deletion_notification.assert_called_once_with(self.patient)
        self.patient.onboarding_state.refresh_from_db()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.DEACTIVATED)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    @patch("firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._unsubscribe_from_zingle_list")
    @patch("firefly.modules.onboarding.statemachine.side_effects.schedule_account_deletion_notification")
    def test_deactivate_user_without_contract(
        self, mock_schedule_account_deletion_notification, mock_unsubscribe_zingle_contact, mock_update_auth0_user
    ):
        person: Person = PersonUserFactory()
        self.update_attribution(
            person=person, attribution_requirement_type=AttributionRequirementType.POC_FORM, contract=None
        )
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        person.user.onboarding_state.to_signedup(actor=person.user)
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        person.user.onboarding_state.poc_completed_at = timezone.now()
        person.user.onboarding_state.save()
        person.user.person.insurance_info.refresh_from_db()
        person.user.person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        person.user.onboarding_state.to_deactivated(actor=person.user)
        person.user.refresh_from_db()
        mock_unsubscribe_zingle_contact.assert_called_once()
        mock_update_auth0_user.assert_called_with(person.user, {"blocked": True})
        self.assertEqual(person.user.userdevices.count(), 0)
        self.assertTrue(person.user.disabled)
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.DEACTIVATED)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    @patch("firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._unsubscribe_from_zingle_list")
    @patch("firefly.modules.onboarding.statemachine.side_effects.schedule_account_deletion_notification")
    @patch(
        "firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._zus_handle_person_change_event"
    )
    def test_deactivate_user_with_established_care(
        self,
        mock_zus_deactivation,
        mock_schedule_account_deletion_notification,
        mock_unsubscribe_zingle_contact,
        mock_update_auth0_user,
    ):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person: Person = PersonUserFactory()
        self.update_attribution(
            person=person, attribution_requirement_type=AttributionRequirementType.POC_FORM, contract=None
        )
        program_info = add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        person.user.onboarding_state.to_signedup(actor=person.user)
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        person.user.onboarding_state.poc_completed_at = timezone.now()
        person.user.onboarding_state.save()
        person.user.person.insurance_info.refresh_from_db()
        person.user.person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        person.user.onboarding_state.to_member(actor=person.user)
        Appointment.objects.create(
            patient=person.user,
            elation_id=34523452345,
            physician=self.physician,
            start=(timezone.now() + timedelta(days=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            status=AppointmentStatus.CHECKED_OUT.value,
        )

        person.user.onboarding_state.to_deactivated(
            actor=person.user, data={"reason": ["I need urgent care, not primary care"], "otherReasonData": ""}
        )
        person.user.refresh_from_db()
        mock_unsubscribe_zingle_contact.assert_not_called()
        mock_zus_deactivation.assert_not_called()
        mock_update_auth0_user.assert_called_with(person.user, {"blocked": True})
        self.assertEqual(person.user.userdevices.count(), 0)
        self.assertTrue(person.user.disabled)
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        discharge_case = Case.objects.filter(person=person, category=self.discharge_category).first()
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        self.assertEqual(
            discharge_case.status,
            DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING,
        )
        discharge_case.action = DischargeMemberCaseStatuses.DONE
        discharge_case.save()
        program_info.refresh_from_db()
        program_enrollment = (
            ProgramEnrollment.objects.filter(person=person, program=care_program).order_by("updated_at").first()
        )
        self.assertEqual(program_info.active, False)
        self.assertEqual(program_enrollment.person.user.onboarding_state.status, OnboardingStatus.DEACTIVATED)

        active_program_enrollment = ProgramEnrollment.objects.filter(
            person=person, status=PrimaryCareProgramStatus.NOT_ESTABLISHED
        ).first()
        self.assertIsNotNone(active_program_enrollment.period.lower)
        self.assertIsNotNone(active_program_enrollment.period.upper)
        self.assertIsNone(active_program_enrollment.reason)
        churned_program_enrollment = ProgramEnrollment.objects.filter(
            person=person, status=PrimaryCareProgramStatus.CHURNED
        ).first()
        self.assertEqual(churned_program_enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(churned_program_enrollment.reason, f"Discharged:{'I need urgent care, not primary care'}")
        self.assertIsNotNone(churned_program_enrollment.period.lower)
        self.assertIsNone(churned_program_enrollment.period.upper)
        mock_zus_deactivation.assert_called_once()
        mock_unsubscribe_zingle_contact.assert_called_once()

    def test_discharge_user_for_employer_contract(self):
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=AttributionRequirementType.POC_FORM,
            contract=self.contract,
        )
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        self.patient.onboarding_state.poc_completed_at = timezone.now()
        self.patient.onboarding_state.save()
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONFIRMED)
        self.discharge_user(self.patient.person)
        self.patient.refresh_from_db()
        self.assertTrue(hasattr(self.patient.person, "internal_attribution_log"))
        self.assertIsNotNone(self.patient.person.internal_attribution_log)
        self.assertEqual(
            self.patient.person.internal_attribution_log.status, AbstractAttributionData.STATUS_NOT_ATTRIBUTED
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        # Verify that discharging twice does not impact the data
        remove_person_from_program(self.patient.person, ProgramCodes.PRIMARY_CARE, data={"reason": "discharge"})
        self.patient.person.internal_attribution_log.refresh_from_db()
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(
            self.patient.person.internal_attribution_log.status, AbstractAttributionData.STATUS_NOT_ATTRIBUTED
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)

    def test_discharge_user_for_payer_contract(self):
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=AttributionRequirementType.POC_FORM,
            contract=self.payer_contract,
        )
        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        self.patient.onboarding_state.poc_completed_at = timezone.now() - timedelta(days=5)
        self.patient.onboarding_state.save()
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.PROVISIONAL)
        remove_person_from_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.refresh_from_db()
        self.assertTrue(hasattr(self.patient.person, "internal_attribution_log"))
        self.assertIsNotNone(self.patient.person.internal_attribution_log)
        self.assertEqual(
            self.patient.person.internal_attribution_log.status, AbstractAttributionData.STATUS_NOT_ATTRIBUTED
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        # Verify that discharging twice does not impact the data
        remove_person_from_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.person.internal_attribution_log.refresh_from_db()
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(
            self.patient.person.internal_attribution_log.status, AbstractAttributionData.STATUS_NOT_ATTRIBUTED
        )
        self.assertIsNotNone(self.patient.person.internal_attribution_log.end_date)
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)

    def test_attribution_logs_for_plan_contract(self):
        # Not a real situation currently
        # adding tests as a safe guard
        # Attribution logs should only be present for payer or employer pmpm contracts
        plan_contract = Contract.objects.create(
            config={
                "payer_id": self.patient.person.insurance_info.insurance_payer.id,
                "plan_description_specific": True,
                "attribution_type": ContractAttributionType.POC_FORM,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.patient.person.insurance_info.insurance_plan),
            contracted_entity=self.patient.person.insurance_info.insurance_plan,
        )
        self.update_attribution(
            person=self.patient.person,
            attribution_requirement_type=AttributionRequirementType.POC_FORM,
            contract=plan_contract,
        )
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        self.patient.onboarding_state.poc_completed_at = timezone.now()
        self.patient.onboarding_state.save()
        self.patient.person.insurance_info.refresh_from_db()
        self.patient.person.attribution.refresh_from_db()
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.person.attribution.status, AttributionConstants.States.CONTRACTED)
        remove_person_from_program(self.patient.person, ProgramCodes.PRIMARY_CARE, data={"reason": "discharge"})
        self.patient.refresh_from_db()

    def test_discharge_user_without_contract(self):
        person: Person = PersonUserFactory()
        self.update_attribution(
            person=person, attribution_requirement_type=AttributionRequirementType.POC_FORM, contract=None
        )
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        person.user.onboarding_state.to_signedup(actor=person.user)
        person.user.onboarding_state.poc_completed_at = timezone.now()
        person.user.onboarding_state.save()
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)
        remove_person_from_program(self.patient.person, ProgramCodes.PRIMARY_CARE, data={"reason": "discharge"})
        person.user.refresh_from_db()
        person.insurance_info.refresh_from_db()
        person.attribution.refresh_from_db()
        self.assertEqual(person.attribution.status, AttributionConstants.States.INIT)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    def test_reactivate_user(self, mock_update_auth0_user):
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": False})
        self.patient.refresh_from_db()
        self.assertFalse(self.patient.disabled)


class OnboardingAPIStatusTestCase(FireflyTestCase):
    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    def test_set_status(self, mock_update_auth0_user):
        self.provider_client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "member"})
        self.patient.onboarding_state.refresh_from_db()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.MEMBER)
        transition_log = PatientTransitionLog.objects.filter(person=self.patient.person).order_by("-created_at").first()

        self.assertIsNotNone(self.patient.onboarding_state.member_at or None)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.MEMBER)
        self.assertEqual(self.patient.onboarding_state.member_updated_by, self.provider)

        self.assertEqual(transition_log.destination, OnboardingStatus.MEMBER)

        self.provider_client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "signedup"})
        self.patient.onboarding_state.refresh_from_db()
        self.assertIsNotNone(self.patient.onboarding_state.signedup_at)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertEqual(self.patient.onboarding_state.signedup_updated_by, self.provider)

        # Test automatic clearing of churned fields
        self.provider_client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "signedup"})
        self.patient.onboarding_state.refresh_from_db()
        self.assertIsNotNone(self.patient.onboarding_state.signedup_at)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertEqual(self.patient.onboarding_state.signedup_updated_by, self.provider)

        response = self.provider_client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "enrolled"})
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {"detail": "Patient does not have access"})
        self.patient.onboarding_state.refresh_from_db()
        self.assertIsNotNone(self.patient.onboarding_state.signedup_at)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertEqual(self.patient.onboarding_state.signedup_updated_by, self.provider)

        self.provider_client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "deactivated"})
        self.patient.onboarding_state.refresh_from_db()
        self.assertIsNotNone(self.patient.onboarding_state.deactivated_at)
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.DEACTIVATED)
        self.assertEqual(self.patient.onboarding_state.deactivated_updated_by, self.provider)

        response = self.client.post(f"/onboarding/set_status/{self.patient.id}/", {"status": "deactivated"})
        self.assertEqual(response.status_code, 200)


class OnboardingMachineTriggerTestCase(FireflyTestCase):
    def test_signup_complete(self):
        # noop signed up
        person = PersonUserFactory()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.INITIALIZED)
        self.provider_client.post(f"/onboarding/{person.user.id}/event/", {"event": "signup_complete"})
        person.user.onboarding_state.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.SIGNEDUP)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number", return_value="**********")
    def test_benefit_signup_complete(self, mock_phone_number):
        # Benefit-only patients should be placed in the coverage pod
        pod, _ = Pod.objects.get_or_create(uid=PodUIDs.COVERAGE_ONLY)
        mg_provider_1 = ProviderDetailFactory()
        mg_provider_2 = ProviderDetailFactory()
        member_guide, _ = Group.objects.get_or_create(name=MEMBER_GUIDE_COVERAGE_ROLE)
        member_guide.user_set.add(mg_provider_1.user)
        member_guide.user_set.add(mg_provider_2.user)
        user = User.objects.create(email="<EMAIL>")
        patient_create_payload = {
            "created_from": "app",
            "dob": "1988-01-01",
            "sex": "Male",
            "gender": ["Man"],
            "pronouns": "He/Him/His",
            "first_name": "Indiana",
            "last_name": "Jones",
            "phone_number": "**********",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.firefly_payer.id,
                "member_id": None,
            },
            "address": {"zip": "02139"},
            "patient_referral_program": "",
            "consent_forms": [self.consent_form.id],
            "programs": [ProgramCodes.BENEFIT],
        }
        client = FireflyTestCase.get_member_client(member=user)
        response = client.post("/bff/app/signup/member-id-confirmation/", patient_create_payload, format="json")
        self.assertEqual(response.status_code, 200)
        user.refresh_from_db()
        self.assertEqual(user.onboarding_state.status, OnboardingStatus.MEMBER)
        self.assertTrue(user.person.care_team.filter(user__groups__name=MEMBER_GUIDE_COVERAGE_ROLE).exists())
        self.assertEqual(list(user.person.pods.all().values_list("id", flat=True)), [pod.id])


class OnboardingSideEffectTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        self.patient_1 = PersonUserFactory.create(account_verified=True).user
        add_person_to_program(self.patient_1.person, ProgramCodes.PRIMARY_CARE)
        self.patient_2 = PersonUserFactory.create(account_verified=True).user
        add_person_to_program(self.patient_2.person, ProgramCodes.PRIMARY_CARE)
        self.patient_3 = PersonUserFactory.create(account_verified=True).user
        add_person_to_program(self.patient_3.person, ProgramCodes.PRIMARY_CARE)
        self.patient_4 = PersonUserFactory.create(account_verified=True).user
        add_person_to_program(self.patient_4.person, ProgramCodes.PRIMARY_CARE)

        self.onboarding_state_1 = self.patient_1.onboarding_state
        self.onboarding_state_2 = self.patient_2.onboarding_state
        self.onboarding_state_3 = self.patient_3.onboarding_state
        CaseCategory.objects.get_or_create(title=ONBOARDING_OUTREACH_COVERAGE_ONLY)
        CaseCategory.objects.get_or_create(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH)
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW, title=INSURANCE_PLAN_NEEDS_REVIEW)

        try:
            # TODO (Kunal): Change how we reference bot users to not use name fields
            User.objects.get(first_name="Referrals", last_name="Group")
        except User.DoesNotExist:
            # TODO (Kunal): Change how we reference bot users to not use name fields
            ProviderDetailFactory.create(user__first_name="Referrals", user__last_name="Group")
        self.task_collection, _ = TaskCollection.objects.get_or_create(title=COVERAGE_INTAKE_SURVEY)
        form, _ = Form.objects.get_or_create(uid=FormUID.INTAKE_SURVEY_FORM)
        self.coverage_intake_template, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.COVERAGE_INTAKE_SURVEY,
            defaults={
                "task_collection": self.task_collection,
                "title": "Quick Coverage Check",
                "form": form,
                "source_type": SOURCE_TYPES["formsubmission"],
                "assign_to_patient": True,
            },
        )
        self.task_collection_2, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")

        form, _ = Form.objects.get_or_create(uid=FormUID.SEGMENTATION_CAPTURE)
        self.segmentation_template, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE,
            defaults={
                "task_collection": self.task_collection_2,
                "title": "Segmentation Capture",
                "form": form,
                "source_type": SOURCE_TYPES["formsubmission"],
            },
        )

    def test_initial_ob_state_tenant_autoenroll(self):
        """For a Tenant which skips onboarding, expect to move to the ENROLLED
        state after signing up"""
        tenant = TenantFactory(feature_access_config={Features.onboarding.value: False})
        self.patient_1.person.tenants.set([tenant])
        self.onboarding_state_1.status = OnboardingStatus.SIGNEDUP
        self.onboarding_state_1.signedup_at = None
        self.onboarding_state_1.signedup_updated_by = None
        self.onboarding_state_1.save()
        self.patient_1.onboarding_state.signup_complete(actor=self.patient_1)
        self.patient_1.onboarding_state.refresh_from_db()
        self.assertEqual(self.onboarding_state_1.status, OnboardingStatus.ENROLLED)
        self.assertEqual(self.onboarding_state_1.enrolled_updated_by, self.patient_1)
        self.assertIsNotNone(self.onboarding_state_1.enrolled_at)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.send_signup_welcome_message")
    def test_send_welcome_message(self, mock_notification):
        welcome_templates = ChatMessageTemplate.objects.filter(uid__startswith="welcome.").values_list(
            "template_text", flat=True
        )
        self.patient_1.onboarding_state.signup_complete()
        mock_notification.assert_called_once_with(self.patient_1, list(welcome_templates), None)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.add_onboarding_journey_virtual_tasks_to_patient")
    def test_send_onboarding_journey_virtual_tasks_without_insurance_form(self, mock_notification):
        patient = PersonUserFactory.create(account_verified=True).user
        task_collection, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")
        TaskCollectionTask.objects.create(
            title="Test task collection task", task_collection=task_collection, days_offset=0
        )
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        patient.onboarding_state.signup_complete(actor=self.patient_1)

        mock_notification.assert_called_once_with(patient)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.add_onboarding_journey_virtual_tasks_to_patient")
    def test_send_onboarding_journey_virtual_tasks(self, mock_notification):
        task_collection, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")
        TaskCollectionTask.objects.create(
            title="Test task collection task", task_collection=task_collection, days_offset=0
        )
        self.patient_1.onboarding_state.signup_complete()
        mock_notification.assert_called_once_with(self.patient_1)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.send_signup_welcome_message")
    def test_send_signup_welcome_message(self, mock_notification):
        pcp_signup = SignupReasons.objects.create(
            label="Meet with PCP", sort_order=1, onboarding_welcome_message="PCP Message"
        )

        self.assertEqual(self.patient_4.onboarding_state.status, OnboardingStatus.INITIALIZED)
        self.patient_4.onboarding_state.signup_reason = pcp_signup
        self.patient_4.onboarding_state.signup_complete()
        mock_notification.assert_called_with(self.patient_4, ["PCP Message"], None)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.OnboardingGuideNotifier.new_signup")
    def test_send_new_patient_sms(self, mock_notification):
        self.assertEqual(self.patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        self.patient_1.onboarding_state.signup_complete()
        mock_notification.assert_called_once_with(self.patient_1)

    def test_create_medical_record_release_task_on_appointment_booking(self):
        add_person_to_program(person=self.patient_2.person, program_uid=ProgramCodes.PRIMARY_CARE)
        tasks = self.med_release_task_collection.tasks.all()
        self.patient_2.onboarding_state.to_member(actor=self.patient_2)
        for task in tasks:
            self.assertEquals(Task.objects.filter(patient=self.patient_2, title=task.title).count(), 0)
        self.assertEquals(FormSubmission.objects.filter(user=self.patient_2, form=self.med_release_form).count(), 0)

        with self.captureOnCommitCallbacks(execute=True):
            # booking appointment would assign the med release form
            Appointment.objects.create(
                patient=self.patient_2,
                elation_id=34523452345,
                physician=self.physician,
                start=(timezone.now() + timedelta(days=2)),
                duration="00:15:00",
                time_slot_type="appointment_slot",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        for task in tasks:
            self.assertEquals(Task.objects.filter(patient=self.patient_2, title=task.title).count(), 1)
        self.assertEquals(FormSubmission.objects.filter(user=self.patient_2, form=self.med_release_form).count(), 1)

    def test_create_medical_record_release_task_on_awv_new_booking(self):
        add_person_to_program(person=self.patient_2.person, program_uid=ProgramCodes.PRIMARY_CARE)
        tasks = self.med_release_task_collection.tasks.all()
        self.patient_2.onboarding_state.to_member(actor=self.patient_2)
        for task in tasks:
            self.assertEquals(Task.objects.filter(patient=self.patient_2, title=task.title).count(), 0)
        self.assertEquals(FormSubmission.objects.filter(user=self.patient_2, form=self.med_release_form).count(), 0)

        with self.captureOnCommitCallbacks(execute=True):
            # booking appointment would assign the med release form
            Appointment.objects.create(
                patient=self.patient_2,
                elation_id=34523452345,
                physician=self.physician,
                start=(timezone.now() + timedelta(days=2)),
                duration="00:15:00",
                time_slot_type="appointment_slot",
                reason=AppointmentReason.AWV_NEW,
                status=AppointmentStatus.SCHEDULED.value,
            )
        for task in tasks:
            self.assertEquals(Task.objects.filter(patient=self.patient_2, title=task.title).count(), 1)
        self.assertEquals(FormSubmission.objects.filter(user=self.patient_2, form=self.med_release_form).count(), 1)

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.add_zingle_contact.send_with_options")
    def test_add_to_zingle_list(self, mock_add_zingle_contact):
        self.patient.test_only = True
        self.patient.save()
        self.patient.refresh_from_db()

        add_person_to_program(person=self.patient_2.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.person.eligible(actor=self.patient)
        mock_add_zingle_contact.assert_not_called()

        # Use preferred name
        self.patient.test_only = False
        self.patient.save()
        self.patient.person.preferred_name = "Preferred"
        self.patient.person.save()
        self.patient.refresh_from_db()

        self.patient.person.eligible(actor=self.patient)
        mock_add_zingle_contact.assert_called_with(
            args=(
                str(self.patient.id),
                self.patient.person.preferred_name,
                self.patient.person.last_name,
                self.patient.person.phone_number,
                int(self.patient.onboarding_state.member_at.timestamp()),
            ),
            delay=2000,
        )

        # When preferred name is null, use first name
        self.patient.person.preferred_name = None
        self.patient.person.save()
        self.patient.person.refresh_from_db()

        self.patient.person.eligible(actor=self.patient)
        mock_add_zingle_contact.assert_called_with(
            args=(
                str(self.patient.id),
                self.patient.person.first_name,
                self.patient.person.last_name,
                self.patient.person.phone_number,
                int(self.patient.onboarding_state.member_at.timestamp()),
            ),
            delay=2000,
        )

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.add_zingle_contact.send_with_options")
    def test_add_to_zingle_list_on_member_transition(self, mock_add_zingle_contact):
        person = PersonUserFactory()
        person.user.test_only = False
        person.user.save()
        person.user.refresh_from_db()

        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.person.eligible()
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.MEMBER)
        # Called without erroring on member_at
        mock_add_zingle_contact.assert_called()

    @mock.patch("firefly.modules.onboarding.statemachine.side_effects.add_zingle_contact.send_with_options")
    def test_add_to_zingle_list_programs(self, mock_add_zingle_contact):
        person = PersonUserFactory()
        person.user.test_only = False
        person.user.save()
        person.user.refresh_from_db()

        add_person_to_program(person=person, program_uid=ProgramCodes.URGENT_CARE)
        person.save()
        person.refresh_from_db()

        person.user.onboarding_state.to_member(actor=person.user)
        mock_add_zingle_contact.assert_not_called()

    def test_assign_intake_survey_form(self):
        # Coverage only test
        person_1 = PersonUserFactory.create()
        patient_1 = person_1.user
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        patient_1.person.insurance_info.state = "ME"
        patient_1.person.insurance_info.save()
        self.assertEqual(patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        patient_1.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        form = Form.objects.get(uid=FormUID.INTAKE_SURVEY_FORM)
        segmentation_form, _ = Form.objects.get_or_create(uid=FormUID.SEGMENTATION_CAPTURE)
        form_submission = FormSubmission.objects.get(user=patient_1, form=form, completed_at=None)
        self.assertIsNotNone(form_submission)
        intake_task = Task.objects.get(
            patient_id=patient_1.id,
            title="Quick Coverage Check",
            autocreated_from=self.coverage_intake_template,
            source_type=SOURCE_TYPES["formsubmission"],
        )
        self.assertIsNotNone(intake_task)
        form_submission = FormSubmission.objects.filter(user=patient_1)

        self.assertEqual(form_submission.count(), 1)

        # Coverage + Care only test
        person_1 = PersonUserFactory.create()
        patient_1 = person_1.user
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        add_person_to_program(person_1, ProgramCodes.PRIMARY_CARE)
        patient_1.person.insurance_info.state = "ME"
        patient_1.person.insurance_info.save()
        self.assertEqual(patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        patient_1.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        form = Form.objects.get(uid=FormUID.INTAKE_SURVEY_FORM)
        form_submission = FormSubmission.objects.get(user=patient_1, form=form, completed_at=None)
        self.assertIsNotNone(form_submission)
        intake_task = Task.objects.get(
            patient_id=patient_1.id,
            title="Quick Coverage Check",
            autocreated_from=self.coverage_intake_template,
            source_type=SOURCE_TYPES["formsubmission"],
        )
        self.assertIsNotNone(intake_task)

        segmentation_form, _ = Form.objects.get_or_create(uid=FormUID.SEGMENTATION_CAPTURE)
        seg_form_submission = FormSubmission.objects.get(user=patient_1, form=segmentation_form, completed_at=None)
        self.assertIsNotNone(seg_form_submission)
        segmentation_task = Task.objects.get(
            patient_id=patient_1.id,
            autocreated_from=self.segmentation_template,
            source_type=SOURCE_TYPES["formsubmission"],
        )

        self.assertIsNotNone(segmentation_task)
        form_submission = FormSubmission.objects.filter(user=patient_1)
        self.assertEqual(form_submission.count(), 2)
        segmentation_capture_task = TaskCollectionTask.objects.get(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE
        )
        self.assertEqual(segmentation_capture_task.task_collection, self.task_collection_2)
        coverage_survey = TaskCollection.objects.get(title=COVERAGE_INTAKE_SURVEY).tasks
        self.assertEqual(coverage_survey.count(), 1)

    def test_assign_intake_survey_form_owner_group(self):
        # Coverage only test
        person_1 = PersonUserFactory.create()
        patient_1 = person_1.user
        create_update_assignee_group_from_user(person_1.user)
        created_by = AssigneeGroup.objects.get(name="Referrals Group")
        created_by.unique_key = "Referrals Group"
        created_by.save()
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        patient_1.person.insurance_info.state = "ME"
        patient_1.person.insurance_info.save()
        self.assertEqual(patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        patient_1.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        form = Form.objects.get(uid=FormUID.INTAKE_SURVEY_FORM)
        form_submission = FormSubmission.objects.get(user=patient_1, form=form, completed_at=None)
        self.assertIsNotNone(form_submission)
        intake_task = Task.objects.get(
            patient_id=patient_1.id,
            title="Quick Coverage Check",
            autocreated_from=self.coverage_intake_template,
            source_type=SOURCE_TYPES["formsubmission"],
        )
        self.assertIsNotNone(intake_task)

        # Coverage + Care only test
        person_1 = PersonUserFactory.create()
        patient_1 = person_1.user
        create_update_assignee_group_from_user(person_1.user)
        created_by = AssigneeGroup.objects.get(name="Referrals Group")
        created_by.unique_key = "Referrals Group"
        created_by.save()
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        add_person_to_program(person_1, ProgramCodes.PRIMARY_CARE)
        patient_1.person.insurance_info.state = "ME"
        patient_1.person.insurance_info.save()
        self.assertEqual(patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        patient_1.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        form = Form.objects.get(uid=FormUID.INTAKE_SURVEY_FORM)
        form_submission = FormSubmission.objects.get(user=patient_1, form=form, completed_at=None)
        self.assertIsNotNone(form_submission)
        intake_task = Task.objects.get(
            patient_id=patient_1.id,
            title="Quick Coverage Check",
            autocreated_from=self.coverage_intake_template,
            source_type=SOURCE_TYPES["formsubmission"],
        )
        self.assertIsNotNone(intake_task)

    def _move_patient_to_covered(self):
        self.patient_1.person.insurance_info.state = "MA"
        self.patient_1.person.insurance_info.save()
        self.assertEqual(self.patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        self.patient_1.onboarding_state.trigger(OnboardingTransitions.INSURANCE_COVERED)

    def test_create_onboarding_outreach_case_for_coverage_only_members_owner_group(self):
        # Coverage only test
        person_1 = PersonUserFactory.create()
        patient_1 = person_1.user
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        patient_1.person.insurance_info.state = "ME"
        patient_1.person.insurance_info.save()
        care_coordinator_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["care_coordinator"]
        owner_group, created = AssigneeGroup.objects.get_or_create(unique_key=care_coordinator_unique_key)
        self.assertEqual(patient_1.onboarding_state.status, OnboardingStatus.INITIALIZED)
        patient_1.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        category = CaseCategory.objects.get(title=ONBOARDING_OUTREACH_COVERAGE_ONLY)
        case = Case.objects.get(person=person_1, category=category, owner_group=owner_group)
        self.assertIsNotNone(case)

    @mock.patch(
        "firefly.modules.onboarding.tasks.create_onboarding_case_for_preferred_language_non_english.send_with_options",
    )
    @mock.patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
    def test_async_case_creation_preferred_language(
        self, elation_update_user_mock, create_onboarding_case_for_preferred_language_non_english_mock
    ):
        with self.captureOnCommitCallbacks(execute=True):
            person = PersonFactory.create(preferred_language=PreferredLanguageFactory(name="Japanese"))
        create_onboarding_case_for_preferred_language_non_english_mock.assert_not_called()
        with self.captureOnCommitCallbacks(execute=True):
            person = PersonUserFactory.create(preferred_language=PreferredLanguageFactory())
        create_onboarding_case_for_preferred_language_non_english_mock.assert_called_once()
        create_onboarding_case_for_preferred_language_non_english_mock.reset_mock()
        with self.captureOnCommitCallbacks(execute=True):
            person.preferred_language = PreferredLanguageFactory(name="Spanish")
            person.save()
        create_onboarding_case_for_preferred_language_non_english_mock.assert_called_once()

    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    @mock.patch.object(ConcurrentRateLimiter, "_acquire", return_value=True)
    def test_create_case_for_english_preferred_language(self, acquire_mock, backend_mock):
        person = PersonUserFactory.create(preferred_language=PreferredLanguageFactory())
        patient = person.user

        create_onboarding_case_for_preferred_language_non_english(patient.pk)

        category = CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH)

        case = Case.objects.filter(category=category, person=person)

        self.assertEqual(len(case), 0)

    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    @mock.patch.object(ConcurrentRateLimiter, "_acquire", return_value=True)
    def test_create_case_for_non_english_preferred_language(self, acquire_mock, backend_mock):
        person = PersonUserFactory.create()
        patient = person.user
        person.preferred_language = PreferredLanguageFactory(name="Other")
        person.preferred_language_other = "Hebrew"
        person.save()

        self.assertEqual(patient.onboarding_state.status, OnboardingStatus.INITIALIZED)

        create_onboarding_case_for_preferred_language_non_english(patient.pk)

        category = CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_PREFERRED_LANG_NON_ENGLISH)

        case = Case.objects.filter(category=category, person=person)

        self.assertEqual(len(case), 1)


class TransitionsTest(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.patient.person.attribution.contract = self.contract
        self.patient.person.attribution.save(update_fields=["contract"])

    def test_signup_complete_on_home_screen(self):
        enrollment_period = (datetime.now(UTC_TIMEZONE) - timedelta(days=1), None)
        # Care-Only
        primary_care_person = PersonUserFactory()
        primary_care_user = primary_care_person.user
        add_person_to_program(primary_care_person, ProgramCodes.PRIMARY_CARE)
        self.assertNotEqual(primary_care_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        primary_care_user.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(primary_care_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertIsNotNone(primary_care_user.onboarding_state.signedup_at)
        # Coverage-Only
        benefit_person = PersonUserFactory()
        benefit_user = benefit_person.user
        add_person_to_program(benefit_person, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        self.assertNotEqual(benefit_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        benefit_user.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(benefit_user.onboarding_state.status, OnboardingStatus.MEMBER)
        self.assertIsNotNone(benefit_user.onboarding_state.member_at)
        # Care+Coverage
        primary_care_benefit_person = PersonUserFactory()
        primary_care_benefit_user = primary_care_benefit_person.user
        add_person_to_program(primary_care_benefit_person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(
            primary_care_benefit_person, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period}
        )
        self.assertNotEqual(primary_care_benefit_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        primary_care_benefit_user.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(primary_care_benefit_user.onboarding_state.status, OnboardingStatus.MEMBER)
        self.assertIsNotNone(primary_care_benefit_user.onboarding_state.member_at)
        # No Program
        programless_person = PersonUserFactory()
        programless_user = programless_person.user
        self.assertNotEqual(programless_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        programless_user.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(programless_user.onboarding_state.status, OnboardingStatus.SIGNEDUP)

    def test_signup_complete_no_program(self):
        person = PersonUserFactory.create()
        patient = person.user
        self.assertFalse(person_is_in_program(person, ProgramCodes.PRIMARY_CARE))
        self.assertFalse(person_is_in_program(person, ProgramCodes.BENEFIT))
        patient.onboarding_state.to_signedup()
        patient.onboarding_state.save()
        patient.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)

    def test_signup_complete_primary_care(self):
        person = PersonUserFactory.create()
        patient = person.user
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        patient.onboarding_state.to_signedup()
        patient.onboarding_state.save()
        patient.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)

    def test_signup_complete_benefit(self):
        # Test coverage only
        person = PersonUserFactory.create()
        patient = person.user
        add_person_to_program(person, ProgramCodes.BENEFIT)
        patient.onboarding_state.to_signedup()
        patient.onboarding_state.save()
        patient.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(patient.onboarding_state.status, OnboardingStatus.MEMBER)
        # Test care + coverage
        person = PersonUserFactory.create()
        patient = person.user
        add_person_to_program(person, ProgramCodes.BENEFIT)
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        patient.onboarding_state.to_signedup()
        patient.onboarding_state.save()
        patient.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        self.assertEqual(patient.onboarding_state.status, OnboardingStatus.MEMBER)

    @mock.patch("firefly.modules.onboarding.statemachine.utils.set_eligibility_check")
    def test_covered_insurance_transition(self, mock_set_eligibility_check):
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()
        self.patient.onboarding_state.status = OnboardingStatus.INITIALIZED
        self.patient.onboarding_state.save()
        self.patient.person.coverage = CoverageStates.INCOMPLETE_INITIAL
        self.patient.person.save()

        self.patient.person.eligible()
        mock_set_eligibility_check.assert_called_once()
        mock_set_eligibility_check.reset_mock()
        # moving again to eligible should not trigger the methods
        self.patient.person.refresh_from_db()
        self.assertEqual(self.patient.person.coverage, CoverageStates.COVERED_INSURANCE)
        self.patient.person.eligible()
        mock_set_eligibility_check.assert_not_called()
        mock_set_eligibility_check.reset_mock()
        self.patient.onboarding_state.insurance_covered()
        mock_set_eligibility_check.assert_not_called()
        self.patient.person.insurance_info.refresh_from_db()

        self.assertEquals(self.patient.onboarding_state.status, OnboardingStatus.MEMBER)
        self.assertEquals(self.patient.person.coverage, CoverageStates.COVERED_INSURANCE)
        self.patient.onboarding_state.insurance_covered()
        mock_set_eligibility_check.assert_not_called()

    @mock.patch("firefly.modules.onboarding.statemachine.utils.set_eligibility_check")
    def test_covered_insurance_transition_with_on_enter_member_migration(self, mock_set_eligibility_check):
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()
        self.patient.onboarding_state.to_initialized(actor=self.patient)
        self.patient.person.coverage = CoverageStates.INCOMPLETE_INITIAL
        self.patient.person.save()
        self.patient.person.eligible()
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.person.coverage, CoverageStates.COVERED_INSURANCE)
        mock_set_eligibility_check.assert_called_once()

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    @patch(
        "firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._recalculate_attribution_and_contract"
    )
    @patch("firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._unsubscribe_from_zingle_list")
    @patch("firefly.modules.onboarding.statemachine.side_effects.schedule_account_deletion_notification")
    def test_account_deactivated_transition(
        self,
        mock_schedule_account_deletion_notification,
        mock_unsubscribe_zingle_contact,
        mock_recalculate_attribution_and_contract,
        mock_update_auth0_user,
    ):
        # person without established care will be removed from PCP program as part of deactivation.
        self.assertFalse(patient_has_established_care(self.patient))
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.patient.onboarding_state.account_deactivated()
        self.assertFalse(person_is_in_program(program_uid=ProgramCodes.PRIMARY_CARE, person=self.patient.person))
        mock_unsubscribe_zingle_contact.assert_called_once()
        # recalculate attribution is called once as part of program enrollment churned side effects
        self.assertEqual(mock_recalculate_attribution_and_contract.call_count, 1)
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": True})
        self.assertEqual(self.patient.userdevices.count(), 0)
        self.assertTrue(self.patient.disabled)
        mock_schedule_account_deletion_notification.assert_called_with(self.patient)

    @patch("firefly.modules.onboarding.statemachine.side_effects.update_auth0_user")
    def test_account_reactivated_transition(self, mock_update_auth0_user):
        self.patient.onboarding_state.to_deactivated(actor=self.patient)
        self.patient.onboarding_state.account_reactivated()
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": False})
        self.patient.refresh_from_db()
        self.assertFalse(self.patient.disabled)


class StateMachineUtilsTestCase(FireflyTestCase):
    @patch("firefly.modules.onboarding.statemachine.side_effects.assign_member_guide")
    @patch("firefly.modules.onboarding.statemachine.side_effects.send_welcome_reason_message")
    def test_handle_primary_care_or_coverage_member_signup_complete(
        self, mock_handle_primary_care_or_benefit_member_activated, mock_handle_benefit_member_activated
    ):
        enrollment_period = (datetime.now(UTC_TIMEZONE) - timedelta(days=1), None)
        #  1a. Member signs up for care, then is enrolled in coverage -> handle care, then benefit-specific
        person_1a = PersonUserFactory()
        add_person_to_program(person_1a, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_1a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        person_1a.refresh_from_db()
        add_person_to_program(person_1a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        #  1b. Member signs up for care, then is enrolled in care -> handle care, then nothing
        person_1b = PersonUserFactory()
        program_info_1b = add_person_to_program(
            person_1b, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period}
        )
        self.provider_client.post(f"/onboarding/{person_1b.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        program_info_1b.on_enroll(data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        #  2a. Member signs up for coverage, then is enrolled in coverage -> handle benefit, then nothing
        person_2a = PersonUserFactory()
        program_info_2a = add_person_to_program(
            person_2a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period}
        )
        self.provider_client.post(f"/onboarding/{person_2a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        person_2a.refresh_from_db()
        program_info_2a.on_enroll(data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        #  2b. Member signs up for coverage, then is enrolled in care -> handle benefit, then care-specific (nothing)
        person_2b = PersonUserFactory()
        add_person_to_program(person_2b, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_2b.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        add_person_to_program(person_2b, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        #  3a. Member signs up for care+coverage, then is enrolled in coverage  -> handle care and benefit, then nothing
        person_3a = PersonUserFactory()
        add_person_to_program(person_3a, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        program_info_3a = add_person_to_program(
            person_3a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period}
        )
        self.provider_client.post(f"/onboarding/{person_3a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        person_3a.refresh_from_db()
        program_info_3a.on_enroll(data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        #  3b. Member signs up for care+coverage, then is enrolled in care -> handle care and benefit, then nothing
        person_3a = PersonUserFactory()
        program_info_3a = add_person_to_program(
            person_3a, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period}
        )
        add_person_to_program(person_3a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_3a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        person_3a.refresh_from_db()
        program_info_3a.on_enroll(data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        #  4a. Member is enrolled in care, then signs up for coverage -> nothing, then handle care and benefit
        person_4a = PersonUserFactory()
        add_person_to_program(person_4a, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        add_person_to_program(person_4a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_4a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        #  4b. Member is enrolled in care, then signs up for care -> nothing, then handle care
        person_4b = PersonUserFactory()
        program_info_4b = add_person_to_program(
            person_4b, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period}
        )
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        program_info_4b.on_enroll(data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_4b.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        #  5a. Member is enrolled in coverage, then signs up for coverage -> nothing, then handle benefit
        person_5a = PersonUserFactory()
        program_info_5a = add_person_to_program(
            person_5a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period}
        )
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        program_info_5a.on_enroll(data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_5a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        #  5b. Member is enrolled in coverage, then signs up for care -> nothing, then handle care and benefit
        person_5b = PersonUserFactory()
        add_person_to_program(person_5b, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        add_person_to_program(person_5b, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_5b.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        #  6a. Member is enrolled in care+coverage, then signs up for coverage -> nothing, then handle care and benefit
        person_6a = PersonUserFactory()
        add_person_to_program(person_6a, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period})
        program_info_6a = add_person_to_program(
            person_6a, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period}
        )
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        program_info_6a.on_enroll(data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_6a.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()
        #  6b. Member is enrolled in care+coverage, then signs up for care -> nothing, then handle care and benefit
        person_6b = PersonUserFactory()
        program_info_6b = add_person_to_program(
            person_6b, ProgramCodes.PRIMARY_CARE, data={"enrollment_period": enrollment_period}
        )
        add_person_to_program(person_6b, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_not_called()
        mock_handle_benefit_member_activated.reset_mock()
        program_info_6b.on_enroll(data={"enrollment_period": enrollment_period})
        self.provider_client.post(f"/onboarding/{person_6b.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        mock_handle_benefit_member_activated.assert_called_once()
        mock_handle_benefit_member_activated.reset_mock()

    @patch("firefly.modules.onboarding.statemachine.utils._handle_primary_care_or_benefit_member_activated")
    def test_handle_primary_care_member_signup_complete(self, mock_handle_primary_care_or_benefit_member_activated):
        # [T] 1. signed up | [T] 2. enrolled in primary care program  ->  Trigger side effects
        person_0 = PersonUserFactory()
        person_0.user.onboarding_state.to_signedup(actor=person_0.user)
        add_person_to_program(person_0, ProgramCodes.PRIMARY_CARE)
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [T] 1. enrolled in primary care program | [T] 2. signed up  ->  Trigger side effects
        person_1 = PersonUserFactory()
        add_person_to_program(person_1, ProgramCodes.PRIMARY_CARE)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        self.provider_client.post(f"/onboarding/{person_1.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [T] 1. enrolled in primary care program | [F] 2. signed up  ->  No-op
        person_2 = PersonUserFactory()
        add_person_to_program(person_2, ProgramCodes.PRIMARY_CARE)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [F] 1. enrolled in primary care program | [T] 2. signed up  ->  No-op
        person_3 = PersonUserFactory()
        self.provider_client.post(f"/onboarding/{person_3.user.id}/event/", {"event": "signup_complete"})
        handle_primary_care_member_activated(person_3.user)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [F] 1. enrolled in primary care program | [F] 2. signed up  ->  No-op
        person_4 = PersonUserFactory()
        handle_primary_care_member_activated(person_4.user)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()

    @patch("firefly.modules.onboarding.statemachine.utils._handle_primary_care_or_benefit_member_activated")
    def test_handle_benefit_member_signup_complete(self, mock_handle_primary_care_or_benefit_member_activated):
        enrollment_period = (datetime.now(UTC_TIMEZONE) - timedelta(days=1), None)
        # [T] 1. signed up | [T] 2. enrolled in benefit program  ->  Trigger side effects
        person_0 = PersonUserFactory()
        person_0.user.onboarding_state.to_signedup(actor=person_0.user)
        add_person_to_program(person_0, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [T] 1. enrolled in benefit program | [T] 2. signed up  ->  Trigger side effects
        person_1 = PersonUserFactory()
        add_person_to_program(person_1, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        self.provider_client.post(f"/onboarding/{person_1.user.id}/event/", {"event": "signup_complete"})
        mock_handle_primary_care_or_benefit_member_activated.assert_called_once()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [T] 1. enrolled in benefit program | [F] 2. signed up  ->  No-op
        person_2 = PersonUserFactory()
        add_person_to_program(person_2, ProgramCodes.BENEFIT, data={"enrollment_period": enrollment_period})
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [F] 1. enrolled in benefit program | [T] 2. signed up  ->  No-op
        person_3 = PersonUserFactory()
        self.provider_client.post(f"/onboarding/{person_3.user.id}/event/", {"event": "signup_complete"})
        handle_benefit_member_activated(person_3.user)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
        mock_handle_primary_care_or_benefit_member_activated.reset_mock()
        # [F] 1. enrolled in benefit program | [F] 2. signed up  ->  No-op
        person_4 = PersonUserFactory()
        handle_benefit_member_activated(person_4.user)
        mock_handle_primary_care_or_benefit_member_activated.assert_not_called()
