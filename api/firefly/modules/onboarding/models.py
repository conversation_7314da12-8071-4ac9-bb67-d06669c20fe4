import logging

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON><PERSON><PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django_deprecate_fields import deprecate_field

from firefly.modules.facts.mixin import BaseFact
from firefly.modules.facts.models import ReferralSource
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.onboarding.statemachine.conditions import OnboardingStateConditions
from firefly.modules.onboarding.statemachine.constants import ONBOARDING_CHOICES
from firefly.modules.onboarding.statemachine.mixin import OnboardingStateMachineMixin
from firefly.modules.onboarding.statemachine.side_effects import OnboardingStateSideEffects
from firefly.modules.signup_reasons.models import SignupReasons

from .constants import CHURNED_DISCHARGED_REASON_CHOICES, DEACTIVATED_REASON_CHOICES, DISCHARGED_REASON_CHOICES

logger = logging.getLogger(__name__)


class OnboardingState(
    SaveHandlersMixin, BaseModelV3, OnboardingStateMachineMixin, OnboardingStateConditions, OnboardingStateSideEffects
):
    """Represents current membership status, and state of tasks required to move between states."""

    patient = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        null=True,
        related_name="onboarding_state",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Signed Up to Activated time stamps
    downloaded_app_at = models.DateTimeField(null=True, blank=True)
    insurance_eligibility_at = models.DateTimeField(null=True, blank=True)

    # Member
    care_team_selection_at = models.DateTimeField(null=True, blank=True)
    poc_sent_at = models.DateTimeField(null=True, blank=True)
    poc_completed_at = models.DateTimeField(null=True, blank=True)
    medical_record_release_at = models.DateTimeField(null=True, blank=True)

    # Removed from UI
    intake_call_at = models.DateTimeField(null=True, blank=True)

    # Churned Reasons time stamps
    #
    # Please update analytics if churned reasons fields change,
    # *especially* if fields are deprecated! Deprecation is likely
    # to break analytics pipelines.
    #
    insurance_not_covered_at = models.DateTimeField(null=True, blank=True)
    insurance_switched_pcp_at = models.DateTimeField(null=True, blank=True)
    deceased_at = models.DateTimeField(null=True, blank=True)
    unhappy_with_service_at = models.DateTimeField(null=True, blank=True)
    not_interested_in_service_at = models.DateTimeField(null=True, blank=True)
    looking_for_urgent_care_at = models.DateTimeField(null=True, blank=True)
    looking_for_another_app_or_service_at = models.DateTimeField(null=True, blank=True)
    not_willing_to_switch_pcp_at = models.DateTimeField(null=True, blank=True)
    seeking_only_mental_health_psychiatrist_at = models.DateTimeField(null=True, blank=True)
    terminated_by_employer_at = models.DateTimeField(null=True, blank=True)

    # Removed churn reasons from UI
    leaving_ma_at = models.DateTimeField(null=True, blank=True)
    cant_get_in_touch_at = models.DateTimeField(null=True, blank=True)
    out_of_state_at = models.DateTimeField(null=True, blank=True)
    confused_about_service_at = models.DateTimeField(null=True, blank=True)
    never_signed_poc_at = models.DateTimeField(null=True, blank=True)

    churned_reasons = deprecate_field(
        ArrayField(
            # DO NOT COPY-PASTE: Prefer TextField over CharField
            models.CharField(  # noqa: TID251
                max_length=255,
                blank=True,
                null=True,
            ),
            null=True,
            blank=True,
        )
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharge_reason = deprecate_field(
        models.CharField(  # noqa: TID251
            max_length=255,
            blank=True,
            null=True,
            choices=CHURNED_DISCHARGED_REASON_CHOICES,
            default=None,
        )
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharged_reason = deprecate_field(
        models.CharField(  # noqa: TID251
            max_length=50,
            blank=True,
            null=True,
            choices=DISCHARGED_REASON_CHOICES,
            default=None,
        )
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    deactivated_reason = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=DEACTIVATED_REASON_CHOICES,
        default=None,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    deactivated_reason_app = ArrayField(models.CharField(max_length=255, blank=True, null=True), null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    deactivated_reason_other = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # Signed Up to Activated Updated User
    downloaded_app_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    insurance_eligibility_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    care_team_selection_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    poc_sent_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    poc_completed_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    medical_record_release_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Member
    intake_call_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Churned Reason Updated User
    leaving_ma_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    unhappy_with_service_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    confused_about_service_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    cant_get_in_touch_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    insurance_not_covered_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    insurance_switched_pcp_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    deceased_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    out_of_state_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    not_interested_in_service_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    looking_for_urgent_care_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    looking_for_another_app_or_service_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    not_willing_to_switch_pcp_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    never_signed_poc_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    seeking_only_mental_health_psychiatrist_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    terminated_by_employer_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Signup Question Information
    signup_reasons: models.ManyToManyField = models.ManyToManyField(SignupReasons, blank=True)

    # TODO: Deprecated. Remove once member signup_reasons have been backfilled with [signup_reason]
    signup_reason = models.ForeignKey(
        SignupReasons,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    signup_reason_updated_at = models.DateTimeField(null=True, blank=True)
    signup_reason_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Signup referral source
    referral_source = models.ForeignKey(
        ReferralSource,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    referral_source_updated_at = models.DateTimeField(null=True, blank=True)
    referral_source_updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # onboarding states/steps at which member is at when the onboarding outreach case was created
    # currently adding only ONBOARDING_CHOICES will change to include more granularity in future
    onboarding_case_creation_state = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(  # noqa: TID251
            max_length=100,
            blank=True,
            null=True,
            choices=ONBOARDING_CHOICES,
        ),
        blank=True,
        null=True,
    )

    case_relations = GenericRelation("cases.CaseRelation")

    # Temporary for migration. Will drop
    is_deleted = models.BooleanField(default=False, db_index=True)

    # During onboarding, we ask the member if they want to book an
    # appointment now vs later. Column stores the choice of the user.
    book_visit_during_onboarding_yn = models.BooleanField(blank=True, null=True)
    book_visit_during_onboarding_yn_at = models.DateTimeField(blank=True, null=True)
    did_book_appointment_during_onboarding_yn = models.BooleanField(blank=True, null=True)
    did_book_appointment_during_onboarding_yn_at = models.DateTimeField(blank=True, null=True)
    # If the member declines to book, we ask them to indicate why. Column stores the questionnaire response(s)
    book_visit_during_onboarding_declined_reasons = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        null=True,
        blank=True,
    )

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_person_change

        if hasattr(self, "patient") and self.patient and hasattr(self.patient, "person") and self.patient.person:
            if not changed("id") and (changed("status") or changed("deleted")):
                logger.info("Person %d: Invoking zus sync from onboarding status change", self.patient.person.id)
                zus_handle_person_change.send(person_id=self.patient.person.id)


class DeactivationReason(BaseFact):
    class Meta(BaseFact.Meta):
        db_table = "deactivation_reasons"
        verbose_name_plural = "Deactivation Reasons"


class ChurnedDischargedReason(BaseFact):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(  # noqa: TID251
        unique=True,
        max_length=255,
        null=False,
        blank=False,
    )

    class Meta(BaseFact.Meta):
        pass


class OnboardingNavigationEvent(BaseModelV3):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, related_name="onboarding_navigation_event", on_delete=models.CASCADE
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    module = models.CharField(max_length=255)  # noqa: TID251
