from django import forms

from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand
from firefly.modules.programs.program_codes import ProgramCodes


class SyncProgram(FireflyAdminCommand):
    class form(forms.Form):
        supported_program_codes = [
            (ProgramCodes.DEPRESSION_AND_ANXIETY, ProgramCodes.DEPRESSION_AND_ANXIETY),
            (ProgramCodes.HYPERTENSION, ProgramCodes.HYPERTENSION),
            (ProgramCodes.DIABETES, ProgramCodes.DIABETES),
            (ProgramCodes.ADHD_ADD, ProgramCodes.ADHD_ADD),
        ]

        program = forms.CharField(
            required=True,
            widget=forms.Select(choices=supported_program_codes + [("", "")]),
            initial="",
        )

    def get_command_arguments(self, data, user):
        args = ["--program_code", data["program"]]

        return args, {
            "user": user,
        }


class SyncRiskScore(FireflyAdminCommand):
    class form(forms.Form):
        pass

    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class ChurnNotEstablishedUsers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        person_id = forms.IntegerField(required=False)
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["person_id"]:
            args += ["-person_id", data["person_id"]]
        if data["limit"]:
            args += ["-limit", data["limit"]]
        if data["offset"]:
            args += ["-offset", data["offset"]]
        return args, {
            "user": user,
        }


class DelEnrollmentForInitializedMembers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        person_id = forms.IntegerField(required=False)
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["person_id"]:
            args += ["-person_id", data["person_id"]]
        if data["limit"]:
            args += ["-limit", data["limit"]]
        if data["offset"]:
            args += ["-offset", data["offset"]]
        return args, {
            "user": user,
        }


class BackfillDischargeInProgressStatus(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {
            "user": user,
        }


class BackfillEstablishedRow(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        person_ids = forms.CharField(
            required=False, help_text="Comma separated list: Only this specific person data will be backfilled"
        )

    def get_command_arguments(self, data, user):
        opts = [
            "--person_ids",
            data["person_ids"],
        ]
        return opts, {"user": user}


class BackfillProgramInfosInInactiveStatus(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {
            "user": user,
        }


class BackfillHistoricProgramEnrollmentsFromStatuses(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class CalculateAttributionForBenefitUsers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class BackfillUsersWithIncorrectDischargeStatus(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["limit"]:
            args += ["-limit", data["limit"]]
        if data["offset"]:
            args += ["-offset", data["offset"]]
        return args, {
            "user": user,
        }


class AddMemberIntoProgram(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        supported_program_codes = [
            (ProgramCodes.BENEFIT, ProgramCodes.BENEFIT),
        ]

        program = forms.CharField(
            required=True,
            widget=forms.Select(choices=supported_program_codes + [("", "")]),
            initial="",
        )
        person_id = forms.IntegerField(required=True)
        started_at = forms.DateTimeField(required=True)
        terminated_at = forms.DateTimeField(required=False)

    def get_command_arguments(self, data, user):
        args = ["-program_code", data["program"]]
        if data["person_id"]:
            args += ["-person_id", data["person_id"]]
        if data["started_at"]:
            args += ["-started_at", data["started_at"]]
        if data["terminated_at"]:
            args += ["-terminated_at", data["terminated_at"]]
        return args, {
            "user": user,
        }


class SetEndDateForEnrollment(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        person_ids = forms.CharField(required=False, help_text="Comma separated list")

    def get_command_arguments(self, data, user):
        args = ["-person_ids", data["person_ids"]]
        return args, {
            "user": user,
        }
