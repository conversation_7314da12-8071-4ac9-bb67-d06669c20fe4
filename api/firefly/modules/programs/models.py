import logging
import uuid
from typing import ClassVar, Optional

from django.conf import settings
from django.contrib.postgres.constraints import ExclusionConstraint
from django.contrib.postgres.fields.ranges import DateTimeRangeField, RangeOperators
from django.db import models
from django.utils import timezone as django_timezone
from django_deprecate_fields import deprecate_field as django_deprecate_field
from pgtrigger import Q
from transitions import EventData
from typing_extensions import Self

from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.firefly_django.models import (
    BaseModelV3,
    InheritanceAllManagerWithSafeDeleteAndBulkUpdate,
    InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate,
    InheritanceManagerWithSafeDeleteAndBulkUpdate,
)
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin

logger = logging.getLogger(__name__)


class Program(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=31, db_index=True, unique=True, primary_key=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    hint_id = django_deprecate_field(models.CharField(unique=True, blank=True, null=True, max_length=16))  # noqa: TID251
    is_clinical = models.BooleanField(blank=True, null=True)


class ProgramInfosManager(InheritanceManagerWithSafeDeleteAndBulkUpdate):
    def get_queryset(self):
        return super().get_queryset().filter(active=True)


def actor_from_program_event_data(data):
    if data is None:
        return None
    elif isinstance(data, EventData):
        return data.kwargs.get("actor")
    else:
        return data.get("actor")


class ProgramInfo(BaseModelV3):
    # Objects with active=False are hidden by default
    # (forwards and backwards relationships)
    # Note: The typing of these class variables appears to behave correctly,
    # however mypy still emits errors of the form:
    #
    #   error: Incompatible types in assignment (expression has type
    #       "InheritanceManagerWithSafeDeleteAndBulkUpdate[Self]", base class "BaseModelV3"
    #       defined the type as "SafeDeleteManagerWithBulkUpdate[ProgramInfo
    #       ]")
    #
    # As BaseModelV3 have different type and inherit multi table model class different type,
    # I am ignoring type same as BaseModelV3
    objects: ClassVar[ProgramInfosManager[Self]] = ProgramInfosManager()  # type: ignore[assignment]
    all_objects: ClassVar[InheritanceAllManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceAllManagerWithSafeDeleteAndBulkUpdate()  # type: ignore[assignment]
    )
    deleted_objects: ClassVar[InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate()  # type: ignore[assignment]
    )
    objects_including_inactive: ClassVar[InheritanceManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceManagerWithSafeDeleteAndBulkUpdate()
    )

    # Subclasses override this
    PROGRAM_CODE: Optional[str] = None
    uid = models.UUIDField(default=uuid.uuid4, unique=True, editable=False, primary_key=True)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, db_index=True, null=False)
    person = models.ForeignKey(
        "user.Person",
        related_name="program_info",
        on_delete=models.CASCADE,
        # Can be null for now, until the backfill is complete and verified.
        # TODO: Remove.
        null=True,
        blank=True,
    )

    active = models.BooleanField(default=True)

    start_date = models.DateTimeField(null=True, blank=True)
    started_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    end_date = models.DateTimeField(null=True, blank=True)
    ended_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # allow lucian user to override a patient enrollment sync for a program
    # if marked false patient will not be picked up for sync
    enrollment_sync = models.BooleanField(
        default=True,
        null=True,
        blank=True,
    )

    def clean(self):
        self.program_id = self.program_id or self.__class__.PROGRAM_CODE

    def save(self, *args, **kwargs):
        self.clean()
        return super().save(*args, **kwargs)

    def on_enroll(self, data=None):
        actor = actor_from_program_event_data(data)
        self.end_date = None
        self.ended_by = None
        self.start_date = django_timezone.now()
        self.started_by = actor
        self.active = True
        self.__create_enrollment_event(enrolled=True)
        self.save()

    def on_unenroll(self, data=None):
        actor = actor_from_program_event_data(data)
        self.end_date = django_timezone.now()
        self.ended_by = actor
        self.active = False
        self.__create_enrollment_event(enrolled=False)
        self.save()

    def __create_enrollment_event(self, enrolled):
        event_code = EventTypeCodes.PROGRAM_ENROLLED if enrolled else EventTypeCodes.PROGRAM_UNENROLLED
        if hasattr(self.person, "user") and self.person.user:
            EventLog.objects.create(
                target=self,
                type=event_code,
                user=self.person.user,
                metadata={
                    "program_code": self.__class__.PROGRAM_CODE,
                    "uid": str(self.uid),
                    "timestamp": str(django_timezone.now()),
                },
            )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["program", "person"],
                condition=Q(deleted=None),
                name="unique_program_person_if_not_deleted",
            )
        ]


class ProgramInfoWithUnenrollReason(ProgramInfo):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unenroll_reason = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
    )

    def on_enroll(self, *args, **kwargs):
        self.unenroll_reason = None
        return super().on_enroll(*args, **kwargs)

    class Meta:
        abstract = True


class ProgramEnrollment(SaveHandlersMixin, BaseModelV3):
    class ReasonCode(models.TextChoices):
        DISCHARGED = "Discharged"

    person = models.ForeignKey("user.Person", related_name="program_enrollments", on_delete=models.CASCADE)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, db_index=True, null=False)
    period = DateTimeRangeField()
    status = models.TextField(null=True, blank=True)
    reason = models.TextField(null=True, blank=True)
    reason_code = models.TextField(choices=ReasonCode.choices, blank=True, null=True)

    def full_clean(self, exclude=None, validate_unique=True, validate_constraints=True):
        # When an instance is saved (via code) - full clean is called without any exclusions. This
        # runs all validations when an instance is being saved via the code.
        # However, when an instance is saved via the admin form - the list of fields on the form
        # and the list of editable fields are derived from the model meta and all non-editable
        # fields are excluded.
        # (See https://github.com/django/django/blob/stable/4.2.x/django/forms/models.py#L136)
        # This list of excluded fields is then passed into full_clean (See https://github.com/django/django/blob/stable/4.2.x/django/forms/models.py#L465)
        # so that fields that cannot be edited in the admin - are removed from validations.
        # Specifically for the Exclusion Constraint - when an excluded field shows up in an expression - the validation
        # is short-circuited (See https://github.com/django/django/blob/stable/4.2.x/django/contrib/postgres/constraints.py#L206).
        # However, this exclusion logic does not get applied to fields in the condition statement - causing the
        # evaluation to fail since the field will not be resolved.
        # See https://github.com/django/django/blob/stable/4.2.x/django/contrib/postgres/constraints.py#L230-L232
        # This is likely to happen with any field marked non-editable in the admin.
        # Currently this includes - the audit fields (created_at, created_by, updated_at and updated_by) along with the
        # deleted field. However - the deleted field is the most likely to have validations set against it.
        # Introduced in https://github.com/django/django/pull/14625/files#diff-36a73f3d714bdcb9de61bae1a4364078e4863ab804d273e97ae0d906f08d714a

        # The following piece of code specifically removes the deleted field from the list of
        # exclusions so that any saves - include checks defined on the deleted field.
        # The `deleted` field gets special treatment since any object save without specifying deleted
        # is intended to undelete. (This is also why saving a deleted object via the admin page
        # will result in the object being undeleted - though the deleted field was marked as an
        # uneditable field in the admin). Removing the other audit fields from the exclusion would not
        # make sense since its neither allowed to update these via admin pages nor would any other code
        # implicitly change these / null these out.
        # After a period of stability - this logic will be moved into BaseModelV3 so that the
        # behavior is applied to all models.
        if exclude is not None and "deleted" in exclude:
            exclude.remove("deleted")
        super().full_clean(exclude=exclude, validate_unique=validate_unique, validate_constraints=validate_constraints)

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_person_change

        if hasattr(self, "person"):
            if not changed("id") and (changed("period") or changed("deleted")):
                logger.info("Person %d: Invoking zus sync from program enrollment change", self.person.id)
                zus_handle_person_change.send(person_id=self.person.id)

    class Meta(BaseModelV3.Meta):
        constraints = [
            ExclusionConstraint(
                name="non_overlapping_enrollments",
                expressions=[
                    ("period", RangeOperators.OVERLAPS),
                    ("person", RangeOperators.EQUAL),
                    ("program", RangeOperators.EQUAL),
                ],
                condition=Q(deleted__isnull=True),
            )
        ]
