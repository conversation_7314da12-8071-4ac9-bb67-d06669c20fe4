from contextlib import contextmanager
from datetime import date, datetime, timedelta
from unittest import skip
from unittest.mock import MagicMock, patch

import pytest
from django.contrib.contenttypes.models import ContentType
from django.db import IntegrityError, transaction
from django.utils import timezone
from rest_framework import status

from firefly.core.feature.testutils import override_switch
from firefly.core.services.zus.constants import WAFFLE_SWITCH_SUBSCRIBE_TO_ZUS_BASED_ON_RISK_SCORE
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.utils import create_update_assignee_group_from_user
from firefly.modules.appointment.constants import AppointmentReason, AppointmentStatus
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.utils import _mark_completed_appointment
from firefly.modules.cases.constants import DischargeMemberCaseStatuses
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.insurance.factories import InsurancePayerFactory, InsurancePlanFactory
from firefly.modules.insurance.models import InsurancePayer, InsurancePlan
from firefly.modules.onboarding.constants import ChurnedDischargedReasons
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER, FIREFLY_PLAN
from firefly.modules.programs.constants import (
    BenefitProgramStatus,
    PrimaryCareProgramStatus,
    ProgramEnrollmentEvents,
)
from firefly.modules.programs.models import Program, ProgramEnrollment, ProgramInfo
from firefly.modules.programs.primary_care.models import PrimaryCareProgramInfo
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.sync import EnrollmentMixin
from firefly.modules.programs.utils import (
    active_or_inactive_program_info_for_person_program,
    add_person_to_program,
    assign_assessment_form,
    get_due_date_considering_earliest_appt,
    get_person_program_enrollments,
    person_is_enrolled_in_program,
    person_is_in_firefly_full_benefit_as_of_today,
    person_is_in_program,
    program_info_for_person_program,
    remove_person_from_program,
    update_program_enrollment,
)
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import SOURCE_TYPES, Task, TaskCollection, TaskCollectionTask


class TestPersonIsInProgramAsOfToday(FireflyTestCase):
    @staticmethod
    @contextmanager
    def personMaker():
        person = PersonUserFactory()
        firefly_payer, _ = InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        firefly_plan, _ = InsurancePlan.objects.get_or_create(name=FIREFLY_PLAN)
        insurance_info = person.insurance_info

        insurance_info.insurance_payer = firefly_payer
        insurance_info.insurance_plan = firefly_plan
        insurance_info.coverage_start = date.today() - timedelta(days=90)
        insurance_info.coverage_end = date.today() + timedelta(days=90)

        yield person

        if person.insurance_info:
            person.insurance_info.save()
        person.save()
        person.refresh_from_db()

    def test_is_false_if_person_has_no_insurance_info(self):
        with self.personMaker() as person:
            person.insurance_info = None
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_false_if_payer_not_firefly(self):
        with self.personMaker() as person:
            person.insurance_info.insurance_payer = InsurancePayerFactory()
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_false_if_plan_not_firefly(self):
        with self.personMaker() as person:
            person.insurance_info.insurance_plan = InsurancePlanFactory()
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_true_if_insurance_info_has_no_end_date(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_end = None
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), True)

    def test_is_false_if_insurance_info_has_no_start_date(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_start = None
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_false_if_end_date_in_the_past(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_end = date.today() - timedelta(days=1)
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_false_if_end_date_is_today(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_end = date.today()
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_false_if_start_date_in_future(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_start = date.today() + timedelta(days=1)
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), False)

    def test_is_true_if_today_between_start_and_end_dates(self):
        with self.personMaker() as person:
            person.insurance_info.coverage_start = date.today() - timedelta(days=1)
            person.insurance_info.coverage_end = date.today() + timedelta(days=1)
        self.assertEqual(person_is_in_firefly_full_benefit_as_of_today(person.id), True)


class ProgramInfoTestCase(FireflyTestCase):
    def test_object_managers_inactive(self):
        Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        program_info = program_info_for_person_program(
            person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE
        )
        self.assertEqual(PrimaryCareProgramInfo.objects.get(pk=program_info.pk), program_info)
        self.assertTrue(self.patient.person.program_info.exists())
        # If the program is inactive, it should be hidden by default
        # (forwards and backwards relationships)
        program_info.active = False
        program_info.save()
        self.assertFalse(PrimaryCareProgramInfo.objects.filter(pk=program_info.pk).exists())
        self.assertFalse(self.patient.person.program_info.exists())
        # The custom inactive manager can still find it, though
        self.assertEqual(
            PrimaryCareProgramInfo.objects_including_inactive.get(pk=program_info.pk),
            program_info,
        )

    def test_eventing(self):
        person = PersonUserFactory()
        content_type = ContentType.objects.get_for_model(PrimaryCareProgramInfo)

        def get_patient_events():
            return EventLog.objects.filter(user_id=person.user.pk, target_content_type=content_type)

        init_event_log_count = get_patient_events().count()
        # Removing a patient from a program they're not already in
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertEqual(get_patient_events().count(), init_event_log_count)
        # Given we add the patient to the program
        program_info = add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)

        patient_events = get_patient_events()
        self.assertEqual(patient_events.count(), init_event_log_count + 1)
        # An enrollment event should have been created
        enroll_event = patient_events.order_by("-created_at").first()
        self.assertEqual(enroll_event.type, EventTypeCodes.PROGRAM_ENROLLED)
        self.assertEqual(enroll_event.target, program_info)
        self.assertEqual(enroll_event.metadata["program_code"], ProgramCodes.PRIMARY_CARE)
        self.assertEqual(enroll_event.metadata["uid"], str(program_info.uid))
        # Given we re-enroll the patient in the program
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertEqual(get_patient_events().count(), init_event_log_count + 1)
        # Given the patient is un-enrolled
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        patient_events = get_patient_events()
        self.assertEqual(patient_events.count(), init_event_log_count + 2)
        # An unenrollment event should have been created
        unenroll_event = patient_events.get(type=EventTypeCodes.PROGRAM_UNENROLLED)
        self.assertEqual(unenroll_event.type, EventTypeCodes.PROGRAM_UNENROLLED)
        self.assertEqual(unenroll_event.metadata["program_code"], ProgramCodes.PRIMARY_CARE)
        self.assertEqual(unenroll_event.metadata["uid"], str(program_info.uid))
        # Given the patient is un-enrolled again
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertEqual(get_patient_events().count(), init_event_log_count + 2)


class ProgramTestCase(FireflyTestCase):
    def test_program_api(self):
        Program.objects.create(uid="baseline program", is_clinical=False)
        Program.objects.create(uid="clinical program", is_clinical=True)
        result = self.provider_client.get("/programs/")
        data = result.json()
        baseline_program = [x for x in data if x["uid"] == "baseline program"]
        clinical_program = [x for x in data if x["uid"] == "clinical program"]
        self.assertEqual(baseline_program[0]["is_clinical"], False)
        self.assertEqual(clinical_program[0]["is_clinical"], True)

    def test_program_info_for_person_program(self):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person = PersonUserFactory()
        self.assertIsNone(program_info_for_person_program(person=person, program_uid=program.uid))
        program_info = PrimaryCareProgramInfo.objects.create(person=person, program=program)
        found_program_info = program_info_for_person_program(person=person, program_uid=program.uid)
        self.assertEqual(
            found_program_info,
            program_info,
        )
        self.assertIsInstance(found_program_info, PrimaryCareProgramInfo)
        found_program_info.active = False
        found_program_info.save()
        found_program_info.refresh_from_db()
        self.assertIsNone(program_info_for_person_program(person=person, program_uid=program.uid))

    def test_patient_is_in_program(self):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person = PersonUserFactory()
        self.assertFalse(person_is_in_program(person=person, program_uid=program.uid))
        program_info = PrimaryCareProgramInfo.objects.create(person=person, program=program)
        program_info.on_enroll()
        self.assertTrue(person_is_in_program(person=person, program_uid=program.uid))
        program_info.on_unenroll()
        self.assertFalse(person_is_in_program(person=person, program_uid=program.uid))
        self.assertIsNone(program_info_for_person_program(person=person, program_uid=program.uid))
        self.assertIsNotNone(active_or_inactive_program_info_for_person_program(person=person, program_uid=program.uid))

    def test_program_id(self):
        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        matching_program_info = PrimaryCareProgramInfo.objects.filter(person=self.patient.person)
        program_info = matching_program_info.first()
        self.assertEqual(program_info.program_id, ProgramCodes.PRIMARY_CARE)
        self.assertTrue(person_is_in_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE))
        # Save through the base class does not clear program_id
        ProgramInfo.objects_including_inactive.get(pk=program_info.pk).save()
        program_info.refresh_from_db()
        self.assertEqual(program_info.program_id, ProgramCodes.PRIMARY_CARE)
        self.assertTrue(person_is_in_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE))

    @patch("firefly.modules.programs.models.ProgramInfo.on_enroll")
    def test_add_person_to_program(self, mock_on_enroll):
        person = PersonUserFactory()
        self.assertFalse(PrimaryCareProgramInfo.objects.filter(person=person).exists())
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        matching_program_info = PrimaryCareProgramInfo.objects.filter(person=person)
        self.assertTrue(matching_program_info.exists())
        self.assertEqual(mock_on_enroll.call_count, 1)
        # Test that the patient can be removed, then re-added to the program
        program_info = matching_program_info.first()
        program_info.active = False
        program_info.save()
        added_to_program = add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info.refresh_from_db()
        self.assertEqual(added_to_program.pk, program_info.pk)
        self.assertEqual(mock_on_enroll.call_count, 2)

    def _assert_task_assigment(self, person, is_task_created: bool = False):
        task_uid_list = [
            TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR,
            TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE,
        ]
        for task_uid in task_uid_list:
            task_collection, _ = TaskCollection.objects.get_or_create(title=task_uid)
            template, _ = TaskCollectionTask.objects.update_or_create(
                uid=task_uid,
                defaults={"task_collection": task_collection, "title": task_collection.title},
            )
            task = Task.objects.filter(person=person, autocreated_from=template)
            form_submission = FormSubmission.incomplete_objects.filter(
                user=person.user,
                form=template.form,
            )
            if is_task_created:
                self.assertTrue(task.exists())
                self.assertEqual(task.count(), 1)
                if template.source_type == SOURCE_TYPES["formsubmission"]:
                    self.assertTrue(form_submission.exists())
            else:
                self.assertFalse(task.exists())
                if template.source_type == SOURCE_TYPES["formsubmission"]:
                    self.assertFalse(form_submission.exists())

    @patch("firefly.modules.programs.models.ProgramInfo.on_unenroll")
    def test_remove_patient_from_program(self, mock_on_unenroll):
        person = PersonUserFactory()
        self.assertFalse(PrimaryCareProgramInfo.objects.filter(person=person).exists())
        result = remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertIsNone(result)
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertTrue(PrimaryCareProgramInfo.objects.filter(person=person).exists())
        result = remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertTrue(result)
        mock_on_unenroll.assert_called_once()

    def test_forms_assigment_and_removal_on_pcp_program_enroll_segmentation(self):
        person = PersonUserFactory()
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self._assert_task_assigment(person=person, is_task_created=True)
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self._assert_task_assigment(person=person, is_task_created=False)
        # add to program again
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self._assert_task_assigment(person=person, is_task_created=True)
        # complete the forms
        task_uid_list = [
            TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR,
            TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE,
        ]
        for task_uid in task_uid_list:
            task_collection, _ = TaskCollection.objects.get_or_create(title=task_uid)
            template, _ = TaskCollectionTask.objects.update_or_create(
                uid=task_uid,
                defaults={
                    "task_collection": task_collection,
                    "title": task_collection.title,
                },
            )
            tasks = Task.objects.filter(person=person, autocreated_from=template)
            if tasks.exists():
                for task in tasks:
                    task.set_is_complete(True, get_lucian_bot_user())
                    task.save()
        # remove from program but task should exists
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self._assert_task_assigment(person=person, is_task_created=True)

    def test_med_release_form_assigment_and_removal(self):
        med_release_task_collection_task, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.MED_RELEASE_FORM,
            task_collection=self.med_release_task_collection,
            title=self.med_release_task_collection.title,
        )
        med_release_task_collection_task.assign_to_patient = True
        med_release_task_collection_task.days_offset = 14
        med_release_task_collection_task.form = self.med_release_form
        med_release_task_collection_task.source_type = SOURCE_TYPES["formsubmission"]
        med_release_task_collection_task.save()
        person = PersonUserFactory()
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        person.user.onboarding_state.to_member()
        # Med release case only gets created when booking an appt
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory.create(
                patient=person.user,
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status="Scheduled",
            )
        # check med release form asssignment
        task = Task.objects.filter(person=person, autocreated_from=med_release_task_collection_task)
        form_submission = FormSubmission.incomplete_objects.filter(
            user=person.user,
            form=med_release_task_collection_task.form,
        )
        self.assertTrue(task.exists())
        self.assertEqual(task.count(), 1)
        if med_release_task_collection_task.source_type == SOURCE_TYPES["formsubmission"]:
            self.assertTrue(form_submission.exists())
        # remove from primary care program
        remove_person_from_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        # check med release form is marked as completed
        task = Task.objects.filter(
            person=person,
            autocreated_from=med_release_task_collection_task,
            is_complete=False,
        )
        form_submission = FormSubmission.incomplete_objects.filter(
            user=person.user,
            form=med_release_task_collection_task.form,
        )
        self.assertFalse(task.exists())
        self.assertEqual(task.count(), 0)
        if med_release_task_collection_task.source_type == SOURCE_TYPES["formsubmission"]:
            self.assertFalse(form_submission.exists())


class ProgramApiTestCase(FireflyTestCase):
    def test_add_remove_patient_to_program(self):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)

        def send_add_request():
            return self.client.post(
                "/programs/add_patient/",
                {
                    "patient_id": self.patient.pk,
                    "program_uid": program.uid,
                },
                format="json",
            )

        def send_remove_request():
            return self.provider_client.post(
                "/programs/remove_patient/",
                {
                    "patient_id": self.patient.pk,
                    "program_uid": program.uid,
                },
                format="json",
            )

        response = send_remove_request()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response = send_add_request()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        response = send_add_request()
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        program_info = PrimaryCareProgramInfo.objects.get(person=self.patient.person)
        self.assertEqual(program_info.started_by, self.patient)
        self.assertIsNotNone(program_info.start_date)
        response = send_remove_request()
        self.assertFalse(PrimaryCareProgramInfo.objects.filter(person=self.patient.person).exists())
        program_info = PrimaryCareProgramInfo.objects_including_inactive.get(person=self.patient.person)
        self.assertEqual(program_info.ended_by, self.provider)
        self.assertIsNotNone(program_info.end_date)

    def test_get_enrollment(self):
        def make_request():
            return self.provider_client.get(
                f"/programs/enrollment/?patient_id={self.patient.pk}",
                format="json",
            )

        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        response = make_request()
        self.assertEqual(response.json(), [program.uid])


class PrimaryCareProgramTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.health_assessment_form, _ = Form.objects.get_or_create(uid=FormUID.HEALTH_ASSESSMENT)
        self.health_assessment_form_v3, _ = Form.objects.get_or_create(uid=FormUID.HEALTH_ASSESSMENT_V3)

        self.health_assessment_form.title = "Health Assessment"
        self.health_assessment_form_v3.title = "Health Review"
        self.health_assessment_form.save()
        self.health_assessment_form_v3.save()

        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.onboarding_state.to_signedup(actor=self.patient)

    def test_assign_assessment_form(self):
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 0)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 0)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 0)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            0,
        )
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )
        # Given that we re-run a second time
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )

    def test_assign_assessment_form_owner_groups(self):
        create_update_assignee_group_from_user(self.patient)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 0)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 0)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 0)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            0,
        )
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )
        # Given that we re-run a second time
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )

    def test_get_due_date_considering_earliest_appt(self):
        return_value = get_due_date_considering_earliest_appt(self.patient)
        self.assertEqual(
            return_value.replace(second=0, microsecond=0),
            (timezone.now() + timedelta(days=2)).replace(second=0, microsecond=0),
        )

        appointment_after_24_hours = Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=timezone.now() + timedelta(hours=48),
            description="Test test",
            elation_id=9999999,
            status="Scheduled",
            physician=self.physician,
            reason=AppointmentReason.AWV_NEW,
        )
        return_value = get_due_date_considering_earliest_appt(self.patient)
        self.assertEqual(return_value, appointment_after_24_hours.start - timedelta(hours=24))

        Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=timezone.now() + timedelta(hours=20),
            description="Test test",
            elation_id=99999,
            status="Scheduled",
            physician=self.physician,
            reason=AppointmentReason.AWV_NEW,
        )
        return_value = get_due_date_considering_earliest_appt(self.patient)
        self.assertEqual(return_value.replace(second=0, microsecond=0), timezone.now().replace(second=0, microsecond=0))


class PrimaryCareProgramTestSegmentationCaptureCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.health_assessment_form, _ = Form.objects.get_or_create(uid=FormUID.HEALTH_ASSESSMENT)
        self.health_assessment_form_v3, _ = Form.objects.get_or_create(uid=FormUID.HEALTH_ASSESSMENT_V3)
        self.health_assessment_form.title = "Health Assessment"
        self.health_assessment_form_v3.title = "Health Review"
        self.health_assessment_form.save()
        self.health_assessment_form_v3.save()

        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.patient.onboarding_state.to_signedup(actor=self.patient)

    def test_assign_assessment_form(self):
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 0)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 0)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 0)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            0,
        )
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )
        # Given that we re-run a second time
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )

    def test_assign_assessment_form_owner_groups(self):
        create_update_assignee_group_from_user(self.patient)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 0)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 0)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 0)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            0,
        )
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )
        # Given that we re-run a second time
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT)
        self.assertEqual(Task.objects.filter(title="Health Assessment").count(), 1)
        self.assertEqual(FormSubmission.objects.filter(form=self.health_assessment_form).count(), 1)
        assign_assessment_form(self.patient, FormUID.HEALTH_ASSESSMENT_V3)
        self.assertEqual(Task.objects.filter(title="Health Review").count(), 1)
        self.assertEqual(
            FormSubmission.objects.filter(form=self.health_assessment_form_v3).count(),
            1,
        )


class TestProgramEnrollmentMixin(FireflyTestCase):
    class TestEnrollment(EnrollmentMixin):
        pass

    def setUp(self):
        FireflyTestCase.setUp(self)
        self.enroll_mixin = self.TestEnrollment()
        self.looker_field_map = {
            "patient_id": "depression_and_anxiety_reporting.patient_id",
            "person_id": "users.person_id",
            "care_plan_created": "depression_and_anxiety_reporting.bh_care_plan_created_by",
            "enrolled_in_program": "depression_and_anxiety_reporting.enrolled_in_program",
        }

    @patch("firefly.modules.programs.sync.logger")
    @patch("firefly.modules.programs.sync.add_person_to_program")
    def test_program_enrollment(self, mock_add_to_program, mock_logger):
        # Scenario
        # enroll person to program DEPRESSION_AND_ANXIETY
        # Expected Behavior
        # Should add person to program

        looker_result = [
            {
                "users.person_id": self.patient.person.id,
                "depression_and_anxiety_reporting.bh_care_plan_created_by": "Template",
                "depression_and_anxiety_reporting.enrolled_in_program": "No",
            }
        ]

        self.enroll_mixin.enroll_patients(looker_result, self.looker_field_map, ProgramCodes.DEPRESSION_AND_ANXIETY)

        mock_add_to_program.assert_called_once_with(self.patient.person, ProgramCodes.DEPRESSION_AND_ANXIETY)

        # Scenario
        # Exception raised while adding person to program
        # Expected Behavior
        # Should call logger to log error

        mock_logger.exception = MagicMock()
        mock_add_to_program.reset_mock()
        mock_add_to_program.side_effect = Exception()
        self.enroll_mixin.enroll_patients(looker_result, self.looker_field_map, ProgramCodes.DEPRESSION_AND_ANXIETY)
        mock_logger.exception.assert_called_once()

    def test_add_person_to_program(self):
        # Scenario
        # Check person getting added to program
        # Expected Behavior
        # person should be enrolled
        looker_result = [
            {
                "users.person_id": self.patient.person.id,
                "depression_and_anxiety_reporting.bh_care_plan_created_by": "Template",
                "depression_and_anxiety_reporting.enrolled_in_program": "No",
            }
        ]
        self.enroll_mixin.enroll_patients(looker_result, self.looker_field_map, ProgramCodes.DEPRESSION_AND_ANXIETY)

        self.assertTrue(
            person_is_in_program(
                person=self.patient.person.id,
                program_uid=ProgramCodes.DEPRESSION_AND_ANXIETY,
            )
        )

        # Scenario
        # Skip person whose enrollment_sync has been marked false
        # Expected Behavior
        # should not call add_person_to_program
        with patch("firefly.modules.programs.sync.add_person_to_program") as mock_add_to_program:
            enrolled_program_info = ProgramInfo.objects.filter(
                person=self.patient.person.id,
                program_id=ProgramCodes.DEPRESSION_AND_ANXIETY,
                active=True,
            ).first()

            enrolled_program_info.enrollment_sync = False
            enrolled_program_info.save()

            self.enroll_mixin.enroll_patients(
                looker_result,
                self.looker_field_map,
                ProgramCodes.DEPRESSION_AND_ANXIETY,
            )

            mock_add_to_program.assert_not_called()


class ProgramEnrollmentTestCase(FireflyTestCase):
    def test_non_overlapping_constraint(self):
        program_1, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        program_2, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        # Default period
        start_1 = date.today()
        end_1 = start_1 + timedelta(days=7)
        period_1 = (start_1, end_1)
        # Same as default period, but offset by one day
        start_2 = date.today() + timedelta(days=1)
        end_2 = start_2 + timedelta(days=7)
        period_2 = (start_2, end_2)
        # Starts on same day that default period ends
        start_3 = end_1
        end_3 = start_3 + timedelta(days=7)
        period_3 = (start_3, end_3)
        # Period in the future
        start_4 = date.today() + timedelta(days=30)
        end_4 = start_4 + timedelta(days=7)
        period_4 = (start_4, end_4)
        # Empty end date and start date before default period
        start_5 = date.today() - timedelta(days=7)
        end_5 = None
        period_5 = (start_5, end_5)
        # Empty end date and start date in the future
        start_6 = date.today() + timedelta(days=60)
        end_6 = None
        period_6 = (start_6, end_6)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        person_3 = PersonUserFactory()
        # Test creating new program enrollment
        enrollment_1 = ProgramEnrollment.objects.create(
            person=person_1, program=program_1, period=period_1, status="test"
        )
        # Test new program enrollment with same dates and member, but different program
        ProgramEnrollment.objects.create(person=person_1, program=program_2, period=period_1, status="test")
        # Test new program enrollment with same dates and program, but different member
        ProgramEnrollment.objects.create(person=person_2, program=program_1, period=period_1, status="test")
        # Test new program enrollment with overlapping dates, same program, but different member
        ProgramEnrollment.objects.create(person=person_3, program=program_1, period=period_2, status="test")
        # Test new program enrollment with the same start date as a previous end date
        ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_3, status="test")
        # Test new program enrollment with different dates, but same program and member
        ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_4, status="test")
        # Test new program enrollment with overlapping dates, and same member and program
        with transaction.atomic():  # noqa: TID251
            with pytest.raises(IntegrityError):
                ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_2, status="test")
        # Test new program enrollment with missing end date (in future), but same program and member
        with transaction.atomic():  # noqa: TID251
            with pytest.raises(IntegrityError):
                ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_5, status="test")
        # Test new program enrollment with missing end date (in past), but same program and member
        ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_6, status="test")
        # Test delete program enrollment and recreate
        enrollment_1.delete()
        ProgramEnrollment.objects.create(person=person_1, program=program_1, period=period_1, status="test")

    @patch("firefly.core.user.signals.update_auth0_user")
    def test_appointment_checked_out(self, mock_update_auth0_user):
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()

        start_1 = datetime.now()
        start_2 = start_1 + timedelta(days=8)
        period_2 = (start_2, None)

        benefit = ProgramEnrollment.objects.create(
            person=person_1, program=benefit_program, period=period_2, status="test"
        )
        appt = AppointmentFactory(
            patient=person_1.user,
            patient_joined_video=True,
        )
        _mark_completed_appointment(appt)
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.CHECKED_OUT.value)
        benefit.refresh_from_db()
        self.assertEqual(benefit.status, "test")

        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            event_date=start_1,
        )
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        appt = AppointmentFactory(
            patient=person_1.user,
            patient_joined_video=True,
        )
        _mark_completed_appointment(appt)
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.CHECKED_OUT.value)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
            event_date=appt.start + timedelta(days=7),
        )
        appt = AppointmentFactory(
            patient=person_1.user,
            patient_joined_video=True,
        )
        _mark_completed_appointment(appt)
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.CHECKED_OUT.value)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.CHURNED)

        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_REENROLLED,
            event_date=appt.start + timedelta(days=8),
        )

        # since the user has completed the appointment
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        appt = AppointmentFactory(
            patient=person_1.user, patient_joined_video=True, start=timezone.now() + timedelta(days=9)
        )
        _mark_completed_appointment(appt)
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.CHECKED_OUT.value)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)

        appt_1 = AppointmentFactory(
            patient=person_1.user,
            patient_joined_video=True,
        )
        _mark_completed_appointment(appt_1)
        appt_1.refresh_from_db()
        self.assertEqual(appt_1.status, AppointmentStatus.CHECKED_OUT.value)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person_1).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)

    def test_update_program_enrollment(self):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        # primary_care test cases
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
        )
        primary_care = ProgramEnrollment.objects.get(
            program=care_program,
            person=person_1,
        )
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNone(primary_care.period.upper)
        self.assertIsNotNone(primary_care.period.lower)
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT,
        )
        primary_care = ProgramEnrollment.objects.filter(
            program=care_program,
            person=person_1,
        )
        not_establised_primary_care = primary_care.first()
        self.assertEqual(not_establised_primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNotNone(not_establised_primary_care.period.upper)
        self.assertIsNotNone(not_establised_primary_care.period.lower)

        establised_primary_care = primary_care.last()
        self.assertEqual(establised_primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)
        self.assertIsNone(establised_primary_care.period.upper)
        self.assertIsNotNone(establised_primary_care.period.lower)

        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
        )
        establised_primary_care = ProgramEnrollment.objects.get(
            program=care_program, person=person_1, status=PrimaryCareProgramStatus.ESTABLISHED
        )
        self.assertEqual(establised_primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)
        self.assertIsNotNone(establised_primary_care.period.upper)
        self.assertIsNotNone(establised_primary_care.period.lower)
        churned_primary_care = ProgramEnrollment.objects.get(
            program=care_program, person=person_1, status=PrimaryCareProgramStatus.CHURNED
        )
        lower_period = churned_primary_care.period.lower
        self.assertEqual(churned_primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(churned_primary_care.period.upper)
        self.assertIsNotNone(lower_period)

        # Try to churn again
        # Expected: Should not create a new enrollment
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
        )
        churned_primary_care = ProgramEnrollment.objects.filter(
            program=care_program, person=person_1, status=PrimaryCareProgramStatus.CHURNED
        )
        self.assertEqual(churned_primary_care.count(), 1)
        self.assertEqual(churned_primary_care.first().period.lower, lower_period)

    def test_update_benefit_program_enrollment_debug(self):
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        person_1 = PersonUserFactory()
        period_1 = (datetime(day=1, month=1, year=2024, tzinfo=UTC_TIMEZONE), None)
        add_person_to_program(person_1, ProgramCodes.BENEFIT, data={"enrollment_period": period_1})
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_1)
        self.assertEqual(enrollments.count(), 1)
        enrollment = enrollments.first()
        self.assertEqual(enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertIsNotNone(enrollment.period.lower)
        self.assertIsNone(enrollment.period.upper)

    def test_update_benefit_program_enrollment(self):
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        person_1 = PersonUserFactory()
        user_1_created_at = person_1.user.created_at.replace(tzinfo=UTC_TIMEZONE)
        add_person_to_program(person_1, ProgramCodes.BENEFIT)
        person_2 = PersonUserFactory()
        add_person_to_program(person_2, ProgramCodes.BENEFIT)
        # Create new enrollment with start date in future
        # Use Case 1: No existing enrollment found
        person_1_start_date = (datetime.now(UTC_TIMEZONE) + timedelta(days=20)).replace(tzinfo=UTC_TIMEZONE)
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(datetime.now(UTC_TIMEZONE) + timedelta(days=20), None),
        )
        # Check that there is only one program enrollment for this user
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_1)
        self.assertEqual(enrollments.count(), 2)
        elected_enrollment = ProgramEnrollment.objects.get(
            program=benefit_program, person=person_1, status=BenefitProgramStatus.ELECTED
        )
        # Check that the enrollment is in the "elected" status
        self.assertEqual(elected_enrollment.status, BenefitProgramStatus.ELECTED)
        self.assertEqual(elected_enrollment.period.lower.date(), user_1_created_at.date())
        self.assertEqual(elected_enrollment.period.upper.date(), person_1_start_date.date())

        enrolled_enrollment = ProgramEnrollment.objects.get(
            program=benefit_program, person=person_1, status=BenefitProgramStatus.ENROLLED
        )
        # Check that the enrollment is in the "elected" status
        self.assertEqual(enrolled_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertEqual(enrolled_enrollment.period.lower.date(), person_1_start_date.date())
        self.assertIsNone(enrolled_enrollment.period.upper)

        start_date = datetime.now(UTC_TIMEZONE) - timedelta(days=20)
        # Create new enrollment with start date in past
        # Use Case 1: No existing enrollment found
        person_2_start_date = start_date.replace(tzinfo=UTC_TIMEZONE)
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_2,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(start_date, None),
        )
        # Check that there is only one program enrollment for this user
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_2)
        self.assertEqual(enrollments.count(), 1)
        enrollment = enrollments.first()
        # Check that the enrollment is in the "enrolled" status
        self.assertEqual(enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertIsNotNone(enrollment.period.lower.date(), person_2_start_date.date())
        self.assertIsNone(enrollment.period.upper)

        # Use case 2: Existing enrollment found with start date, but no end date, and start dates match
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_2,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(start_date, None),
        )
        # Check that there is only one program enrollment for this user
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_2)
        self.assertEqual(enrollments.count(), 1)
        enrollment = enrollments.first()
        # Check that the enrollment is in the "enrolled" status
        self.assertEqual(enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertEqual(enrollment.period.lower.date(), person_2_start_date.date())
        self.assertIsNone(enrollment.period.upper)

        end_date = datetime.now(UTC_TIMEZONE)
        person_2_end_date = end_date.replace(tzinfo=UTC_TIMEZONE)
        # Use case 2: Existing enrollment found, but status is wrong
        # existing enrollment has Enrolled status and new status is Terminated
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_2,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
            enrollment_period=(start_date, end_date),
        )
        # Check that there is still just one program enrollment for this user after updates
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_2)
        self.assertEqual(enrollments.count(), 2)
        terminated_enrollment = ProgramEnrollment.objects.get(
            program=benefit_program, person=person_2, status=BenefitProgramStatus.TERMINATED
        )
        # Check that the enrollment is in the "terminated" status
        self.assertEqual(terminated_enrollment.status, BenefitProgramStatus.TERMINATED)
        self.assertEqual(terminated_enrollment.period.lower.date(), person_2_end_date.date())
        self.assertIsNone(terminated_enrollment.period.upper)
        enrolled_enrollment = ProgramEnrollment.objects.get(
            program=benefit_program, person=person_2, status=BenefitProgramStatus.ENROLLED
        )
        # Check that the enrollment is in the "enrolled" status
        self.assertEqual(enrolled_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertEqual(enrolled_enrollment.period.lower.date(), person_2_start_date.date())
        self.assertEqual(enrolled_enrollment.period.upper.date(), person_2_end_date.date())
        # Create new enrollment for same user with same start and end dates
        # Use case 3: No change to program enrollment if existing start date is equal to new end date with same status
        # If existing status and status is terminated, skip update.
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_2,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
            enrollment_period=(start_date, end_date),
        )
        self.assertEqual(enrollments.count(), 2)
        # Check that the enrollment is in the "terminated" status
        self.assertEqual(terminated_enrollment.status, BenefitProgramStatus.TERMINATED)
        self.assertEqual(terminated_enrollment.period.lower.date(), person_2_end_date.date())
        self.assertIsNone(terminated_enrollment.period.upper)
        enrolled_enrollment = ProgramEnrollment.objects.get(
            program=benefit_program, person=person_2, status=BenefitProgramStatus.ENROLLED
        )
        # Check that the enrollment is in the "enrolled" status
        self.assertEqual(enrolled_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertEqual(enrolled_enrollment.period.lower.date(), person_2_start_date.date())
        self.assertEqual(enrolled_enrollment.period.upper.date(), person_2_end_date.date())
        # Create new enrollment for same user with start date in past
        # Use case 4: Existing enrollment found with start date, but no end date, and new start date isn't
        # the plan start date, - status and dates of existing enrollment(Elected will be moved to Enrolled)
        # will be updated
        person_3 = PersonUserFactory()
        new_start = datetime.now(UTC_TIMEZONE) - timedelta(days=400)
        person_3_new_start = new_start.replace(tzinfo=UTC_TIMEZONE)
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_3,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(new_start, None),
        )
        # Check that there is still just one program enrollment for this user after updates
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_3)
        self.assertEqual(enrollments.count(), 1)
        enrollment = enrollments.first()
        # Check that enrollment start date is updated for this user
        self.assertEqual(enrollment.period.lower.date(), person_3_new_start.date())
        self.assertIsNone(enrollment.period.upper)
        self.assertEqual(enrollment.status, BenefitProgramStatus.ENROLLED)
        # Create new enrollment for same user with start date of 1/1
        # Use case 5: Existing enrollment found with start date,
        # but no end date, and new start date is the plan start date
        health_plan_start = datetime(day=1, month=1, year=date.today().year, tzinfo=UTC_TIMEZONE)
        person_1_health_plan_start = health_plan_start.replace(
            hour=0, minute=0, second=0, microsecond=0, tzinfo=UTC_TIMEZONE
        )
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_3,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(health_plan_start, None),
        )
        # Check that there are now two program enrollments for this user after updates
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_3)
        self.assertEqual(enrollments.count(), 2)
        last_enrollment = enrollments.first()
        new_enrollment = enrollments.last()
        # Check that the original enrollment is in the "terminated" status
        self.assertEqual(last_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertEqual(last_enrollment.period.lower.date(), person_3_new_start.date())
        self.assertEqual(last_enrollment.period.upper.date(), person_1_health_plan_start.date())
        # Check that a new enrollment is created for this user
        self.assertEqual(new_enrollment.period.lower.date(), person_1_health_plan_start.date())
        self.assertIsNone(new_enrollment.period.upper)
        self.assertEqual(new_enrollment.status, BenefitProgramStatus.ENROLLED)

    def test_update_benefit_program_enrollment_with_multiple_year(self):
        """
        1. create a person and add to Benefit program with 1/1/2023 as start date
        2. add to Benefit program with 1/1/2023 as start date and 11/1/2023 as end date
        3. add to Benefit program with 1/1/2024 as start date
        4. add to Benefit program with 1/1/2024 as start date and 1/1/2025 as end date
        5. add to Benefit program with 1/1/2025 as start date and 1/1/2026 as end date
        """
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.BENEFIT)

        # add to Benefit program with 1/1/2023 as start date
        person_1_start_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year - 1, month=1, day=1, tzinfo=UTC_TIMEZONE
        )
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_1_start_date, None),
        )
        program_enrollments = ProgramEnrollment.objects.filter(
            person=person,
            program=benefit_program,
        )
        self.assertEqual(program_enrollments.count(), 1)
        program_enrollment = program_enrollments.last()
        self.assertEqual(program_enrollment.period.lower, person_1_start_date)
        self.assertEqual(program_enrollment.status, BenefitProgramStatus.ENROLLED)

        # add to Benefit program with 1/1/2023 as start date and 11/1/2023 as end date
        person_1_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year - 1, month=11, day=30, tzinfo=UTC_TIMEZONE
        )
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_1_start_date, person_1_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 2)
        enrolled_program_enrollment = program_enrollments.first()
        self.assertEqual(enrolled_program_enrollment.period.lower, person_1_start_date)
        self.assertEqual(enrolled_program_enrollment.period.upper, person_1_end_date)
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        termianted_program_enrollment = program_enrollments.last()
        self.assertEqual(termianted_program_enrollment.period.lower, person_1_end_date)
        self.assertIsNone(termianted_program_enrollment.period.upper)
        self.assertEqual(termianted_program_enrollment.status, BenefitProgramStatus.TERMINATED)

        # add to Benefit program with 1/1/2024 as start date
        person_2_start_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year, month=1, day=1, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_2_start_date, None),
        )
        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 3)
        termianted_program_enrollment = ProgramEnrollment.objects.get(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        self.assertEqual(termianted_program_enrollment.period.lower, person_1_end_date)
        self.assertEqual(termianted_program_enrollment.period.upper, person_2_start_date)

        latest_program_enrollment = program_enrollments.last()
        self.assertEqual(latest_program_enrollment.period.lower, person_2_start_date)
        self.assertIsNone(latest_program_enrollment.period.upper)
        self.assertEqual(latest_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        # add to Benefit program with 1/1/2024 as start date and 1/1/2025 as end date
        person_2_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 1, month=1, day=1, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_2_start_date, person_2_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 4)
        termianted_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        termianted_program_enrollment_with_end_date = termianted_program_enrollments.first()
        self.assertEqual(termianted_program_enrollment_with_end_date.period.lower, person_1_end_date)
        self.assertEqual(termianted_program_enrollment_with_end_date.period.upper, person_2_start_date)

        termianted_program_enrollment_without_end_date = termianted_program_enrollments.last()
        self.assertEqual(termianted_program_enrollment_without_end_date.period.lower, person_2_end_date)
        self.assertIsNone(termianted_program_enrollment_without_end_date.period.upper)
        self.assertEqual(termianted_program_enrollment_without_end_date.status, BenefitProgramStatus.TERMINATED)

        enrolled_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(enrolled_program_enrollments.count(), 2)

        enrolled_program_enrollment = enrolled_program_enrollments.first()
        self.assertEqual(enrolled_program_enrollment.period.lower, person_1_start_date)
        self.assertEqual(enrolled_program_enrollment.period.upper, person_1_end_date)

        enrolled_program_enrollment = enrolled_program_enrollments.last()
        self.assertEqual(enrolled_program_enrollment.period.lower, person_2_start_date)
        self.assertEqual(enrolled_program_enrollment.period.upper, person_2_end_date)
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        # add to Benefit program with 1/1/2025 as start date and 1/1/2026 as end date
        person_3_start_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 1, month=1, day=1, tzinfo=UTC_TIMEZONE
        )
        person_3_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 2, month=1, day=1, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_3_start_date, person_3_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 5)

        enrolled_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(enrolled_program_enrollments.count(), 3)

        enrolled_program_enrollment = enrolled_program_enrollments.last()
        self.assertEqual(enrolled_program_enrollment.period.lower.date(), person_3_start_date.date())
        self.assertEqual(enrolled_program_enrollment.period.upper.date(), person_3_end_date.date())
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        self.assertEqual(program_enrollments.count(), 2)
        termianted_program_enrollment = program_enrollments.last()
        self.assertEqual(termianted_program_enrollment.period.lower.date(), person_3_end_date.date())
        self.assertIsNone(termianted_program_enrollment.period.upper)

        # Update end date/ terminated date to a future date but start date remains the same
        person_4_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 2, month=10, day=10, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_3_start_date, person_4_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 5)

        enrolled_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(enrolled_program_enrollments.count(), 3)

        enrolled_program_enrollment = enrolled_program_enrollments.last()
        self.assertEqual(enrolled_program_enrollment.period.lower.date(), person_3_start_date.date())
        self.assertEqual(enrolled_program_enrollment.period.upper.date(), person_4_end_date.date())
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        self.assertEqual(program_enrollments.count(), 2)
        termianted_program_enrollment = program_enrollments.last()
        self.assertEqual(termianted_program_enrollment.period.lower.date(), person_4_end_date.date())
        self.assertIsNone(termianted_program_enrollment.period.upper)

        # Update end date/ terminated date to a past date but start date remains the same
        person_5_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 2, month=5, day=5, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_3_start_date, person_5_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 5)

        enrolled_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(enrolled_program_enrollments.count(), 3)

        enrolled_program_enrollment = enrolled_program_enrollments.last()
        self.assertEqual(enrolled_program_enrollment.period.lower.date(), person_3_start_date.date())
        self.assertEqual(enrolled_program_enrollment.period.upper.date(), person_5_end_date.date())
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        self.assertEqual(program_enrollments.count(), 2)
        termianted_program_enrollment = program_enrollments.last()
        self.assertEqual(termianted_program_enrollment.period.lower.date(), person_5_end_date.date())
        self.assertIsNone(termianted_program_enrollment.period.upper)

        # add to Benefit program with 1/1/2028 as start date and 1/1/2029 as end date
        person_4_start_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 3, month=1, day=1, tzinfo=UTC_TIMEZONE
        )
        person_6_end_date = (datetime.now(UTC_TIMEZONE)).replace(
            year=datetime.now(UTC_TIMEZONE).year + 4, month=1, day=1, tzinfo=UTC_TIMEZONE
        )

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(person_4_start_date, person_6_end_date),
        )

        program_enrollments = ProgramEnrollment.objects.filter(person=person, program=benefit_program)
        self.assertEqual(program_enrollments.count(), 7)

        enrolled_program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(enrolled_program_enrollments.count(), 4)

        enrolled_program_enrollment = enrolled_program_enrollments.last()
        self.assertEqual(enrolled_program_enrollment.period.lower.date(), person_4_start_date.date())
        self.assertEqual(enrolled_program_enrollment.period.upper.date(), person_6_end_date.date())
        self.assertEqual(enrolled_program_enrollment.status, BenefitProgramStatus.ENROLLED)

        program_enrollments = ProgramEnrollment.objects.filter(
            person=person, program=benefit_program, status=BenefitProgramStatus.TERMINATED
        )
        self.assertEqual(program_enrollments.count(), 3)
        termianted_program_enrollment = program_enrollments.last()
        self.assertEqual(termianted_program_enrollment.period.lower.date(), person_6_end_date.date())
        self.assertIsNone(termianted_program_enrollment.period.upper)

    @patch(
        "firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._subscribe_to_zus_based_on_risk_score"
    )
    @patch(
        "firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._zus_handle_person_change_event"
    )
    @override_switch(WAFFLE_SWITCH_SUBSCRIBE_TO_ZUS_BASED_ON_RISK_SCORE, active=True)
    def test_program_enrollment_event(self, mock_zus_deactivate, mock_subscribe_to_zus):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person = PersonUserFactory()
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_ENROLLED}",
                "event_date": "11/12/2023",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.get(
            program=care_program,
            person=person,
        )
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertNotEqual(primary_care.period.lower, datetime(2023, 11, 12, tzinfo=UTC_TIMEZONE))
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime(2023, 11, 12, tzinfo=UTC_TIMEZONE).date(),
        )
        self.assertIsNone(primary_care.period.upper)
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {"event": f"{ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT}"},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person)
        not_establised_primary_care = primary_care.first()
        self.assertEqual(not_establised_primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertEqual(
            not_establised_primary_care.period.lower.date(),
            datetime(2023, 11, 12, tzinfo=UTC_TIMEZONE).date(),
        )
        self.assertEqual(
            not_establised_primary_care.period.upper.date(),
            datetime.now().date(),
        )
        establised_primary_care = primary_care.last()
        self.assertEqual(establised_primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)
        self.assertEqual(establised_primary_care.period.lower.date(), datetime.now().date())
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_UNENROLLED}",
                "event_date": "11/12/2023",
                "reason": "can't get in touch",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person)
        establised_primary_care = primary_care.filter(status=PrimaryCareProgramStatus.ESTABLISHED).first()
        self.assertEqual(establised_primary_care.status, PrimaryCareProgramStatus.ESTABLISHED)
        self.assertIsNotNone(establised_primary_care.period.lower)
        self.assertIsNotNone(establised_primary_care.period.upper)
        churned_primary_care = primary_care.last()
        self.assertEqual(churned_primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(churned_primary_care.reason, "can't get in touch")
        self.assertIsNotNone(churned_primary_care.period.lower)
        self.assertIsNone(churned_primary_care.period.upper)
        mock_zus_deactivate.assert_called_once()

        mock_subscribe_to_zus.reset_mock()
        response = self.client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_REENROLLED}",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_subscribe_to_zus.assert_called_once()

        primary_care = ProgramEnrollment.objects.filter(program=care_program, person=person)
        churned_primary_care = primary_care.filter(status=PrimaryCareProgramStatus.CHURNED).first()
        self.assertEqual(churned_primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(churned_primary_care.reason, "can't get in touch")
        self.assertIsNotNone(churned_primary_care.period.lower)
        self.assertIsNotNone(churned_primary_care.period.upper)

        not_establised_primary_care = primary_care.last()
        self.assertEqual(not_establised_primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNotNone(not_establised_primary_care.period.lower)
        self.assertIsNone(not_establised_primary_care.period.upper)

    def test_get_program_enrollment(self):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        person = PersonUserFactory()
        self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {"event": f"{ProgramEnrollmentEvents.PROGRAM_ENROLLED}"},
            format="json",
        )
        response = self.provider_client.get(f"/programs/people/{person.id}/enrollment/")
        result = response.json()
        self.assertEqual(result[0]["person"], person.id)
        self.assertEqual(result[0]["program"], care_program.uid)
        self.assertEqual(result[0]["status"], PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNone(result[0]["established_date"])
        self.assertIsNotNone(result[0]["period"]["lower"])
        self.assertIsNone(result[0]["period"]["upper"])
        self.assertTrue(person_is_enrolled_in_program(person, care_program))
        start_time = datetime.now(UTC_TIMEZONE)
        appt = AppointmentFactory(patient=person.user, patient_joined_video=True, start=start_time)
        _mark_completed_appointment(appt)
        appt = AppointmentFactory(
            patient=person.user,
            patient_joined_video=True,
            start=start_time + timedelta(days=2),
        )
        _mark_completed_appointment(appt)
        appt.refresh_from_db()
        response = self.provider_client.get(f"/programs/people/{person.id}/enrollment/")
        result = response.json()
        self.assertEqual(len(result), 2)
        self.assertEqual(result[1]["status"], PrimaryCareProgramStatus.ESTABLISHED)
        self.assertTrue(person_is_enrolled_in_program(person, care_program))
        self.assertEqual(
            result[1]["established_date"],
            start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
        )
        self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {"event": f"{ProgramEnrollmentEvents.PROGRAM_UNENROLLED}"},
            format="json",
        )
        response = self.provider_client.get(f"/programs/people/{person.id}/enrollment/")
        result = response.json()
        self.assertEqual(len(result), 3)
        self.assertEqual(result[2]["person"], person.id)
        self.assertEqual(result[2]["program"], care_program.uid)
        self.assertEqual(result[2]["status"], PrimaryCareProgramStatus.CHURNED)
        self.assertFalse(person_is_enrolled_in_program(person, care_program))
        self.assertIsNone(result[0]["established_date"])
        self.assertIsNotNone(result[0]["period"]["lower"])
        self.assertIsNotNone(result[0]["period"]["upper"])

        self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {"event": f"{ProgramEnrollmentEvents.PROGRAM_REENROLLED}"},
            format="json",
        )
        response = self.provider_client.get(f"/programs/people/{person.id}/enrollment/")
        result = response.json()
        self.assertEqual(len(result), 4)
        self.assertEqual(result[2]["person"], person.id)
        self.assertEqual(result[2]["program"], care_program.uid)
        self.assertEqual(result[2]["status"], PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(result[2]["established_date"])
        self.assertIsNotNone(result[2]["period"]["lower"])
        self.assertIsNotNone(result[2]["period"]["upper"])
        self.assertEqual(result[3]["person"], person.id)
        self.assertEqual(result[3]["program"], care_program.uid)
        self.assertEqual(result[3]["status"], PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertTrue(person_is_enrolled_in_program(person, care_program))
        self.assertIsNotNone(result[1]["established_date"])
        self.assertIsNotNone(result[3]["period"]["lower"])
        self.assertIsNone(result[3]["period"]["upper"])
        self.assertTrue(person_is_enrolled_in_program(person, care_program))

        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(datetime.now(UTC_TIMEZONE) - timedelta(days=20), None),
        )
        response = self.provider_client.get(f"/programs/people/{person.id}/enrollment/")
        result = response.json()
        self.assertEqual(len(result), 5)
        self.assertEqual(result[0]["person"], person.id)
        self.assertEqual(result[0]["program"], benefit_program.uid)
        self.assertEqual(result[0]["status"], BenefitProgramStatus.ENROLLED)
        self.assertTrue(person_is_enrolled_in_program(person, benefit_program))

    @patch(
        "firefly.modules.programs.enrollment.PrimaryCareProgramEnrollmentSideEffects._zus_handle_person_change_event"
    )
    def test_discharge_workflow(self, mock_zus_deactivation):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person = PersonUserFactory()
        program_info = add_person_to_program(
            person=person,
            program_uid=ProgramCodes.PRIMARY_CARE,
        )
        state_machine_content = {
            "transitions": [
                {
                    "dest": "Done",
                    "source": ["Approval Pending", "Emergency Care"],
                    "trigger": "Done",
                },
                {
                    "dest": "Approval Pending",
                    "source": ["Draft"],
                    "trigger": "Approval Pending",
                },
                {"dest": "Will Not Do", "source": "*", "trigger": "Will Not Do"},
                {
                    "dest": "Emergency Care",
                    "source": ["Approval Pending"],
                    "trigger": "Emergency Care",
                },
                {
                    "dest": "Will Not Do",
                    "source": "*",
                    "trigger": "deferred",
                    "system_action": "system_close",
                },
            ],
            "initial_state": "Draft",
            "state_with_categories": [
                {
                    "state": {"name": "Draft", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 0, "use_business_days": None}],
                },
                {
                    "state": {
                        "name": "Will Not Do",
                        "on_enter": "cancel_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "deferred",
                },
                {
                    "state": {
                        "name": "Approval Pending",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                    "due_date": [{"days": 1, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Emergency Care",
                        "on_enter": "approve_pending_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                    "due_date": [{"days": 30, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Done",
                        "on_enter": "complete_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Member Discharge",
            content=state_machine_content,
        )

        category = CaseCategory.objects.create(
            title="Member Discharge",
            unique_key="member_discharge",
            state_machine_definition=state_machine_definition,
            description="default",
        )
        self.provider_client.patch(
            f"/programs/primary-care/{person.user.id}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )
        program_enrollment = (
            ProgramEnrollment.objects.filter(
                person=person,
                program=care_program,
            )
            .order_by("updated_at")
            .first()
        )
        program_info.refresh_from_db()
        self.assertEqual(
            program_info.pending_discharge_reason,
            ChurnedDischargedReasons.ELECTED_TO_LEAVE,
        )
        self.assertEqual(
            program_enrollment.status,
            PrimaryCareProgramStatus.NOT_ESTABLISHED,
        )
        self.assertEqual(
            program_enrollment.reason,
            f"Discharged:{ChurnedDischargedReasons.ELECTED_TO_LEAVE} (Discharge in progress)",
        )
        mock_zus_deactivation.assert_not_called()
        discharge_case = Case.objects.filter(
            person=person,
            category=category,
        ).first()
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        self.assertEqual(
            discharge_case.status,
            DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING,
        )
        # cancel_discharge should move them to Not Established
        self.provider_client.patch(
            f"/programs/primary-care/{person.user.id}/pending-discharge/v2",
            {"pending_discharge_reason": None},
            format="json",
        )
        self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.CANCEL_DISCHARGE}",
            },
            format="json",
        )
        program_enrollment.refresh_from_db()
        self.assertIsNone(program_enrollment.reason)
        self.assertEqual(program_enrollment.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNone(program_enrollment.period.upper)
        self.provider_client.patch(
            f"/programs/primary-care/{person.user.id}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        discharge_case.action = DischargeMemberCaseStatuses.EMERGENCY_CARE
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.EMERGENCY_CARE)
        program_info.refresh_from_db()
        self.assertIsNotNone(program_info.discharge_pending_approved_at)
        program_enrollment.refresh_from_db()
        self.assertEqual(
            program_enrollment.status,
            PrimaryCareProgramStatus.NOT_ESTABLISHED,
        )
        self.assertEqual(
            program_enrollment.reason,
            f"Discharged:{ChurnedDischargedReasons.ELECTED_TO_LEAVE} (Discharge in progress)",
        )
        self.assertIsNone(program_enrollment.period.upper)
        self.assertEqual(program_info.active, True)
        # test pending discharge only check program enrollment discharge reason
        program_info.pending_discharge_reason = None
        program_info.save()
        discharge_case.action = DischargeMemberCaseStatuses.DONE
        discharge_case.save()
        program_info.refresh_from_db()
        program_enrollment.refresh_from_db()
        self.assertEqual(program_info.active, False)

        active_program_enrollment = ProgramEnrollment.objects.filter(
            person=person, status=PrimaryCareProgramStatus.NOT_ESTABLISHED
        ).first()
        self.assertIsNotNone(active_program_enrollment.period.lower)
        self.assertIsNotNone(active_program_enrollment.period.upper)
        self.assertIsNone(active_program_enrollment.reason)
        churned_program_enrollment = ProgramEnrollment.objects.filter(
            person=person, status=PrimaryCareProgramStatus.CHURNED
        ).first()
        self.assertEqual(churned_program_enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(churned_program_enrollment.reason, f"Discharged:{ChurnedDischargedReasons.ELECTED_TO_LEAVE}")
        self.assertIsNotNone(churned_program_enrollment.period.lower)
        self.assertIsNone(churned_program_enrollment.period.upper)
        mock_zus_deactivation.assert_called_once()

    @skip("flaky")
    def test_program_reenrollment_workflow(self):
        care_program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person = PersonUserFactory()
        # scenerio 1: on re enroll event onboarding state move back to signed Up
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_ENROLLED}",
                "event_date": "11/12/2023",
            },
            format="json",
        )
        person.user.onboarding_state.to_signedup()
        person.user.onboarding_state.downloaded_app_at = datetime.now(UTC_TIMEZONE)
        person.user.onboarding_state.save(update_fields=["downloaded_app_at"])
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.get(
            program=care_program,
            person=person,
        )
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2023", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertTrue(person_is_in_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE))
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_UNENROLLED}",
                "event_date": "11/12/2024",
                "reason": "can't get in touch",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2024", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        self.assertFalse(
            ProgramInfo.objects.filter(person=person, program_id=ProgramCodes.PRIMARY_CARE, active=True).exists()
        )
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_REENROLLED}",
                "event_date": "11/12/2025",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2025", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        person.user.onboarding_state.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertTrue(person_is_in_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE))

        # scenerio 2: on re enroll event onboarding state move back to member
        person.user.onboarding_state.to_member()
        person.user.onboarding_state.care_team_selection_at = datetime.now(UTC_TIMEZONE)
        person.user.onboarding_state.save(update_fields=["care_team_selection_at"])
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_UNENROLLED}",
                "event_date": "11/12/2026",
                "reason": "can't get in touch",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2026", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_REENROLLED}",
                "event_date": "11/12/2027",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2027", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        person.user.onboarding_state.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.MEMBER)

        # scenerio 3: on re enroll event onboarding state move back to initialized
        person.user.onboarding_state.to_initialized()
        person.user.onboarding_state.care_team_selection_at = None
        person.user.onboarding_state.downloaded_app_at = None
        person.user.onboarding_state.save(update_fields=["care_team_selection_at", "downloaded_app_at"])
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_UNENROLLED}",
                "event_date": "11/12/2028",
                "reason": "can't get in touch",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2028", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        response = self.provider_client.post(
            f"/programs/{care_program.uid}/people/{person.id}/enrollment/event/",
            {
                "event": f"{ProgramEnrollmentEvents.PROGRAM_REENROLLED}",
                "event_date": "11/12/2029",
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        primary_care = ProgramEnrollment.objects.filter(person=person, program=care_program).last()
        self.assertEqual(primary_care.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertEqual(
            primary_care.period.lower.date(),
            datetime.strptime("11/12/2029", "%m/%d/%Y").date(),
        )
        self.assertIsNone(primary_care.period.upper)
        person.user.onboarding_state.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.INITIALIZED)

    def test_unenroll_primary_care_program(self):
        enrollment_1 = self._get_unenrolled_enrollment({"reason": "Test"})
        self.assertEqual(enrollment_1.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(enrollment_1.reason, "Test")
        self.assertIsNone(enrollment_1.reason_code)
        enrollment_2 = self._get_unenrolled_enrollment({"reason": "Discharged"})
        self.assertEqual(enrollment_2.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(enrollment_2.reason, "Discharged")
        self.assertEqual(enrollment_2.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)
        enrollment_3 = self._get_unenrolled_enrollment({"is_discharge": True})
        self.assertEqual(enrollment_3.status, PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(enrollment_3.reason)
        self.assertEqual(enrollment_3.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)
        enrollment_4 = self._get_unenrolled_enrollment({"is_discharge": False})
        self.assertEqual(enrollment_4.status, PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(enrollment_4.reason)
        self.assertIsNone(enrollment_4.reason_code)
        enrollment_5 = self._get_unenrolled_enrollment({"reason": "Discharged", "is_discharge": True})
        self.assertEqual(enrollment_5.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(enrollment_5.reason, "Discharged")
        self.assertEqual(enrollment_5.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)
        person_6 = PersonUserFactory()
        add_person_to_program(person_6, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(person_6, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person_6, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(person_6, ProgramCodes.PRIMARY_CARE, data={"reason": "Discharged"})
        enrollment_6 = (
            get_person_program_enrollments(person_6, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
        self.assertEqual(enrollment_6.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(enrollment_6.reason, "Discharged")
        self.assertEqual(enrollment_6.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)

    def test_unenroll_benefit_program(self):
        benefit_program, _ = Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        benefit_program_info_1 = add_person_to_program(person_1, ProgramCodes.BENEFIT)
        benefit_program_info_2 = add_person_to_program(person_2, ProgramCodes.BENEFIT)
        start_date = datetime.now(UTC_TIMEZONE) - timedelta(days=7)
        end_date = datetime.now(UTC_TIMEZONE) - timedelta(days=1)
        update_program_enrollment(
            program_uid=ProgramCodes.BENEFIT,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            enrollment_period=(start_date, None),
        )
        enrollment_period = (start_date, end_date)
        # Unenroll member with existing enrollment from benefit program, terminating their enrollment
        benefit_program_info_1.on_unenroll(data={"enrollment_period": enrollment_period})
        # Check that there are 2 program enrollment for this user after updates
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_1)
        self.assertEqual(enrollments.count(), 2)
        enrolled_enrollment = enrollments.first()
        # Check that the enrollment is in the "Enrolled" status
        self.assertEqual(enrolled_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertIsNotNone(enrolled_enrollment.period.lower)
        self.assertIsNotNone(enrolled_enrollment.period.upper)

        enrolled_enrollment = enrollments.last()
        # Check that the enrollment is in the "terminated" status
        self.assertEqual(enrolled_enrollment.status, BenefitProgramStatus.TERMINATED)
        self.assertIsNotNone(enrolled_enrollment.period.lower)
        self.assertIsNone(enrolled_enrollment.period.upper)
        # Unenroll member from benefit program, creating new enrollment with "terminated" status
        benefit_program_info_2.on_unenroll(data={"enrollment_period": enrollment_period})
        # Check that there is a program enrollment
        enrollments = ProgramEnrollment.objects.filter(program=benefit_program, person=person_2)
        self.assertEqual(enrollments.count(), 2)
        enolled_enrollment = enrollments.first()
        # Check that the enrollment is in the "ENROLLED" status
        self.assertEqual(enolled_enrollment.status, BenefitProgramStatus.ENROLLED)
        self.assertIsNotNone(enolled_enrollment.period.lower)
        self.assertIsNotNone(enolled_enrollment.period.upper)
        terminated_enrollment = enrollments.last()
        # Check that the enrollment is in the "Terminated" status
        self.assertEqual(terminated_enrollment.status, BenefitProgramStatus.TERMINATED)
        self.assertIsNotNone(terminated_enrollment.period.lower)
        self.assertIsNone(terminated_enrollment.period.upper)

    def _get_unenrolled_enrollment(self, data):
        person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(person, ProgramCodes.PRIMARY_CARE, data=data)
        return (
            get_person_program_enrollments(person, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
