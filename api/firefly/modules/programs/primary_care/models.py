import logging

from django.contrib.auth.models import Group
from django.db import models
from django.utils import timezone
from django_deprecate_fields import deprecate_field

from firefly.core.assignment.utils import get_user_from_role_in_care_team
from firefly.core.user.constants import NP_ROLE, RISK_SCORE_CHOICES
from firefly.core.user.models.models import User
from firefly.core.user.utils import get_default_caregiver_practice, get_default_primary_physician
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.onboarding.constants import CHURNED_DISCHARGED_REASON_CHOICES, DISCHARGED_REASON_CHOICES
from firefly.modules.physician.models import Physician
from firefly.modules.practice.models import Practice
from firefly.modules.programs.constants import PrimaryCareProgramStatus, ProgramEnrollmentEvents
from firefly.modules.programs.models import ProgramEnrollment, ProgramInfo
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import (
    assign_cyd_and_onboarding_assessment,
    get_person_program_enrollments,
    update_program_enrollment,
)

logger = logging.getLogger(__name__)


class PrimaryCareProgramInfo(SaveHandlersMixin, ProgramInfo):
    PROGRAM_CODE = ProgramCodes.PRIMARY_CARE

    primary_physician = models.ForeignKey(
        Physician,
        related_name="primary_patient",
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        default=get_default_primary_physician,
    )
    caregiver_practice = models.ForeignKey(
        Practice,
        related_name="patient",
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        default=get_default_caregiver_practice,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    risk_score = models.CharField(max_length=15, choices=RISK_SCORE_CHOICES, null=True, blank=True)  # noqa: TID251
    has_blood_pressure_cuff = models.BooleanField(blank=True, null=True)

    # --- Fields related to the pending discharge workflow ---
    # Once the pending period is over, this reason will be the eventual discharge reason
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    discharge_pending_reason = models.CharField(  # noqa: TID251
        max_length=255, null=True, blank=True, choices=DISCHARGED_REASON_CHOICES
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pending_discharge_reason = models.CharField(  # noqa: TID251
        max_length=255, null=True, blank=True, choices=CHURNED_DISCHARGED_REASON_CHOICES
    )
    discharge_pending_started_at = models.DateTimeField(null=True, blank=True)
    discharge_pending_approved_at = models.DateTimeField(null=True, blank=True)
    # e.g. letter to patient through Universal
    discharge_pending_communication_sent_at = models.DateTimeField(null=True, blank=True)

    date_of_last_awv = models.DateField(null=True, blank=True)
    date_of_last_external_awv = models.DateField(null=True, blank=True)
    # store latest of date_of_last_awv and date_of_last_external_awv
    last_wellness_checked_at = models.DateField(null=True, blank=True)
    is_pregnant_at = deprecate_field(models.DateTimeField(null=True, blank=True))

    wellness_visit_reminder_1_sent_at = models.DateTimeField(null=True, blank=True)

    wellness_visit_reminder_2_sent_at = models.DateTimeField(null=True, blank=True)

    wellness_visit_reminder_3_sent_at = models.DateTimeField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_awv_reminder_id = models.CharField(max_length=255, default="", blank=True, null=True)  # noqa: TID251

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.core.services.zus.utils.utils import fetch_new_patient_history
        from firefly.modules.change_data_capture.subscribers.elation.subscribers import (
            elation_update_user,
        )

        if changed("primary_physician"):
            logger.info("Person %d: Invoking elation update", self.person.id)
            elation_update_user.send(person_id=self.person.id)
        if self.person.care_team.exists():
            logger.info("Primary physician added, Zus HIE call for person %s", self.person.id)
            fetch_new_patient_history(person_id=self.person.id)

    def on_enroll(self, *args, **kwargs):
        log_prefix: str = "on_primary_care_enroll"
        try:
            care_team_np: User = get_user_from_role_in_care_team(Group.objects.get(name=NP_ROLE), self.person)
            if care_team_np:
                self.primary_physician = care_team_np.providerdetail.physician
        except Exception:
            logger.exception(
                "%s : Unable to assign primary physician from care team NP to person: %s", log_prefix, self.person
            )
        super().on_enroll(*args, **kwargs)
        event_date = (kwargs.get("data") if kwargs.get("data") else {}).get("event_date")
        program_event = (kwargs.get("data") if kwargs.get("data") else {}).get(
            "program_event", ProgramEnrollmentEvents.PROGRAM_ENROLLED
        )
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=self.person,
            program_event=program_event,
            event_date=event_date,
        )
        assign_cyd_and_onboarding_assessment(person=self.person, log_prefix=log_prefix)
        from firefly.modules.attribution.utils import recalculate_contract_and_attribution

        recalculate_contract_and_attribution(person=self.person)

    def on_unenroll(self, *args, **kwargs):
        super().on_unenroll(*args, **kwargs)
        data = kwargs.get("data", {}) or {}
        event_date = data.get("event_date") if data.get("event_date") else timezone.now()
        reason = data.get("reason")
        is_discharge = True if reason and "discharge" in reason.lower() or data.get("is_discharge") else False
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=self.person,
            program_event=ProgramEnrollmentEvents.PROGRAM_UNENROLLED,
            reason=reason,
            event_date=event_date,
        )
        if is_discharge:
            enrollments = (
                get_person_program_enrollments(self.person, include_primary_care_churned=True)
                .filter(program__uid=ProgramCodes.PRIMARY_CARE, status=PrimaryCareProgramStatus.CHURNED)
                .order_by("-period__startswith")
            )
            if enrollments.exists():
                enrollment = enrollments.first()
                enrollment.reason_code = ProgramEnrollment.ReasonCode.DISCHARGED.label
                enrollment.save()
            else:
                logger.error('Error marking primary care program enrollment "discharged": churned enrollment not found')
