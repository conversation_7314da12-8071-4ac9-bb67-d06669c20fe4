from datetime import date, datetime, timedelta
from unittest import mock
from unittest.mock import patch

from django.conf import settings
from django.contrib.auth.models import Group
from django.core.management import call_command
from django.utils import timezone

from firefly.bff.app.utils import get_appointment_physician_name
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import HEALTH_GUIDE_ROLE, MD_ROLE, NP_ROLE, RN_ROLE
from firefly.core.user.factories import PersonUserFactory, ProviderDetailFactory
from firefly.modules.cases.constants import DischargeMemberCaseStatuses
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.onboarding.constants import ChurnedDischargedReasons, DischargedReason
from firefly.modules.onboarding.statemachine.constants import OnboardingTransitions
from firefly.modules.programs.constants import PrimaryCareProgramStatus, ProgramEnrollmentEvents
from firefly.modules.programs.models import Program, ProgramEnrollment
from firefly.modules.programs.primary_care.models import PrimaryCareProgramInfo
from firefly.modules.programs.primary_care.sync_risk_score import RiskScoreSync
from firefly.modules.programs.primary_care.utils import (
    DISCHARGE_TEXT,
    get_churned_former_established_members,
    initiate_discharge_for_established_member_with_invalid_insurance,
)
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.tasks import backfill_discharge_pending_cases
from firefly.modules.programs.utils import (
    active_or_inactive_program_info_for_person_program,
    add_person_to_program,
    get_person_program_enrollments,
    is_current_enrollment_status,
    program_enrollment_for_person_program,
    program_info_for_person_program,
    remove_person_from_program,
    update_program_enrollment,
)
from firefly.modules.quality.management.commands.sync_elation_care_gaps import QUALITY_PROGRAM
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import SOURCE_TYPES, Task, TaskCollection, TaskCollectionTask
from firefly.modules.work_units.constants import StatusCategory


class PrimaryCareProgramInfoTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": ["Approval Pending", "Emergency Care"], "trigger": "Done"},
                {"dest": "Approval Pending", "source": ["Draft"], "trigger": "Approval Pending"},
                {"dest": "Will Not Do", "source": "*", "trigger": "Will Not Do"},
                {"dest": "Emergency Care", "source": ["Approval Pending"], "trigger": "Emergency Care"},
                {"dest": "Will Not Do", "source": "*", "trigger": "deferred", "system_action": "system_close"},
            ],
            "initial_state": "Draft",
            "state_with_categories": [
                {
                    "state": {"name": "Draft", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 0, "use_business_days": None}],
                },
                {
                    "state": {"name": "Will Not Do", "on_enter": "cancel_discharge", "ignore_invalid_triggers": None},
                    "category": "deferred",
                },
                {
                    "state": {"name": "Approval Pending", "ignore_invalid_triggers": None},
                    "category": "in_progress",
                    "due_date": [{"days": 1, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Emergency Care",
                        "on_enter": "approve_pending_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                    "due_date": [{"days": 30, "use_business_days": True}],
                },
                {
                    "state": {"name": "Done", "on_enter": "complete_discharge", "ignore_invalid_triggers": None},
                    "category": "complete",
                },
            ],
        }

        state_machine_definition = StateMachineDefinition.objects.create(
            title="Member Discharge",
            content=state_machine_content,
        )

        self.category = CaseCategory.objects.create(
            title="Member Discharge",
            unique_key="member_discharge",
            state_machine_definition=state_machine_definition,
            description="default",
        )

    @mock.patch("firefly.modules.tasks.collections.create_tasks_from_task_template")
    def test_enroll_management_command(self, mock_create_task_from_template):
        Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        task_collection_task, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR
        )
        task_collection_task_1, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE,
        )

        pcp_person = PersonUserFactory.create()
        add_person_to_program(person=pcp_person, program_uid=ProgramCodes.PRIMARY_CARE)
        mock_create_task_from_template.assert_called()
        mock_create_task_from_template.reset_mock()

        # If we assign a task from this task collection to the patient, adding the patient to the program again should
        # NOT create the task again
        Task.objects.create(
            title="Segmentation task",
            owner_group=None,
            priority=2,
            source_type=SOURCE_TYPES["formsubmission"],
            patient=pcp_person.user,
            autocreated_from=task_collection_task_1,
        )
        Task.objects.create(
            title="Some task",
            owner_group=None,
            priority=2,
            source_type=SOURCE_TYPES["formsubmission"],
            patient=pcp_person.user,
            autocreated_from=task_collection_task,
        )
        add_person_to_program(person=pcp_person, program_uid=ProgramCodes.PRIMARY_CARE)
        mock_create_task_from_template.assert_not_called()

        no_program_person = PersonUserFactory.create()

        self.assertEqual(pcp_person.program_info.count(), 1)
        self.assertEqual(no_program_person.program_info.count(), 0)

        # Test that the no_program_person is not enrolled in any program
        self.assertEqual(pcp_person.program_info.count(), 1)
        self.assertEqual(no_program_person.program_info.count(), 0)

    @mock.patch("firefly.modules.tasks.collections.create_tasks_from_task_template")
    def test_assign_segmentation_capture_on_enroll(self, mock_create_task_from_template):
        Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        task_collection_task, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR,
        )
        task_collection, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")

        task_collection_task_2, _ = TaskCollectionTask.objects.update_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE,
            defaults={
                "task_collection": task_collection,
                "title": "Segmentation Capture",
                "source_type": SOURCE_TYPES["formsubmission"],
            },
        )

        pcp_person = PersonUserFactory.create()
        add_person_to_program(person=pcp_person, program_uid=ProgramCodes.PRIMARY_CARE)
        # If we assign a task from this task collection to the patient, adding the patient to the program again should
        # NOT create the task again
        Task.objects.create(
            title="Some task",
            owner_group=None,
            priority=2,
            source_type=SOURCE_TYPES["formsubmission"],
            patient=pcp_person.user,
            autocreated_from=task_collection_task,
        )
        Task.objects.create(
            title="Segmentation Capture",
            owner_group=None,
            priority=0,
            source_type=SOURCE_TYPES["formsubmission"],
            patient=pcp_person.user,
            autocreated_from=task_collection_task_2,
        )

        add_person_to_program(person=pcp_person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertEqual(mock_create_task_from_template.call_count, 2)

    def test_get_discharge_pending(self):
        person = PersonUserFactory()
        endpoint = f"/programs/primary-care/{person.user.pk}/pending-discharge/v2"
        result = self.provider_client.get(endpoint)
        self.assertEqual(result.status_code, 404)
        add_person_to_program(
            person=person,
            program_uid=ProgramCodes.PRIMARY_CARE,
        )
        result = self.provider_client.get(endpoint)
        self.assertEqual(result.status_code, 200)
        self.assertEqual(result.json()["pending_discharge_reason"], None)
        self.assertEqual(result.json()["discharge_pending_approved_at"], None)

    def test_patch_discharge_pending(self):
        endpoint = f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2"
        program_info = add_person_to_program(
            person=self.patient.person,
            program_uid=ProgramCodes.PRIMARY_CARE,
        )
        # Test Bad request for a bad reason
        result = self.provider_client.patch(
            endpoint,
            {"pending_discharge_reason": "bad_reason"},
            format="json",
        )
        self.assertEqual(result.status_code, 400)
        self.assertIsNotNone(result.json()["pending_discharge_reason"])
        # Given a new pending discharge is started
        self.assertIsNone(program_info.discharge_pending_reason)
        self.assertIsNone(program_info.pending_discharge_reason)
        self.assertIsNone(program_info.discharge_pending_started_at)
        self.assertIsNone(program_info.discharge_pending_approved_at)
        self.assertIsNone(program_info.discharge_pending_communication_sent_at)
        # Test cancellation of discharge
        result = self.provider_client.patch(
            endpoint,
            {"pending_discharge_reason": None},
            format="json",
        )
        self.assertEqual(result.status_code, 200)
        program_info.refresh_from_db()
        self.assertIsNone(program_info.discharge_pending_reason)
        self.assertIsNone(program_info.pending_discharge_reason)
        self.assertIsNone(program_info.discharge_pending_started_at)
        self.assertIsNone(program_info.discharge_pending_approved_at)
        self.assertIsNone(program_info.discharge_pending_communication_sent_at)
        # Test successful for a valid new reason and mapping to the old reason
        result = self.provider_client.patch(
            endpoint,
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )
        program_info.refresh_from_db()
        self.assertEqual(program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertIsNotNone(program_info.discharge_pending_started_at)
        discharge_pending_started_at = program_info.discharge_pending_started_at
        self.assertIsNone(program_info.discharge_pending_approved_at)
        self.assertIsNone(program_info.discharge_pending_communication_sent_at)
        # Given an attempt to start a pending discharge after it has already started
        # the fields should not reset
        program_info.discharge_pending_approved_at = datetime.utcnow()
        result = self.provider_client.patch(
            endpoint,
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )
        program_info.refresh_from_db()
        self.assertEqual(program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertEqual(program_info.discharge_pending_started_at, discharge_pending_started_at)
        # Sending an empty pending discharge reason should reset the flow
        program_info.discharge_pending_approved_at = datetime.utcnow()
        result = self.provider_client.patch(
            endpoint,
            {"pending_discharge_reason": None},
            format="json",
        )
        program_info.refresh_from_db()
        self.assertIsNone(program_info.discharge_pending_reason)
        self.assertIsNone(program_info.pending_discharge_reason)
        self.assertIsNone(program_info.discharge_pending_started_at)
        self.assertIsNone(program_info.discharge_pending_approved_at)
        self.assertIsNone(program_info.discharge_pending_communication_sent_at)

    def test_set_primary_physician_from_care_team_on_enroll(self):
        def get_member_by_group(care_team_members, group):
            for care_team_member in care_team_members:
                if care_team_member.get("group") == group:
                    return care_team_member
            return None

        self.provider_1 = ProviderDetailFactory.create()
        self.provider_2 = ProviderDetailFactory.create()
        self.provider_3 = ProviderDetailFactory.create()
        self.provider_4 = ProviderDetailFactory.create()
        self.provider_1.internal_role = None
        self.provider_1.save()

        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)
        group_rn, _ = Group.objects.get_or_create(name=RN_ROLE)

        group_md.user_set.add(self.provider_1.user)
        group_np.user_set.add(self.provider_2.user)
        group_hg.user_set.add(self.provider_3.user)
        group_rn.user_set.add(self.provider_4.user)

        payload = {
            "care_team": {
                "md": self.provider_1.user_id,
                "np": self.provider_2.user_id,
                "rn": self.provider_4.user_id,
                "health_guide": self.provider_3.user_id,
                "behavioral_health": None,
            }
        }
        url = "/user/{}/care-team/v2/"
        set_careteam = self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)

        # Add user to primary care program
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)

        url = "/user/{}/care-team/v2/"
        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()
        actual_names_with_title = [physician["physician_name_with_title"] for physician in care_team_members]
        expected_name = get_appointment_physician_name(self.provider_1.physician)
        self.assertTrue(expected_name in actual_names_with_title)

        self.assertEqual(
            get_member_by_group(care_team_members, NP_ROLE).get("id"),
            self.patient.person.primary_physician.provider.user.id,
        )

        self.provider_2.user.is_active = False
        self.provider_2.user.save()
        url = "/user/{}/care-team/v2/?isActive=${}"
        response = self.client.get(url.format(self.patient.id, True))
        care_team_members = response.json()
        (self.assertIsNone(get_member_by_group(care_team_members, NP_ROLE)),)

    @patch("firefly.modules.programs.primary_care.models.update_program_enrollment")
    def test_primary_care_program_enrollment(self, mock_update_program_enrollment):
        person_1 = PersonUserFactory()
        add_person_to_program(person=person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        mock_update_program_enrollment.assert_called_once_with(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            program_event=ProgramEnrollmentEvents.PROGRAM_ENROLLED,
            event_date=None,
        )
        mock_update_program_enrollment.reset_mock()
        remove_person_from_program(person=person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        mock_update_program_enrollment.assert_called_once()
        person_1.refresh_from_db()

    def test_reenroll_primary_care(self):
        person = PersonUserFactory.create()
        program_info = add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info.discharge_pending_approved_at = datetime.now()
        program_info.discharge_pending_started_at = datetime.now()
        program_info.discharge_pending_communication_sent_at = datetime.now()
        program_info.pending_discharge_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info.discharge_pending_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info.save(
            update_fields=[
                "discharge_pending_approved_at",
                "discharge_pending_communication_sent_at",
                "discharge_pending_started_at",
                "pending_discharge_reason",
                "discharge_pending_reason",
            ]
        )
        self.assertIsNotNone(program_info.discharge_pending_approved_at)
        self.assertIsNotNone(program_info.discharge_pending_started_at)
        self.assertIsNotNone(program_info.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info.pending_discharge_reason)
        self.assertIsNotNone(program_info.discharge_pending_reason)
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info = program_info_for_person_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        self.assertIsNone(program_info.discharge_pending_approved_at)
        self.assertIsNone(program_info.discharge_pending_started_at)
        self.assertIsNone(program_info.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info.pending_discharge_reason)
        self.assertIsNone(program_info.discharge_pending_reason)
        self.assertEqual(program_info.active, True)


class RiskScoreSyncTestCase(FireflyTestCase):
    @patch("firefly.modules.programs.primary_care.sync_risk_score.LookerSync.get_looker_results")
    def test_risk_score_sync(self, mock_looker_result):
        # Scenario
        # Update risk score of patient
        # Expected Behavior
        # Should update patient risk score

        mock_looker_result.return_value = [
            {
                "risk_stratification.person_id": self.patient.person.id,
                "risk_stratification.patient_id": self.patient.id,
                "risk_stratification.final_risk_score": "High",
            }
        ]

        add_person_to_program(self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        person_program_info = program_info_for_person_program(self.patient.person, ProgramCodes.PRIMARY_CARE)

        sync = RiskScoreSync()
        sync.sync()

        person_program_info = program_info_for_person_program(self.patient.person, ProgramCodes.PRIMARY_CARE)

        self.assertEquals(
            person_program_info.risk_score,
            mock_looker_result.return_value[0]["risk_stratification.final_risk_score"],
        )

        # Scenario
        # Skip updating risk score of patients which already have a risk score
        # Expected Behavior
        # should not update risk score

        person_program_info.risk_score = "Intermediate"
        person_program_info.save()

        sync.sync()

        self.assertNotEquals(
            person_program_info.risk_score,
            mock_looker_result.return_value[0]["risk_stratification.final_risk_score"],
        )


class BackfillLastAwvDatesTestCase(FireflyTestCase):
    @patch("firefly.core.services.elation.care_gaps.client.ElationCareGapsClient.update_care_gap")
    @patch(
        "firefly.modules.programs.primary_care.management.commands.backfill_last_awv_dates_in_primary_care_model.get_most_recent_awv_date_rows"
    )
    def test_backfill_last_awv_visit_dates_in_primary_care_model(
        self, mock_get_measure_report_rows, mock_update_care_gap
    ):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        person_3 = PersonUserFactory()
        mock_get_measure_report_rows.return_value = [
            {
                "PATIENT_ID": person_1.user.id,
                "DATE_OF_LAST_AWV": date(2021, 1, 1),
            },
            {
                "PATIENT_ID": person_2.user.id,
                "DATE_OF_LAST_AWV": date(2022, 1, 1),
            },
            {
                "PATIENT_ID": person_3.user.id,
                "DATE_OF_LAST_AWV": date(2023, 1, 1),
            },
        ]
        PrimaryCareProgramInfo.objects.create(person=person_1, program=program, elation_awv_reminder_id=123)

        call_command(
            "backfill_last_awv_dates_in_primary_care_model",
            dry_run_off=True,
            user=self.provider,
        )

        mock_get_measure_report_rows.assert_called_once_with(
            "SELECT PATIENT_ID, DATE_OF_LAST_AWV FROM ANALYTICS.PUBLIC_TO_LUCIAN.LUCIAN_MEMBER_MOST_RECENT_AWV"
        )
        primary_care_infos = PrimaryCareProgramInfo.objects.filter(person=person_1)
        self.assertEqual(primary_care_infos.count(), 1)
        # Primary care info should be created as per result in Snowflake.
        primary_care_info_1 = primary_care_infos[0]
        self.assertEqual(primary_care_info_1.date_of_last_awv, date(2021, 1, 1))
        mock_update_care_gap.assert_not_called()

    @patch("firefly.core.services.elation.care_gaps.client.ElationCareGapsClient.get_care_gap")
    @patch("firefly.core.services.elation.care_gaps.client.ElationCareGapsClient.update_care_gap")
    @patch(
        "firefly.modules.programs.primary_care.management.commands.backfill_last_awv_dates_in_primary_care_model.get_most_recent_awv_date_rows"
    )
    def test_awv_clinical_reminder_closure(self, mock_get_measure_report_rows, mock_update_care_gap, mock_get_care_gap):
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "New"}, "category": StatusCategory.NOT_STARTED},
                {
                    "state": {"name": "Automated Outreach 1", "on_enter": "yoy_care_awv_email_reminder"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 2", "on_enter": "yoy_care_awv_push_notification"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 3", "on_enter": "yoy_care_awv_sms_reminder"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {"state": {"name": "Manual Outreach"}, "category": StatusCategory.IN_PROGRESS},
                {"state": {"name": "Final Outreach"}, "category": StatusCategory.IN_PROGRESS},
                {"state": {"name": "Done", "on_enter": "clear_outreach_history"}, "category": StatusCategory.COMPLETE},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "New",
            "transitions": [
                {"dest": "New", "source": ["Done", "Will Not Do"], "trigger": "New"},
                {
                    "dest": "Automated Outreach 1",
                    "source": ["New", "Done", "Will Not Do"],
                    "trigger": "Automated Outreach 1",
                },
                {"dest": "Automated Outreach 2", "source": ["Automated Outreach 1"], "trigger": "Automated Outreach 2"},
                {"dest": "Automated Outreach 3", "source": ["Automated Outreach 2"], "trigger": "Automated Outreach 3"},
                {"dest": "Manual Outreach", "source": ["Automated Outreach 3"], "trigger": "Manual Outreach"},
                {"dest": "Final Outreach", "source": ["Manual Outreach"], "trigger": "Final Outreach"},
                {
                    "dest": "Will Not Do",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Will Not Do",
                },
                {
                    "dest": "Done",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Done",
                },
                {"dest": "Done", "source": "*", "trigger": "auto_close", "system_action": "system_close"},
                {"dest": "New", "source": "*", "trigger": "reopened", "system_action": "system_reopen"},
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Annual Wellness Visit",
            content=state_machine_content,
        )
        category = CaseCategory.objects.create(
            title="Annual Wellness Visit",
            unique_key="annual_wellness_visit",
            state_machine_definition=state_machine_definition,
        )
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        person_3 = PersonUserFactory()
        mock_get_measure_report_rows.return_value = [
            {
                "PATIENT_ID": person_1.user.id,
                "DATE_OF_LAST_AWV": date(2021, 1, 1),
            },
            {
                "PATIENT_ID": person_2.user.id,
                "DATE_OF_LAST_AWV": date(2022, 1, 1),
            },
            {
                "PATIENT_ID": person_3.user.id,
                "DATE_OF_LAST_AWV": date(2023, 1, 1),
            },
        ]
        mock_get_care_gap.return_value = {"status": "open"}
        program_info = PrimaryCareProgramInfo.objects.create(
            person=person_1, program=program, elation_awv_reminder_id=123
        )

        call_command(
            "backfill_last_awv_dates_in_primary_care_model",
            dry_run_off=True,
            user=self.provider,
        )

        mock_get_measure_report_rows.assert_called_once_with(
            "SELECT PATIENT_ID, DATE_OF_LAST_AWV FROM ANALYTICS.PUBLIC_TO_LUCIAN.LUCIAN_MEMBER_MOST_RECENT_AWV"
        )
        primary_care_infos = PrimaryCareProgramInfo.objects.filter(person=person_1)
        self.assertEqual(primary_care_infos.count(), 1)
        # Primary care info should be created as per result in Snowflake.
        primary_care_info_1 = primary_care_infos[0]
        self.assertEqual(primary_care_info_1.date_of_last_awv, date(2021, 1, 1))

        # Shouldn't close reminder, when last awv is not within year
        mock_update_care_gap.assert_not_called()

        mock_get_measure_report_rows.return_value = [
            {
                "PATIENT_ID": person_1.user.id,
                "DATE_OF_LAST_AWV": datetime.date(timezone.now() - timedelta(days=300)),
            },
            {
                "PATIENT_ID": 123,
                "DATE_OF_LAST_AWV": date(2022, 1, 1),
            },
            {
                "PATIENT_ID": 456,
                "DATE_OF_LAST_AWV": date(2023, 1, 1),
            },
        ]
        Case.objects.create(person=person_1, category=category)
        program_info.elation_awv_reminder_id = None
        program_info.save()
        call_command(
            "backfill_last_awv_dates_in_primary_care_model",
            dry_run_off=True,
            user=self.provider,
        )
        # Should close awv case in Lucian if it's open, though there is no reminder id
        annual_wellness_visit_cases = person_1.cases.filter(category=category)
        self.assertTrue(annual_wellness_visit_cases.exists())
        self.assertEqual(annual_wellness_visit_cases[0].status, "Done")
        self.assertEqual(
            annual_wellness_visit_cases[0].description,
            "Wellness visit completed on " + str(datetime.date(timezone.now() - timedelta(days=300))),
        )
        program_info.elation_awv_reminder_id = 123
        program_info.save()
        call_command(
            "backfill_last_awv_dates_in_primary_care_model",
            dry_run_off=True,
            user=self.provider,
        )
        # Should close reminder, when last awv is within year
        mock_update_care_gap.assert_called_once_with(
            quality_program=QUALITY_PROGRAM,
            id=primary_care_info_1.elation_awv_reminder_id,
            status="closed",
        )
        primary_care_infos = PrimaryCareProgramInfo.objects.filter(person=person_1)
        self.assertEqual(primary_care_infos.count(), 1)
        # Primary care info should be created as per result in Snowflake.
        primary_care_info_1 = primary_care_infos[0]
        self.assertEqual(primary_care_info_1.date_of_last_awv, datetime.date(timezone.now() - timedelta(days=300)))
        self.assertIsNone(primary_care_info_1.elation_awv_reminder_id)

    @patch("firefly.core.services.elation.care_gaps.client.ElationCareGapsClient.get_care_gap")
    @patch("firefly.core.services.elation.care_gaps.client.ElationCareGapsClient.update_care_gap")
    @patch(
        "firefly.modules.programs.primary_care.management.commands.backfill_last_awv_dates_in_primary_care_model.get_most_recent_awv_date_rows"
    )
    def test_do_not_reclose_clinical_reminder(
        self, mock_get_measure_report_rows, mock_update_care_gap, mock_get_care_gap
    ):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        person_3 = PersonUserFactory()
        mock_get_measure_report_rows.return_value = [
            {
                "PATIENT_ID": person_1.user.id,
                "DATE_OF_LAST_AWV": date(2021, 1, 1),
            },
            {
                "PATIENT_ID": person_2.user.id,
                "DATE_OF_LAST_AWV": date(2022, 1, 1),
            },
            {
                "PATIENT_ID": person_3.user.id,
                "DATE_OF_LAST_AWV": date(2023, 1, 1),
            },
        ]
        mock_get_care_gap.return_value = {"status": "closed"}
        PrimaryCareProgramInfo.objects.create(person=person_1, program=program, elation_awv_reminder_id=123)

        call_command(
            "backfill_last_awv_dates_in_primary_care_model",
            dry_run_off=True,
            user=self.provider,
        )

        mock_get_measure_report_rows.assert_called_once_with(
            "SELECT PATIENT_ID, DATE_OF_LAST_AWV FROM ANALYTICS.PUBLIC_TO_LUCIAN.LUCIAN_MEMBER_MOST_RECENT_AWV"
        )
        primary_care_infos = PrimaryCareProgramInfo.objects.filter(person=person_1)
        self.assertEqual(primary_care_infos.count(), 1)
        # Primary care info should be created as per result in Snowflake.
        primary_care_info_1 = primary_care_infos[0]
        self.assertEqual(primary_care_info_1.date_of_last_awv, date(2021, 1, 1))

        # Shouldn't close reminder, when last awv is not within year
        mock_update_care_gap.assert_not_called()


class CloseAWVCasesTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "New"}, "category": StatusCategory.NOT_STARTED},
                {
                    "state": {"name": "Automated Outreach 1", "on_enter": "yoy_care_awv_email_reminder"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 2", "on_enter": "yoy_care_awv_push_notification"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 3", "on_enter": "yoy_care_awv_sms_reminder"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {"state": {"name": "Manual Outreach"}, "category": StatusCategory.IN_PROGRESS},
                {"state": {"name": "Final Outreach"}, "category": StatusCategory.IN_PROGRESS},
                {"state": {"name": "Done", "on_enter": "clear_outreach_history"}, "category": StatusCategory.COMPLETE},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "New",
            "transitions": [
                {"dest": "New", "source": ["Done", "Will Not Do"], "trigger": "New"},
                {
                    "dest": "Automated Outreach 1",
                    "source": ["New", "Done", "Will Not Do"],
                    "trigger": "Automated Outreach 1",
                },
                {"dest": "Automated Outreach 2", "source": ["Automated Outreach 1"], "trigger": "Automated Outreach 2"},
                {"dest": "Automated Outreach 3", "source": ["Automated Outreach 2"], "trigger": "Automated Outreach 3"},
                {"dest": "Manual Outreach", "source": ["Automated Outreach 3"], "trigger": "Manual Outreach"},
                {"dest": "Final Outreach", "source": ["Manual Outreach"], "trigger": "Final Outreach"},
                {
                    "dest": "Will Not Do",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Will Not Do",
                },
                {
                    "dest": "Done",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Done",
                },
                {"dest": "Done", "source": "*", "trigger": "auto_close", "system_action": "system_close"},
                {"dest": "New", "source": "*", "trigger": "reopened", "system_action": "system_reopen"},
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Annual Wellness Visit",
            content=state_machine_content,
        )
        self.category = CaseCategory.objects.create(
            title="Annual Wellness Visit",
            unique_key="annual_wellness_visit",
            state_machine_definition=state_machine_definition,
        )

    def test_close_awv_case_when_patient_is_not_due_with_dry_run_off(self):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        PrimaryCareProgramInfo.objects.create(
            person=person_1,
            program=program,
            elation_awv_reminder_id=123,
            date_of_last_awv=(timezone.now() - timedelta(days=100)).date(),
        )
        Case.objects.create(person=person_1, status_category=StatusCategory.IN_PROGRESS, category=self.category)
        PrimaryCareProgramInfo.objects.create(
            person=person_2,
            program=program,
            elation_awv_reminder_id=1234,
            date_of_last_awv=(timezone.now() - timedelta(days=366)).date(),
        )
        Case.objects.create(person=person_2, status_category=StatusCategory.IN_PROGRESS, category=self.category)

        call_command(
            "close_awv_cases_for_patients_not_due_for_awv",
            dry_run_off=True,
            user=self.provider,
        )

        # Should close case when patient is not due for AWV
        case = Case.objects.filter(person=person_1).first()
        self.assertEqual(case.status_category, StatusCategory.COMPLETE)
        # Should not close case when patient is due for AWV
        case = Case.objects.filter(person=person_2).first()
        self.assertNotEqual(case.status_category, StatusCategory.COMPLETE)

    def test_close_awv_case_when_patient_is_not_due_with_dry_run(self):
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        PrimaryCareProgramInfo.objects.create(
            person=person_1,
            program=program,
            elation_awv_reminder_id=123,
            date_of_last_awv=(timezone.now() - timedelta(days=100)).date(),
        )
        Case.objects.create(person=person_1, status_category=StatusCategory.IN_PROGRESS, category=self.category)
        PrimaryCareProgramInfo.objects.create(
            person=person_2,
            program=program,
            elation_awv_reminder_id=1234,
            date_of_last_awv=(timezone.now() - timedelta(days=366)).date(),
        )
        Case.objects.create(person=person_2, status_category=StatusCategory.IN_PROGRESS, category=self.category)

        call_command(
            "close_awv_cases_for_patients_not_due_for_awv",
            dry_run_off=False,
            user=self.provider,
        )

        # Should not close case when patient is not due for AWV, when dry run ON
        case = Case.objects.filter(person=person_1).first()
        self.assertNotEqual(case.status_category, StatusCategory.COMPLETE)
        # Should not close case when patient is due for AWV
        case = Case.objects.filter(person=person_2).first()
        self.assertNotEqual(case.status_category, StatusCategory.COMPLETE)


class DischargeWorkflowTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.program_info = add_person_to_program(
            person=self.patient.person,
            program_uid=ProgramCodes.PRIMARY_CARE,
        )

        state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": ["Approval Pending", "Emergency Care"], "trigger": "Done"},
                {"dest": "Approval Pending", "source": ["Draft"], "trigger": "Approval Pending"},
                {"dest": "Will Not Do", "source": "*", "trigger": "Will Not Do"},
                {"dest": "Emergency Care", "source": ["Approval Pending"], "trigger": "Emergency Care"},
                {"dest": "Will Not Do", "source": "*", "trigger": "deferred", "system_action": "system_close"},
            ],
            "initial_state": "Draft",
            "state_with_categories": [
                {
                    "state": {"name": "Draft", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 0, "use_business_days": None}],
                },
                {
                    "state": {"name": "Will Not Do", "on_enter": "cancel_discharge", "ignore_invalid_triggers": None},
                    "category": "deferred",
                },
                {
                    "state": {"name": "Approval Pending", "ignore_invalid_triggers": None},
                    "category": "in_progress",
                    "due_date": [{"days": 1, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Emergency Care",
                        "on_enter": "approve_pending_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                    "due_date": [{"days": 30, "use_business_days": True}],
                },
                {
                    "state": {"name": "Done", "on_enter": "complete_discharge", "ignore_invalid_triggers": None},
                    "category": "complete",
                },
            ],
        }

        state_machine_definition = StateMachineDefinition.objects.create(
            title="Member Discharge",
            content=state_machine_content,
        )

        self.category = CaseCategory.objects.create(
            title="Member Discharge",
            unique_key="member_discharge",
            state_machine_definition=state_machine_definition,
            description="default",
        )

    @mock.patch("firefly.modules.programs.primary_care.signals.reminders")
    def test_create_and_cancel_discharge_case(self, mock_reminders):
        add_person_to_program(
            person=self.patient.person,
            program_uid=ProgramCodes.PRIMARY_CARE,
        )
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertEqual(self.program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(self.program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertIsNotNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)
        program_enrollment = ProgramEnrollment.objects.filter(
            person=self.patient.person,
            program__uid=ProgramCodes.PRIMARY_CARE,
        ).first()
        self.assertEqual(
            program_enrollment.reason, f"Discharged:{ChurnedDischargedReasons.ELECTED_TO_LEAVE}{DISCHARGE_TEXT}"
        )
        discharge_case = Case.objects.filter(person=self.patient.person, category=self.category).first()

        self.assertIsNotNone(discharge_case)
        self.assertIsNotNone(discharge_case.description)
        self.assertEqual(discharge_case.description, "Discharge reason: Member chooses to leave firefly. default")

        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["INSURANCE_EXPIRED_30_DAYS"],
            [
                {
                    "external_user_id": self.patient.person.id,
                    "send_to_existing_only": True,
                }
            ],
        )

        # Let's move the case between the usual workflow -> starts at Draft, move to Pending Clinical Approval, to
        # Emergency Care to ensure that we capture the discharge approval date
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.DRAFT)
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING)
        discharge_case.action = DischargeMemberCaseStatuses.EMERGENCY_CARE
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.EMERGENCY_CARE)
        self.program_info.refresh_from_db()
        self.assertIsNotNone(self.program_info.discharge_pending_approved_at)

        # let's say discharge was canceled after it was started via api
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": None},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertIsNone(self.program_info.discharge_pending_reason)
        self.assertIsNone(self.program_info.pending_discharge_reason)
        self.assertIsNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

        discharge_case.refresh_from_db()
        self.assertIsNotNone(discharge_case)
        self.assertEqual(discharge_case.status, "Will Not Do")

    def test_completing_discharge_actions(self):
        add_person_to_program(person=self.patient.person, program_uid=ProgramCodes.PRIMARY_CARE)
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertEqual(self.program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(self.program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertIsNotNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

        discharge_case = Case.objects.filter(person=self.patient.person, category=self.category).first()

        self.assertIsNotNone(discharge_case)

        # Let's move the case between the usual workflow -> starts at Draft, move to Pending Clinical Approval, to
        # Emergency Care to ensure that we capture the discharge approval date
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.DRAFT)
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING)
        discharge_case.action = DischargeMemberCaseStatuses.EMERGENCY_CARE
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.EMERGENCY_CARE)
        self.program_info.refresh_from_db()
        self.assertIsNotNone(self.program_info.discharge_pending_approved_at)

        # Let's complete discharge and ensure onboarding state is Discharged
        discharge_case.action = DischargeMemberCaseStatuses.DONE
        discharge_case.save()
        program_enrollment = ProgramEnrollment.objects.filter(
            person=self.patient.person, program__uid=ProgramCodes.PRIMARY_CARE, period__endswith__isnull=True
        ).first()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.DONE)
        self.assertEqual(program_enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(program_enrollment.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)
        self.assertTrue("discharge" in program_enrollment.reason.lower())

    def test_cancel_discharge_from_case(self):
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertEqual(self.program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(self.program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertIsNotNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

        discharge_case = Case.objects.filter(person=self.patient.person, category=self.category).first()

        self.assertIsNotNone(discharge_case)

        # Let's move the case between the usual workflow -> starts at Draft, move to Pending Clinical Approval, to
        # Emergency Care to ensure that we capture the discharge approval date
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.DRAFT)
        discharge_case.action = DischargeMemberCaseStatuses.WILL_NOT_DO
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.WILL_NOT_DO)
        self.program_info.refresh_from_db()
        self.assertIsNone(self.program_info.discharge_pending_reason)
        self.assertIsNone(self.program_info.pending_discharge_reason)
        self.assertIsNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

    def test_cancel_discharge_when_case_completed(self):
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": ChurnedDischargedReasons.ELECTED_TO_LEAVE},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertEqual(self.program_info.discharge_pending_reason, DischargedReason.ELECTED_TO_LEAVE)
        self.assertEqual(self.program_info.pending_discharge_reason, ChurnedDischargedReasons.ELECTED_TO_LEAVE)
        self.assertIsNotNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

        discharge_case = Case.objects.filter(person=self.patient.person, category=self.category).first()

        self.assertIsNotNone(discharge_case)

        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.DRAFT)
        discharge_case.action = DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.CLINICIAN_APPROVAL_PENDING)
        discharge_case.action = DischargeMemberCaseStatuses.EMERGENCY_CARE
        discharge_case.save()
        self.assertEqual(discharge_case.status, DischargeMemberCaseStatuses.EMERGENCY_CARE)
        self.program_info.refresh_from_db()
        self.assertIsNotNone(self.program_info.discharge_pending_approved_at)

        # let's say discharge was canceled after it was started via api
        result = self.provider_client.patch(
            f"/programs/primary-care/{self.patient.pk}/pending-discharge/v2",
            {"pending_discharge_reason": None},
            format="json",
        )

        self.assertEqual(result.status_code, 200)
        self.program_info.refresh_from_db()
        self.assertIsNone(self.program_info.discharge_pending_reason)
        self.assertIsNone(self.program_info.pending_discharge_reason)
        self.assertIsNone(self.program_info.discharge_pending_started_at)
        self.assertIsNone(self.program_info.discharge_pending_approved_at)
        self.assertIsNone(self.program_info.discharge_pending_communication_sent_at)

        discharge_case.refresh_from_db()
        self.assertIsNotNone(discharge_case)
        self.assertEqual(discharge_case.status, "Will Not Do")

    def test_initiate_discharge_for_established_member_with_invalid_insurance(self):
        members_to_discharge = self._create_members_to_discharge()
        for member in members_to_discharge:
            initiate_discharge_for_established_member_with_invalid_insurance(member)
            self._assert_discharge_started(member)
        members_to_not_discharge = self._create_members_to_not_discharge()
        for member in members_to_not_discharge:
            initiate_discharge_for_established_member_with_invalid_insurance(member)
            self._assert_discharge_not_started(member)

    @patch(
        "firefly.modules.programs.primary_care.management.commands.create_discharge_cases_for_members_with_invalid_insurance.create_discharge_pending_cases_for_established_members_with_invalid_insurance"  # noqa
    )
    def test_create_discharge_cases_admin_command(self, mock_create_discharge_pending_cases):
        members_to_discharge = self._create_members_to_discharge()
        self._create_members_to_not_discharge()
        call_command("create_discharge_cases_for_members_with_invalid_insurance", user=self.provider, dry_run_off=True)
        member_ids = [member.pk for member in members_to_discharge]
        mock_create_discharge_pending_cases.send.assert_called_once_with(member_ids)

    def _create_members_to_discharge(self):
        # Established member in "ineligible insurance" coverage state > 30 days
        ineligible_insurance_member = self._setup_care_only()
        self._move_member_to_established(ineligible_insurance_member)
        ineligible_insurance_member.to_ineligible_insurance(actor=ineligible_insurance_member.user)
        ineligible_insurance_member.insurance_info.coverage_end = date.today() - timedelta(days=90)
        ineligible_insurance_member.insurance_info.save(update_fields=["coverage_end"])
        # Established member in "ineligible insurance" coverage state > 30 days with coverage timestamp
        ineligible_member_coverage_ts = self._setup_care_only()
        self._move_member_to_established(ineligible_member_coverage_ts)
        ineligible_member_coverage_ts.to_ineligible_insurance(actor=ineligible_member_coverage_ts.user)
        ineligible_member_coverage_ts.insurance_info.coverage_end = None
        ineligible_member_coverage_ts.insurance_info.save(update_fields=["coverage_end"])
        ineligible_member_coverage_ts.coverage_ts = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=90)
        ineligible_member_coverage_ts.save(update_fields=["coverage_ts"])
        # Established member in "ineligible medicaid" coverage state > 30 days
        ineligible_medicaid_member = self._setup_care_only()
        self._move_member_to_established(ineligible_medicaid_member)
        ineligible_medicaid_member.to_ineligible_medicaid(actor=ineligible_medicaid_member.user)
        ineligible_medicaid_member.insurance_info.coverage_end = date.today() - timedelta(days=90)
        ineligible_medicaid_member.insurance_info.save(update_fields=["coverage_end"])
        # Established member in "ineligible uninsured" coverage state > 30 days
        ineligible_uninsured_member = self._setup_care_only()
        self._move_member_to_established(ineligible_uninsured_member)
        ineligible_uninsured_member.to_ineligible_uninsured(actor=ineligible_uninsured_member.user)
        ineligible_uninsured_member.insurance_info.coverage_end = date.today() - timedelta(days=90)
        ineligible_uninsured_member.insurance_info.save(update_fields=["coverage_end"])
        # Established member in "incomplete expired" coverage state > 30 days
        incomplete_expired_member = self._setup_care_only()
        self._move_member_to_established(incomplete_expired_member)
        incomplete_expired_member.to_incomplete_expired(actor=incomplete_expired_member.user)
        incomplete_expired_member.insurance_info.coverage_end = date.today() - timedelta(days=90)
        incomplete_expired_member.insurance_info.save(update_fields=["coverage_end"])
        return [
            ineligible_insurance_member,
            ineligible_member_coverage_ts,
            ineligible_medicaid_member,
            ineligible_uninsured_member,
            incomplete_expired_member,
        ]

    def _create_members_to_not_discharge(self):
        # Established member in invalid coverage state < 30 days
        ineligible_insurance_member = self._setup_care_only()
        self._move_member_to_established(ineligible_insurance_member)
        ineligible_insurance_member.to_ineligible_insurance(actor=ineligible_insurance_member.user)
        # Established member in valid coverage state
        valid_coverage_member = self._setup_care_only()
        self._move_member_to_established(valid_coverage_member)
        # Not Established member in invalid coverage state
        not_established_member = self._setup_care_only()
        not_established_member.to_ineligible_insurance(actor=not_established_member.user)
        self.assertTrue(
            is_current_enrollment_status(
                not_established_member, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.NOT_ESTABLISHED
            )
        )
        # Churned member in invalid coverage state
        churned_member = self._setup_care_only()
        remove_person_from_program(churned_member, ProgramCodes.PRIMARY_CARE)
        churned_member.to_ineligible_insurance(actor=churned_member.user)
        self.assertTrue(
            is_current_enrollment_status(churned_member, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.CHURNED)
        )
        return [ineligible_insurance_member, valid_coverage_member, not_established_member, churned_member]

    def _move_member_to_established(self, person):
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        self.assertTrue(
            is_current_enrollment_status(person, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.ESTABLISHED)
        )

    def _assert_discharge_started(self, person):
        primary_care_program_info = program_info_for_person_program(person, ProgramCodes.PRIMARY_CARE)
        # Assert that `pending_discharge_reason` is set to "insurance not covered ..."
        self.assertEqual(
            primary_care_program_info.pending_discharge_reason, ChurnedDischargedReasons.INSURANCE_NOT_COVERED
        )
        # Assert that `discharge_pending_started_at` is set
        self.assertIsNotNone(primary_care_program_info.discharge_pending_started_at)
        # Assert that discharge is not approved yet
        self.assertIsNone(primary_care_program_info.discharge_pending_approved_at)
        self.assertIsNone(primary_care_program_info.discharge_pending_communication_sent_at)
        # Assert that program enrollment reason is set to "Discharged ..."
        primary_care_enrollment = program_enrollment_for_person_program(person, ProgramCodes.PRIMARY_CARE)
        self.assertEqual(
            primary_care_enrollment.reason,
            f"Discharged:{primary_care_program_info.pending_discharge_reason}{DISCHARGE_TEXT}",
        )
        # Assert that discharge pending case is created
        discharge_case = Case.objects.filter(person=person, category=self.category).first()
        self.assertIsNotNone(discharge_case)

    def _assert_discharge_not_started(self, person):
        primary_care_program_info = active_or_inactive_program_info_for_person_program(
            person, ProgramCodes.PRIMARY_CARE
        )
        # Assert that `pending_discharge_reason` is not set
        self.assertIsNone(primary_care_program_info.pending_discharge_reason)
        # Assert that `discharge_pending_started_at` is not set
        self.assertIsNone(primary_care_program_info.discharge_pending_started_at)
        # Assert that discharge is not approved yet
        self.assertIsNone(primary_care_program_info.discharge_pending_approved_at)
        self.assertIsNone(primary_care_program_info.discharge_pending_communication_sent_at)
        # Assert that program enrollment reason is not set
        primary_care_enrollment = (
            get_person_program_enrollments(person, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
        self.assertIsNone(primary_care_enrollment.reason)
        # Assert that discharge pending case is not created
        discharge_case = Case.objects.filter(person=person, category=self.category)
        self.assertFalse(discharge_case.exists())

    @staticmethod
    def _setup_care_only():
        person = PersonUserFactory()
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        person.user.onboarding_state.trigger(OnboardingTransitions.SIGNUP_COMPLETE)
        person.user.onboarding_state.to_signedup(actor=person.user)
        person.user.onboarding_state.to_member(actor=person.user)
        return person


class TestClearDischargeData(FireflyTestCase):
    def setUp(self):
        super().setUp()
        state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": ["Approval Pending", "Emergency Care"], "trigger": "Done"},
                {"dest": "Approval Pending", "source": ["Draft"], "trigger": "Approval Pending"},
                {"dest": "Will Not Do", "source": "*", "trigger": "Will Not Do"},
                {"dest": "Emergency Care", "source": ["Approval Pending"], "trigger": "Emergency Care"},
                {"dest": "Will Not Do", "source": "*", "trigger": "deferred", "system_action": "system_close"},
            ],
            "initial_state": "Draft",
            "state_with_categories": [
                {
                    "state": {"name": "Draft", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 0, "use_business_days": None}],
                },
                {
                    "state": {"name": "Will Not Do", "on_enter": "cancel_discharge", "ignore_invalid_triggers": None},
                    "category": "deferred",
                },
                {
                    "state": {"name": "Approval Pending", "ignore_invalid_triggers": None},
                    "category": "in_progress",
                    "due_date": [{"days": 1, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Emergency Care",
                        "on_enter": "approve_pending_discharge",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                    "due_date": [{"days": 30, "use_business_days": True}],
                },
                {
                    "state": {"name": "Done", "on_enter": "complete_discharge", "ignore_invalid_triggers": None},
                    "category": "complete",
                },
            ],
        }

        state_machine_definition = StateMachineDefinition.objects.create(
            title="Member Discharge",
            content=state_machine_content,
        )

        self.category = CaseCategory.objects.create(
            title="Member Discharge",
            unique_key="member_discharge",
            state_machine_definition=state_machine_definition,
            description="default",
        )
        # add 3 person
        self.person_1 = PersonUserFactory.create()
        self.person_2 = PersonUserFactory.create()
        self.person_3 = PersonUserFactory.create()
        self.person_4 = PersonUserFactory.create()
        # add all person to primary_care program
        program_info_1 = add_person_to_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_2 = add_person_to_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_4 = add_person_to_program(person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE)
        # set discharge data for person_1 nd person_2, person_4
        program_info_1.discharge_pending_approved_at = datetime.now()
        program_info_1.discharge_pending_started_at = datetime.now()
        program_info_1.discharge_pending_communication_sent_at = datetime.now()
        program_info_1.pending_discharge_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_1.discharge_pending_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_1.save(
            update_fields=[
                "discharge_pending_approved_at",
                "discharge_pending_communication_sent_at",
                "discharge_pending_started_at",
                "pending_discharge_reason",
                "discharge_pending_reason",
            ]
        )
        program_info_2.discharge_pending_approved_at = datetime.now()
        program_info_2.discharge_pending_started_at = datetime.now()
        program_info_2.discharge_pending_communication_sent_at = datetime.now()
        program_info_2.pending_discharge_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_2.discharge_pending_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_2.save(
            update_fields=[
                "discharge_pending_approved_at",
                "discharge_pending_communication_sent_at",
                "discharge_pending_started_at",
                "pending_discharge_reason",
                "discharge_pending_reason",
            ]
        )
        program_info_4.discharge_pending_approved_at = datetime.now()
        program_info_4.discharge_pending_started_at = datetime.now()
        program_info_4.discharge_pending_communication_sent_at = datetime.now()
        program_info_4.pending_discharge_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_4.discharge_pending_reason = ChurnedDischargedReasons.CANT_GET_IN_TOUCH
        program_info_4.active = False  # set active false for program_info_4
        program_info_4.save(
            update_fields=[
                "discharge_pending_approved_at",
                "discharge_pending_communication_sent_at",
                "discharge_pending_started_at",
                "pending_discharge_reason",
                "discharge_pending_reason",
                "active",
            ]
        )

    def test_clear_discharge_script_with_dry_run_on(self):
        # before running script person_1, person_2, person_4 discharge data should not be None
        # and person_3 should be None person_4 active should be False
        program_info_1 = program_info_for_person_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_2 = program_info_for_person_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_3 = program_info_for_person_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_4 = active_or_inactive_program_info_for_person_program(
            person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE
        )
        self.assertIsNotNone(program_info_1.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_1.discharge_pending_started_at)
        self.assertIsNotNone(program_info_1.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_1.pending_discharge_reason)
        self.assertIsNotNone(program_info_1.discharge_pending_reason)
        self.assertEqual(program_info_1.active, True)
        self.assertIsNotNone(program_info_2.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_2.discharge_pending_started_at)
        self.assertIsNotNone(program_info_2.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_2.pending_discharge_reason)
        self.assertIsNotNone(program_info_2.discharge_pending_reason)
        self.assertEqual(program_info_2.active, True)
        self.assertIsNone(program_info_3.discharge_pending_approved_at)
        self.assertIsNone(program_info_3.discharge_pending_started_at)
        self.assertIsNone(program_info_3.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_3.pending_discharge_reason)
        self.assertIsNone(program_info_3.discharge_pending_reason)
        self.assertEqual(program_info_3.active, True)
        self.assertIsNotNone(program_info_4.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_4.discharge_pending_started_at)
        self.assertIsNotNone(program_info_4.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_4.pending_discharge_reason)
        self.assertIsNotNone(program_info_4.discharge_pending_reason)
        self.assertEqual(program_info_4.active, False)
        call_command(
            "clear_discharge_data",
            user=self.provider,
            dry_run_off=True,
        )
        # after running script all user discharge data should be None for person_1, person_2 and person_3
        # person_4 discharge data should remain same
        program_info_1 = program_info_for_person_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_2 = program_info_for_person_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_3 = program_info_for_person_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_4 = active_or_inactive_program_info_for_person_program(
            person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE
        )

        self.assertIsNone(program_info_1.discharge_pending_approved_at)
        self.assertIsNone(program_info_1.discharge_pending_started_at)
        self.assertIsNone(program_info_1.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_1.pending_discharge_reason)
        self.assertIsNone(program_info_1.discharge_pending_reason)
        self.assertEqual(program_info_1.active, True)
        self.assertIsNone(program_info_2.discharge_pending_approved_at)
        self.assertIsNone(program_info_2.discharge_pending_started_at)
        self.assertIsNone(program_info_2.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_2.pending_discharge_reason)
        self.assertIsNone(program_info_2.discharge_pending_reason)
        self.assertEqual(program_info_2.active, True)
        self.assertIsNone(program_info_3.discharge_pending_approved_at)
        self.assertIsNone(program_info_3.discharge_pending_started_at)
        self.assertIsNone(program_info_3.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_3.pending_discharge_reason)
        self.assertIsNone(program_info_3.discharge_pending_reason)
        self.assertEqual(program_info_3.active, True)
        self.assertIsNotNone(program_info_4.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_4.discharge_pending_started_at)
        self.assertIsNotNone(program_info_4.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_4.pending_discharge_reason)
        self.assertIsNotNone(program_info_4.discharge_pending_reason)
        self.assertEqual(program_info_4.active, False)

    def test_clear_discharge_script_with_dry_run_off(self):
        # before running script person_1, person_2, person_4 discharge data should not be None
        # and person_3 should be None person_4 active should be False
        program_info_1 = program_info_for_person_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_2 = program_info_for_person_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_3 = program_info_for_person_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_4 = active_or_inactive_program_info_for_person_program(
            person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE
        )
        self.assertIsNotNone(program_info_1.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_1.discharge_pending_started_at)
        self.assertIsNotNone(program_info_1.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_1.pending_discharge_reason)
        self.assertIsNotNone(program_info_1.discharge_pending_reason)
        self.assertEqual(program_info_1.active, True)
        self.assertIsNotNone(program_info_2.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_2.discharge_pending_started_at)
        self.assertIsNotNone(program_info_2.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_2.pending_discharge_reason)
        self.assertIsNotNone(program_info_2.discharge_pending_reason)
        self.assertEqual(program_info_2.active, True)
        self.assertIsNone(program_info_3.discharge_pending_approved_at)
        self.assertIsNone(program_info_3.discharge_pending_started_at)
        self.assertIsNone(program_info_3.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_3.pending_discharge_reason)
        self.assertIsNone(program_info_3.discharge_pending_reason)
        self.assertEqual(program_info_3.active, True)
        self.assertIsNotNone(program_info_4.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_4.discharge_pending_started_at)
        self.assertIsNotNone(program_info_4.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_4.pending_discharge_reason)
        self.assertIsNotNone(program_info_4.discharge_pending_reason)
        self.assertEqual(program_info_4.active, False)
        call_command(
            "clear_discharge_data",
            user=self.provider,
            dry_run_off=False,
        )
        # after run script no changes as dry run off
        program_info_1 = program_info_for_person_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_2 = program_info_for_person_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_3 = program_info_for_person_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        program_info_4 = active_or_inactive_program_info_for_person_program(
            person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE
        )
        self.assertIsNotNone(program_info_1.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_1.discharge_pending_started_at)
        self.assertIsNotNone(program_info_1.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_1.pending_discharge_reason)
        self.assertIsNotNone(program_info_1.discharge_pending_reason)
        self.assertEqual(program_info_1.active, True)
        self.assertIsNotNone(program_info_2.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_2.discharge_pending_started_at)
        self.assertIsNotNone(program_info_2.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_2.pending_discharge_reason)
        self.assertIsNotNone(program_info_2.discharge_pending_reason)
        self.assertEqual(program_info_2.active, True)
        self.assertIsNone(program_info_3.discharge_pending_approved_at)
        self.assertIsNone(program_info_3.discharge_pending_started_at)
        self.assertIsNone(program_info_3.discharge_pending_communication_sent_at)
        self.assertIsNone(program_info_3.pending_discharge_reason)
        self.assertIsNone(program_info_3.discharge_pending_reason)
        self.assertEqual(program_info_3.active, True)
        self.assertIsNotNone(program_info_4.discharge_pending_approved_at)
        self.assertIsNotNone(program_info_4.discharge_pending_started_at)
        self.assertIsNotNone(program_info_4.discharge_pending_communication_sent_at)
        self.assertIsNotNone(program_info_4.pending_discharge_reason)
        self.assertIsNotNone(program_info_4.discharge_pending_reason)
        self.assertEqual(program_info_4.active, False)


class PrimaryCareProgramManagementCommandTestCase(FireflyTestCase):
    def test_backfill_discharged_program_enrollment_reason_code(self):
        # Not-established primary care member
        not_established_member = PersonUserFactory()
        add_person_to_program(not_established_member, ProgramCodes.PRIMARY_CARE)
        # Established primary care member
        established_member = PersonUserFactory()
        add_person_to_program(established_member, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, established_member, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT
        )
        # Churned primary care member
        churned_member = PersonUserFactory()
        add_person_to_program(churned_member, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(churned_member, ProgramCodes.PRIMARY_CARE)
        # Pending discharge primary care member
        pending_discharge_member = PersonUserFactory()
        add_person_to_program(pending_discharge_member, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, pending_discharge_member, ProgramEnrollmentEvents.PROGRAM_ENROLLED
        )
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, established_member, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT
        )
        remove_person_from_program(pending_discharge_member, ProgramCodes.PRIMARY_CARE)
        # Discharged primary care member 1
        discharged_member_1 = PersonUserFactory()
        add_person_to_program(discharged_member_1, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, discharged_member_1, ProgramEnrollmentEvents.PROGRAM_ENROLLED
        )
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, established_member, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT
        )
        remove_person_from_program(discharged_member_1, ProgramCodes.PRIMARY_CARE)
        discharged_enrollment_1 = (
            get_person_program_enrollments(discharged_member_1, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE, status=PrimaryCareProgramStatus.CHURNED)
            .first()
        )
        discharged_enrollment_1.reason = "DISCHARGED"
        discharged_enrollment_1.save()
        # Discharged primary care member 2
        discharged_member_2 = PersonUserFactory()
        add_person_to_program(discharged_member_2, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, discharged_member_2, ProgramEnrollmentEvents.PROGRAM_ENROLLED
        )
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, established_member, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT
        )
        remove_person_from_program(discharged_member_2, ProgramCodes.PRIMARY_CARE)
        discharged_enrollment_2 = (
            get_person_program_enrollments(discharged_member_2, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE, status=PrimaryCareProgramStatus.CHURNED)
            .first()
        )
        discharged_enrollment_2.reason = "test discharge test"
        discharged_enrollment_2.save()
        # Run admin command to set discharged reason codes
        call_command("backfill_discharged_program_enrollment_reason_code", user=self.provider, dry_run_off=True)
        not_established_enrollment = (
            get_person_program_enrollments(not_established_member)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
        established_enrollment = (
            get_person_program_enrollments(established_member).filter(program__uid=ProgramCodes.PRIMARY_CARE).first()
        )
        churned_enrollment = (
            get_person_program_enrollments(churned_member, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
        pending_discharge_enrollment = (
            get_person_program_enrollments(pending_discharge_member, include_primary_care_churned=True)
            .filter(program__uid=ProgramCodes.PRIMARY_CARE)
            .first()
        )
        discharged_enrollment_1.refresh_from_db()
        discharged_enrollment_2.refresh_from_db()
        self.assertEqual(not_established_enrollment.status, PrimaryCareProgramStatus.NOT_ESTABLISHED)
        self.assertIsNone(not_established_enrollment.reason_code)
        self.assertEqual(established_enrollment.status, PrimaryCareProgramStatus.ESTABLISHED)
        self.assertIsNone(established_enrollment.reason_code)
        self.assertEqual(churned_enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(churned_enrollment.reason_code)
        self.assertEqual(pending_discharge_enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertIsNone(pending_discharge_enrollment.reason_code)
        self.assertEqual(discharged_enrollment_1.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(discharged_enrollment_1.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)
        self.assertEqual(discharged_enrollment_2.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(discharged_enrollment_2.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)

    @patch(
        "firefly.modules.programs.primary_care.management.commands.backfill_discharge_pending_cases.backfill_discharge_pending_cases"
    )
    def test_backfill_discharge_pending_cases_command(self, mock_backfill_discharge_pending_cases):
        (
            person_1,
            person_2,
            person_3,
            person_4,
            person_5,
            person_6,
            person_7,
            person_8,
        ) = self.create_churned_former_established_members()
        start_date = (date.today() - timedelta(days=30)).strftime("%Y-%m-%d")
        call_command("backfill_discharge_pending_cases", user=self.provider, start_date=start_date, dry_run_off=True)
        mock_backfill_discharge_pending_cases.send.assert_called_once_with([person_2.pk, person_6.pk, person_8.pk])

    def test_backfill_discharge_pending_cases_task(self):
        (
            person_1,
            person_2,
            person_3,
            person_4,
            person_5,
            person_6,
            person_7,
            person_8,
        ) = self.create_churned_former_established_members()
        expected_members = [person_2.pk, person_6.pk, person_8.pk]
        backfill_discharge_pending_cases(expected_members)

    def test_get_churned_former_established_members(self):
        (
            person_1,
            person_2,
            person_3,
            person_4,
            person_5,
            person_6,
            person_7,
            person_8,
        ) = self.create_churned_former_established_members()
        # Get all churned, formerly established members
        start_date = date.today() - timedelta(days=30)
        members = get_churned_former_established_members(start_date)
        self.assertEqual(members.count(), 3)
        self.assertIn(person_2, members)
        self.assertIn(person_6, members)
        self.assertIn(person_8, members)

    def create_churned_former_established_members(self):
        self.category, _ = CaseCategory.objects.get_or_create(
            title="Member Discharge", unique_key="member_discharge", description="default"
        )
        enrollment_period_1 = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=60)
        enrollment_period_2 = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=50)
        enrollment_period_3 = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=40)
        # Churned, formerly not_established member --> Should not be included
        person_1 = PersonUserFactory()
        add_person_to_program(person_1, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(person_1, ProgramCodes.PRIMARY_CARE)
        # Churned, formerly established member --> Should be included
        person_2 = PersonUserFactory()
        add_person_to_program(person_2, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_2, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_2, ProgramCodes.PRIMARY_CARE)
        # Not established, formerly established member --> Should not be included
        person_3 = PersonUserFactory()
        add_person_to_program(person_3, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_3, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_3, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person_3, ProgramCodes.PRIMARY_CARE)
        # Discharged, formerly established member --> Should not be included
        person_4 = PersonUserFactory()
        program_info = add_person_to_program(person_4, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_4, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_4, ProgramCodes.PRIMARY_CARE)
        program_info.refresh_from_db()
        program_info.pending_discharge_reason = ChurnedDischargedReasons.INSURANCE_NOT_COVERED
        program_info.discharge_pending_started_at = datetime.now()
        program_info.save()
        # Churned, not established member (multiple enrollments) --> Should not be included
        person_5 = PersonUserFactory()
        add_person_to_program(person_5, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_5, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_5, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person_5, ProgramCodes.PRIMARY_CARE)
        remove_person_from_program(person_5, ProgramCodes.PRIMARY_CARE)
        # Churned, established member (multiple enrollments) --> Should be included
        person_6 = PersonUserFactory()
        add_person_to_program(person_6, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_6, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_6, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person_6, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_6, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_6, ProgramCodes.PRIMARY_CARE)
        # Churned, formerly established member from past --> Should not be included
        person_7 = PersonUserFactory()
        add_person_to_program(person_7, ProgramCodes.PRIMARY_CARE, data={"event_date": enrollment_period_1})
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE,
            person_7,
            ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT,
            event_date=enrollment_period_2,
        )
        remove_person_from_program(person_7, ProgramCodes.PRIMARY_CARE, data={"event_date": enrollment_period_3})
        # Churned, established member (multiple enrollments from past) --> Should be included
        person_8 = PersonUserFactory()
        add_person_to_program(person_8, ProgramCodes.PRIMARY_CARE, data={"event_date": enrollment_period_1})
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE,
            person_8,
            ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT,
            event_date=enrollment_period_2,
        )
        remove_person_from_program(person_8, ProgramCodes.PRIMARY_CARE, data={"event_date": enrollment_period_3})
        add_person_to_program(person_8, ProgramCodes.PRIMARY_CARE)
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, person_8, ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT)
        remove_person_from_program(person_8, ProgramCodes.PRIMARY_CARE)
        return person_1, person_2, person_3, person_4, person_5, person_6, person_7, person_8
