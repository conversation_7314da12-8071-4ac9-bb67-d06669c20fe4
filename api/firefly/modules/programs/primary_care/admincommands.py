from django import forms

from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand


class BackfillLastAwvDatesInPrimaryCareModel(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        opts = []
        return opts, {"user": user}


class ClearDischargeData(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        limit = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["limit"]:
            args += ["-limit", data["limit"]]
        return args, {"user": user}


class BackfillDischargedProgramEnrollmentReasonCode(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {"user": user}


class BackfillDischargePendingCases(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        start_date = forms.DateField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--start_date", data["start_date"]]
        return args, {"user": user}


class CreateDischargeCasesForMembersWithInvalidInsurance(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {"user": user}
