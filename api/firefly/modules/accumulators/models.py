from django.db import models

from firefly.core.user.models import Person
from firefly.modules.firefly_django.models import BaseModelV3


class Accumulators(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over Cha<PERSON><PERSON>ield
    block_of_business_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    member = models.OneToOneField(
        Person,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="accumulators",
        null=True,
        blank=True,
    )
    effective_since = models.DateTimeField(null=True, blank=True)
    copay = models.FloatField(null=True, blank=True)
    deductible = models.FloatField(null=True, blank=True)
    out_of_pocket = models.FloatField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "accumulators"
