from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.insurance.constants import AttributionRequirementType
from firefly.modules.insurance.models import Contract
from firefly.modules.statemachines.attribution.mixin import AttributionStateMachineMixin


class AbstractAttributionData(BaseModelV3):
    STATUS_ATTRIBUTED = "attributed"
    STATUS_NOT_ATTRIBUTED = "not_attributed"
    STATUS_CHOICES = [
        (STATUS_ATTRIBUTED, "attributed"),
        (STATUS_NOT_ATTRIBUTED, "not_attributed"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(max_length=255, choices=STATUS_CHOICES, null=True, blank=True)  # noqa: TID251
    last_run_time = models.DateTimeField(null=True, blank=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        abstract = True


class PayerRosterRecord(AbstractAttributionData):
    from firefly.core.user.models import Person

    person = models.ForeignKey(
        Person,
        on_delete=models.CASCADE,
        related_name="payer_roster_records",
        null=False,
        blank=False,
    )
    contract = models.ForeignKey(
        Contract,
        related_name="payer_roster_records",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["person", "contract"],
                name="payer_roster_record_person_contract_uniq",
            ),
        ]


class InternalAttributionLog(AbstractAttributionData):
    from firefly.core.user.models import Person

    person = models.OneToOneField(
        Person,
        on_delete=models.CASCADE,
        related_name="internal_attribution_log",
        null=False,
        blank=False,
    )


class Attribution(AttributionStateMachineMixin):
    from firefly.core.user.models import Person

    person = models.OneToOneField(
        Person,
        on_delete=models.CASCADE,
        related_name="attribution",
        null=False,
        blank=False,
    )
    ATTRIBUTION_REQUIREMENT_TYPE_CHOICES = (
        (AttributionRequirementType.PCP_SELECTION, AttributionRequirementType.PCP_SELECTION),
        (
            AttributionRequirementType.WELLNESS_VISIT_IN_TWO_YEARS,
            AttributionRequirementType.WELLNESS_VISIT_IN_TWO_YEARS,
        ),
        (AttributionRequirementType.POC_FORM, AttributionRequirementType.POC_FORM),
        (AttributionRequirementType.VISITS, AttributionRequirementType.VISITS),
        (AttributionRequirementType.PHONECALL, AttributionRequirementType.PHONECALL),
        (AttributionRequirementType.FEE_FOR_SERVICE, AttributionRequirementType.FEE_FOR_SERVICE),
        (AttributionRequirementType.AUTO_ATTRIBUTED, AttributionRequirementType.AUTO_ATTRIBUTED),
        (AttributionRequirementType.OPT_IN_PCP, AttributionRequirementType.OPT_IN_PCP),
    )
    contract = models.ForeignKey(
        Contract,
        related_name="attributions",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    attribution_requirement_type = models.CharField(  # noqa: TID251
        max_length=127, blank=True, null=True, choices=ATTRIBUTION_REQUIREMENT_TYPE_CHOICES
    )
    # See ContractFinder.IneligibleReasons
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    contract_failure_reason = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
