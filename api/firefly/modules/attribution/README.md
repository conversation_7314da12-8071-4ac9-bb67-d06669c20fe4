# Attribution

The Attribution module manages member attribution to Firefly based on contract requirements and payer roster data. It tracks whether members are attributed to Firefly for value-based care contracts and synchronizes attribution status with external payers like BCBS.

## Overview

This module handles member attribution logic for value-based care contracts. It determines whether members meet attribution requirements (such as PCP selection, wellness visits, or POC forms) and tracks attribution status through state machines. The module also synchronizes attribution data with external payers to ensure accurate reporting and contract compliance.

## Business Logic

The attribution process works as follows:

1. **Contract Assignment**: Members are assigned to contracts based on their insurance plans
2. **Requirement Evaluation**: System checks if members meet attribution requirements for their contract
3. **Internal Attribution**: Tracks Firefly's internal view of attribution status
4. **Payer Reconciliation**: Synchronizes with external payer rosters (e.g., BCBS) to confirm attribution
5. **State Management**: Uses state machines to track attribution lifecycle

## Core Models

### Attribution
Main attribution record with state machine:
- **person**: One-to-one relationship with Person
- **contract**: Associated value-based care contract
- **attribution_requirement_type**: Type of requirement (PCP_SELECTION, WELLNESS_VISIT_IN_TWO_YEARS, POC_FORM, etc.)
- **contract_failure_reason**: Reason if contract assignment failed
- **State Machine**: Tracks attribution status (contracted, provisional, confirmed, etc.)

### PayerRosterRecord
External payer attribution data:
- **person**: Member being tracked
- **contract**: Associated contract
- **status**: attributed or not_attributed
- **start_date**: When attribution began
- **end_date**: When attribution ended (if applicable)
- **last_run_time**: Last synchronization timestamp

### InternalAttributionLog
Firefly's internal attribution tracking:
- **person**: One-to-one relationship with Person
- **status**: Internal attribution status
- **start_date**: When internal attribution began
- **end_date**: When internal attribution ended
- **last_run_time**: Last calculation timestamp

## Attribution Requirements by Type

### PCP_SELECTION
Members must select Firefly as their primary care provider:
- **Trigger**: Member selects PCP through insurance portal or form
- **Validation**: Checks if Firefly provider is selected as PCP
- **Implementation**: `update_pcp_attribution_for_person()`

### WELLNESS_VISIT_IN_TWO_YEARS
Members must have a wellness visit within two years:
- **Trigger**: Wellness visit completion
- **Validation**: Checks for wellness visits in the last 24 months
- **Implementation**: `update_wellness_visit_based_attribution()`

### POC_FORM
Members must complete Point of Care (POC) form:
- **Trigger**: POC form completion during onboarding
- **Validation**: Checks onboarding state for POC completion
- **Implementation**: `update_poc_attribution_for_person()`

### VISITS
Members must have qualifying visits:
- **Trigger**: Visit completion
- **Validation**: Checks for sufficient visit history
- **Implementation**: `update_visit_attribution_for_person()`

### FEE_FOR_SERVICE
Fee-for-service attribution based on visits:
- **Trigger**: Visit completion
- **Validation**: Checks visit patterns for FFS contracts
- **Implementation**: `update_visit_attribution_for_person()`

### AUTO_ATTRIBUTED
Automatic attribution based on eligibility:
- **Trigger**: Eligibility start/end dates
- **Validation**: Checks eligibility periods and onboarding status
- **Implementation**: `update_auto_attribution_for_person()`

### OPT_IN_PCP
Opt-in PCP attribution:
- **Trigger**: Member opts in to Firefly PCP
- **Validation**: Checks opt-in status
- **Implementation**: `update_opt_in_pcp_attribution_for_person()`

## BCBS Attribution Synchronization

### Data Source
- **Snowflake Integration**: Pulls attribution data from `analytics.PUBLIC_TO_LUCIAN.bcbs_to_firefly_member_list_for_lucian`
- **Plan Types**: Supports both HMO and PPO plans
- **Attribution Status**: Tracks `is_bcbs_attributed` field from BCBS

### Synchronization Process
1. **Data Retrieval**: Queries Snowflake for BCBS attribution data
2. **Record Processing**: Creates or updates `PayerRosterRecord` entries
3. **Status Mapping**: Maps BCBS attribution status to internal status
4. **Date Tracking**: Records attribution start/end dates
5. **Reconciliation**: Compares internal vs. external attribution status

## Core Functions

### Attribution State Calculation
- **`recalculate_attribution_state_for_person()`**: Main function to recalculate attribution based on requirements
- **`recalculate_attribution_based_on_attribution_logs_and_roster()`**: Reconciles internal logs with payer roster data
- **`update_attribution_for_attributed_user()`**: Updates attribution status for confirmed members

### Requirement-Specific Functions
- **`update_pcp_attribution_for_person()`**: Handles PCP selection attribution
- **`update_wellness_visit_based_attribution()`**: Processes wellness visit requirements
- **`update_poc_attribution_for_person()`**: Manages POC form completion attribution
- **`update_visit_attribution_for_person()`**: Handles visit-based attribution
- **`update_auto_attribution_for_person()`**: Processes automatic attribution
- **`update_opt_in_pcp_attribution_for_person()`**: Manages opt-in PCP attribution

### BCBS Synchronization Functions
- **`sync_attribution_data()`**: Main synchronization function for BCBS data
- **`sync_attribution_data_for_contract()`**: Contract-specific synchronization
- **`get_hmo_attribution_data_rows()`**: Retrieves HMO attribution data from Snowflake
- **`get_ppo_attribution_data_rows()`**: Retrieves PPO attribution data from Snowflake

## Management Commands

### Backfill Attribution
- **Command**: `backfill_attribution_for_members`
- **Purpose**: Recalculates attribution for members based on their attribution requirement type
- **Parameters**:
  - `--attribution_requirement_type`: Type of requirement to process
  - `--attribution_status`: Filter by current attribution status
  - `--user_pk` / `--person_pk`: Process specific users/persons
  - `--limit` / `--offset`: Pagination support

### BCBS Data Sync
- **Command**: `sync_bcbs_attribution_data`
- **Purpose**: Synchronizes attribution data from BCBS via Snowflake
- **Parameters**:
  - `--limit` / `--offset`: Pagination support
  - `--delete_redundant_rows`: Clean up redundant records
- **Contracts**: Processes both `bcbs_ma_hmo` and `bcbs_ma_ppo` contracts

## Dramatiq Tasks

### Background Processing
- **`sync_attribution_data_async`**: Asynchronous task for BCBS data synchronization
  - **Queue**: "backfill"
  - **Parameters**: contract_pk, log_prefix, limit, offset, dry_run_off, delete_redundant_rows
  - **Purpose**: Processes BCBS attribution data in background

## State Machine Integration

### Attribution States
The Attribution model uses a state machine to track attribution lifecycle:
- **Contracted**: Member is assigned to a contract but not yet attributed
- **Provisional**: Member meets internal attribution requirements
- **Confirmed**: External payer confirms attribution
- **Ended**: Attribution has ended

### State Transitions
- **Contract Assignment**: Moves member to "contracted" state
- **Requirement Fulfillment**: Triggers transition to "provisional" state
- **Payer Confirmation**: Moves to "confirmed" state
- **Attribution End**: Transitions back to "contracted" or ends attribution

## Integration Points

### Internal Modules
- **Insurance Module**: Contract assignment and member eligibility
- **Onboarding Module**: POC form completion tracking
- **Visits Module**: Visit-based attribution requirements
- **User Module**: Member and provider relationships

### External Systems
- **Snowflake**: BCBS attribution data source
- **BCBS Systems**: External payer attribution confirmation
- **State Machines**: Attribution lifecycle management

## Use Cases

### Value-Based Care Attribution
1. Member enrolls in insurance plan with VBC contract
2. System assigns member to appropriate contract
3. Attribution requirement is determined based on contract type
4. Member completes required actions (PCP selection, visits, forms)
5. Internal attribution log is updated to "attributed"
6. System synchronizes with external payer for confirmation
7. Attribution state moves to "confirmed" when payer confirms

### BCBS Attribution Reconciliation
1. Daily sync job retrieves BCBS attribution data from Snowflake
2. System compares BCBS attribution status with internal records
3. PayerRosterRecord is created/updated with external status
4. Attribution state is reconciled between internal and external views
5. Discrepancies are flagged for review

### Attribution Requirement Changes
1. Contract attribution requirements are updated
2. Affected members are identified
3. Attribution status is recalculated based on new requirements
4. Members who no longer meet requirements are moved to appropriate state
5. New requirements trigger appropriate workflows for fulfillment
