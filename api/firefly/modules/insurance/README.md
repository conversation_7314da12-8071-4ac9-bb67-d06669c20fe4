# Insurance

## Motivation

The insurance module manages comprehensive insurance verification, eligibility checking, plan acceptance workflows, and attribution tracking for Firefly members. It integrates with external systems like Trizetto for real-time eligibility verification and Elation for billing coordination, while maintaining internal workflows for plan review and member coverage management.

## Business Context

Insurance management is critical for Firefly's operations, handling:
- Real-time insurance eligibility verification through Trizetto integration
- Plan acceptance workflows for new insurance products
- Attribution tracking for contract compliance and billing
- Member coverage status management and automated transitions
- Integration with appointment scheduling and care delivery workflows
- Contract management for employer and payer relationships

## Core Concepts

### Insurance Hierarchy
- **InsurancePayer**: Insurance companies (e.g., BCBS, Anthem, UniCare)
- **InsurancePlan**: Specific plans offered by payers (e.g., BCBS Blue Access PPO)
- **InsuranceMemberInfo**: Individual member coverage details and status
- **Contract**: Agreements between Firefly and payers/employers
- **Attribution**: Tracking member attribution for contract compliance

### Plan Types and Business Logic
- **HMO (Health Maintenance Organization)**: Requires PCP selection, referrals for specialists
- **PPO (Preferred Provider Organization)**: Direct specialist access, broader network flexibility
- **Plan Acceptance**: Automated and manual review workflows for new plans
- **Coverage Validation**: Real-time eligibility checking and status management

## Technical Implementation

### Core Models

**InsurancePayer**: Insurance company management
- Elation integration with payer codes for billing
- Trizetto integration for eligibility checking
- Firefly acceptance status and PMPM eligibility
- Logo and branding information for UI display

**InsurancePlan**: Plan-specific configuration and state management
- State machine integration for review workflows (draft → review → accepted/rejected)
- Firefly acceptance status with state-specific overrides
- Network associations for provider directory integration
- Automatic case creation for new plan review

**InsuranceMemberInfo**: Member coverage tracking
- Real-time eligibility status with Trizetto integration
- Coverage date management (start/end dates)
- PCP assignment tracking for HMO plans
- Automatic state transitions based on eligibility results

### Eligibility Integration

**Trizetto Integration**: Real-time eligibility verification
- Synchronous eligibility checks from Lucian provider interface
- Asynchronous batch processing for member sweeps
- Comprehensive request/response logging for audit and debugging
- Automatic member status updates based on eligibility results

**Eligibility Workflows**:
1. **Manual Checks**: Provider-initiated from member profile
2. **Scheduled Sweeps**: Monthly batch processing for all members
3. **Pre-Appointment**: 48-hour eligibility verification before visits
4. **Attribution Updates**: Automatic contract and attribution processing

### Attribution Management

**Attribution Requirements**: Contract-specific member attribution tracking
- PCP selection requirements for HMO plans
- Wellness visit completion tracking
- Provider of Choice (POC) form processing
- Automatic attribution status updates based on eligibility results

**Contract Integration**: Employer and payer contract management
- Attribution requirement configuration per contract
- PMPM (Per Member Per Month) eligibility tracking
- Automatic member status transitions based on attribution completion

## Business Logic

### Eligibility Processing Workflow

1. **Eligibility Check Initiation**: Manual or automated trigger
2. **Trizetto API Call**: Real-time eligibility verification with member details
3. **Response Processing**: Parse coverage dates, plan information, and PCP details
4. **Plan Matching**: Link to existing plans or create new plan for review
5. **Member Status Update**: Automatic transitions based on eligibility results
6. **Attribution Processing**: Update contract attribution based on eligibility data

### Plan Acceptance Workflow

**New Plan Discovery**: When Trizetto returns unknown plan descriptions
1. **Automatic Plan Creation**: Create InsurancePlan with plan description from Trizetto
2. **Case Generation**: Create insurance review case for Billing and Insurance team
3. **Manual Review**: Team determines Firefly acceptance status
4. **Bulk Member Update**: Backfill acceptance status for all members with the plan
5. **Status Transitions**: Automatic member transitions to "covered" if plan accepted

### Member Status Management

**Coverage Status Transitions**:
- **Active Coverage**: Valid insurance with current coverage dates
- **Expired Coverage**: Past coverage end date, triggers churn workflows
- **Future Coverage**: Coverage start date in future, maintains covered status
- **Invalid Coverage**: Failed eligibility check, triggers manual review

**HMO-Specific Logic**:
- PCP assignment validation for HMO plans
- Automatic case creation for HMO members without Firefly PCP
- Medicare HMO exception handling (no PCP requirement)

### Automated Scheduling Integration

**Pre-Appointment Verification**: 48-hour eligibility checks
- Automatic eligibility verification before scheduled appointments
- Insurance case creation for members with inactive coverage
- Integration with Braze for appointment reminder notifications
- Provider notification for coverage issues

## Configuration

### Adding New Payers

**Multi-System Setup Process**:

1. **Trizetto Enrollment**:
   - Enroll payer through Madakethealth Portal
   - Add to GatewayEdi integration for eligibility checking
   - Verify payer code configuration

2. **Elation Integration**:
   - Add Insurance Provider in Elation with correct payer code
   - Configure billing settings and claim submission

3. **Lucian Configuration**:
   - Run `pullinsuranceproviders` command to sync from Elation
   - Verify eligibility checking with test member
   - Monitor AutoEligibility request/response logs

### Plan Management

**Plan Discovery and Review**:
- Plans automatically created from Trizetto `plan_description` responses
- New plans trigger insurance review cases for manual acceptance decisions
- Bulk member status updates after plan acceptance/rejection decisions
- State-specific plan acceptance overrides for regional variations

### Contract Configuration

**Employer and Payer Contracts**:
- JSON-based contract configuration with schema validation
- Attribution requirement specification per contract type
- PMPM eligibility and billing configuration
- Employee identifier mapping (SID, SSN, Employee ID)

## API Endpoints

- `GET /insurance-payers/` - List available insurance payers
- `GET /insurance-plans/` - List insurance plans with acceptance status
- `GET/PUT /insurance-member-info/<id>/` - Member insurance details and updates
- `POST /check-insurance-eligibility/` - Manual eligibility verification
- `GET /employers/` - Employer information for contract management

## Limitations

- Trizetto integration requires manual payer enrollment and configuration
- Plan acceptance decisions require manual review by Billing and Insurance team
- Limited real-time eligibility checking for some payer types
- Attribution tracking depends on external data sources and manual processes
- Contract configuration requires careful JSON validation


