from __future__ import annotations

import json
import logging
import os
import re
from datetime import date
from typing import List

import waffle
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON>y<PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django.db.models import JSONField, Q, QuerySet
from django_deprecate_fields import deprecate_field
from jsonschema import validate
from jsonschema.exceptions import ValidationError

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.insurance.constants import AttributionRequirementType, EmployeeIdentifier
from firefly.modules.statemachines.insurance_plan_review.mixin import (
    InsurancePlanReviewStateMachineMixin,
    InsurancePlanReviewTransitionMetadata,
)
from firefly.modules.states.models import State
from firefly.modules.tasks.models import TaskRelation

PAYER_UNICARE = "UniCare"
PAYER_BCBS = "Blue Cross Blue Shield"
PAYER_BCBS_MA = "Blue Cross Blue Shield of Massachusetts"
PAYER_ANTHEM = "Anthem"
_dir_path = os.path.dirname(os.path.realpath(__file__))
PAYER_CONTRACT_CONFIG_SCHEMA = json.load(open(_dir_path + "/payer_contract_config_schema.json", "r"))
EMPLOYER_CONTRACT_CONFIG_SCHEMA = json.load(open(_dir_path + "/employer_contract_config_schema.json", "r"))

WAFFLE_CREATE_CASE_FOR_NEW_INSURANCE_PLAN = "feature.insurance.create_create_for_new_insurance_plan"

logger = logging.getLogger(__name__)


class PayerGroupType:
    PRIVATE = "private"
    GOVERNMENT = "government"


PAYER_GROUP_TYPE_CHOICES = [
    (PayerGroupType.PRIVATE, "Private Payer Group"),
    (PayerGroupType.GOVERNMENT, "Government Payer Group"),
]


class InsurancePayerGroup(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(unique=True, max_length=255, blank=False, null=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
        choices=PAYER_GROUP_TYPE_CHOICES,
    )

    def __str__(self):
        return f"{self.id} - {self.name}"

    class Meta(BaseModelV3.Meta):
        db_table = "insurance_payer_group"


class InsurancePayer(BaseModelV3):
    """An insurance payer (e.g. BCBS) and its associated Elation metadata."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_id = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_name = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_codes = ArrayField(models.CharField(max_length=5), blank=True, default=list)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_payer_code = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_address = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_address1 = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_address2 = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_city = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_state = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_zip = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # phone numbers may be formatted differently in elation
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    elation_phone_number = models.CharField(max_length=255, blank=True, default="")  # noqa: TID251
    # TODO - add accepted plans
    firefly_accepted = models.BooleanField(default=False)
    pmpm_eligible = models.BooleanField(default=False, blank=True, null=True)
    poc_template = models.ForeignKey(
        "documents.POCTemplate",
        related_name="payers",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_group = models.CharField(max_length=50, blank=True, null=True, default=None)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    short_name = models.CharField(max_length=50, blank=True, null=True, default=None)  # noqa: TID251
    hidden = models.BooleanField(
        default=False,
        blank=True,
        null=True,
        help_text="""Should this Payer be hidden from members during signup?
             Some Payers are configured for billing reasons but should not be visible to members""",
    )
    insurance_payer_groups = BaseModelV3ManyToManyField(
        InsurancePayerGroup,
        related_name="payers",
        blank=True,
        through="InsurancePayerInsurancePayerGroups",
    )
    logo = models.ImageField(null=True, blank=True, upload_to="insurance-payer-logos/")

    def __str__(self):
        return f"{self.id} - {self.name}"

    class Meta(BaseModelV3.Meta):
        db_table = "insurance_payers"


class InsurancePayerInsurancePayerGroups(BaseModelV3):
    insurancepayer = models.ForeignKey(
        InsurancePayer,
        related_name="insurancepayer_insurancepayergroups",
        on_delete=models.CASCADE,
    )

    insurancepayergroup = models.ForeignKey(
        InsurancePayerGroup,
        related_name="insurancepayer_insurancepayergroups",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "insurance_payers_insurance_payer_groups"
        verbose_name_plural: str = "Insurance payer insurance payer groups"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["insurancepayer", "insurancepayergroup"],
                condition=Q(deleted=None),
                name="insurance_payer_insurance_payer_groups_uniq",
            )
        ]


# We are importing our Networks from the Ribbon Insurances endpoint.
# Ribbon's UUID for each item is stored as an Alias Mapping. We map from
# our members' insurance plans to these Ribbon versions of the networks
# associated with their plans, which then determine which providers are in-
# or out-of-network for the member.
# See https://ribbon.readme.io/docs/insurances-reference-endpoint
class Network(BaseModelV3):
    # The display_name, from Ribbon
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )

    # The ... name of the plan
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    # Also called "Health plan categories", metal levels are based on how
    # the member and the insurance plan split costs. Typically:
    # Bronze, Silver, Gold, and Platinum.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    metal_level = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    def __str__(self):
        return f"{self.id} - {self.name}"

    def delete(self, *args, **kwargs):
        AliasMapping.delete_mapping_for_object(obj=self, alias_name=AliasName.RIBBON)
        return super().delete(*args, **kwargs)


class InsurancePlan(SaveHandlersMixin, BaseModelV3, InsurancePlanReviewStateMachineMixin):
    """An insurance payers's plan (e.g. BCBS's BLUE ACCESS PPO plan)"""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255)  # noqa: TID251
    firefly_accepted = models.BooleanField(
        default=False,
        help_text="Note that this is used in addition to Firefly accepted states."
        " Even if this value is set to False, and there is any state marked"
        " as Firefly accepted states for this plan, the plan is accepted for that state",
    )
    insurance_payer = models.ForeignKey(
        InsurancePayer,
        related_name="insurance_plans",
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    networks = BaseModelV3ManyToManyField(Network, blank=True, through="InsurancePlanNetworks")

    firefly_accepted_states = BaseModelV3ManyToManyField(State, blank=True, through="InsurancePlanStates")
    insuranceplan_networks: QuerySet["InsurancePlanNetworks"]
    insurance_member_infos: QuerySet["InsuranceMemberInfo"]

    def __str__(self):
        return f"{self.id} - {self.name}"

    def pre_save_mutation(self, changed):
        if not self._state.adding and (changed("firefly_accepted") or changed("insurance_payer")):
            if self.review_state == InsurancePlanReviewTransitionMetadata.States.UNREVIEWED:
                self.review_state = InsurancePlanReviewTransitionMetadata.States.REVIEWED

    class Meta(BaseModelV3.Meta):
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["name", "insurance_payer"],
                condition=Q(deleted=None),
                name="insurance_plans_insurance_payer_uniq",
            )
        ]
        pass


class InsurancePlanNetworks(BaseModelV3):
    insuranceplan = models.ForeignKey(
        InsurancePlan,
        related_name="insuranceplan_networks",
        on_delete=models.CASCADE,
    )

    network = models.ForeignKey(
        Network,
        related_name="insuranceplan_networks",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "insurance_insuranceplan_networks"
        verbose_name_plural: str = "Insurance plan networks"
        constraints = [
            models.UniqueConstraint(
                fields=["insuranceplan", "network"],
                condition=Q(deleted=None),
                name="insurance_insuranceplan_network_uniq",
            )
        ]

    def save(self, *args, **kwargs):
        super(InsurancePlanNetworks, self).save(*args, **kwargs)
        if waffle.switch_is_active(WAFFLE_CREATE_CASE_FOR_NEW_INSURANCE_PLAN):
            if self.network:
                from firefly.modules.insurance.utils import close_cases_for_insurance_plans_with_network

                # if a network was added, find any existing cases and auto-close them
                close_cases_for_insurance_plans_with_network(self.insuranceplan)


class InsurancePlanStates(BaseModelV3):
    insuranceplan = models.ForeignKey(
        InsurancePlan,
        related_name="insuranceplan_states",
        on_delete=models.CASCADE,
    )

    state = models.ForeignKey(
        State,
        related_name="insuranceplan_states",
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name_plural: str = "Insurance plan states"
        constraints = [
            models.UniqueConstraint(
                fields=["insuranceplan", "state"],
                condition=Q(deleted=None),
                name="insurance_plan_states_uniq",
            )
        ]


class Employer(BaseModelV3):
    """An employer for a member, used for attribution."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    employer_abbreviation = models.CharField(max_length=8, blank=True, default="")  # noqa: TID251

    firefly_partner = models.BooleanField(default=False)
    is_PEPM_RTW = deprecate_field(models.BooleanField(default=False))
    is_screening_only = deprecate_field(models.BooleanField(default=False))
    send_screening_reminders = models.BooleanField(default=True)
    screening_reminder_batch = models.IntegerField(null=True, blank=True, default=None)
    send_vaccine_form = models.BooleanField(default=True, null=True, blank=True)
    IDENTIFIER_TYPE_CHOICES = [
        (EmployeeIdentifier.SID, EmployeeIdentifier.SID),
        (EmployeeIdentifier.SSN, EmployeeIdentifier.SSN),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    identifier_type = models.CharField(max_length=255, choices=IDENTIFIER_TYPE_CHOICES, null=True, blank=True)  # noqa: TID251

    def __str__(self):
        return f"{self.id} - {self.name}"


class EmployerGroup(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    block_business_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    master_group_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    client_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    location_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    location_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_sic_code = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    group_size = models.IntegerField(null=True, blank=True)
    group_effective_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = [["group_id", "location_id"]]

    def save(self, keep_deleted=False, **kwargs):
        super().save(keep_deleted=keep_deleted, **kwargs)
        try:
            employer = Employer.objects.get(name=self.client_name)
            alias_ids = AliasMapping.get_alias_id_for_object(employer, AliasName.FLUME)
            if not alias_ids:
                AliasMapping.set_mapping_by_object(employer, AliasName.FLUME, self.group_id)
        except Employer.DoesNotExist:
            logger.exception("Failed to create Flume alias mapping for employer: %s", self.client_name)


class Contract(BaseModelV3):
    def save(self, *args, **kwargs):
        try:
            validate(self.config, PAYER_CONTRACT_CONFIG_SCHEMA)
        except ValidationError:
            validate(self.config, EMPLOYER_CONTRACT_CONFIG_SCHEMA)
        super().save(*args, **kwargs)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # A human-readable, understandable key that the code and analytics can use to refer to
    # this contract (versus the name, which could be changed for legibility, or the ID, which
    # is not intuitive and which varies across environments).
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(  # noqa: TID251
        unique=True,
        max_length=255,
        null=True,
        blank=True,
    )
    config = JSONField(default=dict)
    # This field specifies the frequency, in days, at which automated eligibility checks should be performed
    # If set to 30, the system will run eligibility checks every 30 days
    automated_eligibility_check_frequency_in_days = models.IntegerField(null=True, blank=True)

    # To determine if a person is attributed to a contract, we must at least
    # know if they are enrolled in the relevant program. Note that there may be
    # other criteria that must be met to become attributed.
    class ContractType(models.TextChoices):
        # Look for enrollment in the Primary Care program.
        CARE = "care", "Care"
        # Look for enrollment in the Benefit program.
        COVERAGE = "coverage", "Coverage"

    contract_type = models.TextField(
        help_text="Indicates how enrollment should be determined when calculating attribution.",
        choices=ContractType.choices,
        null=True,
        blank=True,
    )

    contracted_entity_content_type = models.ForeignKey(
        ContentType,
        limit_choices_to={
            "app_label": "insurance",
            "model__in": ("employer", "insurancepayer"),
        },
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    contracted_entity_object_id = models.PositiveIntegerField(null=True, blank=True)
    contracted_entity = GenericForeignKey("contracted_entity_content_type", "contracted_entity_object_id")
    MEMBER_IDENTIFIER_TYPE_CHOICES = [
        (EmployeeIdentifier.EMPLOYEE_ID, EmployeeIdentifier.EMPLOYEE_ID),
        (EmployeeIdentifier.SSN, EmployeeIdentifier.SSN),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    member_identifier_type = models.CharField(  # noqa: TID251
        max_length=255, choices=MEMBER_IDENTIFIER_TYPE_CHOICES, null=True, blank=True
    )


class InsuranceMemberInfo(SaveHandlersMixin, BaseModelV3):
    """Holds payer, insurance metadata (member ID / group), and address for a patient."""

    _save_handlers_mixin__refresh_on_save = True

    ATTRIBUTION_REQUIREMENT_TYPE_CHOICES = (
        (
            AttributionRequirementType.PCP_SELECTION,
            AttributionRequirementType.PCP_SELECTION,
        ),
        (
            AttributionRequirementType.WELLNESS_VISIT_IN_TWO_YEARS,
            AttributionRequirementType.WELLNESS_VISIT_IN_TWO_YEARS,
        ),
        (AttributionRequirementType.POC_FORM, AttributionRequirementType.POC_FORM),
        (AttributionRequirementType.VISITS, AttributionRequirementType.VISITS),
        (AttributionRequirementType.PHONECALL, AttributionRequirementType.PHONECALL),
        (
            AttributionRequirementType.FEE_FOR_SERVICE,
            AttributionRequirementType.FEE_FOR_SERVICE,
        ),
    )

    PLAN_TYPE_HMO = "hmo"
    PLAN_TYPE_PPO = "ppo"
    PLAN_TYPE_CHOICES = [
        (PLAN_TYPE_HMO, "HMO"),
        (PLAN_TYPE_PPO, "PPO"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_type = models.CharField(max_length=3, choices=PLAN_TYPE_CHOICES, blank=True, null=True)  # noqa: TID251

    SOURCE_TYPE_EMPLOYER = "employer"
    SOURCE_TYPE_PRIVATE = "private"
    SOURCE_TYPE_MEDICARE_MEDICAID = "medicare_medicaid"
    SOURCE_TYPE_NONE = "none"
    SOURCE_TYPE_CHOICES = [
        (SOURCE_TYPE_EMPLOYER, "employer"),
        (SOURCE_TYPE_PRIVATE, "private"),
        (SOURCE_TYPE_MEDICARE_MEDICAID, "medicare_medicaid"),
        (SOURCE_TYPE_NONE, "none"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    source_type = models.CharField(max_length=255, choices=SOURCE_TYPE_CHOICES, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_description = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    coverage_start = models.DateField(blank=True, null=True)
    coverage_end = models.DateField(blank=True, null=True)

    insurance_payer = models.ForeignKey(
        InsurancePayer,
        related_name="members",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )

    insurance_plan = models.ForeignKey(
        InsurancePlan,
        related_name="insurance_member_infos",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    member_id = models.CharField(max_length=255, null=True, blank=True)  # member id / policy number  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    member_id_textract = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    is_primary_subscriber = models.BooleanField(default=None, null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    primary_first_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    primary_last_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    primary_dob = models.DateField(blank=True, null=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_number = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    group_number_textract = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    street_address = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    street_address_2 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    city = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    state = models.CharField(max_length=2, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zipcode = models.CharField(max_length=10, blank=True, null=True)  # noqa: TID251

    # Latitude and Longitude ##.###### and ###.######
    latitude = models.DecimalField(max_digits=8, decimal_places=6, null=True, blank=True, default=None)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True, default=None)

    # Whether insurance info has been synced to Elation.
    # After initial sync, insurance info will not be sent to Elation.
    # DRA-1124: See requires_sync_to_elation
    synced_to_elation = models.BooleanField(default=False)

    image_front = models.ImageField(null=True, blank=True, upload_to="insurance-photos/")
    image_back = models.ImageField(null=True, blank=True, upload_to="insurance-photos/")

    tasks = GenericRelation(TaskRelation)

    # Fields to store parsed PCP data from the last eligibility response logs
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pcp_npi = models.CharField(max_length=11, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pcp_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    is_firefly_pcp = models.BooleanField(blank=True, null=True)

    automate_poc = models.BooleanField(blank=True, null=True, default=True)

    # TODO: Deprecate once insurance outreach cases are stable
    needs_review = models.BooleanField(null=True, blank=True)

    firefly_pcp_at = models.DateTimeField(null=True, blank=True)

    @property
    def addresses(self):
        return [
            {
                "street_address": self.street_address,
                "street_address_2": self.street_address_2,
                "city": self.city,
                "state": self.state,
                "zipcode": self.zipcode,
            }
        ]

    @property
    def patient_address(self):
        return self.addresses[0]

    @property
    def is_accepted_payer(self):
        # We accept all UniCare plans
        is_payer_unicare = self.insurance_payer is not None and self.insurance_payer.name == PAYER_UNICARE
        if is_payer_unicare:
            return True
        is_payer_with_accepted_plan = InsurancePlan.objects.filter(
            insurance_payer=self.insurance_payer, firefly_accepted=True
        ).exists()
        return is_payer_with_accepted_plan

    @property
    def is_valid_for_eligibility_checks(self):
        """
        Can this info ever be successfully submitted to Trizetto?
        """
        if not isinstance(self.member_id, str):
            return False
        member_id_len = len(self.member_id)
        if member_id_len < 8 or member_id_len > 20:
            # Avoid the Trizetto error:
            # 'Subscriber ID must be between 8 and 20 characters'
            return False
        return True

    @property
    def triggers_auto_eligibility(self):
        """
        Eligibility checks cost money so we want to limit auto-checks
        to only the insurances we anticipate will be successful
        We still allow manually-triggered checks for insurance infos
        which don't meet these criteria
        """
        return self.is_valid_for_eligibility_checks and self.is_accepted_payer

    @property
    def requires_poc_form(self):
        if not (
            self.insurance_payer
            and self.person.attribution.attribution_requirement_type == AttributionRequirementType.POC_FORM
        ):
            return False
        if not isinstance(self.member_id, str):
            return False
        # Weird BCBS check (DRA-818)
        is_payer_bcbs = "blue cross" in self.insurance_payer.name.lower()
        if is_payer_bcbs and re.search(r"^r[\d]*$", self.member_id, re.IGNORECASE):
            return False
        return True

    def pre_save_validation(self, changed):
        """
        Validate that plan_description is in sync with insurance_plan.name if both are present.
        """
        if self.plan_description and self.insurance_plan and self.insurance_plan.name:
            # Check if plan_description matches insurance_plan.name (case-insensitive)
            if self.plan_description.lower() != self.insurance_plan.name.lower():
                # We don't raise an error here, just log a warning till the existing data has been fixed
                logger.warning(
                    "InsuranceMemberInfo %s: plan_description '%s' does not match insurance_plan.name '%s'",
                    self.id,
                    self.plan_description,
                    self.insurance_plan.name,
                )

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.core.user.utils import assign_or_close_update_pcp_task
        from firefly.modules.change_data_capture.subscribers.elation.subscribers import elation_update_user
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_person_change
        from firefly.modules.firefly_django.utils import get_lucian_bot_user
        from firefly.modules.insurance.utils import (
            create_insurance_plan_review_case,
            handle_invalid_insurance,
            update_contract_and_attribution,
        )

        if hasattr(self, "person") and self.person:
            if changed("coverage_start") or changed("coverage_end") or changed("insurance_plan"):
                # This is an approximation. In the case of care members - the presence of a coverage node
                # in the XML - signified by a benefit node with service type code 30 determines whether the
                # insurance is active. This approximation relies on upstream functions setting the coverage_start
                # field only when an active coverage is found.
                is_eligible = (
                    self.coverage_start
                    and self.coverage_start <= date.today()
                    and (not self.coverage_end or (self.coverage_end and self.coverage_end > date.today()))
                )
                logger.info(
                    "Person %d: Changes detected in insurance fields. Is Eligible %s", self.person.id, is_eligible
                )
                update_contract_and_attribution(self.person)
                # If we got a successful eligibility check and found an attribution type, we are confident that we don't
                # need manual review and can transition automatically
                attribution_type = self.person.attribution.attribution_requirement_type
                if is_eligible and attribution_type is not None:
                    self.person.eligible(actor=get_lucian_bot_user())
                    if self.person.user is not None:
                        self.person.user.onboarding_state.insurance_covered(actor=self.person.user)
                elif not is_eligible:
                    logger.info(
                        "Person %d: Handling invalid insurance with coverage start: %s and end date: %s",
                        self.person.id,
                        self.coverage_start,
                        self.coverage_end,
                    )
                    handle_invalid_insurance(self.person, self.coverage_start, self.coverage_end)
            else:
                logger.info("Person %d: No changes detected in insurance fields", self.person.id)
            if (
                changed("plan_type")
                or changed("state")
                or changed("insurance_payer")
                or changed("is_firefly_pcp")
                or changed("firefly_pcp_at")
            ) and self.person.user:
                assign_or_close_update_pcp_task(self.person.user)
            if not changed("id") and (
                changed("street_address")
                or changed("street_address_2")
                or changed("city")
                or changed("state")
                or changed("zipcode")
                or changed("member_id")
                or changed("group_number")
                or changed("insurance_payer")
                or changed("insurance_payer_id")
            ):
                logger.info("Person %d: Invoking elation update from insurance change", self.person.id)
                zus_handle_person_change.send(person_id=self.person.id)
                elation_update_user.send(person_id=self.person.id)
            if changed("insurance_plan") and self.insurance_plan:
                if self.insurance_plan.review_state == InsurancePlanReviewTransitionMetadata.States.UNREVIEWED:
                    create_insurance_plan_review_case(self.person)

    class Meta:
        db_table = "insurance_member_info"

    def clean(self):
        if self.group_number is not None and "-" in self.group_number:
            log_prefix: str = "group_id_cleaner"
            old_group_number = self.group_number
            self.group_number = old_group_number.replace("-", "")
            logger.info(
                "%s: Insurance info: %s. Updating group number from %s to %s",
                log_prefix,
                str(self.pk),
                old_group_number,
                self.group_number,
            )

    def save(self, *args, **kwargs):
        self.clean()
        return super(InsuranceMemberInfo, self).save(*args, **kwargs)
