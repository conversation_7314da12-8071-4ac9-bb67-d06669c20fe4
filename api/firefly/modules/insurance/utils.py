from __future__ import annotations

import csv
import io
import logging
import re
from datetime import date, datetime, timedelta
from typing import TYPE_CHECKING, List, Optional, Union

import boto3
import trp.trp2 as trp
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.db.models.query import QuerySet
from django.shortcuts import get_object_or_404
from django.utils import timezone

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.services.auto_eligibility_service.models import AutoEligibilityResponseLog
from firefly.core.services.braze.tasks import BrazeRecipient, reminders
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP
from firefly.modules.auto_eligibility.constants import ELATION_PAYER_CODES, TRIZETTO_ERRORS
from firefly.modules.cases.constants import (
    INSURANCE_OUTREACH,
    INSURANCE_PLAN_NEEDS_REVIEW,
    INSURANCE_VISIT_PREP_CATEGORY_GRACE_PERIOD,
    INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY,
    CaseActions,
    CaseStatus,
)
from firefly.modules.eligibility.models import EligibilityRecord
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.insurance.constants import (
    NEW_INSURANCE_PLAN_CASE_CATEGORY,
    AttributionRequirementType,
    ContractAttributionType,
    InsuranceReviewReasons,
)
from firefly.modules.insurance.models import (
    PAYER_ANTHEM,
    PAYER_BCBS,
    PAYER_BCBS_MA,
    Contract,
    Employer,
    InsuranceMemberInfo,
    InsurancePayer,
    InsurancePlan,
)
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.models import Program
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.statemachines.utils import convert_date_to_end_of_working_est
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import TaskCollectionTask
from firefly.modules.work_units.constants import StatusCategory
from firefly.modules.work_units.utils import WORKUNIT_COMPLETION_STATUSES

if TYPE_CHECKING:
    from firefly.core.user.models import Person
    from firefly.modules.cases.models import Case

logger = logging.getLogger(__name__)


def csv_string_to_dict_list(raw: str) -> List[dict]:
    buffer = io.StringIO(raw)
    reader = csv.DictReader(buffer, delimiter=",")
    return list(reader)


def prefix_to_payer_object(prefix: str) -> Union[InsurancePayer, None]:
    try:
        alias_mapping: AliasMapping = AliasMapping.get_alias_mapping_for_alias(
            alias_id=prefix.upper(),
            alias_name=AliasName.BCBS_PREFIX,
            content_type=ContentType.objects.get_for_model(InsurancePayer),
        )
        return InsurancePayer.objects.get(id=alias_mapping.object_id)
    except Exception:
        logger.exception("prefix_to_payer_object: Failed to prefix to payer object for prefix: %s", prefix)
    return None


def contract_and_attribution_for_person(person: Person):
    from firefly.core.user.utils import get_primary_subscriber_employer
    from firefly.modules.attribution.models import AbstractAttributionData, PayerRosterRecord

    insurance_info: Optional[InsuranceMemberInfo] = person.insurance_info
    if not insurance_info:
        raise Exception("Cannot find contract/attribution for person because they do not have insurance info")
    payer_roster_record: Optional[PayerRosterRecord] = PayerRosterRecord.objects.filter(
        person=person,
        status=AbstractAttributionData.STATUS_ATTRIBUTED,
    ).first()
    if (
        hasattr(insurance_info, "insurance_payer")
        and insurance_info.insurance_payer
        and hasattr(insurance_info.insurance_payer, "payer_group")
    ):
        payer_group = insurance_info.insurance_payer.payer_group
    else:
        payer_group = None
    return ContractFinder(
        payer_group=payer_group,
        payer=insurance_info.insurance_payer,
        plan=insurance_info.insurance_plan,
        plan_type=insurance_info.plan_type,
        patient_state_of_residence=insurance_info.patient_address["state"],
        primary_subscriber_employer=get_primary_subscriber_employer(person=person),
        user=person.user,
        payer_roster_record=payer_roster_record,
    ).find()


def update_contract_and_attribution(
    person,
    dry_run_off: bool = True,
    log_prefix: str = "update_contract_and_attribution",
):
    from firefly.modules.attribution.utils import recalculate_attribution_state_for_person

    contract_result = contract_and_attribution_for_person(person)
    logger.info("%s: Person: %s", log_prefix, person.pk)

    try:
        updated_attribution_fields: List[str] = []
        if hasattr(person, "attribution") and person.attribution is not None:
            from firefly.modules.attribution.models import Attribution

            attribution: Attribution = person.attribution
            if attribution.contract != contract_result.get("contract"):
                updated_attribution_fields.append("contract")
                attribution.contract = contract_result.get("contract")
            attribution_requirement_type = contract_result.get("attribution_type")
            if attribution.attribution_requirement_type != attribution_requirement_type:
                updated_attribution_fields.append("attribution_requirement_type")
                attribution.attribution_requirement_type = attribution_requirement_type
            ineligible_reason = contract_result.get("ineligible_reason")
            if attribution.contract_failure_reason != ineligible_reason:
                updated_attribution_fields.append("contract_failure_reason")
                attribution.contract_failure_reason = ineligible_reason
            if len(updated_attribution_fields) > 0 and dry_run_off:
                attribution.save(update_fields=updated_attribution_fields)
                logger.info("%s: Person: %s. Attribution Update complete", log_prefix, person.pk)
    except Exception:
        logger.exception("%s: Person: %s. Failed to process attribution data", log_prefix, person.pk)

    try:
        # Recalculate attribution state based on the contract details
        recalculate_attribution_state_for_person(dry_run_off=dry_run_off, person=person)
    except Exception:
        logger.exception("%s: Person: %s. Failed to recalculate attribution", log_prefix, person.pk)
    return contract_result


# Use prefix of member ID to update person payer
def update_anthem_bcbs_payer(
    person,
    dry_run_off: bool = True,
    log_prefix: str = "update_anthem_bcbs_payer",
):
    prefix = person.insurance_info.member_id[:3]
    payer = prefix_to_payer_object(prefix)
    current_payer: Optional[InsurancePayer] = person.insurance_info.insurance_payer
    if payer is not None and payer != current_payer:
        logger.info(
            "%s: Person: %d Updating payer from %s to %s",
            log_prefix,
            person.pk,
            str(person.insurance_info.insurance_payer),
            str(payer),
        )
        person.insurance_info.insurance_payer = payer
        if dry_run_off:
            person.insurance_info.save(update_fields=["insurance_payer"])


def is_payer_anthem_bcbs(person):
    bcbs_anthem_payers = [PAYER_BCBS, PAYER_ANTHEM]
    return person.insurance_info.insurance_payer.name in bcbs_anthem_payers


def does_contract_match(
    contract: Contract,
    payer_id: int,
    plan_type: Optional[str] = None,
    patient_state_of_residence: Optional[str] = None,
    primary_subscriber_employer_id: Optional[str] = None,
) -> bool:
    contract_plan_type: Optional[str] = contract.config.get("plan_type")
    contract_payer_id: Optional[str] = contract.config.get("payer_id")
    is_state_specific: Optional[bool] = contract.config.get("is_state_specific")
    insurance_state: Optional[str] = contract.config.get("insurance_state")
    exclude_insurance_state: Optional[str] = contract.config.get("exclude_insurance_state")
    contract_employer_id: Optional[str] = contract.config.get("employer_id")
    if (
        # Matches a contract with a employer
        (
            contract_employer_id is not None
            and primary_subscriber_employer_id is not None
            and contract_employer_id == primary_subscriber_employer_id
        )
        # Matches a contract with a payer
        or (
            contract_payer_id is not None
            and contract_payer_id == payer_id
            # either contract accepts all plan types or the plan type matches
            and (contract_plan_type is None or (plan_type is not None and contract_plan_type.upper() == plan_type))
            # either contract accepts out of state members or member resides in the configured state
            and (
                is_state_specific is None
                or is_state_specific is False
                or insurance_state is None
                or (patient_state_of_residence is not None and patient_state_of_residence.upper() == insurance_state)
            )
            # either contract does not exclude a given state or member does not reside in the excluded state
            and (
                exclude_insurance_state is None
                or (
                    patient_state_of_residence is not None
                    and patient_state_of_residence.upper() != exclude_insurance_state
                )
            )
        )
    ):
        return True
    return False


def contract_matches_payer_group_and_residence(contract, payer_group, patient_state_of_residence):
    contract_payer = contract.config.get("payer_group")
    insurance_state = contract.config.get("insurance_state")
    if contract_payer is None or insurance_state is None:
        return False
    return contract_payer == payer_group and insurance_state == patient_state_of_residence


def contract_is_valid_ppo_out_of_state(contract, payer_group):
    contract_payer = contract.config.get("payer_group")
    if contract_payer is None:
        return False
    if contract.config.get("accept_all_ppo") is not True:
        return False
    return contract_payer == payer_group


class ContractFinder:
    """
    Use ContractFinder().find() to look for a Contract and/or an attribution requirement
    """

    class IneligibleReasons:
        # In Lucian, these reasons are mapped to suggestions for fixing
        UNLICENSED_STATE = "UNLICENSED_STATE"
        NOT_PPO = "NOT_PPO"
        NO_CONTRACT_MATCHES_PAYER_AND_STATE = "NO_CONTRACT_MATCHES_PAYER_AND_STATE"
        INELIGIBLE_PLAN = "INELIGIBLE_PLAN"
        INELIGIBLE_PAYER = "INELIGIBLE_PAYER"
        NO_CONTRACT_MATCHING_PAYER = "NO_CONTRACT_MATCHING_PAYER"
        NO_OOS_CONTRACT_ALLOWED = "NO_OOS_CONTRACT_ALLOWED"

    def __init__(
        self,
        payer: Optional[InsurancePayer],
        payer_group: Optional[str],
        plan: Optional[InsurancePlan],
        patient_state_of_residence: Optional[str] = None,
        plan_type: Optional[str] = None,
        primary_subscriber_employer: Optional[Employer] = None,
        user=None,
        payer_roster_record=None,
    ):
        self.payer_group = payer_group
        self.payer = payer
        self.primary_subscriber_employer_id: Optional[int] = None
        if primary_subscriber_employer is not None:
            self.primary_subscriber_employer_id = primary_subscriber_employer.id
        self.plan = plan
        self.plan_type = plan_type.upper() if plan_type else None
        self.patient_state_of_residence = patient_state_of_residence.upper() if patient_state_of_residence else None
        self.contract = None
        self.ineligible_reason = None  # May be set to one of IneligibleReasons
        self.is_out_of_state_payer = False
        self.accept_all_ppo_contract = False
        self.user = user
        self.payer_roster_record = payer_roster_record

    def check_payer_and_plan(self, contract):
        payer_accepted = self.payer and self.payer.firefly_accepted
        plan_accepted = self.plan and (
            self.plan.firefly_accepted_states.filter(abbreviation=self.patient_state_of_residence).exists()
            if self.plan.firefly_accepted_states.exists()
            else self.plan.firefly_accepted
        )
        if contract.config.get("plan_description_specific") is False or (payer_accepted and plan_accepted):
            return True
        elif not payer_accepted:
            self.ineligible_reason = self.IneligibleReasons.INELIGIBLE_PAYER
            return False
        elif not plan_accepted:
            self.ineligible_reason = self.IneligibleReasons.INELIGIBLE_PLAN
            return False

    def find(self):
        """
        Return one of:
            a) {"contract": <Contract>, "attribution_type": <AttributionRequirementType>}
            b) {"attribution_type": AttributionRequirementType.FEE_FOR_SERVICE}
            c) {"ineligible_reason": <str>}
        """
        search_result = self.__search()
        attribution_type = self.__attribution_type()
        if search_result or attribution_type:
            return {"contract": self.contract, "attribution_type": attribution_type}
        return {"ineligible_reason": self.ineligible_reason}

    def __search(self):
        """
        Search through all possible Contracts to find a match

        This logic should be kept up to date with documentation:
        https://whimsical.com/from-member-card-to-contract-DvWSyfgW2Qfo51sSv2QFSf
        """
        from firefly.modules.attribution.models import AbstractAttributionData

        success = False

        # Are we licensed to service this state at all?
        from firefly.modules.states.models import State

        licensed_states = list(State.objects.filter(can_service=True).values_list("abbreviation", flat=True))
        # For care employer contract, user's state information will not be present in roster.
        # Hence Licensed to service will be checked when a user is linked to person.
        if self.user and self.patient_state_of_residence not in licensed_states:
            self.ineligible_reason = self.IneligibleReasons.UNLICENSED_STATE
            logger.info("patient_state_of_residence is not eligible.")
            return False

        contracts = Contract.objects.all().order_by("-id")
        contract: Optional[Contract] = None
        if hasattr(self.payer, "id"):
            payer_id = self.payer.id
        else:
            # payer ID will be None when person is created from Web for the first time
            # and payer ID is not required to find the employer contract

            payer_id = None

        is_roster_override = False
        if (
            self.payer_roster_record is not None
            and self.payer_roster_record.deleted is None
            and self.payer_roster_record.status == AbstractAttributionData.STATUS_ATTRIBUTED
        ):
            contract = self.payer_roster_record.contract
            is_roster_override = True
        if contract is None:
            contract = next(
                filter(
                    lambda contract: does_contract_match(
                        contract=contract,
                        payer_id=payer_id,
                        plan_type=self.plan_type,
                        patient_state_of_residence=self.patient_state_of_residence,
                        primary_subscriber_employer_id=self.primary_subscriber_employer_id,
                    ),
                    contracts,
                ),
                None,
            )
        if contract is None:
            # Look for a Contract belonging to a different Payer which could
            # still apply to this plan
            #
            # First, this is only possible for PPO plans
            from firefly.modules.insurance.models import InsuranceMemberInfo

            if (self.plan_type is None) or self.plan_type != InsuranceMemberInfo.PLAN_TYPE_PPO.upper():
                self.ineligible_reason = self.IneligibleReasons.NOT_PPO
                return False
            # We can only use a Contract which accepts this patient's state
            contract = next(
                filter(
                    lambda contract: contract_matches_payer_group_and_residence(
                        contract, self.payer_group, self.patient_state_of_residence
                    ),
                    contracts,
                ),
                None,
            )
            if contract is not None:
                self.is_out_of_state_payer = True
            else:
                self.ineligible_reason = self.IneligibleReasons.NO_CONTRACT_MATCHES_PAYER_AND_STATE
            # ...and the Contract must also accept out-of-state plans in general
            if contract and not contract.config.get("accepts_out_of_state_payers", False):
                self.ineligible_reason = self.IneligibleReasons.NO_OOS_CONTRACT_ALLOWED
            if self.ineligible_reason is not None:
                # Some payer group contracts allow us to always accept a PPO plan
                # even in out-of-state scenarios
                contract = next(
                    filter(lambda contract: contract_is_valid_ppo_out_of_state(contract, self.payer_group), contracts),
                    None,
                )
                if contract is not None:
                    self.contract = contract
                    self.accept_all_ppo_contract = True
                    self.ineligible_reason = None
                else:
                    self.ineligible_reason = self.IneligibleReasons.NO_OOS_CONTRACT_ALLOWED
                    return False

            success = self.check_payer_and_plan(contract)
        else:
            # Easy path: we have a direct contract with the payer
            if not is_roster_override:
                success = self.check_payer_and_plan(contract)
            else:
                success = True

        if success and not self.ineligible_reason:
            self.contract = contract
        return success

    def __attribution_type(self):
        """
        Return an AttributionRequirementType after finding a contract
        """
        if self.ineligible_reason == self.IneligibleReasons.UNLICENSED_STATE:
            return None
        if self.contract is None and self.payer and self.payer.firefly_accepted and self.payer.pmpm_eligible is False:
            return AttributionRequirementType.FEE_FOR_SERVICE
        if self.contract is None or self.ineligible_reason is not None:
            return None
        if self.accept_all_ppo_contract is True:
            return AttributionRequirementType.FEE_FOR_SERVICE
        config_type = self.contract.config.get("attribution_type")
        if config_type is None:
            raise Exception(f"Contract {self.contract.pk} has no attribution_type")
        elif config_type == ContractAttributionType.POC_FORM:
            return AttributionRequirementType.POC_FORM
        elif config_type == ContractAttributionType.PHONECALL:
            return AttributionRequirementType.PHONECALL
        elif config_type == ContractAttributionType.VISITS_OR_POC_FORM:
            return (
                AttributionRequirementType.VISITS if self.is_out_of_state_payer else AttributionRequirementType.POC_FORM
            )
        elif config_type == ContractAttributionType.NONE:
            return AttributionRequirementType.FEE_FOR_SERVICE
        elif config_type == ContractAttributionType.PCP_SELECTION:
            return AttributionRequirementType.PCP_SELECTION
        elif config_type == ContractAttributionType.WELLNESS_VISIT_IN_TWO_YEARS:
            return AttributionRequirementType.WELLNESS_VISIT_IN_TWO_YEARS
        elif config_type == ContractAttributionType.AUTO_ATTRIBUTED:
            return AttributionRequirementType.AUTO_ATTRIBUTED
        elif config_type == ContractAttributionType.OPT_IN_PCP:
            return AttributionRequirementType.OPT_IN_PCP
        else:
            raise Exception(f"Contract {self.contract.pk} has unexpected attribution_type {config_type}")


def insurance_is_active(insurance_info) -> bool:
    coverage_has_started = insurance_info.coverage_start is not None and insurance_info.coverage_start <= date.today()
    coverage_has_ended = insurance_info.coverage_end is not None and insurance_info.coverage_end <= date.today()
    return coverage_has_started and not coverage_has_ended


def is_firefly_insurance_active(person) -> bool:
    try:
        eligibility_record = EligibilityRecord.objects.get(member=person)
        coverage_has_started = (
            eligibility_record.effective_date is not None and eligibility_record.effective_date.date() <= date.today()
        )
        coverage_has_ended = (
            eligibility_record.termination_date is not None
            and eligibility_record.termination_date.date() <= date.today()
        )
        return coverage_has_started and not coverage_has_ended
    except EligibilityRecord.DoesNotExist:
        return False


def create_outreach_insurance_case(person):
    """Create/update the outreach case for the billing team."""
    from firefly.core.user.models import AssigneeGroup
    from firefly.modules.cases.models import Case, CaseCategory
    from firefly.modules.cases.utils import get_open_case
    from firefly.modules.programs.utils import is_current_enrollment_status

    if person.user is not None and (
        (
            person.user.onboarding_state is not None
            and person.user.onboarding_state.status == OnboardingStatus.DEACTIVATED
        )
        or is_current_enrollment_status(person, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.CHURNED)
    ):
        return
    # If we get an error from Trizetto, still assign the outreach case
    eligibility_response = AutoEligibilityResponseLog.objects.filter(person_id=person.pk).order_by("created_at").last()
    errors = [r for r in eligibility_response.rejections if r in TRIZETTO_ERRORS] if eligibility_response else []
    if len(errors) > 0:
        billing_and_insurance_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["billing_and_insurance"]
        billing_and_insurance_assignee_group = get_object_or_404(
            AssigneeGroup, unique_key=billing_and_insurance_unique_key
        )
        category = CaseCategory.objects.get(unique_key=INSURANCE_OUTREACH)
        payer_name: str = "Unknown"
        if person.insurance_info.insurance_payer is not None:
            payer_name = person.insurance_info.insurance_payer.name
        # Only create a new case if there isn't already an open case in this category
        insurance_outreach_cases = get_open_case(person, INSURANCE_OUTREACH)
        if not insurance_outreach_cases.exists():
            Case.objects.create(
                category=category,
                person=person,
                owner_group=billing_and_insurance_assignee_group,
                description=InsuranceReviewReasons.LAPSED.format(payer_name),
                due_date=convert_date_to_end_of_working_est(timezone.now()),
            )
    else:
        coverage_start = eligibility_response.coverage_start if eligibility_response else None
        coverage_end = eligibility_response.coverage_end if eligibility_response else None
        handle_invalid_insurance(person, coverage_start, coverage_end)


def close_outreach_insurance_case(person):
    """Close the review case for the billing team."""
    from firefly.modules.cases.utils import get_open_case

    insurance_outreach_cases = get_open_case(person, INSURANCE_OUTREACH)
    if insurance_outreach_cases.exists():
        # In case there are multiple open, close all of them
        for insurance_outreach_case in insurance_outreach_cases:
            insurance_outreach_case.action = CaseActions.AUTOCLOSE
            insurance_outreach_case.save(update_fields=["action", "status", "status_category"])
            logger.info("Completed insurance review case for patient %s", person.user.pk)


def handle_invalid_insurance(person, coverage_start, coverage_end, actor=None):
    from firefly.modules.programs.utils import remove_person_from_program
    from firefly.modules.tasks.collections import create_tasks_from_task_template

    # Update coverage status
    coverage_start = coverage_start
    coverage_end = coverage_end
    if coverage_start and coverage_start > date.today():
        # Plan is valid but not effective yet: move to "covered" status
        person.to_covered_insurance(actor=person.user if actor is None else actor)
        # Churn member
        remove_person_from_program(person, ProgramCodes.PRIMARY_CARE, data={"reason": "Churned: invalid insurance"})
    elif coverage_end and coverage_end < date.today():
        # Plan is expired: move to "expired" status
        person.to_incomplete_expired(actor=person.user)
        # Churn or discharge member
        _churn_or_discharge_member_with_invalid_insurance(person)
    else:
        # Trizetto check failed: move to "ineligible insurance" status
        person.to_ineligible_insurance(actor=person.user)
        # Churn or discharge member
        _churn_or_discharge_member_with_invalid_insurance(person)

    # Assign insurance task to member if one isn't already open
    if (
        person.user
        and not person.user.related_tasks.filter(
            autocreated_from__uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE, is_complete=False
        ).exists()
    ):
        task_template = TaskCollectionTask.objects.get(uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE)
        create_tasks_from_task_template(task_template, person.user)

    # Send email / push notification to patients
    if person.user:
        logger.info(
            "handle_invalid_insurance for patient %s",
            person.user.id,
        )
        recipient: list[BrazeRecipient] = [
            {
                "external_user_id": person.id,
                "send_to_existing_only": True,
            }
        ]
        reminders.send(settings.BRAZE["INSURANCE_EXPIRED"], recipient)


def _churn_or_discharge_member_with_invalid_insurance(person):
    """
    If the member is not established and has invalid insurance, we churn them immediately. If the member is established,
    we do nothing for now: we only initiate the discharge process for established members after 30 days of invalid
    insurance (during which time, they can still use the app as normal, but won't have active insurance and thus can't
    book appointments). This is handled by a cron job we run every day that checks if an established member has had
    invalid insurance for 30+ days and begins the discharge process if true.
    """
    from firefly.modules.programs.primary_care.utils import (
        initiate_discharge_for_established_member_with_invalid_insurance,
    )
    from firefly.modules.programs.utils import (
        is_current_enrollment_status,
        remove_person_from_program,
    )

    established = is_current_enrollment_status(person, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.ESTABLISHED)
    if not established:
        remove_person_from_program(person, ProgramCodes.PRIMARY_CARE, data={"reason": "Churned: invalid insurance"})
    else:
        initiate_discharge_for_established_member_with_invalid_insurance(person)


def create_appointment_insurance_outreach_case(person, appointment_date: Optional[str] = None):
    from firefly.modules.cases.models import Case, CaseCategory

    category = CaseCategory.objects.get(unique_key=INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY)
    appointment_insurance_outreach_cases = Case.objects.filter(category=category, person=person)
    payer_name = person.insurance_info.insurance_payer.name
    due_date = (
        datetime.strptime(appointment_date, "%m/%d/%Y %I:%M %p").date()
        if (person.insurance_info.insurance_payer.name == PAYER_BCBS_MA and appointment_date is not None)
        else date.today()
    )
    if should_create_new_case(
        cases=appointment_insurance_outreach_cases,
        grace_period_in_days=INSURANCE_VISIT_PREP_CATEGORY_GRACE_PERIOD,
    ):
        Case.objects.create(
            category=category,
            description=payer_name,
            person=person,
            due_date=convert_date_to_end_of_working_est(due_date),
        )


# should_create_new_case
# If cases exist and closed within grace_period it should not
# create new case
def should_create_new_case(cases: QuerySet[Case], grace_period_in_days: int) -> bool:
    from firefly.modules.cases.utils import get_latest_case

    if len(cases) > 0:
        latest_case = get_latest_case(cases)
        if (
            latest_case
            and latest_case.status_category in (StatusCategory.COMPLETE, StatusCategory.DEFERRED)
            and timezone.now() > (latest_case.updated_at + timedelta(days=grace_period_in_days))
        ):
            return True
    else:
        return True

    return False


def close_appointment_insurance_outreach_case(person):
    from firefly.modules.cases.models import CaseCategory
    from firefly.modules.cases.utils import auto_close_case

    category = CaseCategory.objects.get(unique_key=INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY)
    auto_close_case(category=category, person=person)


def perform_outreach_for_insurance_visit_prep_case(person):
    from firefly.modules.cases.utils import get_open_case

    appointment_insurance_outreach_cases = get_open_case(person, INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY)
    if appointment_insurance_outreach_cases.exists():
        appointment_insurance_outreach_case = appointment_insurance_outreach_cases[0]
        if appointment_insurance_outreach_case.status not in [
            CaseStatus.AUTOMATED_OUTREACH_1,
            CaseStatus.AUTOMATED_OUTREACH_2,
        ]:
            appointment_insurance_outreach_case.action = CaseStatus.AUTOMATED_OUTREACH_1
            appointment_insurance_outreach_case.save()
        elif appointment_insurance_outreach_case.status == CaseStatus.AUTOMATED_OUTREACH_1:
            appointment_insurance_outreach_case.action = CaseStatus.AUTOMATED_OUTREACH_2
            appointment_insurance_outreach_case.save()


def create_insurance_plan_review_case(person):
    """Create/update the outreach case for the billing team."""
    from firefly.modules.cases.models import Case, CaseCategory
    from firefly.modules.cases.utils import get_open_case

    category = CaseCategory.objects.get(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)
    # Only create a new case if there isn't already an open case in this category
    insurance_plan_needs_review_cases = get_open_case(person, INSURANCE_PLAN_NEEDS_REVIEW)
    if not insurance_plan_needs_review_cases.exists():
        Case.objects.create(
            category=category,
            person=person,
        )


def extract_insurance_info(person):
    try:
        logger.info(
            "[DEBUG_INSURANCE_TRANSISTION] entering extract_insurance_info for user %s",
            person.user.id,
        )
        if person.attribution and (person.attribution.contract is None):
            client = boto3.client("textract", region_name="us-east-2")
            image_name = person.insurance_info.image_front
            imageBytes = bytearray(image_name.read())
            response = client.analyze_document(
                Document={
                    "Bytes": imageBytes,
                },
                FeatureTypes=[
                    "QUERIES",
                ],
                QueriesConfig={
                    "Queries": [
                        {"Text": "Alphanumeric member id", "Alias": "Member_id"},
                        {"Text": "What is group number", "Alias": "Group_number"},
                    ]
                },
            )
            doc = trp.TDocumentSchema().load(response)
            page = doc.pages[0]
            # get_query_answers returns a list of [query, alias, answer]
            query_answers = doc.get_query_answers(page=page)
            update_fields: list = []
            for answer in query_answers:
                if answer[1] == "Group_number":
                    group_number = answer[2]
                    if group_number:
                        group_number = re.sub("[^A-Za-z0-9]+", "", group_number)
                        person.insurance_info.group_number_textract = group_number
                        person.insurance_info.group_number = group_number
                    update_fields.append("group_number_textract")
                    update_fields.append("group_number")
            person.insurance_info.save(update_fields=list(set(update_fields)))
            logger.info(
                "[DEBUG_INSURANCE_TRANSISTION] existing extract_insurance_info for user %s",
                person.user.id,
            )

    except Exception:
        logger.exception("Exception occurred while processing document using textract for %d", person.pk)


def create_case_for_new_insurance_plan(plan, person):
    from firefly.modules.cases.models import Case, CaseCategory, CaseRelation

    category = CaseCategory.objects.get(unique_key=NEW_INSURANCE_PLAN_CASE_CATEGORY)
    cases_exist = (
        Case.objects.filter(
            category=category,
        )
        .exclude(status_category__in=WORKUNIT_COMPLETION_STATUSES)
        .exists()
    )
    if cases_exist:
        logger.info(
            "[InsurancePlanNetworks] Case for unmapped insurance plan already exists for insurance plan %s", plan.pk
        )
        return
    case = Case.objects.create(person=person, category=category, description=category.description)
    CaseRelation.objects.create(
        case=case,
        content_type=ContentType.objects.get_for_model(InsurancePlan),
        object_id=plan.pk,
    )
    logger.info(
        "[InsurancePlanNetworks] Created case %s for unmapped created insurance plan %s",
        case.id,
        plan.pk,
    )


def close_cases_for_insurance_plans_with_network(plan):
    from firefly.modules.cases.models import CaseRelation
    from firefly.modules.cases.utils import auto_close_case

    case_relations: CaseRelation = CaseRelation.objects.filter(
        content_type=ContentType.objects.get_for_model(InsurancePlan),
        object_id=plan.pk,
    )

    for case_relation in case_relations:
        case = case_relation.case
        auto_close_case(id=case.id)
        logger.info(
            "[InsurancePlanNetworks] Closed unmapped insurance plan case %s for insurance plan %s",
            case.id,
            plan.pk,
        )


def is_insurance_expired(insurance_member_info: InsuranceMemberInfo):
    from firefly.modules.eligibility.utils import is_pending_or_active_coverage

    if insurance_member_info.coverage_end is not None and not is_pending_or_active_coverage(
        datetime.strftime(insurance_member_info.coverage_end, "%Y-%m-%d")
    ):
        return True
    return False


def is_hmo_user_without_firefly_pcp(insurance_member_info: InsuranceMemberInfo):
    # Medicare HMO patients do not require Firefly as their PCP
    if (
        insurance_member_info.plan_type == InsuranceMemberInfo.PLAN_TYPE_HMO
        and (
            insurance_member_info.insurance_payer
            and not insurance_member_info.insurance_payer.elation_payer_code == ELATION_PAYER_CODES["Medicare"]
        )
        and not insurance_member_info.is_firefly_pcp
    ):
        return True
    return False


def is_insurance_active_for_appointment(insurance_member_info: InsuranceMemberInfo):
    if not is_insurance_expired(insurance_member_info) and (
        (
            insurance_member_info.plan_type == InsuranceMemberInfo.PLAN_TYPE_HMO
            and (
                insurance_member_info.is_firefly_pcp
                or (
                    insurance_member_info.insurance_payer
                    and insurance_member_info.insurance_payer.elation_payer_code == ELATION_PAYER_CODES["Medicare"]
                )
            )
        )
        or insurance_member_info.plan_type == InsuranceMemberInfo.PLAN_TYPE_PPO
    ):
        return True
    return False


def remove_people_in_terminal_states(user_ids):
    from firefly.core.user.models import Person

    now = datetime.now(tz=UTC_TIMEZONE)
    primary_care_program = Program.objects.get(uid=ProgramCodes.PRIMARY_CARE)
    people_in_active_status = Person.objects.filter(
        user_id__in=user_ids,
    ).exclude(
        Q(
            program_enrollments__program=primary_care_program,
            program_enrollments__deleted__isnull=True,
            program_enrollments__period__startswith__lte=now,
            program_enrollments__status=PrimaryCareProgramStatus.CHURNED,
            program_enrollments__reason__contains="Discharged:",
            program_enrollments__period__endswith__isnull=True,
        )
        | Q(user__onboarding_state__status=OnboardingStatus.DEACTIVATED)
    )
    return people_in_active_status
