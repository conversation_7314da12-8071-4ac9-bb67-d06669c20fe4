import contextlib
from datetime import datetime

from django.db import connection


class freeze_database_time_context(contextlib.ContextDecorator):
    """
    A context manager that overrides the now function. Intended to be used to override
    created_at and updated_at fields in tests. The overridden now function is housed in
    a separate schema. At the start of the context manager - the search path is updated
    to use the new schema. When the context manager exits - the schema is removed from
    the search path - so that the normal `now` function is back in effect.
    Usage:

        with freeze_database_time_context(freeze_time=...):
            ... create or update instances
            # any instances that are created or updated
            # will use the value fedined in freeze_time
    """

    def __init__(self, freeze_time: datetime):
        self.freeze_time = freeze_time

    def __enter__(self):
        with connection.cursor() as cursor:
            cursor.execute("CREATE SCHEMA if not exists override;")
            cursor.execute(
                f"""
                CREATE OR REPLACE FUNCTION override.now()
                RETURNS timestamptz IMMUTABLE PARALLEL SAFE AS
                $$
                BEGIN
                    return '{self.freeze_time.strftime("%Y-%m-%dT%H:%M:%SZ")}'::timestamptz;
                END
                $$ language plpgsql;
            """  # noqa: E501
            )
            cursor.execute("set search_path = override,pg_temp,public,pg_catalog;")

    def __exit__(self, *exc):
        with connection.cursor() as cursor:
            cursor.execute("set search_path = pg_temp,public,pg_catalog;")
