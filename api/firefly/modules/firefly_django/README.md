# Firefly Django

## Motivation

The firefly_django module provides enhanced Django framework capabilities specifically designed for healthcare applications requiring comprehensive audit logging, data integrity, and PHI (Protected Health Information) compliance. It extends Django's base functionality with enterprise-grade features for tracking, security, and data management.

## Business Context

This module serves as the foundation for all Firefly Django applications, providing:
- HIPAA-compliant audit logging for all data changes
- Comprehensive change tracking with user attribution
- Soft delete capabilities to maintain data integrity
- Enhanced serialization with security controls
- Administrative command framework with proper context tracking
- Integration with external monitoring and alerting systems

## Core Concepts

### BaseModelV3 Architecture
- **Audit Logging**: Automatic change tracking using PostgreSQL triggers
- **Soft Deletes**: Safe deletion with cascade handling and recovery capabilities
- **User Attribution**: Mandatory user association for all data changes
- **Timestamp Management**: Database-level timestamp handling for accuracy
- **Event Tracking**: Integration with request context and background tasks

### Key Features
1. **PHI Audit Compliance**: Complete change history with user attribution
2. **Soft Delete System**: Reversible deletions with relationship preservation
3. **Enhanced Serialization**: Security-focused API serialization
4. **Command Framework**: Structured admin commands with proper context
5. **History Integration**: Seamless integration with Django admin interface

## Technical Implementation

### BaseModelV3 Features

**Comprehensive Audit Logging**:
- Automatic history capture using django-pghistory and django-pgtrigger
- PostgreSQL trigger-based change tracking (compatible with all save methods)
- User attribution enforcement through database constraints
- Request context integration for web requests, commands, and background tasks

**Soft Delete Capabilities**:
- Safe deletion using django-safedelete package
- Cascade soft deletion for related objects
- Multiple manager access: `all`, `all_with_deleted`, `deleted_only`
- Configurable deletion policies and recovery mechanisms

**Enhanced Security**:
- Non-overrideable fields in serializers for data protection
- Mandatory user context for all operations
- Integration with authentication and authorization systems
- PHI-compliant data handling and access controls


### Context Management

**Request Context Integration**:
- Automatic user attribution for web requests via pghistory middleware
- Request URL tracking for audit trail completeness
- Session and authentication context preservation

**Background Task Context**:
- Dramatiq middleware for task context tracking
- User attribution for background operations
- Task metadata integration with audit logs

**Command Context**:
- FireflyBaseCommand framework for admin operations
- Structured context management for batch operations
- User attribution for command-line activities

### Admin Integration

**BaseModelV3AdminMixin**: Enhanced Django admin interface
- Integrated history viewing with event metadata
- User attribution display and filtering
- Soft delete management and recovery tools
- PHI-compliant data access controls

## Configuration

### Required Settings

**Middleware Configuration**:
```python
MIDDLEWARE = [
    'pghistory.middleware.HistoryMiddleware',
    # ... other middleware
]
```

**Context Processors**: Enable proper user attribution
- Web request context via middleware
- Background task context via Dramatiq middleware
- Command context via FireflyBaseCommand

### Best Practices

**User Attribution**: Always provide user context
- Web requests: Automatic via middleware
- Background tasks: Pass user in task parameters
- Commands: Use FireflyBaseCommand framework

**Soft Delete Handling**: Understand manager behavior
- Use appropriate manager for query needs
- Consider soft-deleted relationships in business logic