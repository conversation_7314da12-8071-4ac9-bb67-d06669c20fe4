"""
Dramatiq's retry middleware does not account for different rate limits for
rate limit exceptions vs non-rate-limit exceptions. Rate limit exceptions occur
very frequently and need to be retried a higher number of times.

Since they are not distinguished from non-rate-limit exceptions - two scenarios can occur
- actors have a high retry limit set to account for rate limit exceptions. This can result in
  a non rate-limit exception being retried a very high number of times - which can be problematic
  especially if these actors deal with outbound systems.
- actors keep a low retry limit. This can result in messages falling on the DLQ when they exhaust the
  retries while acquiring the lock (for the rate limiter)

To account for this, Firefly defines two new middlewares
- RateLimitExceptionRetries: Responsible for retries only when a rate limit has been exceeded
- RetriableExceptionRetries: Responsible for retries when a retriable error other than rate limit violations

In due course, all dramatiq actors will be updated to use the new middlewares and the default Retries
middleware will be deactivated. However, during the transition period the Retry middleware needs to be
deactivated for actors that already use the retry functionality from either `RateLimitExceptionRetries`
or `RetriableExceptionRetries`
"""

import logging
from typing import Optional

from dramatiq.middleware import Retries

logger = logging.getLogger(__name__)

orig = Retries.after_process_message


def after_process_message(self, broker, message, *args, result=None, exception=None, **kwargs):
    actor = broker.get_actor(message.actor_name)
    max_retries_for_rate_limit_exceptions: Optional[int] = actor.options.get("max_retries_for_rate_limit_exceptions")
    max_retries_for_retriable_exceptions: Optional[int] = actor.options.get("max_retries_for_retriable_exceptions")
    if (max_retries_for_rate_limit_exceptions is not None and type(self) is Retries) or (
        max_retries_for_retriable_exceptions is not None and type(self) is Retries
    ):
        logger.info("Skipping dramatiq retry middleware since other retry middlewares are active")
        return
    else:
        return orig(self, broker, message, *args, result=result, exception=exception, **kwargs)


setattr(Retries, "after_process_message", after_process_message)
