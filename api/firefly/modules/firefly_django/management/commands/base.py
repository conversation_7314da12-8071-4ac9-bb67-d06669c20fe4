import abc
import logging
import os
import re
import time
from getpass import getpass
from typing import List, Optional, Union

import pghistory
from django.contrib.auth import authenticate
from django.contrib.auth.base_user import AbstractBaseUser
from django.core.management.base import (
    # This is the one place we need to import BaseCommand in order to extend it.
    BaseCommand,  # noqa: TID251
    CommandError,
    CommandParser,
)
from django.db import connection
from django.http.request import HttpRequest
from django.utils import timezone
from typing_extensions import final

from firefly.core.user.models.models import User
from firefly.modules.firefly_django.fireflyadmincommand.models import DRY_RUN_OFF_FLAG, CommandLastRun
from firefly.modules.firefly_django.utils import get_lucian_bot_user

logger = logging.getLogger(__name__)


def superuser_only(fn):
    """
    Decorator to restrict non-superusers from running an admin command
    """

    def wrapper(*args, **kwargs):
        user = kwargs.get("user")
        if not user.is_superuser:
            raise PermissionError("PermissionError: Only superusers can run this command!")
        return fn(*args, **kwargs)

    return wrapper


class FireflyBaseCommand(BaseCommand, metaclass=abc.ABCMeta):
    def create_parser(self, prog_name, subcommand, **kwargs):
        parser: CommandParser = super().create_parser(prog_name, subcommand, **kwargs)
        self.subcommand_name = subcommand
        self.command_name = f"{prog_name} : {self.subcommand_name}"
        self.log_prefix = f"FireflyBaseCommand: {self.command_name}: "
        logger.info("%s Creating command", self.log_prefix)
        # For command executions from the command line
        #   A phone number is required
        #   Credentials for the account are prompted before the command can be executed
        # For command executions from the admin screen
        #   A user object must be passed in
        # For command executions from migrations
        #   A user object must be passed in
        # For command executions from cron
        #   User object defaults to the luci bot user

        # Command line executions
        parser.add_argument(
            "--user-phone-number",
            type=str,
            required=False,
            help="Phone number of admin account for login",
        )
        # Admin screen invocation/ Migration/ Cron
        parser.add_argument(
            "--user",
            type=User,
            required=False,  # crons do not pass in a user
            help="User invoking the script",
        )
        parser.add_argument(
            f"--{DRY_RUN_OFF_FLAG}",
            action="store_true",
            help="If true, will write to database",
            required=False,
        )
        return parser

    # Subclasses need to define a run method
    # instead of the handle method.
    # This method is then executed in the context of the
    # user(sent as a required argument)
    # Attempting to define commands without this function
    # will result in a mypy error
    @abc.abstractmethod
    def run(self, *args, **options):
        raise NotImplementedError

    def print_db_change_summary(self, summary_dict):
        """
        Uses the output of RecordDatabaseWritesContext
        to print a summary of the DB inserts made during a command's execution
        """

        try:
            logger.info("%s\n-----\nDB change summary:", self.log_prefix)
            table_count = 0
            for table_name_key in summary_dict:
                table_count += 1
                inserts = summary_dict[table_name_key].get(RecordDatabaseWritesContext.OperationNames.INSERT)
                updates = summary_dict[table_name_key].get(RecordDatabaseWritesContext.OperationNames.UPDATE)
                deletes = summary_dict[table_name_key].get(RecordDatabaseWritesContext.OperationNames.DELETE)
                if inserts is not None:
                    logger.info(
                        "%s Table %s INSERTs: %s",
                        self.log_prefix,
                        table_name_key,
                        inserts,
                    )
                if updates is not None:
                    logger.info(
                        "%s Table %s UPDATEs: %s",
                        self.log_prefix,
                        table_name_key,
                        updates,
                    )
                if deletes is not None:
                    logger.info(
                        "%s Table %s DELETEs: True",
                        self.log_prefix,
                        table_name_key,
                    )

            logger.info("%s %d tables modified\n-----", self.log_prefix, table_count)
        except Exception:
            logger.exception("Failed to generate DB change summary")

    # Attempts to define commands with their own handle
    # implementation will result in a mypy error
    @final
    def handle(self, *args, **options):
        user: Optional[AbstractBaseUser] = None
        # Command line invocation
        if getattr(self, "_called_from_command_line", None) is True:
            phone_number = options.pop("user_phone_number", None)
            if phone_number is not None:
                logger.info("%s Invoked from commandline. user_phone_number is set.", self.log_prefix)
                password = getpass(prompt="Password: ", stream=None)
                user = authenticate(
                    request=HttpRequest(),
                    phone_number=phone_number,
                    password=password,
                )
            else:
                logger.info(
                    "%s Invoked from commandline. user_phone_number is not set.Possible cron invocation.",
                    self.log_prefix,
                )
        if user is None:
            # Admin screen/ migrations / code
            user = options.pop("user", None)
            # Cron invocations
            # It is possible that the next block of code gets invoked for
            # invocations from the admin screen. Since the admin console and
            # cron run within the same container, the SYNC_CRON env var is set
            # for both invocation paths

            # TODO: Make user mandatory from admin screens
            if user is None:
                # This could be either from a cron / an admin command
                if os.environ.get("SYNC_CRON") == "1":
                    logger.info(
                        "%s Possibility of a cron invocation. Setting lucian_bot user",
                        self.log_prefix,
                    )
                    user = get_lucian_bot_user()

                else:
                    logger.info("%s Not a cron invocation. User is not set. Exiting", self.log_prefix)

        # All invocation paths should now have a user associated with them
        if user is None:
            raise CommandError("Commands cannot be executed without a valid user")
        else:
            logger.info("%s Invoked by %s", self.log_prefix, user)
        options["user"] = user
        with pghistory.context(user=user.pk, command=self.command_name) as context:
            self.context = context
            with RecordDatabaseWritesContext(self.print_db_change_summary):
                start_time = time.perf_counter()
                CommandLastRun.objects.update_or_create(
                    command_name=self.subcommand_name,
                    defaults={
                        "last_run_at": timezone.now(),
                    },
                )
                self.run(*args, **options)
                logger.info("%s Completed in: %f.", self.log_prefix, time.perf_counter() - start_time)


class RecordDatabaseWritesContext(object):
    """
    Provides a context which records a dict containing a summary of
    the DB tables to which the inner block performs INSERT/UPDATE/DELETE operations
    as well as which columns on that table were changed
    Pass a callback function to operate on this dict
    """

    class OperationNames:
        INSERT = "INSERT"
        UPDATE = "UPDATE"
        DELETE = "DELETE"

    # All sql statements run inside a context. For tracking context
    # select set_config(....) is used which appears at the beginning of
    # the sql statement. Ignoring the first sql that is used for tracking context.
    REGEX_MATCHES = {
        OperationNames.INSERT: r"(.*); INSERT INTO \"(\w+)\" \(([^\)]*)\) ",
        OperationNames.UPDATE: r"(.*); UPDATE \"(\w+)\" SET (.*) WHERE",
        OperationNames.DELETE: r"(.*); DELETE FROM \"(\w+)\"",
    }

    def __init__(self, callback_fn):
        self.callback_fn = callback_fn
        # database_writes_summary looks like:
        # {"table_name": {"operation_name": ("changed_field_1", "changed_field_2")}}
        self.database_writes_summary = {}
        self._pre_execute_hook = None
        self.context: Union[dict, None] = None

    def __enter__(self):
        self._pre_execute_hook = connection.execute_wrapper(self._save_queries)
        self._pre_execute_hook.__enter__()

    def __exit__(self, *args, **kwargs):
        self.callback_fn(self.database_writes_summary)
        self._pre_execute_hook.__exit__(*args, **kwargs)

    def _save_queries(self, execute, sql, params, many, context):
        insert_match = re.match(self.REGEX_MATCHES[self.OperationNames.INSERT], sql)
        update_match = re.match(self.REGEX_MATCHES[self.OperationNames.UPDATE], sql)
        delete_match = re.match(self.REGEX_MATCHES[self.OperationNames.DELETE], sql)

        if insert_match:
            self._record_insert(insert_match)
        elif update_match:
            self._record_update(update_match)
        elif delete_match:
            self._record_delete(delete_match)

        result = execute(sql, params, many, context)
        return result

    def _record_table_change(self, table_name):
        """
        Init database_writes_summary[table_name] as an empty dict to keep track of operations
        """
        self.database_writes_summary[table_name] = self.database_writes_summary.get(table_name) or {}

    def _record_operation(self, table_name: str, operation_name: str, column_list: List[str]):
        """
        Set database_writes_summary[table_name][operation_name] as a set of column names
        """
        self._record_table_change(table_name)
        self.database_writes_summary[table_name][operation_name] = (
            self.database_writes_summary[table_name].get(operation_name) or set()
        )
        self.database_writes_summary[table_name][operation_name] = self.database_writes_summary[table_name][
            operation_name
        ].union(column_list)

    def _record_insert(self, regex_match):
        table_name = regex_match.group(2)
        column_list = regex_match.group(3).split(", ")
        column_list = map(lambda name: name.replace('"', ""), column_list)
        column_list = set(list(column_list))
        self._record_operation(table_name, self.OperationNames.INSERT, column_list)

    def _record_update(self, regex_match):
        # e.g. 'UPDATE "auth_user" SET "disabled" = %s, "other_field" = %s WHERE ...'
        table_name = regex_match.group(2)
        column_list = regex_match.group(3).split(", ")
        # ['"disabled" = %s', '"other_field" = %s']
        sanitized_column_list = []
        for col in column_list:
            # The update regex expects each update field to be separated by a comma
            # This works for most cases except for subqueries where multiple order by
            # fields could be in play - separated by a comma
            if re.match(r"\"(\w*)\"", col) is not None:
                sanitized_column_list.append(re.match(r"\"(\w*)\"", col).group(0).replace('"', ""))
        self._record_operation(table_name, self.OperationNames.UPDATE, sanitized_column_list)

    def _record_delete(self, regex_match):
        table_name = regex_match.group(2)
        column_list = set()  # We don't care about columns when we delete a row
        self._record_operation(table_name, self.OperationNames.DELETE, column_list)
