import logging
from datetime import datetime
from typing import Any, ClassVar, List, Optional

import pghistory
import pgtrigger
from django.conf import settings
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import QuerySet
from django.db.models.fields.related_descriptors import ManyToManyDescriptor
from django.utils.functional import cached_property
from model_utils.managers import InheritanceManagerMixin, InheritanceQuerySet
from safedelete.config import SOFT_DELETE_CASCADE
from safedelete.managers import SafeDeleteAllManager, SafeDeleteDeletedManager, SafeDeleteManager
from safedelete.models import SafeDeleteModel
from safedelete.queryset import SafeDeleteQueryset
from typing_extensions import Self

from firefly.modules.firefly_django.bulk_update_or_create import BulkUpdateOrCreateMixin

logger = logging.getLogger(__name__)


def does_model_use_multi_table_inheritance(cls) -> bool:
    # We change the behavior of our audit logging framework
    # based on the output of this function. One examples would be:
    #   - Child tables in multi table inheritance do not have audit fields.
    #     The trigger definition is updated to just verify that changes are
    #     not attempted without a user in the context. We skip attempting to
    #     update the audit fields for child tables.
    # Add an allow list out of an abundance of caution
    # New models that use multi table inheritance will cause server startup failure
    # And test failure : till they are added in this list
    models_allowed_to_use_multi_table_inheritance: List[str] = [
        "TestModelForMultiTableInheritanceChildOne",
        "TestModelForMultiTableInheritanceChildTwo",
        "AdhdAddProgramInfo",
        "BenefitProgramInfo",
        "ChronicOpioidUseProgramInfo",
        "DepressionAndAnxietyProgramInfo",
        "DiabetesProgramInfo",
        "HypertensionProgramInfo",
        "PrimaryCareProgramInfo",
        "RTWProgramInfo",
        "UrgentCareProgramInfo",
        "WeightLossProgramInfo",
    ]
    if len(cls._meta.get_parent_list()) > 0 and cls.__name__ in models_allowed_to_use_multi_table_inheritance:
        return True
    return False


def generate_audit_trigger_for_model(cls) -> str:
    # Define a trigger that runs before every change
    # Trigger should
    #   - disallow any changes without a user set in the context
    #   - add values in the timestamp audit fields (created_at/ updated_at) based on the current time
    #   - add values in the user audit fields (created_by/ updated_by) based on the user available in the context
    #   - disallow changes to the created_at and created_by fields after the initial creation
    db_trigger_for_model_parts: List[str] = []
    db_trigger_for_model_parts.append(
        """
        IF (nullif(CURRENT_SETTING('pghistory.context_metadata'), '') IS NULL) THEN
            RAISE EXCEPTION 'context is required for any changes to the model';
        END IF;
        IF (CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user' IS NULL) THEN
            RAISE EXCEPTION 'user key in context is required for any changes to the model';
        END IF;
    """
    )
    if does_model_use_multi_table_inheritance(cls) is False:
        db_trigger_for_model_parts.append(
            """
            IF (TG_OP='INSERT') THEN
                NEW.created_by_id = coalesce(
                    nullif(CURRENT_SETTING('pghistory.context_metadata', true), ''),
                    '{}'
                )::json->>'user';
                NEW.created_at = now();
            ELSE
                IF OLD.created_by_id IS NOT NULL THEN
                    NEW.created_by_id = OLD.created_by_id;
                END IF;
                IF OLD.created_at IS NOT NULL THEN
                    NEW.created_at = OLD.created_at;
                END IF;
            END IF;
            NEW.updated_by_id = CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user';
            NEW.updated_at = now();
        """
        )
    db_trigger_for_model_parts.append("RETURN NEW;")
    return "".join(db_trigger_for_model_parts)


def register_triggers(cls, is_audit_history_required: Optional[bool]):
    # Ensure that all updates to the model happen with a context set. The
    # context should contain metadata about the user invoking the action.
    #
    # For web requests, the pghistory middleware adds metadata about the url
    # that was invoked and the user that invoked it.
    #
    # For asynchronous Dramatiq tasks, a Dramatiq middleware adds metadata about
    # the task name, and sets the user to the Lucian Bot user.
    #
    # For direct model updates, the context needs to be set for each invocation.

    # logger.info(f"Registering postgres triggers for {cls}")

    # Uppercase letters break trigger creation, assert that they are lowercase
    assert cls._meta.db_table.islower(), "Model's Meta.db_table value must be lowercase"

    pgtrigger.register(
        pgtrigger.Trigger(
            # Max length of 43 chars, truncate model name, PGTRIGGER adds a unique hash for dups
            name=f"aud_{cls.__name__.lower()[:39]}",
            operation=pgtrigger.Update | pgtrigger.Insert,
            when=pgtrigger.Before,
            func=generate_audit_trigger_for_model(cls),
        )
    )(cls)

    # Don't log the audit history if the is_audit_history_required is False
    if is_audit_history_required is False:
        return cls

    related_name: str = "pgh_events"
    history_fields: Optional[List[str]] = None
    # Support for multi table inheritance
    # If a table uses multi table inheritance, we need to override three things:
    #   - related_name: Without the override: all the event tables from the child table would clash
    #   - fields to track history on: Without the override: pghistory attempts to track fields from the parent
    #   - model_name: When fields are overridden, pghistory attempts to create the model name based on the fields
    #     passed in
    if does_model_use_multi_table_inheritance(cls) is True:
        # To prevent a clash, append the child class name
        related_name = f"pgh_events_{cls.__name__.lower()}"
        # Remove all fields from the parent
        # These fields are not present on the child table in the database
        all_fields = [f.name for f in cls._meta.fields]
        inherited_fields = []
        for parent in cls._meta.get_parent_list():
            inherited_fields.extend([f.name for f in parent._meta.fields])
        non_inherited_fields = [field for field in all_fields if field not in inherited_fields]
        history_fields = non_inherited_fields
    pghistory.track(
        obj_field=pghistory.ObjForeignKey(
            related_name=related_name,
        ),
        fields=history_fields,
        model_name=f"{cls._meta.object_name}Event",
    )(cls)
    return cls


class BaseModelV3Type(models.base.ModelBase):
    def __init__(cls, name, bases, clsdict):
        # Check if the model needs to log the audit history
        is_audit_history_required: bool = True
        for name, value in clsdict.items():
            if name == "IS_AUDIT_HISTORY_REQUIRED":
                is_audit_history_required = value

        # Don't register the BaseModel or any abstract intermediate class
        if name != "BaseModelV3" and not cls._meta.abstract:
            register_triggers(cls, is_audit_history_required)
        super(BaseModelV3Type, cls).__init__(name, bases, clsdict)


class SafeDeleteManagerWithBulkUpdate(SafeDeleteManager, BulkUpdateOrCreateMixin):
    pass


class SafeDeleteAllManagerWithBulkUpdate(SafeDeleteAllManager, BulkUpdateOrCreateMixin):
    pass


class SafeDeleteDeletedManagerWithBulkUpdate(SafeDeleteDeletedManager, BulkUpdateOrCreateMixin):
    pass


# Being added temporarily to aid with m2m audit adoption
class OptionalAuditTimeStamps(models.Model):
    created_at = models.DateTimeField(
        null=True,
        blank=True,
        db_index=True,
    )
    updated_at = models.DateTimeField(
        null=True,
        blank=True,
        db_index=True,
    )

    class Meta:
        abstract = True
        db_table: Optional[str] = None


# Being added temporarily to aid with m2m audit adoption
class RequiredAuditTimeStamps(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        abstract = True
        db_table: Optional[str] = None


class BaseModelV3(SafeDeleteModel, metaclass=BaseModelV3Type):
    # On delete - soft delete self and any related entities
    _safedelete_policy = SOFT_DELETE_CASCADE
    # Define types for fields added at runtime via django-safedelete
    deleted_by_cascade: Optional[bool] = None
    deleted: Optional[datetime]

    # To check if the model requires to log the audit history
    # Override and set this to False for skipping the audit history
    IS_AUDIT_HISTORY_REQUIRED: Optional[bool] = True

    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # Override the deleted field to skip the migration of db_index=True, which was added in a newer version
    # of SafeDeleteModel
    deleted = models.DateTimeField(editable=False, null=True)  # type: ignore[assignment]

    # Note: The typing of these class variables appears to behave correctly,
    # however mypy still emits errors of the form:
    #
    #   error: Incompatible types in assignment (expression has type
    #   "SafeDeleteManagerWithBulkUpdate[BaseModelV3]", variable has type
    #   "SafeDeleteManagerWithBulkUpdate[Self]")  [assignment]
    #
    # Since I can't figure this out, I'm resorting to ignoring it.
    objects: ClassVar[SafeDeleteManagerWithBulkUpdate[Self]] = SafeDeleteManagerWithBulkUpdate()
    all_objects: ClassVar[SafeDeleteAllManagerWithBulkUpdate[Self]] = SafeDeleteAllManagerWithBulkUpdate()
    deleted_objects: ClassVar[SafeDeleteDeletedManagerWithBulkUpdate[Self]] = SafeDeleteDeletedManagerWithBulkUpdate()
    pgh_events: QuerySet[Any]  # define relation to history events model since the relation gets added dynamically

    # This flag tells us if a change to the model was initiated by Elation
    # (in which case we should not turn around and tell Elation about the model change, after saving)
    # Set this to True to skip an update
    _elation_update = False

    # Setting this flag to True will reject any deletion attempts from Elation
    disallow_deletion_from_elation = False

    # This flag tells us if the model's Elation connection should be async
    # (via Change Data Capture) instead of sync
    @property
    def async_elation_updates(self):
        return False

    @property
    def elation_update(self):
        return self._elation_update

    @elation_update.setter
    def elation_update(self, value):
        self._elation_update = value

    class Meta:
        abstract = True
        db_table: Optional[str] = None


class BaseModelV3ManyToManyField(models.ManyToManyField):
    # See https://github.com/makinacorpus/django-safedelete/blob/4e9e391819929678f0cdf8bc761ca48b6e774b50/safedelete/fields.py
    """ManyToMany field that should be used with softdeletable through model
    By default, descriptor of M2M field return all objects of related model,
    filtered just by instance.
    This field adds the special descriptor ``SafeDeleteManyToManyDescriptor`` so
    that deleted relations are automatically filtered
    """

    def contribute_to_class(self, cls, name, **kwargs):
        """Add custom descriptor to source model"""
        super().contribute_to_class(cls, name, **kwargs)
        setattr(cls, self.name, SafeDeleteManyToManyDescriptor(self.remote_field, reverse=False))

    def contribute_to_related_class(self, cls, related):
        """Add custom descriptor to related model"""
        super().contribute_to_related_class(cls, related)
        # See https://github.com/django/django/blame/main/django/db/models/fields/related.py#L1920-L1931
        if not (self.remote_field.is_hidden() and not related.related_model._meta.swapped):
            setattr(cls, related.get_accessor_name(), SafeDeleteManyToManyDescriptor(self.remote_field, reverse=True))


class SafeDeleteManyToManyDescriptor(ManyToManyDescriptor):
    """Custom descriptor that just change ``related_manager_cls``.
    See docstring of ``BaseModelV3ManyToManyField``
    """

    @cached_property
    def related_manager_cls(self):
        """Patch default relateed manager with custom filter"""
        cls = super().related_manager_cls

        class SafeDeleteRelatedManager(cls):
            """Related manager with custom filtration for soft-delete"""

            def __init__(self, instance=None):
                super().__init__(instance)
                field_name = self.target_field.related_query_name()
                filter_key = "{}__deleted__isnull".format(field_name)
                self.core_filters[filter_key] = True

        return SafeDeleteRelatedManager


class TestModel(BaseModelV3):
    id = models.AutoField(primary_key=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    foo = models.CharField(db_index=True, max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bar = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    baz = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    readonly = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251

    __test__ = False

    class Meta(BaseModelV3.Meta):
        abstract = False
        db_table = "testmodel"


# Define querysets and object managers that can be used with multi table
# inheritance
# Since we have limited adoption - these managers should be explicitly assigned
# when we use multi table inheritance when we are interested in the features provided
# by inheritance managers.
class InheritanceWithSafeDeleteQuerySet(SafeDeleteQueryset, InheritanceQuerySet):
    pass


class InheritanceManagerWithSafeDeleteAndBulkUpdate(
    SafeDeleteManager, BulkUpdateOrCreateMixin, InheritanceManagerMixin
):
    _queryset_class = InheritanceWithSafeDeleteQuerySet

    def _add_hints(self, *args, **kwargs):
        pass


class InheritanceAllManagerWithSafeDeleteAndBulkUpdate(
    SafeDeleteAllManager, BulkUpdateOrCreateMixin, InheritanceManagerMixin
):
    _queryset_class = InheritanceWithSafeDeleteQuerySet


class InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate(
    SafeDeleteDeletedManager, BulkUpdateOrCreateMixin, InheritanceManagerMixin
):
    _queryset_class = InheritanceWithSafeDeleteQuerySet


# Multi table inheritance models can have overridden querysets and managers as well
# Create dedicated models to test the functionality independent of querysets and managers
# defined above
class TestModelForMultiTableInheritanceParent(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    foo = models.CharField(db_index=True, max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    bar = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    __test__ = False

    # Note: The typing of these class variables appears to behave correctly,
    # however mypy still emits errors of the form:
    #
    #   error: Incompatible types in assignment (expression has type
    #       "InheritanceManagerWithSafeDeleteAndBulkUpdate[Self]", base class "BaseModelV3"
    #       defined the type as "SafeDeleteManagerWithBulkUpdate[TestModelForMultiTableInheritanceParent
    #       ]")
    #
    # As BaseModelV3 have different type and inherit multi table model class different type,
    # I am ignoring type same as BaseModelV3
    objects: ClassVar[InheritanceManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceManagerWithSafeDeleteAndBulkUpdate()  # type: ignore[assignment]
    )
    all_objects: ClassVar[InheritanceAllManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceAllManagerWithSafeDeleteAndBulkUpdate()  # type: ignore[assignment]
    )
    deleted_objects: ClassVar[InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate[Self]] = (
        InheritanceDeletedManagerWithSafeDeleteAndBulkUpdate()  # type: ignore[assignment]
    )


class TestModelForMultiTableInheritanceChildOne(TestModelForMultiTableInheritanceParent):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    child_property = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251


class TestModelForMultiTableInheritanceChildTwo(TestModelForMultiTableInheritanceParent):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    another_child_property = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251


class TestModelGroup(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    members = BaseModelV3ManyToManyField(
        TestModel,
        through="TestModelGroupMembership",
    )


class TestModelGroupMembership(BaseModelV3):
    test_model = models.ForeignKey(TestModel, on_delete=models.CASCADE)
    group = models.ForeignKey(TestModelGroup, on_delete=models.CASCADE)


class TestModelWithoutAuditHistory(BaseModelV3):
    # It overrides the BaseModelV3 property to ensure we are not logging model audit history
    IS_AUDIT_HISTORY_REQUIRED = False

    # A testing attribute
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251


class GroupObjectPermission(BaseModelV3):
    """
    This model enables row-level access control by linking Django's built-in
    Group and Permission models to specific objects in the system.

    It allows fine-grained permission checks: instead of global access,
    users in a group can be restricted to only certain objects (e.g., specific
    Connections for file downloads).

    Example:
        - Group: 'Network Coordinator'
        - Permission: 'can_view_connection'
        - Object: A specific 'Connection' instance

    This setup ensures that users only see data relevant to their group.
    """

    group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name="group_object_permissions",
        help_text="The Django group to which this permission applies",
    )
    permission = models.ForeignKey(
        Permission, on_delete=models.CASCADE, help_text="The specific permission that grants access to the object"
    )

    content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE, help_text="The type of the object (model) this permission applies to"
    )
    object_id = models.PositiveIntegerField(help_text="The ID of the specific object this permission applies to")
    content_object = GenericForeignKey("content_type", "object_id")

    class Meta:
        unique_together = ("group", "permission", "content_type", "object_id")
