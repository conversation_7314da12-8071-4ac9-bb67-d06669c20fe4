import logging
from typing import List, Optional

from rest_framework import serializers
from typing_extensions import Final

from .models import BaseModelV3, TestModel, does_model_use_multi_table_inheritance

logger = logging.getLogger(__name__)


def get_ref_name_for_model_serializer(
    model_serializer_instance,
) -> Optional[str]:
    # Taken from https://github.com/axnsan12/drf-yasg/blob/master/src/drf_yasg/utils.py#L416-L434
    # Deviations from drf_yasg logic
    #   ref_name is of the format `applabel_serializername` instead of `serializername` to maintain
    #       uniqueness across modules
    #   over-rides of ref_name are not respected
    #   Safeguards against use with non-model serializers
    assert isinstance(model_serializer_instance, serializers.ModelSerializer) is True
    serializer_meta = getattr(model_serializer_instance, "Meta", None)
    serializer_name = type(model_serializer_instance).__name__
    if serializer_name == "NestedSerializer":
        ref_name = None
    else:
        assert serializer_meta is not None
        assert serializer_name is not None
        ref_name = f"{serializer_meta.model._meta.app_label}_{serializer_name}"
        if ref_name.endswith("Serializer"):
            ref_name = ref_name[: -len("Serializer")]
    return ref_name


class BaseSerializerV3(serializers.ModelSerializer):
    # If True, it will save only the fields passed in the arguments to the DB.
    # It does that by passing update_fields to the save method.
    patch_update_fields = False

    def __init__(self, *args, **kwargs):
        assert issubclass(self.Meta.model, BaseModelV3), (
            f"BaseSerializerV3 can only be used with a model that subclasses BaseModelV3: {self.Meta.model}"
        )
        assert issubclass(self.Meta, BaseSerializerV3.Meta), (
            "BaseSerializerV3 requires Meta to also subclass BaseSerializerV3.Meta: {self}"
        )
        # Add the primary key of the model as a readonly field to the list of available fields
        # in the serializer
        # Skip adding the pk for multi table inheritance fields: since it adds a reference to the
        # parent table.
        # We use dict.fromkeys instead of set because sets don't keep insertion order while dicts do.
        # This allows us to keep a stable openapi generation.
        if does_model_use_multi_table_inheritance(self.Meta.model) is False:
            self.Meta.read_only_fields = list(
                dict.fromkeys(self.Meta.read_only_fields + [self.Meta.model._meta.pk.name])
            )
            self.Meta.fields = list(dict.fromkeys(self.Meta.fields + [self.Meta.model._meta.pk.name]))
        assert all(elem in self.Meta.read_only_fields for elem in self.Meta.read_only_base_fields), (
            "Subclasses of BaseSerializerV3 require the primary key, `created_at`, `created_by`,"
            " `updated_at`, and `updated_by` to be read_only"
        )
        self.Meta.ref_name = get_ref_name_for_model_serializer(self)
        super().__init__(*args, **kwargs)

    class Meta:
        model = BaseModelV3
        ref_name = ""

        read_only_base_fields: Final[List[str]] = [
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
        ]
        read_only_fields: List[str] = list(read_only_base_fields)
        fields = read_only_fields


class TestModelSerializer(BaseSerializerV3):
    __test__ = False

    class Meta(BaseSerializerV3.Meta):
        model = TestModel
        fields: List[str] = BaseSerializerV3.Meta.read_only_fields + [
            "foo",
            "bar",
        ]


class ReadWriteSerializerMethodField(serializers.SerializerMethodField):
    def __init__(self, method_name=None, **kwargs):
        super().__init__(**kwargs)
        self.read_only = False

    def to_internal_value(self, data):
        return {self.field_name: data}


class TestModelSerializerWithPatch(BaseSerializerV3):
    patch_update_fields = True
    __test__ = False

    class Meta(BaseSerializerV3.Meta):
        model = TestModel
        fields: List[str] = BaseSerializerV3.Meta.read_only_fields + [
            "foo",
            "bar",
        ]


class DynamicFieldsSerializer(serializers.Serializer):
    """
    A Serializer that takes an additional `fields` argument that controls which
    fields should be displayed.

    This is taken directly from Django Rest Framework docs.
    See https://www.django-rest-framework.org/api-guide/serializers/#dynamically-modifying-fields
    """

    def __init__(self, *args, **kwargs):
        # Don't pass the 'fields' arg up to the superclass
        fields = kwargs.pop("fields", None)

        # Instantiate the superclass normally
        super().__init__(*args, **kwargs)

        if fields is not None:
            # Drop any fields that are not specified in the `fields` argument.
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)
