import os
from unittest.mock import patch

from django.test import TestCase

from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.modules.firefly_django.fireflyadmincommand.models import CommandLastRun
from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand
from firefly.modules.firefly_django.utils import get_lucian_bot_user


class TestCommand(FireflyBaseCommand):
    """Test command for testing management command tracking"""

    def __init__(self):
        # Initialize the command name and log prefix - this is typically done by create_parser
        # which gets invoked by the invoking script (manage.py - which does not get used in this test
        # since this isnt a registered management command)
        self.command_name = "manage.py : test"
        self.subcommand_name = "test"
        self.log_prefix = f"FireflyBaseCommand: {self.command_name}: "

    def run(self, *args, **options):
        """Simple run method that does nothing"""
        pass


class FireflyBaseCommandCronTrackingTestCase(TestCase):
    """Test case for the cron job tracking in FireflyBaseCommand"""

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        reset_context_to_luci_user()
        CommandLastRun.objects.all().delete()

    @patch.dict(os.environ, {"SYNC_CRON": "1"})
    def test_command_tracking_on_execution(self):
        """Test that management commands are tracked when executed"""
        # Create a test command
        command = TestCommand()

        # Execute the command
        command.handle()

        # Verify that a record was created in the database
        self.assertEqual(CommandLastRun.objects.count(), 1)

        # Verify the record has the correct data
        cron_job_run = CommandLastRun.objects.first()
        self.assertIsNotNone(cron_job_run)
        self.assertEqual(cron_job_run.command_name, command.subcommand_name)
        self.assertIsNotNone(cron_job_run.last_run_at)

        # Execute the command again
        command.handle()

        # Verify that no new record was created
        self.assertEqual(CommandLastRun.objects.count(), 1)
        cron_job_run = CommandLastRun.objects.first()
        self.assertIsNotNone(cron_job_run)
        self.assertEqual(cron_job_run.command_name, command.subcommand_name)
        self.assertIsNotNone(cron_job_run.last_run_at)

    @patch.dict(os.environ, {"SYNC_CRON": "0"})
    def test_tracking_for_non_cron_execution(self):
        """Test that non-cron executions are tracked"""
        # Create a test command
        command = TestCommand()

        # Execute the command
        command.handle(user=get_lucian_bot_user())

        # Verify that a record was created
        self.assertEqual(CommandLastRun.objects.count(), 1)
        cron_job_run = CommandLastRun.objects.first()
        self.assertIsNotNone(cron_job_run)
        self.assertEqual(cron_job_run.command_name, command.subcommand_name)
        self.assertIsNotNone(cron_job_run.last_run_at)
