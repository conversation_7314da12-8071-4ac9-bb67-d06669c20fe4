import json
import logging
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, TypedDict, cast

from django.conf import settings
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Count, Exists, F, OuterRef, Q, Value, When
from django.db.models.functions import TruncDate
from django.db.models.query import QuerySet
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_spectacular.utils import OpenApiParameter, extend_schema
from requests.exceptions import HTTPError
from rest_framework.exceptions import ValidationError
from rest_framework.generics import ListAPIView, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_409_CONFLICT,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from rest_framework.views import APIView

from firefly.core.assignment.utils import get_users_with_internal_role_in_care_team
from firefly.core.feature.utils import is_flag_active_for_user
from firefly.core.roles.constants import APPOINTMENT_TYPE_TO_ROLE, ROLE_VALUES
from firefly.core.services.braze.client import BrazeClient
from firefly.core.services.braze.constants import BrazeEvent
from firefly.core.services.braze.tasks import submit_event_async
from firefly.core.services.elation.sync.mixins.object_to_elation import sanitize_value
from firefly.core.user.constants import NP_ROLE
from firefly.core.user.models.models import ProviderDetail, User
from firefly.core.user.permissions import IsProvider
from firefly.modules.appointment.constants import (
    ESTABLISHING_CARE_APPOINTMENT_TYPES,
    NEW_PATIENT_APPOINTMENT_TYPES,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.models import Appointment, Symptom
from firefly.modules.appointment.permissions import (
    CanBookAppointment,
    CanChangeAppointmentifUpdating,
    CanProviderBookAppointmentForPatient,
    CanViewAppointmentifReading,
    CanViewNextAvailableAppointmentSlotByPhysician,
)
from firefly.modules.appointment.serializers import AppointmentWithHealthAssessmentSerializer
from firefly.modules.appointment.slots.elation import AppointmentSlotElationClient
from firefly.modules.appointment.slots.serializers import (
    AppointmentSlotSerializer,
    AppointmentSlotSerializerV2,
    AvailableSlotByPhysicianSerializer,
)
from firefly.modules.appointment.utils import (
    get_next_start_time,
    get_next_start_time_by_risk_score,
    get_overlapping_appointments,
    get_start_time_based_on_coverage_start_date,
    get_visit_type_to_be_enabled_for_new_patient,
    has_user_attended_appointment_type,
)
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.physician.models import Physician, PhysicianPracticingStates
from firefly.modules.schedule.constants import SLOTS_NOT_AVAILABLE_REASONS, WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5
from firefly.modules.schedule.models import (
    AppointmentType,
    AppointmentTypeContractMapping,
    PhysicianAppointmentTypeExcludedStates,
    PhysicianAppointmentTypeMapping,
)
from firefly.modules.schedule.utils.slot_handler import GetAvailableSlotsResponse, get_available_slots
from firefly.modules.schedule.utils.timeslot_handler import create_case_for_appt_slots_unavailable
from firefly.modules.states.models import State

logger = logging.getLogger(__name__)


class BookedAppointmentData(TypedDict):
    booked_slots: int
    total_slots: int


class GetEligiblePhysiciansResponse(TypedDict):
    physicians: List[Physician]
    reasons_for_unavailability: List[str]


def get_physicians_eligible_for_scheduling(
    person=None,
    appointment_type: Optional[AppointmentType] = None,
    physician_id: Optional[int] = None,
) -> GetEligiblePhysiciansResponse:
    """
    Check state licenses to figure out which providers should be bookable
    """
    physicians = Physician.objects.filter((Q(provider__isnull=True) | Q(provider__user__is_active=True))).order_by(
        "created_at",
        "id",
    )
    reasons_for_unavailability = []
    if physician_id:
        physicians = physicians.filter(id=physician_id)
    state = None
    if appointment_type:
        physicians = physicians.annotate(
            has_appointment_type_mapping=Exists(
                PhysicianAppointmentTypeMapping.objects.filter(
                    physician=OuterRef("pk"),
                    appointment_type__unique_key=appointment_type.unique_key,
                )
            )
        )
        if not physician_id:
            physicians = physicians.filter(has_appointment_type_mapping=True)
    if appointment_type is None or not appointment_type.exempt_from_state_licensing:
        if not (person and person.insurance_info and person.insurance_info.state):
            return {
                "physicians": [],
                "reasons_for_unavailability": [],
            }
        state_abbr = person.insurance_info.state
        states = State.objects.filter(abbreviation__iexact=state_abbr)
        if states.count() != 1:
            return {
                "physicians": [],
                "reasons_for_unavailability": [],
            }
        state = states.first()
        if physician_id:
            physicians = physicians.annotate(
                is_licensed_to_user_state=Exists(
                    PhysicianPracticingStates.objects.filter(
                        physician=OuterRef("pk"),
                        state=state,
                    )
                )
            )
        else:
            physicians = physicians.filter(
                Q(practicing_states=state) & Q(physician_practicing_states__deleted__isnull=True)
            )

    if appointment_type and state:
        if physician_id:
            physicians = physicians.annotate(
                is_in_excluded_states=Exists(
                    PhysicianAppointmentTypeExcludedStates.objects.filter(
                        physician_appt_type_mapping__physician_id=OuterRef("pk"),
                        physician_appt_type_mapping__appointment_type__unique_key=appointment_type.unique_key,
                        state__abbreviation=state,
                    )
                )
            )
        else:
            excluded_physician_ids = PhysicianAppointmentTypeMapping.objects.filter(
                Q(appointment_type__unique_key=appointment_type.unique_key)
                & Q(excluded_states__abbreviation=state)
                & Q(physician_appt_type_excluded_states__deleted__isnull=True)
            ).values_list("physician__id", flat=True)
            physicians = physicians.exclude(id__in=excluded_physician_ids)

    if appointment_type and appointment_type.requires_patient_booking_within_care_team and person:
        patient_provider_users: List[User] = get_users_with_internal_role_in_care_team(
            APPOINTMENT_TYPE_TO_ROLE[appointment_type.unique_key], person
        )
        physician_ids = []
        for patient_provider_user in patient_provider_users:
            provider: ProviderDetail = patient_provider_user.providerdetail
            if provider and provider.physician:
                physician_ids.append(provider.physician.id)
        if physician_id:
            physicians = physicians.annotate(
                is_in_care_team=Case(
                    When(id__in=physician_ids, then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField(),
                )
            )
        else:
            physicians = physicians.filter(id__in=physician_ids)
        logger.info(
            "For %s visits for patient %d, defaulting to care team provider",
            appointment_type.unique_key,
            person.user.id,
        )

    if appointment_type and appointment_type.requires_patient_booking_with_previous_provider and person:
        last_appt = (
            Appointment.objects.filter(patient=person.user, reason=appointment_type.unique_key).order_by("start").last()
        )
        if last_appt and last_appt.physician:
            if physician_id:
                physicians = physicians.annotate(
                    is_past_visit_exists=Case(
                        When(id=last_appt.physician.id, then=Value(True)),
                        default=Value(False),
                        output_field=BooleanField(),
                    )
                )
            else:
                physicians = physicians.filter(id=last_appt.physician.id)
        else:
            logger.info("Could not find past %s visits for patient %d.", appointment_type.unique_key, person.user.id)

    if appointment_type and appointment_type.unique_key in NEW_PATIENT_APPOINTMENT_TYPES:
        # Show new appointment type slots only for those physicians who are taking new patients
        # Note: not deleting/regenerating the physician->appointment_type mapping or appointment slots generated in the
        # backend, because in future there could be a case that "a physician accepting new patients" would be on State
        # basis. Hence adding ability to control availability of slots based on is_taking_new_patients flag update
        if physician_id:
            physicians = physicians.annotate(is_taking_new_patient=F("provider__is_taking_new_patients"))
        else:
            physicians = physicians.filter(provider__is_taking_new_patients=True)
    physician_list = list(physicians)
    if physician_id and len(physician_list) > 0:
        physician = physician_list[0]
        if hasattr(physician, "has_appointment_type_mapping") and not physician.has_appointment_type_mapping:
            reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["appointment_type_mapping_missing"])
        if hasattr(physician, "is_licensed_to_user_state") and not physician.is_licensed_to_user_state:
            reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["provider_not_licensed"])
        if hasattr(physician, "is_in_excluded_states") and physician.is_in_excluded_states:
            reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"])
        if hasattr(physician, "is_in_care_team") and not physician.is_in_care_team:
            reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["provider_not_in_care_team"])
        if hasattr(physician, "is_taking_new_patient") and not physician.is_taking_new_patient:
            reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["provider_not_accepting_new_patients"])
        if len(reasons_for_unavailability) > 0:
            physician_list = []
    return {
        "physicians": physician_list,
        "reasons_for_unavailability": reasons_for_unavailability,
    }


date_format = "%Y-%m-%dT%H:%M:%SZ"


class AppointmentAlreadyBookedException(Exception):
    pass


class AppointmentSlotList(ListAPIView):
    """Returns a list of Appointment Slots"""

    model = Appointment
    serializer_class = AppointmentSlotSerializer
    permission_classes = (IsAuthenticated,)
    skip_tenant_access_check = True
    skip_appts_without_physician = False
    include_care_team_info = False
    include_licensed_physicians = False
    include_appt_rules = True

    @extend_schema(
        parameters=[
            OpenApiParameter(name="reason", description="Filter by appointment reason", required=False, type=str),
            OpenApiParameter(
                name="start_time",
                description=f"Filter by appointment start time ({date_format})",
                required=False,
                type=str,
            ),
            OpenApiParameter(
                name="end_time",
                description=f"Filter by appointment end time ({date_format})",
                required=False,
                type=str,
            ),
            OpenApiParameter(name="physician", description="Filter by physician ID", required=False, type=int),
        ],
    )
    def get(self, request):
        return super().get(request)

    def get_user(self) -> User:
        return cast(User, self.request.user)

    def get_queryset(self):
        user = self.get_user()
        start_time_param = self.request.GET.get("start_time", None)
        if start_time_param is not None:
            start_time = timezone.make_aware(datetime.strptime(start_time_param, date_format))
        else:
            start_time = timezone.now()

        if self.include_appt_rules:
            # filter out appointments in the past
            start_time = max(get_next_start_time(user), start_time)
            start_time = get_start_time_based_on_coverage_start_date(user, start_time)

        reason = self.request.GET.get("reason", None)
        appointment_type = None
        try:
            appointment_type = AppointmentType.objects.get(unique_key=reason)
        except AppointmentType.DoesNotExist:
            logger.exception("Missing AppointmentType for reason %s", reason)

        # apply visit buffers
        if self.include_appt_rules:
            start_time = start_time + settings.NEXT_AVAILABLE_VIDEO_VISIT_BUFFER
        else:
            start_time = start_time + settings.NEXT_AVAILABLE_VIDEO_VISIT_BUFFER_FOR_FF_STAFF

        # Check if user is a patient and if has already attended an appointment
        user_has_attended_appt = has_user_attended_appointment_type(user, ESTABLISHING_CARE_APPOINTMENT_TYPES)
        # If the user is requesting a video appointment and has not previously been to one,
        # let's only
        # show appointments with the new patient appointment type.
        if reason == AppointmentReason.VIDEO and user.is_patient and not user_has_attended_appt:
            reason = get_visit_type_to_be_enabled_for_new_patient(request_user=user, person=user.person)

        if reason in NEW_PATIENT_APPOINTMENT_TYPES and user.is_patient and self.include_appt_rules:
            start_time = max(get_next_start_time_by_risk_score(user.person), start_time)

        end_time_param = self.request.GET.get("end_time", None)
        end_time = (
            timezone.make_aware(datetime.strptime(end_time_param, date_format))
            if end_time_param
            else start_time
            + (
                timedelta(weeks=appointment_type.booking_window_in_weeks)
                if appointment_type and appointment_type.booking_window_in_weeks
                else settings.NEXT_AVAILABLE_SLOT_PERIOD
            )
        )
        appt_query = Appointment.objects.filter(
            Q(patient__isnull=True, time_slot_type="appointment_slot")
            & (Q(physician__provider__isnull=True) | Q(physician__provider__user__is_active=True))
        )

        appointment_dates_to_be_excluded: GetAppointmentDatesToBeExcludedResponse = (
            get_appointment_dates_to_be_excluded(user, reason, start_time, end_time, log_prefix="appointment/slot/")
        )
        appt_query = appt_query.exclude(start__date__in=appointment_dates_to_be_excluded["dates_to_be_excluded"])

        if self.skip_appts_without_physician:
            appt_query = appt_query.filter(physician__isnull=False)

        if self.include_care_team_info:
            appt_query = appt_query.annotate(
                physician_in_care_team=Case(
                    When(
                        physician__provider__in=user.person.care_team.all(),
                        then=True,
                    ),
                    default=False,
                )
            )
        if self.include_licensed_physicians:
            licensed_physicians: GetEligiblePhysiciansResponse = get_physicians_eligible_for_scheduling(
                user.person, appointment_type
            )
            appt_query = appt_query.filter(physician__in=licensed_physicians["physicians"])

        if reason == AppointmentReason.VIDEO_NEW_PATIENT and user.is_patient and self.include_appt_rules:
            appt_query = appt_query.filter(visible=True)
        appt_query = appt_query.filter(start__range=[start_time, end_time]).exclude(reason=AppointmentReason.URGENT)
        if not reason:
            appt_query = appt_query.exclude(reason=AppointmentReason.FOCUSED_VISIT)

        if reason:
            appt_query = appt_query.filter(reason=reason)
        physician_id = self.request.GET.get("physician", None)
        if physician_id:
            appt_query = appt_query.filter(physician=physician_id)
            ordered = appt_query.order_by("start")
        else:
            # Show care team NP appointments first
            care_team = Physician.objects.filter(
                provider__in=user.person.care_team.all(),
                provider__user__is_active=True,
            )
            ordered = appt_query.prefetch_related("physician__provider__user__groups").order_by("start")

            # Based on the appointment reason, let's choose which provider we surface to the top of the
            # appointment list
            role = ROLE_VALUES.NP
            if reason == AppointmentReason.HEALTH_GUIDE_BOOKABLE:
                role = ROLE_VALUES.HG
            ordered = sorted(
                ordered,
                key=lambda a: (
                    not (
                        a.physician in care_team
                        and a.physician.provider.internal_role
                        and a.physician.provider.internal_role.role_name == role
                    ),
                    a.start,
                ),
            )

        return ordered


class AppointmentSlotClaim(APIView):
    skip_tenant_access_check = True
    """
    Books the Appointment Slot if unbooked in Elation
    Returns resulting Appointment
    ----
    Elation API Doc - https://docs.elationhealth.com/reference#update-appointment-slot-1
    """

    def post(self, request, elation_id):
        if not request.user.is_patient or not request.user.person.elation_id:
            return Response({"detail": "Can't access appointment slot"}, status=HTTP_403_FORBIDDEN)
        try:
            slot = AppointmentSlotElationClient().get_record(elation_id)
        except HTTPError as http_error:
            return Response({"detail": "%s" % http_error}, status=HTTP_400_BAD_REQUEST)
        if slot["patient"] is not None:
            return Response({"detail": "Slot already booked"}, status=HTTP_409_CONFLICT)
        appointment = claim_appointment_slot(request, slot, elation_id)
        return Response(AppointmentWithHealthAssessmentSerializer(appointment).data)


class AppointmentSlotClaimV2(UpdateAPIView):
    model = Appointment
    queryset = Appointment.objects
    serializer_class = AppointmentWithHealthAssessmentSerializer
    lookup_field = "elation_id"
    permission_classes = (
        IsAuthenticated,
        CanViewAppointmentifReading,
        CanChangeAppointmentifUpdating,
        CanBookAppointment,
    )
    skip_tenant_access_check = True

    def get_user(self) -> User:
        return cast(User, self.request.user)

    def update(self, request, *args, **kwargs):
        user = self.get_user()
        # Send all new changes to the appointment to this function to update
        body = json.loads(request.body) if request.body else {}
        # Currently we only add description to be updated
        # along with the status and patient for claiming an appointment
        extra_params = {}
        description = body.get("description")
        if description:
            extra_params["description"] = sanitize_value(description)
        appointment, error = try_claim_appointment_slot(request, kwargs["elation_id"], extra_params, user)
        if error:
            return error
        return Response(self.serializer_class(appointment).data)


class AppointmentSlotClaimV3(UpdateAPIView):
    model = Appointment
    queryset = Appointment.objects
    serializer_class = AppointmentWithHealthAssessmentSerializer
    lookup_field = "id"
    permission_classes = (
        IsAuthenticated,
        CanViewAppointmentifReading,
        CanChangeAppointmentifUpdating,
        CanBookAppointment,
    )
    skip_tenant_access_check = True

    def get_user(self) -> User:
        return cast(User, self.request.user)

    def update(self, request, *args, **kwargs):
        user = self.get_user()
        # Send all new changes to the appointment to this function to update
        body = json.loads(request.body) if request.body else {}
        # Currently we only add description and symptoms to be updated
        # along with the status and patient for booking an appointment
        extra_params = {}
        description = body.get("description")
        if description:
            extra_params["description"] = sanitize_value(description)
        extra_params["symptom_ids"] = body.get("symptom_ids", [])
        extra_params["other_symptoms"] = body.get("other_symptoms")

        appointment, error = book_an_appointment_slot(kwargs["id"], user, request, extra_params)
        if error:
            return error
        return Response(self.serializer_class(appointment).data)


class BookAppointmentByProvider(AppointmentSlotClaimV3):
    model = Appointment

    def get_permissions(self):
        permission_classes = list(self.permission_classes)
        permission_classes.remove(CanBookAppointment)
        permission_classes.append(IsProvider)
        permission_classes.append(CanProviderBookAppointmentForPatient)
        return [permission() for permission in permission_classes]

    def get_user(self) -> User:
        body = json.loads(self.request.body) if self.request.body else {}
        patient_id = body.get("patient_id")
        if patient_id is None:
            raise ValidationError("missing patient_id")
        user = get_object_or_404(User, pk=patient_id)
        return user


class NextAvailableAppointmentSlotByPhysician(ListAPIView):
    skip_tenant_access_check = True
    """Returns a list of physicians with a next available slot field, ordered by slot time"""

    model = Appointment
    serializer_class = AvailableSlotByPhysicianSerializer
    permission_classes = [CanViewNextAvailableAppointmentSlotByPhysician]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="reason", description="Filter by appointment reason", required=False, type=str),
            OpenApiParameter(
                name="from",
                description=f"Filter by appointment start time ({date_format})",
                required=False,
                type=str,
            ),
        ]
    )
    def get(self, *args, **kwargs):
        return super().get(*args, **kwargs)

    def get_queryset(self):
        reason = self.request.query_params.get("reason", None)
        appointment_type = None
        try:
            appointment_type = AppointmentType.objects.get(unique_key=reason)
        except AppointmentType.DoesNotExist:
            logger.exception("Missing AppointmentType for reason %s", reason)

        licensed_physicians: GetEligiblePhysiciansResponse = get_physicians_eligible_for_scheduling(
            self.request.user.person, appointment_type
        )
        from_time = self.request.query_params.get("from", None)
        if from_time is not None:
            from_time = timezone.make_aware(datetime.strptime(from_time, "%Y-%m-%dT%H:%M:%S.%fZ"))
        else:
            from_time = timezone.now()
        from_time = max(get_next_start_time(self.request.user), from_time)
        from_time = get_start_time_based_on_coverage_start_date(self.request.user, from_time)

        # Check if user is a patient and if has already attended an appointment
        user_has_attended_appt = has_user_attended_appointment_type(
            self.request.user, ESTABLISHING_CARE_APPOINTMENT_TYPES
        )
        if reason:
            # If the user is requesting a video appointment and has not previously been to one,
            # let's only show appointments with the new patient appointment type.
            if reason == AppointmentReason.VIDEO and self.request.user.is_patient and not user_has_attended_appt:
                reason = get_visit_type_to_be_enabled_for_new_patient(
                    request_user=self.request.user, person=self.request.user.person
                )

        if reason in NEW_PATIENT_APPOINTMENT_TYPES and self.request.user.is_patient:
            from_time = max(get_next_start_time_by_risk_score(self.request.user.person), from_time)

        end_time = from_time + (
            timedelta(weeks=appointment_type.booking_window_in_weeks)
            if appointment_type and appointment_type.booking_window_in_weeks
            else settings.NEXT_AVAILABLE_SLOT_PERIOD
        )

        appointment_dates_to_be_excluded: GetAppointmentDatesToBeExcludedResponse = (
            get_appointment_dates_to_be_excluded(
                self.request.user, reason, from_time, end_time, log_prefix="appointment/slot/available-physicians"
            )
        )

        appointment_query = (
            Appointment.objects.filter(
                reason=reason,
                patient__isnull=True,
                time_slot_type="appointment_slot",
                physician__provider__user__is_active=True,
            )
            .exclude(start__date__in=appointment_dates_to_be_excluded["dates_to_be_excluded"])
            .prefetch_related("physician__provider__user__groups")
        )

        if not reason or reason != AppointmentReason.HEALTH_GUIDE_BOOKABLE:
            # We do not need to check for licensure for health guide visits
            appointment_query = appointment_query.filter(physician__in=licensed_physicians["physicians"])

        if reason == AppointmentReason.VIDEO_NEW_PATIENT and self.request.user.is_patient:
            appointment_query = appointment_query.filter(visible=True)

        appointment_query = appointment_query.filter(
            start__range=[
                from_time,
                end_time,
            ]
        ).exclude(reason=AppointmentReason.URGENT)

        if not reason:
            appointment_query.exclude(reason=AppointmentReason.FOCUSED_VISIT)
        available_appointment_slots = (
            appointment_query.annotate(
                is_physician_available=Exists(
                    appointment_query.filter(
                        physician=OuterRef("physician"),
                        start__lte=from_time
                        + (
                            timedelta(weeks=appointment_type.booking_window_in_weeks)
                            if appointment_type and appointment_type.booking_window_in_weeks
                            else settings.NEXT_AVAILABLE_SLOT_PERIOD
                        ),
                    )
                ),
                physician_in_care_team=Case(
                    When(
                        physician__provider__in=self.request.user.person.care_team.all(),
                        then=True,
                    ),
                    default=False,
                ),
                physician_is_care_team_np=(
                    Case(
                        When(
                            physician_in_care_team=True,
                            physician__provider__user__groups__name=NP_ROLE,
                            then=True,
                        ),
                        default=False,
                    )
                ),
            )
            .order_by("physician", "start")
            .distinct("physician")
        )

        # Based on the appointment reason, let's choose which provider we surface to the top of the appointment list
        role = ROLE_VALUES.NP
        if reason == AppointmentReason.HEALTH_GUIDE_BOOKABLE:
            role = ROLE_VALUES.HG

        available_appointment_slots = sorted(
            available_appointment_slots,
            key=lambda a: (
                not a.is_physician_available,
                not (
                    a.physician_in_care_team
                    and a.physician.provider.internal_role
                    and a.physician.provider.internal_role.role_name == role
                ),
                a.start,
            ),
        )

        next_scheduled_appointment = Appointment.objects.filter(
            status=AppointmentStatus.SCHEDULED.value,
            patient=self.request.user,
            start__gte=timezone.now(),
            reason=reason,
        )

        if available_appointment_slots == [] and not next_scheduled_appointment.exists():
            create_case_for_appt_slots_unavailable(
                user=self.request.user,
                reason=reason,
                state=self.request.user.person.insurance_info.state,
                log_prefix="[AppointmentSlotsUnavailable]",
            )

        return available_appointment_slots


class ListPhysiciansAndSlotsForProvider(AppointmentSlotList):
    skip_tenant_access_check = True
    skip_appts_without_physician = True
    include_care_team_info = True
    include_licensed_physicians = True
    include_appt_rules = False
    """Returns a list of physicians (care team + others) with slots for a given date"""

    model = Appointment

    def get_serializer_class(self):
        return AppointmentSlotSerializerV2

    def get_permissions(self):
        permission_classes = list(self.permission_classes)
        permission_classes.append(IsProvider)
        return [permission() for permission in permission_classes]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="reason", description="Filter by appointment reason", required=False, type=str),
            OpenApiParameter(
                name="start_time",
                description=f"Filter by appointment start time ({date_format})",
                required=False,
                type=str,
            ),
            OpenApiParameter(
                name="end_time",
                description=f"Filter by appointment end time ({date_format})",
                required=False,
                type=str,
            ),
            OpenApiParameter(
                name="patient_id",
                description="Filter by patient",
                required=True,
                type=int,
            ),
        ]
    )
    def get_user(self) -> User:
        patient_id = self.request.query_params.get("patient_id")
        if patient_id is None:
            raise ValidationError("missing patient_id")
        user = get_object_or_404(User, pk=patient_id)
        return user


def get_existing_appointment_dates(user, rescheduled_appt_start):
    existing_appointment_dates = [
        app.start.date() for app in user.appointments.exclude(status=AppointmentStatus.CANCELLED.value)
    ]
    # If user is rescheduling an appointment, we should allow rebooking the appointments
    # on same day of the appointment being rescheduled. Hence should remove that day in dates to be excluded
    if rescheduled_appt_start and (rescheduled_appt_start.date() in existing_appointment_dates):
        existing_appointment_dates = existing_appointment_dates.remove(rescheduled_appt_start.date())
    return existing_appointment_dates if existing_appointment_dates else []


class GetAppointmentDatesToBeExcludedResponse(TypedDict):
    dates_to_be_excluded: List[date]
    reasons_for_unavailability: List[str]


def get_appointment_dates_to_be_excluded(
    user,
    reason,
    start_time,
    end_time,
    rescheduled_appt_start=None,
    log_prefix=None,
    choosen_date=None,
) -> GetAppointmentDatesToBeExcludedResponse:
    reasons_for_unavailability: List[str] = []
    # Attempt to get the AppointmentType associated with the reason
    appointment_type: AppointmentType | None = None
    # If there is no configuration set for the client and for particular appointment type,
    # return dates on which an appointment exists
    dates_to_be_excluded: List[date] = get_existing_appointment_dates(user, rescheduled_appt_start)
    if choosen_date and choosen_date in dates_to_be_excluded:
        reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["existing_appointment_present"])

    try:
        appointment_type = AppointmentType.objects.get(unique_key=reason)
    except AppointmentType.DoesNotExist:
        logger.exception("Missing AppointmentType for reason %s", reason)
    # Fetch the client configuration set, to rate limit the appointments for a particular client
    contract = user.person.attribution.contract
    if contract and appointment_type:
        mapping = AppointmentTypeContractMapping.objects.filter(contract=contract, appointment_type=appointment_type)
        if mapping.exists():
            allowed_percentage = mapping[0].percentage_of_allowed_appointments
            dates_exceeding_booking_capacity = []
            if allowed_percentage is not None:
                dates_exceeding_booking_capacity = get_dates_to_be_excluded_based_on_client_config(
                    contract=contract,
                    log_prefix=log_prefix,
                    appointment_type=appointment_type,
                    allowed_percentage=allowed_percentage,
                    start_time=start_time,
                    end_time=end_time,
                    user=user,
                )
                if choosen_date and choosen_date in dates_exceeding_booking_capacity:
                    reasons_for_unavailability.append(
                        f"{contract.name} {SLOTS_NOT_AVAILABLE_REASONS['visit_limit_reached']}"
                    )
            dates_to_be_excluded = sorted(list(set(dates_to_be_excluded + dates_exceeding_booking_capacity)))
    return {"dates_to_be_excluded": dates_to_be_excluded, "reasons_for_unavailability": reasons_for_unavailability}


def get_dates_to_be_excluded_based_on_client_config(
    contract, log_prefix, appointment_type, allowed_percentage, start_time, end_time, user=None
):
    appointment_capacity: Dict[date, BookedAppointmentData] = {}
    dates_to_be_excluded: List[date] = []

    # Get all the physicians mapped to appointment_type selected
    eligible_physicians = (
        Physician.objects.filter((Q(provider__isnull=True) | Q(provider__user__is_active=True)))
        .order_by(
            "created_at",
            "id",
        )
        .annotate(
            has_appointment_type_mapping=Exists(
                PhysicianAppointmentTypeMapping.objects.filter(
                    physician=OuterRef("pk"),
                    appointment_type__unique_key=appointment_type.unique_key,
                )
            )
        )
        .filter(has_appointment_type_mapping=True)
    )
    # If the appointment type is new patient appointment type, consider only those physicians who accept new patients
    if appointment_type and appointment_type.unique_key in NEW_PATIENT_APPOINTMENT_TYPES:
        eligible_physicians = eligible_physicians.filter(provider__is_taking_new_patients=True)

    # Get available appointments
    available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
        provider_ids=[physician.provider.user_id for physician in eligible_physicians],
        start_date_time=start_time,
        end_date_time=end_time,
        reason=appointment_type.unique_key,
        log_prefix=log_prefix,
    )
    available_dates_for_all_providers = []
    for physician in eligible_physicians:
        if physician.provider.user_id in available_slots_respponse["provider_slot_map"].keys():
            slot_dates_for_provider = [
                slot.date() for slot in available_slots_respponse["provider_slot_map"][physician.provider.user_id]
            ]
            for slot_date in slot_dates_for_provider:
                available_dates_for_all_providers.append(slot_date)

    # Group by appointment start date and total available appointment slots on that day
    appointment_counts_by_date = dict(
        (date, available_dates_for_all_providers.count(date)) for date in available_dates_for_all_providers
    )

    # Convert to a list which stores mappings of date and appointment count on that date
    available_appointment_slots_for_each_day = []
    for key, value in appointment_counts_by_date.items():
        available_appointment_slots_for_each_day.append({"appointment_date": key, "count": value})

    # If allowed percentage is 0 for a particular contract, exclude all dates on which there are available slots
    if allowed_percentage == 0:
        for appt in available_appointment_slots_for_each_day:
            dates_to_be_excluded.append(appt["appointment_date"])
        return dates_to_be_excluded

    # Get booked slots as well to consider all slots for calculation of percentage
    booked_slots_for_each_day: QuerySet[Appointment] = (
        Appointment.objects.filter(
            time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
            reason=appointment_type.unique_key,
            start__range=[start_time, end_time],
            patient__isnull=False,
        )
        .annotate(appointment_date=TruncDate("start"))
        .values("appointment_date")
        .annotate(count=Count("id", distinct=True))
        .order_by("appointment_date")
        .exclude(status=AppointmentStatus.CANCELLED.value)
    )

    # Store a map of all slots available for that day, by adding both available and booked slot counts for each day
    all_slots_count_per_day = []
    for booked_slot_map in booked_slots_for_each_day:
        count_of_available_slots_on_same_date = [
            appt["count"]
            for appt in available_appointment_slots_for_each_day
            if appt["appointment_date"] == booked_slot_map["appointment_date"]
        ]
        if len(count_of_available_slots_on_same_date):
            all_slots_count_per_day.append(
                {
                    "appointment_date": booked_slot_map["appointment_date"],
                    "count": booked_slot_map["count"] + count_of_available_slots_on_same_date[0],
                }
            )

    # Group by appointment start date and booked appointment slots on that day with patients of same contract
    booked_appointment_count_per_day_with_patients_of_same_contract = booked_slots_for_each_day.filter(
        patient__person__attribution__contract=contract
    )

    for booked_appt_count_map in booked_appointment_count_per_day_with_patients_of_same_contract:
        all_appointment_count = [
            appt["count"]
            for appt in all_slots_count_per_day
            if appt["appointment_date"] == booked_appt_count_map["appointment_date"]
        ]
        if len(all_appointment_count):
            appointment_capacity[booked_appt_count_map["appointment_date"]] = {
                "total_slots": all_appointment_count[0],
                "booked_slots": booked_appt_count_map["count"],
            }

    for key, val in appointment_capacity.items():
        booked_percentage = (val["booked_slots"] / val["total_slots"]) * 100
        if booked_percentage >= allowed_percentage:
            user_id = user.id if user else None
            logger.info(
                "Restricting %s appointment booking, starting from %s and ending %s, for user %s with contract %s on\
 date %s, with booked appointment count on the day %s, total slots possible %s, booked percentage %s and allowed\
 appointment capacity set as %s",
                appointment_type.unique_key,
                start_time,
                end_time,
                user_id,
                contract.name,
                key,
                val["booked_slots"],
                val["total_slots"],
                booked_percentage,
                allowed_percentage,
            )
            dates_to_be_excluded.append(key)

    return dates_to_be_excluded


def claim_appointment_slot_for_user(
    # Elation id of the person - available in person.elation_id
    person_elation_id: int,
    # Elation id of the appointment - available in appointment.elation_id
    appointment_elation_id: int,
    # appointment_slot is the slot information thats received from elation
    # if null - will be fetched from elation
    appointment_slot: Optional[Any] = None,
    # any extra params that need to be passed during slot booking
    extra_params=None,
):
    extra_params = extra_params or {}
    log_prefix: str = (
        f"claim_appointment_slot_for_user: Person elation id {person_elation_id} "
        f"Appt elation id: {appointment_elation_id}"
    )
    if not appointment_slot:
        logger.info("%s: Refetching slot data", log_prefix)
        appointment_slot = AppointmentSlotElationClient().get_record(appointment_elation_id)
    appointment_slot["patient"] = person_elation_id
    for key in extra_params:
        if key != "elation_id":
            appointment_slot[key] = extra_params.get(key)
    appointment_slot["status"] = {"status": "Scheduled"}
    response = AppointmentSlotElationClient().update_record(appointment_elation_id, appointment_slot)
    logger.info("%s: Elation update complete", log_prefix)
    sync = ElationAppointmentSync()
    if response:
        # TODO: Occasionally the response does not include a proper status, so let's patch the response with a
        # Scheduled status. This should be fixed in Elation eventually and can be removed once it is.\
        saved = sync.save_records([response], status="Scheduled")
        if saved:
            appointment = saved[0]
        else:
            return None
    else:
        return None
    return appointment


def claim_appointment_slot(request, slot, elation_id, extra_params=None, user_data: Optional[User] = None):
    extra_params = extra_params or {}
    user = user_data if user_data else request.user
    person_elation_id = user.person.elation_id
    assert isinstance(person_elation_id, int), "person_elation_id must be an integer"
    appointment = claim_appointment_slot_for_user(
        appointment_elation_id=elation_id,
        appointment_slot=slot,
        extra_params=extra_params,
        person_elation_id=person_elation_id,
    )
    if appointment:
        appointment.refresh_from_db()

    BrazeClient().submit_event(user.person.id, BrazeEvent.VISIT_SCHEDULED, {})
    return appointment


def try_claim_appointment_slot(request, elation_id, extra_params=None, user: Optional[User] = None):
    extra_params = extra_params or {}
    # Ensure the patient doesn't try to book an appt in the past
    try:
        appt = Appointment.objects.get(elation_id=elation_id)
    except Appointment.DoesNotExist:
        return None, Response({"detail: Could not find appointment with specified ID"}, status=HTTP_400_BAD_REQUEST)

    if appt.start < timezone.now():
        return None, Response(
            {"detail": "Cannot book appointment with a start time in the past"},
            status=HTTP_400_BAD_REQUEST,
        )

    user_data = user if user else request.user

    try:
        slot = AppointmentSlotElationClient().get_record(elation_id)
    except HTTPError as http_error:
        return None, Response({"detail": "%s" % http_error}, status=HTTP_400_BAD_REQUEST)
    if slot["patient"] is not None:
        if slot["patient"] != user_data.person.elation_id:
            return None, Response({"detail": "Slot already booked"}, status=HTTP_409_CONFLICT)
        else:
            # This scenario is very unlikely: will only happen when a user has previously
            # booked an appointment
            # but Lucian does not show it being booked while Elation does. If that's the case,
            # let's resync the appt
            # in Lucian and let the user know the user know the appt in in fact booked.
            logger.info(
                "Appointment %s is already claimed by the requesting user %d.",
                elation_id,
                user_data.id,
            )
            sync = ElationAppointmentSync()
            saved = sync.save_records([slot])
            if saved:
                appointment = saved[0]
                return appointment, None
            return None, Response({"detail": "Elation sync failed"}, status=HTTP_500_INTERNAL_SERVER_ERROR)
    appointment = claim_appointment_slot(request, slot, elation_id, extra_params, user_data)
    return appointment, None


def book_appointment_slot(appointment_slot, user, extra_params=None):
    """Books an appointment for a patient with a physician

    Args:
        appointment_slot: The appointment slot
        user: patient
        request: request
        extra_params: json structured params for slot
    Raises:
        ValueError: If the appointment slot is already booked or it's not for today.
    """
    extra_params = extra_params or {}
    log_prefix = f"book_appointment_slot: Appointment: {appointment_slot.pk}"
    if appointment_slot.start < timezone.now():
        logger.info(
            "%s:Cannot book appointment with a start time in the past. slot id: %d", log_prefix, appointment_slot.id
        )
        return None, Response(
            {"detail": "Cannot book appointment with a start time in the past"},
            status=HTTP_400_BAD_REQUEST,
        )
    if appointment_slot.patient is not None:
        logger.info("%s :Cannot book slot already booked. slot id: %d", log_prefix, appointment_slot.id)
        return None, Response({"detail": "Slot already booked"}, status=HTTP_409_CONFLICT)
    # First, acquire a lock on the appointment so that other users cannot
    # update/ book the appointment at the same time
    #
    # Atomic transactions and "SELECT ... FOR UPDATE" should generally be
    # avoided, since it takes a lock on all rows in the result, and only
    # releases them once the transaction is complete, which can result in
    # deadlock.
    logger.info("%s: Will book appointment in Lucian id: %d", log_prefix, appointment_slot.pk)
    update_fields = [
        "status",
        "patient",
    ]
    with transaction.atomic():  # noqa: TID251
        overlapping_appointments: QuerySet[Appointment] = []
        try:
            # If v2.5 flag is enabled we need not handle the overlapping appointment slots as there wouldn't
            # exist any unbooked slots available in backend.
            if (
                appointment_slot.physician
                and appointment_slot.physician.provider
                and appointment_slot.physician.provider.user
                and is_flag_active_for_user(
                    flag_name=WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, user=appointment_slot.physician.provider.user
                )
            ):
                logger.info(
                    "Dynamic scheduling v2.5 enabled for provider %s, skipping to handle overlapping appointments",
                    appointment_slot.physician.provider.user.id,
                )
                for key, value in extra_params.items():
                    if key == "symptom_ids":
                        appointment_slot.symptoms.set(value)
                        continue
                    setattr(appointment_slot, key, value)
                    update_fields.append(key)
                appointment_slot.status = AppointmentStatus.SCHEDULED.value
                appointment_slot.patient = user
                # book appointment: this should asynchronously sync the data to elation
                appointment_slot.save(update_fields=update_fields)
            else:
                # For any given time - multiple dynamic slots can exist
                # Ex: A physician is likely to have one slot for est. members; another for non-established members
                # When one of these slots is booked; the other needs to be nuked so that it is no longer available
                # for members.
                # Should lock one row for each appointment type supported by the physician(currently maxes out at 2)
                if appointment_slot.physician:
                    overlapping_appointments = (
                        get_overlapping_appointments(
                            appointment=appointment_slot,
                            appointment_objects=Appointment.objects.all(),
                            log_prefix=log_prefix,
                        )
                        .filter(
                            time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
                            source=AppointmentSource.LUCIAN,
                        )
                        # Ordering is required so that rows are selected and locked in a deterministic fashion
                        .order_by("-pk")
                        # The nowait parameter allows only one concurrent locking attempt to succeed.
                        # If there are concurrent attempts to book we only want the first request that successfully
                        # gets the lock to continue. The other request should be immediately rejected since even if it
                        # acquires the lock after the first transaction is complete - the first transaction would have
                        # either booked / nuked the slot that the second request was waiting for
                        .select_for_update(nowait=True)
                    )
                logger.info(
                    "%s:[API] Book appointment, should delete slots for same time. To be deleted count : %d",
                    log_prefix,
                    overlapping_appointments.count(),
                )
                deleted_appointment_ids: List[int] = []
                for appointment in overlapping_appointments.iterator():
                    # If any of the appointments within the time range has already been booked
                    # a new appointment cannot be booked within the same time range
                    if appointment.patient is not None:
                        raise AppointmentAlreadyBookedException("Appointment is already booked")
                    if appointment.pk == appointment_slot.pk:
                        for key, value in extra_params.items():
                            if key == "symptom_ids":
                                appointment_slot.symptoms.set(value)
                                continue
                            setattr(appointment_slot, key, value)
                            update_fields.append(key)
                        appointment_slot.status = AppointmentStatus.SCHEDULED.value
                        appointment_slot.patient = user
                        # book appointment: this should asynchronously sync the data to elation
                        appointment_slot.save(update_fields=update_fields)
                    else:
                        # All other dynamic slots that exist in the same time period need to be nuked
                        deleted_appointment_ids.append(appointment.id)
                        logger.info(
                            "%s:[API] will delete appointment slot: %d reason: %s starting at: %s",
                            log_prefix,
                            appointment.id,
                            appointment.reason,
                            appointment.start,
                        )
                        appointment.delete()
                # Maintain an event log, to know the count of affected longer visits due to focused visit booking
                if appointment.reason == AppointmentReason.FOCUSED_VISIT and len(deleted_appointment_ids) > 0:
                    EventLog.objects.create(
                        target=appointment_slot,
                        type=EventTypeCodes.FOCUSED_APPOINTMENT_SCHEDULED,
                        user=appointment_slot.patient,
                        metadata={
                            "current_appointment": {
                                "patient_id": appointment_slot.patient_id,
                                "updated_by_id": appointment_slot.updated_by.id
                                if appointment_slot.updated_by
                                else None,
                            },
                            "overlapping_appointments_deleted": deleted_appointment_ids,
                        },
                    )
        except AppointmentAlreadyBookedException:
            transaction.set_rollback(True)
            logger.exception("%s: Failed to book appointment", log_prefix)
            return None, Response({"detail": "Slot already booked"}, status=HTTP_409_CONFLICT)

    logger.info("%s: Booked appointment in Lucian id: %d", log_prefix, appointment_slot.pk)

    submit_event_async.send(person_id=user.person.id, event_name=BrazeEvent.VISIT_SCHEDULED, properties={})
    return appointment_slot, None


def book_an_appointment_slot(slot_id, user, request, extra_params=None):
    extra_params = extra_params or {}
    log_prefix: str = "AppointmentSlotClaimV3"

    try:
        slot: Appointment = Appointment.objects.get(id=slot_id)
    except Appointment.DoesNotExist:
        return None, Response(
            {"detail": "Appointment is deleted"},
            status=HTTP_409_CONFLICT,
        )

    # flag needs to be active for the physician of the appointment
    # the requesting user can be patient/ an internal member
    appointment: Appointment = None
    if (
        slot.physician
        and slot.physician.provider
        and slot.physician.provider.user
        and slot.source == AppointmentSource.LUCIAN
    ):
        logger.info("%s: Dynamic scheduling enabled for physician %d", log_prefix, slot.physician.pk)
        appointment, error = book_appointment_slot(slot, user, extra_params)
    else:
        description = extra_params.get("description", "")
        symptom_ids = extra_params.get("symptom_ids", None)
        other_symptoms = extra_params.get("other_symptoms", None)
        appointment, error = try_claim_appointment_slot(
            request,
            slot.elation_id,
            {"description": format_appointment_description(description, symptom_ids, other_symptoms)},
            user,
        )
        if appointment:
            appointment.description = description
            if symptom_ids:
                appointment.symptoms.set(symptom_ids)
            if other_symptoms or appointment.other_symptoms:
                appointment.other_symptoms = other_symptoms
            appointment.save(update_fields=["description", "other_symptoms"])

    return appointment, error


def format_appointment_description(description, symptom_ids, other_symptoms):
    symptoms: List[str] = []
    if symptom_ids:
        symptoms = [symptom.label for symptom in Symptom.objects.filter(id__in=symptom_ids).iterator()]
    if other_symptoms:
        symptoms.append(other_symptoms)
    return f"{description}. Symptoms: {', '.join(symptoms)}" if len(symptoms) else description
