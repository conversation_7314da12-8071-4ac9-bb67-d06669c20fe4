import json
import logging
from datetime import date, datetime, timedelta
from datetime import timezone as datetime_timezone
from unittest import mock
from uuid import uuid4

from django.conf import settings
from django.contrib.auth.models import Group
from django.db.models import Q
from django.db.models.query import QuerySet
from django.test.utils import override_settings
from django.utils import timezone
from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from faker import Faker
from psycopg2.extras import DateRange
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_409_CONFLICT,
)

from firefly.core.alias.models import get_content_type
from firefly.core.feature.testutils import override_flag
from firefly.core.roles.models import Role
from firefly.core.services.elation.client.client import ElationClient
from firefly.core.services.elation.sync.mixins.object_to_elation import ObjectToElationR<PERSON>ord
from firefly.core.services.flume.constants import CoverageStatus
from firefly.core.tests.client import FireflyTestAPIClient
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import MD_ROLE, NP_ROLE, RiskScore
from firefly.core.user.factories import PatientUserFactory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models import Person, User
from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.constants import (
    OLD_MEMBER_DATE,
    WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import (
    AppointmentFactory,
    AppointmentSlotFactory,
    SymptomFactory,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.slots.api import (
    get_physicians_eligible_for_scheduling,
    try_claim_appointment_slot,
)
from firefly.modules.appointment.tasks import publish_appointment_to_elation_async
from firefly.modules.cases.factories import CaseCategoryFactory
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.eligibility.models import EligibilityRecord
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.insurance.constants import (
    ContractAttributionType,
    ContractPMPMType,
    EmployerName,
)
from firefly.modules.insurance.factories import ContractFactory
from firefly.modules.insurance.models import Employer, InsuranceMemberInfo, InsurancePayer
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.physician.factories import PhysicianFactory
from firefly.modules.physician.models import Physician
from firefly.modules.programs.constants import BenefitProgramStatus
from firefly.modules.programs.models import Program, ProgramEnrollment
from firefly.modules.programs.primary_care.models import PrimaryCareProgramInfo
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.schedule.constants import (
    CASE_CATEGORY_APPT_SLOT_UNAVAILABLE,
    WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2,
)
from firefly.modules.schedule.factories import (
    AppointmentSlotsFactory,
    AppointmentTypeFactory,
    ProviderScheduleFactory,
    ShiftFactory,
)
from firefly.modules.schedule.models import (
    AppointmentType,
    AppointmentTypeContractMapping,
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    TimeSlot,
)
from firefly.modules.schedule.tasks import create_slots_for_provider_shift
from firefly.modules.statemachines.factories import DefaultStateMachineFactory
from firefly.modules.states.models import State
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)

# This is used to show Video-New appoitments for booking based on the risk score.
# Ex: HIGH - 2 days. It shows appointments that are greater than 2 days(24hrs) from now.
APPOINTMENT_START_TIME_BY_RISK_SCORE = {
    RiskScore.HIGH: timedelta(days=1),
    RiskScore.INTERMEDIATE: timedelta(days=3),
    RiskScore.LOW: timedelta(days=4),
}


# Simulate current system time is 8:00AM EDT
@mock.patch(
    "firefly.modules.appointment.utils.timezone.now",
    return_value=datetime.combine(
        timezone.now().date(),
        settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
        tzinfo=timezone.get_default_timezone(),
    ),
)
class AppointmentSlotLicensingTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.np_group, _ = Group.objects.get_or_create(name=NP_ROLE)
        # Reset the time the patient is created
        self.patient.created_at = timezone.now()
        self.patient.save()
        # Setup serviceable states
        self.MA = State.objects.get(abbreviation="MA")
        self.NH = State.objects.get(abbreviation="NH")
        self.IL = State.objects.get(abbreviation="IL")
        self.CA = State.objects.get(abbreviation="CA")

        # Since we're sitting on top of migrations ensure providers setup explicitly
        for provider in ProviderDetail.objects.all():
            provider.delete()
        MA_provider = ProviderDetailFactory.create()
        self.np_group.user_set.add(MA_provider.user)
        self.MA_physician = MA_provider.physician
        self.MA_physician.practicing_states.add(self.MA)

        MA_provider_not_NP = ProviderDetailFactory.create()
        self.MA_physician_not_NP = MA_provider_not_NP.physician
        self.MA_physician_not_NP.practicing_states.add(self.MA)

        NH_provider = ProviderDetailFactory.create()
        self.NH_physician = NH_provider.physician
        self.NH_physician.practicing_states.add(self.NH)

        MA_NH_provider = ProviderDetailFactory.create()
        self.np_group.user_set.add(MA_NH_provider.user)
        self.MA_NH_physician = MA_NH_provider.physician
        self.MA_NH_physician.practicing_states.add(self.MA)
        self.MA_NH_physician.practicing_states.add(self.NH)

        IL_provider = ProviderDetailFactory.create()
        self.IL_physician = IL_provider.physician
        self.IL_physician.practicing_states.add(self.IL)

        # Set some mocked appointment timing
        two_days = timezone.now().date() + timedelta(days=2)
        tzone = timezone.get_default_timezone()
        next_week = timezone.now().date() + timedelta(days=7)
        self.two_days_am_restart = datetime.combine(two_days, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)
        self.next_week_am_restart = datetime.combine(
            next_week, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone
        )

        # Create some appointments for MA and IL, but prove only MA licensed providers are available
        reason = AppointmentReason.VIDEO
        time_slot_type = "appointment_slot"
        # Let'ss say this is actually an established patient so they would be eligible to see video visits
        Appointment.objects.create(
            reason=reason,
            patient=self.patient,
            time_slot_type=time_slot_type,
            start=self.two_days_am_restart - timedelta(days=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5010,  # Hacky way to bypass elation_id constraint
            status=AppointmentStatus.CHECKED_OUT.value,
            patient_joined_video=True,
        )
        Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.two_days_am_restart + timedelta(hours=1) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5000,  # Hacky way to bypass elation_id constraint
        )
        # A focused visit which should not be returned
        Appointment.objects.create(
            reason=AppointmentReason.FOCUSED_VISIT,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.two_days_am_restart + timedelta(hours=1) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            source="Lucian",
        )
        Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.next_week_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_NH_physician,
            practice=self.MA_NH_physician.practice,
            elation_id=self.MA_NH_physician.id + 5000,  # Hacky way to bypass elation_id constraint
        )
        Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.next_week_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.IL_physician,
            practice=self.IL_physician.practice,
            elation_id=self.IL_physician.id + 5000,  # Hacky way to bypass elation_id constraint
        )
        Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.next_week_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician_not_NP,
            practice=self.MA_physician_not_NP.practice,
            elation_id=self.MA_physician_not_NP.id + 5000,  # Hacky way to bypass elation
        )

        # Ensure patient is in the member onboarding state
        self.patient.onboarding_state.to_signedup()

        default_state_machine = DefaultStateMachineFactory.create()
        self.case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_APPT_SLOT_UNAVAILABLE, state_machine_definition=default_state_machine
        )

    def test_licensed_physicians(self, _mock_timezone_now):
        appointment_type = AppointmentTypeFactory(exempt_from_state_licensing=None)
        # No Providers practing in the state
        person = PersonUserFactory.create()
        person.insurance_info.state = "CA"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )

        # Some Appointment Types do not depend on state licensing
        appointment_type_exempt = AppointmentTypeFactory(exempt_from_state_licensing=True)
        all_physicians = Physician.objects.filter(
            (Q(provider__isnull=True) | Q(provider__user__is_active=True))
        ).order_by(
            "created_at",
            "id",
        )
        self.assertTrue(all_physicians.exists())
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type_exempt),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )
        # add mapping for all physicians
        for physician in all_physicians.iterator():
            PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=physician, appointment_type=appointment_type_exempt
            )
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type_exempt),
            {
                "physicians": list(all_physicians),
                "reasons_for_unavailability": [],
            },
        )

        # If we aren't able to find an Appointment Type, that exception does not apply
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, None),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )

        # Multiple providers in state, some crossing states
        person.insurance_info.state = "MA"
        person.insurance_info.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.MA_physician, appointment_type=appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.MA_physician_not_NP, appointment_type=appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.MA_NH_physician, appointment_type=appointment_type
        )
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [self.MA_physician, self.MA_physician_not_NP, self.MA_NH_physician],
                "reasons_for_unavailability": [],
            },
        )
        person.insurance_info.state = "NH"
        person.insurance_info.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.NH_physician, appointment_type=appointment_type
        )
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [self.NH_physician, self.MA_NH_physician],
                "reasons_for_unavailability": [],
            },
        )
        # Remove licenses - verify that provider does not show up
        self.MA_NH_physician.practicing_states.set([self.MA])
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [self.NH_physician],
                "reasons_for_unavailability": [],
            },
        )

        # Single providers in state
        person.insurance_info.state = "IL"
        person.insurance_info.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.IL_physician, appointment_type=appointment_type
        )
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [self.IL_physician],
                "reasons_for_unavailability": [],
            },
        )
        # Prove case insensitivity
        person.insurance_info.state = "il"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [self.IL_physician],
                "reasons_for_unavailability": [],
            },
        )

        # Patient w/ an invalid state
        person.insurance_info.state = "XX"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )

        # Patient w/ a missing state
        person.insurance_info.state = None
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )

        # Patient w/o an insurance_info
        person.insurance_info = None
        person.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, appointment_type),
            {
                "physicians": [],
                "reasons_for_unavailability": [],
            },
        )

    @mock.patch("firefly.core.user.signals.update_auth0_user")
    def test_inactive_physician(self, _mock_timezone_now, mock_update_auth0_user):
        # Create physician without provider model
        physician_without_provider = PhysicianFactory()
        physician_without_provider.practicing_states.add(self.MA)
        # Create active provider with appointment
        inactive_provider = ProviderDetailFactory.create()
        inactive_physician = inactive_provider.physician
        inactive_physician.practicing_states.add(self.MA)
        Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            patient=None,
            time_slot_type="appointment_slot",
            start=self.two_days_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=inactive_physician,
            practice=inactive_physician.practice,
            elation_id=inactive_physician.id + 5000,  # Hacky way to bypass elation
        )
        # Ensure patient is set for MA.
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 4)
        # Deactivate provider
        inactive_provider.user.is_active = False
        inactive_provider.user.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 3)

    @mock.patch("firefly.core.user.signals.update_auth0_user")
    def test_inactive_physician_health_guide(self, _mock_timezone_now, mock_update_auth0_user):
        # Create physician without provider model
        physician_without_provider = PhysicianFactory()
        physician_without_provider.practicing_states.add(self.MA)
        # Create active provider with appointment
        inactive_provider = ProviderDetailFactory.create()
        inactive_physician = inactive_provider.physician
        inactive_physician.practicing_states.add(self.MA)
        appt: Appointment = Appointment.objects.create(
            reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            patient=None,
            time_slot_type="appointment_slot",
            start=self.two_days_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=inactive_physician,
            practice=inactive_physician.practice,
            elation_id=inactive_physician.id + 5000,  # Hacky way to bypass elation
        )
        # Ensure patient is set for MA.
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.HEALTH_GUIDE_BOOKABLE}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["next_slot"]["appointment_id"], appt.pk)
        # Deactivate provider
        inactive_provider.user.is_active = False
        inactive_provider.user.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.HEALTH_GUIDE_BOOKABLE}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

    def test_unavailable_physician(self, _mock_timezone_now):
        # Ensure patient is set for MA.
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        # Create new NP provider and add to care team
        provider = ProviderDetailFactory.create()
        internal_role, _ = Role.objects.get_or_create(role_name="Nurse Practitioner")
        provider.internal_role = internal_role
        provider.save()
        self.np_group.user_set.add(provider.user)
        physician = provider.physician
        physician.practicing_states.add(self.MA)
        appointment = Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            patient=None,
            time_slot_type="appointment_slot",
            start=self.two_days_am_restart + timedelta(hours=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=physician,
            practice=physician.practice,
            elation_id=physician.id + 5000,  # Hacky way to bypass elation
        )
        self.patient.person.care_team.add(provider)
        # Four appointments should be available and the care team NP appointment should be first
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 4)
        self.assertEqual(response.json()[0]["id"], physician.pk)
        # Four appointments should be available
        appointment.start = self.two_days_am_restart + timedelta(weeks=4)
        appointment.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 4)
        self.assertTrue(response.json()[-1]["is_available"])

    def test_api_next_available_slot(self, _mock_timezone_now):
        # Ensure patient is set for MA.
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()

        # Ensure we enforce user is a patient
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.provider_client.get(url)
        self.assertEqual(response.status_code, 403)

        # Valid use case, 2 physicians available
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 3)

        # Valid use case, no appointments available
        self.patient.person.insurance_info.state = "CA"
        self.patient.person.insurance_info.save()
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)

        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

        start_time = timezone.now() + timedelta(days=2)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.AWV_ESTABLISHED}&from={start_time_str}"
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(start_time + timedelta(weeks=5)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(start_time + timedelta(weeks=5) + timedelta(days=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(timezone.now() + timedelta(hours=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)

    def test_api_next_available_slot_5_weeks(self, _mock_timezone_now):
        start_time = timezone.now() + timedelta(days=2)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.AWV_ESTABLISHED}&from={start_time_str}"
        # after 5 week
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(start_time + timedelta(weeks=5) + timedelta(days=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        # after 5 week
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(start_time + timedelta(weeks=5) + timedelta(days=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        # start before start_time
        AppointmentFactory(
            patient=None,
            physician=self.MA_physician,
            start=(timezone.now() + timedelta(hours=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_ESTABLISHED,
            visible=True,
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)


class AppointmentSlotUtilsTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # Ensure patient is set for MA.
        self.patient.person.insurance_info.state = "MA"
        self.patient.person.insurance_info.save()
        # Create provider in MA
        MA_provider = ProviderDetailFactory.create()
        MA_provider.title = "NP"
        MA_provider.save()
        self.MA_physician = MA_provider.physician
        self.MA_physician.practicing_states.add(State.objects.get(abbreviation="MA"))
        # Create default appointment for MA 1 week from now
        reason = AppointmentReason.VIDEO
        time_slot_type = "appointment_slot"
        tzone = timezone.get_default_timezone()
        today = timezone.now().date()
        tomorrow = today + timedelta(days=1)
        self.today_pm_cutoff = datetime.combine(today, settings.NEW_APPOINTMENT_CUTOFF_TIME["PM"], tzinfo=tzone)
        self.today_am_restart = datetime.combine(today, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)
        self.tomorrow_am_restart = datetime.combine(tomorrow, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)
        # Let'ss say this is actually an established patient so they would be eligible to see video visits
        Appointment.objects.create(
            reason=reason,
            patient=self.patient,
            time_slot_type=time_slot_type,
            start=self.today_am_restart - timedelta(days=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5010,  # Hacky way to bypass elation_id constraint
            status=AppointmentStatus.CHECKED_OUT.value,
            patient_joined_video=True,
        )
        # Appointment today at 9AM EDT
        self.appt2 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.today_am_restart + timedelta(hours=1),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5001,  # Hacky way to bypass elation
        )
        # Appointment today at 12PM EDT
        self.appt3 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.today_am_restart + timedelta(hours=4),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5002,  # Hacky way to bypass elation
        )
        # Appointment today at 5PM EDT
        self.appt4 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.today_pm_cutoff + timedelta(hours=2),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5003,  # Hacky way to bypass elation
        )
        # Appointment tomorrow at 7AM EDT
        self.appt5 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.tomorrow_am_restart - timedelta(hours=1),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5004,  # Hacky way to bypass elation
        )
        # Appointment tomorrow at 9AM EDT
        self.appt6 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.tomorrow_am_restart + timedelta(hours=1),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5005,  # Hacky way to bypass elation
        )
        # Appointment tomorrow at 12PM EDT
        self.appt7 = Appointment.objects.create(
            reason=reason,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.tomorrow_am_restart + timedelta(hours=4),
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            elation_id=self.MA_physician.id + 5006,  # Hacky way to bypass elation
        )
        # A focused visit which should not be returned
        Appointment.objects.create(
            reason=AppointmentReason.FOCUSED_VISIT,
            patient=None,
            time_slot_type=time_slot_type,
            start=self.today_am_restart + timedelta(hours=1) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.MA_physician,
            practice=self.MA_physician.practice,
            source="Lucian",
        )
        self.url = f"/appointment/slot/available-physicians?reason={reason}"
        self.date_format = "%Y-%m-%dT%H:%M:%SZ"

    # Simulate current system time is 7:50AM EDT
    @mock.patch(
        "firefly.modules.appointment.utils.timezone.now",
        return_value=datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
            tzinfo=timezone.get_default_timezone(),
        )
        - timedelta(minutes=10),
    )
    def test_member_slot_times(self, _mock_timezone_now):
        self.patient.onboarding_state.to_signedup()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        # Let's set the patient's creation date to yesterday so we can get Appointment 2 as the next available date
        next_slot = self._get_next_slot_time(self.today_am_restart - timedelta(days=1))
        self.assertEqual(next_slot, self.appt2.start.strftime(self.date_format))

    # Simulate current system time is 3:20PM EDT
    @mock.patch(
        "firefly.modules.appointment.utils.timezone.now",
        return_value=datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["PM"],
            tzinfo=timezone.get_default_timezone(),
        )
        + timedelta(minutes=20),
    )
    def test_non_member_slot_times_after_pm_cutoff(self, _mock_timezone_now):
        self.patient.onboarding_state.to_initialized()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.INITIALIZED)
        # User was created at 7:40 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart - timedelta(minutes=20))
        self.assertEqual(next_slot, self.appt4.start.strftime(self.date_format))
        # User was created at 8:10 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt4.start.strftime(self.date_format))
        # User was created at 12:00 PM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(hours=4))
        self.assertEqual(next_slot, self.appt4.start.strftime(self.date_format))
        # User was created at 2:50 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff - timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt5.start.strftime(self.date_format))
        # User was created at 3:10 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt7.start.strftime(self.date_format))

    # Simulate current system time is 7:50AM EDT
    @mock.patch(
        "firefly.modules.appointment.utils.timezone.now",
        return_value=datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
            tzinfo=timezone.get_default_timezone(),
        )
        - timedelta(minutes=10),
    )
    def test_non_member_slot_times_before_am_restart(self, _mock_timezone_now):
        self.patient.onboarding_state.to_initialized()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.INITIALIZED)
        # User was created at 7:40 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart - timedelta(minutes=20))
        self.assertEqual(next_slot, self.appt3.start.strftime(self.date_format))
        # User was created at 8:10 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt3.start.strftime(self.date_format))
        # User was created at 12:00 PM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(hours=4))
        self.assertEqual(next_slot, self.appt4.start.strftime(self.date_format))
        # User was created at 2:50 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff - timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt5.start.strftime(self.date_format))
        # User was created at 3:10 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt7.start.strftime(self.date_format))

    # Simulate current system time is 8:20AM EDT
    @mock.patch(
        "firefly.modules.appointment.utils.timezone.now",
        return_value=datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
            tzinfo=timezone.get_default_timezone(),
        )
        + timedelta(minutes=20),
    )
    def test_non_member_slot_times_after_am_restart(self, _mock_timezone_now):
        self.patient.onboarding_state.to_initialized()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.INITIALIZED)
        # User was created at 7:40 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart - timedelta(minutes=20))
        self.assertEqual(next_slot, self.appt3.start.strftime(self.date_format))
        # User was created at 8:10 AM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt3.start.strftime(self.date_format))
        # User was created at 12:00 PM EDT
        next_slot = self._get_next_slot_time(self.today_am_restart + timedelta(hours=4))
        self.assertEqual(next_slot, self.appt4.start.strftime(self.date_format))
        # User was created at 2:50 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff - timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt5.start.strftime(self.date_format))
        # User was created at 3:10 PM EDT
        next_slot = self._get_next_slot_time(self.today_pm_cutoff + timedelta(minutes=10))
        self.assertEqual(next_slot, self.appt7.start.strftime(self.date_format))

    def _get_next_slot_time(self, created_at):
        self.patient.created_at = created_at
        self.patient.save()
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        next_slot = response.json()[0]["next_slot"]["scheduled_date"]
        return next_slot


# Simulate current system time is 8:00AM EDT
@mock.patch(
    "firefly.modules.appointment.utils.timezone.now",
    return_value=datetime.combine(
        timezone.now().date(),
        settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
        tzinfo=timezone.get_default_timezone(),
    ),
)
class AppointmentSlotTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # Reset the time the patient is created
        self.patient.created_at = timezone.now()
        self.patient.save()

        self.old_patient = PatientUserFactory.create()
        self.old_person = Person.objects.create(user=self.old_patient)
        self.old_patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - timedelta(days=1)
        self.old_patient.onboarding_state.save()

        default_state_machine = DefaultStateMachineFactory.create()
        self.case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_APPT_SLOT_UNAVAILABLE, state_machine_definition=default_state_machine
        )

        self.appt_slot_patient_user = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()
        self.insurance_info_hmo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="hmo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.appt_slot_person = Person.objects.create(
            user=self.appt_slot_patient_user,
            first_name="Test",
            last_name="Test",
            email=self.appt_slot_patient_user.email,
            insurance_info=self.insurance_info_hmo,
        )
        self.appt_slot_person.save()

        MA = State.objects.get(abbreviation="MA")

        self.provider_1 = ProviderDetailFactory.create()
        self.provider_1.physician.practicing_states.add(MA)
        self.provider_1.title = "MD"
        self.provider_1.save()
        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_md.user_set.add(self.provider_1.user)

        self.provider_2 = ProviderDetailFactory.create()
        self.provider_2.physician.provider = self.provider_2
        self.provider_2.physician.save()
        self.provider_2.physician.practicing_states.add(MA)
        self.provider_2.title = "NP"
        internal_role, _ = Role.objects.get_or_create(role_name="Nurse Practitioner")
        self.provider_2.internal_role = internal_role
        self.provider_2.save()
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_np.user_set.add(self.provider_2.user)
        self.scheduled_date = datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
            tzinfo=timezone.get_default_timezone(),
        ) + timedelta(hours=2)
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider_2.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_2,
            effective_period=DateRange(self.scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        self.provider_3 = ProviderDetailFactory.create()
        group_np.user_set.add(self.provider_3.user)

        self.provider_4 = ProviderDetailFactory.create()
        self.provider_4.physician.practicing_states.add(MA)
        self.provider_4.title = "NP"
        self.provider_4.internal_role = internal_role
        self.provider_4.save()
        group_md, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_md.user_set.add(self.provider_4.user)

        self.provider_5 = ProviderDetailFactory.create()
        self.provider_5.physician.practicing_states.add(MA)
        self.provider_5.title = "NP"
        self.provider_5.internal_role = internal_role
        self.provider_5.save()
        group_md, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_md.user_set.add(self.provider_5.user)

        # Set some mocked appointment timing
        two_days = timezone.now().date() + timedelta(days=2)
        tzone = timezone.get_default_timezone()
        self.two_days_am_restart = datetime.combine(two_days, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)

        # Let'ss say this is actually an established patient so they would be eligible to see video visits
        Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            patient=self.appt_slot_patient_user,
            time_slot_type="appointment_slot",
            start=self.two_days_am_restart - timedelta(days=2) + settings.NEW_APPOINTMENT_BUFFER,
            physician=self.provider_4.physician,
            practice=self.provider_4.physician.practice,
            elation_id=self.provider_4.physician.id + 5010,  # Hacky way to bypass elation_id constraint
            status=AppointmentStatus.CHECKED_OUT.value,
            patient_joined_video=True,
        )

        appointment = Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            physician_id=self.provider_2.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=90) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=9999998,
        )
        appointment.save()
        appointment = Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            physician_id=self.provider_3.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=80) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=9999999,
        )
        appointment.save()
        # Ensure appointments for providers outside of care team are not prioritized
        appointment = Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            physician_id=self.provider_4.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=60)),
            time_slot_type="appointment_slot",
            elation_id=9999997,
        )
        appointment.save()
        appointment = Appointment.objects.create(
            reason=AppointmentReason.VIDEO,
            physician_id=self.provider_5.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=70)),
            time_slot_type="appointment_slot",
            elation_id=9999996,
        )
        appointment.save()

        self.appt_slot_person.care_team.add(self.provider_1)
        self.appt_slot_person.care_team.add(self.provider_2)
        self.appt_slot_person.care_team.add(self.provider_3)
        self.appt_slot_person.care_team.add(self.provider_5)

        # Ensure patient is in the member onboarding state
        self.patient.onboarding_state.to_signedup()
        self.appt_slot_patient_user.onboarding_state.to_signedup()

        # Upon setup, 3 appointments should be available
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        self.client.force_authenticate(user=self.appt_slot_patient_user)
        response = self.client.get(url)
        appointments = response.json()
        self.assertEqual(len(appointments), 3)

        CaseCategoryFactory(unique_key="annual_wellness_visit")

    def test_available_slots_by_physician_is_sorted_by_scheduled_date(self, _mock_timezone_now):
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        self.client.force_authenticate(user=self.appt_slot_patient_user)
        response = self.client.get(url)
        appointments = response.json()
        next_appt = appointments[0]
        care_team = self.appt_slot_person.care_team.all().values_list("physician_id", flat=True)

        # Ensure the last entry is the NP that is not in the care team
        self.assertEqual(appointments[-1]["id"], self.provider_4.physician.id)
        self.assertIn(next_appt["id"], care_team)
        self.assertTrue(next_appt["in_care_team"])
        self.assertEqual(next_appt["title"], "NP")

        # After adding provider_4 to the care team, the last provider should change to the NP with the latest
        # appointment time
        self.appt_slot_person.care_team.add(self.provider_4)
        response = self.client.get(url)
        appointments = response.json()
        self.assertEqual(appointments[-1]["id"], self.provider_2.physician.id)

    def test_next_available_appt_request(self, _mock_timezone_now):
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        now = timezone.now()
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician

        # Create video appointment that is too soon to see
        Appointment.objects.create(
            elation_id=990001,
            physician=physician_1,
            start=(now + timedelta(minutes=5)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        video_slots_url = "/appointment/slot/?physician={pid}&reason=Video".format(pid=physician_1.id)
        self.client.force_authenticate(user=self.appt_slot_patient_user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 0)

        # Create video appointment that we can see
        Appointment.objects.create(
            elation_id=990002,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(minutes=60)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        video_slots_url = "/appointment/slot/?physician={pid}&reason=Video".format(pid=physician_1.id)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 1)
        self.assertEqual(available_slots[0]["id"], 990002)

        # Test date filters, and that we filter out appointments that are too soon
        start_time = now.strftime(date_format)
        end_time = (self.two_days_am_restart + timedelta(minutes=65)).strftime(date_format)
        slots_url_with_timefilters = ("/appointment/slot/?start_time={start_time}&end_time={end_time}").format(
            start_time=start_time, end_time=end_time
        )
        next_avail_resp = self.client.get(slots_url_with_timefilters)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 2)

        # Create appt in past
        Appointment.objects.create(
            elation_id=990005,
            physician=physician_1,
            start=(now - timedelta(minutes=15)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason="Video",
        )
        # Test date filters return empty if in the past
        start_time = (now - timedelta(minutes=60)).strftime(date_format)
        end_time = now.strftime(date_format)
        slots_url_with_timefilters = ("/appointment/slot/?start_time={start_time}&end_time={end_time}").format(
            start_time=start_time, end_time=end_time
        )
        next_avail_resp = self.client.get(slots_url_with_timefilters)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 0)

    def test_appointment_slot_for_users_in_plan_elect_period(self, _mock_timezone_now):
        person_in_plan_elect_period = PersonUserFactory()
        now = timezone.now()
        enrollment_period = (now + timedelta(days=60), now + timedelta(days=300))
        add_person_to_program(
            person=person_in_plan_elect_period,
            program_uid=ProgramCodes.BENEFIT,
            data={"enrollment_period": enrollment_period},
        )
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        MA = State.objects.get(abbreviation="MA")
        person_in_plan_elect_period.insurance_info.state = "MA"
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        EligibilityRecord.objects.create(
            member=person_in_plan_elect_period,
            employee=person_in_plan_elect_period,
            effective_date=enrollment_period[0],
            termination_date=enrollment_period[1],
            coverage_status=CoverageStatus.ACTIVE,
        )

        # Create video appointment in two days
        Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(hours=55)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
        )
        video_new_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(physician=physician_1, appointment_type=video_type)
        PhysicianAppointmentTypeMapping.objects.get_or_create(physician=physician_1, appointment_type=video_new_type)
        video_slots_url = "/appointment/slot/?physician={pid}&reason=Video".format(pid=physician_1.id)
        self.client.force_authenticate(user=person_in_plan_elect_period.user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 0)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_physicians_resp = self.client.get(next_available_url)
        next_avail_physicians = json.loads(next_avail_physicians_resp.content)
        self.assertEqual(len(next_avail_physicians), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

        # Create video appointment after effective start date
        Appointment.objects.create(
            elation_id=190002,
            physician=physician_1,
            start=(now + timedelta(days=61)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_slots_url = "/appointment/slot/?physician={pid}&reason={reason}".format(
            pid=physician_1.id, reason=AppointmentReason.VIDEO_NEW_PATIENT
        )
        self.client.force_authenticate(user=person_in_plan_elect_period.user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 1)

        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_physicians_resp = self.client.get(next_available_url)
        next_avail_physicians = json.loads(next_avail_physicians_resp.content)
        self.assertEqual(len(next_avail_physicians), 1)
        self.assertEqual(
            next_avail_physicians[0].get("next_slot", {}).get("scheduled_date"),
            (now + timedelta(days=61)).strftime(date_format),
        )

        # set eligibility_record start date to None
        eligibility_record = EligibilityRecord.objects.get(member=person_in_plan_elect_period)
        eligibility_record.effective_date = None
        eligibility_record.save(update_fields=["effective_date"])

        video_slots_url = "/appointment/slot/?physician={pid}&reason={reason}".format(
            pid=physician_1.id, reason=AppointmentReason.VIDEO_NEW_PATIENT
        )
        self.client.force_authenticate(user=person_in_plan_elect_period.user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 1)

        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_physicians_resp = self.client.get(next_available_url)
        next_avail_physicians = json.loads(next_avail_physicians_resp.content)
        self.assertEqual(len(next_avail_physicians), 1)
        next_avail_appointment = list(
            filter(lambda next_avail_physician: next_avail_physician["id"] == physician_1.id, next_avail_physicians)
        )
        self.assertEqual(len(next_avail_appointment), 1)
        if next_avail_appointment:
            self.assertEqual(
                next_avail_appointment[0].get("next_slot", {}).get("scheduled_date"),
                (now + timedelta(days=61)).strftime(date_format),
            )

        # set eligibility_record start date to past date
        eligibility_record = EligibilityRecord.objects.get(member=person_in_plan_elect_period)
        eligibility_record.effective_date = now - timedelta(days=30)
        eligibility_record.save(update_fields=["effective_date"])

        elected_enrollment = ProgramEnrollment.objects.get(
            person=person_in_plan_elect_period, program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
        )
        elected_enrollment.period = (now - timedelta(days=60), now - timedelta(days=30))
        elected_enrollment.save()
        enrolled_enrollment = ProgramEnrollment.objects.get(
            person=person_in_plan_elect_period, program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
        )
        enrolled_enrollment.period = (now - timedelta(days=30), enrolled_enrollment.period.upper)
        enrolled_enrollment.save()

        end_time = (now + timedelta(weeks=12)).strftime(date_format)
        video_slots_url = "/appointment/slot/?physician={pid}&reason={reason}&end_time={end_time}".format(
            pid=physician_1.id, reason=AppointmentReason.VIDEO_NEW_PATIENT, end_time=end_time
        )
        self.client.force_authenticate(user=person_in_plan_elect_period.user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 2)

        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_physicians_resp = self.client.get(next_available_url)
        next_avail_physicians = json.loads(next_avail_physicians_resp.content)
        self.assertEqual(len(next_avail_physicians), 1)
        next_avail_appointment = list(
            filter(lambda next_avail_physician: next_avail_physician["id"] == physician_1.id, next_avail_physicians)
        )
        self.assertEqual(len(next_avail_appointment), 1)
        if next_avail_appointment:
            self.assertEqual(
                next_avail_appointment[0].get("next_slot", {}).get("scheduled_date"),
                (now + timedelta(hours=55)).strftime(date_format),
            )

    def test_appointment_slot_order(self, _mock_timezone_now):
        self.client.force_authenticate(user=self.appt_slot_patient_user)
        response = self.client.get("/appointment/slot/")
        appointments = response.json()
        next_appt = appointments[0]
        care_team = self.appt_slot_person.care_team.all().values_list("physician_id", flat=True)
        self.assertIn(next_appt["physician"], care_team)

    def test_cannot_book_multiple_appointments_on_same_day(self, _mock_timezone_now):
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        self.client.force_authenticate(user=self.appt_slot_patient_user)
        response = self.client.get(url)
        appointments = response.json()
        self.assertEqual(len(appointments), 3)
        appointment = Appointment.objects.create(
            patient=self.appt_slot_patient_user,
            physician_id=self.provider_2.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=100) + settings.NEW_APPOINTMENT_BUFFER),
            elation_id=9999995,
        )
        appointment.save()
        response = self.client.get(url)
        appointments = response.json()
        self.assertEqual(len(appointments), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

    def test_surface_previous_provider_for_requires_patient_booking_with_previous_provider(self, *args):
        MA = State.objects.get(abbreviation="MA")
        appointment_type = AppointmentType.objects.create(
            unique_key=str(uuid4()),
            requires_patient_booking_with_previous_provider=True,
            duration=60,
            buffer_time_in_minutes=0,
        )
        url = f"/appointment/slot/available-physicians?reason={appointment_type.unique_key}"
        self.client.force_authenticate(user=self.appt_slot_patient_user)

        # create appts
        bh1 = ProviderDetailFactory.create()
        bh1.physician.practicing_states.add(MA)
        bh1.title = "NP"
        bh1.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=bh1.physician, appointment_type=appointment_type
        )

        bh2 = ProviderDetailFactory.create()
        bh2.physician.practicing_states.add(MA)
        bh2.title = "NP"
        bh2.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=bh2.physician, appointment_type=appointment_type
        )

        # early appt but with a different provider
        appointment_different_bh = Appointment.objects.create(
            physician_id=bh1.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=60)),
            time_slot_type="appointment_slot",
            elation_id=8888888,
            reason=appointment_type.unique_key,
        )
        appointment_different_bh.save()

        # late appt with the same bh provider as before
        appointment_same_bh = Appointment.objects.create(
            physician_id=bh2.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=70)),
            time_slot_type="appointment_slot",
            elation_id=8888889,
            reason=appointment_type.unique_key,
        )
        appointment_same_bh.save()

        response = self.client.get(url)
        appointments = response.json()

        # We should see all appointments available (2)
        self.assertEqual(len(appointments), 2)

        # Let's say the patient did have an appointment in the past. We should only see appts for the last seen provider
        old_bh = Appointment.objects.create(
            physician_id=bh2.physician.id,
            start=(timezone.now() - timedelta(days=5)),
            time_slot_type="appointment_slot",
            elation_id=8888880,
            status=AppointmentStatus.CHECKED_OUT,
            patient_joined_video=True,
            patient=self.appt_slot_patient_user,
            reason=appointment_type.unique_key,
        )
        old_bh.save()

        response = self.client.get(url)
        appointments = response.json()

        # We should only see one entry for the previously seen BH guide
        self.assertEqual(len(appointments), 1)
        self.assertEqual(appointments[0]["id"], bh2.physician.id)

    def test_new_patient_appointment_type(self, _mock_timezone_now):
        physician_1 = self.provider_1.physician
        # Let's create a handful of various types of appointments.
        # This appointment will be booked.
        new_appointment = Appointment.objects.create(
            elation_id=990005,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(minutes=200)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        # These appointments will all be a day in advance since we cannot book two appointments in one day.
        Appointment.objects.create(
            elation_id=990002,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(days=1) + timedelta(minutes=60)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        Appointment.objects.create(
            elation_id=990003,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(days=1) + timedelta(minutes=100)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        Appointment.objects.create(
            elation_id=990004,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(days=1) + timedelta(minutes=150)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        # We should not see this appointment since its a Video-New type and we will have already booked and attended
        # an appointment.
        Appointment.objects.create(
            elation_id=990006,
            physician=physician_1,
            start=(self.two_days_am_restart + timedelta(days=1) + timedelta(minutes=250)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )

        # Test NextAvailableAppointmentSlotByPhysician API
        next_available_url = "/appointment/slot/available-physicians?reason=Video"
        self.client.force_authenticate(user=self.patient)
        next_avail_resp = self.client.get(next_available_url)
        slot = json.loads(next_avail_resp.content)
        self.assertTrue(slot[0]["next_slot"]["reason"], AppointmentReason.VIDEO_NEW_PATIENT)

        # Test AppointmentSlotList API
        video_slots_url = "/appointment/slot/?physician={pid}&reason=Video".format(pid=physician_1.id)
        self.client.force_authenticate(user=self.patient)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 2)
        for slot in available_slots:
            self.assertEqual(slot["reason"], AppointmentReason.VIDEO_NEW_PATIENT)

        # Let's book and attend an appointment
        new_appointment.patient = self.patient
        new_appointment.patient_joined_video = True
        new_appointment.status = AppointmentStatus.CHECKED_OUT.value
        new_appointment.save()

        # We should only get back Video type appointments now
        # Test NextAvailableAppointmentSlotByPhysician API
        next_available_url = "/appointment/slot/available-physicians?reason=Video"
        self.client.force_authenticate(user=self.patient)
        next_avail_resp = self.client.get(next_available_url)
        slot = json.loads(next_avail_resp.content)
        self.assertTrue(slot[0]["next_slot"]["reason"], AppointmentReason.VIDEO)

        # Test AppointmentSlotList API
        self.client.force_authenticate(user=self.patient)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 3)
        for slot in available_slots:
            self.assertEqual(slot["reason"], AppointmentReason.VIDEO)

    @override_settings(APPOINTMENT_START_TIME_BY_RISK_SCORE=APPOINTMENT_START_TIME_BY_RISK_SCORE)
    def test_available_slots_by_risk_score(self, _mock_timezone_now):
        video_new_2_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        awv_new_2_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_NEW,
            visible=True,
        )
        video_new_3_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=3)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        awv_new_3_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=3)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_NEW,
            visible=True,
        )
        video_new_4_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=4)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        awv_new_4_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=4)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_NEW,
            visible=True,
        )

        # Add patient to primary care
        person_program_info = add_person_to_program(self.appt_slot_person, ProgramCodes.PRIMARY_CARE)

        person_program_info.risk_score = RiskScore.LOW
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["id"], video_new_4_days_out.elation_id)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_4_days_out.elation_id)
        # VIDEO visits shouldn't be filtered
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 4)
        url = f"/appointment/slot/?reason={AppointmentReason.AWV_NEW}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["id"], awv_new_4_days_out.elation_id)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.AWV_NEW}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], awv_new_4_days_out.elation_id)
        # VIDEO visits shouldn't be filtered
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 4)

        person_program_info.risk_score = RiskScore.INTERMEDIATE
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 2)
        available_slots = [video_new_3_days_out.elation_id, video_new_4_days_out.elation_id]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_3_days_out.elation_id)
        url = f"/appointment/slot/?reason={AppointmentReason.AWV_NEW}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 2)
        available_slots = [awv_new_3_days_out.elation_id, awv_new_4_days_out.elation_id]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.AWV_NEW}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], awv_new_3_days_out.elation_id)

        person_program_info.risk_score = RiskScore.HIGH
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 3)
        available_slots = [
            video_new_2_days_out.elation_id,
            video_new_4_days_out.elation_id,
            video_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_2_days_out.elation_id)
        url = f"/appointment/slot/?reason={AppointmentReason.AWV_NEW}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 3)
        available_slots = [
            awv_new_2_days_out.elation_id,
            awv_new_4_days_out.elation_id,
            awv_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.AWV_NEW}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], awv_new_2_days_out.elation_id)

        # If the patient doesn't have a risk score then the staggering logic should consider the
        # patient to be of LOW risk and stagger appointment based on LOW risk.
        person_program_info.risk_score = None
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 1)
        self.assertEqual(response[0]["id"], video_new_4_days_out.elation_id)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_4_days_out.elation_id)

    def test_urgent_visit_slots(self, mack_timezone_now):
        AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(hours=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.URGENT,
        )
        AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(hours=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        appt_video_new_outside_buffer = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=3)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        appt_video = 9999998
        url = "/appointment/slot/?physician={pid}".format(pid=self.provider_2.physician.id)
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 2)
        for slot in response:
            self.assertIn(slot["id"], [appt_video_new_outside_buffer.elation_id, appt_video])
        # Should be able to book urgent visit only if urgent_visit is set to True
        url = "/appointment/slot/?physician={pid}&reason={reason}".format(
            pid=self.provider_2.physician.id, reason=AppointmentReason.URGENT
        )
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 0)

        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.URGENT}"
        response = self.client.get(next_available_url)
        response = response.json()
        self.assertEqual(len(response), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

        self.appt_slot_patient_user.urgent_visit = True
        self.appt_slot_patient_user.save()

        url = "/appointment/slot/?physician={pid}".format(pid=self.provider_2.physician.id)
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 2)  # Will show only VIDEO_NEW slots, as Urgent slots are deprecated

    @override_settings(APPOINTMENT_START_TIME_BY_RISK_SCORE=APPOINTMENT_START_TIME_BY_RISK_SCORE)
    def test_available_slots_based_on_insurance_type_old(self, _mock_timezone_now):
        # If an HMO patient is high risk, they should see the max between the high risk config (1 day) and the default
        # config (2 days). So they should ONLY see 2 appts with the old config: 1 day for high risk patients.

        self.appt_slot_patient_user.created_at = timezone.now()
        self.appt_slot_patient_user.save()

        video_new_3_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=3)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_new_4_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=4)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_new_1_day_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=1) + timedelta(hours=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )

        # Add patient to primary care
        person_program_info = add_person_to_program(self.appt_slot_person, ProgramCodes.PRIMARY_CARE)

        # patient by default is hmo
        person_program_info.risk_score = RiskScore.HIGH
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 2)
        available_slots = [
            video_new_4_days_out.elation_id,
            video_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_3_days_out.elation_id)

        # update patient to ppo, we should see all appts
        self.appt_slot_patient_user.person.insurance_info = self.insurance_info_ppo
        self.appt_slot_patient_user.person.insurance_info.save()
        self.appt_slot_patient_user.person.save()

        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 3)
        available_slots = [
            video_new_1_day_out.elation_id,
            video_new_4_days_out.elation_id,
            video_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_1_day_out.elation_id)

    @override_settings(NEW_APPOINTMENT_BUFFER=timedelta(hours=1))
    def test_available_slots_based_on_insurance_type_new(self, _mock_timezone_now):
        # Let's assume the config is used to risk based times for PPO patients, all other patients will now be
        # seeing appointments at least 48 hours out. Let's check if PPO patients are able to see all appointments even
        # 1 hour out and hmo patients can only see 48 hours out. The config will be set to 1 hour for all risk scores.

        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()

        insurance_info_hmo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="hmo",
            member_id="010011",
            group_number="11234",
            state="MA",
        )
        insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="010011",
            group_number="11234",
            state="MA",
        )

        appt_slot_patient_user = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        appt_slot_person = Person.objects.create(
            user=appt_slot_patient_user,
            first_name="Test",
            last_name="Test",
            email=appt_slot_patient_user.email,
            insurance_info=insurance_info_hmo,
        )
        appt_slot_person.save()

        appt_slot_person.care_team.add(self.provider_1)
        appt_slot_person.care_team.add(self.provider_2)
        appt_slot_person.care_team.add(self.provider_3)
        appt_slot_person.care_team.add(self.provider_5)

        appt_slot_patient_user.created_at = timezone.now()
        appt_slot_patient_user.save()
        appt_slot_patient_user.onboarding_state.to_signedup()

        self.client.force_authenticate(user=appt_slot_patient_user)

        video_new_3_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=3)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_new_4_days_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(days=4)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_new_2_hours_out = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() + timedelta(hours=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )

        # Add patient to primary care
        person_program_info = add_person_to_program(appt_slot_person, ProgramCodes.PRIMARY_CARE)

        # patient by default is hmo
        person_program_info.risk_score = RiskScore.LOW
        person_program_info.save()
        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)

        response = response.json()
        self.assertEqual(len(response), 2)
        available_slots = [
            video_new_4_days_out.elation_id,
            video_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_3_days_out.elation_id)

        # update patient to ppo
        appt_slot_patient_user.person.insurance_info = insurance_info_ppo
        appt_slot_patient_user.person.insurance_info.save()
        appt_slot_patient_user.person.save()

        url = f"/appointment/slot/?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        response = response.json()
        self.assertEqual(len(response), 3)
        available_slots = [
            video_new_2_hours_out.elation_id,
            video_new_4_days_out.elation_id,
            video_new_3_days_out.elation_id,
        ]
        for appt in response:
            self.assertIn(appt["id"], available_slots)
        next_available_url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        next_avail_resp = self.client.get(next_available_url)
        slot = next_avail_resp.json()
        self.assertEqual(slot[0]["next_slot"]["id"], video_new_2_hours_out.elation_id)

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    def test_try_claiming_appt_successfully(
        self, mock_pull, mock_update_record, mock_get_record, mock_submit_event, _mock_timezone_now
    ):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": None}
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_update_record.return_value = test_slot
        mock_pull.return_value = appointment

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)
        mock_update_record.assert_called_once()
        appointment.refresh_from_db()
        self.assertEqual(appointment.patient.id, self.old_patient.id)
        self.assertEqual(appointment.status, "Scheduled")

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.save_records")
    def test_try_claiming_appt_previously_booked_by_same_user(
        self, mock_save_records, mock_get_record, mock_submit_event, _mock_timezone_now
    ):
        elation_id = 98981923123
        patient_elation_id = 1234

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient=self.old_patient,
                physician=self.provider_2.physician,
                start=scheduled_date,
                duration="00:15:00",
                time_slot_type="appointment_slot",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                elation_id=elation_id,
            )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to have been booked by the same user
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_get_record.return_value = test_slot
        mock_save_records.return_value = [appointment]

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)

    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    def test_try_claiming_appt_when_already_booked(self, mock_get_record, _mock_timezone_now):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": 1235}

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)

    def test_try_claiming_appt_in_the_past(self, _mock_timezone_now):
        elation_id = 98981923123
        AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() - timedelta(minutes=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": "test"}

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)

    def test_try_claiming_appointment_without_permission(self, _mock_timezone_now):
        elation_id = 98981923123
        AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() - timedelta(minutes=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": "test"}
        # the user is not part of care team and not an member yet.
        patient = PatientUserFactory.create()
        Person.objects.create(user=patient)

        client = FireflyTestAPIClient()
        client.force_authenticate(user=patient)

        response = client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_403_FORBIDDEN)

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    @mock.patch("firefly.modules.appointment.slots.api.try_claim_appointment_slot", wraps=try_claim_appointment_slot)
    def test_try_claiming_appointment_with_emoji_description(
        self,
        mock_try_claim_appointment_slot,
        mock_pull,
        mock_update_record,
        mock_get_record,
        mock_submit_event,
        _mock_timezone_now,
    ):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "Hello 👋, this is a text with emojis 😊"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": None}
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_update_record.return_value = test_slot
        mock_pull.return_value = appointment

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)

        # Assert emojis have been stripped from the description
        mock_try_claim_appointment_slot.assert_called_once()
        args, _ = mock_try_claim_appointment_slot.call_args
        __, ___, params, ____ = args
        self.assertEqual(params, {"description": "Hello , this is a text with emojis "})

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    def test_try_claiming_appt_successfully_v3_api(
        self, mock_pull, mock_update_record, mock_get_record, mock_submit_event, _mock_timezone_now
    ):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": None}
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_update_record.return_value = test_slot
        mock_pull.return_value = appointment

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)
        mock_update_record.assert_called_once()
        appointment.refresh_from_db()
        self.assertEqual(appointment.patient.id, self.old_patient.id)
        self.assertEqual(appointment.description, appt_description)
        self.assertEqual(appointment.status, "Scheduled")

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    def test_try_claiming_appt_successfully_v3_with_symptoms_api(
        self, mock_pull, mock_update_record, mock_get_record, mock_submit_event, _mock_timezone_now
    ):
        sample_symptom = SymptomFactory()
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        other_symptoms = "my unrecognizable symptom"
        payload = {
            "description": appt_description,
            "symptom_ids": [sample_symptom.pk],
            "other_symptoms": other_symptoms,
        }

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": None}
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": f"{appt_description}. Symptoms: {sample_symptom.label}, {other_symptoms}",
            "status": {"status": "Scheduled"},
        }
        mock_update_record.return_value = test_slot
        mock_pull.return_value = appointment

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)
        mock_update_record.assert_called_once()
        appointment.refresh_from_db()
        self.assertEqual(appointment.patient.id, self.old_patient.id)
        self.assertEqual(appointment.description, appt_description)
        self.assertEqual(appointment.status, "Scheduled")
        self.assertEqual(appointment.symptoms.count(), 1)
        self.assertEqual(appointment.symptoms.first().pk, sample_symptom.pk)
        self.assertEqual(appointment.other_symptoms, other_symptoms)

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    def test_try_claiming_appt_successfully_v3_source_lucian_api(self, mock_submit_event, _mock_timezone_now):
        sample_symptom = SymptomFactory()
        appt_description = "test description"
        appointment = AppointmentSlotFactory(
            physician=self.provider_2.physician,
            start=self.scheduled_date,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=None,
            source=AppointmentSource.LUCIAN,
        )

        other_symptoms = "my unrecognizable symptom"
        payload = {
            "description": appt_description,
            "symptom_ids": [sample_symptom.pk],
            "other_symptoms": other_symptoms,
        }

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)
        appointment.refresh_from_db()
        self.assertEqual(appointment.patient.id, self.old_patient.id)
        self.assertEqual(appointment.description, appt_description)
        self.assertEqual(appointment.status, "Scheduled")
        self.assertEqual(appointment.symptoms.count(), 1)
        self.assertEqual(appointment.symptoms.first().pk, sample_symptom.pk)
        self.assertEqual(appointment.other_symptoms, other_symptoms)

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.save_records")
    def test_try_claiming_appt_previously_booked_by_same_user_v3_api(
        self, mock_save_records, mock_get_record, mock_submit_event, _mock_timezone_now
    ):
        elation_id = 98981923123
        patient_elation_id = 1234

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient=self.old_patient,
                physician=self.provider_2.physician,
                start=scheduled_date,
                duration="00:15:00",
                time_slot_type="appointment_slot",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                elation_id=elation_id,
            )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to have been booked by the same user
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_get_record.return_value = test_slot
        mock_save_records.return_value = [appointment]

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)

    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    def test_try_claiming_appt_when_already_booked_v3_api(self, mock_get_record, _mock_timezone_now):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": 1235}

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)

    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    def test_try_book_appt_when_already_deleted_v3_api(self, mock_get_record, _mock_timezone_now):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "test description"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": 1235}

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        appointment.delete()

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)

    def test_try_claiming_appt_in_the_past_v3_api(self, _mock_timezone_now):
        elation_id = 98981923123
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() - timedelta(minutes=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": "test"}

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)

    def test_try_claiming_appointment_without_permission_v3_api(self, _mock_timezone_now):
        elation_id = 98981923123
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=(timezone.now() - timedelta(minutes=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": "test"}
        # the user is not part of care team and not an member yet.
        patient = PatientUserFactory.create()
        Person.objects.create(user=patient)

        client = FireflyTestAPIClient()
        client.force_authenticate(user=patient)

        response = client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_403_FORBIDDEN)

    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    @mock.patch("firefly.modules.appointment.slots.api.try_claim_appointment_slot", wraps=try_claim_appointment_slot)
    def test_try_claiming_appointment_with_emoji_description_v3_api(
        self,
        mock_try_claim_appointment_slot,
        mock_pull,
        mock_update_record,
        mock_get_record,
        mock_submit_event,
        _mock_timezone_now,
    ):
        elation_id = 98981923123
        patient_elation_id = 1234
        appt_description = "Hello 👋, this is a text with emojis 😊"
        scheduled_date = timezone.now() + timedelta(hours=1)
        appointment = AppointmentFactory(
            patient=None,
            physician=self.provider_2.physician,
            start=scheduled_date,
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            elation_id=elation_id,
        )

        payload = {"elation_id": elation_id, "description": appt_description}

        # Mock the elation time slot to be available
        mock_get_record.return_value = {"patient": None}
        test_slot = {
            "id": elation_id,
            "patient": patient_elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": scheduled_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider_2.physician.id,
            "practice": 87543382020,
            "duration": 15,
            "description": appt_description,
            "status": {"status": "Scheduled"},
        }
        mock_update_record.return_value = test_slot
        mock_pull.return_value = appointment

        # Ensure the patient has permission to book the appointment. Patients
        # onboarding initiated before OLD_MEMBER_DATE are not restricted from booking
        # appointments.
        # Set the elation_id on the old_person object
        self.old_person.elation_id = patient_elation_id
        self.old_person.save()

        client = FireflyTestAPIClient()
        client.force_authenticate(user=self.old_patient)

        client.patch(f"/appointment/slot/v3/{appointment.id}", format="json", data=payload)

        # Assert emojis have been stripped from the description
        mock_try_claim_appointment_slot.assert_called_once()
        args, _ = mock_try_claim_appointment_slot.call_args
        __, ___, params, ____ = args
        self.assertEqual(params, {"description": "Hello , this is a text with emojis "})


class ListPhysiciansAndSlotsForProviderTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.date_format = "%Y-%m-%dT%H:%M:%SZ"
        # Reset the time the patient is created
        self.patient.created_at = timezone.now()
        self.patient.save()

        default_state_machine = DefaultStateMachineFactory.create()
        self.case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_APPT_SLOT_UNAVAILABLE, state_machine_definition=default_state_machine
        )

        self.appt_slot_patient_user = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        # self.onboarding_state, created = OnboardingState.objects.get_or_create(
        #     patient=self.appt_slot_patient_user
        # )
        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()
        self.insurance_info_hmo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="hmo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.appt_slot_person = Person.objects.create(
            user=self.appt_slot_patient_user,
            first_name="Test",
            last_name="Test",
            email=self.appt_slot_patient_user.email,
            insurance_info=self.insurance_info_hmo,
        )
        self.appt_slot_person.save()
        self.appt_slot_person.employer = self.employer
        self.appt_slot_person.save()
        self.appt_slot_person.attribution.contract = self.contract
        self.appt_slot_person.attribution.save()
        self.assertIsNotNone(self.appt_slot_person.attribution.contract)

        MA = State.objects.get(abbreviation="MA")
        TX = State.objects.get(abbreviation="TX")

        self.provider_1 = ProviderDetailFactory.create()
        self.provider_1.physician.practicing_states.add(MA)
        self.provider_1.title = "MD"
        self.provider_1.save()
        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_md.user_set.add(self.provider_1.user)

        self.provider_licensed_in_MA = ProviderDetailFactory.create()
        self.provider_licensed_in_MA.physician.practicing_states.add(MA)
        self.provider_licensed_in_MA.title = "NP"
        internal_role, _ = Role.objects.get_or_create(role_name="Nurse Practitioner")
        self.provider_licensed_in_MA.internal_role = internal_role
        self.provider_licensed_in_MA.save()
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_np.user_set.add(self.provider_licensed_in_MA.user)

        self.provider_3 = ProviderDetailFactory.create()
        group_np.user_set.add(self.provider_3.user)

        self.another_provider_licensed_in_MA = ProviderDetailFactory.create()
        self.another_provider_licensed_in_MA.physician.practicing_states.add(MA)
        self.another_provider_licensed_in_MA.title = "NP"
        self.another_provider_licensed_in_MA.internal_role = internal_role
        self.another_provider_licensed_in_MA.save()
        group_md, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_md.user_set.add(self.another_provider_licensed_in_MA.user)

        self.provider_5 = ProviderDetailFactory.create()
        self.provider_5.physician.practicing_states.add(TX)
        self.provider_5.title = "NP"
        self.provider_5.internal_role = internal_role
        self.provider_5.save()
        group_md, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_md.user_set.add(self.provider_5.user)

        # Set some mocked appointment timing
        two_days = timezone.now().date() + timedelta(days=2)
        tzone = timezone.get_default_timezone()
        self.two_days_am_restart = datetime.combine(two_days, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)

        # Let's say member is already established
        Appointment.objects.create(
            physician_id=self.provider_licensed_in_MA.physician.id,
            start=(self.two_days_am_restart - timedelta(days=3) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=1999998,
            patient=self.appt_slot_patient_user,
            status=AppointmentStatus.CHECKED_OUT.value,
            patient_joined_video=True,
        )

        self.appt_with_physician_licensed_in_MA = Appointment.objects.create(
            physician_id=self.provider_licensed_in_MA.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=90) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=9999998,
        )
        self.appt_with_physician_licensed_in_MA.save()
        # A focused visit which should not be returned
        Appointment.objects.create(
            reason=AppointmentReason.FOCUSED_VISIT,
            patient=None,
            time_slot_type="appointment_slot",
            start=(self.two_days_am_restart + timedelta(minutes=90) + settings.NEW_APPOINTMENT_BUFFER),
            physician_id=self.provider_licensed_in_MA.physician.id,
            source="Lucian",
        )
        appointment = Appointment.objects.create(
            physician_id=self.provider_3.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=80) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=9999999,
        )
        appointment.save()
        # Ensure appointments for providers outside of care team are not prioritized
        self.appt_with_another_physician_licensed_in_MA = Appointment.objects.create(
            physician_id=self.another_provider_licensed_in_MA.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=60)),
            time_slot_type="appointment_slot",
            elation_id=9999997,
        )
        self.appt_with_another_physician_licensed_in_MA.save()
        appointment = Appointment.objects.create(
            physician_id=self.provider_5.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=70)),
            time_slot_type="appointment_slot",
            elation_id=9999996,
        )
        appointment.save()

        self.appt_slot_person.care_team.add(self.provider_1)
        self.appt_slot_person.care_team.add(self.provider_licensed_in_MA)
        self.appt_slot_person.care_team.add(self.provider_3)

        # Ensure patient is in the member onboarding state
        self.patient.onboarding_state.to_signedup()
        self.appt_slot_patient_user.onboarding_state.to_signedup()

    # Simulate current system time is 8:00AM EDT
    @mock.patch(
        "firefly.modules.appointment.utils.timezone.now",
        return_value=datetime.combine(
            timezone.now().date(),
            settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"],
            tzinfo=timezone.get_default_timezone(),
        ),
    )
    def test_should_show_licensed_physicians_only(self, _mock_timezone_now):
        now = timezone.now()
        start_time = now.strftime(self.date_format)
        # establish the patient
        Appointment.objects.create(
            physician_id=self.physician.id,
            start=(self.two_days_am_restart - timedelta(days=80) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=2234567,
            status=AppointmentStatus.CHECKED_OUT.value,
            patient=self.patient,
            patient_joined_video=True,
        )
        url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO}&patient_id={self.patient.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(url)
        appointments = response.json()

        self.assertEqual(len(appointments), 2)
        self.assertEqual(appointments[0]["physician_id"], self.another_provider_licensed_in_MA.physician.id)
        self.assertEqual(appointments[0]["id"], self.appt_with_another_physician_licensed_in_MA.id)
        self.assertEqual(appointments[0]["elation_id"], self.appt_with_another_physician_licensed_in_MA.elation_id)
        self.assertEqual(appointments[1]["physician_id"], self.provider_licensed_in_MA.physician.id)
        self.assertEqual(appointments[1]["id"], self.appt_with_physician_licensed_in_MA.id)
        self.assertEqual(appointments[1]["elation_id"], self.appt_with_physician_licensed_in_MA.elation_id)

        # If no reason is given, do not show focused visit
        url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&patient_id={self.patient.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(url)
        appointments = response.json()

        self.assertEqual(len(appointments), 2)

    def test_should_flag_care_team(self):
        now = timezone.now()
        start_time = now.strftime(self.date_format)
        url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO}&patient_id={self.appt_slot_patient_user.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(url)
        appointments = response.json()

        self.assertEqual(len(appointments), 2)
        self.assertEqual(appointments[0]["physician_id"], self.provider_licensed_in_MA.physician.id)
        self.assertEqual(appointments[0]["id"], self.appt_with_physician_licensed_in_MA.id)
        self.assertEqual(appointments[0]["elation_id"], self.appt_with_physician_licensed_in_MA.elation_id)
        self.assertEqual(appointments[0]["in_care_team"], True)

    def test_should_return_all_slots_in_2days_for_physician(self):
        provider_in_MA = ProviderDetailFactory.create()
        provider_in_MA.physician.practicing_states.add(State.objects.get(abbreviation="MA"))
        provider_in_MA.title = "MD"
        provider_in_MA.save()
        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_md.user_set.add(provider_in_MA.user)

        appointment = Appointment.objects.create(
            physician_id=provider_in_MA.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=80) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=1234567,
        )
        appointment.save()
        appointment = Appointment.objects.create(
            physician_id=provider_in_MA.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=90) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=7654321,
        )
        appointment.save()
        appointment = Appointment.objects.create(
            physician_id=provider_in_MA.physician.id,
            start=(self.two_days_am_restart + timedelta(minutes=120) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=9986764,
        )
        appointment.save()
        patient = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Abc@12345",  # pragma: allowlist secret
        )
        patient_person = Person.objects.create(
            user=patient,
            first_name="Test2days",
            last_name="Test",
            email=patient.email,
            insurance_info=self.insurance_info_ppo,
        )
        # establish the patient
        Appointment.objects.create(
            physician_id=provider_in_MA.physician.id,
            start=(self.two_days_am_restart - timedelta(days=80) + settings.NEW_APPOINTMENT_BUFFER),
            time_slot_type="appointment_slot",
            elation_id=2234567,
            status=AppointmentStatus.CHECKED_OUT.value,
            patient=patient,
            patient_joined_video=True,
        )
        patient_person.care_team.add(provider_in_MA)
        patient.onboarding_state.to_signedup()
        now = timezone.now()
        start_time = now.strftime(self.date_format)
        url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO}&patient_id={patient.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(url)
        appointments = response.json()
        self.assertEqual(len(appointments), 5)
        same_physician_data = []
        for appt in appointments:
            if appt["physician_id"] == provider_in_MA.physician.id:
                same_physician_data.append(appt)
                self.assertEqual(appt["in_care_team"], True)
        # Since we created three slots for this physician
        self.assertEqual(len(same_physician_data), 3)

    def test_appointment_slot_provider_initiated_with_no_coverage_start_time_dependency(self):
        person_in_plan_elect_period = PersonUserFactory()
        add_person_to_program(person=person_in_plan_elect_period, program_uid=ProgramCodes.BENEFIT)
        now = timezone.now()
        MA = State.objects.get(abbreviation="MA")
        person_in_plan_elect_period.insurance_info.state = "MA"
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        EligibilityRecord.objects.create(
            member=person_in_plan_elect_period,
            employee=person_in_plan_elect_period,
            effective_date=now + timedelta(days=60),
            termination_date=now + timedelta(days=300),
            coverage_status=CoverageStatus.ACTIVE,
        )
        ProgramEnrollment.objects.update_or_create(
            person=person_in_plan_elect_period,
            program_id=ProgramCodes.BENEFIT,
            status=BenefitProgramStatus.ELECTED,
            defaults={
                "period": (now + timedelta(days=60), now + timedelta(days=300)),
            },
        )

        # Create video appointment in two days
        Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(hours=55)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        video_slots_url = "/appointment/slot/?physician={pid}&reason=Video".format(pid=physician_1.id)
        self.client.force_authenticate(user=person_in_plan_elect_period.user)
        next_avail_resp = self.client.get(video_slots_url)
        available_slots = json.loads(next_avail_resp.content)
        self.assertEqual(len(available_slots), 1)
        start_time = now.strftime(self.date_format)
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={self.patient.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 1)

    def test_appt_slot_visibility_buffers(self):
        MA = State.objects.get(abbreviation="MA")
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()

        person.user.created_at = timezone.now() - timedelta(days=5)
        person.user.save()

        now = timezone.now()
        start_time = now.strftime(self.date_format)

        # should display 15 minutes later slot to FF staff
        appt = Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(minutes=15)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={person.user.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 1)

        # should not display to patient
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)
        appt.delete()

        # should not display 10 minutes later slot to FF staff
        appt_10_mins = Appointment.objects.create(
            elation_id=190002,
            physician=physician_1,
            start=(now + timedelta(minutes=10)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={person.user.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)
        appt_10_mins.delete()

    def test_appt_slot_hmo_ppo_rules(self):
        MA = State.objects.get(abbreviation="MA")
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()

        person.user.created_at = timezone.now() - timedelta(days=5)
        person.user.save()

        now = timezone.now()
        start_time = now.strftime(self.date_format)

        appt = Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(hours=10)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={self.appt_slot_patient_user.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 1)
        # should not display to patient
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)
        appt.delete()

        patient = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Abc@12345",  # pragma: allowlist secret
        )
        Person.objects.create(
            user=patient,
            first_name="Test2days",
            last_name="Test",
            email=patient.email,
            insurance_info=self.insurance_info_ppo,
        )

        appt = Appointment.objects.create(
            elation_id=190002,
            physician=physician_1,
            start=(now + timedelta(hours=0.5)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={patient.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 1)
        # should not display to patient
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 2)
        appt.delete()

    def test_appt_slot_visible_rule(self):
        MA = State.objects.get(abbreviation="MA")
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()

        person.user.created_at = timezone.now() - timedelta(days=5)
        person.user.save()

        now = timezone.now()
        start_time = now.strftime(self.date_format)

        Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(hours=50)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=False,
        )
        next_available_url = f"/appointment/slot/provider/available-physicians-with-slots?start_time={start_time}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&patient_id={self.appt_slot_patient_user.id}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        appointments = response.json()
        self.assertEqual(len(appointments), 1)

        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)
        open_cases: QuerySet[Case] = Case.objects.filter(category=self.case_category).exclude(
            Q(status_category=StatusCategory.COMPLETE) | Q(status_category=StatusCategory.DEFERRED)
        )
        self.assertEqual(open_cases.count(), 1)

    @override_flag(WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, active=True)
    def test_booking_video_visit_type_as_first_appt(self):
        MA = State.objects.get(abbreviation="MA")
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()

        person.user.created_at = timezone.now() - timedelta(days=5)
        person.user.save()
        now = timezone.now()
        # Make Video-New, AWV New slots available
        Appointment.objects.create(
            elation_id=190001,
            physician=physician_1,
            start=(now + timedelta(hours=50)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        Appointment.objects.create(
            elation_id=190002,
            physician=physician_1,
            start=(now + timedelta(hours=50)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.AWV_NEW,
            visible=True,
        )

        # Should show "AWV-New slot" when there is no date_of_last_awv configured
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["next_slot"]["reason"], AppointmentReason.AWV_NEW)

        # Should show "AWV New slot" when patient is due for AWV
        program, _ = Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        primary_care_info = PrimaryCareProgramInfo.objects.create(
            person=person,
            program=program,
            elation_awv_reminder_id=None,
            date_of_last_awv=timezone.now() - timedelta(days=400),
        )
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        self.client.force_authenticate(user=person.user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["next_slot"]["reason"], AppointmentReason.AWV_NEW)

        # Should show "Video-New slot" only when patient is not due
        primary_care_info.date_of_last_awv = timezone.now() - timedelta(days=40)
        primary_care_info.save(update_fields=["date_of_last_awv"])
        url = f"/appointment/slot/available-physicians?reason={AppointmentReason.VIDEO}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["next_slot"]["reason"], AppointmentReason.VIDEO_NEW_PATIENT)

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2, active=True)
    def test_booking_rule_when_allowed_appointment_capacity_reached(self):
        video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=5,
        )
        video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.AWV_NEW,
            unique_key=AppointmentReason.AWV_NEW,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=5,
        )
        # Add configuration for client to set percentage_of_allowed_appointments to 30%
        AppointmentTypeContractMapping.objects.create(
            contract=self.contract, appointment_type=video_new_appointment_type, percentage_of_allowed_appointments=30
        )
        MA = State.objects.get(abbreviation="MA")
        TX = State.objects.get(abbreviation="TX")
        provider_1 = ProviderDetailFactory.create()
        physician_1 = provider_1.physician
        physician_1.practicing_states.add(MA)

        provider_2 = ProviderDetailFactory.create()
        physician_2 = provider_2.physician
        physician_2.practicing_states.add(MA)
        physician_2.practicing_states.add(TX)

        # Generate time slots for providers
        new_type_mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=physician_1, appointment_type=video_new_appointment_type
        )
        established_type_mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=physician_1, appointment_type=video_appointment_type
        )
        new_type_mapping_2 = PhysicianAppointmentTypeMapping.objects.create(
            physician=physician_2, appointment_type=video_new_appointment_type
        )
        est_type_mapping_2 = PhysicianAppointmentTypeMapping.objects.create(
            physician=physician_2, appointment_type=video_appointment_type
        )
        today = datetime.now().date()
        future_day: date = today + timedelta(days=4)
        day_of_week = future_day.isoweekday()
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=new_type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "50"},
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=established_type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "50"},
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=new_type_mapping_2,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "80"},
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=est_type_mapping_2,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "20"},
        )
        provider_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider_1,
            effective_period=(
                today,
                future_day + timedelta(days=6),
            ),
        )
        provider_schedule_2: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider_2,
            effective_period=(
                today,
                future_day + timedelta(days=6),
            ),
        )
        shift_1 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=provider_schedule,
            effective_period=(
                today,
                future_day + timedelta(days=6),
            ),
            start_time=datetime.strptime("10:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:30:00", "%H:%M:%S"),
        )
        shift_2 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=provider_schedule_2,
            effective_period=(
                today,
                future_day + timedelta(days=6),
            ),
            start_time=datetime.strptime("15:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("18:30:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=provider_1.user_id,
            log_prefix="",
            start_date=today.strftime("%Y-%m-%d"),
            end_date=(future_day + timedelta(days=6)).strftime("%Y-%m-%d"),
            dry_run_off=True,
        )
        create_slots_for_provider_shift(
            provider_id=provider_2.user_id,
            log_prefix="",
            start_date=today.strftime("%Y-%m-%d"),
            end_date=(future_day + timedelta(days=6)).strftime("%Y-%m-%d"),
            dry_run_off=True,
        )

        start_time = future_day.strftime(self.date_format)

        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={self.appt_slot_patient_user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        physician_1_slots = json["appointments"][0]["slots"]
        physician_2_slots = json["appointments"][1]["slots"]

        # 4 Video-New slots should be available for 3 hour shift for provider_1 and provider_2
        # 10:30, 11:15, 12:00, 12:45 Video-New slots will be available
        self.assertEqual(len(physician_1_slots), 4)
        self.assertEqual(len(physician_2_slots), 4)

        self.assertEqual(
            physician_1_slots,
            [
                datetime(future_day.year, future_day.month, future_day.day, 10, 30, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 11, 15, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 12, 0, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 12, 45, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
            ],
        )

        self.assertEqual(
            physician_2_slots,
            [
                datetime(future_day.year, future_day.month, future_day.day, 15, 30, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 16, 15, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 17, 0, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
                datetime(future_day.year, future_day.month, future_day.day, 17, 45, tzinfo=NY_TIMEZONE)
                .astimezone(datetime_timezone.utc)
                .strftime(self.date_format),
            ],
        )

        # # APPOINTMENT BOOKING 1
        # Book the first open appointment slot for user from configured contract with provider_1
        self.patient.person.employer = self.employer
        self.patient.person.save()
        self.patient.person.attribution.contract = self.contract
        self.patient.person.attribution.save()
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=physician_1_slots[0],
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            duration="00:30:00",
            physician=physician_1,
        )
        all_slots = TimeSlot.objects.filter(shift=shift_1).order_by("period")
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])

        # Booked appts from same contract = 1
        # Total appts available = 3 (physician_1) + 4 (physician_2) + 1 booked = 8
        # percentage of booked appts = 1 / 8 = 12.5%
        # Allowed capacity not reached, slots should be available
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={self.appt_slot_patient_user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        physician_1_slots = json["appointments"][0]["slots"]
        physician_2_slots = json["appointments"][1]["slots"]
        # 3 Video-New slots should be available after booking first appointment slot for provider_1
        # 11:15, 12:00, 12:45 Video-New slots will be available
        self.assertEqual(len(physician_1_slots), 3)
        # 4 Video-New slots should be available for provider_2
        # 15:30, 16:15, 17:00, 17:45 Video-New slots will be available
        self.assertEqual(len(physician_2_slots), 4)

        # # APPOINTMENT BOOKING 2
        # Book appointment for a different user from same contract, with provider_2
        client_another_person = PersonUserFactory()
        client_another_person.employer = self.employer
        client_another_person.save(update_fields=["employer"])
        add_person_to_program(person=client_another_person, program_uid=ProgramCodes.PRIMARY_CARE)
        client_another_person.user.onboarding_state.to_member(actor=client_another_person.user)
        self.assertIsNotNone(client_another_person.attribution)
        client_another_person.attribution.contract = self.contract
        client_another_person.attribution.save()
        client_another_person.attribution.refresh_from_db()
        self.assertIsNotNone(client_another_person.attribution.contract)
        appt = AppointmentFactory.create(
            patient=client_another_person.user,
            start=physician_2_slots[0],
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            duration="00:30:00",
            physician=physician_2,
        )
        all_slots = TimeSlot.objects.filter(shift=shift_2).order_by("period")
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])
        # If same person requests booking again, should not show any slots for the same day
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={client_another_person.user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        self.assertEqual(len(json["appointments"]), 0)

        # When other user from same contract requests booking, appointment slots should be available
        client_another_person_2 = PersonUserFactory()
        client_another_person_2.employer = self.employer
        client_another_person_2.save(update_fields=["employer"])
        add_person_to_program(person=client_another_person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        client_another_person_2.user.onboarding_state.to_member(actor=client_another_person_2.user)
        self.assertIsNotNone(client_another_person_2.attribution)
        client_another_person_2.attribution.contract = self.contract
        client_another_person_2.attribution.save()
        client_another_person_2.attribution.refresh_from_db()
        self.assertIsNotNone(client_another_person_2.attribution.contract)
        # Booked appts from same contract = 2
        # Total appts available = 3 (physician_1) + 3 (physician_2) + 2 booked = 8
        # percentage of booked appts = 2 / 8 = 25%
        # Allowed capacity not reached, slots should be available
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={client_another_person_2.user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        physician_1_slots = json["appointments"][0]["slots"]
        physician_2_slots = json["appointments"][1]["slots"]
        # 3 Video-New slots should be available for provider_1
        # 10:30, 11:15, 12:00, 12:45 Video-New slots will be available
        self.assertEqual(len(physician_1_slots), 3)
        # 3 Video-New slots should be available for provider_2
        # 16:15, 17:00, 17:45 Video-New slots will be available
        self.assertEqual(len(physician_2_slots), 3)

        # # APPOINTMENT BOOKING 3
        # Book appointment for a user from different contract, with provider_2
        idexx_person = PersonUserFactory()
        idexx_employer, _ = Employer.objects.get_or_create(name=EmployerName.IDEXX)
        idexx_person.employer = idexx_employer
        idexx_person.save(update_fields=["employer"])
        add_person_to_program(person=idexx_person, program_uid=ProgramCodes.PRIMARY_CARE)
        idexx_person.user.onboarding_state.to_member(actor=idexx_person.user)
        self.assertIsNotNone(idexx_person.attribution)
        idexx_contract = ContractFactory(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": idexx_employer.id,
                "allowable_group_ids": ["123123"],
                "is_coverage_program_enrollment_enabled": True,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.AUTO_ATTRIBUTED,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(idexx_employer),
            contracted_entity=idexx_employer,
        )
        idexx_person.attribution.contract = idexx_contract
        idexx_person.attribution.save()
        idexx_person.attribution.refresh_from_db()
        self.assertIsNotNone(idexx_person.attribution.contract)
        appt = AppointmentFactory.create(
            patient=idexx_person.user,
            start=physician_2_slots[0],
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            duration="00:30:00",
            physician=physician_2,
        )
        all_slots = TimeSlot.objects.filter(shift=shift_2).order_by("period")
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[2])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[3])

        # Booked appts from same contract = 2
        # All booked appointments = 2 (another client) + 1 (idexx) = 3
        # Total appts available = 3 (physician_1) + 2 (physician_2) + 3 booked = 8
        # percentage of booked appts = 2 / 8 = 25%
        # Allowed capacity not reached for booked appointments from same contract, slots should be available
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={client_another_person_2.user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        physician_1_slots = json["appointments"][0]["slots"]
        physician_2_slots = json["appointments"][1]["slots"]
        # 3 Video-New slots should be available for provider_1
        # 10:30, 11:15, 12:00, 12:45 Video-New slots will be available
        self.assertEqual(len(physician_1_slots), 3)
        # 2 Video-New slots should be available for provider_2
        # 17:00, 17:45 Video-New slots will be available
        self.assertEqual(len(physician_2_slots), 2)

        # # APPOINTMENT BOOKING 4
        # Book appointment for a user from same configured contract, with provider_2
        client_another_person_3 = PersonUserFactory()
        client_another_person_3.employer = self.employer
        client_another_person_3.save(update_fields=["employer"])
        add_person_to_program(person=client_another_person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        client_another_person_3.user.onboarding_state.to_member(actor=client_another_person_3.user)
        self.assertIsNotNone(client_another_person_3.attribution)
        client_another_person_3.attribution.contract = self.contract
        client_another_person_3.attribution.save()
        client_another_person_3.attribution.refresh_from_db()
        self.assertIsNotNone(client_another_person_3.attribution.contract)
        appt = AppointmentFactory.create(
            patient=client_another_person_3.user,
            start=physician_2_slots[0],
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            duration="00:30:00",
            physician=physician_2,
        )
        all_slots = TimeSlot.objects.filter(shift=shift_2).order_by("period")
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[5])

        # Booked appts from same contract = 3
        # All booked appointments = 3 (another client) + 1 (idexx) = 4
        # Total appts available = 3 (physician_1) + 1 (physician_2) + 4 booked = 8
        # percentage of booked appts = 3 / 8 = 37.5%
        # Allowed capacity of 30% reached, hence slots should not be available
        # when another client contracted person requests booking
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={client_another_person_2.user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        slots = json["appointments"]
        self.assertEqual(len(slots), 0)

        # Slots should be available when person with another contract requests booking, though allowed capacity reached
        # for configured contract
        idexx_person = PersonUserFactory()
        idexx_person.employer = idexx_employer
        idexx_person.save(update_fields=["employer"])
        add_person_to_program(person=idexx_person, program_uid=ProgramCodes.PRIMARY_CARE)
        idexx_person.user.onboarding_state.to_member(actor=idexx_person.user)
        self.assertIsNotNone(idexx_person.attribution)
        idexx_person.attribution.contract = idexx_contract
        idexx_person.attribution.save()
        idexx_person.attribution.refresh_from_db()
        self.assertIsNotNone(idexx_person.attribution.contract)
        next_available_url = f"/bff/crm/available-appointment-slots/?patient_id={idexx_person.user.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time}"  # noqa: E501
        self.provider_client.force_authenticate(user=self.provider)
        response = self.provider_client.get(next_available_url)
        json = response.json()
        physician_1_slots = json["appointments"][0]["slots"]
        physician_2_slots = json["appointments"][1]["slots"]
        # 3 Video-New slots should be available for provider_1
        # 10:30, 11:15, 12:00, 12:45 Video-New slots will be available
        self.assertEqual(len(physician_1_slots), 3)
        # 2 Video-New slots should be available for provider_2
        # 17:00, 17:45 Video-New slots will be available
        self.assertEqual(len(physician_2_slots), 2)


@override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True))
@mock.patch("firefly.modules.appointment.slots.api.submit_event_async")
@mock.patch.object(ObjectToElationRecord, "object_pre_save_callback")
@mock.patch.object(ElationClient, "create_record")
@mock.patch.object(ElationClient, "update_record")
@mock.patch.object(ElationClient, "init_session")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch.object(
    WindowRateLimiter,
    "_acquire",
    return_value=True,
)
class BookAppointmentByProviderTestCaseWithWaffleOn(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider = ProviderDetailFactory.create()
        self.patient = PatientUserFactory.create()
        Person.objects.create(user=self.patient)
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - timedelta(days=1)
        self.patient.onboarding_state.save()

    def test_book_appointment_with_past_date(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        _init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        ElationAppointmentSync().connect_model_listener()
        slot_past_time = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=datetime.now() - timedelta(hours=2),
            source=AppointmentSource.LUCIAN,
        )
        payload = {"description": "test", "patient_id": self.patient.id}
        response = self.provider_client.patch(
            f"/appointment/slot/provider/book-appointment/v2/{slot_past_time.id}", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        create_record_mock.assert_not_called()
        mock_submit_event.send.assert_not_called()
        object_pre_save_callback_mock.assert_not_called()

    def test_book_appointment_with_already_booked_slot(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        _init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        ElationAppointmentSync().connect_model_listener()
        booked_appointment = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=datetime.now() + timedelta(hours=1),
            source=AppointmentSource.LUCIAN,
        )
        person = PersonUserFactory()
        booked_appointment.patient = person.user
        booked_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            booked_appointment.save(update_fields=["patient", "status"])
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=booked_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {booked_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=booked_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        payload = {"description": "test", "patient_id": self.patient.id}
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.patch(
                f"/appointment/slot/provider/book-appointment/v2/{booked_appointment.id}", format="json", data=payload
            )
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        create_record_mock.assert_not_called()
        mock_submit_event.send.assert_not_called()
        create_record_mock.assert_not_called()
        object_pre_save_callback_mock.assert_not_called()

    def test_book_appointment_source_lucian(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        _init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        scheduled_date = datetime.now() + timedelta(hours=2)
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        ElationAppointmentSync().connect_model_listener()
        slot_future_time = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=scheduled_date,
            source=AppointmentSource.LUCIAN,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        create_record_mock.reset_mock()
        payload = {"description": "test", "patient_id": self.patient.id}
        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.patch(
                f"/appointment/slot/provider/book-appointment/v2/{slot_future_time.id}", format="json", data=payload
            )
        slot_future_time.refresh_from_db()
        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(slot_future_time.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(slot_future_time.patient, self.patient)
        self.assertEqual(slot_future_time.description, "test")
        self.patient.person.elation_id = Faker().pyint()
        self.patient.person.save()
        # should not call pre save if source Lucian
        object_pre_save_callback_mock.assert_not_called()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=slot_future_time.pk,
            log_prefix=f"appointment_scheduled: Appointment: {slot_future_time.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=slot_future_time.pk, log_prefix="")
        # should call post save if source Lucian
        create_record_mock.assert_called_once()
        update_record_mock.assert_not_called()
        mock_submit_event.send.assert_called_once()

    @mock.patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.pull")
    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    @mock.patch("firefly.modules.appointment.elation.ElationAppointmentSync.save_records")
    def test_book_appointment_source_elation(
        self,
        mock_save_records,
        mock_braze_submit_event,
        pull_mock,
        get_record_mock,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        _init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        ElationAppointmentSync().connect_model_listener()
        faker: Faker = Faker()
        expected_description: str = faker.pystr()
        with self.captureOnCommitCallbacks(execute=True):
            slot_future_time_elation_source = AppointmentSlotFactory(
                physician=self.provider.physician,
                start=datetime.now() + timedelta(hours=2),
                source=AppointmentSource.ELATION,
                status=AppointmentStatus.SCHEDULED.value,
                patient=self.patient,
                description=expected_description,
            )
        object_pre_save_callback_mock.reset_mock()
        create_record_mock.reset_mock()
        get_record_mock.return_value = {"patient": None}
        pull_mock.return_value = slot_future_time_elation_source
        mock_save_records.return_value = [slot_future_time_elation_source]
        self.patient.person.elation_id = faker.pyint()
        self.patient.person.save()
        test_slot = {
            "id": faker.pyint(),
            "patient": self.patient.person.elation_id,
            "time_slot_type": "appointment_slot",
            "scheduled_date": slot_future_time_elation_source.start.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "reason": "Video-New",
            "physician": self.provider.physician.id,
            "practice": settings.ELATION["PRACTICE_ID"],
            "duration": 15,
            "description": expected_description,
            "status": {"status": "Scheduled"},
        }
        update_record_mock.return_value = test_slot
        payload = {"description": "test", "patient_id": self.patient.id}
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.patch(
                f"/appointment/slot/provider/book-appointment/v2/{slot_future_time_elation_source.id}",
                format="json",
                data=payload,
            )
        slot_future_time_elation_source.refresh_from_db()
        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(slot_future_time_elation_source.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(slot_future_time_elation_source.patient, self.patient)
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=slot_future_time_elation_source.pk,
            log_prefix=f"appointment_scheduled: Appointment: {slot_future_time_elation_source.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=slot_future_time_elation_source.pk, log_prefix="")
        create_record_mock.assert_not_called()
        update_record_mock.assert_called_once()
        mock_braze_submit_event.assert_called_once()


class AppointmentSlotExcludingStateTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        # Setup states
        self.MA = State.objects.get(abbreviation="MA")
        self.NH = State.objects.get(abbreviation="NH")

        self.video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            buffer_time_in_minutes=15,
            duration=30,
        )

        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
        )

        # Since we're sitting on top of migrations ensure providers setup explicitly
        for provider in ProviderDetail.objects.all():
            provider.delete()

        MA_excluded_provider = ProviderDetailFactory.create()
        self.MA_excluded_physician = MA_excluded_provider.physician
        ma_excluded_type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.MA_excluded_physician, appointment_type=self.video_appointment_type
        )
        ma_excluded_type_mapping.excluded_states.add(self.MA)
        self.MA_excluded_physician.practicing_states.add(self.MA)
        self.MA_excluded_physician.practicing_states.add(self.NH)

        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.MA_excluded_physician, appointment_type=self.video_new_appointment_type
        )

        provider = ProviderDetailFactory.create()
        self.physician = provider.physician
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.physician, appointment_type=self.video_new_appointment_type
        )
        self.physician.practicing_states.add(self.MA)
        self.physician.practicing_states.add(self.NH)

    def test_physician_appt_type_excluded_state_video(self):
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, self.video_appointment_type),
            {
                "physicians": [self.physician],
                "reasons_for_unavailability": [],
            },
        )

    def test_physician_appt_type_excluded_state_video_new(self):
        person = PersonUserFactory.create()
        person.insurance_info.state = "MA"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, self.video_new_appointment_type),
            {
                "physicians": [self.MA_excluded_physician, self.physician],
                "reasons_for_unavailability": [],
            },
        )

    def test_physician_appt_type_without_excluded_state(self):
        person = PersonUserFactory.create()
        person.insurance_info.state = "NH"
        person.insurance_info.save()
        self.assertEqual(
            get_physicians_eligible_for_scheduling(person, self.video_appointment_type),
            {
                "physicians": [self.MA_excluded_physician, self.physician],
                "reasons_for_unavailability": [],
            },
        )
