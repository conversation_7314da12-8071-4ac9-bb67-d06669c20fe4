# Appointments

Managing Appointments uses <PERSON> as the source of truth. We also sync appointment data to our EMR, Elation. The firefly.modules.schedule app contains the logic for Shift management and schedule generation.

## Appointment Types

Appointments are created with an Appointment Type. Appointment Types determine the category of care which the Appointment represents. A few examples are:
1) `Video-New`, for new / unestablished patients
2) `Video`, a general-purpose Appointment with an established patient
3) `Focused`, an Appointment focused on a relatively non-complex symptom like Cold or Flu
4) `Health Guide Consult`, for an appointment with a Health Guide instead of an NP or MD e.g. for a discussion about nutrition or weight loss

Appointment Types are configurable with a few attributes such as:
1) Duration
2) Buffer time (i.e. time between appointments reserved for post and pre-next-visit work)
3) Rules for who can book, and which providers can conduct, the appointment

## Booking Workflow

The appointment booking process follows a structured workflow:

### 1. Eligibility Check
```python
from firefly.modules.appointment.utils import get_can_book_appointments

# Determine which appointment types the patient can book
eligible_types = get_can_book_appointments(person=patient)
```

### 2. Appointment Type Triage
Based on patient needs, triage to appropriate appointment type:
- **Manual Selection**: <PERSON><PERSON> chooses between Medical visit vs. Health Guide visit
- **Automated Triage**: System triages to `Video` or `Focused` based on symptoms

### 3. Provider and Time Selection
- Search available clinicians for selected appointment type
- Filter by provider qualifications and appointment type rules
- Display available time slots based on provider shifts

### 4. Slot Claiming
```python
from firefly.modules.appointment.models import AppointmentSlotClaimV2

# Claim the appointment slot for the patient
claim = AppointmentSlotClaimV2.objects.create(
    person=patient,
    appointment_type=selected_type,
    provider=selected_provider,
    start_time=selected_time
)
```

### Side Effects
Once an appointment is booked:
- **Provider Shifts**: Scheduling logic updates provider availability
- **Elation Sync**: Appointment synced to EMR system
- **Notifications**: Confirmation messages sent to patient and provider

## Technical Implementation

### Core Models
- **Appointment**: Main appointment record with status, timing, and participants
- **AppointmentType**: Defines appointment categories with duration and rules
- **AppointmentSlotClaimV2**: Handles the booking process and slot management

### Integration Points
- **Elation EMR**: Bidirectional sync for appointment data
- **Scheduling Module**: Provider shift management and availability
- **Notifications**: Email/SMS confirmations and reminders

### Business Rules
- **Eligibility Rules**: Defined per appointment type (new vs. established patients)
- **Provider Rules**: Which provider types can conduct each appointment type
- **Duration Rules**: Appointment length and buffer time requirements

## Cancellation Workflow

Appointment cancellation triggers several automated processes:

### 1. Availability Release
- Canceled slots become available for re-booking
- Provider shifts updated to reflect availability
- Scheduling rules determine if slot can be immediately released

### 2. Side Effects
- **Elation Sync**: Cancellation synced to EMR
- **Notifications**: Cancellation confirmations sent
- **Scheduling Logic**: Multiple rules may affect related appointments

### 3. Business Rules
- **Cancellation Windows**: Different rules for last-minute vs. advance cancellations
- **Provider Impact**: Cancellations may affect provider scheduling