import logging
from datetime import datetime, timedelta
from unittest import mock

from callee import InstanceOf
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import override_settings
from django.utils import timezone
from dramatiq.rate_limits.backends import StubBackend
from mock import patch
from psycopg2.extras import DateRange
from rest_framework.exceptions import ValidationError
from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.feature.testutils import override_flag
from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.roles.models import Role
from firefly.core.services.braze.constants import BrazeEvent
from firefly.core.services.elation.sync.mixins.object_to_elation import ObjectToElationRecord
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PatientUserFactory, ProviderDetailFactory
from firefly.core.user.models import User
from firefly.modules.appointment.api import AppointmentList<PERSON>reateView
from firefly.modules.appointment.constants import (
    CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
    OLD_MEMBER_DATE,
    WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE,
    AppointmentCampaign,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import AppointmentFactory, SymptomFactory
from firefly.modules.appointment.models import Appointment, AppointmentCancelationReason
from firefly.modules.appointment.serializers import AppointmentSerializer
from firefly.modules.appointment.signals import notify_provider_of_last_minute_appointment
from firefly.modules.appointment.utils import (
    assign_post_visit_survey,
    auto_cancel_appointments_with_incomplete_health_assessment,
    create_case_for_non_english_speaker,
    mark_appointments,
)
from firefly.modules.cases.constants import (
    INSURANCE_PLAN_NEEDS_REVIEW,
    AWVCaseStatuses,
    CaseCategoryTitle,
)
from firefly.modules.cases.factories import CaseCategoryFactory, CaseFactory
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.facts.factories import PreferredLanguageFactory
from firefly.modules.features.constants import Features
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.factories import FormFactory, FormSubmissionFactory
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.onboarding.constants import (
    CASE_CATEGORY_UNIQUE_KEY_FOR_ONBOARDING_FIRST_APPT_OUTREACH,
)
from firefly.modules.physician.factories import PhysicianFactory
from firefly.modules.physician.models import Physician
from firefly.modules.practice.models import Practice
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, program_info_for_person_program
from firefly.modules.schedule.factories import ProviderScheduleFactory
from firefly.modules.schedule.models import PhysicianAppointmentTypeMapping, PhysicianVisitMixRatio
from firefly.modules.statemachines.contants import (
    FIRST_APPOINTMENT_OUTREACH_STATE_MACHINE_CONTENT,
    StateMachineSystemAction,
)
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.models import Task, TaskCollection, TaskCollectionTask
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.modules.tenants.models import Tenant
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)


def _get_primary_care_data(person):
    primary_care_program_info = program_info_for_person_program(person, ProgramCodes.PRIMARY_CARE)
    if primary_care_program_info:
        return (
            primary_care_program_info.caregiver_practice,
            primary_care_program_info.primary_physician,
        )
    return (
        Practice.objects.first(),
        Physician.objects.first(),
    )


class AppointmentHasAccessToFeatures(FireflyTestCase):
    def setUp(self):
        super().setUp()
        CaseCategoryFactory(
            title="Test Category",
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
        )

    def test_feature_access_to_appointment_cancel_view(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        patient = self.patient
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() + timedelta(minutes=30)),
            duration="00:15:00",
            description="A past appointment which should be cancellable",
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )

        url = "/appointment/%s/cancel/v2/" % appointment.id
        self.client.force_authenticate(user=patient)
        # Case 1: Tenant Assocaited with the Patient has access
        response = self.client.post(url, {}, format="json")
        self.assertEqual(response.status_code, 200)

        # Case 2: Tenant Assocaited with the Patient has no access
        self.patient.person.tenants.clear()
        self.tenant, _ = Tenant.objects.update_or_create(
            key=FIREFLY_TENANT_KEY,
            defaults={
                "feature_access_config": {
                    Features.appointment_booking.value: False,
                    Features.chat.value: True,
                }
            },
        )
        self.patient.person.tenants.add(self.tenant.id)
        response = self.client.get(f"/appointment/{appointment.id}/cancel/v2/")
        self.assertEqual(response.status_code, 403)

    def test_feature_access_to_appointment_list_view(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)

        # Case 1: Tenant Assocaited with the Patient has access
        response = self.client.get("/appointment/")
        self.assertEqual(response.status_code, 200)

        # Case 2: Tenant Assocaited with the Patient has no access
        self.patient.person.tenants.clear()
        self.tenant, _ = Tenant.objects.update_or_create(
            key=FIREFLY_TENANT_KEY,
            defaults={
                "feature_access_config": {
                    Features.appointment_booking.value: False,
                    Features.chat.value: True,
                }
            },
        )
        self.patient.person.tenants.add(self.tenant.id)
        response = self.client.get("/appointment/")
        self.assertEqual(response.status_code, 403)

    def test_appointment_join_view(self, *args):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            start=timezone.now() + relativedelta(years=80),
            reason=AppointmentReason.VIDEO,
        )
        self.assertFalse(appointment.scheduled_at is None)
        join_response = self.client.get("/appointment/%s/join/" % appointment.id)
        self.assertEqual(join_response.status_code, 200)

        # Case 2: Tenant associated with the Patient has no access
        self.patient.person.tenants.clear()
        self.tenant, _ = Tenant.objects.update_or_create(
            key=FIREFLY_TENANT_KEY,
            defaults={
                "feature_access_config": {
                    Features.appointment_booking.value: False,
                    Features.chat._name_: True,
                }
            },
        )
        self.patient.person.tenants.add(self.tenant.id)
        join_response = self.client.get("/appointment/%s/join/" % appointment.id)
        self.assertEqual(join_response.status_code, 403)

    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    def test_braze_call_in_appointment_join_view(self, submit_event_mock, _backend_mock, *args):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            start=timezone.now() + relativedelta(years=80),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            patient_joined_video=True,
        )
        self.assertFalse(appointment.scheduled_at is None)
        join_response = self.client.get("/appointment/%s/join/" % appointment.id)
        self.assertEqual(join_response.status_code, 200)
        submit_event_mock.assert_called_once_with(
            self.patient.person.pk,
            BrazeEvent.FIRST_VISIT_COMPLETE,
            {},
        )

    def test_feature_access_to_appointment_retrieve_update_view(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        # Create appointment
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() + relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )

        # Case 1: Tenant Assocaited with the Patient has access
        response = self.client.get(f"/appointment/{appointment.id}")
        self.assertEqual(response.status_code, 200)

        # When retrieving let's make sure the health_assessment key is present, comes
        # from AppointmentWithHealthAssessmentSerializer
        self.assertTrue("health_assessment" in response.data)

        # Case 2: Tenant Assocaited with the Patient has no access
        self.patient.person.tenants.clear()
        self.tenant, _ = Tenant.objects.update_or_create(
            key=FIREFLY_TENANT_KEY,
            defaults={
                "feature_access_config": {
                    Features.appointment_booking.value: False,
                    Features.chat._name_: True,
                }
            },
        )
        self.patient.person.tenants.add(self.tenant.id)
        response = self.client.get(f"/appointment/{appointment.id}")
        self.assertEqual(response.status_code, 403)


@patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class AppointmentTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        # Pre-load test fixtures
        self.contract

        CaseCategoryFactory(
            title="Test Category",
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
        )
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)

    def test_appointment_list_request(self, mock_elation_update_user):
        sample_symptom = SymptomFactory()
        practice, pcp = _get_primary_care_data(self.patient.person)
        appt = AppointmentFactory(
            patient_id=self.patient.pk,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() + relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=False,
            other_symptoms="A different symptom",
        )
        appt.symptoms.set([sample_symptom])
        AliasMapping.set_mapping_by_object(
            obj=appt,
            alias_id="123",
            alias_name=AliasName.CALENDAR_EVENT_ID,
        )

        appt_2 = AppointmentFactory(
            patient_id=self.patient.pk,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() + relativedelta(years=79),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=False,
        )
        appt_2.symptoms.set([sample_symptom])

        response = self.client.get("/appointment/")
        self.assertEqual(response.status_code, 200)
        response_elements = response.json()
        self.assertEqual(len(response_elements), 2)
        self.assertEqual(response_elements[0]["id"], appt.pk)
        self.assertIsNotNone(response_elements[0]["appointment_symptoms"])
        self.assertEqual(len(response_elements[0]["appointment_symptoms"]), 1)
        self.assertEqual(response_elements[0]["appointment_symptoms"][0]["symptom"]["id"], sample_symptom.pk)
        self.assertEqual(response_elements[0]["other_symptoms"], "A different symptom")
        self.assertEqual(response_elements[0]["calendar_event_id"], "123")
        self.assertEqual(response_elements[1]["calendar_event_id"], None)
        view = AppointmentListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get("/appointment/", format="json")
        force_authenticate(request, user=self.provider)
        # 14 queries
        # 1 to fetch tenant data
        # 1 to check permissions
        # 2 to fetch content type
        # 1 to fetch appointment data with summary note and rating
        # 1 to fetch care team details
        # 2 to fetch state details
        # 2 to fetch practice locations
        # 1 to fetch appointment images
        # 2 to fetch form submissions
        # 1 to fetch symptoms
        with self.assertNumQueries(14):
            response = view(request)
            response.render()

    def test_appointment_without_physician_provider(self, mock_elation_update_user):
        physician = PhysicianFactory(provider=None)
        with self.assertRaises(ValidationError) as error:
            appointment = AppointmentFactory(
                physician=physician,
            )
            self.assertEqual(
                str(error.exception.detail["physician"]),
                f"Appointment None cannot have a missing provider for Physician f{appointment.physician_id}",
            )

    def test_cancellation(self, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        patient = self.patient
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() + timedelta(hours=24)),
            duration="00:15:00",
            description="A future appointment",
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        appointment.cancel()
        self.assertEqual(appointment.deleted, None)
        self.assertEqual(appointment.status, AppointmentStatus.CANCELLED.value)

        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() + timedelta(hours=40)),
            time_slot_type="appointment_slot",
            duration="00:15:00",
            description="Another future appointment",
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        appointment.cancel()
        self.assertEqual(appointment.patient_id, None)
        self.assertEqual(appointment.description, "")

    def test_update_due_date_on_appointment_cancellation(self, mock_elation_update_user):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        practice, pcp = _get_primary_care_data(patient.person)

        appt_1 = None
        with self.captureOnCommitCallbacks(execute=True):
            # Create appointment
            appt_1 = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            (appt_1.start - timedelta(hours=24)),
        )

        # When cancelling one and only one appt, due_date should be updated to 2 days after the current time
        with self.captureOnCommitCallbacks(execute=True):
            self.assertTrue(appt_1.cancel())
        due_date = Task.objects.get(
            patient_id=patient.id,
            title=form.title,
            owner_group_id=patient.assignee_group.id,
        ).due_date

        expected_due_date = timezone.now() + timedelta(days=2)
        self.assertEqual(
            due_date.year,
            expected_due_date.year,
        )
        self.assertEqual(
            due_date.month,
            expected_due_date.month,
        )
        self.assertEqual(
            due_date.day,
            expected_due_date.day,
        )

        appt_2 = None
        with self.captureOnCommitCallbacks(execute=True):
            # When an appointment is booked in future, should consider new appt.start
            appt_2 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=5),
                duration="00:15:00",
                description="Back and ankle pain 2",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )
        appt_3 = None
        with self.captureOnCommitCallbacks(execute=True):
            # When new appt is created before existing appt, should consider new appt.start
            appt_3 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment_slot",
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_3.start - timedelta(hours=24),
        )

        # When earliest appt is cancelled, should update due_date considering next earliest appt
        with self.captureOnCommitCallbacks(execute=True):
            self.assertTrue(appt_3.cancel())
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )

        # When HAv2 is completed and a new booking/cancelation happens, then due_date shouldn't be updated
        form_submission = FormSubmission.objects.get(user_id=patient.id, form=form)
        form_submission.completed_at = timezone.now()
        form_submission.save()

        with self.captureOnCommitCallbacks(execute=True):
            self.assertTrue(appt_2.cancel())
        appt_4 = None
        with self.captureOnCommitCallbacks(execute=True):
            appt_4 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=6),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment",
            )

            AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=7),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment",
            )

        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )
        with self.captureOnCommitCallbacks(execute=True):
            self.assertTrue(appt_4.cancel())
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )

    def test_awv_case_closure_on_appointment_booking(self, mock_elation_update_user):
        patient = self.patient
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "New"}, "category": StatusCategory.NOT_STARTED},
                {
                    "state": {"name": "Automated Outreach 1"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 2"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 3"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Manual Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Final Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {"state": {"name": "Done"}, "category": StatusCategory.COMPLETE},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "New",
            "transitions": [
                {"dest": "New", "source": ["Done", "Will Not Do"], "trigger": "New"},
                {
                    "dest": "Automated Outreach 1",
                    "source": ["New"],
                    "trigger": "Automated Outreach 1",
                },
                {
                    "dest": "Automated Outreach 2",
                    "source": ["Automated Outreach 1"],
                    "trigger": "Automated Outreach 2",
                },
                {
                    "dest": "Automated Outreach 3",
                    "source": ["Automated Outreach 2"],
                    "trigger": "Automated Outreach 3",
                },
                {
                    "dest": "Manual Outreach",
                    "source": ["Automated Outreach 3"],
                    "trigger": "Manual Outreach",
                },
                {
                    "dest": "Final Outreach",
                    "source": ["Manual Outreach"],
                    "trigger": "Final Outreach",
                },
                {
                    "dest": "Will Not Do",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Will Not Do",
                },
                {
                    "dest": "Done",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Done",
                },
                {
                    "dest": "Done",
                    "source": "*",
                    "trigger": "auto_close",
                    "system_action": "system_close",
                },
                {
                    "dest": "New",
                    "source": "*",
                    "trigger": "reopened",
                    "system_action": "system_reopen",
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Annual Wellness Visit",
            content=state_machine_content,
        )
        category = CaseCategory.objects.create(
            title="Annual Wellness Visit",
            unique_key="annual_wellness_visit",
            state_machine_definition=state_machine_definition,
        )
        case = Case.objects.create(
            category=category,
            person=patient.person,
        )
        self.assertEqual(case.status, AWVCaseStatuses.NEW)
        with self.captureOnCommitCallbacks(execute=True):
            # Book appointment
            AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        case = Case.objects.filter(
            category=category,
            person=patient.person,
        ).first()
        # Do not close case, when the appointment booked is not with Physician/NP
        self.assertEqual(case.status, AWVCaseStatuses.NEW)

        np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        np_physician = PhysicianFactory.create(npi="**********")
        ProviderDetailFactory(
            internal_role=np_role,
            physician=np_physician,
        )
        appt = None
        with self.captureOnCommitCallbacks(execute=True):
            appt = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=6),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
                physician=np_physician,
            )

        # Close case, when the appointment booked is with Physician/NP
        case = Case.objects.filter(
            category=category,
            person=patient.person,
        ).first()
        self.assertEqual(case.status, AWVCaseStatuses.DONE)
        self.assertEqual(
            case.description,
            "Booked with " + appt.physician.first_name + " " + appt.physician.last_name + " on " + str(appt.start),
        )

    @override_flag(WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, active=True)
    def test_awv_case_closure_on_awv_appointment_booking(self, mock_elation_update_user):
        patient = self.patient
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "New"}, "category": StatusCategory.NOT_STARTED},
                {
                    "state": {"name": "Automated Outreach 1"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 2"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 3"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Manual Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Final Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {"state": {"name": "Done"}, "category": StatusCategory.COMPLETE},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "New",
            "transitions": [
                {"dest": "New", "source": ["Done", "Will Not Do"], "trigger": "New"},
                {
                    "dest": "Automated Outreach 1",
                    "source": ["New"],
                    "trigger": "Automated Outreach 1",
                },
                {
                    "dest": "Automated Outreach 2",
                    "source": ["Automated Outreach 1"],
                    "trigger": "Automated Outreach 2",
                },
                {
                    "dest": "Automated Outreach 3",
                    "source": ["Automated Outreach 2"],
                    "trigger": "Automated Outreach 3",
                },
                {
                    "dest": "Manual Outreach",
                    "source": ["Automated Outreach 3"],
                    "trigger": "Manual Outreach",
                },
                {
                    "dest": "Final Outreach",
                    "source": ["Manual Outreach"],
                    "trigger": "Final Outreach",
                },
                {
                    "dest": "Will Not Do",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Will Not Do",
                },
                {
                    "dest": "Done",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Done",
                },
                {
                    "dest": "Done",
                    "source": "*",
                    "trigger": "auto_close",
                    "system_action": "system_close",
                },
                {
                    "dest": "New",
                    "source": "*",
                    "trigger": "reopened",
                    "system_action": "system_reopen",
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Annual Wellness Visit",
            content=state_machine_content,
        )
        category = CaseCategory.objects.create(
            title="Annual Wellness Visit",
            unique_key="annual_wellness_visit",
            state_machine_definition=state_machine_definition,
        )
        case = Case.objects.create(
            category=category,
            person=patient.person,
        )
        self.assertEqual(case.status, AWVCaseStatuses.NEW)
        with self.captureOnCommitCallbacks(execute=True):
            # Book appointment
            AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.AWV_ESTABLISHED,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        case = Case.objects.filter(
            category=category,
            person=patient.person,
        ).first()
        # Do not close case, when the appointment booked is not with Physician/NP
        self.assertEqual(case.status, AWVCaseStatuses.NEW)

        np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        np_physician = PhysicianFactory.create(npi="**********")
        ProviderDetailFactory(
            internal_role=np_role,
            physician=np_physician,
        )
        appt = None
        with self.captureOnCommitCallbacks(execute=True):
            appt = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=6),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.AWV_ESTABLISHED,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
                physician=np_physician,
            )

        # Close case, when the appointment booked is with Physician/NP
        case = Case.objects.filter(
            category=category,
            person=patient.person,
        ).first()
        self.assertEqual(case.status, AWVCaseStatuses.DONE)
        self.assertEqual(
            case.description,
            "Booked with " + appt.physician.first_name + " " + appt.physician.last_name + " on " + str(appt.start),
        )

    @override_flag(WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, active=True)
    def test_do_not_close_awv_case_on_other_appointment_booking(self, mock_elation_update_user):
        patient = self.patient
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)
        state_machine_content = {
            "state_with_categories": [
                {"state": {"name": "New"}, "category": StatusCategory.NOT_STARTED},
                {
                    "state": {"name": "Automated Outreach 1"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 2"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Automated Outreach 3"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Manual Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {
                    "state": {"name": "Final Outreach"},
                    "category": StatusCategory.IN_PROGRESS,
                },
                {"state": {"name": "Done"}, "category": StatusCategory.COMPLETE},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "New",
            "transitions": [
                {"dest": "New", "source": ["Done", "Will Not Do"], "trigger": "New"},
                {
                    "dest": "Automated Outreach 1",
                    "source": ["New"],
                    "trigger": "Automated Outreach 1",
                },
                {
                    "dest": "Automated Outreach 2",
                    "source": ["Automated Outreach 1"],
                    "trigger": "Automated Outreach 2",
                },
                {
                    "dest": "Automated Outreach 3",
                    "source": ["Automated Outreach 2"],
                    "trigger": "Automated Outreach 3",
                },
                {
                    "dest": "Manual Outreach",
                    "source": ["Automated Outreach 3"],
                    "trigger": "Manual Outreach",
                },
                {
                    "dest": "Final Outreach",
                    "source": ["Manual Outreach"],
                    "trigger": "Final Outreach",
                },
                {
                    "dest": "Will Not Do",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Will Not Do",
                },
                {
                    "dest": "Done",
                    "source": [
                        "New",
                        "Automated Outreach 1",
                        "Automated Outreach 2",
                        "Automated Outreach 3",
                        "Manual Outreach",
                        "Final Outreach",
                    ],
                    "trigger": "Done",
                },
                {
                    "dest": "Done",
                    "source": "*",
                    "trigger": "auto_close",
                    "system_action": "system_close",
                },
                {
                    "dest": "New",
                    "source": "*",
                    "trigger": "reopened",
                    "system_action": "system_reopen",
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Annual Wellness Visit",
            content=state_machine_content,
        )
        category = CaseCategory.objects.create(
            title="Annual Wellness Visit",
            unique_key="annual_wellness_visit",
            state_machine_definition=state_machine_definition,
        )
        case = Case.objects.create(
            category=category,
            person=patient.person,
        )
        self.assertEqual(case.status, AWVCaseStatuses.NEW)

        np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        np_physician = PhysicianFactory.create(npi="**********")
        ProviderDetailFactory(
            internal_role=np_role,
            physician=np_physician,
        )
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=6),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
                physician=np_physician,
            )

        # Do not close case, when the appointment is booked with non AWV visit type
        case = Case.objects.filter(
            category=category,
            person=patient.person,
        ).first()
        self.assertEqual(case.status, AWVCaseStatuses.NEW)

    @patch("firefly.modules.appointment.signals.check_eligibility_for_patient")
    def test_eligibility_on_appointment_booking(self, check_eligibility_for_patient_mock, mock_elation_update_user):
        patient = self.patient
        practice, _pcp = _get_primary_care_data(patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            # Book appointment
            appointment = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        check_eligibility_for_patient_mock.send.assert_called_once_with(
            user_id=patient.pk,
            requested_by_user_id=get_lucian_bot_user().pk,
            appointment_date=f"{appointment.start.strftime('%m/%d/%Y %I:%M %p')}",
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            appointment_id=appointment.id,
        )

    def test_onboarding_outreach_case_closure_on_video_new_booking(self, mock_elation_update_user):
        patient = self.patient
        practice, _pcp = _get_primary_care_data(patient.person)

        # Create onboarding outreach case, to test closure after Video-New booking
        onboarding_appt_outreach_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_ONBOARDING_FIRST_APPT_OUTREACH,
        )
        first_apt_outreach, _ = StateMachineDefinition.objects.get_or_create(
            content=FIRST_APPOINTMENT_OUTREACH_STATE_MACHINE_CONTENT, title="First Appointment Outreach"
        )
        onboarding_appt_outreach_category.state_machine_definition = first_apt_outreach
        onboarding_appt_outreach_category.save(update_fields=["state_machine_definition"])
        CaseFactory(category=onboarding_appt_outreach_category, person=patient.person)
        outreach_case = Case.objects.filter(person=patient.person).first()
        self.assertEqual(outreach_case.status_category, StatusCategory.NOT_STARTED)
        with self.captureOnCommitCallbacks(execute=True):
            # Book appointment
            AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        outreach_case = Case.objects.filter(person=patient.person).first()
        self.assertEqual(outreach_case.status_category, StatusCategory.COMPLETE)

    def test_onboarding_outreach_case_closure_on_awv_new_booking(self, mock_elation_update_user):
        patient = self.patient
        practice, _pcp = _get_primary_care_data(patient.person)

        # Create onboarding outreach case, to test closure after Video-New booking
        onboarding_appt_outreach_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_ONBOARDING_FIRST_APPT_OUTREACH,
        )
        first_apt_outreach, _ = StateMachineDefinition.objects.get_or_create(
            content=FIRST_APPOINTMENT_OUTREACH_STATE_MACHINE_CONTENT, title="First Appointment Outreach"
        )
        onboarding_appt_outreach_category.state_machine_definition = first_apt_outreach
        onboarding_appt_outreach_category.save(update_fields=["state_machine_definition"])
        CaseFactory(category=onboarding_appt_outreach_category, person=patient.person)
        outreach_case = Case.objects.filter(person=patient.person).first()
        self.assertEqual(outreach_case.status_category, StatusCategory.NOT_STARTED)
        with self.captureOnCommitCallbacks(execute=True):
            # Book appointment
            AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.AWV_NEW,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        outreach_case = Case.objects.filter(person=patient.person).first()
        self.assertEqual(outreach_case.status_category, StatusCategory.COMPLETE)

    def test_update_due_date_on_appointment_deletion(self, mock_elation_update_user):
        patient = self.patient
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            # Create appointment
            appt_1 = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            (appt_1.start - timedelta(hours=24)),
        )

        # When deleting one and only one appt, due_date of the existing task should be set to 2 days after current time
        with self.captureOnCommitCallbacks(execute=True):
            appt_1.delete()
        appt_1.refresh_from_db()
        due_date = Task.objects.get(
            patient_id=patient.id,
            title=form.title,
            owner_group_id=patient.assignee_group.id,
        ).due_date
        expected_due_date = timezone.now() + timedelta(days=2)
        self.assertEqual(
            due_date.year,
            expected_due_date.year,
        )
        self.assertEqual(
            due_date.month,
            expected_due_date.month,
        )
        self.assertEqual(
            due_date.day,
            expected_due_date.day,
        )
        appt_2 = None
        with self.captureOnCommitCallbacks(execute=True):
            # When an appointment is booked in future, should consider new appt.start
            appt_2 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=5),
                duration="00:15:00",
                description="Back and ankle pain 2",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )
        appt_3 = None
        with self.captureOnCommitCallbacks(execute=True):
            # Create new appt, should consider new appt.start
            appt_3 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=4),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment",
            )
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_3.start - timedelta(hours=24),
        )

        # When earliest appt is deleted, should update due_date considering next earliest appt
        with self.captureOnCommitCallbacks(execute=True):
            appt_3.delete()
        appt_3.refresh_from_db()
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )

        # When the HAv2 is completed, new booking/cancelation happens due_date shouldn't be updated
        formSubmission = FormSubmission.objects.get(user_id=patient.id, form=form)
        formSubmission.completed_at = timezone.now()
        formSubmission.save()
        with self.captureOnCommitCallbacks(execute=True):
            appt_2.delete()
        appt_2.refresh_from_db()
        appt_4 = None
        with self.captureOnCommitCallbacks(execute=True):
            appt_4 = AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=6),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment",
            )

            AppointmentFactory(
                patient_id=patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + timedelta(days=7),
                duration="00:15:00",
                description="Back and ankle pain 3",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
                time_slot_type="appointment",
            )

        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )
        with self.captureOnCommitCallbacks(execute=True):
            appt_4.delete()
        appt_4.refresh_from_db()
        self.assertEqual(
            Task.objects.get(
                patient_id=patient.id,
                title=form.title,
                owner_group_id=patient.assignee_group.id,
            ).due_date,
            appt_2.start - timedelta(hours=24),
        )

    def test_no_cancel_past_appointment(self, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        patient = self.patient
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() - timedelta(minutes=15)),
            duration="00:15:00",
            description="A past appointment which should not cancellable",
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        with self.captureOnCommitCallbacks(execute=True):
            appointment.cancel()
        self.assertEqual(appointment.deleted, None)
        self.assertEqual(appointment.status, AppointmentStatus.SCHEDULED.value)

    @patch("firefly.core.services.braze.client.BrazeClient.submit_event")
    def test_cancel_past_appointment_api(self, mock_submit_event, mock_elation_update_user):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        patient = self.patient
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() - timedelta(minutes=15)),
            duration="00:15:00",
            description="A past appointment which should not cancellable",
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )

        url = "/appointment/%s/cancel/v2/" % appointment.id
        self.client.force_authenticate(user=patient)
        with self.captureOnCommitCallbacks(execute=True):
            response = self.client.post(url, {}, format="json")
        mock_submit_event.assert_not_called()
        self.assertEqual(response.status_code, 422)
        self.assertEqual(response.data, {"detail": "Unable to cancel appointment."})
        self.assertEqual(appointment.deleted, None)
        self.assertIsNone(appointment.sms_notice_sent_at)
        self.assertEqual(appointment.status, AppointmentStatus.SCHEDULED.value)

    def test_needs_last_minute_notification(self, mock_elation_update_user):
        appointment = AppointmentFactory.build(
            patient_id=self.patient.pk,
            sms_notice_sent_at=None,
            scheduled_at=timezone.now(),
            start=(timezone.now() + timedelta(minutes=125)),  # a little more than two hours from now -> no alert
        )
        self.assertFalse(appointment.needs_last_minute_notification)
        appointment = AppointmentFactory.build(
            patient_id=self.patient.pk,
            sms_notice_sent_at=None,
            scheduled_at=timezone.now(),
            start=(timezone.now() + timedelta(minutes=1)),  # only a minute from now -> needs an alert
        )
        self.assertTrue(appointment.needs_last_minute_notification)
        appointment = AppointmentFactory.build(
            patient_id=self.patient.pk,
            scheduled_at=timezone.now(),
            start=(timezone.now() + timedelta(minutes=1)),  # only a minute from now
            sms_notice_sent_at=timezone.now(),  # but alert has already been sent
        )
        self.assertFalse(appointment.needs_last_minute_notification)

    def test_appointment_update_request(self, mock_elation_update_user):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        # Create appointment
        appt = AppointmentFactory(
            patient_id=self.patient.pk,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() + relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )

        self.assertFalse(appt.scheduled_at is None)

        # Change appointment duration
        appt_patch_payload = {"description": "Back and ankle pain"}
        patch_response = self.client.patch("/appointment/%s" % appt.id, appt_patch_payload, format="json")
        self.assertEqual(patch_response.status_code, 200)

        appt.refresh_from_db()
        self.assertEqual(
            AppointmentSerializer(instance=appt).data.get("description"),
            "Back and ankle pain",
        )
        appt.refresh_from_db()
        self.assertEqual(
            AppointmentSerializer(instance=appt).data.get("description"),
            "Back and ankle pain",
        )

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_assign_health_assessment_awv_new(self, mocked_assign_assessment_form, mock_elation_update_user):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            # Create multiple appointments
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=12, day=2),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.AWV_NEW,
                status=AppointmentStatus.SCHEDULED.value,
            )
        mocked_assign_assessment_form.assert_called_once()

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_assign_health_assessment_video_new(self, mocked_assign_assessment_form, mock_elation_update_user):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            # Create multiple appointments
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=12, day=2),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        mocked_assign_assessment_form.assert_called_once()

    def test_assign_health_assessment(self, mock_elation_update_user):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            # Create multiple appointments
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=12, day=2),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        appt_2 = None
        with self.captureOnCommitCallbacks(execute=True):
            appt_2 = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=11, day=26),
                duration="00:15:00",
                description="Back and ankle pain",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )

        task = Task.objects.get(
            patient_id=self.patient.id,
            title=form.title,
            owner_group_id=self.patient.assignee_group.id,
        )

        self.assertTrue(FormSubmission.objects.filter(user_id=self.patient.id, form=form).exists())

        # Due date of HAv2 should be 24 hrs before the earliest appt
        self.assertIsNotNone(task)
        self.assertEqual(task.due_date, appt_2.start - timedelta(days=1))

        # When a new appt.start >= HAv2_task.due_date, shouldn't update due_date of the existing task
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=11, day=28),
                duration="00:15:00",
                description="Back and ankle pain",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        task.refresh_from_db()
        self.assertEqual(task.due_date, appt_2.start - timedelta(days=1))

        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=10, day=30),
                duration="00:15:00",
                description="Back and ankle pain",
                reason=AppointmentReason.URGENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        task.refresh_from_db()
        # Due date of HAv2 should be 24 hrs before the earliest of Video-New appts, shouldn't consider Urgent appts
        self.assertEqual(task.due_date, appt_2.start - timedelta(days=1))

        # When a new appt.start < HAv2_task.due_date, should update due_date of the existing task to be
        # 24 hrs before the new appointment.start
        appt_4 = None
        with self.captureOnCommitCallbacks(execute=True):
            appt_4 = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=10, day=28),
                duration="00:15:00",
                description="Back and ankle pain",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        task.refresh_from_db()
        self.assertEqual(task.due_date, appt_4.start - timedelta(days=1))

        # Should not assign HAv2, if any of the existing appt is complete
        appt_4.status = AppointmentStatus.BILLED
        appt_4.save()
        task.is_complete = True

        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=9, day=28),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
            )
        task.refresh_from_db()
        self.assertEqual(task.due_date, appt_4.start - timedelta(days=1))

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_no_assign_health_assessment_for_non_primary_care_appt(
        self, mocked_assign_assessment_form, mock_elation_update_user
    ):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()

        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                status=AppointmentStatus.SCHEDULED.value,
            )

        mocked_assign_assessment_form.assert_not_called()

    def test_include_health_assessment_in_appointment_serializer(self, mock_elation_update_user):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=12, day=2),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )

        form_submission = FormSubmission.objects.filter(
            user_id=self.patient.person.user.id,
            form=form,
        ).first()

        response = self.client.get("/appointment/?status=Scheduled")

        self.assertEqual(response.data[0]["health_assessment"], form_submission.id)

        form_submission.completed_at = timezone.now()
        form_submission.save()

        response = self.client.get("/appointment/?status=Scheduled")

        self.assertIsNone(response.data[0]["health_assessment"])

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_ha_v3_assignment_when_ha_v1_is_completed(self, mock_assign_asssessment_form, mock_elation_update_user):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        # Creating HAv1 form to check HAv2 is not re-assigned when it exists
        ha_v1_form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT,
            title="About You & Your Health",
            description="Test",
            is_active=True,
        )
        ha_v3_form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        FormSubmissionFactory(form=ha_v1_form, user=self.patient, completed_at=timezone.now())

        # Create appointment
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
            )
        ha_v3_form_submission = FormSubmission.objects.filter(user_id=self.patient.id, form=ha_v3_form)

        self.assertFalse(ha_v3_form_submission.exists())
        mock_assign_asssessment_form.assert_not_called()

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_ha_v2_assignment_when_ha_v2_is_completed(self, mock_assign_asssessment_form, mock_elation_update_user):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        # Creating HAv3 form to check HAv2 is not re-assigned when it is completed
        ha_v3_form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        FormSubmissionFactory(form=ha_v3_form, user=self.patient, completed_at=timezone.now())

        # Create appointment
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
            )

        mock_assign_asssessment_form.assert_not_called()

    @patch("firefly.modules.appointment.signals.assign_assessment_form")
    def test_ha_v2_assignment_with_wrong_reason(self, mock_assign_asssessment_form, mock_elation_update_user):
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()

        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        # Create appointment
        with self.captureOnCommitCallbacks(execute=True):
            appt = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
            )
        # If reason is other than "Video-New" do not assign HAv2
        appt.reason = AppointmentReason.VIDEO
        appt.save()
        ha_v2_form_submission = FormSubmission.objects.filter(user_id=self.patient.id, form=form)
        self.assertFalse(ha_v2_form_submission.exists())
        mock_assign_asssessment_form.assert_not_called()

    def test_ha_v2_assignment_when_already_completed(self, mock_elation_update_user):
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        form.refresh_from_db()

        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        # If HAv2 is completed, do not re-assign
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now().replace(year=2025, month=12, day=2),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )
        ha_v2_form_submission = FormSubmission.objects.filter(user_id=self.patient.id, form=form)
        ha_v2_form_submission.update(completed_at=timezone.now())
        with self.captureOnCommitCallbacks(execute=True):
            AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=2091),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
            )
        incomplete_ha_v2_form_submission = FormSubmission.objects.filter(
            user_id=self.patient.id, form=form, completed_at__isnull=True
        )
        self.assertFalse(incomplete_ha_v2_form_submission.exists())

    @patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.update_record")
    @patch("firefly.modules.appointment.slots.elation.AppointmentSlotElationClient.get_record")
    def test_claiming_appt_without_second_get_from_elation(self, mock_get, mock_update, mock_elation_update_user):
        elation_id = 98981923123
        practice, pcp = _get_primary_care_data(self.patient.person)
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient=None,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=(timezone.now() + timedelta(days=1)),
                duration="00:15:00",
                time_slot_type="appointment_slot",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                elation_id=elation_id,
            )

        payload = {"elation_id": elation_id, "description": "test"}

        update_return = ElationAppointmentSync().get_elation_friendly_payload(appointment)

        # mock elation response
        test_slot = {"patient": None}
        mock_get.return_value = test_slot
        mock_update.return_value = update_return

        self.client.patch(f"/appointment/slot/v2/{elation_id}", format="json", data=payload)
        appointment.refresh_from_db()
        self.assertEqual(appointment.status, "Scheduled")

    @patch.object(ObjectToElationRecord, "send_object")
    def test_appointment_join_request(self, patched_elation_send_object, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        with self.captureOnCommitCallbacks(execute=True):
            appt = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO,
                patient_joined_video=False,
            )
        self.assertFalse(appt.scheduled_at is None)
        patched_elation_send_object.reset_mock()
        with override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True)):
            join_response = self.client.get("/appointment/%s/join/" % appt.id)
        self.assertEqual(join_response.status_code, 200)
        self.assertTrue("session_name" in join_response.json())
        self.assertTrue("sdk" in join_response.json())
        self.assertTrue("token" in join_response.json())
        appt.refresh_from_db()
        self.assertTrue(appt.patient_joined_video)
        patched_elation_send_object.assert_not_called()

    @patch("firefly.core.user.signals.update_auth0_user")
    def test_mark_appointments(self, _mock_update_auth0_user, mock_elation_update_user):
        category = CaseCategoryFactory(title=CaseCategoryTitle.APPOINTMENT_NO_SHOW)
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)
        self.patient.person.enable_appt_notifications = True
        self.patient.person.save()

        provider = ProviderDetailFactory(physician=PhysicianFactory())
        provider.user.is_active = False
        provider.user.save()
        patient_wo_person = PatientUserFactory()
        missed_appt = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        completed_appt = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=True,
        )
        missed_appt_for_patient_wo_person = AppointmentFactory(
            patient=patient_wo_person,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        completed_appt_with_in_active_physician = AppointmentFactory(
            patient=self.patient,
            physician_id=provider.physician.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=True,
        )
        completed_appt_30_mins_back = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(hours=1),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=True,
        )
        self.assertEqual(completed_appt_30_mins_back.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(missed_appt.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(completed_appt.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(missed_appt_for_patient_wo_person.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(
            completed_appt_with_in_active_physician.status,
            AppointmentStatus.SCHEDULED.value,
        )
        mark_appointments(days=1)
        missed_appt.refresh_from_db()
        completed_appt.refresh_from_db()
        missed_appt_for_patient_wo_person.refresh_from_db()
        completed_appt_with_in_active_physician.refresh_from_db()
        completed_appt_30_mins_back.refresh_from_db()
        self.assertEqual(completed_appt_30_mins_back.status, AppointmentStatus.CHECKED_OUT.value)
        self.assertEqual(missed_appt.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(completed_appt.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(missed_appt_for_patient_wo_person.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(
            completed_appt_with_in_active_physician.status,
            AppointmentStatus.SCHEDULED.value,
        )
        case = Case.objects.filter(category_id=category.id, person_id=missed_appt.patient.person.id)
        self.assertEqual(case.count(), 0)
        mark_appointments()
        missed_appt.refresh_from_db()
        completed_appt.refresh_from_db()
        missed_appt_for_patient_wo_person.refresh_from_db()
        completed_appt_with_in_active_physician.refresh_from_db()
        self.assertEqual(missed_appt.status, AppointmentStatus.NOT_SEEN.value)
        self.assertEqual(completed_appt.status, AppointmentStatus.CHECKED_OUT.value)
        self.assertEqual(missed_appt_for_patient_wo_person.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(
            completed_appt_with_in_active_physician.status,
            AppointmentStatus.SCHEDULED.value,
        )
        case = Case.objects.filter(category_id=category.id, person_id=missed_appt.patient.person.id)
        self.assertEqual(case.count(), 1)
        missed_appt_new_patient = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back 2",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        completed_appt_new_patient = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back 2",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            patient_joined_video=True,
        )
        in_progess_appt = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now(),
            duration="00:15:00",
            description="Strained back 2",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            patient_joined_video=True,
        )
        self.assertEqual(missed_appt_new_patient.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(completed_appt_new_patient.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(in_progess_appt.status, AppointmentStatus.SCHEDULED.value)
        mark_appointments()
        missed_appt_new_patient.refresh_from_db()
        completed_appt_new_patient.refresh_from_db()
        in_progess_appt.refresh_from_db()
        self.assertEqual(missed_appt_new_patient.status, AppointmentStatus.NOT_SEEN.value)
        self.assertEqual(completed_appt_new_patient.status, AppointmentStatus.CHECKED_OUT.value)
        # Shouldn't complete the in progress appt
        self.assertEqual(in_progess_appt.status, AppointmentStatus.SCHEDULED.value)

        # Let's check the API response as well
        response = self.client.get("/appointment/?missed=True&reason=Video")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]["status"], AppointmentStatus.NOT_SEEN.value)
        self.assertEqual(response.data[0]["missed"], True)

        response = self.client.get("/appointment/?missed=True")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]["status"], AppointmentStatus.NOT_SEEN.value)
        self.assertEqual(response.data[0]["missed"], True)

        # We should have 5 non-missed appointments
        response = self.client.get("/appointment/?missed=False&reason=Video")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 5)

        response = self.client.get("/appointment/?missed=False")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 5)

        appt = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(minutes=15),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=True,
        )
        mark_appointments()
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.SCHEDULED.value)

        appt = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(minutes=50),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            patient_joined_video=True,
        )
        mark_appointments()
        appt.refresh_from_db()
        self.assertEqual(appt.status, AppointmentStatus.CHECKED_OUT.value)

    @patch("firefly.modules.appointment.utils.reminders")
    def test_auto_cancel_appointments_with_incomplete_health_assessment(self, mock_reminders, mock_elation_update_user):
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(self.patient.person)

        appt_1 = None
        appt_2 = None
        with self.captureOnCommitCallbacks(execute=True):
            # Create appointment
            appt_1 = AppointmentFactory(
                patient_id=self.patient.pk,
                practice_id=practice.id,
                physician_id=pcp.id,
                start=timezone.now() + timedelta(hours=23),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )

            appt_2 = AppointmentFactory(
                patient_id=self.patient.pk,
                practice_id=practice.id,
                physician_id=pcp.id,
                start=timezone.now() + timedelta(days=3),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )

        mock_reminders.reset_mock()
        with self.captureOnCommitCallbacks(execute=True):
            auto_cancel_appointments_with_incomplete_health_assessment()
        mock_reminders.send.assert_called_once_with(settings.BRAZE["HA_APPT_CANCEL"], InstanceOf(list))

        appt_1.refresh_from_db()
        self.assertIsNone(appt_1.patient)
        self.assertEqual(
            appt_1.cancelation_reason,
            AppointmentCancelationReason.INCOMPLETE_HEALTH_ASSESSMENT,
        )
        self.assertTrue(appt_1.canceled_by_system)

        # Confirm that we store the cancelation reason in the cancelation event logs

        appointment_cancellation_logs = EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_CANCELLED)
        self.assertEqual(appointment_cancellation_logs.count(), 1)
        event = appointment_cancellation_logs.first()

        bot = get_lucian_bot_user()

        self.assertEqual(
            event.metadata["current_appointment"],
            {
                "elation_id": appt_1.elation_id,
                "physician_id": appt_1.physician_id,
                "reason": appt_1.reason,
                "description": appt_1.description,
                "patient_id": appt_1.patient_id,
                "updated_by_id": bot.pk,
                "cancelation_reason": AppointmentCancelationReason.INCOMPLETE_HEALTH_ASSESSMENT,
                "cancellation_reasons": [],
                "patient_other_reason": None,
                "firefly_other_reason": None,
                "reschedule_action": None,
                "canceled_by_system": True,
                "action_performed": "canceled",
                "action_performed_by": "system",
            },
        )

        # Due date of the task should also be updated after cancelation
        self.assertEqual(
            Task.objects.get(
                patient_id=self.patient.id,
                title=form.title,
                owner_group_id=self.patient.assignee_group.id,
            ).due_date,
            (appt_2.start - timedelta(hours=24)),
        )

    def test_do_not_auto_cancel_appointment_when_completed_appointment_exists(self, mock_elation_update_user):
        patient = self.patient
        User.objects.get_or_create(first_name="Clinical Operations", last_name="Group")
        FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT,
            title="About You & Your Health",
            description="Test",
            is_active=True,
        )
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appt_1 = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=23),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.CHECKED_OUT.value,
        )

        appt_2 = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(days=3),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        auto_cancel_appointments_with_incomplete_health_assessment()

        appt_1.refresh_from_db()
        appt_2.refresh_from_db()
        self.assertIsNotNone(appt_1.patient)
        self.assertIsNotNone(appt_2.patient)

    def test_do_not_auto_cancel_appointment_when_completed_HA_exists(self, mock_elation_update_user):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        ha_v2_form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )
        FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT,
            title="About You & Your Health",
            description="Test",
            is_active=True,
        )
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        appt_1 = None
        appt_2 = None
        with self.captureOnCommitCallbacks(execute=True):
            # Create appointment
            appt_1 = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                physician_id=pcp.id,
                start=timezone.now() + timedelta(hours=23),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )

            appt_2 = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                physician_id=pcp.id,
                start=timezone.now() + timedelta(days=3),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
        # when a completed HAv2 exists, shouldn't cancel the appointment
        self.assertTrue(FormSubmission.objects.filter(user_id=self.patient.id, form=ha_v2_form).exists())
        ha_v2_form_submission = FormSubmission.objects.filter(user_id=self.patient.id, form=ha_v2_form)
        ha_v2_form_submission.update(completed_at=timezone.now())

        auto_cancel_appointments_with_incomplete_health_assessment()

        appt_1.refresh_from_db()
        appt_2.refresh_from_db()
        self.assertIsNotNone(appt_1.patient)
        self.assertIsNotNone(appt_2.patient)

    def test_reason_in_query_param(self, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - relativedelta(years=80),
            duration="00:15:00",
            description="Strained back 2",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        response = self.client.get(
            f"/appointment/?reason__in={AppointmentReason.VIDEO},{AppointmentReason.VIDEO_NEW_PATIENT}"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)

    @patch("firefly.modules.appointment.signals.assign_post_visit_survey")
    def test_throttle_post_visit_survey(self, mock_assign_survey, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=7),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        appointment = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() + timedelta(days=7),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        appointment.status = AppointmentStatus.COMPLETED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        mock_assign_survey.assert_not_called()

    @patch("firefly.modules.appointment.signals.assign_post_visit_survey")
    def test_assign_new_patient_survey(self, mock_assign_survey, mock_elation_update_user):
        self.patient.onboarding_state.to_member()
        practice, pcp = _get_primary_care_data(self.patient.person)
        appointment = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=50),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        appointment.status = AppointmentStatus.COMPLETED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        uid = FormUID.POST_VISIT_REVIEW_ESTABLISHED
        mock_assign_survey.assert_called_with(self.patient, uid)

    @patch("firefly.modules.appointment.signals.assign_post_visit_survey")
    def test_assign_bh_patient_survey(self, mock_assign_survey, mock_elation_update_user):
        self.patient.onboarding_state.to_member()
        practice, pcp = _get_primary_care_data(self.patient.person)
        AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=60),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        appointment = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=50),
            duration="00:15:00",
            description="Strained back",
            reason="Behavioral Health",
        )
        appointment.status = AppointmentStatus.COMPLETED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        uid = FormUID.POST_VISIT_REVIEW_ESTABLISHED
        mock_assign_survey.assert_called_with(self.patient, uid)

    @patch("firefly.modules.appointment.signals.assign_post_visit_survey")
    def test_assign_established_patient_survey(self, mock_assign_survey, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=60),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        appointment = AppointmentFactory(
            patient=self.patient,
            physician_id=pcp.id,
            practice_id=practice.id,
            start=timezone.now() - timedelta(days=50),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
        )
        self.patient.onboarding_state.to_member(actor=self.patient)
        appointment.status = AppointmentStatus.COMPLETED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        uid = FormUID.POST_VISIT_REVIEW_ESTABLISHED
        mock_assign_survey.assert_called_with(self.patient, uid)

    @patch("firefly.modules.appointment.signals.render_to_string")
    @patch("firefly.modules.appointment.signals.twilio_sms")
    def test_notify_provider_of_last_minute_appointment(
        self,
        mock_twilio_sms,
        mock_render_to_string,
        mock_elation_update_user,
    ):
        provider = ProviderDetailFactory(physician=PhysicianFactory())
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                physician=provider.physician,
            )
        mock_render_to_string.return_value = "Test message"

        def trigger_signal():
            notify_provider_of_last_minute_appointment(appointment=appointment)

        # Given that the appointment is not last minute, and should not need a notification
        with mock.patch.object(
            Appointment,
            "needs_last_minute_notification",
            new_callable=mock.PropertyMock,
            return_value=False,
        ):
            trigger_signal()
        mock_twilio_sms.send.assert_not_called()
        # Given that the appointment is last minute, and should need a notification
        with mock.patch.object(
            Appointment,
            "needs_last_minute_notification",
            new_callable=mock.PropertyMock,
            return_value=True,
        ):
            trigger_signal()
        mock_twilio_sms.send.assert_called_once_with(
            provider.user.phone_number,
            mock_render_to_string.return_value,
            "LAST_MIN_APPOINTMENT_REMINDER",
        )

    @patch("firefly.modules.appointment.signals.render_to_string")
    @patch("firefly.modules.appointment.signals.twilio_sms")
    def test_notify_provider_of_last_minute_cancellation(
        self,
        mock_twilio_sms,
        mock_render_to_string,
        mock_elation_update_user,
    ):
        provider = ProviderDetailFactory(physician=PhysicianFactory())
        mock_render_to_string.return_value = "Test message"
        appointment_long = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment_long = AppointmentFactory(
                patient_id=self.patient.pk,
                practice_id=self.practice.id,
                physician=provider.physician,
                start=(timezone.now() + timedelta(days=30)),
                duration="00:15:00",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )

        appointment_long.cancel()
        mock_twilio_sms.send.assert_not_called()

        appointment_short = None
        with self.captureOnCommitCallbacks(execute=True):
            # should send cancellation
            appointment_short = AppointmentFactory(
                patient_id=self.patient.pk,
                practice_id=self.practice.id,
                physician=provider.physician,
                start=(timezone.now() + timedelta(minutes=60)),
                duration="00:15:00",
                reason=AppointmentReason.VIDEO_NEW_PATIENT,
                status=AppointmentStatus.SCHEDULED.value,
            )

        appointment_short.cancel()
        mock_twilio_sms.send.assert_called_once()

    def test_visible_field_on_create(self, mock_elation_update_user):
        new_appt = AppointmentFactory(visible=None)
        self.assertEqual(new_appt.visible, False)
        provider = ProviderDetailFactory(physician=PhysicianFactory())
        new_appt = Appointment.objects.create(
            elation_id=990003,
            physician=provider.physician,
            start=timezone.now(),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason=AppointmentReason.VIDEO,
        )
        self.assertEqual(new_appt.visible, False)

    @override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True))
    @patch.object(ObjectToElationRecord, "send_object")
    def test_pre_save_appointment_sync(self, mock_send_object, mock_elation_update_user):
        # explicitly connect the model handler since we
        # want to test this behavior
        ElationAppointmentSync().connect_model_listener()
        provider = ProviderDetailFactory(physician=PhysicianFactory())
        # Scenerio
        # Source = Elation
        # should sync to Elation
        Appointment.objects.create(
            elation_id=990005,
            physician=provider.physician,
            start=timezone.now(),
            duration="00:15:00",
            time_slot_type="appointment",
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.ELATION,
        )
        mock_send_object.assert_called_once()

        # Scenerio
        # Source = Lucian
        # should skip sync to Elation
        mock_send_object.reset_mock()
        Appointment.objects.create(
            physician=provider.physician,
            start=timezone.now(),
            duration="00:15:00",
            time_slot_type="appointment",
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
        )
        mock_send_object.assert_not_called()


@patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class AppointmentEventTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)
        CaseCategoryFactory(
            title="Test Category",
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
        )
        self.scheduled_date = datetime.now() + timedelta(hours=2)

        self.appt_provider = ProviderDetailFactory()
        # create provider schedule
        ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.appt_provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )

    def test_appointment_event_logs_booking_views(self, mock_elation_update_user):
        # Give the patient permission to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - timedelta(days=1)
        self.patient.onboarding_state.save()

        # Test a Patient booking
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                physician=self.appt_provider.physician,
                start=self.scheduled_date,
                patient=None,
                source=AppointmentSource.LUCIAN,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
        with self.captureOnCommitCallbacks(execute=True):
            response = self.client.patch(
                f"/appointment/slot/v3/{appointment.id}", format="json", data={"description": "Testing Event Logs"}
            )

        self.assertEqual(response.status_code, 200)
        event_logs = list(
            EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED, target_object_id=appointment.id)
        )
        self.assertEqual(len(event_logs), 1)
        self.assertEqual(event_logs[0].metadata["current_appointment"]["action_performed_by"], "patient")
        self.assertEqual(event_logs[0].metadata["current_appointment"]["updated_by_id"], self.patient.id)

        # Test a Provider booking
        with self.captureOnCommitCallbacks(execute=True):
            appointment.patient_id = None
            appointment.deleted = None
            appointment.save(update_fields=["patient_id", "deleted"])
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.patch(
                f"/appointment/slot/provider/book-appointment/v2/{appointment.id}",
                format="json",
                data={"description": "Testing Event Logs", "patient_id": self.patient.id},
            )

        self.assertEqual(response.status_code, 200)
        event_logs = list(
            EventLog.objects.filter(
                type=EventTypeCodes.APPOINTMENT_SCHEDULED, target_object_id=appointment.id
            ).order_by("pk")
        )
        self.assertEqual(len(event_logs), 2)
        self.assertEqual(event_logs[1].metadata["current_appointment"]["action_performed_by"], "provider")
        self.assertEqual(event_logs[1].metadata["current_appointment"]["updated_by_id"], self.provider.id)

        # Test a reschedule
        # Try to call change API for same appointment with same physician
        with self.captureOnCommitCallbacks(execute=True):
            appointment.patient_id = None
            appointment.deleted = None
            appointment.save(update_fields=["patient_id", "deleted"])
        with self.captureOnCommitCallbacks(execute=True):
            response = self.client.post(
                f"/appointment/{appointment.pk}/change/v2/",
                format="json",
                data={
                    "id": appointment.pk,
                    "description": "Reschedule",
                },
            )

        self.assertEqual(response.status_code, 200)
        event_logs = list(
            EventLog.objects.filter(
                type=EventTypeCodes.APPOINTMENT_SCHEDULED, target_object_id=appointment.id
            ).order_by("pk")
        )
        self.assertEqual(len(event_logs), 2)
        self.assertEqual(event_logs[0].metadata["current_appointment"]["action_performed_by"], "patient")
        self.assertEqual(event_logs[0].metadata["current_appointment"]["updated_by_id"], self.patient.id)

    def test_appointment_event_log_appointment(self, mock_elation_update_user):
        practice, pcp = _get_primary_care_data(self.patient.person)
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )
        self.assertEqual(
            EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED).count(),
            1,
        )

        self.assertTrue(appointment.cancel())

        # Events are also created when time_slot_type is 'appointment'
        appointment_cancellation_logs = EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_CANCELLED)
        self.assertEqual(
            appointment_cancellation_logs.count(),
            1,
        )
        event = appointment_cancellation_logs.first()

        bot = get_lucian_bot_user()

        self.assertEqual(
            event.metadata["current_appointment"],
            {
                "elation_id": appointment.elation_id,
                "physician_id": appointment.physician_id,
                "reason": appointment.reason,
                "description": appointment.description,
                "patient_id": appointment.patient_id,
                "updated_by_id": bot.pk,
                "cancelation_reason": None,
                "cancellation_reasons": [],
                "patient_other_reason": None,
                "firefly_other_reason": None,
                "reschedule_action": None,
                "canceled_by_system": None,
                "action_performed": "canceled",
                "action_performed_by": "provider",
            },
        )


class AppointmentUtilsTestCase(FireflyTestCase):
    def test_assign_post_visit_survey(self):
        form = Form.objects.create()
        uid = "TestSurvey1"
        task_collection = TaskCollection.objects.create(title="Test Task Collection")
        TaskCollectionTask.objects.create(
            uid=uid,
            days_offset=1,
            form=form,
            task_collection=task_collection,
            title="Test Task Collection Task Title",
        )
        tasks1 = Task.objects.filter(patient=self.patient, is_complete=False, autocreated_from__uid=uid)
        self.assertEqual(len(tasks1), 0)
        assign_post_visit_survey(self.patient, uid)
        tasks2 = Task.objects.filter(patient=self.patient, is_complete=False, autocreated_from__uid=uid)
        self.assertEqual(len(tasks2), 1)
        task2 = tasks2.first()
        self.assertEqual(task2.autocreated_from.uid, uid)
        assign_post_visit_survey(self.patient, uid)
        tasks3 = Task.objects.filter(patient=self.patient, is_complete=False, autocreated_from__uid=uid)
        self.assertEqual(len(tasks3), 1)
        task3 = tasks3.first()
        self.assertEqual(task3.autocreated_from.uid, uid)
        self.assertNotEqual(task2.id, task3.id)


@patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class AppointmentNonEnglishCaseTests(FireflyTestCase):
    def setUp(self):
        super().setUp()
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)

    def test_create_case_for_non_english_speaker(self, mock_elation_update_user):
        from django.contrib.contenttypes.models import ContentType

        state_machine_content = {
            "initial_state": "New",
            "state_with_categories": [
                {"state": {"name": "New"}, "category": "not_started"},
                {"state": {"name": "Done"}, "category": "complete"},
            ],
            "transitions": [
                {"dest": "Done", "source": "*", "trigger": "Done"},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "Done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Test",
            content=state_machine_content,
        )
        category = CaseCategoryFactory(
            title="Test Category",
            unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
            state_machine_definition=state_machine_definition,
        )
        practice, pcp = _get_primary_care_data(self.patient.person)
        patient = self.patient
        self.patient.person.preferred_language = PreferredLanguageFactory(name="Other")
        self.patient.person.preferred_language_other = "French"
        self.patient.person.save()
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient_id=patient.pk,
                practice_id=practice.id,
                start=(timezone.now() + timedelta(hours=24)),
                duration="00:15:00",
                description="A future appointment",
                reason=AppointmentReason.VIDEO,
                status=AppointmentStatus.SCHEDULED.value,
            )

        # Test Only one case created with appointment object
        filtered_case = Case.objects.filter(
            person=patient.person,
            category=category,
            relations__content_type=ContentType.objects.get_for_model(appointment),
            relations__object_id=appointment.pk,
        )

        self.assertEqual(filtered_case.count(), 1)

        case = filtered_case.first()
        filtered_case_relation = CaseRelation.objects.filter(
            case=case,
            content_type=ContentType.objects.get_for_model(appointment),
            object_id=appointment.pk,
        )

        self.assertEqual(filtered_case.count(), 1)
        self.assertEqual(filtered_case_relation.count(), 1)

        # Test No new case created after calling the function again
        create_case_for_non_english_speaker(appointment)

        self.assertEqual(filtered_case.count(), 1)
        self.assertEqual(filtered_case_relation.count(), 1)
        self.assertEqual(filtered_case[0].status, "New")

        # appointment cancel
        with self.captureOnCommitCallbacks(execute=True):
            appointment.cancel()
        filtered_case[0].refresh_from_db()
        self.assertEqual(filtered_case[0].status, "Done")
        self.assertEqual(filtered_case[0].action, "auto_close")
        self.assertEqual(filtered_case[0].status_category, "complete")

    @patch("firefly.modules.appointment.signals.create_case_for_non_english_speaker")
    def test_create_case_is_called_when_appt_scheduled(
        self, mock_create_case_for_non_english_speaker, mock_elation_update_user
    ):
        practice, pcp = _get_primary_care_data(self.patient.person)
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient_id=self.patient.pk,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment",
                status=AppointmentStatus.SCHEDULED.value,
            )

        mock_create_case_for_non_english_speaker.assert_called_once_with(appointment)

    @patch("firefly.modules.appointment.signals.create_case_for_non_english_speaker")
    def test_create_case_when_no_patient_initially(
        self, mock_create_case_for_non_english_speaker, mock_elation_update_user
    ):
        practice, pcp = _get_primary_care_data(self.patient.person)
        appointment = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment = AppointmentFactory(
                patient=None,
                physician_id=pcp.id,
                practice_id=practice.id,
                start=timezone.now() + relativedelta(years=80),
                duration="00:15:00",
                description="Strained back",
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )

        mock_create_case_for_non_english_speaker.assert_not_called()

        appointment.patient = self.patient
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()

        mock_create_case_for_non_english_speaker.assert_called_once_with(appointment)
