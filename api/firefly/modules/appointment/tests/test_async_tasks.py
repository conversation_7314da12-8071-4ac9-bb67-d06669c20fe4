from unittest import mock

from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from faker import Faker

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.appointment.constants import AppointmentSource
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.tasks import (
    apply_appointment_rules_async,
    publish_appointment_to_calendar_async,
    publish_appointment_to_elation_async,
    remove_appointment_from_calendar_async,
)


@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch.object(
    WindowRateLimiter,
    "_acquire",
    return_value=True,
)
class AppointmentAsyncTasksTestCase(FireflyTestCase):
    @mock.patch.object(ElationAppointmentSync, "sync_appointment_to_elation")
    def test_publish_appointment_to_elation_async(self, sync_appointment_to_elation_mock, _backend_mock, _acquire_mock):
        self.patient.person.elation_id = Faker().pyint()
        self.patient.person.save()
        appointment: Appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            elation_id=None,
            practice_id=self.practice.id,
            source=AppointmentSource.LUCIAN,
        )
        deleted_appt: Appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            elation_id=None,
            practice_id=self.practice.id,
            source=AppointmentSource.LUCIAN,
        )
        deleted_appt.delete()
        for test_appt in [appointment, deleted_appt]:
            sync_appointment_to_elation_mock.reset_mock()
            publish_appointment_to_elation_async(appointment_id=test_appt.pk, log_prefix="")
            sync_appointment_to_elation_mock.assert_called_once_with(appointment=test_appt, log_prefix="")

    @mock.patch.object(ElationAppointmentSync, "sync_appointment_to_elation")
    def test_publish_appointment_to_elation_async_without_elation_id(
        self, sync_appointment_to_elation_mock, _backend_mock, _acquire_mock
    ):
        appointment: Appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            elation_id=None,
            practice_id=self.practice.id,
            source=AppointmentSource.LUCIAN,
        )
        sync_appointment_to_elation_mock.reset_mock()
        with self.assertRaises(Exception):
            publish_appointment_to_elation_async(appointment_id=appointment.pk, log_prefix="")
        sync_appointment_to_elation_mock.assert_not_called()

    @mock.patch("firefly.modules.appointment.tasks.publish_appointment_to_calendar")
    def test_publish_appointment_to_calendar_async(
        self, publish_appointment_to_calendar_mock, _backend_mock, _acquire_mock
    ):
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            practice_id=self.practice.id,
        )
        publish_appointment_to_calendar_async(appointment_id=appointment.pk)
        publish_appointment_to_calendar_mock.assert_called_once_with(appointment=appointment)

    @mock.patch("firefly.modules.appointment.tasks.remove_appointment_from_calendar")
    def test_remove_appointment_from_calendar_async(
        self, remove_appointment_from_calendar_mock, _backend_mock, _acquire_mock
    ):
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            practice_id=self.practice.id,
        )
        remove_appointment_from_calendar_async(appointment_id=appointment.pk)
        remove_appointment_from_calendar_mock.assert_called_once_with(appointment=appointment)

    @mock.patch("firefly.modules.appointment.tasks.remove_appointment_from_calendar")
    def test_remove_deleted_appointment_from_calendar_async(
        self, remove_appointment_from_calendar_mock, _backend_mock, _acquire_mock
    ):
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            practice_id=self.practice.id,
        )
        appointment.delete()
        remove_appointment_from_calendar_async(appointment_id=appointment.pk)
        remove_appointment_from_calendar_mock.assert_called_once_with(appointment=appointment)

    @mock.patch("firefly.modules.appointment.tasks.apply_appointment_rules")
    def test_apply_appointment_rules_async(self, apply_appointment_rules_mock, _backend_mock, _acquire_mock):
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            practice_id=self.practice.id,
        )
        apply_appointment_rules_async(appointment_id=appointment.pk)
        apply_appointment_rules_mock.assert_called_once_with(appointment=appointment)

    @mock.patch("firefly.modules.appointment.tasks.apply_appointment_rules")
    def test_apply_appointment_rules_async_for_deleted_appt(
        self, apply_appointment_rules_mock, _backend_mock, _acquire_mock
    ):
        appointment = AppointmentFactory(
            patient_id=self.patient.pk,
            practice_id=self.practice.id,
        )
        appointment.delete()
        apply_appointment_rules_async(appointment_id=appointment.pk)
        apply_appointment_rules_mock.assert_called_once_with(appointment=appointment)
