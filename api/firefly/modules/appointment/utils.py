from __future__ import annotations

import json
import logging
import zoneinfo
from datetime import date, datetime, time, timedelta
from typing import TYPE_CHECKING, Dict, List, Optional, Sequence

from dateutil.relativedelta import relativedelta
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Case, DateTimeField, F, OuterRef, Q, Subquery, When
from django.db.models.functions import Greatest, Least
from django.db.models.query import QuerySet
from django.template.loader import render_to_string
from googleapiclient.errors import HttpError
from psycopg2.extras import DateTimeTZRange
from rest_framework import status
from typing_extensions import NotRequired, TypedDict

from firefly.bff.app.authenticated.constants import ONBOARDING_FORMS_UID
from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.feature.utils import is_flag_active_for_user
from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.services.elation.sync.mixins.object_to_elation import sanitize_value
from firefly.core.services.google_calendar.client import GoogleCalendarClient, get_calendar_client
from firefly.core.services.google_calendar.types import CalendarEvent
from firefly.core.services.twilio.tasks import twilio_sms
from firefly.modules.appointment.models import Appointment, Interval
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.physician.models import Physician
from firefly.modules.programs.constants import ProgramEnrollmentEvents
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import (
    get_next_benefit_program_enrollment,
    person_is_in_program,
    program_info_for_person_program,
    update_program_enrollment,
)
from firefly.modules.schedule.constants import (
    APPOINTMENT_RULES,
    SLOT_SIZE,
    WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5,
    AppointmentRules,
)
from firefly.modules.schedule.models import AppointmentType
from firefly.modules.schedule.utils.rule_handler import (
    apply_appointment_capacity,
    create_or_release_slots_for_physician,
    get_appointment_types_for_physician,
)
from firefly.modules.schedule.utils.slot_handler import GetAvailableSlotsResponse
from firefly.modules.work_units.constants import StatusCategory

if TYPE_CHECKING:
    # Avoid import cycles
    from firefly.core.user.models import User

import waffle
from django.conf import settings
from django.contrib.auth.models import Group
from django.utils import timezone
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST, HTTP_409_CONFLICT

from firefly.core.assignment.utils import (
    get_user_from_role_in_care_team,
    get_users_with_internal_role_in_care_team,
)
from firefly.core.services.braze.tasks import reminders
from firefly.core.services.elation.sync.exceptions import ObjectToElationException
from firefly.core.user.constants import MD_ROLE, NP_ROLE, RiskScore
from firefly.modules.appointment.constants import (
    APPT_BUFFER_TIME,
    CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
    ESTABLISHING_CARE_APPOINTMENT_TYPES,
    OLD_MEMBER_DATE,
    VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS,
    WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE,
    WAFFLE_SWITCH_AWV_BOOKING,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    CancelReasonType,
    SlotType,
)
from firefly.modules.appointment.types import AppointmentSlotReleaseConfig
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import FormSubmission
from firefly.modules.tasks.collections import create_tasks_from_task_template
from firefly.modules.tasks.models import Task, TaskCollectionTask

from .models import AppointmentCancelationReason, CancellationReason

logger = logging.getLogger(__name__)
date_format = "%Y-%m-%dT%H:%M:%SZ"


class AppointmentCalendarEvent(TypedDict, total=False):
    start: datetime
    end: datetime
    title: str
    color: str
    url: NotRequired[Optional[str]]  # url is only required for specific event types


def mark_appointments(batch=0, appt_id=None, debug=False, limit=0, days=0):
    """Mark all the appointments as either completed or missed. If the passed in batch size is 0, we will mark all
    appointments as missed or completed. The batch size otherwise corresponds to the the number of years prior to the
    current date. Days corresponds to the number of days previous to mark. For example if days=2 then it referes to
    today to 2 days from now. If days=0 then it will run for all appointments"""
    if not appt_id:
        time_buffer = timedelta(minutes=30)
        appt_queryset = Appointment.objects.filter(
            status=AppointmentStatus.SCHEDULED.value,
            patient__isnull=False,
            physician__provider__isnull=False,
            patient__person__isnull=False,
            physician__provider__user__is_active=True,
        )
        if batch == 0:
            # Run on all appointments prior to now
            batch_start_time = timezone.now() - time_buffer
            appt_queryset = appt_queryset.filter(start__lte=batch_start_time)
        else:
            batch_start_time = timezone.now() - time_buffer - timedelta(weeks=(batch - 1) * 52)
            batch_end_time = timezone.now() - time_buffer - timedelta(weeks=(batch) * 52)
            appt_queryset = appt_queryset.filter(start__lte=batch_start_time, start__gte=batch_end_time)
        if days > 0:
            days_start_time = timezone.now() - timedelta(days=1)
            appt_queryset = appt_queryset.filter(start__gte=days_start_time)
        logger.info(
            "Mark Appointments: Found %s appointments in queryset.",
            appt_queryset.count(),
        )

        if limit:
            logger.info("Applying limit %s.", limit)
            appt_queryset = Appointment.objects.filter(id__in=appt_queryset[:limit])
        for appt in appt_queryset:
            if appt.patient_joined_video:
                # Don't close the appointments that are in progress.
                if (appt.start + appt.duration + timedelta(minutes=APPT_BUFFER_TIME)) <= timezone.now():
                    if not debug:
                        _mark_completed_appointment(appt)
                    else:
                        logger.info("Will mark the appointment %d as complete", appt.pk)
            else:
                if not debug:
                    _mark_missed_appointment(appt)
                else:
                    logger.info("Will mark the appointment %d as missed", appt.pk)
    else:
        appt = Appointment.all_objects.get(id=appt_id)
        logger.info("Mark Appointments: Found appointment id %d in queryset.", appt.pk)
        if not debug:
            if appt.patient_joined_video:
                if (appt.start + appt.duration + timedelta(minutes=APPT_BUFFER_TIME)) <= timezone.now():
                    _mark_completed_appointment(appt)
            else:
                _mark_missed_appointment(appt)


def _mark_missed_appointment(appt):
    try:
        appt.status = AppointmentStatus.NOT_SEEN.value
        appt.save()
        logger.info("Mark Missed Appointments: Marking appointment %d missed", appt.pk)
        create_case_for_missed_appointment(appt)
    except ObjectToElationException:
        logger.warning(
            "Mark Missed Appointments: Could not find appointment %d in Elation, likely deleted in Elation...",
            appt.pk,
        )
    except Exception:
        logger.exception(
            "Mark Missed Appointments: Could not mark appointment %d as missed",
            appt.pk,
        )


# create appointment no show cases for missed appointment
def create_case_for_missed_appointment(appt):
    from firefly.modules.cases.constants import CaseCategoryTitle
    from firefly.modules.cases.models import CaseCategory
    from firefly.modules.cases.utils import create_case_if_not_exist

    category = CaseCategory.objects.get(title=CaseCategoryTitle.APPOINTMENT_NO_SHOW)
    case = create_case_if_not_exist(category_id=category.id, person_id=appt.patient.person.id)
    if case is not None:
        logger.info("created case %s for no show appointment %s", case.id, appt.pk)


def create_case_for_non_english_speaker(appointment: Appointment) -> None:
    """
    Creates a case if appointment is scheduled for member who have preferred language other than English
    """
    from django.contrib.contenttypes.models import ContentType

    from firefly.modules.cases.models import Case, CaseCategory, CaseRelation

    person = appointment.patient.person if (appointment.patient and hasattr(appointment.patient, "person")) else None
    if person and person.has_non_english_preferred_language:
        assert person.preferred_language is not None
        category = CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)
        selected_language = (
            person.preferred_language_other if person.preferred_language_other else person.preferred_language.name
        )
        description = f"Preferred language is {selected_language}"

        case_exists = Case.objects.filter(
            person=person,
            category=category,
            relations__content_type=ContentType.objects.get_for_model(Appointment),
            relations__object_id=appointment.pk,
        ).exists()
        if not case_exists:
            case = Case.objects.create(person=person, category=category, description=description)
            CaseRelation.objects.create(
                case=case,
                content_type=ContentType.objects.get_for_model(Appointment),
                object_id=appointment.pk,
            )
            logger.info(
                "Created case %s for Non-English speaker's scheduled appointment %s",
                case.id,
                appointment.pk,
            )


def auto_close_case_for_non_english_speaker(appointment: Appointment) -> None:
    """
    auto close case if appointment is cancelled
    """
    from firefly.modules.cases.models import CaseCategory
    from firefly.modules.cases.utils import auto_close_case

    person = appointment.patient.person if (appointment.patient and hasattr(appointment.patient, "person")) else None
    if person:
        category = CaseCategory.objects.get(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)

        auto_close_case(
            person=person,
            category=category,
            relations__content_type=ContentType.objects.get_for_model(Appointment),
            relations__object_id=appointment.pk,
        )


def _mark_completed_appointment(appt):
    try:
        appt.status = AppointmentStatus.CHECKED_OUT.value
        appt.save()
        logger.info("Mark Completed Appointments: Marking appointment %d completed", appt.pk)
        # update primary care program status to Established
        update_program_enrollment(
            program_uid=ProgramCodes.PRIMARY_CARE,
            person=appt.patient.person,
            program_event=ProgramEnrollmentEvents.APPOINTMENT_CHECKED_OUT,
            event_date=appt.start,
        )
    except Exception:
        logger.warning(
            "Mark Missed Appointments: Could not complete appointment %d",
            appt.pk,
        )


def auto_cancel_appointments_with_incomplete_health_assessment(appt_id=None, debug=False):
    """Mark appointments as cancelled, when the Health Assessment task assigned to the patient is incomplete after
    the due_date."""
    if not appt_id:
        appt_queryset = Appointment.objects.filter(
            status=AppointmentStatus.SCHEDULED.value,
            reason__in=[AppointmentReason.VIDEO_NEW_PATIENT],
            patient__isnull=False,
            patient_joined_video=False,
            physician__provider__isnull=False,
            start__lte=timezone.now() + timedelta(hours=24),
            start__gte=timezone.now(),
        )
        logger.info(
            "Cancel Appointments: Found %s appointments in queryset.",
            appt_queryset.count(),
        )
        for appt in appt_queryset:
            try:
                if (
                    not patient_has_established_care(appt.patient)
                    and FormSubmission.objects.filter(
                        user=appt.patient,
                        form__uid__in=[FormUID.HEALTH_ASSESSMENT_V2, FormUID.HEALTH_ASSESSMENT_V3],
                        completed_at__isnull=True,
                        expired_at__isnull=True,
                    ).exists()
                    and not debug
                ):
                    _cancel_appointment(appt)
                    continue
            except Task.DoesNotExist:
                logger.info("Task does not exist for the patient")
            except Exception:
                logger.info("Unable to cancel appointment %s", appt.pk)
                continue

    else:
        appt = Appointment.all_objects.get(id=appt_id)
        logger.info("Cancel Appointments: Found appointment id %d in queryset.", appt.pk)
        if not debug:
            _cancel_appointment(appt)


def _cancel_appointment(appt):
    try:
        personId = appt.patient.person.id
        firstName = appt.patient.person.first_name
        startTime = _get_appointment_start_time(appt)
        apptDate = appt.start_user_local.strftime("%A, %B %-d")
        provider = appt.physician.name_with_credentials
        appt.cancel(
            canceled_by_system=True,
            cancelation_reason=AppointmentCancelationReason.INCOMPLETE_HEALTH_ASSESSMENT,
        )
        reminders.send(
            settings.BRAZE["HA_APPT_CANCEL"],
            [
                {
                    "external_user_id": personId,
                    "send_to_existing_only": True,
                    "trigger_properties": {
                        "first_name": firstName,
                        "appointment_time": startTime,
                        "date": apptDate,
                        "provider": provider,
                    },
                }
            ],
        )

        logger.info("Cancel Appointments: Cancelling appointment %d", appt.pk)
    except ObjectToElationException:
        logger.warning(
            "Cancel Appointments: Could not find appointment %d in Elation, likely deleted in Elation...",
            appt.pk,
        )


def _get_appointment_start_time(appointment):
    try:
        start_time = appointment.start_user_local.strftime("%-I:%M %p %Z")
    except AttributeError:
        start_time = f"{appointment.start.strftime('%-I:%M %p %Z')} EST"
    return start_time


def _get_appointment_start_date_time(appointment):
    try:
        start_time = appointment.start_user_local.strftime("%m/%d/%Y %-I:%M %p %Z")
    except AttributeError:
        start_time = f"{appointment.start.strftime('%m/%d/%Y  %-I:%M %p %Z')} EST"
    return start_time


def get_next_start_time(user):
    """
    Prevent appointments within next 1 hours from being surfaced to patients not yet in the
    "member" onboarding state.
    """
    tzone = timezone.get_default_timezone()
    if user.is_patient:
        # Check if account was created between the hours of 3PM and 8AM
        today = timezone.now().date()
        tomorrow = today + timedelta(days=1)
        midnight = datetime.combine(today, time(hour=0), tzinfo=tzone)
        today_pm_cutoff = datetime.combine(today, settings.NEW_APPOINTMENT_CUTOFF_TIME["PM"], tzinfo=tzone)
        today_am_restart = datetime.combine(today, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone)

        # Account was created today between 3:01PM and 11:59PM
        if user.created_at > today_pm_cutoff:
            # Hide appointments before tomorrow at 11AM
            return datetime.combine(tomorrow, settings.NEW_APPOINTMENT_RESTART_TIME, tzinfo=tzone)
        # Account was created today between 12:00AM and 7:59AM
        elif midnight < user.created_at < today_am_restart:
            # Hide appointments before today at 11AM
            return datetime.combine(today, settings.NEW_APPOINTMENT_RESTART_TIME, tzinfo=tzone)
        else:
            return user.created_at + settings.NEW_APPOINTMENT_BUFFER
    return timezone.now()


def get_next_start_time_by_coverage_start_date(person, from_time: datetime):
    """
    calculate start time of the appointment based on eligibility start date of the user.
    if there is no eligibility start date, use user created date.
    """
    if person_is_in_program(person=person, program_uid=ProgramCodes.BENEFIT):
        user: User = person.user
        user_created_at = user.created_at
        benefit_enrollment = get_next_benefit_program_enrollment(person)
        if not benefit_enrollment:
            return user_created_at + settings.NEXT_AVAILABLE_SLOT_PERIOD_FOR_PLAN_ELECT_PERIOD
        if benefit_enrollment.period.lower <= from_time:
            return from_time
        else:
            return benefit_enrollment.period.lower
    return from_time


def get_next_start_time_by_risk_score(person):
    """
    Hides all the appointments that falls under the configured time for a risk score.
    It returns the datetime from when the appointments should be avaialble for booking.
    If a patient doesn't have a risk score then it is considered to be LOW. If the patient
    is not PPO, let's set the score to the max of the config score and 48 hours.
    """
    risk_score = person.risk_score or RiskScore.LOW
    new_start_date_delta = settings.APPOINTMENT_START_TIME_BY_RISK_SCORE.get(risk_score)

    if new_start_date_delta:
        if (
            person.insurance_info
            and person.insurance_info.plan_type
            and person.insurance_info.plan_type.lower() == "ppo"
        ):
            return timezone.now() + new_start_date_delta
        else:
            return max(
                timezone.now() + new_start_date_delta,
                timezone.now() + timedelta(days=2),
            )
    return timezone.now()


def assign_post_visit_survey(user, uid):
    try:
        logger.info("Assigning %s form to user %d", uid, user.id)
        # Get task template
        task_template = TaskCollectionTask.objects.get(uid=uid)
        # Delete existing tasks and incomplete form submissions
        form_submissions = FormSubmission.objects.filter(
            user=user,
            form=task_template.form,
            completed_at__isnull=True,
        )
        form_submissions.delete()
        tasks = Task.objects.filter(patient=user, autocreated_from=task_template, is_complete=False)
        tasks.delete()
        # Assign new form
        create_tasks_from_task_template(task_template, user)
    except Exception:
        logger.exception("Failed to assign %s form for user %d", uid, user.id)


# Patient has at least one completed appointment
def patient_has_established_care(patient, start_dt=None, end_dt=None):
    appointment = Appointment.objects.filter(patient=patient, status=AppointmentStatus.CHECKED_OUT.value)
    if start_dt:
        appointment = appointment.filter(start__gte=start_dt)
    if end_dt:
        appointment = appointment.filter(start__lt=end_dt)
    if appointment:
        return True
    return False


# return patient established date
def get_patient_established_date(patient, start_dt=None, end_dt=None):
    appointments = Appointment.objects.filter(patient=patient, status=AppointmentStatus.CHECKED_OUT.value).order_by(
        "start"
    )
    if start_dt:
        appointments = appointments.filter(start__gte=start_dt)
    if end_dt:
        appointments = appointments.filter(start__lt=end_dt)
    if appointments:
        return appointments[0].start
    return None


def get_appointment_slot_release_configs() -> List[AppointmentSlotReleaseConfig]:
    return settings.APPOINTMENT_SLOT_RELEASE_CONFIGS


def has_user_attended_appointment_type(user: "User", appt_types: Sequence[str]):
    has_user_attended_appointment_type = (
        user.is_patient
        and Appointment.objects.filter(
            patient=user,
            patient_joined_video=True,
            status=AppointmentStatus.CHECKED_OUT.value,
            reason__in=appt_types,
        ).exists()
    )
    if not has_user_attended_appointment_type:
        past_appointments = list(
            Appointment.objects.filter(
                patient=user,
            ).values()
        )

        logger.info(
            "[PAST_APPOINTMENT_DATA]User: %s \n User is a patient: %s \n Appointment count for user: %s \
\n Appointments patient joined: %s \n Appointments checked out: %s \n Appointments of required type: %s",
            user.id,
            user.is_patient,
            len(past_appointments),
            len([appt for appt in past_appointments if appt["patient_joined_video"]]),
            len([appt for appt in past_appointments if appt["status"] == AppointmentStatus.CHECKED_OUT.value]),
            len([appt for appt in past_appointments if appt["reason"] in appt_types]),
        )
    return has_user_attended_appointment_type


def get_can_book_appointments(user: "User", request_user: Optional["User"] = None) -> Dict[str, bool]:
    from firefly.modules.onboarding.statemachine.utils import is_activated_member

    can_book_appointments = {reason: False for reason in ESTABLISHING_CARE_APPOINTMENT_TYPES}

    person = user.person
    onboarding_forms_completed = False
    onboarding_forms = FormSubmission.objects.filter(
        user=user,
        form__uid__in=ONBOARDING_FORMS_UID,
        expired_at__isnull=True,
    )
    # check that any of the onboarding forms is completed before booking appointments.
    # for benefit user, appointment is blocked based on CYD and onboarding status
    onboarding_forms_completed = (
        onboarding_forms.exclude(completed_at__isnull=True).exists() if onboarding_forms.exists() else True
    ) or person_is_in_program(person=person, program_uid=ProgramCodes.BENEFIT)
    primary_care_program_info = program_info_for_person_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
    # Request from app doesn't send request_user as none,
    # in that case verify if flag is enabled for user directly
    if request_user is None:
        request_user = user

    # We will only block appointments on new users that have not filled in their health assessment or
    # onboarding assessment. They must have also chosen a care team.
    if (
        hasattr(user, "onboarding_state")
        and user.onboarding_state is not None
        and user.onboarding_state.initialized_at is not None
        and user.onboarding_state.initialized_at < OLD_MEMBER_DATE
        or (
            # appointment is blocked based on CYD and onboarding status
            (
                get_user_from_role_in_care_team(Group.objects.get(name=NP_ROLE), user.person)
                or get_user_from_role_in_care_team(Group.objects.get(name=MD_ROLE), user.person)
            )
            and is_activated_member(user.person)
            and onboarding_forms_completed
        )
    ):
        # Let's check which appointment types they are allowed to book
        # Check if patient is established
        if has_user_attended_appointment_type(user, ESTABLISHING_CARE_APPOINTMENT_TYPES):
            can_book_appointments[AppointmentReason.VIDEO] = True
            # If patient has BH in their care team, let's allow them to self book
            # OR If request user is a provider, let's allow them to book from lucian
            if (request_user and request_user.is_provider) or (
                len(get_users_with_internal_role_in_care_team([ROLE_VALUES.BH], user.person))
            ):
                can_book_appointments[AppointmentReason.BEHAVIORAL_HEALTH] = True
            # All established patients will be able to book health guide visits
            can_book_appointments[AppointmentReason.HEALTH_GUIDE_BOOKABLE] = True
            # If patient needs an Annual wellness visit this year, let's allow them to self book
            if (
                waffle.switch_is_active(WAFFLE_SWITCH_AWV_BOOKING)
                and request_user
                and not is_flag_active_for_user(flag_name=WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, user=request_user)
                and is_awv_required(user)
            ):
                can_book_appointments[AppointmentReason.AWV_BOOKABLE] = True
            # For patient enable Est AWV before 1 month from the time they are overdue for an annual wellness visit
            # Or if the request user is a provider, always allow them to book an AWV for patient
            if (
                request_user
                and is_flag_active_for_user(flag_name=WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, user=request_user)
                and (
                    (request_user.is_provider)
                    or not does_awv_exist_from_the_date(
                        primary_care_program_info, (timezone.now() - relativedelta(months=11)).date()
                    )
                )
            ):
                can_book_appointments[AppointmentReason.AWV_ESTABLISHED] = True

        # Otherwise only book VIDEO-NEW or AWV New based on last annual visit date
        else:
            reason = get_visit_type_to_be_enabled_for_new_patient(request_user, person)
            can_book_appointments[reason] = True

    return can_book_appointments


def get_visit_type_to_be_enabled_for_new_patient(
    request_user,
    person,
):
    primary_care_program_info = program_info_for_person_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
    # Enable AWV New for patients who require an AWV this year, else enable Video-New
    if (
        request_user
        and is_flag_active_for_user(flag_name=WAFFLE_FLAG_ENABLE_AWV_VISIT_TYPE, user=request_user)
        and not does_awv_exist_from_the_date(primary_care_program_info, (timezone.now() - timedelta(days=365)).date())
    ):
        return AppointmentReason.AWV_NEW
    return AppointmentReason.VIDEO_NEW_PATIENT


def does_awv_exist_from_the_date(primary_care_program_info, date):
    return (
        primary_care_program_info
        and primary_care_program_info.date_of_last_awv
        and primary_care_program_info.date_of_last_awv >= date
    )


def is_awv_required(user: "User") -> bool:
    from firefly.modules.cases.models import Case, CaseCategory

    awv_case_category = CaseCategory.objects.get(unique_key="annual_wellness_visit")
    annual_wellness_visit_case = Case.objects.filter(
        category=awv_case_category,
        person=user.person,
        status_category=StatusCategory.IN_PROGRESS,
    )
    if annual_wellness_visit_case.exists():
        return True
    return False


def get_awv_date_for_new_patient(user: "User", date_of_last_external_awv: Optional[date]):
    date_of_last_awv: Optional[date] = date_of_last_external_awv
    if (
        user.person.insurance_info
        and user.person.insurance_info.coverage_start
        and date_of_last_external_awv
        and user.person.insurance_info.coverage_start > date_of_last_external_awv
    ):
        date_of_last_awv = None
    return date_of_last_awv


def update_awv_date_for_new_patient(user: "User", date_of_last_external_awv: Optional[date]):
    date_of_last_awv: Optional[date] = get_awv_date_for_new_patient(user, date_of_last_external_awv)
    primary_care_program_info = program_info_for_person_program(
        person=user.person, program_uid=ProgramCodes.PRIMARY_CARE
    )
    primary_care_program_info.date_of_last_awv = date_of_last_awv
    primary_care_program_info.date_of_last_external_awv = date_of_last_external_awv
    primary_care_program_info.save(update_fields=["date_of_last_awv", "date_of_last_external_awv"])


def get_action_performed_by(appointment: Appointment, canceled_by_patient: bool = False) -> str:
    if appointment.canceled_by_system:
        return "system"
    elif (
        appointment.patient_id and appointment.updated_by and appointment.updated_by.id == appointment.patient_id
    ) or canceled_by_patient:
        return "patient"
    return "provider"


def get_start_time_based_on_coverage_start_date(user, from_time):
    """
    start date of an appointment should be based on coverage start date for benefit users.
    if the benefit user is not linked to eligibility record, then start date is same as PCP users
    TODO: use case: benefit users without eligibility record is still in discussion
    and from date will be update in future - reference
    https://firefly-health.slack.com/archives/C05GU4MDLKW/p1698221070354279
    """

    if user.is_patient:
        next_from_date = get_next_start_time_by_coverage_start_date(user.person, from_time)
        if next_from_date:
            from_time = max(next_from_date, from_time)
    return from_time


def publish_appointment_to_calendar(appointment: Appointment):
    calendar_client: GoogleCalendarClient = get_calendar_client()
    log_prefix: str = f"publish_appointment_to_calendar: Appointment {appointment.pk}:"
    if not (
        appointment.physician
        and appointment.physician.calendar_url
        and appointment.start
        and appointment.duration
        and appointment.reason
    ):
        logger.info("%s: Missing mandatory attributes", log_prefix)
        return
    if appointment.status == AppointmentStatus.CANCELLED.value:
        logger.info("%s: Skipping cancelled appointment", log_prefix)
        return
    event: CalendarEvent = calendar_client.add_event_to_calendar(
        calendar_id=str(appointment.physician.calendar_url),
        end_time=appointment.start + appointment.duration,
        start_time=appointment.start,
        timezone="UTC",
        event_summary=str(appointment.reason),
    )
    logger.info("%s: Published to calendar. Event details: %s", log_prefix, event)
    if event:
        AliasMapping.set_mapping_by_object(
            obj=appointment,
            alias_id=event["id"],
            alias_name=AliasName.GOOGLE,
        )
    logger.info("%s: Complete", log_prefix)


def remove_appointment_from_calendar(appointment: Appointment):
    calendar_client: GoogleCalendarClient = get_calendar_client()
    log_prefix: str = f"remove_appointment_from_calendar: Appointment {appointment.pk}:"
    if not (appointment.physician and appointment.physician.calendar_url):
        logger.info("%s: Missing calendar configuration", log_prefix)
        return
    # For an appointment to be cancelled
    # for appointments: either the status should be cancelled
    # for appointment slots: the patient should be set to None
    # see appointment/models.py::Appointment.cancel()
    if appointment.deleted is None and (
        (appointment.time_slot_type == SlotType.APPOINTMENT_SLOT and appointment.patient is not None)
        or (
            appointment.time_slot_type == SlotType.APPOINTMENT
            and appointment.status != AppointmentStatus.CANCELLED.value
        )
    ):
        logger.info("%s: Skipping not cancelled appointment. Status: %s", log_prefix, appointment.status)
        return
    appointment_google_calendar_alias_queryset: QuerySet[AliasMapping] = AliasMapping.objects.filter(
        content_type=ContentType.objects.get_for_model(Appointment),
        object_id=appointment.pk,
        alias_name=AliasName.GOOGLE,
    )
    if not appointment_google_calendar_alias_queryset.exists():
        logger.info("%s: No event found. Skipping", log_prefix)
        return
    for appointment_google_calendar_alias in appointment_google_calendar_alias_queryset.iterator():
        try:
            removal_response: str = calendar_client.remove_event_from_calendar(
                calendar_id=str(appointment.physician.calendar_url),
                event_id=appointment_google_calendar_alias.alias_id,
            )
            logger.info("%s: Removed event from calendar. Response: %s", log_prefix, str(removal_response))
            appointment_google_calendar_alias.delete()
            logger.info("%s: Cleared alias", log_prefix)
        except HttpError as http_error:
            # https://developers.google.com/calendar/api/guides/errors#410_gone
            if http_error.status_code == status.HTTP_410_GONE:
                # event has already been deleted
                appointment_google_calendar_alias.delete()
                logger.info("%s: Clearing alias since entry has already been deleted", log_prefix)
            else:
                raise http_error
    logger.info("%s: Complete", log_prefix)


def apply_appointment_rules(appointment: Appointment):
    """
    Scheduling side-effects e.g. after an Appointment is booked, cancelled, or deleted
    """

    log_prefix: str = f"apply_appointment_rules: appointment: {appointment.pk} start: {appointment.start}"
    logger.info("%s: Started", log_prefix)
    if appointment.physician and appointment.physician.provider and appointment.physician.provider.user:
        # Book appointment should delete other unbooked slots for same time
        if appointment.patient is not None and appointment.status != AppointmentStatus.CANCELLED.value:
            deleted_appointment_ids: List[int] = []
            del_appointments: QuerySet[Appointment] = get_overlapping_appointments(
                appointment=appointment, appointment_objects=Appointment.objects.all(), log_prefix=log_prefix
            ).filter(
                patient=None,
                time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
            )

            logger.info(
                "%s: Book appt flow should delete slots for same time. To be deleted count : %d",
                log_prefix,
                del_appointments.count(),
            )
            for del_appointment in del_appointments.iterator():
                deleted_appointment_ids.append(del_appointment.id)
                logger.info(
                    "%s: will delete appointment slot: %d reason: %s starting at: %s",
                    log_prefix,
                    del_appointment.id,
                    del_appointment.reason,
                    del_appointment.start,
                )
                # Do not attempt a bulk update here - signals will be skipped
                del_appointment.delete()
            # Maintain an event log, to know the count of affected longer visits due to focused visit booking
            if appointment.reason == AppointmentReason.FOCUSED_VISIT and len(deleted_appointment_ids) > 0:
                EventLog.objects.create(
                    target=appointment,
                    type=EventTypeCodes.FOCUSED_APPOINTMENT_SCHEDULED,
                    user=appointment.patient,
                    metadata={
                        "current_appointment": {
                            "patient_id": appointment.patient_id,
                            "updated_by_id": appointment.updated_by.id if appointment.updated_by else None,
                        },
                        "overlapping_appointments_deleted": deleted_appointment_ids,
                    },
                )
        # Cancel appointment should release other unbooked slots for same time
        else:
            logger.info("%s: Cancel appt flow should undelete slots for same time", log_prefix)
            all_appointment_types_data, _ = get_appointment_types_for_physician(
                appointment.physician, appointment.start
            )
            appt_end_time = (
                appointment.start
                + timedelta(minutes=all_appointment_types_data[appointment.reason]["duration"])
                + timedelta(minutes=all_appointment_types_data[appointment.reason]["buffer"])
            )
            for appt_type_reason in all_appointment_types_data.keys():
                logger.info(
                    "%s: physician: %d, %s: will release slots", log_prefix, appointment.physician.pk, appt_type_reason
                )
                create_or_release_slots_for_physician(
                    appointment.physician,
                    appt_type_reason,
                    all_appointment_types_data,
                    appointment.start,
                    appt_end_time,
                    log_prefix=f"{log_prefix} physician: {appointment.physician.pk}",
                    dry_run_off=True,
                )
        apply_rules(log_prefix=log_prefix, appointment=appointment)
    logger.info("%s: Completed", log_prefix)


def apply_rules(appointment: Appointment, log_prefix: str):
    rules: List[str] = [k for k, v in APPOINTMENT_RULES.items()]
    for rule in rules:
        logger.info("%s: Applying appointment rule %s for appointment: %d", log_prefix, rule, appointment.pk)
        if APPOINTMENT_RULES[rule] and rule == AppointmentRules.EST_NEW_APPOINTMENT_CAPACITY:
            apply_appointment_capacity(log_prefix=log_prefix, appointment=appointment)


def should_release_slot(appointment: Appointment, log_prefix: Optional[str] = None) -> bool:
    from firefly.modules.schedule.models import PhysicianVisitMixRatio

    # If the provider's visit mix for this type is exactly 0%
    # we don't want the provider to ever have this type of visit so return False
    unique_key = (
        VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS[appointment.reason]
        if appointment.reason in VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS
        else appointment.reason
    )
    try:
        visit_mix = PhysicianVisitMixRatio.objects.get(
            day_of_week=appointment.start.isoweekday(),
            physician_appointment_type__physician=appointment.physician,
            # We use the mapping for appointment reason to the type of visit mix ratio we should be using
            physician_appointment_type__appointment_type__unique_key=unique_key,
        )
    except PhysicianVisitMixRatio.DoesNotExist:
        # If there is currently no configuration for this visit type, definitely don't release it now
        logger.info(
            (
                "%s: For Appointment %s: starting at: %s reason: %s should_release_slot is False"
                " because no Physician Visit Mix Ratio exists"
            ),
            log_prefix,
            appointment,
            appointment.start,
            appointment.reason,
        )
        return False
    if visit_mix.percentage_of_slots == 0:
        logger.info(
            (
                "%s: For Appointment %s: starting at: %s reason: %s should_release_slot is False"
                " because percentage_of_slots == 0"
            ),
            log_prefix,
            appointment,
            appointment.start,
            appointment.reason,
        )
        return False
    # If existing appointment present for same time
    # or existing slots present with same reason for same time
    # return False
    overlapping_appointments: QuerySet[Appointment]
    if appointment.physician:
        overlapping_appointments = get_overlapping_appointments(
            appointment=appointment, appointment_objects=Appointment.objects.all(), log_prefix=log_prefix
        ).filter(
            ((Q(patient__isnull=False) | Q(reason=appointment.reason)) & ~Q(status=AppointmentStatus.CANCELLED.value)),
            time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
        )
    if overlapping_appointments.exists():
        logger.info(
            (
                "%s: For Appointment %s: starting at: %s reason: %s should_release_slot is False"
                " because of overlap with existing Appointment"
            ),
            log_prefix,
            appointment,
            appointment.start,
            appointment.reason,
        )
        return False
    if not does_time_slot_exist_for_appointment(appointment):
        logger.info(
            (
                "%s: For Appointment %s: starting at: %s reason: %s should_release_slot is False"
                " because no Time Slot exists"
            ),
            log_prefix,
            appointment,
            appointment.start,
            appointment.reason,
        )
        return False
    return True


def does_time_slot_exist_for_times(physician: Physician, start_time: datetime, end_time: datetime) -> bool:
    from firefly.modules.schedule.models import TimeSlot

    assert physician.provider is not None
    existing_slots_count = TimeSlot.objects.filter(
        shift__schedule__provider=physician.provider,
        period__overlap=DateTimeTZRange(start_time, end_time),
    ).count()
    return existing_slots_count >= int(((end_time - start_time).total_seconds() / 60) / SLOT_SIZE)


def does_time_slot_exist_for_appointment(appointment: Appointment) -> bool:
    from firefly.modules.schedule.models import AppointmentType

    appointment_type = AppointmentType.objects.get(unique_key=appointment.reason)
    if appointment.physician and appointment.physician.provider:
        return does_time_slot_exist_for_times(
            appointment.physician,
            appointment.start,
            appointment.start + timedelta(minutes=appointment_type.duration),
        )
    return False


def notify_clinician_for_last_minute_cancellation(appointment: Appointment, patient: "User", log_prefix: str):
    log_prefix = f"{log_prefix}: notify_clinician_for_last_minute_cancellation"
    logger.info("%s: started", log_prefix)
    time_until_start = appointment.start - timezone.now()
    needs_last_minute_cancellation_notification = time_until_start < timedelta(
        hours=3
    ) and time_until_start > timedelta(minutes=0)

    if not needs_last_minute_cancellation_notification:
        return

    if not hasattr(appointment.physician, "provider"):
        return

    if appointment.physician:
        provider_detail = appointment.physician.provider
        phone_number = provider_detail.user.phone_number
        assert phone_number is not None

        message = render_to_string(
            "last_minute_appointment_cancellation.txt",
            {
                "provider_user": provider_detail.user,
                "patient": patient,
                "appointment_start": appointment.start.astimezone(NY_TIMEZONE).strftime("%-I:%M %p"),
            },
        )
        twilio_sms.send(phone_number, message, "LAST_MIN_APPOINTMENT_CANCELLATION")
        logging.info(
            "%s:Provider notified of last minute appointment cancellation with the following message: %s",
            log_prefix,
            message,
        )
    logger.info("%s: completed", log_prefix)


class Reason(TypedDict):
    label: str
    children: List[str]


def combine_reason_values(reason_dict: Dict[str, Dict[str, Reason]], reason_type) -> str:
    reason = f"{reason_type}: "
    reason += ", ".join(
        [
            f"""{r["label"]}({", ".join(r["children"])})""" if len(r["children"]) else r["label"]
            for r in reason_dict[reason_type].values()
        ]
    )
    return reason


def format_cancellation_reason(
    cancelation_reason: str,
    cancellation_reason_ids: List[int],
    canceled_by_system: bool,
    patient_other_reason: str,
    firefly_other_reason: str,
) -> str:
    if canceled_by_system:
        return f"{CancelReasonType.SYSTEM}: {cancelation_reason}"
    cancellation_reasons = (
        CancellationReason.objects.filter(id__in=cancellation_reason_ids)
        .order_by("-parent")
        .values("uid", "label", "is_patient_facing", "parent__uid")
    )
    reason = ""
    reason_dict: Dict[str, Dict[str, Reason]] = {}
    for r in cancellation_reasons:
        reason_type = CancelReasonType.FIREFLY
        if r["is_patient_facing"]:
            reason_type = CancelReasonType.PATIENT
        if r["parent__uid"] is None:
            if reason_type in reason_dict:
                reason_dict[reason_type][r["uid"]] = {"label": r["label"], "children": []}
            else:
                reason_dict[reason_type] = {r["uid"]: {"label": r["label"], "children": []}}
            if r["uid"] == "other":
                reason_dict[reason_type][r["uid"]]["children"].append(patient_other_reason)
            if r["uid"] == "other_firefly":
                reason_dict[reason_type][r["uid"]]["children"].append(firefly_other_reason)
        else:
            if r["parent__uid"] in reason_dict[reason_type]:
                reason_dict[reason_type][r["parent__uid"]]["children"].append(r["label"])
    if CancelReasonType.FIREFLY in reason_dict:
        reason += combine_reason_values(reason_dict, CancelReasonType.FIREFLY)
    if CancelReasonType.PATIENT in reason_dict:
        if reason:
            reason += ", "
        reason += combine_reason_values(reason_dict, CancelReasonType.PATIENT)
    return reason


def get_overlapping_appointments(
    appointment: Appointment, appointment_objects: QuerySet[Appointment], log_prefix: Optional[str]
) -> QuerySet[Appointment]:
    from firefly.modules.schedule.models import AppointmentType

    booked_appointment_type = AppointmentType.objects.get(unique_key=appointment.reason)

    assert appointment.physician is not None
    assert booked_appointment_type.buffer_time_in_minutes is not None
    appointment_end: datetime = appointment.start + appointment.duration
    appointment_end_time: datetime = appointment_end + timedelta(minutes=booked_appointment_type.buffer_time_in_minutes)

    shift_start_time, shift_stop_time = get_shift_of_the_appointment(appointment=appointment, log_prefix=log_prefix)
    if not shift_start_time:
        shift_start_time = appointment.start
    if not shift_stop_time:
        shift_stop_time = appointment_end_time

    return get_overlapping_appointments_for_shift(
        appointment.physician,
        appointment_objects,
        appointment.start,
        max(min(appointment_end_time, shift_stop_time), appointment_end),
        shift_start_time,
        shift_stop_time,
    )


def get_overlapping_appointments_for_shift(
    physician: Physician,
    appointment_objects: QuerySet[Appointment],
    overlap_start_time: datetime,
    overlap_end_time: datetime,
    shift_start_time: datetime,
    shift_stop_time: datetime,
) -> QuerySet[Appointment]:
    from firefly.modules.schedule.models import AppointmentType

    return appointment_objects.annotate(
        buffer_time=Subquery(
            AppointmentType.objects.filter(unique_key=OuterRef("reason")).values("buffer_time_in_minutes")[:1]
        ),
        # The minimum required length of the appointment
        appt_time_end=Case(
            When(
                start__isnull=False,
                duration__isnull=False,
                then=F("start") + F("duration"),
            ),
            default=None,
            output_field=DateTimeField(),
        ),
        # The length of the appointment including buffer
        appt_end=models.ExpressionWrapper(
            F("appt_time_end") + Interval(F("buffer_time")), output_field=DateTimeField()
        ),
        end=Case(
            When(appt_end__isnull=True, then=F("appt_end")),
            When(start__lt=shift_start_time, then=Greatest(Least(F("appt_end"), shift_start_time), F("appt_time_end"))),
            When(start__lt=shift_stop_time, then=Greatest(Least(F("appt_end"), shift_stop_time), F("appt_time_end"))),
            default=F("appt_end"),
            output_field=DateTimeField(),
        ),
    ).filter(
        (
            (Q(start__gte=overlap_start_time) & Q(start__lt=overlap_end_time))
            | (Q(end__gt=overlap_start_time) & Q(end__lte=overlap_end_time))
        ),
        physician=physician,
        end__isnull=False,
    )


def get_shift_of_the_appointment(
    appointment: Appointment, log_prefix: Optional[str] = None
) -> tuple[Optional[datetime], Optional[datetime]]:
    from firefly.modules.schedule.models import DayOfWeek, ProviderSchedule, Shift

    if appointment.physician and appointment.physician.provider:
        # Convert appointment start in UTC to timezone supported by provider,
        # to support comparision with shift timings
        effective_schedule_for_appointment: Optional[ProviderSchedule] = (
            ProviderSchedule.objects.filter(
                effective_period__contains=appointment.start,
                provider__physician_id=int(appointment.physician.id),
            )
            .order_by("-created_at", "-id")
            .first()
        )
        assert effective_schedule_for_appointment is not None
        tz_supported_by_provider = zoneinfo.ZoneInfo(str(effective_schedule_for_appointment.timezone))
        appt_start = appointment.start.astimezone(zoneinfo.ZoneInfo(effective_schedule_for_appointment.timezone))
        appt_end = (appointment.start + appointment.duration).astimezone(
            zoneinfo.ZoneInfo(effective_schedule_for_appointment.timezone)
        )
        appt_start_time = appt_start.strftime("%H:%M:%S")
        appt_end_time = appt_end.strftime("%H:%M:%S")
        day_of_week = appt_start.strftime("%A")
        day_of_week_iso: int = getattr(DayOfWeek, day_of_week.upper()).value
        shift_data: QuerySet[Shift] = Shift.objects.filter(
            schedule=effective_schedule_for_appointment,
            day_of_week=day_of_week_iso,
            start_time__lte=appt_start_time,
            stop_time__gte=appt_end_time,
            effective_period__contains=appointment.start,
        ).order_by("start_time")

        shift_data_count = shift_data.count()

        if shift_data_count:
            if shift_data_count > 1:
                logger.info("%s: Multiple shifts found for appointment %s", log_prefix, appointment.pk)
            shift: Shift = shift_data[0]
            if shift:
                shift_start_time = (
                    datetime(
                        year=appt_start.date().year,
                        month=appt_start.date().month,
                        day=appt_start.date().day,
                        hour=shift.start_time.hour,
                        minute=shift.start_time.minute,
                        second=shift.start_time.second,
                    ).replace(tzinfo=tz_supported_by_provider)
                ).astimezone(UTC_TIMEZONE)

                shift_stop_time = (
                    datetime(
                        year=appt_start.date().year,
                        month=appt_start.date().month,
                        day=appt_start.date().day,
                        hour=shift.stop_time.hour,
                        minute=shift.stop_time.minute,
                        second=shift.stop_time.second,
                    ).replace(tzinfo=tz_supported_by_provider)
                ).astimezone(UTC_TIMEZONE)
            return shift_start_time, shift_stop_time
        logger.info("%s: No shift data found for appointment %s", log_prefix, appointment.pk)
    return None, None


def send_params_to_book_appointment(request, log_prefix, user=None):
    from firefly.core.user.models.models import User
    from firefly.modules.appointment.slots.api import book_an_appointment_slot

    body = json.loads(request.body) if request.body else {}
    extra_params = {}
    description = body.get("description")
    if description is not None:
        extra_params["description"] = sanitize_value(description)
    extra_params["symptom_ids"] = body.get("symptom_ids", [])
    extra_params["other_symptoms"] = body.get("other_symptoms")
    if user is None:
        user = User.objects.get(id=body.get("patient_id"))
    physician_id = body.get("physician_id")
    physician = Physician.objects.filter(id=physician_id).first()
    reason = body.get("reason")
    appointment_type = AppointmentType.objects.filter(unique_key=reason).first()
    isValidSlot: bool = True
    if is_flag_active_for_user(flag_name=WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, user=physician.provider.user):
        # While booking, validate that this slot is available for the given time.
        # There could be a scenario where this slot was available when the user opened the available slots window,
        # but while booking, this slot might no longer be available due to the visit mix ratio.
        isValidSlot = is_valid_slot(
            provider_id=physician.provider.user_id,
            start_time=body.get("scheduled_date"),
            reason=reason,
            log_prefix=log_prefix,
        )
    if isValidSlot:
        existing_appointment_slot, created = Appointment.objects.get_or_create(
            physician=physician,
            reason=reason,
            start=body.get("scheduled_date"),
            source=AppointmentSource.LUCIAN,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            visible=True,
            duration=timedelta(minutes=appointment_type.duration),
        )
        if created:
            logger.info(
                "[AppointmentCreated] No existing  slot available for physician: %s reason: %s scheduled_date: %s",
                body.get("physician_id"),
                body.get("reason"),
                body.get("scheduled_date"),
            )
        appointment_id = existing_appointment_slot.id

        logger.info(
            "%s: [BookAppointmentSlotV2] Booking %d, %s appointment starting at %s, with \
    physician %d for patient %d",
            log_prefix,
            existing_appointment_slot.id,
            body.get("reason"),
            body.get("scheduled_date"),
            body.get("physician_id"),
            user.id,
        )

        appointment, error = book_an_appointment_slot(appointment_id, user, request, extra_params)
        return appointment, error
    else:
        return None, Response(
            {"detail": "Slot not available for the given time"},
            status=HTTP_409_CONFLICT,
        )


def is_valid_slot(provider_id: int, start_time: str, reason: str, log_prefix: str) -> bool:
    """
    Checks if the given start time is within the available time slots for a given provider.

    Parameters:
    provider_id (int): The ID of the provider for whom the slot availability is being checked.
    start_time (str): The desired start time for the slot, provided in ISO 8601 format ("YYYY-MM-DDTHH:MM:SSZ").
    reason (str): The reason for the request, which may be used to filter available slots.
    log_prefix (str): A prefix to be used in logs for tracking purposes.

    Returns:
    bool: True if the given start time is within the provider's available slots, False otherwise.
    """
    from firefly.modules.schedule.utils.slot_handler import get_available_slots

    appointment_start_time = timezone.make_aware(datetime.strptime(start_time, date_format))
    start_date_time = datetime(
        appointment_start_time.year,
        appointment_start_time.month,
        appointment_start_time.day,
        0,
        0,
        0,
        tzinfo=NY_TIMEZONE,
    )
    end_date_time = datetime(
        start_date_time.year, start_date_time.month, start_date_time.day, 23, 59, 0, tzinfo=NY_TIMEZONE
    )
    available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
        provider_ids=[provider_id],
        start_date_time=start_date_time,
        end_date_time=end_date_time,
        reason=reason,
        log_prefix=log_prefix,
    )
    slot_date_times = [
        slot_date.time() for slot_date in available_slots_respponse["provider_slot_map"].get(provider_id, [])
    ]

    return appointment_start_time.time() in slot_date_times


def book_custom_appointment(request, log_prefix):
    from firefly.modules.appointment.tasks import update_custom_appointment_eventlog

    """
    Books a "Custom" appointment for a patient with a specific physician.

    This function takes a request object containing appointment details and a log prefix
    for logging purposes. It parses the request body to extract patient, physician,
    scheduling information, and details related to the "Break the Glass" event.
    It then creates an appointment record in the database and logs the event.

    Args:
        request: An HttpRequest object containing the appointment details in its body (JSON format).
                 The body should include:
                 - "patient_id" (int): The ID of the patient.
                 - "physician_id" (int): The ID of the physician.
                 - "reason" (str): The unique key of the appointment type (used to fetch duration).
                 - "scheduled_date" (str): The desired start date and time for the appointment
                                            in "%Y-%m-%dT%H:%M:%SZ" format (UTC).
                 - "description" (str, optional): A description for the appointment.
                 - "custom_appointment_reasons" (str, optional): The reason for custom appointment.
                 - "symptom_ids" (list of int, optional): A list of symptom IDs.
                 - "other_symptoms" (str, optional): Any other symptoms not covered by IDs.
        log_prefix (str): A string to prefix log messages for easier identification.

    Returns:
        tuple: A tuple containing the created Appointment object and a Response object.
               - If the appointment is successfully created, the first element is the
                 Appointment object and the second is None.
               - If there's an error (e.g., past start time, existing appointment), the
                 first element is None and the second is a Response object with an
                 appropriate error message and HTTP status code.
    """
    from firefly.core.user.models.models import User

    body = json.loads(request.body) if request.body else {}
    extra_params = {}
    description = body.get("description")
    custom_appointment_reasons = body.get("custom_appointment_reasons")
    if description is not None:
        extra_params["description"] = sanitize_value(description)
    extra_params["symptom_ids"] = body.get("symptom_ids", [])
    other_symptoms = body.get("other_symptoms")
    patient = User.objects.get(id=body.get("patient_id"))
    physician_id = body.get("physician_id")
    physician = Physician.objects.filter(id=physician_id).first()
    reason = body.get("reason")
    start = body.get("scheduled_date")

    # validate for appointment start time not in past
    if datetime.strptime(start, "%Y-%m-%dT%H:%M:%SZ") < datetime.now():
        logger.info("%s:Cannot book appointment with a start time in the past. ", log_prefix)
        return None, Response(
            {"detail": "Cannot book appointment with a start time in the past"},
            status=HTTP_400_BAD_REQUEST,
        )
    appointment_type = AppointmentType.objects.filter(unique_key=reason).first()

    # book appointment
    appointment, created = Appointment.objects.get_or_create(
        physician=physician,
        reason=reason,
        start=start,
        source=AppointmentSource.LUCIAN,
        time_slot_type=SlotType.APPOINTMENT,
        visible=True,
        duration=timedelta(minutes=appointment_type.duration),
        status=AppointmentStatus.SCHEDULED.value,
        patient=patient,
        other_symptoms=other_symptoms,
    )

    # validated if appointment already exists
    if not created:
        return None, Response(
            {"detail": "Already appointment exists"},
            status=HTTP_409_CONFLICT,
        )

    # save symptom_ids of appointment
    for key, value in extra_params.items():
        if key == "symptom_ids":
            appointment.symptoms.set(value)
            continue

    # async call to update eventlog with custom_appointment_reasons and custom_appointment=True
    update_custom_appointment_eventlog.send(
        appointment_id=appointment.pk, custom_appointment_reasons=custom_appointment_reasons, log_prefix=log_prefix
    )
    return appointment, None


def is_custom_appointment(appointment: Appointment) -> bool:
    """
    Check if the appointment is a custom appointment.

    Args:
        appointment (Appointment): The appointment object to check.

    Returns:
        bool: True if the appointment is a custom appointment, False otherwise.
    """
    is_custom_appointment = False
    event_log = EventLog.objects.filter(
        target_object_id=appointment.pk,
        type=EventTypeCodes.APPOINTMENT_SCHEDULED,
    ).first()
    if (
        event_log
        and event_log.metadata
        and "current_appointment" in event_log.metadata
        and "custom_appointment" in event_log.metadata["current_appointment"]
    ):
        is_custom_appointment = event_log.metadata["current_appointment"]["custom_appointment"]

    return is_custom_appointment
