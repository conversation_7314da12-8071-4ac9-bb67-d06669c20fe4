import logging
import zoneinfo
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional, Tuple

from django.conf import settings
from django.db import models
from django.db.models import Func, Q, constraints
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_deprecate_fields import deprecate_field
from rest_framework.exceptions import ValidationError

from firefly.core.services.zoom.utils import VIDEO_SDK
from firefly.modules.appointment.constants import (
    EVENT_LOG_ACTION_PERFORMED,
    REASON_CHOICES,
    SLOT_TYPE_CHOICES,
    SOURCE_CHOICES,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.facts.mixin import BaseFact, SortableFact
from firefly.modules.firefly_django.constants import NY_TIMEZONE
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.physician.models import Physician
from firefly.modules.practice.models import Practice

logger = logging.getLogger(__name__)


class Interval(Func):
    function = "INTERVAL"
    template = "(%(expressions)s * %(function)s '1' MINUTE)"


class AppointmentCancelationReason(models.TextChoices):
    INCOMPLETE_HEALTH_ASSESSMENT = "incomplete-health-assessment", _("Incomplete Health Assessment")
    PROVIDER_UNAVAILABLE = "provider-unavailable", _("Provider Unavailable")

    def __eq__(self, other: object) -> bool:
        return str(self) == str(other)


class SymptomCategory(BaseFact):
    """
    Category of Symptoms mapped to appointment duration
    """

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=255)  # noqa: TID251

    class Meta(BaseFact.Meta):
        verbose_name_plural = "Symptom categories"
        constraints = [
            models.UniqueConstraint(fields=["uid"], condition=Q(deleted=None), name="symptom_category_uid_uniq")
        ]


class HomeSymptomCategory(SortableFact):
    """
    Category of Symptoms to be displayed on the home page
    """

    class Meta(SortableFact.Meta):
        verbose_name_plural = "Home symptom categories"


class Symptom(SortableFact):
    """
    List of Symptoms for a visit ordered for display on home page
    """

    category = models.ForeignKey(
        "SymptomCategory",
        related_name="symptoms",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    home_page_category = models.ForeignKey(
        "HomeSymptomCategory",
        related_name="symptoms",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    class Meta(SortableFact.Meta):
        pass


class Appointment(SaveHandlersMixin, BaseModelV3):
    elation_id = models.BigIntegerField(
        unique=True,
        blank=True,
        null=True,
    )

    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="appointments",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    physician = models.ForeignKey(Physician, related_name="appointments", null=True, on_delete=models.CASCADE)

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    practice = models.ForeignKey(Practice, related_name="appointments", null=True, on_delete=models.SET_NULL)  # noqa: TID251

    # Status gets packaged up and synced to Elation
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=255,
        # default='Scheduled',  # cannot set default yet
        null=True,
        blank=True,
        db_index=True,
    )  # need to allow null and blanks for now

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    source = models.CharField(  # noqa: TID251
        max_length=255,
        choices=SOURCE_CHOICES,
        db_index=True,
    )

    # The datetime when the appointment starts
    start = models.DateTimeField(db_index=True)

    # The datetime when a user scheduled the appoinment
    scheduled_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp when reminder SMS sent to provider.
    sms_notice_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp when reminder email/mobile push sent to patient.
    patient_reminder_sent_at = models.DateTimeField(null=True, blank=True)

    # Timestamp for reminder email/push sent to patient 24 hours before start
    patient_24_reminder_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp for reminder email/push sent to patient 30 mins before start
    patient_30_mins_reminder_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp for reminder email/push sent to patient 5 mins before start
    patient_5_mins_reminder_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp for first reminder email/push sent to patient without health assessment complete
    patient_health_1_reminder_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    # Timestamp for second reminder email/push sent to patient without health assessment complete
    patient_health_2_reminder_sent_at = models.DateTimeField(default=None, null=True, blank=True)

    duration = models.DurationField(default=timedelta(minutes=30))

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason = models.CharField(  # noqa: TID251
        max_length=255,
        choices=REASON_CHOICES,
        default=AppointmentReason.VIDEO,
        blank=True,
        db_index=True,
    )

    description = models.TextField(blank=True)

    symptoms = BaseModelV3ManyToManyField(
        Symptom, related_name="appointments", blank=True, through="AppointmentSymptoms"
    )
    other_symptoms = models.TextField(blank=True, null=True, help_text="Comma-separated list of free-text symptoms")

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    time_slot_type = models.CharField(max_length=16, choices=SLOT_TYPE_CHOICES, default="appointment", db_index=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    tokbox_session_id = models.CharField(max_length=255, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    session_sdk = models.CharField(  # noqa: TID251
        choices=[
            (VIDEO_SDK.TOKBOX.value, "TokBox"),
            (VIDEO_SDK.ZOOM.value, "Zoom"),
        ],
        max_length=255,
        blank=True,
        null=True,
    )

    patient_joined_video = models.BooleanField(default=False)

    visible = models.BooleanField(
        null=True,
        blank=True,
        help_text=(
            "If True this slot will be available for booking an appointment."
            f" Only applies for type {AppointmentReason.VIDEO_NEW_PATIENT}"
        ),
    )

    # TODO: should be handled by EventLog
    cancelation_reason = models.TextField(blank=True, null=True)

    canceled_by_system = models.BooleanField(blank=True, null=True)

    @property
    def start_user_local(self):
        """Get start datetime in user timezone."""
        earliest_local_time = None
        if self.patient is not None:
            userdevices = self.patient.userdevices.all()

            # If user has multiple devices
            # Find the one with the earliest time
            for device in userdevices:
                if device.device_timezone is not None:
                    local_time_using_device_tz = self.start.astimezone(zoneinfo.ZoneInfo(device.device_timezone))
                    if (earliest_local_time is None) or (local_time_using_device_tz < earliest_local_time):
                        earliest_local_time = local_time_using_device_tz
        # If a timezone is not found: default to America/New_York
        if earliest_local_time is None:
            earliest_local_time = self.start.astimezone(NY_TIMEZONE)

        return earliest_local_time

    @property
    def needs_last_minute_notification(self):
        if self.scheduled_at is None or self.start is None:
            return False
        if self.sms_notice_sent_at is not None or self.physician is None or self.patient is None:
            return False

        # Is it currently less than 2 hours away from the scheduled start?
        time_until_start = self.start - self.scheduled_at
        return time_until_start < timedelta(hours=2) and time_until_start > timedelta(minutes=0)

    @property
    def missed(self):
        return self.status == AppointmentStatus.NOT_SEEN.value

    # Used to check when `missed` changes
    _pre_save_missed = False
    _save_handlers_mixin__refresh_on_save = True

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._pre_save_missed = self.missed

    def pre_save_mutation(self, changed):
        from firefly.modules.appointment.signals import (
            create_elation_appointment,
            send_reschedule_appointment_push,
        )

        log_prefix: str = f"appointment_pre_save_mutation {str(self.pk)}"
        logger.info("%s: Starting", log_prefix)
        send_reschedule_appointment_push(self)
        create_elation_appointment(self)
        logger.info("%s: Complete", log_prefix)

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.appointment.signals import (
            create_appointment_slots,
            event_appointment_cancelled,
            event_appointment_completed,
            event_appointment_deleted,
            event_appointment_scheduled,
            event_appointment_status_cancelled,
            notify_provider_of_last_minute_appointment,
        )

        log_prefix: str = f"appointment_post_save_side_effect {str(self.pk)}"
        logger.info("%s: Starting", log_prefix)
        if (
            changed(field_name="patient", debug=True, log_prefix=log_prefix)
            or changed(field_name="patient_id", debug=True, log_prefix=log_prefix)
        ) and self.patient is None:
            logger.info("%s: Patient changed to None. Trigerring event_appointment_cancelled", log_prefix)
            old_patient_value = get_old_value_for_changed_field("patient")
            if old_patient_value is not None:
                changed_fields_by_name: Dict = {"patient": [old_patient_value.pk, self.patient]}
                event_appointment_cancelled(self, changed_fields_by_name)
        elif (
            changed(field_name="id", debug=True, log_prefix=log_prefix)
            or changed(field_name="status", debug=True, log_prefix=log_prefix)
            or changed(field_name="patient", debug=True, log_prefix=log_prefix)
            or changed(field_name="patient_id", debug=True, log_prefix=log_prefix)
        ):
            changed_fields_by_name: Dict = {}
            logger.info("%s: Id/status/patient has changed. Trigerring event_appointment_scheduled", log_prefix)
            if changed("id"):
                changed_fields_by_name["id"] = [get_old_value_for_changed_field("id"), self.pk]
            if changed("status"):
                changed_fields_by_name["status"] = [get_old_value_for_changed_field("status"), self.status]
            if changed("patient"):
                old_value = (
                    get_old_value_for_changed_field("patient").id
                    if get_old_value_for_changed_field("patient") is not None
                    else get_old_value_for_changed_field("patient")
                )
                changed_fields_by_name["patient"] = [old_value, self.patient_id]
            if changed("patient_id"):
                changed_fields_by_name["patient"] = [get_old_value_for_changed_field("patient_id"), self.patient_id]
            event_appointment_scheduled(self, changed_fields_by_name)
        elif changed("deleted") and self.deleted is not None:
            logger.info("%s: Appointment got deleted. Trigerring event_appointment_deleted", log_prefix)
            event_appointment_deleted(self)
        if changed(field_name="status", debug=True, log_prefix=log_prefix):
            if self.physician and self.patient and self.status == AppointmentStatus.COMPLETED.value:
                logger.info("%s: status has changed to completed. Trigerring event_appointment_completed", log_prefix)
                changed_fields_by_name["status"] = [get_old_value_for_changed_field("status"), self.status]
                event_appointment_completed(self, changed_fields_by_name)
            elif self.time_slot_type == SlotType.APPOINTMENT and self.status == AppointmentStatus.CANCELLED.value:
                logger.info(
                    "%s: Cancelled Appoitment slot type appointment. Trigerring event_appointment_status_cancelled",
                    log_prefix,
                )
                event_appointment_status_cancelled(self)
        if self.needs_last_minute_notification:
            notify_provider_of_last_minute_appointment(self)
        create_appointment_slots(self)
        logger.info("%s: Complete", log_prefix)

    def save(self, *args, **kwargs):
        if self.deleted is None and self.physician_id and not hasattr(self.physician, "provider"):
            raise ValidationError(
                {
                    "physician": (
                        f"Appointment {self.id} cannot have a missing provider for Physician f{self.physician_id}"
                    )
                }
            )

        # Set visible to False if its None.
        if self.visible is None:
            self.visible = False
            if "update_fields" in kwargs and kwargs["update_fields"]:
                kwargs["update_fields"].append("visible")

        # If the appointment was previously canceled and now has a patient associated with it, we can clear
        # the cancelation reason, we use None for canceled_by_system since the appt is no longer canceled
        if self.patient and self.cancelation_reason:
            self.cancelation_reason = None
            self.canceled_by_system = None
            if "update_fields" in kwargs and kwargs["update_fields"]:
                kwargs["update_fields"].append("cancelation_reason")
                kwargs["update_fields"].append("canceled_by_system")

        # Set scheduled at
        self.set_scheduled_at(
            kwargs["update_fields"] if "update_fields" in kwargs and kwargs["update_fields"] else None
        )

        if self.source is None or self.source == "":
            logger.info("setting appointment source to Elation for appointment elation id: %d", self.elation_id)
            self.source = AppointmentSource.ELATION

        if self.elation_update and self.source == AppointmentSource.LUCIAN:
            logger.info("Attempt to update appointment currently managed in lucian via elation. ID: %d", self.pk)
        super().save(*args, **kwargs)
        self._pre_save_missed = self.missed

    def get_cancelable_status(self) -> Tuple[bool, Optional[str]]:
        """
        Can we cancel this Appointment? Return a user-facing reason if not
        """
        if self.start <= timezone.now():
            return (False, "Appointment start time has already passed")
        return (True, None)

    def cancel(
        self,
        cancelation_reason=None,
        canceled_by_system=None,
        cancellation_reason_ids: Optional[List[int]] = None,
        canceled_by_patient=None,
        reschedule_action=None,
        patient_other_reason=None,
        firefly_other_reason=None,
    ):
        from firefly.modules.appointment.utils import (
            format_cancellation_reason,
            get_action_performed_by,
            is_custom_appointment,
        )
        from firefly.modules.events.models import EventLog, EventTypeCodes
        from firefly.modules.schedule.models import ShiftException

        can_cancel, _ = self.get_cancelable_status()
        if not can_cancel or self.status == AppointmentStatus.CANCELLED.value:
            return False

        custom_appointment = is_custom_appointment(self)
        # Elation has 2 concepts related to appointments: appointments and appointment slots.
        # An appointment is an ad-hoc meeting with a patient, explicitly scheduled by a clinician.
        # An appointment slot, on the other hand, is an open slot on the clinician's schedule, which appears as bookable
        # for scheduling by a patient.
        # If an appointment slot is cancelled - it is opened up so that another patient can book it.
        # However if an appointment is cancelled - the status is set to cancelled. (Since it was intended for use with
        # a specific member). This also shows up in the elation calendar marked as "CANCELLED". On the other hand
        # cancelled slots will not leave a marker on the elation calendar.
        old_patient_id = self.patient_id
        if self.time_slot_type == SlotType.APPOINTMENT_SLOT or custom_appointment:
            self.patient = None
            self.description = ""  # Clear our the description from the previous appt
            self.other_symptoms = None  # Clear our the other_symptoms from the previous appt
            self.symptoms.set([])  # Clear our the symptoms from the previous appt
        else:
            self.status = AppointmentStatus.CANCELLED.value
        # If shift exception present cancel reason set to Provider Unavailable
        is_physician_unavailable: bool = ShiftException.objects.filter(
            schedule__provider__physician=self.physician,
            period__contains=self.start,
        ).exists()
        if is_physician_unavailable:
            canceled_by_system = True
            cancelation_reason = AppointmentCancelationReason.PROVIDER_UNAVAILABLE
        if cancellation_reason_ids and len(cancellation_reason_ids):
            cancelation_reason = format_cancellation_reason(
                cancelation_reason,
                cancellation_reason_ids,
                canceled_by_system,
                patient_other_reason,
                firefly_other_reason,
            )
        self.cancelation_reason = cancelation_reason
        self.canceled_by_system = canceled_by_system
        # Remove scheduled event notified time, to resend notification on scheduling again
        self.sms_notice_sent_at = None
        # This will cancel appointment
        # and will apply appointment rules
        # when source Elation
        # if patient Cancel appointment
        # appointment rule will undel Lucian Video and Video-new slot for same time
        # and then apply rule
        self.save()
        # Sending all cancellation related info directly to EventLog instead of storing on Appointment
        EventLog.objects.create(
            target=self,
            type=EventTypeCodes.APPOINTMENT_CANCELLED,
            user_id=old_patient_id,
            metadata={
                "changed": {"patient": [old_patient_id, self.patient_id]},
                "current_appointment": {
                    "elation_id": self.elation_id,
                    "physician_id": self.physician_id,
                    "reason": self.reason,
                    "description": self.description,
                    "patient_id": self.patient_id,
                    "updated_by_id": self.updated_by.id if self.updated_by else None,
                    "cancelation_reason": cancelation_reason,
                    "cancellation_reasons": list(set(cancellation_reason_ids)) if cancellation_reason_ids else [],
                    "patient_other_reason": patient_other_reason,
                    "firefly_other_reason": firefly_other_reason,
                    "reschedule_action": reschedule_action,
                    "canceled_by_system": canceled_by_system,
                    "action_performed": EVENT_LOG_ACTION_PERFORMED.CANCELED,
                    "action_performed_by": get_action_performed_by(self, canceled_by_patient),
                },
            },
        )
        if (
            self.source == AppointmentSource.LUCIAN
            or (
                self.physician
                and self.physician.provider
                and self.physician.provider.user
                and self.appointment_slots.all().count() > 0
            )
            or is_physician_unavailable
        ):
            # Deletes the previously booked slot from elation
            # Ressurects the same slot within lucian to be open for other members
            self.delete()
        return True

    def set_scheduled_at(self, update_fields=None):
        if self.scheduled_at is None and self.physician is not None and self.patient is not None:
            # When we save an appointment using update_fields, we will want to force updating the scheduled_at
            # timestamp. This happens when we sync with Elation which uses update_fields.
            if update_fields:
                update_fields.append("scheduled_at")
            self.scheduled_at = timezone.now()

        if self.scheduled_at is not None and (self.physician is None or self.patient is None):
            if update_fields:
                update_fields.append("scheduled_at")
            self.scheduled_at = None

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.CheckConstraint(
                name="%(app_label)s_%(class)s_elation_id_valid",
                check=models.Q(source="Lucian") | models.Q(elation_id__isnull=False),
            ),
        ]
        db_table = "appointments"
        permissions = (("join_appointment", "Can join appointment"),)


class AppointmentImage(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    appointment = models.ForeignKey(Appointment, related_name="images", null=True, on_delete=models.SET_NULL)  # noqa: TID251

    image = models.ImageField(upload_to="appointment-images/")

    class Meta(BaseModelV3.Meta):
        db_table = "appointment_images"


# A session for an appointment refers to an instance when the clinician and the
# member joined the appointment. Ideally there would be one of these for an appointment
# - however its realistic that we will run into scenarios where we have multiple of these
# because of folks having to drop out / rejoin an appointment
class AppointmentSession(BaseModelV3):
    appointment = models.ForeignKey(Appointment, related_name="sessions", null=False, on_delete=models.CASCADE)
    # Defines a unique session
    identifier = models.TextField(blank=False, null=False)
    # Each appointment is named uniquely -  this logic lives in
    # firefly/core/services/zoom/client.py::ZoomClient::get_jwt_properties_from_appointment
    # adding here so that we have a record of the naming in case we decide to update
    # our convention
    name = models.TextField(blank=False, null=False)
    # These will be filled out after the session is complete
    start_at = models.DateTimeField(null=True, blank=True)
    end_at = models.DateTimeField(null=True, blank=True)
    has_recording = models.BooleanField(blank=True, null=True)
    external_recording_url = models.URLField(max_length=255, null=True, blank=True)
    # TODO: Update to fields - once we can point our file field to use
    # env specific s3 buckets
    s3_recording_url = models.URLField(max_length=255, null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        constraints = [
            constraints.UniqueConstraint(
                fields=[
                    "appointment",
                    "identifier",
                ],
                condition=Q(deleted=None),
                name="appointment_session_unique_appointment_identifier",
            ),
        ]


class Transcript(BaseModelV3):
    session = models.OneToOneField(
        AppointmentSession,
        related_name="transcript",
        null=False,
        on_delete=models.CASCADE,
    )
    scribe_job_name = models.TextField(blank=False, null=False)
    STATUS_COMPLETED = "COMPLETED"
    STATUS_QUEUED = "QUEUED"
    STATUS_IN_PROGRESS = "IN_PROGRESS"
    STATUS_FAILED = "FAILED"
    STATUS_CHOICES = [
        (STATUS_COMPLETED, "COMPLETED"),
        (STATUS_QUEUED, "QUEUED"),
        (STATUS_IN_PROGRESS, "IN_PROGRESS"),
        (STATUS_FAILED, "FAILED"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(max_length=255, choices=STATUS_CHOICES, null=True, blank=True)  # noqa: TID251
    started_at = models.DateTimeField(null=False, blank=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    transcript_uri = models.TextField(blank=True, null=True)
    summary_uri = models.TextField(blank=True, null=True)
    transcript_json = models.JSONField(blank=True, null=True)
    summary_json = models.JSONField(blank=True, null=True)


class SummaryNote(BaseModelV3):
    appointment = deprecate_field(
        models.OneToOneField(
            Appointment,
            related_name="summary_note",
            null=False,
            on_delete=models.CASCADE,
        )
    )
    note = deprecate_field(models.TextField(blank=True, null=True))


class SummaryRating(BaseModelV3):
    note = deprecate_field(
        models.OneToOneField(
            SummaryNote,
            related_name="rating",
            null=False,
            on_delete=models.CASCADE,
        )
    )
    rating = deprecate_field(models.IntegerField(null=True, blank=True))
    comment = deprecate_field(models.TextField(null=True, blank=True))


class CancellationReason(BaseModelV3):
    """
    Either a (parent) reason or a sub-reason for canceling an appointment
    """

    uid = models.TextField()
    label = models.TextField()
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    parent = models.ForeignKey("CancellationReason", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251
    is_patient_facing = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["uid"],
                condition=Q(deleted=None),
                name="cancellation_reasons_uid_uniq",
            )
        ]


class AppointmentSymptoms(BaseModelV3):
    appointment = models.ForeignKey(
        Appointment,
        related_name="appointment_symptoms",
        on_delete=models.CASCADE,
    )
    symptom = models.ForeignKey(
        Symptom,
        related_name="appointment_symptoms",
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["appointment", "symptom"],
                condition=Q(deleted=None),
                name="appointment_symptoms_uniq",
            )
        ]
