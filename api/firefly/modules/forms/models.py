import logging
import uuid
from contextlib import contextmanager

from django.conf import settings
from django.contrib.contenttypes.fields import GenericF<PERSON>ign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.query_utils import Q
from django.utils.translation import gettext_lazy as _
from django_deprecate_fields import deprecate_field
from rest_framework.exceptions import ValidationError

from firefly.modules.appointment.constants import AppointmentStatus
from firefly.modules.appointment.models import Appointment
from firefly.modules.cases.constants import CaseCategoryUniqueIndentifiers
from firefly.modules.content.models import Content
from firefly.modules.firefly_django.models import BaseModelV3, SafeDeleteManagerWithBulkUpdate
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.forms.constants import (
    RESOURCE_CHOICES,
    VENDOR_CHOICES,
    VENDOR_CHOICES_FOR_RESOURCE_MAP,
    VENDOR_RESOURCE_MAP,
    FormUID,
    Vendors,
)
from firefly.modules.forms.mixin import PollableJobMixin
from firefly.modules.tenants.models import Tenant

logger = logging.getLogger(__name__)


def _get_uuid():
    return str(uuid.uuid4())


class Form(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255)  # noqa: TID251
    description = models.TextField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, default=_get_uuid)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    content = models.ForeignKey(Content, related_name="about", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    is_active = models.BooleanField(default=True)
    is_system_only = models.BooleanField(blank=True, null=True)
    show_overview = models.BooleanField(default=True)

    question_json = JSONField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor = models.CharField(choices=VENDOR_CHOICES, max_length=255, null=True, blank=True, default=Vendors.FIREFLY)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor_form_id = deprecate_field(models.CharField(max_length=255, null=True, blank=True))  # noqa: TID251

    # Default due date offset for form, (in days).
    default_due_date_offset = models.IntegerField(default=2)

    on_assign_task_collection = models.ForeignKey(
        "tasks.TaskCollection",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        help_text="A TaskCollection used to generate tasks when a FormSubmission is assigned to a patient",
        related_name="+",
    )
    on_complete_task_collection = models.ForeignKey(
        "tasks.TaskCollection",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        help_text="A TaskCollection used to generate tasks when a FormSubmission is completed by a patient",
        related_name="+",
    )

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    tenant = models.ForeignKey(Tenant, on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251

    submission_viewable = models.BooleanField(default=True)

    case_category = models.ForeignKey(
        "cases.CaseCategory",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        help_text="Category of case that should be created for the FormSubmission",
    )
    case_config = models.JSONField(
        null=True,
        blank=True,
        help_text="Defines behavior of case connected to this FormSubmission",
    )
    create_linked_case_after_submit = models.BooleanField(
        null=True,
        blank=True,
        help_text="Create linked Cases after submission, instead of the default, which is after assignment",
    )
    expires_after = models.IntegerField(
        null=True,
        blank=True,
        default=None,
        help_text="Defines number of days after which form expires, (in days).",
    )
    current_form_definition = models.ForeignKey(
        "forms.FormDefinition",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        help_text=f"For vendor {Vendors.FIREFLY_V2}: the versioned template which should be considered current",
        related_name="current_for_forms",
    )

    class Meta(BaseModelV3.Meta):
        db_table = "forms"
        constraints = [
            models.UniqueConstraint(
                fields=["uid"],
                condition=Q(deleted=None),
                name="formuid_unique_not_deleted",
            )
        ]

    def save(self, *args, **kwargs):
        if self.current_form_definition and self.current_form_definition.form_id != self.id:
            raise ValidationError(
                f"FormDefinition {self.current_form_definition.id}"
                + f" is configured for Form {self.current_form_definition.form_id}"
            )

        adding = self._state.adding

        super().save(*args, **kwargs)
        if adding:
            FormSection.objects.get_or_create(form=self, defaults={"position": 0, "title": "Default"})


class IncompleteFormSubmissionManager(SafeDeleteManagerWithBulkUpdate["FormSubmission"]):
    def get_queryset(self):
        return super().get_queryset().filter(completed_at__isnull=True, expired_at__isnull=True)


class FormDefinition(BaseModelV3):
    """
    A versioned definition of a Form
    """

    form: models.Field = models.ForeignKey(Form, on_delete=models.CASCADE)
    # Monotonically increasing version number of the definition for each form.
    version = models.IntegerField(blank=False, null=False)
    # Defines components, styling, etc.
    # May eventually break out this data into separate fields
    attributes = JSONField(null=True, blank=True)
    # If a form is rendered to PDF using a custom template, specify the key here
    pdf_template_key = models.TextField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["form_id", "version"],
                condition=Q(Q(deleted=None) & ~Q(version=None)),
                name="formdef_version_unique",
            )
        ]


class FormSubmission(SaveHandlersMixin, BaseModelV3):
    """A form submission by a user. Answer data is stored as a JSON field."""

    # Note: This would normally overwrite the default `objects` manager. See
    # https://docs.djangoproject.com/en/3.2/topics/db/managers/#manager-names
    #
    # We still want `objects` as added by BaseModelV3 to be the default, so we
    # make it explicit with `default_manager_name` in this model's Meta class.
    # See https://docs.djangoproject.com/en/3.2/topics/db/managers/#default-managers
    incomplete_objects = IncompleteFormSubmissionManager()

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="assigned_form_submissions",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="form_submissions",
    )
    form = models.ForeignKey(Form, on_delete=models.CASCADE)
    # Which version of a Form was this FormSubmision started/completed with?
    # This may change for a given submission, e.g. if a member tries to resume an in-progress submission
    # after the form's current form definition had been updated
    form_definition = models.ForeignKey(FormDefinition, on_delete=models.CASCADE, null=True, blank=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")

    # A dictionary mapping field ids to data values
    # {
    #   "1234123": "Answer"
    # }
    data = JSONField(default=dict, null=True, blank=True)
    # Temporary field to store raw data while we prepare to move away from transforming/decamelizing
    # the raw data coming from our forms engine
    raw_data = JSONField(default=dict, null=True, blank=True)

    # TODO: Add state - assigned, in_progress, completed, reviewed, expired
    # TODO: Add reviewed_at, reviewed_by
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    expired_at = models.DateTimeField(null=True, blank=True)

    metadata = JSONField(default=dict, null=True, blank=True)

    task_relations = GenericRelation("tasks.TaskRelation")
    case_relations = GenericRelation("cases.CaseRelation")

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor_submission_id = models.CharField(max_length=255, unique=True, null=True, blank=True)  # noqa: TID251

    # Allow skipping the automatic applying of rules during save()
    # e.g. in a backfill command
    # where we do not want to trigger case state transitions
    _skip_rule_application = False

    @property
    def skip_rule_application(self):
        return self._skip_rule_application

    @skip_rule_application.setter
    def skip_rule_application(self, value):
        self._skip_rule_application = value

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.forms.utils import send_form_assigned_notification

        if changed("id") and self.form and self.user and not self.completed_at and not self.deleted:
            # Don't send a notification if the member User created the form
            # They already know it was assigned and we shouldn't bug them
            if self.user != self.created_by:
                send_form_assigned_notification.send(form_submission_id=self.id)

    def save(self, *args, **kwargs):
        from firefly.modules.forms.utils import mark_form_submission_tasks_complete

        created = self.pk is None

        if created and self.form and self.form.current_form_definition and self.form_definition is None:
            self.form_definition = self.form.current_form_definition

        super().save(*args, **kwargs)

        if self.completed_at:
            mark_form_submission_tasks_complete(self)

        if created and not self.form.create_linked_case_after_submit:
            self.create_linked_case()

        # conditions on forms should be set appropirately if we
        # need to not run a rule during form creation
        try:
            from .rules import apply_rules

            if not self.skip_rule_application:
                apply_rules(self)
            else:
                logger.info("Skipping rule application for FormSubmission %s", self.id)
        except Exception:
            logger.exception("Applying rules failed on FormSubmission %s", self.id)

    def create_linked_case(self):
        """
        Creates a Case with the configured category
        This could be run when the Form is first created or when the Form is submitted
        """
        from firefly.modules.cases.models import Case, CaseRelation
        from firefly.modules.notifications.schedulers import _get_appointment_start_time

        if self.form.case_category is None:
            return
        case, created = Case.objects.get_or_create(
            category=self.form.case_category,
            person=self.user.person,
            relations__content_type=ContentType.objects.get_for_model(FormSubmission),
            relations__object_id=self.pk,
            defaults={"description": self.form.case_category.description, "notes": self.form.case_category.notes},
        )
        if not created:
            return case

        if self.form.uid in [FormUID.HEALTH_ASSESSMENT_V2, FormUID.HEALTH_ASSESSMENT_V3]:
            next_earliest_appointment = (
                Appointment.objects.filter(
                    patient=self.user,
                    status=AppointmentStatus.SCHEDULED.value,
                )
                .order_by("start")
                .first()
            )

            if (
                next_earliest_appointment
                and case.category.unique_key == CaseCategoryUniqueIndentifiers.HEALTH_REVIEW_FORM
            ):
                description = f"Upcoming appointment : {next_earliest_appointment.start_user_local.strftime('%A, %B %-d')} at {_get_appointment_start_time(next_earliest_appointment)}"  # noqa: E501
                if case.description is None:
                    case.description = description
                    case.save(update_fields=["description"])

        CaseRelation.objects.create(
            case=case,
            content_object=self,
        )
        return case

    class Meta:
        db_table = "form_submissions"
        # See note above. We still want `objects` as added by BaseModelV3 to be
        # the default manager, not `incomplete_objects`.
        default_manager_name = "objects"


@contextmanager
def disable_rule_application(form_submission: FormSubmission):
    form_submission.skip_rule_application = True
    yield
    form_submission.skip_rule_application = False


# Maps form submission to resources in different vendors(Zus/Elation)
# Mostly helpful to understand if the resource is generated as expected during auto upload of any form data to
# the vendor resources
# Eg: Upon submitting Health Review form, a non visit note would be created automatically in Elation, with
# the data filled in the form. This model stores the identifier for non visit note id for the corresponding
# form submission
class FormDataVendorResourceMap(BaseModelV3):
    form_submission = models.ForeignKey(
        FormSubmission,
        related_name="form_submissions",
        on_delete=models.CASCADE,
    )
    vendor = models.TextField(choices=VENDOR_CHOICES_FOR_RESOURCE_MAP)
    resource = models.TextField(choices=RESOURCE_CHOICES)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor_resource_id = models.CharField(max_length=255)  # noqa: TID251

    def save(self, *args, **kwargs):
        if self.resource not in VENDOR_RESOURCE_MAP[self.vendor]:
            raise ValidationError({"resource": ("Resource does not belong to the selected vendor")})
        return super().save(*args, **kwargs)


class PdfExportJobStatus(models.TextChoices):
    """
    Describes the lifecycle state of a PDF export job:

        Pending --> In Progress --> Complete
        ^                     |
        |                     +---> Failed
        |                                |
        +--------------------------------+
    """

    PENDING = "pending", _("Pending")
    IN_PROGRESS = "in-progress", _("In progress")
    COMPLETE = "complete", _("Complete")
    FAILED = "failed", _("Failed")

    def __eq__(self, other: object) -> bool:
        return str(self) == str(other)


class PdfExportJob(PollableJobMixin, BaseModelV3):
    """
    Represents the lifecycle of generating a PDF to display a form submission.
    """

    form_submission = models.ForeignKey(
        FormSubmission,
        related_name="pdf_export_jobs",
        on_delete=models.CASCADE,
        help_text="Form submission from which to generate a PDF.",
    )

    file = models.FileField(
        upload_to="forms-pdf-export/",
        blank=True,
        null=True,
        help_text="Generated PDF file, if job status is Complete.",
    )

    def job_handler(self, job_id):
        from .tasks import export_pdf_file

        logger.info(
            "[PdfExportJob job_handler]: Starting pdf export job: %d for form submission: %d",
            job_id,
            self.form_submission.id,
        )
        self.file = export_pdf_file(self.form_submission)
        self.save(update_fields=["file"])


# DEPRECATED: Do not use
# These Models were part of the V1 firefly built form engine which are no longer used. These models are kept to support
# legacy data and should not be used for future use cases.
class FormSection(BaseModelV3):
    form = models.ForeignKey(Form, related_name="sections", null=True, on_delete=models.CASCADE)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, default=_get_uuid)  # noqa: TID251

    title = models.TextField()
    description = models.TextField(null=True, blank=True)

    position = models.IntegerField()

    logic = JSONField(null=True, blank=True)

    class Meta:
        db_table = "form_sections"
        ordering = ["position"]
        constraints = [
            models.UniqueConstraint(
                fields=["uid"],
                condition=Q(deleted=None),
                name="formsection_unique_not_deleted",
            )
        ]


# DEPRECATED: Do not use
# These Models were part of the V1 firefly built form engine which are no longer used. These models are kept to support
# legacy data and should not be used for future use cases.
class FormQuestion(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    form = models.ForeignKey(Form, related_name="questions", null=True, on_delete=models.SET_NULL)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    section = models.ForeignKey(FormSection, related_name="questions", null=True, on_delete=models.SET_NULL)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, default=_get_uuid)  # noqa: TID251

    # Subsection title
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    label = models.CharField(max_length=63, default="", blank=True)  # noqa: TID251

    # Question text
    text = models.TextField(null=True, blank=True)

    # Subtext for question
    description = models.TextField(null=True, blank=True)

    # Question type and type-specific metadata
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(max_length=63)  # noqa: TID251
    type_options = JSONField(default=dict, blank=True)

    position = models.IntegerField()

    condition = JSONField(null=True, blank=True)
    requires_non_form_data = models.BooleanField(default=False)  # TODO: Deprecate this field

    required = models.BooleanField(default=False)
    hidden = models.BooleanField(default=False)

    # Is last question
    is_terminal = models.BooleanField(default=False)

    # Configurations for validation, logic (show/hide) and computations
    validation = JSONField(null=True, blank=True)
    logic = JSONField(null=True, blank=True)
    formula = JSONField(null=True, blank=True)

    class Meta:
        db_table = "form_questions"
        ordering = ["position"]


# DEPRECATED: Do not use
# These Models were part of the V1 firefly built form engine which are no longer used. These models are kept to support
# legacy data and should not be used for future use cases.
class QuestionSubmission(BaseModelV3):
    """Represents a patient's answer to a question within a form"""

    """UID will be set in post_save signal"""
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = deprecate_field(models.CharField(max_length=63, default=_get_uuid))  # noqa: TID251
    user = deprecate_field(models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE))
    form_question = deprecate_field(
        models.ForeignKey(FormQuestion, related_name="question_submissions", on_delete=models.CASCADE)
    )
    form_submission = deprecate_field(
        models.ForeignKey(
            FormSubmission,
            related_name="question_submissions",
            on_delete=models.CASCADE,
        )
    )
    """Denormalized info about the question we asked
    s.t. we can tell what the patient answered even if the FormQuestion later changes
    """
    form_question_uid = deprecate_field(models.TextField(null=True, blank=True))
    form_question_text = deprecate_field(models.TextField(null=True, blank=True))
    form_question_description = deprecate_field(models.TextField(null=True, blank=True))
    form_question_is_calculated = deprecate_field(models.BooleanField(null=True, blank=True))
    """Store the string representation of the answer
    This may be set directly by the client
    or automatically if the question is calculated
    (by compute_calculated_field)
    """
    answer = deprecate_field(models.TextField(null=True, blank=True))
    """If there was an error calculating the value, store that error here.
    """
    error = deprecate_field(models.TextField(null=True, blank=True))
