# Forms

The Forms module manages form definitions, submissions, and lifecycle for Firefly Health. Forms are now primarily managed through the **Forms V2 system** using the forms-ui frontend and Form.io engine.

## Architecture Overview

### System Components
The forms system consists of two main services:

1. **forms-ui React frontend** (part of the monorepo) which allows users to create new Form Definitions, fill out Form Submissions, and view Form Submissions
2. **pdf-export Node app** (also part of the monorepo under `api/`). This is deployed as a sidecar container alongside the api container. Its purpose is to render the forms-ui frontend in a headless browser to generate a PDF. We use this Node app, vs. our existing Django app, to generate PDFs, because it was already built and Node can better handle long-running requests compared to Django

### Key Concepts

#### Form Definitions vs Form Submissions

**Form Definitions** are how we store the questions, formatting, images, etc. of any given Form. As we "make updates to a Form", we are really creating new Form Definitions. This allows us to keep track of the history of changes to the form's contents, so we can think about a Form like the "Quick Health Check" form as having different versions (Form Definition #1 when it is first created, Form Definition #2 after an update, etc.). A Form will be associated with just 1 current Form Definition at a time, which defines the questions we'll ask members if we were to assign the Form at that time. But, we'll also be able to look back at older Form Definitions for that Form.

**Form Submissions** represent a submission of a particular form by a member; when we assign a Form to a member, they are really seeing a task to complete a Form Submission. A Form Submission is connected to exactly 1 Form Definition so that, even as changes are made to a form and new Form Definitions are created, we will always be able to tie the member's submission back to the version of the Form which was current at the time. One example where this is important is updates which change the order of questions; if a member answered "Yes" to question #3, we want to know what question #3 was when the patient answered it, not what question #3 is now, in the Form's current Form Definition.

### Form Engines
Forms use different rendering engines tracked by `Form.vendor_id`:
1. **Form.io Engine**: Current primary engine built on Form.io technology
2. **Legacy Firefly Engine**: Deprecated JSON-based system (kept for historical data)

## Making Changes to Forms

### For Content Updates
1. Navigate to [forms-ui frontend](https://forms-ui.dev.i.firefly.health/)
2. Edit the form using the visual editor
3. Save changes (creates new Form Definition version)
4. Contact engineering to activate the new version via Django admin (`Form.current_form_definition`)

### For Technical Changes
- **Case Integration**: Configure `Case category` and `Case config` in Django admin
- **Form Logic**: Use forms-ui editor for conditional logic and validation
- **Confirmation Pages**: Engineers edit `FormDefinition.attributes.formDefinition.confirmationPage`

## Development Workflow

### Creating New Forms
1. Use the forms-ui frontend to create and configure forms
2. Test form functionality in development environment
3. Coordinate with engineering team for production deployment

### Updating Existing Forms
1. Edit forms through the visual editor (creates new Form Definition version)
2. Test changes thoroughly before requesting activation
3. Contact engineering to update `Form.current_form_definition` in production

## Testing & Validation

### Edit a Form => Create a new Form Definition
1. Navigate to the forms-ui frontend, e.g. https://forms-ui.dev.i.firefly.health/
2. Edit a Form Definition by clicking on the pencil icon for a given Form
3. Make an update using the visual editor
4. After saving your changes, you should see a message: "You are editing a new version of this Form. It is not being assigned to members yet."

### Updating a Form's current Form Definition
1. In Django admin, update `Form.current_form_definition`. You should not be able to save any record where `FormDefinition.form != Form.id`
2. After saving, the forms-ui frontend page for that Form should show the newly-updated current version in the "Developer info" section
3. `FormDefinition.attributes.formDefinition.confirmationPage` should carry over between versions

### Fill out a form from the app
1. Use Lucian to assign a Form to a patient
2. In the app, open the Form
3. Fill out some partial information, e.g. a single answer, and then close the app
4. Reopen the app. The partial information entered before should be pre-populated even though the Form hasn't been completed

### View a submission
1. Use Lucian, navigate to the member's page and the Forms tab
2. Click on the Form Submission. The Form.io viewer should render the question and filled answers
3. Use the "Download PDF" button and verify that the PDF matches the expected info from Lucian

## Form data gotchas

### Camelization

Form question UIDs are camelized/decamelized between front-end and back-end and between JSON/Python. When form logic references a UID `some_uid`, the JSON value for the key should be `someUid`. This is especially tricky when referencing another field from within JSON:


```json
// formdata/form.json
{
"questions": [
    { "uid" : "covidPastFever"},
    {
        "uid": "calculation",
        "formula": {
            "args": [
                "covid_past_fever",
            ],
            "type": "sum"
        }
    }
],
```

In the above example, notice that the question we want to reference has UID `"covidPastFever"` but even within this JSON we need to reference it with `"covid_past_fever"` (the decamelized value) instead.

## Form Case Linking (Optional)
To automatically create a case on Form assignment, select a `Case category`

### Case Config (Optional)
Configure this to change case action e.g. change the case's status on the form submission

When this is configured, you can still skip the application of the rules by setting the `skip_rule_application` property to `True` (or use the `with disable_rule_application()...` context manager). This may be useful for things like backfills, which need to write to the table but should not trigger case actions.

#### Examples:
##### 1. Change case status when the form is submitted and the case is currently in the specific status

```json
{
  "rules": [
    {
      "then": [
        {
          "case__action": "Needs Review"
        }
      ],
      "when": [
        {
          "case__status__in": [
            "Assigned",
            "Final Outreach",
            "Stalled"
          ],
          "form_submission__completed_at__isnull": false
        }
      ]
    }
  ]
}
```

##### 2. Change case status when the form is submitted and allow transition from any status
To achieve this,

1) Omit the “case__status__in” from the Case Config
2) In the State machine of the Case linked to the form, the action state should be reachable from all the states (“*”)
```json
{
  "dest": "Needs Review",
  "source": "*",
  "trigger": "Needs Review"
}
```

## expire_old_incomplete_form_submissions :
This is used to expire any form submission and uses bulk update hence no side effect will be executed

## expire_all_incomplete_form_submissions_with_configured_expiry_date :
1) Forms have `expire_after` field denoting that the form submission expires after certain number of days
2) Case config in Forms have a rule to close all the cases with no open tasks when a form submission expires
3) This admin command scans through all the form submissions which are expired and updates `expired_at` field on form submission
4) In-order to trigger the above rule configured in the forms we need to individually save() the form submissions as
   bulk_update does not trigger the above rule/side effect.

## Core Business Logic

### Key Code Locations

**Models and Data Layer:**
- `firefly/modules/forms/models.py` - Core form models (Form, FormDefinition, FormSubmission)
- `firefly/modules/forms/v2/` - Forms V2 API and serializers for current form system
- `firefly/modules/forms/rules.py` - Case config rules processing and application

**Form Processing:**
- `firefly/modules/forms/utils.py` - Form assignment, expiration, and lifecycle utilities
- `firefly/modules/forms/tasks.py` - Asynchronous form processing and expiration tasks
- `firefly/modules/forms/elation_upload.py` - EMR form data synchronization

**Management Commands:**
- `firefly/modules/forms/management/commands/expire_old_incomplete_form_submissions.py` - Bulk expiration
- `firefly/modules/forms/management/commands/expire_all_incomplete_form_submissions_with_configured_expiry_date.py` - Individual expiration with side effects
- `firefly/modules/forms/management/commands/auto_upload_health_assessment_v3_data_to_elation.py` - Elation integration
- `firefly/modules/forms/management/commands/backfill_forms_missing_cases.py` - Case linking backfill

**Frontend Integration:**
- `forms-ui/` - React frontend for form creation and editing
- `api/pdf-export/` - Node.js service for PDF generation from form submissions

**Legacy Components (Deprecated):**
- `FormQuestion` and `QuestionSubmission` models - Kept for historical data only
- Legacy JSON-based form engine - No longer used for new forms

**Key Business Rules:**
- **Form Versioning**: Form Definitions track version history, Form Submissions link to specific versions
- **Case Linking**: Forms can automatically create cases via `Case category` configuration
- **Case Config Rules**: JSON rules that trigger case actions on form submission
- **Form Expiry**: Forms expire after configured days, triggering case closure rules
- **Rule Application**: Can be disabled via `skip_rule_application` or `disable_rule_application()` context manager

## Related Documentation

- **Frontend Documentation**: See `/forms-ui/README.md` for comprehensive frontend documentation
- **Forms V2 API**: See `/api/firefly/modules/forms/v2/` for current API implementation
- **Case Management**: See `/api/firefly/modules/cases/README.md` for case integration details
