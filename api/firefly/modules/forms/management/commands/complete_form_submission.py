import logging
from datetime import datetime

from django.utils import timezone

from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand
from firefly.modules.forms.utils import complete_form_submission

logger = logging.getLogger(__name__)


class Command(FireflyBaseCommand):
    help = "Complete a FormSubmission and related tasks of a given FormSubmission id"

    def add_arguments(self, parser):
        parser.add_argument("--form_submission_id", type=str, required=True, help="Form Submission ID")
        parser.add_argument("--debug", type=bool, required=False, default=False)
        parser.add_argument(
            "--completed_date",
            type=str,
            required=False,
            default=None,
            help=("FormSubmission will be marked with this completed date yyyy-mm-dd hh:mm:ss"),
        )

    def run(self, *args, **options):
        logger.info("Starting complete_form_submission.")

        debug = options["debug"]
        form_submission_id = options["form_submission_id"]
        if options["completed_date"]:
            completed_date = datetime.strptime(options["completed_date"], "%Y-%m-%d %H:%M:%S")
        else:
            completed_date = timezone.now()

        if debug:
            logger.info("Running in debug mode")

        complete_form_submission(form_submission_id, completed_date, debug=debug)
