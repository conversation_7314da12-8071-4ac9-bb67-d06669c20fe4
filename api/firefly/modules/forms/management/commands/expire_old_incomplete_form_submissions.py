import logging
from datetime import datetime

from django.utils import timezone

from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand
from firefly.modules.forms.utils import expire_all_incomplete_form_submissions

logger = logging.getLogger(__name__)

EXPECTED_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"


class Command(FireflyBaseCommand):
    help = "Expire all the submissions and related tasks of a given Form uid and expiration date"

    def add_arguments(self, parser):
        parser.add_argument("--form_uid", type=str, required=True, help="Form UID")
        parser.add_argument(
            "--created_before_date",
            type=str,
            required=False,
            default=None,
            help=("FormSubmissions created before or on this date will be expired yyyy-mm-dd hh:mm:ss"),
        )
        parser.add_argument("--debug", type=bool, required=False, default=False)
        parser.add_argument("--batch_size", type=int, required=True)
        parser.add_argument("--max_rows", type=int, required=True)

    def run(self, *args, **options):
        logger.info("Starting expire_old_incomplete_form_submissions.")

        if options["created_before_date"]:
            created_before_date = datetime.strptime(options["created_before_date"], EXPECTED_DATE_FORMAT)
        else:
            created_before_date = timezone.now()

        debug = options["debug"]
        form_uid = options["form_uid"]
        batch_size = options["batch_size"]
        max_rows = options["max_rows"]

        if debug:
            logger.info("Running in debug mode")

        expire_all_incomplete_form_submissions(
            form_uid,
            created_before_date,
            debug=debug,
            batch_size=batch_size,
            max_rows=max_rows,
        )
