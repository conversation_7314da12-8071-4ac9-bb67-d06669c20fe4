import zoneinfo
from enum import Enum
from typing import List

from django.contrib.postgres.constraints import ExclusionConstraint
from django.contrib.postgres.fields.ranges import DateRangeField, DateTimeRangeField, RangeOperators
from django.db import models
from django.db.models import Q

from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.models import Appointment, SymptomCategory
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.insurance.models import Contract
from firefly.modules.physician.models import Physician
from firefly.modules.states.models import State


# Day of Week ISO standard numbers
class DayOfWeek(Enum):
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7


DAY_OF_WEEK_CHOICES = [
    (DayOfWeek.MONDAY.value, "Monday"),
    (DayOfWeek.TUESDAY.value, "Tuesday"),
    (DayOfWeek.WEDNESDAY.value, "Wednesday"),
    (DayOfWeek.THURSDAY.value, "Thursday"),
    (DayOfWeek.FRIDAY.value, "Friday"),
    (DayOfWeek.SATURDAY.value, "Saturday"),
    (DayOfWeek.SUNDAY.value, "Sunday"),
]


class ExceptionReason(models.TextChoices):
    LATE_CALL_OUT = "Late Call out", "Late Call out"
    PTO = "PTO", "PTO"
    LATE_PTO = "Late PTO", "Late PTO"
    MEETING = "Meeting", "Meeting"
    OFFSITE_BUSINESS = "Offsite Business", "Offsite Business"
    OTHER = "Other", "Other"


# A provider's schedule denotes the timezone that they work in
# Downstream models also extend this to layer in the concepts of
# Shifts - What time is the provider available on a specific date
# Events - When is the provider not available on a given date
class ProviderSchedule(BaseModelV3):
    TIMEZONE_CHOICES = ((x, x) for x in sorted(zoneinfo.available_timezones(), key=str.lower))

    provider = models.ForeignKey(
        ProviderDetail,
        related_name="schedules",
        on_delete=models.CASCADE,
    )
    timezone = models.TextField(null=False, blank=False, choices=TIMEZONE_CHOICES)
    effective_period = DateRangeField(blank=False, null=False, help_text="What date range is this schedule active on")

    class Meta(BaseModelV3.Meta):
        constraints = [
            ExclusionConstraint(
                name="non_overlapping_schedules",
                expressions=[
                    ("effective_period", RangeOperators.OVERLAPS),
                    ("provider", RangeOperators.EQUAL),
                    ("timezone", RangeOperators.EQUAL),
                ],
            ),
        ]


class ReviewStatus:
    REVIEW_PENDING = "Review Pending"
    APPROVED = "Approved"
    REJECTED = "Rejected"
    COMPLETED = "Completed"


class IngestionReviewStatus(models.TextChoices):
    PENDING = ReviewStatus.REVIEW_PENDING, ReviewStatus.REVIEW_PENDING
    APPROVED = ReviewStatus.APPROVED, ReviewStatus.APPROVED
    REJECTED = ReviewStatus.REJECTED, ReviewStatus.REJECTED
    COMPLETED = ReviewStatus.COMPLETED, ReviewStatus.COMPLETED


class ScheduleIngestionJob(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    job_name = models.CharField(max_length=100, blank=False, null=False, unique=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    review_status = models.CharField(  # noqa: TID251
        max_length=100,
        blank=False,
        null=False,
        choices=IngestionReviewStatus.choices,
        default=ReviewStatus.REVIEW_PENDING,
    )
    provider = models.ForeignKey(
        ProviderDetail,
        related_name="ingestion_jobs",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )


class StagingShift(BaseModelV3):
    day_of_week = models.PositiveSmallIntegerField(
        choices=DAY_OF_WEEK_CHOICES,
        null=False,
        blank=False,
    )
    start_time = models.TimeField(blank=False, null=False)
    stop_time = models.TimeField(blank=False, null=False)
    # a provider might work from 10 AM - 4 PM in January
    # but work 11 AM - 5 PM for the remainder of the year
    effective_period = DateRangeField(blank=False, null=False, help_text="What date range is this shift active on")
    schedule = models.ForeignKey(
        ProviderSchedule,
        related_name="staging_shifts",
        on_delete=models.CASCADE,
        null=True,
    )
    ingestion_job = models.ForeignKey(
        ScheduleIngestionJob,
        related_name="staging_shifts",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )


# A shift represents the usual working hours of a physician on a given date
# Shift information is associated with the day of the week and the date
# range when the shift is active
class Shift(BaseModelV3):
    day_of_week = models.PositiveSmallIntegerField(
        choices=DAY_OF_WEEK_CHOICES,
        null=False,
        blank=False,
    )
    start_time = models.TimeField(blank=False, null=False)
    stop_time = models.TimeField(blank=False, null=False)
    # a provider might work from 10 AM - 4 PM in January
    # but work 11 AM - 5 PM for the remainder of the year
    effective_period = DateRangeField(blank=False, null=False, help_text="What date range is this shift active on")
    schedule = models.ForeignKey(
        ProviderSchedule,
        related_name="shifts",
        on_delete=models.CASCADE,
        null=True,
    )
    staging_shift = models.OneToOneField(
        StagingShift,
        related_name="shift",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )


class StagingShiftException(BaseModelV3):
    schedule = models.ForeignKey(
        ProviderSchedule,
        related_name="staging_shift_exceptions",
        on_delete=models.CASCADE,
        null=True,
    )
    period = DateTimeRangeField(blank=False, null=False, help_text="period for this event")
    ingestion_job = models.ForeignKey(
        ScheduleIngestionJob,
        related_name="staging_shift_exceptions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=ExceptionReason.choices,
    )


class ShiftException(BaseModelV3):
    schedule = models.ForeignKey(
        ProviderSchedule,
        related_name="shift_exceptions",
        on_delete=models.CASCADE,
        null=True,
    )
    period = DateTimeRangeField(blank=False, null=False, help_text="period for this event")
    staging_exception = models.OneToOneField(
        StagingShiftException,
        related_name="shift_exception",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=ExceptionReason.choices,
    )

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["schedule", "period"],
                condition=Q(deleted__isnull=True),
                name="schedule_period_uniq",
            ),
        ]


class TimeSlot(BaseModelV3):
    shift = models.ForeignKey(
        Shift,
        related_name="time_slots",
        on_delete=models.CASCADE,
    )
    period = DateTimeRangeField(blank=False, null=False, help_text="period for this slots")

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["shift", "period"],
                condition=Q(deleted__isnull=True),
                name="shift_period_slot_uniq",
            ),
        ]


class AppointmentSlots(BaseModelV3):
    slot = models.ForeignKey(
        TimeSlot,
        related_name="appointment_slots",
        on_delete=models.CASCADE,
    )
    appointment = models.ForeignKey(
        Appointment,
        related_name="appointment_slots",
        on_delete=models.CASCADE,
    )

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["slot", "appointment"],
                condition=Q(deleted__isnull=True),
                name="slot_appointment_uniq",
            ),
        ]


# Stores appointment types supported in the system
# Model can be expanded to store configuration for different
# appointment types including duration / pre and post buffer durations
# for appointment prep and appointment documentation
class AppointmentType(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(unique=True, max_length=255, null=False, blank=False)  # noqa: TID251
    duration = models.IntegerField(null=False, blank=False, default=0)
    buffer_time_in_minutes = models.IntegerField(null=False, blank=False, default=0)
    requires_patient_booking_with_previous_provider = models.BooleanField(
        null=True,
        blank=True,
        help_text=(
            "For appointments like Behavioral Health, we want to give the patient a consistent experience"
            " with the same provider between visits. Booking with a new provider is possible via a provider booking"
        ),
    )
    exempt_from_state_licensing = models.BooleanField(
        null=True,
        blank=True,
        help_text=(
            "e.g. for Behavioral Health or Health Guide visits,"
            " the provider does not need to be licensed in the patient's state to conduct the visit"
        ),
    )
    symptom_categories = BaseModelV3ManyToManyField(
        SymptomCategory, related_name="visit_types", blank=True, through="SymptomCategoryAppointmentTypes"
    )
    requires_patient_booking_within_care_team = models.BooleanField(
        null=True,
        blank=True,
        help_text=("For appointments like Behavioral Health, we want booking within the care team only"),
    )
    booking_window_in_weeks = models.IntegerField(null=True, blank=True)


# Stores mapping of the appointment types supported by each physician
class PhysicianAppointmentTypeMapping(BaseModelV3):
    physician = models.ForeignKey(
        Physician,
        related_name="physician_appointment_type_mappings",
        on_delete=models.CASCADE,
    )
    appointment_type = models.ForeignKey(
        AppointmentType,
        related_name="physician_appointment_type_mappings",
        on_delete=models.CASCADE,
    )
    excluded_states = BaseModelV3ManyToManyField(
        State,
        related_name="provider_excluded_states",
        blank=True,
        through="PhysicianAppointmentTypeExcludedStates",
    )

    @property
    def excluded_states_list(self):
        return "\n".join([state.abbreviation for state in self.excluded_states.all()])

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["physician", "appointment_type"],
                condition=Q(deleted=None),
                name="schedule_physician_appointment_type_uniq",
            )
        ]


class PhysicianAppointmentTypeExcludedStates(BaseModelV3):
    physician_appt_type_mapping = models.ForeignKey(
        PhysicianAppointmentTypeMapping,
        related_name="physician_appt_type_excluded_states",
        on_delete=models.CASCADE,
    )
    state = models.ForeignKey(
        State,
        related_name="physician_appt_type_excluded_states",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "physician_appointment_type_excluded_states"
        verbose_name_plural = "Physician Appointment Type Excluded States"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["physician_appt_type_mapping", "state"],
                condition=Q(deleted=None),
                name="physician_appt_type_excluded_states_uniq",
            )
        ]


# Stores the appointment types supported in a given shift
# for a provider along with the expected ratios for each appointment type
# Will contain one row for each shift of a provider for each appointment type
# supported by the provider
class PhysicianVisitMixRatio(BaseModelV3):
    day_of_week = models.PositiveSmallIntegerField(choices=DAY_OF_WEEK_CHOICES)
    physician_appointment_type = models.ForeignKey(
        PhysicianAppointmentTypeMapping, related_name="visit_mix_ratios", on_delete=models.CASCADE
    )
    percentage_of_slots = models.FloatField(null=False)


class SymptomCategoryAppointmentTypes(BaseModelV3):
    appointment_type = models.ForeignKey(
        AppointmentType,
        related_name="symptomcategory_appointmenttype",
        on_delete=models.CASCADE,
    )
    symptom_category = models.ForeignKey(
        SymptomCategory,
        related_name="symptomcategory_appointmenttype",
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name_plural: str = "Symptom category Appointment types"
        constraints = [
            models.UniqueConstraint(
                fields=["appointment_type", "symptom_category"],
                condition=Q(deleted=None),
                name="appointmenttype_symptomcategory_uniq",
            )
        ]


class AppointmentTypeContractMapping(BaseModelV3):
    appointment_type = models.ForeignKey(
        AppointmentType,
        related_name="appointment_type_contract",
        on_delete=models.CASCADE,
    )
    contract = models.ForeignKey(
        Contract,
        related_name="appointment_type_contract",
        on_delete=models.CASCADE,
    )
    percentage_of_allowed_appointments = models.IntegerField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["appointment_type", "contract"],
                condition=Q(deleted=None),
                name="appointment_type_contract_uniq",
            )
        ]


class AvailableAppointmentSlotsSnapshot(BaseModelV3):
    # avoid foreign key constraint to avoid index creation and cascade delete
    physician_id = models.IntegerField(null=False, blank=False)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    # next pr will deprecate this
    state_abbreviation = models.CharField(max_length=2, null=True, blank=True)  # noqa: TID251
    # next pr will deprecate this
    contract_id = models.IntegerField(null=True, blank=True)
    contract_ids = models.TextField(null=True, blank=True)
    state_ids = models.TextField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    appointment_type_unique_key = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    start_at = models.DateTimeField(null=False, blank=False)
    snapshot_date = models.DateTimeField(null=False, blank=False)
