import mock
from dramatiq.rate_limits.backends import StubBackend

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.schedule.factories import ShiftExceptionFactory, ShiftFactory
from firefly.modules.schedule.models import Shift, ShiftException
from firefly.modules.schedule.tasks import (
    publish_shift_exception_to_calendar_async,
    publish_shift_to_calendar_async,
    remove_shift_exception_from_calendar_async,
    remove_shift_from_calendar_async,
)


class ShiftAsyncTasksTestCase(FireflyTestCase):
    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch("firefly.modules.schedule.tasks.publish_shift_to_calendar")
    def test_publish_shift_to_calendar_async(self, publish_shift_to_calendar_mock, _backend_mock):
        shift = ShiftFactory()
        publish_shift_to_calendar_async(shift_id=shift.pk)
        publish_shift_to_calendar_mock.assert_called_once_with(shift=shift)

    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch("firefly.modules.schedule.tasks.remove_shift_from_calendar")
    def test_remove_shift_from_calendar_async(self, remove_shift_from_calendar_mock, _backend_mock):
        shift: Shift = ShiftFactory.create()
        deleted_shift: Shift = ShiftFactory.create()
        deleted_shift.delete()
        for test_shift in [shift, deleted_shift]:
            remove_shift_from_calendar_mock.reset_mock()
            remove_shift_from_calendar_async(shift_id=test_shift.pk)
            remove_shift_from_calendar_mock.assert_called_once_with(shift=test_shift)


class ShiftExceptionAsyncTasksTestCase(FireflyTestCase):
    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch("firefly.modules.schedule.tasks.publish_shift_exception_to_calendar")
    def test_publish_shift_exception_to_calendar_async(self, publish_shift_exception_to_calendar_mock, _backend_mock):
        shift_exception = ShiftExceptionFactory()
        publish_shift_exception_to_calendar_async(shift_exception_id=shift_exception.pk)
        publish_shift_exception_to_calendar_mock.assert_called_once_with(shift_exception=shift_exception)

    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch("firefly.modules.schedule.tasks.remove_shift_exception_from_calendar")
    def test_remove_shift_exception_from_calendar_async(self, remove_shift_exception_from_calendar_mock, _backend_mock):
        shift_exception = ShiftExceptionFactory()
        deleted_shift_exception: ShiftException = ShiftExceptionFactory.create()
        deleted_shift_exception.delete()
        for test_shift in [shift_exception, deleted_shift_exception]:
            remove_shift_exception_from_calendar_mock.reset_mock()
            remove_shift_exception_from_calendar_async(shift_exception_id=test_shift.pk)
            remove_shift_exception_from_calendar_mock.assert_called_once_with(shift_exception=test_shift)
