from typing import Optional

from dramatiq.rate_limits.backends import Stub<PERSON>ackend
from mock import patch

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.schedule.factories import (
    ScheduleIngestionJobFactory,
    ShiftFactory,
    StagingShiftFactory,
)
from firefly.modules.schedule.models import <PERSON><PERSON>tat<PERSON>, ScheduleIngestionJob, Shift, StagingShift
from firefly.modules.schedule.tasks import generate_slot_for_provider, promote_staging_schedule_data


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
class SchedulePromotionTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_no_shift_exists(self, _backend_mock, lucian_slot_generator_mock):
        staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        self.assertEqual(Shift.objects.count(), 0)
        promote_staging_schedule_data(ingestion_job_id=self.ingestion_job.pk)
        self.assertEqual(Shift.objects.count(), 1)
        shift_data: Optional[Shift] = Shift.objects.first()
        self.assertIsNotNone(shift_data)
        if shift_data:
            self.assertEqual(shift_data.day_of_week, staging_shift.day_of_week)
            self.assertEqual(shift_data.start_time, staging_shift.start_time)
            self.assertEqual(shift_data.stop_time, staging_shift.stop_time)
            self.assertEqual(shift_data.effective_period, staging_shift.effective_period)
            self.assertEqual(shift_data.schedule, staging_shift.schedule)
            self.assertEqual(shift_data.staging_shift, staging_shift)
        generate_slot_for_provider(
            provider_id=shift_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_data.start_time.strftime("%Y-%m-%d"),
            end_date=shift_data.stop_time.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=self.ingestion_job.id,
        )
        self.ingestion_job.refresh_from_db()
        self.assertEqual(self.ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_matching_shift_exists(self, _backend_mock, lucian_slot_generator_mock):
        # Create a shift linked to a staging shift
        # This mimics that a shift exists from a previous ingestion job
        staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        linked_shift: Shift = ShiftFactory.create(
            day_of_week=staging_shift.day_of_week,
            start_time=staging_shift.start_time,
            stop_time=staging_shift.stop_time,
            effective_period=staging_shift.effective_period,
            schedule=staging_shift.schedule,
            staging_shift=staging_shift,
        )
        # Create an ingestion job where shift information has not changed
        # Expected data: All data on the shift should remain the same
        # with the exception of it being linked to the new staging shift entry
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()
        another_staging_shift: StagingShift = StagingShiftFactory.create(
            day_of_week=staging_shift.day_of_week,
            start_time=staging_shift.start_time,
            stop_time=staging_shift.stop_time,
            effective_period=staging_shift.effective_period,
            schedule=staging_shift.schedule,
            ingestion_job=another_ingestion_job,
        )

        self.assertEqual(Shift.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(Shift.objects.count(), 1)
        linked_shift.refresh_from_db()
        self.assertIsNone(linked_shift.deleted)
        self.assertEqual(linked_shift.day_of_week, another_staging_shift.day_of_week)
        self.assertEqual(linked_shift.start_time, another_staging_shift.start_time)
        self.assertEqual(linked_shift.stop_time, another_staging_shift.stop_time)
        self.assertEqual(linked_shift.effective_period, another_staging_shift.effective_period)
        self.assertEqual(linked_shift.schedule, another_staging_shift.schedule)
        self.assertEqual(linked_shift.staging_shift, another_staging_shift)
        generate_slot_for_provider(
            provider_id=linked_shift.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=linked_shift.start_time.strftime("%Y-%m-%d"),
            end_date=linked_shift.stop_time.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=another_ingestion_job.id,
        )
        another_ingestion_job.refresh_from_db()
        self.assertEqual(another_ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_non_matching_shift_exists(self, _backend_mock, lucian_slot_generator_mock):
        # Create a shift linked to a staging shift
        # This mimics that a shift exists from a previous ingestion job
        staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        unmatched_shift: Shift = ShiftFactory.create(
            day_of_week=staging_shift.day_of_week,
            start_time=staging_shift.start_time,
            stop_time=staging_shift.stop_time,
            effective_period=staging_shift.effective_period,
            schedule=staging_shift.schedule,
            staging_shift=staging_shift,
        )
        # Create an ingestion job where shift information has changed
        # Expected data: New shift data should be created and linked to the new staging shift entry
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()
        another_staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=another_ingestion_job,
        )

        self.assertEqual(Shift.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(Shift.objects.count(), 1)
        # Older shift data should be nuked
        unmatched_shift.refresh_from_db()
        self.assertIsNotNone(unmatched_shift.deleted)
        shift_data: Optional[Shift] = Shift.objects.first()
        self.assertIsNotNone(shift_data)
        if shift_data:
            self.assertEqual(shift_data.day_of_week, another_staging_shift.day_of_week)
            self.assertEqual(shift_data.start_time, another_staging_shift.start_time)
            self.assertEqual(shift_data.stop_time, another_staging_shift.stop_time)
            self.assertEqual(shift_data.effective_period, another_staging_shift.effective_period)
            self.assertEqual(shift_data.schedule, another_staging_shift.schedule)
            self.assertEqual(shift_data.staging_shift, another_staging_shift)
        generate_slot_for_provider(
            provider_id=shift_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_data.start_time.strftime("%Y-%m-%d"),
            end_date=shift_data.stop_time.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=another_ingestion_job.id,
        )
        another_ingestion_job.refresh_from_db()
        self.assertEqual(another_ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_no_staging_shift_exists(self, _backend_mock, lucian_slot_generator_mock):
        # Create a shift linked to a staging shift
        # This mimics that a shift exists from a previous ingestion job
        staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        unmatched_shift: Shift = ShiftFactory.create(
            day_of_week=staging_shift.day_of_week,
            start_time=staging_shift.start_time,
            stop_time=staging_shift.stop_time,
            effective_period=staging_shift.effective_period,
            schedule=staging_shift.schedule,
            staging_shift=staging_shift,
        )
        # Create an ingestion job where shift information has changed, but there are no staging shifts existing
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()

        self.assertEqual(Shift.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(Shift.objects.count(), 1)
        # Older shift data should not be deleted
        unmatched_shift.refresh_from_db()
        self.assertIsNone(unmatched_shift.deleted)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion__with_deleted_staging_shift_when_no_shift_exists(
        self, _backend_mock, lucian_slot_generator_mock
    ):
        deleted_staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        deleted_staging_shift.delete()
        staging_shift: StagingShift = StagingShiftFactory.create(
            ingestion_job=self.ingestion_job,
        )
        self.assertEqual(Shift.objects.count(), 0)
        promote_staging_schedule_data(ingestion_job_id=self.ingestion_job.pk)
        self.assertEqual(Shift.objects.count(), 1)
        shift_data: Optional[Shift] = Shift.objects.first()
        self.assertIsNotNone(shift_data)
        if shift_data:
            self.assertEqual(shift_data.day_of_week, staging_shift.day_of_week)
            self.assertEqual(shift_data.start_time, staging_shift.start_time)
            self.assertEqual(shift_data.stop_time, staging_shift.stop_time)
            self.assertEqual(shift_data.effective_period, staging_shift.effective_period)
            self.assertEqual(shift_data.schedule, staging_shift.schedule)
            self.assertEqual(shift_data.staging_shift, staging_shift)
        generate_slot_for_provider(
            provider_id=shift_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_data.start_time.strftime("%Y-%m-%d"),
            end_date=shift_data.stop_time.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=self.ingestion_job.id,
        )
        self.ingestion_job.refresh_from_db()
        self.assertEqual(self.ingestion_job.review_status, ReviewStatus.COMPLETED)
