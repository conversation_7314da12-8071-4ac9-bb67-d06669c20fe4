from typing import Optional

from dramatiq.rate_limits.backends import St<PERSON><PERSON><PERSON>end
from mock import patch

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.schedule.factories import (
    ScheduleIngestionJobFactory,
    ShiftExceptionFactory,
    StagingShiftExceptionFactory,
)
from firefly.modules.schedule.models import (
    Review<PERSON>tatus,
    ScheduleIngestionJob,
    ShiftException,
    StagingShiftException,
)
from firefly.modules.schedule.tasks import generate_slot_for_provider, promote_staging_schedule_data


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
class ScheduleExceptionPromotionTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_no_shift_exception_exists(self, _backend_mock, lucian_slot_generator_mock):
        staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        self.assertEqual(ShiftException.objects.count(), 0)
        promote_staging_schedule_data(ingestion_job_id=self.ingestion_job.pk)
        shift_exception_data: Optional[ShiftException] = ShiftException.objects.first()
        self.assertIsNotNone(shift_exception_data)
        if shift_exception_data:
            self.assertEqual(shift_exception_data.period, staging_shift_exception.period)
            self.assertEqual(shift_exception_data.schedule, staging_shift_exception.schedule)
            self.assertEqual(shift_exception_data.staging_exception, staging_shift_exception)
        generate_slot_for_provider(
            provider_id=shift_exception_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_exception_data.period.lower.strftime("%Y-%m-%d"),
            end_date=shift_exception_data.period.upper.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=self.ingestion_job.id,
        )
        self.ingestion_job.refresh_from_db()
        self.assertEqual(self.ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_no_staging_shift_exists(self, _backend_mock, lucian_slot_generator_mock):
        # Create a shift exception linked to a staging shift exception
        # This mimics that a shift exception exists from a previous ingestion job
        staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        linked_shift_exception: ShiftException = ShiftExceptionFactory.create(
            period=staging_shift_exception.period,
            schedule=staging_shift_exception.schedule,
            staging_exception=staging_shift_exception,
        )
        # Create an ingestion job where shift information has not changed
        # Expected data: All data on the shift should remain the same
        # with the exception of it being linked to the new staging shift entry
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()

        self.assertEqual(ShiftException.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(ShiftException.objects.count(), 1)
        linked_shift_exception.refresh_from_db()
        self.assertIsNone(linked_shift_exception.deleted)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_matching_shift_exception_exists(self, _backend_mock, lucian_slot_generator_mock):
        # Create a shift exception linked to a staging shift exception
        # This mimics that a shift exception exists from a previous ingestion job
        staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        linked_shift_exception: ShiftException = ShiftExceptionFactory.create(
            period=staging_shift_exception.period,
            schedule=staging_shift_exception.schedule,
            staging_exception=staging_shift_exception,
        )
        # Create an ingestion job where shift information has not changed
        # Expected data: All data on the shift should remain the same
        # with the exception of it being linked to the new staging shift entry
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()
        another_staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            period=staging_shift_exception.period,
            schedule=staging_shift_exception.schedule,
            ingestion_job=another_ingestion_job,
        )
        self.assertEqual(ShiftException.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(ShiftException.objects.count(), 1)
        linked_shift_exception.refresh_from_db()
        self.assertIsNone(linked_shift_exception.deleted)
        self.assertEqual(linked_shift_exception.period, another_staging_shift_exception.period)
        self.assertEqual(linked_shift_exception.schedule, another_staging_shift_exception.schedule)
        self.assertEqual(linked_shift_exception.staging_exception, another_staging_shift_exception)
        generate_slot_for_provider(
            provider_id=linked_shift_exception.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=linked_shift_exception.period.lower.strftime("%Y-%m-%d"),
            end_date=linked_shift_exception.period.upper.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=another_ingestion_job.id,
        )
        another_ingestion_job.refresh_from_db()
        self.assertEqual(another_ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion_when_non_matching_shift_exception_exists(
        self, _backend_mock, lucian_slot_generator_mock
    ):
        # Create a shift exception linked to a staging shift exception
        # This mimics that a shift exception exists from a previous ingestion job
        staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        unmatched_shift_exception: ShiftException = ShiftExceptionFactory.create(
            period=staging_shift_exception.period,
            schedule=staging_shift_exception.schedule,
            staging_exception=staging_shift_exception,
        )
        # Create an ingestion job where shift information has changed
        # Expected data: New shift data should be created and linked to the new staging shift entry
        another_ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()
        another_staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=another_ingestion_job,
        )
        self.assertEqual(ShiftException.objects.count(), 1)
        promote_staging_schedule_data(ingestion_job_id=another_ingestion_job.pk)
        self.assertEqual(ShiftException.objects.count(), 1)
        # Older shift data should be nuked
        unmatched_shift_exception.refresh_from_db()
        self.assertIsNotNone(unmatched_shift_exception.deleted)
        shift_exception_data: Optional[ShiftException] = ShiftException.objects.first()
        self.assertIsNotNone(shift_exception_data)
        if shift_exception_data:
            self.assertEqual(shift_exception_data.period, another_staging_shift_exception.period)
            self.assertEqual(shift_exception_data.schedule, another_staging_shift_exception.schedule)
            self.assertEqual(shift_exception_data.staging_exception, another_staging_shift_exception)
        generate_slot_for_provider(
            provider_id=shift_exception_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_exception_data.period.lower.strftime("%Y-%m-%d"),
            end_date=shift_exception_data.period.upper.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=another_ingestion_job.id,
        )
        another_ingestion_job.refresh_from_db()
        self.assertEqual(another_ingestion_job.review_status, ReviewStatus.COMPLETED)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    def test_schedule_promotion__with_deleted_staging_shift_when_no_shift_exists(
        self, _backend_mock, lucian_slot_generator_mock
    ):
        deleted_staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        deleted_staging_shift_exception.delete()
        staging_shift_exception: StagingShiftException = StagingShiftExceptionFactory.create(
            ingestion_job=self.ingestion_job,
        )
        promote_staging_schedule_data(ingestion_job_id=self.ingestion_job.pk)
        self.assertEqual(ShiftException.objects.count(), 1)
        shift_exception_data: Optional[ShiftException] = ShiftException.objects.first()
        self.assertIsNotNone(shift_exception_data)
        if shift_exception_data:
            self.assertEqual(shift_exception_data.period, staging_shift_exception.period)
            self.assertEqual(shift_exception_data.schedule, staging_shift_exception.schedule)
            self.assertEqual(shift_exception_data.staging_exception, staging_shift_exception)
        generate_slot_for_provider(
            provider_id=shift_exception_data.schedule.provider.user_id,
            log_prefix="Generate slot",
            start_date=shift_exception_data.period.lower.strftime("%Y-%m-%d"),
            end_date=shift_exception_data.period.upper.strftime("%Y-%m-%d"),
            dry_run_off=True,
            ingestion_job_id=self.ingestion_job.id,
        )
        self.ingestion_job.refresh_from_db()
        self.assertEqual(self.ingestion_job.review_status, ReviewStatus.COMPLETED)
