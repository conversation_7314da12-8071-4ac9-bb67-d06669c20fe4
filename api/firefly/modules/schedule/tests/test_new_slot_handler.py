from datetime import date, datetime, time, timedelta, timezone

from psycopg2.extras import Date<PERSON>ange

from firefly.core.feature.testutils import override_flag
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import ProviderDetailFactory
from firefly.modules.appointment.constants import AppointmentReason, AppointmentSource, AppointmentStatus, SlotType
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.tasks import update_custom_appointment_eventlog
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.schedule.constants import (
    WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5,
)
from firefly.modules.schedule.factories import (
    AppointmentSlotsFactory,
    ProviderScheduleFactory,
    ShiftFactory,
)
from firefly.modules.schedule.models import (
    Appointment,
    AppointmentType,
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    TimeSlot,
)
from firefly.modules.schedule.tasks import appointment_slot_generation, create_slots_for_provider_shift
from firefly.modules.schedule.utils.slot_handler import GetAvailableSlotsResponse, get_available_slots
from firefly.modules.tasks.tasks import NY_TIMEZONE


class SlotHandlerTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports
        from firefly.core.tests.utils import reset_context_to_luci_user

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.today = date(2024, 12, 18)
        cls.tomorrow = date(2024, 12, 19)
        cls.day_of_week = cls.today.isoweekday()
        cls.tomorrow_day_of_week = cls.tomorrow.isoweekday()

        # Create providers once for all tests
        cls.provider_for_test = ProviderDetailFactory.create()
        cls.provider_without_mapping = ProviderDetailFactory.create()

        # Create appointment types once for all tests
        cls.focused_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.FOCUSED_VISIT,
            unique_key=AppointmentReason.FOCUSED_VISIT,
            duration=15,
            buffer_time_in_minutes=15,
        )
        cls.video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            duration=30,
            buffer_time_in_minutes=15,
        )

        # Create physician appointment type mappings once for all tests
        PhysicianAppointmentTypeMapping.objects.create(
            physician=cls.provider_for_test.physician, appointment_type=cls.focused_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=cls.provider_for_test.physician, appointment_type=cls.video_appointment_type
        )

        # Create schedules once for all tests
        cls.schedule = ProviderScheduleFactory.create(
            provider=cls.provider_for_test,
            effective_period=DateRange(
                cls.today - timedelta(weeks=4),
                cls.today + timedelta(weeks=4),
            ),
        )
        cls.schedule_for_provider_without_mapping = ProviderScheduleFactory.create(
            provider=cls.provider_without_mapping,
            effective_period=DateRange(
                cls.today - timedelta(weeks=4),
                cls.today + timedelta(weeks=4),
            ),
        )

        # Create shifts once for all tests
        cls.shift_1 = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule,
            effective_period=DateRange(cls.today, cls.today + timedelta(days=4)),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:15:00", "%H:%M:%S"),
        )
        cls.shift_2 = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule_for_provider_without_mapping,
            effective_period=DateRange(cls.today, cls.today + timedelta(days=4)),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:15:00", "%H:%M:%S"),
        )
        cls.shift_3 = ShiftFactory.create(
            day_of_week=cls.tomorrow_day_of_week,
            schedule=cls.schedule,
            effective_period=DateRange(cls.today, cls.today + timedelta(days=4)),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:15:00", "%H:%M:%S"),
        )

    def setUp(self):
        super().setUp()
        # Use class-level objects for tests
        self.today = self.__class__.today
        self.tomorrow = self.__class__.tomorrow
        self.day_of_week = self.__class__.day_of_week
        self.tomorrow_day_of_week = self.__class__.tomorrow_day_of_week
        self.provider_for_test = self.__class__.provider_for_test
        self.provider_without_mapping = self.__class__.provider_without_mapping
        self.focused_appointment_type = self.__class__.focused_appointment_type
        self.video_appointment_type = self.__class__.video_appointment_type
        self.schedule = self.__class__.schedule
        self.shift = self.__class__.shift_1  # Default to first shift for backward compatibility

        # Create slots for each test (this is expensive but necessary for test isolation)
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.__class__.provider_for_test.user_id,  # Use the ProviderDetail's user_id
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=self.__class__.provider_for_test.user_id,  # Use the ProviderDetail's user_id
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            create_slots_for_provider_shift(
                provider_id=self.__class__.provider_without_mapping.user_id,  # Use the ProviderDetail's user_id
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=self.__class__.provider_without_mapping.user_id,  # Use the ProviderDetail's user_id
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )

    def test_get_available_slots_when_start_time_is_passed_from_middle_of_the_shift(self):
        # As shift starts from 10 am, send start time as 10:45 and 2 slots should be reduced from the list
        date_today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, 10, 45, 0, tzinfo=NY_TIMEZONE)
        # Scenerio 1: get available slots for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=1), time.min),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        # But as start time is passed from 10:45 - 10 and 10:30 slots should be ignored
        # total slots possible = 10
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 10)

        # Scenerio 2: get available slots for 2 days for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=2), time.min),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15 for both days
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12 * 2 = 24
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        # But 2 slots - 10 and 10:30 should be reduced on first day
        # Total possible slots should be 22
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 22)
        self.assertEqual(
            available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id),
            [
                datetime(2024, 12, 18, 16, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 16, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 17, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 17, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 18, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 18, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 15, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 15, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 16, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 16, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 17, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 17, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 18, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 18, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 19, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 19, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 20, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 20, 30, tzinfo=UTC_TIMEZONE),
            ],
        )
        # Scenerio 3: get available slots for video visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=1), time.min),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # But as the start time is sent from 10:45, 10:00 slot should not be sent as available
        # Total possible slots = 7
        # 10:45, 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 7)

        all_slots = TimeSlot.objects.all().order_by("period")
        # Scenerio 4: get available slots for for focused visit when appointment present
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(date_today.year, date_today.month, date_today.day, 10, 45, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            duration="00:30:00",
            physician=self.provider_for_test.physician,
        )
        # 10:45 slot is 4th time slot in the shift available and consumes 2 time slots
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[3])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # But as start time is passed as 10:45
        # Possible slots = 10
        # Video appointment exist: 10:45 - 11:30
        # 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=1), time.min),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 9)

        # Scenerio 5: get available slots for for focused visit after 2nd appointment booked
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(date_today.year, date_today.month, date_today.day, 11, 30, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=self.provider_for_test.physician,
        )
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[6])
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # But as the start time is passed as 10:45
        # Total possible slots = 10
        # appointment exist: 10:45 - 11:30 and 11:30 - 12
        # 12:00, 12:30
        # 13:00, 13:30, 14:00, 14:30, 15:00, 15:30
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=1), time.min),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 8)

        # Scenerio 6: get available slots for for focused visit when no mapping exists
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_without_mapping.user_id],
            start_date_time=date_time_today,
            end_date_time=datetime.combine(date_time_today + timedelta(days=1), time.min),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertIsNone(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id))

    def test_get_available_slots(self):
        today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, tzinfo=NY_TIMEZONE)
        # Scenerio 1: get available slots for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 12)

        # Scenerio 2: get available slots for 2 days for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=2),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15 for both days
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12 * 2 = 24
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 24)
        slots = available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 15, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 15, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 16, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 16, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 17, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 17, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 18, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 18, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 15, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 15, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 16, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 16, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 17, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 17, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 18, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 18, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 19, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 19, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 20, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 19, 20, 30, tzinfo=UTC_TIMEZONE),
            ],
        )
        # Scenerio 3: get available slots for for video visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # 10:00, 10:45, 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 8)

        all_slots = TimeSlot.objects.all().order_by("period")
        # Scenerio 4: get available slots for for focused visit when appointment present
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(today.year, today.month, today.day, 10, 0, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            duration="00:30:00",
            physician=self.provider_for_test.physician,
        )
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # appointment exist: 10 - 10:30
        # 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 10)

        # Scenerio 5: get available slots for for focused visit after 2nd appointment booked
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(today.year, today.month, today.day, 11, 0, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=self.provider_for_test.physician,
        )
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # appointment exist: 10 - 10:30 and 11:15 - 11:30
        # 11:30, 12:00, 12:30
        # 13:00, 13:30, 14:00, 14:30, 15:00, 15:30
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 9)
        # Scenerio 6: get available slots for for focused visit when no mapping exists
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_without_mapping.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        self.assertEqual(available_slots_respponse["provider_slot_map"], {})

    def test_get_available_slots_with_exclude_dates(self):
        today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, tzinfo=NY_TIMEZONE)
        # Scenerio 1: get available slots for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 12)

        # Scenerio 2: get available slots for focused visit with exclude date
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            exclude_dates=[today],
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # exclude date: today
        # 0 slot available
        self.assertEqual(available_slots_respponse["provider_slot_map"], {})

        # Scenerio 3: get available slots for 2 days for focused visit with exclude date
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=2),
            reason=AppointmentReason.FOCUSED_VISIT,
            exclude_dates=[today],
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30, 16:00
        # 1 day exclude 1 day to connsider
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 12)

    def test_get_available_slots_for_overlap_appointment(self):
        today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, tzinfo=NY_TIMEZONE)
        # Scenerio 1: get available slots for focused visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:00, 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 12)

        # Scenerio 2: get available slots for for video visit no appointment present
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # 10:00, 10:45, 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 8)

        all_slots = TimeSlot.objects.all().order_by("period")
        # Scenerio 3: get available slots for for video and focused visit when focused appointment present at 10
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(today.year, today.month, today.day, 10, 0, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=self.provider_for_test.physician,
        )
        appt_slot = AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # 10 - 10:15 appointment booked so possible slots
        # 10:45, 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 7)
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10 - 10:15 appointment booked so possible slots
        # 10:30, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 11)

        # Scenerio 4: get available slots for for video and focused visit when focused appointment present at 10:30
        appt.delete()
        appt_slot.delete()
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(today.year, today.month, today.day, 10, 30, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=self.provider_for_test.physician,
        )
        appt_slot = AppointmentSlotsFactory(appointment=appt, slot=all_slots[2])
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # 10:30 - 10:45 appointment booked so possible slots
        # 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 6)
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:30 - 10:45 appointment booked so possible slots
        # 10:00, 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 11)

        # Scenerio 5: get available slots for for video and focused visit when video appointment present at 10
        appt.delete()
        appt_slot.delete()
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(today.year, today.month, today.day, 10, 0, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            duration="00:30:00",
            physician=self.provider_for_test.physician,
        )
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        # shift 10 - 16:15
        # Video slots time: 30 mins buff time: 15 mins
        # possible slots: 6*60/45 = 8
        # 10:00 - 10:30 appointment booked so possible slots
        # 10:45, 11:30, 12:15, 13:00, 13:45, 14:30 15:15
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 7)
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[self.provider_for_test.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # shift 10 - 16:15
        # focused slots time: 15 mins buff time: 15 mins
        # possible slots: 6*60/30 = 12
        # 10:00 - 10:30 appointment booked so possible slots
        # 11:00, 11:30, 12:00, 12:30, 13:00
        # 13:30, 14:00, 14:30, 15:00, 15:30
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(self.provider_for_test.user_id)), 10)

    def test_scenerio_with_no_last_buff_shift(self):
        today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, tzinfo=NY_TIMEZONE)
        provider = ProviderDetailFactory.create()
        day_of_week = today.isoweekday()
        focused_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.FOCUSED_VISIT,
            unique_key=AppointmentReason.FOCUSED_VISIT,
            duration=15,
            buffer_time_in_minutes=15,
        )
        video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            duration=30,
            buffer_time_in_minutes=15,
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=focused_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=video_appointment_type
        )
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(
                today - timedelta(weeks=4),
                today + timedelta(weeks=4),
            ),
        )
        shift_1 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(today, today + timedelta(days=4)),
            start_time=datetime.strptime("8:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("11:15:00", "%H:%M:%S"),
        )
        shift_2 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(today, today + timedelta(days=4)),
            start_time=datetime.strptime("14:45:00", "%H:%M:%S"),
            stop_time=datetime.strptime("17:30:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=today.strftime("%Y-%m-%d"),
                end_date=(today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=today.strftime("%Y-%m-%d"),
                end_date=(today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        all_available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        slots = all_available_slots_respponse["provider_slot_map"].get(provider.user_id)
        # should not consider 17:15 - 17:30 slots as no buff present
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 13, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 14, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 14, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 15, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 15, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 45, tzinfo=UTC_TIMEZONE),
            ],
        )
        all_available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        slots = all_available_slots_respponse["provider_slot_map"].get(provider.user_id)
        # should not consider 15:45 - 16:15 slots
        # and 17:00 - 17:30 slots as no buff present
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 13, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 14, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 15, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 15, tzinfo=UTC_TIMEZONE),
            ],
        )
        appt = AppointmentFactory()
        all_slots = TimeSlot.objects.filter(shift=shift_1)
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[3])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[6])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[9])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[10])
        all_available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        slots = all_available_slots_respponse["provider_slot_map"].get(provider.user_id)
        # should not consider 8:30 - 11:15 slots as all appointments booked
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 19, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 45, tzinfo=UTC_TIMEZONE),
            ],
        )
        all_slots = TimeSlot.objects.filter(shift=shift_2)
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[3])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[6])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[7])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[9])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[10])
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.FOCUSED_VISIT,
            log_prefix="",
        )
        # should not consider any slots as all appointments booked
        self.assertEqual(available_slots_respponse["provider_slot_map"].get(provider.user_id), [])

    def test_visit_mix_margin(self):
        today = date(2024, 12, 18)
        date_time_today = datetime(2024, 12, 18, tzinfo=NY_TIMEZONE)
        provider = ProviderDetailFactory.create()
        day_of_week = today.isoweekday()
        awv_est_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.AWV_ESTABLISHED,
            unique_key=AppointmentReason.AWV_ESTABLISHED,
            duration=30,
            buffer_time_in_minutes=15,
        )
        video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            duration=30,
            buffer_time_in_minutes=15,
        )
        video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            duration=30,
            buffer_time_in_minutes=15,
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=awv_est_appointment_type
        )
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(
                today - timedelta(weeks=4),
                today + timedelta(weeks=4),
            ),
        )
        shift_1 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(today, today + timedelta(days=4)),
            start_time=datetime.strptime("13:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("18:30:00", "%H:%M:%S"),
        )
        video_type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider.physician, appointment_type=video_appointment_type
        )
        video_new_type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider.physician, appointment_type=video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=video_type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "100"},
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=video_new_type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "0"},
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=today.strftime("%Y-%m-%d"),
                end_date=(today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=today.strftime("%Y-%m-%d"),
                end_date=(today + timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        all_available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.AWV_ESTABLISHED,
            log_prefix="",
        )
        slots = all_available_slots_respponse["provider_slot_map"].get(provider.user_id)
        # should not consider 17:15 - 17:30 slots as no buff present
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 18, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 19, 15, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 0, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 20, 45, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 21, 30, tzinfo=UTC_TIMEZONE),
                datetime(2024, 12, 18, 22, 15, tzinfo=UTC_TIMEZONE),
            ],
        )
        appt = AppointmentFactory()
        all_slots = TimeSlot.objects.filter(shift=shift_1)
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[3])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[4])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[6])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[7])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[9])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[10])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[12])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[13])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[15])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[16])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[18])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[19])
        # should not consider custom appointments
        with self.captureOnCommitCallbacks(execute=True):
            custom_appt_1 = Appointment.objects.create(
                physician=provider.physician,
                reason=AppointmentReason.AWV_ESTABLISHED,
                start=datetime(2024, 12, 18, 17, 30, tzinfo=UTC_TIMEZONE),
                source=AppointmentSource.LUCIAN,
                time_slot_type=SlotType.APPOINTMENT,
                visible=True,
                duration=timedelta(minutes=30),
                status=AppointmentStatus.SCHEDULED.value,
                patient=self.patient,
            )
            custom_appt_2 = Appointment.objects.create(
                physician=provider.physician,
                reason=AppointmentReason.AWV_ESTABLISHED,
                start=datetime(2024, 12, 18, 16, 30, tzinfo=UTC_TIMEZONE),
                source=AppointmentSource.LUCIAN,
                time_slot_type=SlotType.APPOINTMENT,
                visible=True,
                duration=timedelta(minutes=30),
                status=AppointmentStatus.SCHEDULED.value,
                patient=self.patient,
            )
        update_custom_appointment_eventlog(
            appointment_id=custom_appt_1.id, custom_appointment_reasons=["test"], log_prefix=""
        )
        update_custom_appointment_eventlog(
            appointment_id=custom_appt_2.id, custom_appointment_reasons=["test"], log_prefix=""
        )
        all_available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=date_time_today,
            end_date_time=date_time_today + timedelta(days=1),
            reason=AppointmentReason.AWV_ESTABLISHED,
            log_prefix="",
        )
        slots = all_available_slots_respponse["provider_slot_map"].get(provider.user_id)
        # should not consider 15:45 - 16:15 slots
        # and 17:00 - 17:30 slots as no buff present
        self.assertEqual(
            slots,
            [
                datetime(2024, 12, 18, 18, 30, tzinfo=UTC_TIMEZONE),
            ],
        )

    def test_get_available_slots_query_count_with_visit_mix(self):
        today = date.today()
        days_ahead = 0 - today.weekday()  # Calculate the number of days until Monday (weekday() returns 0 for Monday)
        if days_ahead <= 0:  # If today is Monday, set it to the next Monday
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        day_of_week = next_monday.isoweekday()
        next_day_of_week = (next_monday + timedelta(days=1)).isoweekday()
        awv_est_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.AWV_ESTABLISHED,
            unique_key=AppointmentReason.AWV_ESTABLISHED,
            duration=30,
            buffer_time_in_minutes=15,
        )
        video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            duration=30,
            buffer_time_in_minutes=15,
        )
        video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            duration=30,
            buffer_time_in_minutes=15,
        )
        provider_ids = []
        for i in range(10):
            provider = ProviderDetailFactory.create()
            provider_ids.append(provider.user_id)
            PhysicianAppointmentTypeMapping.objects.create(
                physician=provider.physician, appointment_type=awv_est_appointment_type
            )
            schedule: ProviderSchedule = ProviderScheduleFactory.create(
                provider=provider,
                effective_period=DateRange(
                    next_monday - timedelta(weeks=4),
                    next_monday + timedelta(weeks=4),
                ),
            )
            ShiftFactory.create(
                day_of_week=day_of_week,
                schedule=schedule,
                effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
                start_time=datetime.strptime("13:30:00", "%H:%M:%S"),
                stop_time=datetime.strptime("18:30:00", "%H:%M:%S"),
            )
            ShiftFactory.create(
                day_of_week=next_day_of_week,
                schedule=schedule,
                effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
                start_time=datetime.strptime("13:30:00", "%H:%M:%S"),
                stop_time=datetime.strptime("18:30:00", "%H:%M:%S"),
            )
            video_type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=provider.physician, appointment_type=video_appointment_type
            )
            video_new_type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=provider.physician, appointment_type=video_new_appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=video_type_mapping,
                day_of_week=day_of_week,
                defaults={"percentage_of_slots": "100"},
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=video_new_type_mapping,
                day_of_week=day_of_week,
                defaults={"percentage_of_slots": "0"},
            )
            AppointmentFactory.create(
                patient=self.patient,
                start=datetime(today.year, today.month, today.day, 13, 30, 0, tzinfo=NY_TIMEZONE),
                reason=AppointmentReason.VIDEO,
                duration="00:30:00",
                physician=self.provider_for_test.physician,
            )

            with self.captureOnCommitCallbacks(execute=True):
                create_slots_for_provider_shift(
                    provider_id=provider.user_id,
                    log_prefix="",
                    start_date=next_monday.strftime("%Y-%m-%d"),
                    end_date=(next_monday + timedelta(days=4)).strftime("%Y-%m-%d"),
                    dry_run_off=True,
                )
                appointment_slot_generation(
                    provider_id=provider.user_id,
                    log_prefix="",
                    start_date=next_monday.strftime("%Y-%m-%d"),
                    end_date=(next_monday + timedelta(days=4)).strftime("%Y-%m-%d"),
                    dry_run_off=True,
                )
        """
            1. schedule_appointmenttype
            2. schedule_timeslot
            3. schedule_appointmentslots
            4. auth_user_provider_details
            5. auth_user
            6. physicians
            7. schedule_physicianvisitmixratio
            8. appointments
            9. schedule_providerschedule
        """
        next_monday_date_time = datetime(
            next_monday.year, next_monday.month, next_monday.day, 0, 0, 0, tzinfo=NY_TIMEZONE
        )
        with self.assertNumQueries(9):
            available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
                provider_ids=provider_ids,
                start_date_time=next_monday_date_time,
                end_date_time=next_monday_date_time + timedelta(days=4),
                reason=AppointmentReason.AWV_ESTABLISHED,
                log_prefix="",
            )
            self.assertEqual(len(available_slots_respponse["provider_slot_map"]), 10)

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=True)
    def test_get_available_slots_2_5(self):
        today = date.today()
        days_ahead = 0 - today.weekday()  # Calculate the number of days until Monday (weekday() returns 0 for Monday)
        if days_ahead <= 0:  # If today is Monday, set it to the next Monday
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        day_of_week = next_monday.isoweekday()
        video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            duration=30,
            buffer_time_in_minutes=15,
        )
        focused_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.FOCUSED_VISIT,
            unique_key=AppointmentReason.FOCUSED_VISIT,
            duration=15,
            buffer_time_in_minutes=15,
        )
        provider = ProviderDetailFactory.create()
        video_type_mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=focused_appointment_type
        )
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(
                next_monday - timedelta(weeks=4),
                next_monday + timedelta(weeks=4),
            ),
        )
        shift = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:15:00", "%H:%M:%S"),
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=video_type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "100"},
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=next_monday.strftime("%Y-%m-%d"),
                end_date=(next_monday + timedelta(days=4)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )

        next_monday_date_time = datetime(
            next_monday.year, next_monday.month, next_monday.day, 0, 0, 0, tzinfo=NY_TIMEZONE
        )
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=next_monday_date_time,
            end_date_time=next_monday_date_time + timedelta(days=4),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        self.assertEqual(
            available_slots_respponse["provider_slot_map"].get(provider.user_id),
            [
                datetime(next_monday.year, next_monday.month, next_monday.day, 10, 00, tzinfo=NY_TIMEZONE).astimezone(
                    timezone.utc
                ),
                datetime(next_monday.year, next_monday.month, next_monday.day, 10, 45, tzinfo=NY_TIMEZONE).astimezone(
                    timezone.utc
                ),
                datetime(next_monday.year, next_monday.month, next_monday.day, 11, 30, tzinfo=NY_TIMEZONE).astimezone(
                    timezone.utc
                ),
            ],
        )
        # book 10 AM EST Focused visit slot
        all_slots = TimeSlot.objects.filter(shift=shift).order_by("period")
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(next_monday.year, next_monday.month, next_monday.day, 15, 00, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=provider.physician,
        )
        appt = AppointmentFactory.create(
            patient=self.patient,
            start=datetime(next_monday.year, next_monday.month, next_monday.day, 15, 15, 0, tzinfo=NY_TIMEZONE),
            reason=AppointmentReason.FOCUSED_VISIT,
            duration="00:15:00",
            physician=provider.physician,
            source=AppointmentSource.ELATION,
        )
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        # slot should be available from 10:30 instead of 10:45 for video visit
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=next_monday_date_time,
            end_date_time=next_monday_date_time + timedelta(days=1),
            reason=AppointmentReason.VIDEO,
            log_prefix="",
        )
        self.assertEqual(
            available_slots_respponse["provider_slot_map"].get(provider.user_id),
            [
                datetime(next_monday.year, next_monday.month, next_monday.day, 10, 30, tzinfo=NY_TIMEZONE).astimezone(
                    timezone.utc
                ),
                datetime(next_monday.year, next_monday.month, next_monday.day, 11, 15, tzinfo=NY_TIMEZONE).astimezone(
                    timezone.utc
                ),
            ],
        )

    def test_bh_health(self):
        today = date.today()
        days_ahead = 0 - today.weekday()  # Calculate the number of days until Monday (weekday() returns 0 for Monday)
        if days_ahead <= 0:  # If today is Monday, set it to the next Monday
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        day_of_week = next_monday.isoweekday()
        hg_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            unique_key=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            duration=30,
            buffer_time_in_minutes=15,
        )
        provider = ProviderDetailFactory.create()
        health_guide__type_mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=hg_appointment_type
        )
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(
                next_monday - timedelta(weeks=4),
                next_monday + timedelta(weeks=4),
            ),
        )
        ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
            start_time=datetime.strptime("12:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("15:45:00", "%H:%M:%S"),
        )
        ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
            start_time=datetime.strptime("16:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("17:45:00", "%H:%M:%S"),
        )
        ShiftFactory.create(
            day_of_week=day_of_week + 1,
            schedule=schedule,
            effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
            start_time=datetime.strptime("10:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("11:45:00", "%H:%M:%S"),
        )
        ShiftFactory.create(
            day_of_week=day_of_week + 1,
            schedule=schedule,
            effective_period=DateRange(next_monday, next_monday + timedelta(days=4)),
            start_time=datetime.strptime("13:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:15:00", "%H:%M:%S"),
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=health_guide__type_mapping,
            day_of_week=day_of_week,
            defaults={"percentage_of_slots": "100"},
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=health_guide__type_mapping,
            day_of_week=day_of_week + 1,
            defaults={"percentage_of_slots": "100"},
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=next_monday.strftime("%Y-%m-%d"),
                end_date=(next_monday + timedelta(days=4)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=provider.user_id,
                log_prefix="",
                start_date=next_monday.strftime("%Y-%m-%d"),
                end_date=(next_monday + timedelta(days=4)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        next_monday_date_time = datetime(
            next_monday.year, next_monday.month, next_monday.day, 0, 0, 0, tzinfo=NY_TIMEZONE
        )
        available_slots_respponse: GetAvailableSlotsResponse = get_available_slots(
            provider_ids=[provider.user_id],
            start_date_time=next_monday_date_time,
            end_date_time=next_monday_date_time + timedelta(days=4),
            reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            log_prefix="",
        )
        self.assertEqual(len(available_slots_respponse["provider_slot_map"].get(provider.user_id)), 7)
