from datetime import datetime, timedelta

from dramatiq.rate_limits import ConcurrentRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from mock import patch
from psycopg2.extras import DateRange, DateTimeTZRange

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import ProviderDetailFactory
from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.constants import AppointmentSource, SlotType
from firefly.modules.appointment.models import Appointment
from firefly.modules.cases.models import CaseCategory
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.schedule.constants import CASE_CATEGORY_CANCELLED_APPT_BY_PROVIDER
from firefly.modules.schedule.factories import (
    ProviderScheduleFactory,
    ScheduleIngestionJobFactory,
    ShiftExceptionFactory,
    ShiftFactory,
    StagingShiftExceptionFactory,
    StagingShiftFactory,
)
from firefly.modules.schedule.models import (
    PhysicianAppointmentTypeMapping,
    ProviderSchedule,
    ScheduleIngestionJob,
    Shift,
    ShiftException,
    TimeSlot,
)
from firefly.modules.schedule.tasks import (
    create_slots_for_provider_shift,
    generate_slot_for_provider,
)
from firefly.modules.schedule.utils.data_ingestion import promote_staging_schedule_for_ingestion_job
from firefly.modules.statemachines.factories import DefaultStateMachineFactory


class ShiftExceptionSignalsTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_appointment_type
        )
        today = datetime.now()
        day_of_week = today.weekday() + 1
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )
        self.shift: Shift = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("9:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=self.provider.user_id,
            start_date=today.strftime("%Y-%m-%d"),
            end_date=today.strftime("%Y-%m-%d"),
            dry_run_off=True,
        )

    def _get_time_slots(self):
        return TimeSlot.objects.filter(shift=self.shift)

    def test_shift_exception_signal(self):
        now_10_am = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
        self.assertEqual(self._get_time_slots().count(), 12)
        # create partial day shift exception 10 - 12
        shift_exception_10_12: ShiftException = ShiftExceptionFactory.create(
            schedule=self.shift.schedule,
            period=DateTimeTZRange(now_10_am, now_10_am + timedelta(hours=2)),
        )
        # shold delete 10 - 12 timeslot
        # do not delete 9 - 10 timeslot
        self.assertEqual(self._get_time_slots().count(), 4)
        for timeslot in self._get_time_slots().iterator():
            self.assertTrue(timeslot.period.lower >= (now_10_am - timedelta(hours=1)).astimezone(UTC_TIMEZONE))
            self.assertTrue(timeslot.period.upper <= now_10_am.astimezone(UTC_TIMEZONE))

        # update shift exception to 9 - 11
        # create 9 - 11 exception and delte 10 - 12 exception
        shift_exception_9_11, _ = ShiftException.objects.update_or_create(
            schedule=self.shift.schedule,
            period=DateTimeTZRange(now_10_am - timedelta(hours=1), now_10_am + timedelta(hours=1)),
        )
        shift_exception_10_12.delete()
        # shold delete 9 - 11 timeslot
        # and undelete 11 - 12 timeslot
        self.assertEqual(self._get_time_slots().count(), 4)
        for timeslot in self._get_time_slots().iterator():
            self.assertTrue(timeslot.period.lower >= (now_10_am + timedelta(hours=1)).astimezone(UTC_TIMEZONE))
            self.assertTrue(timeslot.period.upper <= (now_10_am + timedelta(hours=2)).astimezone(UTC_TIMEZONE))
        shift_exception_9_11.delete()
        self.assertEqual(self._get_time_slots().count(), 12)
        # create full day shift exception
        ShiftExceptionFactory.create(
            schedule=self.shift.schedule,
            period=DateTimeTZRange(
                datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),
                datetime.now().replace(hour=23, minute=59, second=59, microsecond=0),
            ),
        )
        # should delete all timeslots for today
        self.assertEqual(self._get_time_slots().count(), 0)

    @patch("firefly.modules.schedule.tasks.lucian_slot_generator")
    @patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @patch.object(
        ConcurrentRateLimiter,
        "_acquire",
        return_value=True,
    )
    def test_shift_exception_slot_regeneration(self, _acquire_mock, _backend_mock, lucian_slot_generator_mock):
        provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test_Provider", last_name="Slots")
        # Mapping provider to Video and Video-New appointment types
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider.physician, appointment_type=self.video_new_appointment_type
        )
        today = datetime.now()
        day_of_week = today.weekday() + 1
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )
        ingestion_job: ScheduleIngestionJob = ScheduleIngestionJobFactory.create()
        StagingShiftFactory.create(
            ingestion_job=ingestion_job,
            schedule=schedule,
            day_of_week=day_of_week,
            start_time=datetime.strptime("17:45:00", "%H:%M:%S"),
            stop_time=datetime.strptime("20:30:00", "%H:%M:%S"),
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
        )
        shift: Shift = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("17:45:00", "%H:%M:%S"),
            stop_time=datetime.strptime("20:30:00", "%H:%M:%S"),
        )
        default_state_machine = DefaultStateMachineFactory.create()
        case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=CASE_CATEGORY_CANCELLED_APPT_BY_PROVIDER, state_machine_definition=default_state_machine
        )
        generate_slot_for_provider(
            provider_id=provider.user_id,
            log_prefix="Generate slot",
            start_date=today.strftime("%Y-%m-%d"),
            end_date=today.strftime("%Y-%m-%d"),
            dry_run_off=True,
        )
        # 17.45-18
        # 18-18.15
        # 18.15-18.30
        # 18.30-18.45
        # 18.45-19
        # 19-19.15
        # 19.15-19.30
        # 19.30-19.45
        # 19.45-20
        # 20-20.15
        # 20.15-20.30
        self.assertEqual(TimeSlot.objects.filter(shift=shift).count(), 11)
        # Video + Video-New
        # start time : 17.45 (2)
        # start time : 18.30 (2)
        # start time : 19.15 (2)
        # start time : 20 (2)
        self.assertEqual(
            Appointment.objects.filter(
                physician=provider.physician,
                time_slot_type=SlotType.APPOINTMENT_SLOT,
                visible=True,
                source=AppointmentSource.LUCIAN,
                status=None,
            ).count(),
            8,
        )
        now_17_45_pm = datetime.now().replace(hour=17, minute=45, second=0, microsecond=0)
        StagingShiftExceptionFactory.create(
            schedule=schedule,
            ingestion_job=ingestion_job,
            period=DateTimeTZRange(now_17_45_pm, now_17_45_pm + timedelta(minutes=30)),
        )
        promote_staging_schedule_for_ingestion_job(ingestion_job)
        generate_slot_for_provider(
            provider_id=provider.user_id,
            log_prefix="Generate slot",
            start_date=today.strftime("%Y-%m-%d"),
            end_date=today.strftime("%Y-%m-%d"),
            dry_run_off=True,
        )
        self.assertEqual(TimeSlot.objects.all().count(), 9)
        appt_slots = Appointment.objects.filter(
            physician=provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            visible=True,
            source=AppointmentSource.LUCIAN,
            status=None,
        )
        self.assertEqual(appt_slots.count(), 6)
