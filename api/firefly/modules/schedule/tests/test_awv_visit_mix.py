from datetime import datetime, timedelta

import mock
from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from psycopg2.extras import DateRange

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import ProviderDetailFactory
from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.constants import (
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.utils import apply_appointment_rules, apply_rules
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.schedule.factories import ProviderScheduleFactory, ShiftFactory
from firefly.modules.schedule.models import (
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
)
from firefly.modules.schedule.tasks import generate_slot_for_provider


@mock.patch("firefly.modules.appointment.signals.apply_appointment_rules_async.send_with_options")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
class TestAWVVisitMixWorkflow(FireflyTestCase):
    # This test will cover full end to end appointment booking and cancel workflow
    # while maintaining the visit mix ratios and deleting of slots

    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch.object(
        WindowRateLimiter,
        "_acquire",
        return_value=True,
    )
    def setUp(self, _acquire_mock, _mutex_mock):
        super().setUp()

        # create provider for test
        self.test_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")
        self.test_provider_without_short_visit: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="WithoutShortAppointment"
        )
        self.test_provider_without_awv_visit: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="WithoutAWVAppointment"
        )
        self.test_provider_with_only_established: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="AllEstablishedAppointments"
        )
        self.test_provider_with_only_new: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="AllNewAppointments"
        )

        # set date 3 days after as within 36 hours rule will not run
        self.three_days_after = datetime.now() + timedelta(days=3)
        self.three_days_after_str = self.three_days_after.strftime("%Y-%m-%d")
        self.day_of_week = self.three_days_after.isoweekday()
        self.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")

        # Set up a provider supporting all types of visits
        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
            self.focused_visit_type,
            self.awv_established_visit_type,
            self.awv_new_visit_type,
        ]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Setup mapping for a provider without short visit mapping
        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
            self.awv_established_visit_type,
        ]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_without_short_visit.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Setup mapping for a provider without awv visit mapping
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_without_awv_visit.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Setup mapping for a provider with only Established visit types [Video, AWV Established, Focused]
        for appointment_type in [self.video_appointment_type, self.focused_visit_type, self.awv_established_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_with_only_established.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "100"},
            )

        # Setup mapping for a provider with only New visit types [Video-New, AWV New]
        for appointment_type in [self.video_new_appointment_type, self.awv_new_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_with_only_new.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "100"},
            )

        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9 - 9:30 : awv new slot
        # 9 - 9:30 : awv established slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 22)

        # create provider schedule
        self.schedule_without_short_visit: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider_without_short_visit,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift_without_short_visit = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_without_short_visit,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_without_short_visit.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9 - 9:30 : awv new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 12)

        # create provider schedule
        self.schedule_without_awv_visit: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider_without_awv_visit,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift_without_awv_visit = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_without_awv_visit,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_without_awv_visit.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_awv_visit.physician), 14)

        # create provider schedule
        self.schedule_with_only_established: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider_with_only_established,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift_with_only_established = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_only_established,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_with_only_established.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : awv established slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 14)

        # create provider schedule
        self.schedule_with_only_new: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider_with_only_new,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift_with_only_new = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_only_new,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_with_only_new.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video-new slot
        # 9 - 9:30 : awv new slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_new.physician), 8)

    def test_booking_video_new_slots(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 video-new visits deletes the rest of video-new slots and all awv new slots
        # After that if we book a focused and then cancel it
        # it should only regenerate focused and video slots
        # Video-new slots should remain deleted
        # After that if we cancel Video-New appointment, it should regenerate Video-New and AWV New slots

        # Book a Video-New visit
        # Before rule there was 4 Video, 4 Video new, 4 AWV new and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO_NEW_PATIENT, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new, 4 AWV New, 4 AWV Established, 4 Focused slots
        # and 1 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

        # Book another Video-New visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO_NEW_PATIENT, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 2 Video, 0 Video-new slots, 0 AWV New, 4 AWV Established,  3 Focused slots
        # and 2 Video-new appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 9:45 - 10:15 : awv new slot (deleted)
        # 9:45 - 10:15 : awv established slot (deleted)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot (deleted)
        # 10:30 - 11:00 : awv new slot (deleted)
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 11:15 - 11:45 : awv new slot (deleted)
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 7)

        # book a focused appointment
        focused_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        focused_appointment.patient = self.patient
        focused_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            focused_appointment.save()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_appointment)

        # After rule there should be 1 Video, 0 Video-new slots, 0 AWV New, 1 AWV Established
        # 2 Focused slots and 2 Video-new, 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 9:45 - 10:15 : awv new slot (deleted)
        # 9:45 - 10:15 : awv established slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 10:30 - 11:00 : awv new slot (deleted)
        # 10:30 - 11:00 : awv established slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 11:15 - 11:45 : awv new slot (deleted)
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 4)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            focused_appointment.cancel()
        focused_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_appointment)
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=focused_appointment)
        focused_appointment.refresh_from_db()
        self.assertIsNone(focused_appointment.deleted)
        self.assertIsNotNone(focused_appointment.physician)

        # After rule there should be 2 Video, 0 Video-new slots, 0 AWV New, 2 AWV Established, 3 Focused slots and
        # 2 Video-new appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 9:45 - 10:15 : awv new slot (deleted)
        # 9:45 - 10:15 : awv established slot (deleted)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot (deleted)
        # 10:30 - 11:00 : awv new slot (deleted)
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 11:15 - 11:45 : awv new slot (deleted)
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 7)

        # cancel video-new appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots, 3 AWV New, 3 AWV Established, 4 Focused slots
        # and 1 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

    def test_booking_awv_slots(self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock):
        # Scenario:
        # Booking 2 awv established visits deletes the rest of video, focused and awv established slots

        # Book AWV Established visit
        # Before rule there were 4 Video, 4 AWV Established, 4 Video new, 4 AWV New and 6 focused slots
        # and 0 appointments

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.AWV_ESTABLISHED, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 AWV Established, 3 Video-new slots, 3 AWV New, 4 Focused slots
        # and 1 AWV Established appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

        # Book another AWV Established visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.AWV_ESTABLISHED, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 0 AWV Established, 2 Video-new slots, 2 AWV New, 0 Focused slots
        # and 2 AWV Established appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : awv new slot (deleted)
        # 9:45 - 10:15 : awv established slot (BOOKED)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot (deleted)
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot (deleted)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            6,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 4)

        # cancel AWV Estbalished appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 AWV Established, 3 Video-new slots, 3 AWV New, 4 Focused slots
        # and 1 AWV Established appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

    def test_booking_video_slots_for_provider_without_short_visits_with_awv_flag_enabled(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Situation when we selectively enable focused visits for few providers, but enable AWV for all
        # Scenario:
        # Booking 2 video visits deletes the rest of video and awv established slots

        # Book a Video visit
        # Before rule there were 4 Video, 4 AWV established, 4 Video new, 4 AWV new slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_short_visit
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 AWV Established, 3 Video-new slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv established slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 9)

        # Book another Video visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_short_visit
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 0 AWV Established, 2 Video-new slots and 2 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : awv established slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv established slot (deleted)
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv established slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 2)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 AWV Established, 3 Video-new slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (deleted)
        # 9 - 9:30 : awv established slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 9:45 - 10:15 : awv established slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 10:30 - 11:00 : awv established slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        # 11:15 - 11:45 : awv established slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 9)

    def test_booking_video_slots_for_provider_without_awv_visits_with_both_flags_enabled(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Situation when we enable only focused flag to a provider without awv mapping
        # Scenario:
        # Booking 2 video visits deletes the rest of video and focused slots

        # Book a Video visit
        # Before rule there was 4 Video, 6 Focused, 4 Video new slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_awv_visit
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new, 4 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_awv_visit.physician), 10)

        # Book another Video visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_awv_visit
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 2 Video-new slots, 0 Focused and 2 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            6,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_awv_visit.physician), 2)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new, 4 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_awv_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_awv_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_awv_visit.physician), 10)

    def test_booking_awv_slots_for_provider_with_only_new_visits(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 AWV New visits does not delete the rest of Video New and AWV New slots

        # Book AWV New visit
        # Before rule there was 4 Video-New and 4 AWV New and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider_with_only_new
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video-New, 3 AWV New slots and 1 AWV New appointment
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_new.physician), 6)

        # Book another AWV New visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider_with_only_new
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 2 Video-New, 2 AWV New slots and 2 AWV New appointment
        # as per 100% capacity
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_new.physician), 4)

        # Book another AWV New visit
        third_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider_with_only_new
        )
        third_appointment.patient = self.patient
        third_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            third_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=third_appointment)

        # After rule there should be 1 Video-New, 1 AWV New slots and 3 AWV New appointment
        # as per 100% capacity
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 10:30 - 11:00 : awv new slot (BOOKED)
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            3,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_new.physician), 2)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            third_appointment.cancel()
        third_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=third_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=third_appointment)
        third_appointment.refresh_from_db()
        self.assertIsNone(third_appointment.deleted)
        self.assertIsNotNone(third_appointment.physician)

        # After rule there should be 2 Video-New, 2 AWV New slots and 2 AWV New appointment
        # as per 100% capacity
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_new.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider_with_only_new.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_new.physician), 4)

    def _get_slots_count(self, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=True,
        ).count()

    def _get_deleted_slots_count_by_reason(self, reason, physician):
        return Appointment.deleted_objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=True,
        ).count()

    def _get_booked_appointment_count_by_reason(self, reason, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=False,
        ).count()

    def _get_first_slot_by_reason(self, reason: AppointmentReason, provider: ProviderDetail):
        return (
            Appointment.objects.filter(
                physician=provider.physician,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("start")
            .first()
        )


@mock.patch("firefly.modules.appointment.signals.apply_appointment_rules_async.send_with_options")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
class TestAWVVisitMixCapacity(FireflyTestCase):
    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch.object(
        WindowRateLimiter,
        "_acquire",
        return_value=True,
    )
    def setUp(self, _acquire_mock, _mutex_mock):
        super().setUp()

        # create provider for test
        self.test_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")

        # set date 3 days after as within 36 hours rule will not run
        self.three_days_after = datetime.now() + timedelta(days=3)
        self.three_days_after_str = self.three_days_after.strftime("%Y-%m-%d")
        self.day_of_week = self.three_days_after.isoweekday()
        self.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")

        # Create mapping for awv new visit, without configuring visit mix ratio
        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.awv_new_visit_type
        )

        # Configure visit mix ratio of New vs Est to be 75% vs 25%
        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.day_of_week,
            defaults={"percentage_of_slots": "75"},
        )

        # Create mapping for awv established visit, without configuring visit mix ratio
        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.awv_established_visit_type
        )

        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.day_of_week,
            defaults={"percentage_of_slots": "25"},
        )

        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:30 : est awv slot
        # 9 - 9:30 : awv new slot
        # 9:45 - 10:15 : est awv slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : est awv slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

    def test_applying_rule_to_new_awv_takes_video_new_visit_mix(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Total awv new appointments possible for the day = 4
        # 75% * (Total awv new appointments possible for the day) = 3
        # Booking 3 awv new visits deletes the rest of awv new and video new, because awv new visits
        # consider visit mix % of parent type "Video-New" which is set to 75%

        # Book awv new visit
        # Before rule there were 4 Video, 4 Video new, 4 AWV New, 4 AWV Established and 0 appointments

        first_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots, 3 AWV New, 3 AWV Established slots
        # and 1 AWV New appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : est awv slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : est awv slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : est awv slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 12)

        # Book another AWV New visit
        second_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 2 Video, 2 Video-new slots, 2 AWV New, 2 AWV Established slots
        # and 2 AWV New appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : est awv slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : est awv slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : est awv slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 8)

        # Book another AWV New visit
        third_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.AWV_NEW, self.test_provider
        )
        third_appointment.patient = self.patient
        third_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            third_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=third_appointment)

        # After rule there should be 1 Video, 0 Video-new slots, 0 AWV New, 1 AWV Established slots
        # and 3 AWV New appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : est awv slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : est awv slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 10:30 - 11:00 : est awv slot (deleted)
        # 10:30 - 11:00 : awv new slot (BOOKED)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 2)

        # cancel awv new appointment, and all the new visit slots (awv-new and video-new) should be released
        with self.captureOnCommitCallbacks(execute=True):
            third_appointment.cancel()
        third_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=third_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=third_appointment)
        third_appointment.refresh_from_db()
        self.assertIsNone(third_appointment.deleted)
        self.assertIsNotNone(third_appointment.physician)

        # After rule there should be 2 Video, 2 Video-new slots, 2 AWV New, 2 AWV Established slots
        # and 2 AWV New appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : est awv slot (deleted)
        # 9 - 9:30 : awv new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 9:45 - 10:15 : est awv slot (deleted)
        # 9:45 - 10:15 : awv new slot (BOOKED)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : est awv slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 8)

    def test_applying_rule_to_est_awv_takes_video_visit_mix(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Total awv est appointments possible for the day = 4
        # 25% * (Total awv est appointments possible for the day) = 1
        # Booking 1 awv est visit deletes the rest of awv est and video slots, because awv est visits
        # consider visit mix % of parent type "Video" which is set to 25%

        # Book awv new visit
        # Before rule there were 4 Video, 4 Video new, 4 AWV New, 4 AWV Established and 0 appointments

        first_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.AWV_ESTABLISHED, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 0 Video, 3 Video-new slots, 3 AWV New, 0 AWV Established slots
        # and 1 AWV Established appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9 - 9:30 : est awv slot (BOOKED)
        # 9 - 9:30 : awv new slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : est awv slot (deleted)
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : est awv slot (deleted)
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : est awv slot (deleted)
        # 11:15 - 11:45 : awv new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 6)

        # cancel awv est appointment, and all the est visit slots (awv-est and video) should be released
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.cancel()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=first_appointment)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)

        # After rule there should be 4 Video, 4 Video-new slots, 4 AWV New, 4 AWV Established slots
        # and 2 AWV New appointments
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9 - 9:30 : est awv slot
        # 9 - 9:30 : awv new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 9:45 - 10:15 : est awv slot
        # 9:45 - 10:15 : awv new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 10:30 - 11:00 : est awv slot
        # 10:30 - 11:00 : awv new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 11:15 - 11:45 : est awv slot
        # 11:15 - 11:45 : awv new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_ESTABLISHED, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.AWV_NEW, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 16)

    def _get_slots_count(self, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=True,
        ).count()

    def _get_deleted_slots_count_by_reason(self, reason, physician):
        return Appointment.deleted_objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=True,
        ).count()

    def _get_booked_appointment_count_by_reason(self, reason, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=False,
        ).count()

    def _get_first_unbooked_slot_by_reason(self, reason: AppointmentReason, provider: ProviderDetail):
        return (
            Appointment.objects.filter(
                physician=provider.physician,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("start")
            .first()
        )
