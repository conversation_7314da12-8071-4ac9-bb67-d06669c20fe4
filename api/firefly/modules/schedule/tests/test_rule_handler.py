from datetime import datetime, time, timedelta
from typing import Dict, List, Union
from unittest.mock import ANY

import mock
from django.conf import settings
from django.db.models.query import QuerySet
from django.test import override_settings
from django.utils import timezone
from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from faker import Faker
from psycopg2.extras import DateRange, DateTimeTZRange

from firefly.core.feature.testutils import override_flag
from firefly.core.services.elation.client.client import ElationClient
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PatientUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import Person, ProviderDetail
from firefly.modules.appointment.constants import (
    OLD_MEMBER_DATE,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.tasks import publish_appointment_to_elation_async
from firefly.modules.appointment.utils import apply_appointment_rules, apply_rules
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.schedule.constants import WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5
from firefly.modules.schedule.factories import (
    ProviderScheduleFactory,
    ShiftExceptionFactory,
    ShiftFactory,
)
from firefly.modules.schedule.models import (
    AppointmentType,
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    ShiftException,
    TimeSlot,
)
from firefly.modules.schedule.tasks import generate_slot_for_provider
from firefly.modules.schedule.utils.rule_handler import (
    _apply_visit_mix_ratio,
    apply_rules_for_timeslot,
)


def configure_visit_mix_for_appointment(appointment: Appointment):
    appointment_type = AppointmentType.objects.get(unique_key=appointment.reason)
    type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
        physician=appointment.physician, appointment_type=appointment_type
    )
    return PhysicianVisitMixRatio.objects.get_or_create(
        physician_appointment_type=type_mapping,
        day_of_week=appointment.start.isoweekday(),
        defaults={"percentage_of_slots": 50},
    )


@override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True))
@mock.patch.object(ElationClient, "delete_record")
@mock.patch.object(ElationClient, "create_record")
@mock.patch.object(ElationClient, "init_session")
@mock.patch("firefly.modules.appointment.slots.api.submit_event_async")
@mock.patch("firefly.modules.appointment.signals.apply_appointment_rules_async")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch.object(
    WindowRateLimiter,
    "_acquire",
    return_value=True,
)
class TestBookAppointmentCancelWorkflow(FireflyTestCase):
    # This test will cover full end to end appointment booking and cancel workflow
    # TODO: add more scenerio to cover different workflow
    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch.object(
        WindowRateLimiter,
        "_acquire",
        return_value=True,
    )
    def setUp(self, _acquire_mock, _mutex_mock):
        super().setUp()
        # create provider for test
        self.provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )
        # set date 3 days after as within 36 hours rule will not run
        self.three_days_after = datetime.now() + timedelta(days=3)
        self.three_days_after_str = self.three_days_after.strftime("%Y-%m-%d")
        self.day_of_week = self.three_days_after.isoweekday()
        self.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 1 PM
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )
        time_at_12_noon = (
            datetime.combine(self.three_days_after.date(), time(hour=12))
            .replace(tzinfo=NY_TIMEZONE)
            .astimezone(UTC_TIMEZONE)
        )
        # A shift exception from 12 noon to 1pm to test proper logic
        ShiftExceptionFactory.create(
            schedule=self.schedule,
            period=DateTimeTZRange(
                time_at_12_noon,
                time_at_12_noon + timedelta(hours=1),
            ),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # create provider for testing short visits
        self.provider_with_short_visits: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="Short Appointment"
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_with_short_visits.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_with_short_visits.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_with_short_visits.physician, appointment_type=self.focused_visit_type
        )
        # create provider schedule
        self.schedule_with_short_visits: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 1 PM
        self.shift_with_short_visits = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )
        # A shift exception from 12 noon to 1pm to test proper logic
        ShiftExceptionFactory.create(
            schedule=self.schedule_with_short_visits,
            period=DateTimeTZRange(
                time_at_12_noon,
                time_at_12_noon + timedelta(hours=1),
            ),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # create user for appointment book
        patient = PatientUserFactory.create()
        Person.objects.create(user=patient)
        patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - timedelta(days=1)
        patient.onboarding_state.save()
        self.client.force_authenticate(user=patient)
        patient.person.elation_id = Faker().pyint()
        patient.person.save()
        self.patient.person.elation_id = Faker().pyint()
        self.patient.person.save()

    def test_generated_time_and_appointment_slots(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        actual_slots = []
        slots: QuerySet[TimeSlot] = TimeSlot.objects.all().order_by("period")
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # Time slots should be generated for 2 providers defined in setup->self.provider,self.provider_with_short_visits
        expected_slots_for_provider = [
            "09:00:00",
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:15:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
        ]
        expected_slots_for_provider_with_short_visits = [
            "09:00:00",
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:15:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
        ]
        total_expected_slots = sorted(expected_slots_for_provider + expected_slots_for_provider_with_short_visits)
        self.assertEqual(actual_slots, total_expected_slots)
        appointments = Appointment.objects.filter(physician=self.provider.physician)
        appointments_for_provider_with_short_visits = Appointment.objects.filter(
            physician=self.provider_with_short_visits.physician
        )
        actual_appointments = []
        for appointment in appointments.iterator():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        for appointment in appointments_for_provider_with_short_visits.iterator():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # two slots for each time (one for video; another for video new)
        expected_appointments_for_provider = [
            "09:00:00",
            "09:00:00",
            "09:45:00",
            "09:45:00",
            "10:30:00",
            "10:30:00",
            "11:15:00",
            "11:15:00",
        ]
        expected_appointments_for_provider_with_short_visits = [
            "09:00:00",
            "09:00:00",
            "09:45:00",
            "09:45:00",
            "10:30:00",
            "10:30:00",
            "11:15:00",
            "11:15:00",
            "09:00:00",  # Focused visits
            "09:30:00",
            "10:00:00",
            "10:30:00",
            "11:00:00",
            "11:30:00",
        ]
        total_expected_appointments = sorted(
            expected_appointments_for_provider_with_short_visits + expected_appointments_for_provider
        )
        self.assertEqual(sorted(actual_appointments), total_expected_appointments)
        # update shift to be from 9:15 AM to  end time 12 PM
        self.shift.delete()
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_slots = []
        slots: QuerySet[TimeSlot] = TimeSlot.objects.all().order_by("period")
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        expected_slots = [
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:15:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
        ]
        total_expected_slots = sorted(expected_slots + expected_slots_for_provider_with_short_visits)
        self.assertEqual(actual_slots, total_expected_slots)
        actual_appointments = []
        for appointment in appointments.iterator():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # two slots for each time (one for video; another for video new)
        expected_appointments = [
            "09:15:00",
            "09:15:00",
            "10:00:00",
            "10:00:00",
            "10:45:00",
            "10:45:00",
            "11:30:00",
            "11:30:00",
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

    def test_generated_time_and_appointment_slots_with_booked_appointment(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
        )
        appointments = Appointment.objects.filter(physician=self.provider.physician).order_by("start")
        # book the 9 AM slot
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{first_appointment.id}", format="json", data={})
        self.shift.delete()
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        expected_appointments = [
            "09:00:00",  # booked appointment
            # remainder of expected appointments based on shift
            "10:00:00",
            "10:00:00",
            "10:45:00",
            "10:45:00",
            "11:30:00",
            "11:30:00",
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        first_appointment.cancel()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # Should release all slots in the new shift created
        expected_appointments = [
            "09:15:00",
            "09:15:00",
            "10:00:00",
            "10:00:00",
            "10:45:00",
            "10:45:00",
            "11:30:00",
            "11:30:00",
        ]
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        self.assertEqual(sorted(actual_appointments), expected_appointments)

    def test_generated_time_and_appointment_slots_with_booked_appointment_with_multiple_overlap(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
        last_appointment: Appointment = self._get_last_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
        )
        appointments = Appointment.objects.filter(physician=self.provider.physician).order_by("start")
        # book the 11:15 AM slot
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{last_appointment.id}", format="json", data={})
        self.shift.delete()
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        expected_appointments = [
            "09:15:00",
            "09:15:00",
            "10:00:00",
            "10:00:00",
            # "10:45:00", deleted
            # "10:45:00", deleted
            "11:15:00",  # booked appointment
            # "11:30:00",  not created
            # "11:30:00",  not created
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        last_appointment.cancel()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=last_appointment)
        # Should release all slots in the new shift created
        expected_appointments = [
            "09:15:00",
            "09:15:00",
            "10:00:00",
            "10:00:00",
            "10:45:00",
            "10:45:00",
            "11:30:00",
            "11:30:00",
        ]
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        self.assertEqual(sorted(actual_appointments), expected_appointments)

    def test_generated_time_and_appointment_slots_with_booked_appointment_with_short_visits(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_with_short_visits.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
        appointments = Appointment.objects.filter(physician=self.provider_with_short_visits.physician).order_by("start")
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        expected_appointments = [
            ["09:00:00", "Focused"],
            ["09:00:00", "Video"],
            ["09:00:00", "Video-New"],
            ["09:30:00", "Focused"],
            ["09:45:00", "Video"],
            ["09:45:00", "Video-New"],
            ["10:00:00", "Focused"],
            ["10:30:00", "Focused"],
            ["10:30:00", "Video"],
            ["10:30:00", "Video-New"],
            ["11:00:00", "Focused"],
            ["11:15:00", "Video"],
            ["11:15:00", "Video-New"],
            ["11:30:00", "Focused"],
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
        )

        # book the 9 AM Video-New slot
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{first_appointment.id}", format="json", data={})
        self.shift_with_short_visits.delete()
        # Update the shift to start from 9:15
        shift_at_9_15 = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_appointments = []
        appointments = Appointment.objects.filter(physician=self.provider_with_short_visits.physician).order_by("start")
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        expected_appointments = [
            ["09:00:00", "Video-New"],  # booked appointment
            # Remainder of expected appointments based on new shift
            # Timeslots linked to 9:15 Video, 9:15 Video-New, 9:15 Focused shouldn't be available for other appointments
            ["09:45:00", "Focused"],
            ["10:00:00", "Video"],
            ["10:00:00", "Video-New"],
            ["10:15:00", "Focused"],
            ["10:45:00", "Focused"],
            ["10:45:00", "Video"],
            ["10:45:00", "Video-New"],
            ["11:15:00", "Focused"],
            ["11:30:00", "Video"],
            ["11:30:00", "Video-New"],
            ["11:45:00", "Focused"],
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        first_appointment.cancel()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # Should release all slots in the new shift created
        expected_appointments = [
            ["09:15:00", "Focused"],
            ["09:15:00", "Video"],
            ["09:15:00", "Video-New"],
            ["09:45:00", "Focused"],
            ["10:00:00", "Video"],
            ["10:00:00", "Video-New"],
            ["10:15:00", "Focused"],
            ["10:45:00", "Focused"],
            ["10:45:00", "Video"],
            ["10:45:00", "Video-New"],
            ["11:15:00", "Focused"],
            ["11:30:00", "Video"],
            ["11:30:00", "Video-New"],
            ["11:45:00", "Focused"],
        ]
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        # Book a focused visit
        first_focused_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{first_focused_appointment.id}", format="json", data={})
        shift_at_9_15.delete()

        # Update the shift to start from 9:30
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_appointments = []
        appointments = Appointment.objects.filter(physician=self.provider_with_short_visits.physician).order_by("start")
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        expected_appointments = [
            ["09:15:00", "Focused"],  # booked appointment
            # Remainder of expected appointments based on new shift
            # Timeslots linked to 9:30 Video, 9:30 Video-New, 9:30 Focused shouldn't be available for other appointments
            ["10:00:00", "Focused"],
            ["10:15:00", "Video"],
            ["10:15:00", "Video-New"],
            ["10:30:00", "Focused"],
            ["11:00:00", "Focused"],
            ["11:00:00", "Video"],
            ["11:00:00", "Video-New"],
            ["11:30:00", "Focused"],
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        first_focused_appointment.cancel()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_focused_appointment)
        # Should release all slots in the new shift created
        expected_appointments = [
            ["09:30:00", "Focused"],
            ["09:30:00", "Video"],
            ["09:30:00", "Video-New"],
            ["10:00:00", "Focused"],
            ["10:15:00", "Video"],
            ["10:15:00", "Video-New"],
            ["10:30:00", "Focused"],
            ["11:00:00", "Focused"],
            ["11:00:00", "Video"],
            ["11:00:00", "Video-New"],
            ["11:30:00", "Focused"],
        ]
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        self.assertEqual(sorted(actual_appointments), expected_appointments)

    def test_generated_time_and_appointment_slots_with_booked_appointment_with_short_visits_multiple_overlap(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_with_short_visits.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
        appointments = Appointment.objects.filter(physician=self.provider_with_short_visits.physician).order_by("start")
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        expected_appointments = [
            ["09:00:00", "Focused"],
            ["09:00:00", "Video"],
            ["09:00:00", "Video-New"],
            ["09:30:00", "Focused"],
            ["09:45:00", "Video"],
            ["09:45:00", "Video-New"],
            ["10:00:00", "Focused"],
            ["10:30:00", "Focused"],
            ["10:30:00", "Video"],
            ["10:30:00", "Video-New"],
            ["11:00:00", "Focused"],
            ["11:15:00", "Video"],
            ["11:15:00", "Video-New"],
            ["11:30:00", "Focused"],
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)
        appt_at_11_15: Appointment = self._get_last_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
        )

        # book the 11:15 AM Video-New slot
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{appt_at_11_15.id}", format="json", data={})
        self.shift_with_short_visits.delete()
        # Update the shift to start from 9:15
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_appointments = []
        appointments = Appointment.objects.filter(physician=self.provider_with_short_visits.physician).order_by("start")
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        expected_appointments = [
            ["09:15:00", "Focused"],
            ["09:15:00", "Video"],
            ["09:15:00", "Video-New"],
            ["09:45:00", "Focused"],
            ["10:00:00", "Video"],
            ["10:00:00", "Video-New"],
            ["10:15:00", "Focused"],
            ["10:45:00", "Focused"],
            # ["10:45:00", "Video"], deleted
            # ["10:45:00", "Video-New"], deleted
            ["11:15:00", "Video-New"],  # booked appointment
            # ["11:15:00", "Focused"],  not created
            # ["11:30:00", "Video"],  not created
            # ["11:30:00", "Video-New"],  not created
            # ["11:45:00", "Focused"], deleted
        ]
        self.assertEqual(sorted(actual_appointments), expected_appointments)

        appt_at_11_15.cancel()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appt_at_11_15)
        # Should release all slots in the new shift created at the correct time
        expected_appointments = [
            ["09:15:00", "Focused"],
            ["09:15:00", "Video"],
            ["09:15:00", "Video-New"],
            ["09:45:00", "Focused"],
            ["10:00:00", "Video"],
            ["10:00:00", "Video-New"],
            ["10:15:00", "Focused"],
            ["10:45:00", "Focused"],
            ["10:45:00", "Video"],
            ["10:45:00", "Video-New"],
            ["11:15:00", "Focused"],
            ["11:30:00", "Video"],
            ["11:30:00", "Video-New"],
            ["11:45:00", "Focused"],
        ]
        actual_appointments = []
        for appointment in appointments.all():
            actual_appointments.append(
                [
                    appointment.start.astimezone(self.schedule_with_short_visits.timezone).strftime("%H:%M:%S"),
                    appointment.reason,
                ]
            )
        self.assertEqual(sorted(actual_appointments), expected_appointments)

    def test_book_cancel_workflow(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        # Detailed view of book and cancel workflows - https://docs.google.com/spreadsheets/d/11SJdA-hdbu9eAsIoqcjqbWApTEBhTxkM7NLcvdE2ACA/edit?gid=0#gid=0&range=A1
        ElationAppointmentSync().connect_model_listener()
        slots: List[TimeSlot] = TimeSlot.objects.all()
        self.assertEqual(slots.count(), 24)
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 8)
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Book first video-new slot
        # Get first video new visit
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
        )
        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        # Before rule there was 4 Video and 4 Video new slots and 0 appointment
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        create_record_mock.return_value = {"id": "1234567"}
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_appointment.refresh_from_db()
        self.assertIsNotNone(first_appointment.elation_id)
        apply_appointment_rules_async_mock.send_with_options.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # After rule there should be 3 Video 3 Video-new slots and 1 Video-new appointment
        # 9 - 9:30 : video slot deleted
        # 9 - 9:30 : video-new slot Booked
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 6)

        # Book second video-new slot
        create_record_mock.reset_mock()
        create_record_mock.return_value = {"id": "12345678"}
        publish_appointment_to_elation_async_mock.send.reset_mock()
        appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
        )
        appointment.patient = self.patient
        appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appointment)
        # After rule there should be 2 Video 0 Video-new slots and 2 Video-new appointment for 50% capacity
        # 9 - 9:30 : video slot deleted
        # 9 - 9:30 : video-new slot Booked
        # 9:45 - 10:15 : video slot deleted
        # 9:45 - 10:15 : video-new slot booked
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot deleted
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot deleted
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 2)

        # Book 1 of the remaining video visits
        established_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO, physician=self.provider.physician
        )
        established_appointment.patient = self.patient
        established_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            established_appointment.save()
        established_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=established_appointment)
        # After rule there should be 1 Video 0 Video-new slots and 2 Video-new, 1 Video appointment for 50% capacity
        # 9 - 9:30 : video slot deleted
        # 9 - 9:30 : video-new slot Booked
        # 9:45 - 10:15 : video slot deleted
        # 9:45 - 10:15 : video-new slot Booked
        # 10:30 - 11:00 : video slot Booked
        # 10:30 - 11:00 : video-new slot deleted
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot deleted
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 1)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            established_appointment.cancel()
        established_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=established_appointment)
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=established_appointment)
        established_appointment.refresh_from_db()
        self.assertIsNone(established_appointment.deleted)
        self.assertIsNotNone(established_appointment.physician)
        # After rule there should be 2 Video 0 Video-new slots and 2 Video-new appointment for 50% capacity
        # 9 - 9:30 : video slot deleted
        # 9 - 9:30 : video-new slot Booked
        # 9:45 - 10:15 : video slot deleted
        # 9:45 - 10:15 : video-new slot booked
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot deleted
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot deleted
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 2)

        # cancel video-new appointment
        # Step 1: set patient None for appintment
        # Step 2: If source Lucian appointment will get deleted
        # Step 3: Elation will sync appointment and delete from Elation
        # Step 4: Appointment rule will apply
        # Step 5: Appointment physician and elation will set to None
        publish_appointment_to_elation_async_mock.send.reset_mock()
        appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appointment)
        appt_slot_video_new: Appointment = Appointment.objects.filter(
            start=appointment.start,
            patient=None,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            physician=self.provider.physician,
        ).first()
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=appt_slot_video_new)
        appointment.refresh_from_db()
        self.assertIsNone(appointment.deleted)
        self.assertIsNotNone(appointment.physician)
        # After rule there should be 3 Video 3 Video-new slots and 1 Video-new appointment
        # 9 - 9:30 : video slot deleted
        # 9 - 9:30 : video-new slot Booked
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 6)

        # cancel second video-new appointment
        first_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)
        # After rule there should be 4 Video 4 Video-new slots and 0 appointment
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(reason=AppointmentReason.VIDEO, physician=self.provider.physician),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider.physician), 8)

    def test_book_cancel_workflow_for_bh_visits(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        ElationAppointmentSync().connect_model_listener()
        bh_specialist: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot2")
        bh_appointment_type_mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=bh_specialist.physician, appointment_type=self.behavioral_health_appointment_type
        )
        # create provider schedule
        bh_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=bh_specialist,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )
        # set shift start time 9 Am end time 2 PM
        bh_shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=bh_schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:00:00", "%H:%M:%S"),
        )
        # generate BH provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=bh_specialist.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        behavioral_health_slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[bh_shift],
        )
        self.assertEqual(behavioral_health_slots.count(), 28)
        self.assertEqual(self._get_slots_count(physician=bh_specialist.physician), 7)
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=bh_appointment_type_mapping,
            day_of_week=self.three_days_after.weekday(),
            defaults={"percentage_of_slots": "100"},
        )

        # Book first bh slot
        # Get first bh visit
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
        )
        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        # Before rule there will be 7 Behavioral Health slots and 0 appointments
        # 9:00 - 10:00
        # 10:00 - 11:00
        # 11:00 - 12:00
        # 12:00 - 13:00
        # 13:00 - 14:00
        # 14:00 - 15:00
        # 15:00 - 16:00
        create_record_mock.return_value = {"id": "1234567"}
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_appointment.refresh_from_db()
        self.assertIsNotNone(first_appointment.elation_id)
        apply_appointment_rules_async_mock.send_with_options.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # # After rule there should be 6 BH slots and 1 BH appointment
        # # 9:00 - 10:00 : BH slot Booked
        # # 10:00 - 11:00 : BH slot
        # # 11:00 - 12:00 : BH slot
        # # 12:00 - 13:00 : BH slot
        # # 13:00 - 14:00 : BH slot
        # # 14:00 - 15:00 : BH slot
        # # 15:00 - 16:00 : BH slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=bh_specialist.physician), 6)

        # # Book another BH slot
        # # Considering middle appointment, to check for previous and next appointments existence
        create_record_mock.reset_mock()
        create_record_mock.return_value = {"id": "12345678"}
        publish_appointment_to_elation_async_mock.send.reset_mock()
        appointment: Appointment = self._get_second_slot_by_reason(
            reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
        )
        appointment.patient = self.patient
        appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appointment)
        # # After rule there should be 5 BH slots and 2 BH appointments
        # # 9:00 - 10:00 : BH slot Booked
        # # 10:00 - 11:00 : BH slot
        # # 11:00 - 12:00 : BH slot Booked
        # # 12:00 - 13:00 : BH slot
        # # 13:00 - 14:00 : BH slot
        # # 14:00 - 15:00 : BH slot
        # # 15:00 - 16:00 : BH slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=bh_specialist.physician), 5)

        # cancel BH appointment
        # Step 1: set patient None for appintment
        # Step 2: If source Lucian appointment will get deleted
        # Step 3: Elation will sync appointment and delete from Elation
        # Step 4: Appointment rule will apply
        # Step 5: Appointment physician and elation will set to None
        publish_appointment_to_elation_async_mock.send.reset_mock()
        first_appointment.refresh_from_db()
        first_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        bh_appt_slot: Appointment = Appointment.objects.filter(
            start=first_appointment.start,
            patient=None,
            reason=AppointmentReason.BEHAVIORAL_HEALTH,
            physician=bh_specialist.physician,
        ).first()
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=bh_appt_slot)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)
        # After rule there should be 6 BH slots and 1 BH appointment
        # # 9:00 - 10:00 : BH slot (released)
        # # 10:00 - 11:00 : BH slot
        # # 11:00 - 12:00 : BH slot Booked
        # # 12:00 - 13:00 : BH slot
        # # 13:00 - 14:00 : BH slot
        # # 14:00 - 15:00 : BH slot
        # # 15:00 - 16:00 : BH slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=bh_specialist.physician), 6)

        # cancel next booked BH appointment
        next_appointment = Appointment.objects.filter(
            physician=bh_specialist.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=False,
        ).first()
        next_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            next_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        next_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=next_appointment)
        next_appointment.refresh_from_db()
        self.assertIsNone(next_appointment.deleted)
        self.assertIsNotNone(next_appointment.physician)
        # After rule there should be 7 BH slots and no BH appointments booked
        # # 9:00 - 10:00 : BH slot
        # # 10:00 - 11:00 : BH slot
        # # 11:00 - 12:00 : BH slot
        # # 12:00 - 13:00 : BH slot
        # # 13:00 - 14:00 : BH slot
        # # 14:00 - 15:00 : BH slot
        # # 15:00 - 16:00 : BH slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.BEHAVIORAL_HEALTH, physician=bh_specialist.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=bh_specialist.physician), 7)

    def test_book_cancel_workflow_for_hg_visits(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        ElationAppointmentSync().connect_model_listener()
        health_guide: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot3")
        # create provider schedule
        hg_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=health_guide,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )
        # set shift start time 9 AM end time 11:30 AM
        hg_shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=hg_schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("11:30:00", "%H:%M:%S"),
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=health_guide.physician, appointment_type=self.health_guide_appointment_type
        )
        # generate hg provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=health_guide.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        health_guide_slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[hg_shift],
        )
        self.assertEqual(health_guide_slots.count(), 10)
        self.assertEqual(self._get_slots_count(physician=health_guide.physician), 3)

        # Book first hg slot
        # Get first hg visit
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
        )
        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        # Before rule there will be 3 Health Guide slots and 0 appointments
        # 9:00 - 9:45
        # 9:45 - 10:30
        # 10:30 - 11:15
        create_record_mock.return_value = {"id": "1234567"}
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
            ),
            0,
        )
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_appointment.refresh_from_db()
        self.assertIsNotNone(first_appointment.elation_id)
        apply_appointment_rules_async_mock.send_with_options.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # After rule there should be 2 HG slots and 1 HG appointment
        # 9:00 - 9:45 (Booked)
        # 9:45 - 10:30
        # 10:30 - 11:15
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=health_guide.physician), 2)

        # cancel HG appointment
        # Step 1: set patient None for appintment
        # Step 2: If source Lucian appointment will get deleted
        # Step 3: Elation will sync appointment and delete from Elation
        # Step 4: Appointment rule will apply
        # Step 5: Appointment physician and elation will set to None
        publish_appointment_to_elation_async_mock.send.reset_mock()
        first_appointment.refresh_from_db()
        first_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        hg_appt_slot: Appointment = Appointment.objects.filter(
            start=first_appointment.start,
            patient=None,
            reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            physician=health_guide.physician,
        ).first()
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=hg_appt_slot)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)
        # After rule there should be 3 HG slots and 0 HG appointment
        # 9:00 - 9:45 (Booked)
        # 9:45 - 10:30
        # 10:30 - 11:15
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, physician=health_guide.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=health_guide.physician), 3)

    def test_book_cancel_workflow_with_short_visit(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        # Detailed view of book and cancel workflows - https://docs.google.com/spreadsheets/d/11SJdA-hdbu9eAsIoqcjqbWApTEBhTxkM7NLcvdE2ACA/edit?gid=0#gid=0&range=A16
        ElationAppointmentSync().connect_model_listener()
        slots: List[TimeSlot] = TimeSlot.objects.all()
        self.assertEqual(slots.count(), 24)
        # 4 Video slots, 4 Video-New, 6 Sick Visit slots
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 14)
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_with_short_visits.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Book first video-new slot
        # Get first video new visit
        first_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
        )
        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        # Before rule there was 4 Video, 4 Video new slots, 6 Focused slots and 0 appointments
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        create_record_mock.side_effect = [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}]
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_appointment.refresh_from_db()
        self.assertIsNotNone(first_appointment.elation_id)
        apply_appointment_rules_async_mock.send_with_options.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 10)

        # Book focused slot
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        first_focused_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        first_focused_appointment.patient = self.patient
        first_focused_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_focused_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_focused_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_focused_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_focused_appointment.refresh_from_db()
        self.assertIsNotNone(first_focused_appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_focused_appointment)
        # After rule there should be 2 Video, 2 Video-new, 3 Focused slots
        # and 1 Video-new, 1 Focused appointment for 50% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot(BOOKED)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 7)
        focused_appointment_scheduled_logs = EventLog.objects.filter(
            type=EventTypeCodes.FOCUSED_APPOINTMENT_SCHEDULED,
            target_object_id=first_focused_appointment.id,
        )
        self.assertEqual(focused_appointment_scheduled_logs.count(), 1)
        event = focused_appointment_scheduled_logs.first()

        bot = get_lucian_bot_user()

        self.assertEqual(
            event.metadata["current_appointment"],
            {
                "patient_id": first_focused_appointment.patient_id,
                "updated_by_id": bot.pk,
            },
        )
        # Should store ids of overlapping Video and Video-New appointments at 9:45
        overlapping_video_slot = Appointment.deleted_objects.filter(reason=AppointmentReason.VIDEO).order_by("start")[1]
        overlapping_video_new_slot = Appointment.deleted_objects.filter(
            reason=AppointmentReason.VIDEO_NEW_PATIENT
        ).order_by("start")[0]

        self.assertEqual(
            sorted(event.metadata["overlapping_appointments_deleted"]),
            sorted([overlapping_video_slot.id, overlapping_video_new_slot.id]),
        )

        # Book last video-new slot
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        appointment: Appointment = self._get_second_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
        )
        appointment.patient = self.patient
        appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appointment)
        # After rule there should be 1 Video, 0 Video-new, 1 Focused slots,
        # 2 Video-new, 1 focused appointment for 50% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot (BOOKED)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot(BOOKED)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 2)

        # Book last focused slot
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        focused_visit: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        focused_visit.patient = self.patient
        focused_visit.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            focused_visit.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=focused_visit.pk,
            log_prefix=f"appointment_scheduled: Appointment: {focused_visit.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=focused_visit.pk, log_prefix="")
        create_record_mock.assert_called_once()
        focused_visit.refresh_from_db()
        self.assertIsNotNone(focused_visit.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_visit)
        # After rule there should be 0 slots,
        # 2 Video-new, 2 focused appointments for 50% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot (BOOKED)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot(BOOKED)
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 0)

        # cancel video-new appointment
        # Step 1: set patient None for appintment
        # Step 2: If source Lucian appointment will get deleted
        # Step 3: Elation will sync appointment and delete from Elation
        # Step 4: Appointment rule will apply
        # Step 5: Appointment physician and elation will set to None
        publish_appointment_to_elation_async_mock.send.reset_mock()
        appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=appointment)
        appt_slot_video_new: Appointment = Appointment.objects.filter(
            start=appointment.start,
            patient=None,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            physician=self.provider_with_short_visits.physician,
        ).first()
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=appt_slot_video_new)
        appointment.refresh_from_db()
        self.assertIsNone(appointment.deleted)
        self.assertIsNotNone(appointment.physician)
        # After rule there should be 0 video, 1 video-new, 0 focused slots,
        # 1 Video-new, 2 focused appointments for 50% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot(BOOKED)
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 1)

        # cancel first focused visit
        first_focused_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_focused_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_focused_appointment)
        first_focused_appointment.refresh_from_db()
        self.assertIsNone(first_focused_appointment.deleted)
        self.assertIsNotNone(first_focused_appointment.physician)
        # After rule there should be 2 video, 2 video-new, 3 focused slots
        # and 1 Video-new, 1 focused appointments for 50% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 7)

        # cancel second video-new appointment
        first_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)
        # After rule there should be 3 video, 3 video-new, 7 focused slots
        # and 1 focused appointment for 50% capacity
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 11)

        # cancel second focused appointment
        focused_visit.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            focused_visit.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        focused_visit.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_visit)
        focused_visit.refresh_from_db()
        self.assertIsNone(focused_visit.deleted)
        self.assertIsNotNone(focused_visit.physician)
        # After rule there should be 4 video, 4 video-new, 8 focused slots
        # and 0 appointments booked
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 14)

    def test_book_cancel_short_visit_from_api(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        ElationAppointmentSync().connect_model_listener()
        slots: List[TimeSlot] = TimeSlot.objects.all()
        self.assertEqual(slots.count(), 24)
        # 4 Video slots, 4 Video-New, 6 Sick Visit slots
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 14)
        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_with_short_visits.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Book first focused slot
        # Get first focused visit
        first_appointment: Appointment = self._get_second_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        # Before rule there was 4 Video, 4 Video new slots, 6 Focused slots and 0 appointments
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        create_record_mock.side_effect = [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}]
        with self.captureOnCommitCallbacks(execute=True):
            self.client.patch(f"/appointment/slot/v3/{first_appointment.id}", format="json", data={})
        # After rule there should be 2 Video, 2 Video-new slots, 5 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 9)

        # cancel focused appointment
        publish_appointment_to_elation_async_mock.reset_mock()
        first_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)
        first_appointment.refresh_from_db()
        self.assertIsNone(first_appointment.deleted)
        self.assertIsNotNone(first_appointment.physician)
        # After rule there should be 4 video, 4 video-new, 6 focused slots
        # and 0 appointments booked
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.provider_with_short_visits.physician), 14)

    def test_book_cancel_workflow_with_overlapping_appointment_from_different_shift(
        self,
        _acquire_mock,
        _backend_mock,
        publish_appointment_to_elation_async_mock,
        apply_appointment_rules_async_mock,
        _submit_event_async_mock,
        _init_session_mock,
        create_record_mock,
        delete_record_mock,
    ):
        # Detailed view of book and cancel workflows - https://docs.google.com/spreadsheets/d/11SJdA-hdbu9eAsIoqcjqbWApTEBhTxkM7NLcvdE2ACA/edit?gid=0#gid=0&range=A30
        # set shift start time 8:30 AM and end time 9 AM, so that buffer time overlaps with 9 AM shift
        self.shift_with_short_visits_2 = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("08:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("09:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # set shift start time 8:00 AM and end time 8:30 AM, so that buffer time overlaps with 8:30 AM shift
        self.shift_with_short_visits_3 = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule_with_short_visits,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("08:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("08:30:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider_with_short_visits.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        ElationAppointmentSync().connect_model_listener()

        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type, self.focused_visit_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_with_short_visits.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        # Book video-new slot from 8:30 am shift
        second_appointment: Appointment = self._get_second_slot_by_reason(
            reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
        )
        # Before booking
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00)
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30)
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 3 (starting at 9:00)
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=75),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=165),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=75),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=165),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=60),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=90),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=150),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=180),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

        # Book visit
        # This should do following events
        # Step 1: Book appointment to Lucian
        # Step 2: Post save appointment will created in Elation and elation_id will be updated in Lucian appointment
        # Step 3: Appointment rule will apply
        create_record_mock.side_effect = [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}]
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=second_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {second_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=second_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        second_appointment.refresh_from_db()
        self.assertIsNotNone(second_appointment.elation_id)
        apply_appointment_rules_async_mock.send_with_options.assert_called_once_with(
            kwargs={
                "appointment_id": second_appointment.pk,
            },
            delay=2000,
        )
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) shouldn't be affected
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start - timedelta(minutes=30),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30) should be deleted
            # Slots of shift 3 (starting at 9:00) shouldn't be affected,
            # though the buffer time of shift 1 overlaps with the appointments in shift 3
            # Video-New slots of shift 3 (starting at 9:00 am)
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=75),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=165),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=75),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=165),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3
            {
                "start": second_appointment.start + timedelta(minutes=30),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=60),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=90),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=120),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=150),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start + timedelta(minutes=180),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )

        # Book focused slot from 9 AM shift
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        second_focused_appointment: Appointment = self._get_second_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        second_focused_appointment.patient = self.patient
        second_focused_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=second_focused_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {second_focused_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=second_focused_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        second_focused_appointment.refresh_from_db()
        self.assertIsNotNone(second_focused_appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_focused_appointment)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) shouldn't be affected
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30) should remain deleted
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=30),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=60),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=120),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=150),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )

        # Book focused slot from 8 AM shift
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        first_focused_appointment: Appointment = self._get_first_slot_by_reason(
            reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
        )
        first_focused_appointment.patient = self.patient
        first_focused_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_focused_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_focused_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=first_focused_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        first_focused_appointment.refresh_from_db()
        self.assertIsNotNone(first_focused_appointment.elation_id)
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_focused_appointment)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) should be deleted
            # Slots of shift 2 (starting at 8:30) should remain deleted
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=30),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=60),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=120),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=150),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            2,
        )

        # cancel focused appointment from shift 1 (8:00 am shift)
        # Step 1: set patient None for appintment
        # Step 2: If source Lucian appointment will get deleted
        # Step 3: Elation will sync appointment and delete from Elation
        # Step 4: Appointment rule will apply
        # Step 5: Appointment physician and elation will set to None
        publish_appointment_to_elation_async_mock.send.reset_mock()
        first_focused_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            first_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        first_focused_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_focused_appointment)
        appt_slot_video_new: Appointment = Appointment.objects.filter(
            start=first_focused_appointment.start,
            patient=None,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            physician=self.provider_with_short_visits.physician,
        ).first()
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=appt_slot_video_new)
        first_focused_appointment.refresh_from_db()
        self.assertIsNone(first_focused_appointment.deleted)
        self.assertIsNotNone(first_focused_appointment.physician)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) should be released
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30) should remain deleted
            # Slots from shift 3 should not be affected/released
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start + timedelta(minutes=30),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=60),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=120),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=150),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )

        # cancel next booked focused visit
        second_focused_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            second_focused_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        second_focused_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_focused_appointment)
        second_focused_appointment.refresh_from_db()
        self.assertIsNone(second_focused_appointment.deleted)
        self.assertIsNotNone(second_focused_appointment.physician)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) should be released
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30) should remain deleted
            # Overlapping slots from shift 3 should released
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=30),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=60),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=120),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=150),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )

        # cancel Video-New appointment
        second_appointment.patient = None
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)
        expected_open_appointment_slots = [
            # Slots of shift 1 (starting at 8:00) should be released
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start - timedelta(minutes=60),  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Slots of shift 2 (starting at 8:30) should be released
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_appointment.start,  # 8:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Overlapping slots from shift 3 should released
            # Video-New slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_with_short_visits.physician,
            },
            # Video slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=45),  # 9:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=135),  # 11:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_with_short_visits.physician,
            },
            # Focused slots of shift 3 (9:00 am)
            {
                "start": second_focused_appointment.start,  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=30),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=60),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=90),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=120),  # 11:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
            {
                "start": second_focused_appointment.start + timedelta(minutes=150),  # 11:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_with_short_visits.physician,
            },
        ]
        self.assertEqual(
            self._get_slots_count(physician=self.provider_with_short_visits.physician),
            len(expected_open_appointment_slots),
        )
        for item in expected_open_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.provider_with_short_visits.physician
            ),
            0,
        )
        self.shift_with_short_visits_2.delete()

    def _get_slots_count(self, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=True,
        ).count()

    def _get_deleted_slots_count_by_reason(self, reason, physician):
        return Appointment.deleted_objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=True,
        ).count()

    def _get_booked_appointment_count_by_reason(self, reason, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=False,
        ).count()

    def _get_first_slot_by_reason(self, reason, physician):
        return (
            Appointment.objects.filter(
                physician=physician,
                time_slot_type=SlotType.APPOINTMENT_SLOT,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("start")
            .first()
        )

    def _get_last_slot_by_reason(self, reason, physician):
        return (
            Appointment.objects.filter(
                physician=physician,
                time_slot_type=SlotType.APPOINTMENT_SLOT,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("-start")
            .first()
        )

    def _get_second_slot_by_reason(self, reason, physician):
        return (
            Appointment.objects.filter(
                physician=physician,
                time_slot_type=SlotType.APPOINTMENT_SLOT,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            ).order_by("start")
        )[1]


@override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=True)
class TestSlotGenerationWithV2_5FlagOn(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # create provider for test
        self.provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )
        # set date 3 days after as within 36 hours rule will not run
        self.three_days_after = datetime.now() + timedelta(days=3)
        self.three_days_after_str = self.three_days_after.strftime("%Y-%m-%d")
        self.day_of_week = self.three_days_after.isoweekday()
        self.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 1 PM
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )
        time_at_12_noon = (
            datetime.combine(self.three_days_after.date(), time(hour=12))
            .replace(tzinfo=NY_TIMEZONE)
            .astimezone(UTC_TIMEZONE)
        )
        # A shift exception from 12 noon to 1pm to test proper logic
        ShiftExceptionFactory.create(
            schedule=self.schedule,
            period=DateTimeTZRange(
                time_at_12_noon,
                time_at_12_noon + timedelta(hours=1),
            ),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

    def test_generated_time_and_appointment_slots(
        self,
    ):
        actual_slots = []
        slots: QuerySet[TimeSlot] = TimeSlot.objects.all().order_by("period")
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # Time slots should be generated for provider defined in setup->self.provider
        expected_slots_for_provider = [
            "09:00:00",
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:15:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
        ]
        self.assertEqual(actual_slots, expected_slots_for_provider)
        appointments = Appointment.objects.filter(physician=self.provider.physician)
        actual_appointments = []
        for appointment in appointments.iterator():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # No appointment slots should be generated for provider
        self.assertEqual(sorted(actual_appointments), [])
        # update shift to be from 9:15 AM to  end time 12 PM
        self.shift.delete()
        ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:15:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )
        actual_slots = []
        slots: QuerySet[TimeSlot] = TimeSlot.objects.all().order_by("period")
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        expected_slots = [
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:15:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
        ]
        self.assertEqual(actual_slots, expected_slots)
        actual_appointments = []
        for appointment in appointments.iterator():
            actual_appointments.append(appointment.start.astimezone(self.schedule.timezone).strftime("%H:%M:%S"))
        # No appointment slots should be generated
        self.assertEqual(sorted(actual_appointments), [])


class TestApplyRulesForTimeslot(FireflyTestCase):
    @mock.patch(
        "firefly.modules.schedule.utils.rule_handler._apply_visit_mix_ratio",
    )
    def test_apply_rules(self, mock_apply_visit_mix_ratio):
        appointment_type = AppointmentType.objects.get_or_create(unique_key=AppointmentReason.VIDEO)
        provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Rule")
        # scenerio 1
        # all slots within 36 hours
        available_slots = [timezone.now(), timezone.now() + timedelta(hours=20)]
        slots = apply_rules_for_timeslot(
            available_slots=available_slots,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={},
            log_prefix="",
        )
        self.assertEqual(len(slots), len(available_slots))
        mock_apply_visit_mix_ratio.assert_not_called()

        # scenerio 2
        # slots with 36 hours + outside 36 hours
        # should call visit mix
        available_slots = [timezone.now(), timezone.now() + timedelta(hours=20)]
        slots_more_than_36_hours = [timezone.now() + timedelta(hours=40)]
        slots = apply_rules_for_timeslot(
            available_slots=available_slots + slots_more_than_36_hours,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={},
            log_prefix="",
        )
        mock_apply_visit_mix_ratio.assert_called_once_with(
            slots=slots_more_than_36_hours,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={},
            log_prefix="",
            logger=ANY,
        )

    def test_apply_visit_mix_ratio(self):
        appointment_type, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.VIDEO, duration=30, buffer_time_in_minutes=15
        )
        provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Rule")
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider.physician, appointment_type=appointment_type
        )

        slot_1 = (timezone.now() + timedelta(hours=40)).replace(hour=10, minute=45)
        slots_visit_mix_to_be_applied = [slot_1]
        # scenario 1
        # No shift and visit mix exists
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)

        est_visit_mix, _ = PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=slot_1.isoweekday(),
            defaults={"percentage_of_slots": 50},
        )

        # scenario 2
        # No shift exists
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)

        # create provider schedule
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )
        # set shift start time 10 AM end time 4 PM
        self.shift = ShiftFactory.create(
            day_of_week=slot_1.isoweekday(),
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )

        slot_range = [
            {"hour": 10, "minute": 00},
            {"hour": 10, "minute": 15},
            {"hour": 10, "minute": 30},
            {"hour": 10, "minute": 45},
            {"hour": 11, "minute": 00},
            {"hour": 11, "minute": 15},
            {"hour": 11, "minute": 30},
            {"hour": 11, "minute": 45},
            {"hour": 12, "minute": 00},
            {"hour": 12, "minute": 15},
            {"hour": 12, "minute": 30},
            {"hour": 12, "minute": 45},
        ]

        for range in slot_range:
            start_date = (timezone.now() + timedelta(hours=40)).replace(
                hour=range["hour"], minute=range["minute"], second=0, microsecond=0
            )
            end_date = start_date + timedelta(minutes=15)
            TimeSlot.objects.create(shift=self.shift, period=(start_date, end_date))

        # scenario 3
        # No booked appointment exists
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 1)
        self.assertEqual(slots[0], slot_1)

        # scenario 4
        # 1 Video booked appointment exists
        first_video = Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=slot_1.replace(hour=10, minute=00),
            description="Test test",
            elation_id=9999999,
            status="Scheduled",
            physician=provider.physician,
            reason=AppointmentReason.VIDEO,
        )
        date = first_video.start.date()
        appointment_types = AppointmentType.objects.filter(unique_key__in=[first_video.reason]).values(
            "unique_key", "duration", "buffer_time_in_minutes"
        )
        app_type_dict: Dict[str, Dict[str, Union[str, int]]] = {apt["unique_key"]: apt for apt in appointment_types}
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [first_video]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 1)
        self.assertEqual(slots[0], slot_1)

        # scenario 5
        # 1 more Video appt exists, which means visit mix ratio is achieved
        # no slots should be released
        second_video = Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=slot_1.replace(hour=11, minute=30),
            description="Test test",
            elation_id=123123123,
            status="Scheduled",
            physician=provider.physician,
            reason=AppointmentReason.VIDEO,
        )

        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [first_video, second_video]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)

        # scenario 6
        # If the request is for New appts, slot should be released
        appointment_type_awv_new, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.AWV_NEW, duration=30, buffer_time_in_minutes=15
        )
        appointment_type_video_new, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT, duration=30, buffer_time_in_minutes=15
        )
        type_mapping_video_new, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider.physician, appointment_type=appointment_type_video_new
        )

        new_visit_mix, _ = PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping_video_new,
            day_of_week=slot_1.isoweekday(),
            defaults={"percentage_of_slots": 50},
        )

        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type_awv_new,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): new_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 1)
        self.assertEqual(slots[0], slot_1)

        # scenario 7
        # Focused request should also not release the slot
        appointment_type, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.FOCUSED_VISIT, duration=30, buffer_time_in_minutes=15
        )
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [first_video, second_video]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)

        # scenario 8
        # Visit mix ratio is changed the est slots should be released
        est_visit_mix.percentage_of_slots = 75
        est_visit_mix.save()
        new_visit_mix.percentage_of_slots = 25
        new_visit_mix.save()

        appointment_type, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.FOCUSED_VISIT, duration=30, buffer_time_in_minutes=15
        )
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [first_video, second_video]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 1)
        self.assertEqual(slots[0], slot_1)

        # scenario 9
        # Visit mix ratio is changed and 1 New appt exists
        first_video_new = Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=slot_1.replace(hour=12, minute=15),
            description="Test test",
            elation_id=456456456,
            status="Scheduled",
            physician=provider.physician,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        appointment_types = AppointmentType.objects.filter(
            unique_key__in=[AppointmentReason.AWV_NEW, AppointmentReason.VIDEO_NEW_PATIENT]
        ).values("unique_key", "duration", "buffer_time_in_minutes")
        app_type_dict: Dict[str, Dict[str, Union[str, int]]] = {apt["unique_key"]: apt for apt in appointment_types}

        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type_awv_new,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): new_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [first_video_new]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): 12},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)

        first_video.delete()
        second_video.delete()
        first_video_new.delete()

        # Create an exception to delete time slots and evaluate visit mix behaviour
        ShiftException.objects.create(
            schedule=schedule,
            period=DateTimeTZRange(
                (timezone.now() + timedelta(hours=40)).replace(hour=11, minute=30, second=0, microsecond=0),
                (timezone.now() + timedelta(hours=40)).replace(hour=13, minute=00, second=0, microsecond=0),
            ),
        )
        count = TimeSlot.objects.all().count()
        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict={},
            booked_slots_dict={},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): count},
            log_prefix="",
        )
        self.assertEqual(len(slots), 1)
        self.assertEqual(slots[0], slot_1)

        est_visit_mix.percentage_of_slots = 50
        est_visit_mix.save()
        new_visit_mix.percentage_of_slots = 50
        new_visit_mix.save()

        # Create an appointment, next Video should not go.
        appt = Appointment.objects.create(
            patient=self.patient,
            time_slot_type="appointment_slot",
            start=slot_1.replace(hour=10, minute=00),
            description="Test test",
            elation_id=453453,
            status="Scheduled",
            physician=provider.physician,
            reason=AppointmentReason.VIDEO,
        )

        appointment_type, _ = AppointmentType.objects.get_or_create(
            unique_key=AppointmentReason.VIDEO, duration=30, buffer_time_in_minutes=15
        )

        appointment_types = AppointmentType.objects.filter(unique_key__in=[AppointmentReason.VIDEO]).values(
            "unique_key", "duration", "buffer_time_in_minutes"
        )
        app_type_dict: Dict[str, Dict[str, Union[str, int]]] = {apt["unique_key"]: apt for apt in appointment_types}

        slots = _apply_visit_mix_ratio(
            slots=slots_visit_mix_to_be_applied,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict={slot_1.isoweekday(): est_visit_mix},
            app_type_dict=app_type_dict,
            booked_slots_dict={date: [appt]},
            provider_timeslot_dict={slots_visit_mix_to_be_applied[0].date(): count},
            log_prefix="",
        )
        self.assertEqual(len(slots), 0)
