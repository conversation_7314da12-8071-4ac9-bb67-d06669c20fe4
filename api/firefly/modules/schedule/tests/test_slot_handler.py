import zoneinfo
from datetime import date, datetime, time, timedelta
from typing import List

import mock
from django.db.models.query import QuerySet
from dramatiq.rate_limits import ConcurrentRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from mock import patch
from psycopg2.extras import Date<PERSON>ange, DateTimeTZRange

from firefly.core.feature.testutils import override_flag
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.constants import (
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import AppointmentFactory, AppointmentSlotFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.cases.constants import INSURANCE_PLAN_NEEDS_REVIEW
from firefly.modules.cases.models import CaseCategory
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.schedule.constants import (
    RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW,
    SLOT_DAYS,
    SLOT_SIZE,
    WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5,
)
from firefly.modules.schedule.factories import (
    AppointmentSlotsFactory,
    ProviderScheduleFactory,
    ShiftExceptionFactory,
    ShiftFactory,
)
from firefly.modules.schedule.models import (
    AppointmentSlots,
    DayOfWeek,
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    Shift,
    TimeSlot,
)
from firefly.modules.schedule.tasks import (
    appointment_slot_generation,
    consume_slots_by_appointments,
    create_slots_for_provider_shift,
    delete_lucian_slots,
    generate_slot_for_provider,
    lucian_slot_generator,
    release_unbooked_slots,
    remove_appointment_slots,
)
from firefly.modules.schedule.utils.timeslot_handler import generate_appointment_slots


@mock.patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class TestCreateSlotsForProviderShift(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot.physician, appointment_type=self.video_appointment_type
        )
        self.provider_slot_without_mapping: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="Slot"
        )
        another_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot2")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=another_provider.physician, appointment_type=self.video_appointment_type
        )
        today: date = date.today()
        future_day: date = date.today() + timedelta(days=180)

        self.provider_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=(
                today,
                future_day,
            ),
        )
        self.provider_schedule_without_mapping: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot_without_mapping,
            effective_period=(
                today,
                future_day,
            ),
        )
        schedule_for_another_provider: ProviderSchedule = ProviderScheduleFactory.create(
            provider=another_provider,
            effective_period=(
                today,
                future_day,
            ),
        )
        schedule_outside_effective_period: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=(
                future_day,
                future_day,
            ),
            timezone="UTC",
        )
        self.shift_for_provider_on_monday_at_ten_thirty: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=self.provider_schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        self.shift_for_provider_without_mapping_on_monday_at_ten_thirty: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=self.provider_schedule_without_mapping,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        self.shift_for_provider_on_wednesday: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.WEDNESDAY.value,
            schedule=self.provider_schedule,
            effective_period=(today, None),
            start_time=datetime.strptime("11:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        self.shift_for_provider_on_monday_at_three_thirty: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=self.provider_schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("15:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:00:00", "%H:%M:%S"),
        )
        # shift for provider 2
        self.shift_for_another_provider_on_monday: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=schedule_for_another_provider,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        # shift outside window for correct schedule
        ShiftFactory.create(
            day_of_week=DayOfWeek.TUESDAY.value,
            schedule=self.provider_schedule,
            effective_period=(
                future_day,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        # shift for outside schedule
        ShiftFactory.create(
            day_of_week=DayOfWeek.TUESDAY.value,
            schedule=schedule_outside_effective_period,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        self.tz: zoneinfo.ZoneInfo = zoneinfo.ZoneInfo(self.provider_schedule.timezone)

    def test_create_slots_for_provider_shift_with_dry_run(self, mock_elation_update_user):
        # verify that dry run does not create any slots
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=False,
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_on_monday_at_ten_thirty,
                self.shift_for_provider_on_wednesday,
                self.shift_for_provider_on_monday_at_three_thirty,
            ],
        )
        self.assertEqual(slots.count(), 0)

    def test_create_slots_for_provider_shift(self, mock_elation_update_user):
        difference = (
            self.shift_for_provider_on_monday_at_ten_thirty.stop_time
            - self.shift_for_provider_on_monday_at_ten_thirty.start_time
        )
        minutes = difference.total_seconds() / 60
        slot_count = (minutes / SLOT_SIZE) * 2
        difference = self.shift_for_provider_on_wednesday.stop_time - self.shift_for_provider_on_wednesday.start_time
        minutes = difference.total_seconds() / 60
        slot_count = (minutes / SLOT_SIZE) * 2 + slot_count
        difference = (
            self.shift_for_provider_on_monday_at_three_thirty.stop_time
            - self.shift_for_provider_on_monday_at_three_thirty.start_time
        )
        minutes = difference.total_seconds() / 60
        slot_count = (minutes / SLOT_SIZE) * 2 + slot_count

        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_on_monday_at_ten_thirty,
                self.shift_for_provider_on_wednesday,
                self.shift_for_provider_on_monday_at_three_thirty,
            ],
        )
        self.assertEqual(slots.count(), slot_count)

        create_slots_for_provider_shift(
            provider_id=self.provider_slot_without_mapping.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_without_mapping_on_monday_at_ten_thirty,
            ],
        )
        self.assertEqual(slots.count(), 0)

    def test_create_slots_for_provider_shift_with_date_filter(self, mock_elation_update_user):
        # run for a wednesday when the provider is available from 11 AM - 2 pm
        date_for_test: datetime = date.today()
        while date_for_test.isoweekday() != DayOfWeek.WEDNESDAY.value:
            date_for_test = date_for_test + timedelta(days=1)
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date_for_test.strftime("%Y-%m-%d"),
            end_date=date_for_test.strftime("%Y-%m-%d"),
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_on_monday_at_ten_thirty,
                self.shift_for_provider_on_wednesday,
                self.shift_for_provider_on_monday_at_three_thirty,
            ],
        ).order_by("period")
        difference = self.shift_for_provider_on_wednesday.stop_time - self.shift_for_provider_on_wednesday.start_time
        minutes = difference.total_seconds() / 60
        slot_count = minutes / SLOT_SIZE
        # Expect 12 slots - for a shift of three hours with a slot size of 15 mins
        self.assertEqual(slot_count, 12)
        self.assertEqual(slots.count(), slot_count)
        actual_slots = []
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.tz).strftime("%H:%M:%S"))
        expected_slots = [
            "11:00:00",
            "11:15:00",
            "11:30:00",
            "11:45:00",
            "12:00:00",
            "12:15:00",
            "12:30:00",
            "12:45:00",
            "13:00:00",
            "13:15:00",
            "13:30:00",
            "13:45:00",
        ]
        self.assertEqual(actual_slots, expected_slots)

    def test_create_slots_for_provider_shift_with_date_filter_and_partial_leave(self, mock_elation_update_user):
        # run for a wednesday when the provider is available from 11 AM - 2 pm
        date_for_test: datetime = date.today()
        while date_for_test.isoweekday() != DayOfWeek.WEDNESDAY.value:
            date_for_test = date_for_test + timedelta(days=1)
        # Add unavailability from 11:30-12:45
        eleven_thirty_on_day_of_test = datetime.combine(
            date_for_test,
            time(hour=11, minute=30),
            tzinfo=self.tz,
        )
        eleven_thirty_on_day_of_test = datetime.strptime(
            eleven_thirty_on_day_of_test.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=self.tz)

        twelve_forty_five_on_day_of_test = datetime.combine(
            date_for_test,
            time(hour=12, minute=45),
            tzinfo=self.tz,
        )
        twelve_forty_five_on_day_of_test = datetime.strptime(
            twelve_forty_five_on_day_of_test.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=self.tz)
        ShiftExceptionFactory.create(
            schedule=self.provider_schedule,
            period=DateTimeTZRange(eleven_thirty_on_day_of_test, twelve_forty_five_on_day_of_test),
        )
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date_for_test.strftime("%Y-%m-%d"),
            end_date=date_for_test.strftime("%Y-%m-%d"),
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_on_monday_at_ten_thirty,
                self.shift_for_provider_on_wednesday,
                self.shift_for_provider_on_monday_at_three_thirty,
            ],
        ).order_by("period")
        actual_slots = []
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.tz).strftime("%H:%M:%S"))
        expected_slots = [
            "11:00:00",
            "11:15:00",
            "12:45:00",
            "13:00:00",
            "13:15:00",
            "13:30:00",
            "13:45:00",
        ]
        self.assertEqual(actual_slots, expected_slots)

    def test_create_slots_for_provider_shift_with_date_filter_and_full_leave(self, mock_elation_update_user):
        # run for a wednesday when the provider is available from 11 AM - 2 pm
        date_for_test: datetime = date.today()
        while date_for_test.isoweekday() != DayOfWeek.WEDNESDAY.value:
            date_for_test = date_for_test + timedelta(days=1)
        # Add unavailability for entire day
        midnight_on_day_of_test = datetime.combine(
            date_for_test,
            time(hour=00, minute=00),
            tzinfo=self.tz,
        )
        midnight_on_day_of_test = datetime.strptime(
            midnight_on_day_of_test.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=self.tz)

        eleven_fifty_nine_day_of_test = datetime.combine(
            date_for_test,
            time(hour=23, minute=59),
            tzinfo=self.tz,
        )
        eleven_fifty_nine_day_of_test = datetime.strptime(
            eleven_fifty_nine_day_of_test.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=self.tz)
        ShiftExceptionFactory.create(
            schedule=self.provider_schedule,
            period=DateTimeTZRange(midnight_on_day_of_test, eleven_fifty_nine_day_of_test),
        )
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date_for_test.strftime("%Y-%m-%d"),
            end_date=date_for_test.strftime("%Y-%m-%d"),
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[
                self.shift_for_provider_on_monday_at_ten_thirty,
                self.shift_for_provider_on_wednesday,
                self.shift_for_provider_on_monday_at_three_thirty,
            ],
        ).order_by("period")
        actual_slots = []
        for slot in slots.all():
            actual_slots.append(slot.period.lower.astimezone(self.tz).strftime("%H:%M:%S"))
        expected_slots = []
        self.assertEqual(actual_slots, expected_slots)

    def test_create_slots_with_shift_end_date(self, mock_elation_update_user):
        Shift.objects.all().delete()
        day_of_week = date.today().weekday() + 1
        # shift which end before slot generation day
        # default slot generation day 60 days
        ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=self.provider_schedule,
            effective_period=(
                date.today(),
                date.today() + timedelta(days=6),
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
        )
        slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__schedule__provider=self.provider_slot,
        )
        # 15 mins time slots for two hours for a single day = 8 time slots
        self.assertEqual(slots.count(), 8)
        ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=self.provider_schedule,
            effective_period=(
                date.today() + timedelta(days=6),
                date.today() + timedelta(days=12),
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        # 15 mins time slots for two hours for two day = 16 time slots
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
        )
        self.assertEqual(slots.count(), 16)


@mock.patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class TestConsumeSlotsByAppointments(FireflyTestCase):
    def setUp(self):
        super().setUp()
        today: date = date.today()
        future_day: date = date.today() + timedelta(days=90)
        now: datetime = datetime.now()
        start_time_1 = datetime(now.year, now.month, now.day, 0, 0, 0, tzinfo=UTC_TIMEZONE)
        start_time_2 = start_time_1 + timedelta(hours=1)
        start_time_3 = start_time_1 + timedelta(days=1)
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot.physician, appointment_type=self.video_appointment_type
        )
        self.provider_2: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot2")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_2.physician, appointment_type=self.video_appointment_type
        )
        self.appointment_1 = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT,
            start=start_time_1,
        )
        self.appointment_2 = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT,
            start=start_time_2,
        )
        self.appointment_3 = AppointmentFactory.create(
            physician=self.provider_2.physician,
            time_slot_type=SlotType.APPOINTMENT,
            start=start_time_1,
        )
        self.appointment_4 = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT,
            start=start_time_3,
        )
        self.appointment_5 = AppointmentFactory.create(
            physician=self.provider_2.physician,
            time_slot_type="Event",
            start=start_time_1,
        )

        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        schedule_1: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_2,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        shift_1: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        shift_2: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.MONDAY.value,
            schedule=schedule_1,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("14:00:00", "%H:%M:%S"),
        )
        self.timeslot_1: TimeSlot = TimeSlot.objects.create(
            shift=shift_2, period=(start_time_1, start_time_1 + timedelta(minutes=15))
        )
        self.timeslot_2: TimeSlot = TimeSlot.objects.create(
            shift=shift_2,
            period=(
                start_time_1 + timedelta(minutes=15),
                start_time_1 + timedelta(minutes=30),
            ),
        )
        self.timeslot_3: TimeSlot = TimeSlot.objects.create(
            shift=shift_1, period=(start_time_1, start_time_1 + timedelta(minutes=15))
        )
        self.timeslot_4: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_1 + timedelta(minutes=15),
                start_time_1 + timedelta(minutes=30),
            ),
        )
        self.timeslot_5: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_1 + timedelta(minutes=30),
                start_time_1 + timedelta(minutes=45),
            ),
        )
        self.timeslot_6: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_2,
                start_time_2 + timedelta(minutes=15),
            ),
        )
        self.timeslot_7: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_2 + timedelta(minutes=15),
                start_time_2 + timedelta(minutes=30),
            ),
        )
        self.timeslot_8: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_2 + timedelta(minutes=30),
                start_time_2 + timedelta(minutes=45),
            ),
        )
        self.timeslot_9: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_3,
                start_time_3 + timedelta(minutes=15),
            ),
        )
        self.timeslot_10: TimeSlot = TimeSlot.objects.create(
            shift=shift_1,
            period=(
                start_time_3 + timedelta(minutes=15),
                start_time_3 + timedelta(minutes=30),
            ),
        )

    @patch("firefly.modules.schedule.tasks.apply_appointment_rules")
    def test_consume_slots_by_appointments(self, apply_appointment_rules_mock, mock_elation_update_user):
        consume_slots_by_appointments(provider_id=self.provider_slot.user_id, dry_run_off=False)
        appointment_slots: AppointmentSlots = AppointmentSlots.objects.filter(
            appointment__in=[
                self.appointment_1,
                self.appointment_2,
                self.appointment_3,
                self.appointment_4,
                self.appointment_5,
            ]
        )
        self.assertEqual(appointment_slots.count(), 0)
        apply_appointment_rules_mock.assert_not_called()
        consume_slots_by_appointments(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=date.today().strftime("%Y-%m-%d"),
        )
        appointment_slots: AppointmentSlots = AppointmentSlots.objects.filter(
            appointment__in=[
                self.appointment_1,
                self.appointment_2,
                self.appointment_3,
                self.appointment_4,
                self.appointment_5,
            ]
        )
        self.assertEqual(appointment_slots.count(), 4)
        self.assertEqual(apply_appointment_rules_mock.call_count, 2)
        apply_appointment_rules_mock.reset_mock()
        consume_slots_by_appointments(provider_id=self.provider_slot.user_id, dry_run_off=True)
        appointment_slots: AppointmentSlots = AppointmentSlots.objects.filter(
            appointment__in=[
                self.appointment_1,
                self.appointment_2,
                self.appointment_3,
                self.appointment_4,
                self.appointment_5,
            ]
        )
        self.assertEqual(appointment_slots.count(), 6)
        self.assertEqual(apply_appointment_rules_mock.call_count, 3)
        apply_appointment_rules_mock.reset_mock()

        consume_slots_by_appointments(provider_id=self.provider_slot.user_id, dry_run_off=True)
        appointment_slots: AppointmentSlots = AppointmentSlots.objects.filter(
            appointment__in=[
                self.appointment_1,
                self.appointment_2,
                self.appointment_3,
                self.appointment_4,
                self.appointment_5,
            ]
        )
        self.assertEqual(appointment_slots.count(), 6)
        self.assertEqual(apply_appointment_rules_mock.call_count, 3)


@mock.patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
class TestAppointmentSlotGeneration(FireflyTestCase):
    def setUp(self):
        super().setUp()
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        # Mapping a provider to Video and Video-New appointment types
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot.physician, appointment_type=self.video_new_appointment_type
        )

        # Mapping provider to health guide consult and behavioral health appointment types,
        # to test mapping to appointment types having different buffer time and duration
        self.provider2: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot2")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider2.physician, appointment_type=self.health_guide_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider2.physician, appointment_type=self.behavioral_health_appointment_type
        )
        # Mapping BH specialists to Behavioral Health appointment type,
        # so that only those appointment slots are generated
        self.bh_specialist: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot2")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.bh_specialist.physician, appointment_type=self.behavioral_health_appointment_type
        )

        today: date = date.today()
        future_day: date = date.today() + timedelta(days=90)

        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        self.shift_1: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.WEDNESDAY.value,
            schedule=schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("15:00:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=self.provider_slot.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
        )
        self.slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[self.shift_1],
        )

        # Creating a schedule, shifts and generating slots for provider2
        schedule_for_provider2: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider2,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        self.shift_for_provider2_on_tuesday: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.TUESDAY.value,
            schedule=schedule_for_provider2,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=self.provider2.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
        )
        self.provider2_slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[self.shift_for_provider2_on_tuesday],
        )

        # Creating a schedule, shifts and generating slots for Behavioral Health Specialist
        schedule_for_bh_specialist: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.bh_specialist,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        self.shift_for_bh_specialist_on_thursday: Shift = ShiftFactory.create(
            day_of_week=DayOfWeek.THURSDAY.value,
            schedule=schedule_for_bh_specialist,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=self.bh_specialist.user_id,
            dry_run_off=True,
            start_date=date.today().strftime("%Y-%m-%d"),
            end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
        )
        self.behavioral_health_slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            shift__in=[self.shift_for_bh_specialist_on_thursday],
        )

    def test_appointment_slot_generation_with_date_filter(self, mock_elation_update_user):
        self.assertEqual(self.slots.count(), 40)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider_slot.user_id,
                dry_run_off=True,
                start_date=date.today().strftime("%Y-%m-%d"),
                end_date=date.today().strftime("%Y-%m-%d"),
            )
        appt_slots = AppointmentSlots.objects.all()
        if date.today().isoweekday() == self.shift_1.day_of_week:
            self.assertEqual(appt_slots.count(), 28)
        else:
            self.assertEqual(appt_slots.count(), 0)

    def test_appointment_slot_generation(self, mock_elation_update_user):
        self.assertEqual(self.slots.count(), 40)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider_slot.user_id,
                dry_run_off=True,
                start_date=date.today().strftime("%Y-%m-%d"),
                end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
            )
            # Generate BH and HG slots for provider2
            appointment_slot_generation(
                provider_id=self.provider2.user_id,
                dry_run_off=True,
                start_date=date.today().strftime("%Y-%m-%d"),
                end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
            )
            # Generate slots for Behavioral Health Specialist
            appointment_slot_generation(
                provider_id=self.bh_specialist.user_id,
                dry_run_off=True,
                start_date=date.today().strftime("%Y-%m-%d"),
                end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
            )
        appt_slots = AppointmentSlots.objects.all()
        # Video and Video-New Appointment slots:
        #  10 - 10:30, 10:45 - 11:15, 11:30 - 12:00, 12:15 - 12:45, 13:00 - 13:30, 13:45 - 14:15, 14:30 - 15:00
        # Total 7 appointment
        # per slot 2 type Video and Video-new - total 14 appointment
        # generates for 2 week, consider Wednesday of week, so 14 * 2 = 28 appointments total
        # each appointment consist 2 AppointmentSlot objects as time slot length is 15 mins and
        # Appointment is for 30 mins, total slots = 28 * (30/15) = 56
        #
        # Provider 2:
        #  Health Guide Consult Appointment slots:
        #   10 - 10:30, 10:45 - 11:15, 11:30 - 12:00, 12:15 - 12:45 (on Tuesday for Health Guide)
        #   Total 4 appointments
        #   per slot 1 type Health Guide Consult - total 4 appointment
        #   generates for 2 week, consider Tuesday of week, so 4 * 2 = 8 appointments total
        #   each appointment consist 2 AppointmentSlot objects as time slot length is 15 mins and
        #   Appointment is for 30 mins, total slots = 8 * (30/15) = 16
        #  Behavioral Health Appointment slots:
        #   10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00 (on Tuesday for Health Guide)
        #   Total 3 appointments
        #   per slot 1 type Behavioral Health - total 3 appointment
        #   generates for 2 week, consider Tuesday of week, so 3 * 2 = 6 appointments total
        #   each appointment consist 4 AppointmentSlot objects as time slot length is 15 mins and
        #   Appointment is for 60 mins, total slots = 6 * (60/15) = 24
        #
        # Behavioral Health Appointment slots:
        #  10:00 - 11:00, 11:00 - 12:00 (on Thursday for Behavioral Health Specialist)
        # Total 2 appointments
        # per slot 1 type Behavioral Health - total 2 appointments
        # generates for 2 week, consider Tuesday of week, so 2 * 2 = 4 appointments total
        # each appointment consist 4 AppointmentSlot objects as time slot length is 15 mins and
        # Appointment is for 60 mins, total slots = 4 * (60/15) = 16
        self.assertEqual(appt_slots.count(), 112)
        video_appts = Appointment.objects.filter(reason=AppointmentReason.VIDEO, status=None).order_by("start")
        self.assertEqual(video_appts.count(), 14)
        video_new_appts = Appointment.objects.filter(reason=AppointmentReason.VIDEO_NEW_PATIENT, status=None).order_by(
            "start"
        )
        self.assertEqual(video_new_appts.count(), 14)
        health_guide_appts = Appointment.objects.filter(
            reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE, status=None
        ).order_by("start")
        self.assertEqual(health_guide_appts.count(), 8)
        bh_appts = Appointment.objects.filter(reason=AppointmentReason.BEHAVIORAL_HEALTH, status=None).order_by("start")
        self.assertEqual(bh_appts.count(), 10)
        # create duplicate slot
        dup_video_slot: Appointment = AppointmentFactory.create(
            physician=video_appts[0].physician,
            start=video_appts[0].start,
            reason=video_appts[0].reason,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            duration=timedelta(minutes=self.video_appointment_type.duration),
            visible=True,
            source=AppointmentSource.LUCIAN,
            status=None,
        )
        # delete 1st video slot
        with self.captureOnCommitCallbacks(execute=True):
            video_appts[0].delete()
        # delete duplicate video-slot
        with self.captureOnCommitCallbacks(execute=True):
            dup_video_slot.delete()
        # re generate slots
        # it should not create new slots for deleted slots
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider_slot.user_id,
                dry_run_off=True,
                start_date=date.today().strftime("%Y-%m-%d"),
                end_date=(date.today() + timedelta(days=13)).strftime("%Y-%m-%d"),
            )
        video_appts = Appointment.objects.filter(reason=AppointmentReason.VIDEO, status=None)
        self.assertEqual(video_appts.count(), 13)
        video_new_appts = Appointment.objects.filter(reason=AppointmentReason.VIDEO_NEW_PATIENT, status=None)
        self.assertEqual(video_new_appts.count(), 14)

    def test_appointment_slot_generation_with_existing_appointment(self, mock_elation_update_user):
        self.assertEqual(self.slots.count(), 40)
        exs_appt = None
        with self.captureOnCommitCallbacks(execute=True):
            exs_appt: Appointment = AppointmentFactory.create(
                physician=self.provider_slot.physician,
                start=self.slots[1].period.lower,
                time_slot_type=SlotType.APPOINTMENT,
                duration=timedelta(minutes=self.video_appointment_type.duration),
                source=AppointmentSource.LUCIAN,
            )
        appt_slots = AppointmentSlots.objects.all()
        slot_ids: List[int] = []
        for appt_slot in appt_slots.iterator():
            slot_ids.append(appt_slot.id)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider_slot.user_id,
                dry_run_off=True,
            )
        appt_slots = AppointmentSlots.objects.all().exclude(id__in=slot_ids).order_by("slot")
        # for day 1: appointment exists: 20
        # 11, 11:45, 12:30, 13:15, 14:00
        # for day 2: appointment not exists: 28
        # 10:00, 10:45, 11:30, 12:15, 13:00, 13:45, 14:30
        self.assertEqual(appt_slots.count(), 48)
        appt = (
            Appointment.objects.filter(reason=AppointmentReason.VIDEO).exclude(id__in=[exs_appt.pk]).order_by("-start")
        )
        self.assertEqual(appt.count(), 12)
        appt = Appointment.objects.filter(reason=AppointmentReason.VIDEO_NEW_PATIENT).exclude(id__in=[exs_appt.pk])
        self.assertEqual(appt.count(), 12)

    def test_appointment_slot_generation_with_existing_appointments(self, mock_elation_update_user):
        self.assertEqual(self.slots.count(), 40)
        with self.captureOnCommitCallbacks(execute=True):
            exs_appt_1: Appointment = AppointmentFactory.create(
                physician=self.provider_slot.physician,
                start=self.slots[0].period.lower,
                time_slot_type=SlotType.APPOINTMENT,
                duration=timedelta(minutes=self.video_appointment_type.duration),
                source=AppointmentSource.LUCIAN,
            )
        with self.captureOnCommitCallbacks(execute=True):
            exs_appt_2: Appointment = AppointmentFactory.create(
                physician=self.provider_slot.physician,
                start=self.slots[4].period.lower,
                time_slot_type=SlotType.APPOINTMENT,
                duration=timedelta(minutes=self.video_appointment_type.duration),
                source=AppointmentSource.LUCIAN,
            )
        appt_slots = AppointmentSlots.objects.all()
        slot_ids: List[int] = []
        for appt_slot in appt_slots.iterator():
            slot_ids.append(appt_slot.id)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider_slot.user_id,
                dry_run_off=True,
            )
        appt_slots = AppointmentSlots.objects.all().exclude(id__in=slot_ids).order_by("slot")
        # for day 1: appointment exists: 16
        #  11:45, 12:30, 13:15, 14:00
        # for day 2: appointment not exists: 28
        # 10:00, 10:45, 11:30, 12:15, 13:00, 13:45, 14:30
        self.assertEqual(appt_slots.count(), 44)
        appt = Appointment.objects.filter(reason=AppointmentReason.VIDEO).exclude(id__in=[exs_appt_1.pk, exs_appt_2.pk])
        self.assertEqual(appt.count(), 11)
        appt = Appointment.objects.filter(reason=AppointmentReason.VIDEO_NEW_PATIENT).exclude(
            id__in=[exs_appt_1.pk, exs_appt_2.pk]
        )
        self.assertEqual(appt.count(), 11)

    def test_appointment_slot_generation_with_multiple_types_of_existing_appointments(self, mock_elation_update_user):
        self.assertEqual(self.provider2_slots.count(), 24)
        with self.captureOnCommitCallbacks(execute=True):
            exs_appt_1: Appointment = AppointmentFactory.create(
                physician=self.provider2.physician,
                start=self.provider2_slots[0].period.lower,
                time_slot_type=SlotType.APPOINTMENT,
                duration=timedelta(minutes=self.health_guide_appointment_type.duration),
                source=AppointmentSource.LUCIAN,
                reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE,
            )
        with self.captureOnCommitCallbacks(execute=True):
            exs_appt_2: Appointment = AppointmentFactory.create(
                physician=self.provider2.physician,
                start=self.provider2_slots[3].period.lower,
                time_slot_type=SlotType.APPOINTMENT,
                duration=timedelta(minutes=self.behavioral_health_appointment_type.duration),
                source=AppointmentSource.LUCIAN,
                reason=AppointmentReason.BEHAVIORAL_HEALTH,
            )
        appt_slots = AppointmentSlots.objects.all()
        slot_ids: List[int] = []
        for appt_slot in appt_slots.iterator():
            slot_ids.append(appt_slot.id)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=self.provider2.user_id,
                dry_run_off=True,
            )
        appt_slots = AppointmentSlots.objects.all().exclude(id__in=slot_ids).order_by("slot")
        # for day 1: appointments exist (1HG/1BH): 12 slots remain
        #  11:45, 12:30
        # for day 2: appointment not exists: 16 slots remain
        # 10:00, 10:45, 11:30, 12:15
        self.assertEqual(appt_slots.count(), 28)
        appt = Appointment.objects.filter(reason=AppointmentReason.HEALTH_GUIDE_BOOKABLE).exclude(id=exs_appt_1.pk)
        self.assertEqual(appt.count(), 6)
        appt = Appointment.objects.filter(reason=AppointmentReason.BEHAVIORAL_HEALTH).exclude(id=exs_appt_2.pk)
        self.assertEqual(appt.count(), 4)

    def test_appointment_slot_generation_for_multiple_shift(self, mock_elation_update_user):
        provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1")
        # Mapping provider to Video and Video-New appointment types
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider_slot.physician, appointment_type=self.video_appointment_type
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider_slot.physician, appointment_type=self.video_new_appointment_type
        )
        today: date = date.today()
        future_day: date = date.today() + timedelta(days=90)
        day_of_week = today.weekday() + 1
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=provider_slot,
            effective_period=(
                today,
                future_day,
            ),
            timezone="UTC",
        )
        shift_1 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("9:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("10:45:00", "%H:%M:%S"),
        )
        shift_2 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("12:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("13:00:00", "%H:%M:%S"),
        )
        shift_3 = ShiftFactory.create(
            day_of_week=day_of_week,
            schedule=schedule,
            effective_period=(
                today,
                future_day,
            ),
            start_time=datetime.strptime("14:30:00", "%H:%M:%S"),
            stop_time=datetime.strptime("16:30:00", "%H:%M:%S"),
        )
        create_slots_for_provider_shift(
            provider_id=provider_slot.user_id,
            dry_run_off=True,
            start_date=today.strftime("%Y-%m-%d"),
            end_date=today.strftime("%Y-%m-%d"),
        )
        self.assertEqual(TimeSlot.objects.filter(shift__in=[shift_1, shift_2, shift_3]).count(), 15)
        with self.captureOnCommitCallbacks(execute=True):
            appointment_slot_generation(
                provider_id=provider_slot.user_id,
                dry_run_off=True,
                start_date=today.strftime("%Y-%m-%d"),
                end_date=today.strftime("%Y-%m-%d"),
            )
        self.assertEqual(Appointment.objects.all().count(), 12)
        self.assertEqual(AppointmentSlots.objects.all().count(), 24)

    def test_generate_appointment_slots_with_existing_appointment(self, mock_elation_update_user):
        now_10_am = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
        TimeSlot.objects.get_or_create(period=(now_10_am, now_10_am + timedelta(minutes=15)), shift=self.shift_1)
        TimeSlot.objects.get_or_create(
            period=(now_10_am + timedelta(minutes=15), now_10_am + timedelta(minutes=30)), shift=self.shift_1
        )
        # get all timeslots for given time or next 14 days
        time_slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
            period__startswith__gte=now_10_am,
            period__endswith__lte=now_10_am + timedelta(minutes=30),
        ).order_by("period")
        slots: List[TimeSlot] = []
        # convert to list of TimeSlots
        for time_slot in time_slots.iterator():
            slots.append(time_slot)
        with self.captureOnCommitCallbacks(execute=True):
            generate_appointment_slots(all_slots=slots, provider=self.provider_slot, log_prefix="", dry_run_off=True)
        appointment = Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(appointment.count(), 2)
        with self.captureOnCommitCallbacks(execute=True):
            generate_appointment_slots(all_slots=slots, provider=self.provider_slot, log_prefix="", dry_run_off=True)
        appointment = Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(appointment.count(), 2)
        # delete video slot
        # it should not re-create video slot as video-new slot exists for same time
        Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=AppointmentReason.VIDEO,
        ).delete()
        with self.captureOnCommitCallbacks(execute=True):
            generate_appointment_slots(all_slots=slots, provider=self.provider_slot, log_prefix="", dry_run_off=True)
        appointment = Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(appointment.count(), 1)
        # delete both slots
        # it should re-create both slots
        Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        ).delete()
        with self.captureOnCommitCallbacks(execute=True):
            generate_appointment_slots(all_slots=slots, provider=self.provider_slot, log_prefix="", dry_run_off=True)
        appointment = Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(appointment.count(), 2)
        # test for multiple deleted slot for same reason same time
        dup_appt_video = Appointment.objects.create(
            physician=self.provider_slot.physician,
            start=slots[0].period.lower,
            reason=AppointmentReason.VIDEO,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            duration=timedelta(minutes=self.video_appointment_type.duration),
            visible=True,
            source=AppointmentSource.LUCIAN,
            status=None,
        )
        dup_appt_video.delete()
        with self.captureOnCommitCallbacks(execute=True):
            generate_appointment_slots(all_slots=slots, provider=self.provider_slot, log_prefix="", dry_run_off=True)
        appointment = Appointment.objects.filter(
            start=slots[0].period.lower,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(appointment.count(), 2)


@patch.object(ElationAppointmentSync, "pull")
class TestRemoveAppointmentSlots(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot.physician, appointment_type=self.video_appointment_type
        )
        self.provider_slot_without_mapping: ProviderDetail = ProviderDetailFactory.create(
            first_name="Test", last_name="Slot"
        )
        person = PersonUserFactory()
        now: datetime = datetime.now()
        start_time = datetime(now.year, now.month, now.day, now.hour, 0, 0, tzinfo=UTC_TIMEZONE)
        # create Elation appointment
        self.booked_elation_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=person.user,
            status=AppointmentStatus.SCHEDULED.value,
        )
        start_time = start_time + timedelta(minutes=self.video_appointment_type.duration)
        # create Elation cancel appointment
        self.cancelled_elation_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT,
            start=start_time,
            source=AppointmentSource.ELATION,
            status=AppointmentStatus.CANCELLED.value,
        )
        AppointmentSlotsFactory.create(appointment=self.cancelled_elation_appt)
        start_time = start_time + timedelta(minutes=self.video_appointment_type.duration)
        # create Elation appointment slots
        self.scheduled_elation_appt_slot: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_elation_appt_slot)
        # create Elation appointment slots for worng provider
        self.scheduled_elation_appt_slot_with_wrong_provider: Appointment = AppointmentFactory.create(
            physician=self.provider_slot_without_mapping.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_elation_appt_slot_with_wrong_provider)
        # create Elation appointment event
        self.elation_event: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type="event",
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.elation_event)
        start_time = start_time + timedelta(minutes=self.video_appointment_type.duration)
        self.scheduled_elation_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_elation_appt)
        start_time = start_time + timedelta(minutes=self.video_appointment_type.duration)
        # create Lucian appointment
        self.booked_lucian_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.LUCIAN,
            patient=person.user,
            status=AppointmentStatus.SCHEDULED.value,
        )
        AppointmentSlotsFactory.create(appointment=self.booked_lucian_appt)
        start_time = start_time + timedelta(minutes=self.video_appointment_type.duration)
        # create Lucian apointment slots
        self.scheduled_lucian_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_lucian_appt)
        # Unbooked Lucian apointment with appointment slots
        self.unbooked_lucian_appt: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.unbooked_lucian_appt)
        self.scheduled_lucian_appt_without_appointment_slots: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        start_time = start_time + timedelta(days=10)
        # Elation appointment slots outside date
        self.scheduled_elation_appt_outside_test_end_date: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_elation_appt_outside_test_end_date)
        # appointment outside max lookahead period
        self.scheduled_elation_appt_outside_max_look_ahead: Appointment = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time + timedelta(days=SLOT_DAYS),
            source=AppointmentSource.ELATION,
            patient=None,
        )
        AppointmentSlotsFactory.create(appointment=self.scheduled_elation_appt_outside_max_look_ahead)

    def test_remove_appointment_slots_with_dry_run(self, pull_mock):
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 12)
        start_date = datetime.today().strftime("%Y-%m-%d")
        end_date = (datetime.today() + timedelta(days=7)).strftime("%Y-%m-%d")
        remove_appointment_slots(
            provider_id=self.provider_slot.user, start_date=start_date, end_date=end_date, dry_run_off=False
        )
        self.assertEqual(pull_mock.call_count, 3)
        pull_mock.assert_has_calls(
            [
                mock.call(self.scheduled_elation_appt_slot.elation_id),
                mock.call(self.scheduled_elation_appt.elation_id),
                mock.call(self.scheduled_lucian_appt_without_appointment_slots.elation_id),
            ]
        )
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 12)
        pull_mock.reset_mock()
        # call for provider without mapping
        remove_appointment_slots(
            provider_id=self.provider_slot_without_mapping.user,
            start_date=start_date,
            end_date=end_date,
            dry_run_off=False,
        )
        self.assertEqual(pull_mock.call_count, 0)
        # add mapping
        mapping = PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider_slot_without_mapping.physician, appointment_type=self.video_appointment_type
        )
        remove_appointment_slots(
            provider_id=self.provider_slot_without_mapping.user,
            start_date=start_date,
            end_date=end_date,
            dry_run_off=False,
        )
        self.assertEqual(pull_mock.call_count, 1)
        mapping.delete()

    def test_remove_appointment_slots_with_dry_run_off(self, pull_mock):
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 12)
        start_date = datetime.today().strftime("%Y-%m-%d")
        end_date = (datetime.today() + timedelta(days=7)).strftime("%Y-%m-%d")
        remove_appointment_slots(
            provider_id=self.provider_slot.user, start_date=start_date, end_date=end_date, dry_run_off=True
        )
        self.assertEqual(pull_mock.call_count, 3)
        pull_mock.assert_has_calls(
            [
                mock.call(self.scheduled_elation_appt_slot.elation_id),
                mock.call(self.scheduled_elation_appt.elation_id),
                mock.call(self.scheduled_lucian_appt_without_appointment_slots.elation_id),
            ]
        )
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 9)

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=True)
    def test_remove_appointment_slots_with_v2_5_flag_on(self, pull_mock):
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 12)
        start_date = datetime.today().strftime("%Y-%m-%d")
        end_date = (datetime.today() + timedelta(days=7)).strftime("%Y-%m-%d")
        remove_appointment_slots(
            provider_id=self.provider_slot.user, start_date=start_date, end_date=end_date, dry_run_off=True
        )
        self.assertEqual(pull_mock.call_count, 5)
        pull_mock.assert_has_calls(
            [
                mock.call(self.scheduled_elation_appt_slot.elation_id),
                mock.call(self.scheduled_elation_appt.elation_id),
                mock.call(self.scheduled_lucian_appt.elation_id),
                mock.call(self.unbooked_lucian_appt.elation_id),
                mock.call(self.scheduled_lucian_appt_without_appointment_slots.elation_id),
            ]
        )
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 7)

    def test_remove_appointment_slots_without_end_date(self, pull_mock):
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 12)
        start_date = datetime.today().strftime("%Y-%m-%d")
        remove_appointment_slots(provider_id=self.provider_slot.user, start_date=start_date, dry_run_off=True)
        self.assertEqual(pull_mock.call_count, 4)
        pull_mock.assert_has_calls(
            [
                mock.call(self.scheduled_elation_appt_slot.elation_id),
                mock.call(self.scheduled_elation_appt.elation_id),
                mock.call(self.scheduled_lucian_appt_without_appointment_slots.elation_id),
                mock.call(self.scheduled_elation_appt_outside_test_end_date.elation_id),
            ]
        )
        appts = Appointment.objects.all()
        self.assertEqual(appts.count(), 8)


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
@patch("firefly.modules.schedule.tasks.timezone.now")
class TestReleaseUnbookedSlots(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        diff_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1")
        person = PersonUserFactory()
        tomorrow: datetime = datetime.now() + timedelta(days=1)
        # Adjust the start date to ensure slot release logic will not run
        after_slot_release_window = datetime.now() + timedelta(hours=RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW + 1)
        # within the release window
        self.today_time = datetime(
            datetime.now().year, datetime.now().month, datetime.now().day, 4, 0, 0, tzinfo=NY_TIMEZONE
        ).astimezone(UTC_TIMEZONE)
        start_time = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 4, 0, 0, tzinfo=NY_TIMEZONE).astimezone(
            UTC_TIMEZONE
        )
        self.day_of_week = tomorrow.isoweekday()
        # create provider schedule
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 8 AM end time 11 AM UTC
        # which is 4 AM to 7 AM ET
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )

        schedule_for_diff_provider: ProviderSchedule = ProviderScheduleFactory.create(
            provider=diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )

        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_slot.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=start_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )
            # Make it such that today only has video slots
            # and tomorrow has both video and new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=diff_provider.physician, appointment_type=appointment_type
            )
            # Make it such that tomorrow only has video slots
            # and today has both video and new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=start_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=start_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.today_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )

        create_slots_for_provider_shift(self.provider_slot.user_id, True)
        create_slots_for_provider_shift(diff_provider.user_id, True)

        # create appointment for tomorrow
        appt = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            source=AppointmentSource.LUCIAN,
            patient=person.user,
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        # delete this appt
        appt.delete()
        start_time = start_time + timedelta(
            minutes=self.video_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )
        # create appointment slots for tomorrow
        # video slot as delete
        # video_new as active
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )

        start_time_for_diff_provider = start_time

        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )
        # create appointment slots for tomorrow
        # video slot as active
        # video_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )
        # create appointment slots for tomorrow
        # both video and video_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time = after_slot_release_window
        # video slot as active
        # video_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        # diff_provider
        appt_slot = AppointmentFactory.create(
            physician=diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )

    def test_release_unbooked_slots_with_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time
        active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )
        active_appt_slots_for_provider = active_appt_slots.filter(physician=self.provider_slot.physician)
        self.assertEqual(active_appt_slots.count(), 4)
        self.assertEqual(active_appt_slots_for_provider.count(), 3)
        release_unbooked_slots(provider_id=self.provider_slot.user.id, dry_run_off=True)
        # should release video slots for today and all for tomorrow and for given provider
        self.assertEqual(active_appt_slots.count(), 11)
        self.assertEqual(active_appt_slots_for_provider.count(), 10)

        # test for second call
        release_unbooked_slots(provider_id=self.provider_slot.user.id, dry_run_off=True)
        self.assertEqual(active_appt_slots.count(), 11)
        self.assertEqual(active_appt_slots_for_provider.count(), 10)

    def test_release_unbooked_slots_without_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time
        active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(active_appt_slots.count(), 4)
        release_unbooked_slots(dry_run_off=True)
        # should release video slots for today and all for tomorrow and for given provider
        # should release all slots for today and only video for tomorrow and for other provider
        active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(active_appt_slots.count(), 16)

        # test for second call
        release_unbooked_slots(dry_run_off=True)
        self.assertEqual(active_appt_slots.count(), 16)

    def test_release_unbooked_slots_with_dry_run_on(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time
        active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(active_appt_slots.count(), 4)
        release_unbooked_slots()
        # should not release slots for tomorrow
        active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )
        self.assertEqual(active_appt_slots.count(), 4)


@patch("firefly.modules.schedule.tasks.create_or_release_slots_for_physician")
class TestReleaseUnbookedSlotsWith2_5Flag(FireflyTestCase):
    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=False)
    def test_release_unbooked_slots_with_provider_with_flag_off(self, mock_create_or_release_slots_for_physician):
        provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider_slot.physician, appointment_type=self.video_new_appointment_type
        )
        release_unbooked_slots(provider_id=provider_slot.user.id, dry_run_off=True)
        # Should create or release slots when v2.5 flag is off
        mock_create_or_release_slots_for_physician.assert_called_once()

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=False)
    def test_release_unbooked_slots_without_provider_with_flag_off(self, mock_create_or_release_slots_for_physician):
        provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider_slot.physician, appointment_type=self.video_new_appointment_type
        )
        release_unbooked_slots(dry_run_off=True)
        # Should create or release slots when v2.5 flag is off
        mock_create_or_release_slots_for_physician.assert_called_once()

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=True)
    def test_release_unbooked_slots_with_provider(self, mock_create_or_release_slots_for_physician):
        provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider_slot.physician, appointment_type=self.video_new_appointment_type
        )
        release_unbooked_slots(provider_id=provider_slot.user.id, dry_run_off=True)
        # Should not create or release slots when v2.5 flag is ON
        mock_create_or_release_slots_for_physician.assert_not_called()

    @override_flag(WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, active=True)
    def test_release_unbooked_slots_without_provider(self, mock_create_or_release_slots_for_physician):
        provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider_slot.physician, appointment_type=self.video_new_appointment_type
        )
        release_unbooked_slots(dry_run_off=True)
        # Should not create or release slots when v2.5 flag is ON
        mock_create_or_release_slots_for_physician.assert_not_called()


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
@patch("firefly.modules.schedule.tasks.timezone.now")
class TestReleaseUnbookedSlotsWithFocusedVisits(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        self.diff_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1")
        self.single_day_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1Day")
        person = PersonUserFactory()
        tomorrow: datetime = datetime.now() + timedelta(days=1)
        # Adjust the start date to ensure slot release logic will not run
        self.after_slot_release_window = datetime.now() + timedelta(hours=RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW + 1)
        # within the release window
        self.today_time = datetime(
            datetime.now().year, datetime.now().month, datetime.now().day, 4, 0, 0, tzinfo=NY_TIMEZONE
        ).astimezone(UTC_TIMEZONE)
        self.start_time = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 4, 0, 0, tzinfo=NY_TIMEZONE).astimezone(
            UTC_TIMEZONE
        )
        self.day_of_week = tomorrow.isoweekday()
        # create provider schedule
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 8 AM end time 11 AM UTC
        # which is 4 AM to 7 AM ET
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )
        schedule_for_diff_provider: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )
        schedule_for_single_day_provider: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.single_day_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_for_single_day_provider = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule_for_single_day_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift_for_single_day_provider = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule_for_single_day_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )

        # Creating only physician appointment type mapping for focused type
        # Note: We don't maintain visit mix ratio for focused visits in backend
        # aa it would be sharing the visit mix ratio set for Video appointment type
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider_slot.physician, appointment_type=self.focused_visit_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.diff_provider.physician, appointment_type=self.focused_visit_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.single_day_provider.physician, appointment_type=self.focused_visit_type
        )

        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_slot.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.start_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )
            # Make it such that today only has video and focused slots
            # and tomorrow has all video, focused and new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO_NEW_PATIENT:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.diff_provider.physician, appointment_type=appointment_type
            )
            # Make it such that tomorrow only has video and focused slots
            # and today has all video, focused and new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO_NEW_PATIENT:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.start_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.start_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.today_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )

            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.single_day_provider.physician, appointment_type=appointment_type
            )
            # Make it such that tomorrow only has video-new slots
            # and today has all video, focused and new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO_NEW_PATIENT:
                # Mock that the mapping itself doesn't exist
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.start_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.today_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )

        create_slots_for_provider_shift(self.provider_slot.user_id, True)
        create_slots_for_provider_shift(self.diff_provider.user_id, True)
        create_slots_for_provider_shift(self.single_day_provider.user_id, True)

        # create appointment for tomorrow
        appt = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=self.start_time,
            source=AppointmentSource.LUCIAN,
            patient=person.user,
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        # delete this appt
        appt.delete()
        focused_start_time = self.start_time + timedelta(
            minutes=self.focused_visit_type.duration + self.focused_visit_type.buffer_time_in_minutes
        )  # 08:30
        start_time = self.start_time + timedelta(
            minutes=self.video_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 08:45
        # create appointment slots for tomorrow
        # video slot as delete
        # video_new as active
        # focused as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )

        focused_start_time = focused_start_time + timedelta(
            minutes=self.focused_visit_type.duration + self.focused_visit_type.buffer_time_in_minutes
        )  # 9:00
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=focused_start_time,
            reason=AppointmentReason.FOCUSED_VISIT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time_for_diff_provider = start_time
        focused_start_time_for_diff_provider = focused_start_time

        focused_start_time = focused_start_time + timedelta(
            minutes=self.focused_visit_type.duration + self.focused_visit_type.buffer_time_in_minutes
        )  # 9:30
        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 9:30
        # create appointment slots for tomorrow
        # video slot as active
        # video_new as deleted
        # focused as active
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=focused_start_time,
            reason=AppointmentReason.FOCUSED_VISIT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )

        focused_start_time = focused_start_time + timedelta(
            minutes=self.focused_visit_type.duration + self.focused_visit_type.buffer_time_in_minutes
        )  # 10:00
        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 10:15
        # create appointment slots for tomorrow
        # all video, focused and video_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=focused_start_time,
            reason=AppointmentReason.FOCUSED_VISIT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time = self.after_slot_release_window
        # video slot as active
        # video_new as deleted
        # Focused as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.FOCUSED_VISIT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        # diff_provider
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=focused_start_time_for_diff_provider,
            reason=AppointmentReason.FOCUSED_VISIT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )

        self.active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )

        expected_active_appointment_slots = [
            # No slots for today and for tomorrow:
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_with_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots(provider_id=self.provider_slot.user.id, dry_run_off=True)
        # should release slots for tomorrow only and for provided provider
        expected_active_appointment_slots = [
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=30),  # 8:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=120),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=150),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_with_single_day_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots(provider_id=self.single_day_provider.user.id, dry_run_off=True)
        # should release slots for tomorrow only and for provided provider
        expected_active_appointment_slots = [
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_without_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots(dry_run_off=True)
        # should release slots for tomorrow only
        expected_active_appointment_slots = [
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=30),  # 8:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=30),  # 8:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=120),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=120),  # 10:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.single_day_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=150),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=150),  # 10:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_with_dry_run_on(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots()
        # should not release slots for tomorrow
        expected_active_appointment_slots = [
            # No slots for today and for tomorrow:
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=60),  # 9:00
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.FOCUSED_VISIT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
@patch("firefly.modules.schedule.tasks.timezone.now")
class TestReleaseUnbookedSlotsWithAWVNewVisits(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_slot: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot")
        self.diff_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Slot1")
        person = PersonUserFactory()
        tomorrow: datetime = datetime.now() + timedelta(days=1)
        # Adjust the start date to ensure slot release logic will not run
        self.after_slot_release_window = datetime.now() + timedelta(hours=RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW + 1)
        # within the release window
        self.today_time = datetime(
            datetime.now().year, datetime.now().month, datetime.now().day, 4, 0, 0, tzinfo=NY_TIMEZONE
        ).astimezone(UTC_TIMEZONE)
        self.start_time = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 4, 0, 0, tzinfo=NY_TIMEZONE).astimezone(
            UTC_TIMEZONE
        )
        self.day_of_week = tomorrow.isoweekday()
        # create provider schedule
        schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")
        schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider_slot,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 8 AM end time 11 AM UTC
        # which is 4 AM to 7 AM ET
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )
        schedule_for_diff_provider: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="07:00:00",
        )
        self.today_shift_for_diff_provider = ShiftFactory.create(
            day_of_week=self.today_time.isoweekday(),
            schedule=schedule_for_diff_provider,
            effective_period=DateRange(schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time="04:00:00",
            stop_time="04:30:00",
        )

        # Creating only physician appointment type mapping for AWV New type
        # Note: We don't maintain visit mix ratio for AWV New  visits in backend
        # aa it would be sharing the visit mix ratio set for Video-New appointment type
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider_slot.physician, appointment_type=self.awv_new_visit_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.diff_provider.physician, appointment_type=self.awv_new_visit_type
        )

        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.provider_slot.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.start_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )
            # Make it such that today only has video slots
            # and tomorrow has all video, Video-New and AWV New slots
            if appointment_type.unique_key == AppointmentReason.VIDEO_NEW_PATIENT:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.today_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.diff_provider.physician, appointment_type=appointment_type
            )
            # Make it such that tomorrow only has video slots
            # and today has all video, video-new and awv-new slots
            if appointment_type.unique_key == AppointmentReason.VIDEO_NEW_PATIENT:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.start_time.isoweekday(),
                    defaults={"percentage_of_slots": "0"},
                )
            else:
                PhysicianVisitMixRatio.objects.get_or_create(
                    physician_appointment_type=type_mapping,
                    day_of_week=self.start_time.isoweekday(),
                    defaults={"percentage_of_slots": "100"},
                )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.today_time.isoweekday(),
                defaults={"percentage_of_slots": "50"},
            )

        create_slots_for_provider_shift(self.provider_slot.user_id, True)
        create_slots_for_provider_shift(self.diff_provider.user_id, True)

        # create appointment for tomorrow
        appt = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=self.start_time,
            source=AppointmentSource.LUCIAN,
            patient=person.user,
            reason=AppointmentReason.VIDEO,
            status=AppointmentStatus.SCHEDULED.value,
        )
        # delete this appt
        appt.delete()
        start_time = self.start_time + timedelta(
            minutes=self.video_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 08:45
        # create appointment slots for tomorrow
        # video slot as delete
        # video_new as active
        # awv_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.AWV_NEW,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time_for_diff_provider = start_time
        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 9:30
        # create appointment slots for tomorrow
        # video slot as active
        # video_new as deleted
        # awv_new as active
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.AWV_NEW,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        start_time = start_time + timedelta(
            minutes=self.video_new_appointment_type.duration + self.video_appointment_type.buffer_time_in_minutes
        )  # 10:15
        # create appointment slots for tomorrow
        # all video, awv_new and video_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.AWV_NEW,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        start_time = self.after_slot_release_window
        # video slot as active
        # video_new as deleted
        # awv_new as deleted
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.provider_slot.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time,
            reason=AppointmentReason.AWV_NEW,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        # diff_provider
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()
        appt_slot = AppointmentFactory.create(
            physician=self.diff_provider.physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            start=start_time_for_diff_provider,
            reason=AppointmentReason.AWV_NEW,
            source=AppointmentSource.LUCIAN,
            patient=None,
        )
        appt_slot.delete()

        self.active_appt_slots = Appointment.objects.filter(
            patient__isnull=True,
            source=AppointmentSource.LUCIAN,
        )

        expected_active_appointment_slots = [
            # No slots for today and for tomorrow:
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_with_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots(provider_id=self.provider_slot.user.id, dry_run_off=True)
        # should release slots for tomorrow only and for provided provider
        print("Today log", self.today_time, self.start_time)
        print("hysicians with provider", self.provider_slot.physician.id, self.diff_provider.physician.id)
        expected_active_appointment_slots = [
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_without_provider(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time
        print("hysicians", self.provider_slot.physician.id, self.diff_provider.physician.id)

        release_unbooked_slots(dry_run_off=True)
        # should release slots for both providers
        expected_active_appointment_slots = [
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.today_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time,  # 8:00
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.AWV_NEW,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=135),  # 10:15
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        # self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )

    def test_release_unbooked_slots_with_dry_run_on(self, timezone_now_mock, _acquire_mock, _backend_mock):
        timezone_now_mock.return_value = self.today_time

        release_unbooked_slots()
        # should not release slots for tomorrow
        expected_active_appointment_slots = [
            # No slots for today and for tomorrow:
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=45),  # 8:45
                "reason": AppointmentReason.VIDEO,
                "physician": self.diff_provider.physician,
            },
            {
                "start": self.start_time + timedelta(minutes=90),  # 9:30
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
            {
                "start": self.after_slot_release_window,
                "reason": AppointmentReason.VIDEO,
                "physician": self.provider_slot.physician,
            },
        ]
        self.assertEqual(self.active_appt_slots.count(), len(expected_active_appointment_slots))
        for item in expected_active_appointment_slots:
            self.assertTrue(
                Appointment.objects.filter(
                    start=item["start"],
                    reason=item["reason"],
                    physician=item["physician"],
                    patient__isnull=True,
                    source=AppointmentSource.LUCIAN,
                ).exists(),
                f"Expected Appointment not found: {item}",
            )


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
@patch("firefly.modules.schedule.tasks.generate_slot_for_provider")
class TestLucianSlotGenerator(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider: ProviderDetail = ProviderDetailFactory.create()
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_appointment_type
        )
        self.provider1: ProviderDetail = ProviderDetailFactory.create()
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider1.physician, appointment_type=self.video_appointment_type
        )

    def test_lucian_slot_generator_with_flag_on(self, generate_slot_for_provider_mock, _acquire_mock, _backend_mock):
        lucian_slot_generator(
            provider_ids=f"{self.provider.user_id},{self.provider1.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        self.assertEqual(generate_slot_for_provider_mock.send.call_count, 2)
        generate_slot_for_provider_mock.reset_mock()
        lucian_slot_generator()
        self.assertEqual(
            generate_slot_for_provider_mock.send.call_count,
            PhysicianAppointmentTypeMapping.objects.filter(physician__isnull=False).count(),
        )

    def test_lucian_slot_generator_appt_type_mapping(
        self, generate_slot_for_provider_mock, _acquire_mock, _backend_mock
    ):
        provider2: ProviderDetail = ProviderDetailFactory.create()
        lucian_slot_generator(
            provider_ids=f"{self.provider.user_id},{self.provider1.user_id},{provider2.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        self.assertEqual(generate_slot_for_provider_mock.send.call_count, 2)
        PhysicianAppointmentTypeMapping.objects.create(
            physician=provider2.physician, appointment_type=self.video_appointment_type
        )
        generate_slot_for_provider_mock.reset_mock()
        lucian_slot_generator(
            provider_ids=f"{self.provider.user_id},{self.provider1.user_id},{provider2.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        self.assertEqual(generate_slot_for_provider_mock.send.call_count, 3)


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
@patch("firefly.modules.schedule.tasks.consume_slots_by_appointments")
@patch("firefly.modules.schedule.tasks.appointment_slot_generation")
@patch("firefly.modules.schedule.tasks.remove_appointment_slots")
@patch("firefly.modules.schedule.tasks.create_slots_for_provider_shift")
class TestGenerateSlotProvider(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider: ProviderDetail = ProviderDetailFactory.create()
        self.log_prefix: str = "lucian_slot_generator"

    def test_generate_slot_for_provider(
        self,
        create_slots_for_provider_shift_mock,
        remove_appointment_slots_mock,
        appointment_slot_generation_mock,
        consume_slots_by_appointments_mock,
        _acquire_mock,
        _backend_mock,
    ):
        generate_slot_for_provider(provider_id=self.provider.user_id, log_prefix=self.log_prefix)
        create_slots_for_provider_shift_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date=None,
            end_date=None,
            dry_run_off=False,
        )
        remove_appointment_slots_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date=None,
            end_date=None,
            dry_run_off=False,
        )
        appointment_slot_generation_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date=None,
            end_date=None,
            dry_run_off=False,
        )
        consume_slots_by_appointments_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date=None,
            end_date=None,
            dry_run_off=False,
        )
        create_slots_for_provider_shift_mock.reset_mock()
        remove_appointment_slots_mock.reset_mock()
        appointment_slot_generation_mock.reset_mock()
        consume_slots_by_appointments_mock.reset_mock()
        generate_slot_for_provider(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix,
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        create_slots_for_provider_shift_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        remove_appointment_slots_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        appointment_slot_generation_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )
        consume_slots_by_appointments_mock.assert_called_with(
            provider_id=self.provider.user_id,
            log_prefix=self.log_prefix + f": provider: {self.provider.user_id}",
            start_date="2024-01-01",
            end_date="2024-01-01",
            dry_run_off=True,
        )


@patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@patch.object(
    ConcurrentRateLimiter,
    "_acquire",
    return_value=True,
)
class TestDeleteLucianSlots(FireflyTestCase):
    def setUp(self):
        super().setUp()
        today: datetime = datetime.now()
        two_days_after: datetime = datetime.now() + timedelta(days=2)
        self.provider: ProviderDetail = ProviderDetailFactory.create()
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider.physician, appointment_type=self.video_appointment_type
        )
        self.provider1: ProviderDetail = ProviderDetailFactory.create()
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.provider1.physician, appointment_type=self.video_appointment_type
        )
        # create slot for today with provider 1
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(today.year, today.month, today.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
        )
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(today.year, today.month, today.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
        )
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(today.year, today.month, today.day, 9, 45, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
        )
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(today.year, today.month, today.day, 9, 45, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
        )
        # create slot for after 2 days with provider 1
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(two_days_after.year, two_days_after.month, two_days_after.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
        )
        AppointmentSlotFactory.create(
            physician=self.provider.physician,
            start=datetime(two_days_after.year, two_days_after.month, two_days_after.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
        )
        # create slot for after 2 days with provider 2
        AppointmentSlotFactory.create(
            physician=self.provider1.physician,
            start=datetime(two_days_after.year, two_days_after.month, two_days_after.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO,
            source=AppointmentSource.LUCIAN,
        )
        AppointmentSlotFactory.create(
            physician=self.provider1.physician,
            start=datetime(two_days_after.year, two_days_after.month, two_days_after.day, 9, 0, 0, tzinfo=UTC_TIMEZONE),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            source=AppointmentSource.LUCIAN,
        )

    def test_delete_lucian_slots_with_flag_on(
        self,
        _acquire_mock,
        _backend_mock,
    ):
        all_appointments_count: int = Appointment.objects.filter(source=AppointmentSource.LUCIAN).count()
        # with dry run off
        delete_lucian_slots()
        self.assertEqual(Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), 0)
        # with dry run on
        delete_lucian_slots(dry_run_off=True)
        self.assertEqual(
            Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), all_appointments_count
        )

    def test_delete_lucian_slots_with_provider_filter_flag_on_(
        self,
        _acquire_mock,
        _backend_mock,
    ):
        all_appointments_count: int = Appointment.objects.filter(
            source=AppointmentSource.LUCIAN, physician=self.provider.physician
        ).count()
        # with dry run off
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}")
        self.assertEqual(Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), 0)
        # with dry run on
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}", dry_run_off=True)
        self.assertEqual(
            Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), all_appointments_count
        )

    def test_delete_lucian_slots_with_date_filter_flag_on(
        self,
        _acquire_mock,
        _backend_mock,
    ):
        today: str = datetime.now().strftime("%Y-%m-%d")
        all_appointments_count: int = Appointment.objects.filter(
            start__range=(datetime.strptime(today, "%Y-%m-%d").date(), datetime.strptime(today, "%Y-%m-%d").date()),
            source=AppointmentSource.LUCIAN,
        ).count()
        # with dry run off
        delete_lucian_slots(start_date=today, end_date=today)
        self.assertEqual(Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), 0)
        # with dry run on
        delete_lucian_slots(start_date=today, end_date=today, dry_run_off=True)
        self.assertEqual(
            Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), all_appointments_count
        )

    def test_delete_lucian_slots_with_date_and_provider_filter_flag_on(
        self,
        _acquire_mock,
        _backend_mock,
    ):
        today: str = datetime.now().strftime("%Y-%m-%d")
        all_appointments_count: int = Appointment.objects.filter(
            start__range=(datetime.strptime(today, "%Y-%m-%d").date(), datetime.strptime(today, "%Y-%m-%d").date()),
            source=AppointmentSource.LUCIAN,
            physician=self.provider.physician,
        ).count()
        # with dry run off
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}", start_date=today, end_date=today)
        self.assertEqual(Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), 0)
        # with dry run on
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}", start_date=today, end_date=today, dry_run_off=True)
        self.assertEqual(
            Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), all_appointments_count
        )

    def test_delete_lucian_slots_with_start_date_and_provider_filter_flag_on(
        self,
        _acquire_mock,
        _backend_mock,
    ):
        today: str = datetime.now().strftime("%Y-%m-%d")
        all_appointments_count: int = Appointment.objects.filter(
            start__gte=datetime.strptime(today, "%Y-%m-%d").date(),
            source=AppointmentSource.LUCIAN,
            physician=self.provider.physician,
        ).count()
        # with dry run off
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}", start_date=today)
        self.assertEqual(Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), 0)
        # with dry run on
        delete_lucian_slots(provider_ids=f"{self.provider.user_id}", start_date=today, dry_run_off=True)
        self.assertEqual(
            Appointment.deleted_objects.filter(source=AppointmentSource.LUCIAN).count(), all_appointments_count
        )
