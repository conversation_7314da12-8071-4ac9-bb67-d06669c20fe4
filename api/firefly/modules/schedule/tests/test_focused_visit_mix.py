from datetime import datetime, timed<PERSON>ta

import mock
from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from psycopg2.extras import DateRange

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import ProviderDetailFactory
from firefly.core.user.models.models import ProviderDetail
from firefly.modules.appointment.constants import (
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.utils import apply_appointment_rules, apply_rules
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.schedule.factories import ProviderScheduleFactory, ShiftFactory
from firefly.modules.schedule.models import (
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
)
from firefly.modules.schedule.tasks import generate_slot_for_provider


@mock.patch("firefly.modules.appointment.signals.apply_appointment_rules_async.send_with_options")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
class TestFocusedVisitMixWorkflow(FireflyTestCase):
    # This test will cover full end to end appointment booking and cancel workflow
    # while maintaining the visit mix ratios and deleting of slots

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports
        from firefly.core.tests.utils import reset_context_to_luci_user

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        # Create providers once for all tests
        cls.test_provider = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")
        cls.test_provider_without_short_visit = ProviderDetailFactory.create(
            first_name="Test", last_name="ShortAppointment"
        )
        cls.test_provider_with_only_established = ProviderDetailFactory.create(
            first_name="Test", last_name="AllShortAppointment"
        )
        cls.test_provider_with_shorter_shift = ProviderDetailFactory.create(
            first_name="Test", last_name="AppointmentShift"
        )

        # Set date 3 days after as within 36 hours rule will not run
        cls.three_days_after = datetime.now() + timedelta(days=3)
        cls.three_days_after_str = cls.three_days_after.strftime("%Y-%m-%d")
        cls.day_of_week = cls.three_days_after.isoweekday()
        cls.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")

        # Create appointment types once for all tests (using cached properties from FireflyTestCase)
        # These will be accessed via self.focused_visit_type, self.video_appointment_type, etc.

        # Create schedules once for all tests
        cls.schedule = ProviderScheduleFactory.create(
            provider=cls.test_provider,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        cls.schedule_without_short_visit = ProviderScheduleFactory.create(
            provider=cls.test_provider_without_short_visit,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        cls.schedule_with_only_established = ProviderScheduleFactory.create(
            provider=cls.test_provider_with_only_established,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        cls.schedule_with_shorter_shift = ProviderScheduleFactory.create(
            provider=cls.test_provider_with_shorter_shift,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone="UTC",
        )

        # Create shifts once for all tests
        cls.shift = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        cls.shift_without_short_visit = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule_without_short_visit,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        cls.shift_with_only_established = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule_with_only_established,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )
        cls.shift_with_shorter_shift = ShiftFactory.create(
            day_of_week=cls.day_of_week,
            schedule=cls.schedule_with_shorter_shift,
            effective_period=DateRange(cls.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("11:00:00", "%H:%M:%S"),
        )

    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch.object(
        WindowRateLimiter,
        "_acquire",
        return_value=True,
    )
    def setUp(self, _acquire_mock, _mutex_mock):
        super().setUp()

        # Use class-level objects for tests
        self.test_provider = self.__class__.test_provider
        self.test_provider_without_short_visit = self.__class__.test_provider_without_short_visit
        self.test_provider_with_only_established = self.__class__.test_provider_with_only_established
        self.test_provider_with_shorter_shift = self.__class__.test_provider_with_shorter_shift
        self.three_days_after = self.__class__.three_days_after
        self.three_days_after_str = self.__class__.three_days_after_str
        self.day_of_week = self.__class__.day_of_week
        self.schedule_start_date = self.__class__.schedule_start_date
        self.schedule = self.__class__.schedule
        self.shift = self.__class__.shift

        # Create appointment type mappings per test (these need to be per-test for isolation)
        # Create mapping for focused visits, without configuring visit mix ratio
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.focused_visit_type
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider_with_shorter_shift.physician, appointment_type=self.focused_visit_type
        )

        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
        ]:
            type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
            type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_with_shorter_shift.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

        for appointment_type in [self.video_appointment_type, self.video_new_appointment_type]:
            type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.test_provider_without_short_visit.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider_with_only_established.physician, appointment_type=self.focused_visit_type
        )

        video_type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider_with_only_established.physician,
            appointment_type=self.video_appointment_type,
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=video_type_mapping,
            day_of_week=self.day_of_week,
            defaults={"percentage_of_slots": "100"},
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 14)

        # Use class-level schedules and shifts
        self.schedule_without_short_visit = self.__class__.schedule_without_short_visit
        self.shift_without_short_visit = self.__class__.shift_without_short_visit

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_without_short_visit.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 8)

        # Use class-level schedules and shifts
        self.schedule_with_only_established = self.__class__.schedule_with_only_established
        self.shift_with_only_established = self.__class__.shift_with_only_established

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_with_only_established.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9:45 - 10:15 : video slot
        # 10:30 - 11:00 : video slot
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 10)

        # Use class-level schedules and shifts
        self.schedule_with_shorter_shift = self.__class__.schedule_with_shorter_shift
        self.shift_with_shorter_shift = self.__class__.shift_with_shorter_shift

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider_with_shorter_shift.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 10)

    def test_booking_video_new_slots(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 video-new visits deletes the rest of video-new slots
        # After that if we book a focused and then cancel it
        # it should only regenerate focused and video slots
        # Video-new slots should remain deleted

        # Book a Video-New visit
        # Before rule there was 4 Video, 4 Video new and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO_NEW_PATIENT, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 10)

        # Book another Video-New visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO_NEW_PATIENT, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 2 Video, 0 Video-new slots, 3 Focused slots and 2 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 5)

        # book a focused appointment
        focused_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        focused_appointment.patient = self.patient
        focused_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            focused_appointment.save()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_appointment)

        # After rule there should be 1 Video, 0 Video-new slots, 2 Focused slots and 2 Video-new, 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 3)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            focused_appointment.cancel()
        focused_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=focused_appointment)
        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=focused_appointment)
        focused_appointment.refresh_from_db()
        self.assertIsNone(focused_appointment.deleted)
        self.assertIsNotNone(focused_appointment.physician)

        # After rule there should be 2 Video, 0 Video-new slots, 3 Focused slots and 2 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (BOOKED)
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot (deleted)
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot (deleted)
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 5)

        # cancel video-new appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Video-new appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (BOOKED)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 10)

    def test_booking_video_slots(self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock):
        # Scenario:
        # Booking 2 video visits deletes the rest of video and focused slots

        # Book a Video visit
        # Before rule there was 4 Video and 4 Video new and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(AppointmentReason.VIDEO, self.test_provider)
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 10)

        # Book another Video visit
        second_appointment: Appointment = self._get_first_slot_by_reason(AppointmentReason.VIDEO, self.test_provider)
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 2 Video-new slots, 0 Focused slots and 2 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            6,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 2)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 10)

    def test_booking_video_slots_for_provider_without_short_visits(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 video visits deletes the rest of video slots

        # Book a Video visit
        # Before rule there was 4 Video and 4 Video new slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_short_visit
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 6)

        # Book another Video visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_without_short_visit
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 2 Video-new slots and 2 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 2)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_without_short_visit.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_without_short_visit.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_without_short_visit.physician), 6)

    def test_booking_focused_slots(self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock):
        # Scenario:
        # Booking 2 focused visits deletes the rest of video and focused slots

        # Book a Focused visit
        # Before rule there was 4 Video and 4 Video new and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots, 5 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 11)

        # Book another Focused visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 2 Video-new slots, 0 Focused slots and 2 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 2)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots, 5 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 11)

    def test_booking_focused_slots_for_provider_with_only_established_visits(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 4 focused visits does not delete the rest of video and focused slots

        # Book a Focused visit
        # Before rule there was 4 Video and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_only_established
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 5 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9:45 - 10:15 : video slot
        # 10:30 - 11:00 : video slot
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 8)

        # Book another Focused visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_only_established
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 2 Video, 4 Focused slots and 2 Focused appointment
        # as per 100% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 10:30 - 11:00 : video slot
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 6)

        # Book another Focused visit
        third_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_only_established
        )
        third_appointment.patient = self.patient
        third_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            third_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=third_appointment)

        # After rule there should be 2 Video, 3 Focused slots and 3 Focused appointment
        # as per 100% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 10:30 - 11:00 : video slot
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (BOOKED)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            3,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 5)

        # Book another Focused visit
        fourth_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_only_established
        )
        fourth_appointment.patient = self.patient
        fourth_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            fourth_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=fourth_appointment)

        # After rule there should be 1 Video, 2 Focused slots and 4 Focused appointment
        # as per 100% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (BOOKED)
        # 10:30 - 10:45: focused slot (BOOKED)
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            4,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 3)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            fourth_appointment.cancel()
        fourth_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=fourth_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=fourth_appointment)
        fourth_appointment.refresh_from_db()
        self.assertIsNone(fourth_appointment.deleted)
        self.assertIsNotNone(fourth_appointment.physician)

        # After rule there should be 2 Video, 3 Focused slots and 3 Focused appointment
        # as per 100% capacity
        # 9 - 9:30 : video slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 10:30 - 11:00 : video slot
        # 11:15 - 11:45 : video slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (BOOKED)
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_only_established.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_only_established.physician
            ),
            3,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_only_established.physician), 5)

    def test_booking_video_slots_for_provider_with_shorter_shift(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 video visits deletes the rest of video and focused slots

        # Book a Video visit
        # Before rule there was 3 Video and 3 Video new and 4 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_with_shorter_shift
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 2 Video, 2 Video-new slots, 2 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 6)

        # Book another Video visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO, self.test_provider_with_shorter_shift
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 1 Video-new slots, 0 Focused slots and 2 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (BOOKED)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 1)

        # cancel video appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 2 Video, 2 Video-new slots, 2 Focused slots and 1 Video appointment
        # 9 - 9:30 : video slot (BOOKED)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (deleted)
        # 9:30 - 9:45: focused slot (deleted)
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 6)

    def test_booking_focused_slots_for_provider_with_shorter_shift(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Booking 2 focused visits deletes the rest of video and focused slots

        # Book a Focused visit
        # Before rule there was 3 Video and 3 Video new and 4 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_shorter_shift
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 2 Video, 2 Video-new slots, 3 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 7)

        # Book another Focused visit
        second_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider_with_shorter_shift
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 1 Video-new slots, 0 Focused slots and 2 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            3,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 1)

        # cancel focused appointment
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 2 Video, 2 Video-new slots, 3 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider_with_shorter_shift.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider_with_shorter_shift.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider_with_shorter_shift.physician), 7)

    def test_visit_mix_with_mulitple_days_shift_data(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # set next day shift start time 4 pm end time 5 PM
        next_day = self.three_days_after + timedelta(days=1)
        ShiftFactory.create(
            day_of_week=next_day.isoweekday(),
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("16:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("17:00:00", "%H:%M:%S"),
        )

        first_appointment: Appointment = self._get_first_slot_by_reason(
            AppointmentReason.VIDEO_NEW_PATIENT, self.test_provider
        )

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # No extra slots should be generated
        # Following slots should remain
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 14)

    def _get_slots_count(self, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=True,
        ).count()

    def _get_deleted_slots_count_by_reason(self, reason, physician):
        return Appointment.deleted_objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=True,
        ).count()

    def _get_booked_appointment_count_by_reason(self, reason, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=False,
        ).count()

    def _get_first_slot_by_reason(self, reason: AppointmentReason, provider: ProviderDetail):
        return (
            Appointment.objects.filter(
                physician=provider.physician,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("start")
            .first()
        )


@mock.patch("firefly.modules.appointment.signals.apply_appointment_rules_async.send_with_options")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
class TestFocusedVisitMixCapacity(FireflyTestCase):
    @mock.patch(
        "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
        return_value=StubBackend(),
    )
    @mock.patch.object(
        WindowRateLimiter,
        "_acquire",
        return_value=True,
    )
    def setUp(self, _acquire_mock, _mutex_mock):
        super().setUp()

        # create provider for test
        self.test_provider: ProviderDetail = ProviderDetailFactory.create(first_name="Test", last_name="Appointment")

        # set date 3 days after as within 36 hours rule will not run
        self.three_days_after = datetime.now() + timedelta(days=3)
        self.three_days_after_str = self.three_days_after.strftime("%Y-%m-%d")
        self.day_of_week = self.three_days_after.isoweekday()
        self.schedule_start_date = datetime.strptime("2024-01-01", "%Y-%m-%d")

        # Create mapping for focused visit, without configuring visit mix ratio
        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.focused_visit_type
        )

        # Configure visit mix ratio of New vs Est to be 60% vs 40%
        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.day_of_week,
            defaults={"percentage_of_slots": "60"},
        )

        type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.test_provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.day_of_week,
            defaults={"percentage_of_slots": "40"},
        )

        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.test_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        # set shift start time 9 Am end time 12 PM
        self.shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            start_time=datetime.strptime("09:00:00", "%H:%M:%S"),
            stop_time=datetime.strptime("12:00:00", "%H:%M:%S"),
        )

        # generate provider appointment slots
        with self.captureOnCommitCallbacks(execute=True):
            generate_slot_for_provider(
                log_prefix="",
                provider_id=self.test_provider.user_id,
                start_date=self.three_days_after_str,
                end_date=self.three_days_after_str,
                dry_run_off=True,
            )

        # Following slots should be generated
        # 9 - 9:30 : video slot
        # 9 - 9:30 : video-new slot
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 14)

    def test_applying_rule_to_focused_takes_video_visit_mix(
        self, publish_appointment_to_elation_async_mock, apply_appointment_rules_async_mock
    ):
        # Scenario:
        # Total Focused Appointments possible for the day = 6
        # 40% * (Total Focused Appointments possible for the day) = 2
        # Booking 2 focused visits deletes the rest of video and focused slots, because focused visits
        # consider visit mix % of parent type "Video" which is set to 40%

        # Book a Focused visit
        # Before rule there were 4 Video and 4 Video new and 6 focused slots and 0 appointment

        first_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        first_appointment.patient = self.patient
        first_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            first_appointment.save()

        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=first_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {first_appointment.pk}",
        )
        apply_appointment_rules_async_mock.assert_called_once_with(
            kwargs={
                "appointment_id": first_appointment.pk,
            },
            delay=2000,
        )
        apply_appointment_rules_async_mock.reset_mock()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=first_appointment)

        # After rule there should be 3 Video, 3 Video-new slots, 4 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 11)

        # Book another Focused visit
        second_appointment: Appointment = self._get_first_unbooked_slot_by_reason(
            AppointmentReason.FOCUSED_VISIT, self.test_provider
        )
        second_appointment.patient = self.patient
        second_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.save()

        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # After rule there should be 0 Video, 2 Video-new slots, 0 Focused slots and 2 Focused appointments
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot (deleted)
        # 9:45 - 10:15 : video-new slot (deleted)
        # 10:30 - 11:00 : video slot (deleted)
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot (deleted)
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot (BOOKED)
        # 10 - 10:15: focused slot (deleted)
        # 10:30 - 10:45: focused slot (deleted)
        # 11 - 11:15: focused slot (deleted)
        # 11:30 - 11:45: focused slot (deleted)

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            4,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            2,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 2)

        # cancel focused appointment, and all the Established visit slots (Focused and Video) should be released
        with self.captureOnCommitCallbacks(execute=True):
            second_appointment.cancel()
        second_appointment.refresh_from_db()
        with self.captureOnCommitCallbacks(execute=True):
            apply_appointment_rules(appointment=second_appointment)

        # Explicitly calling apply_rules as apply_rules will skip first time as no appointment slots exists
        # As captureOnCommitCallbacks call post save function after execution of whole function
        apply_rules(log_prefix="", appointment=second_appointment)
        second_appointment.refresh_from_db()
        self.assertIsNone(second_appointment.deleted)
        self.assertIsNotNone(second_appointment.physician)

        # After rule there should be 3 Video, 3 Video-new slots, 5 Focused slots and 1 Focused appointment
        # 9 - 9:30 : video slot (deleted)
        # 9 - 9:30 : video-new slot (deleted)
        # 9:45 - 10:15 : video slot
        # 9:45 - 10:15 : video-new slot
        # 10:30 - 11:00 : video slot
        # 10:30 - 11:00 : video-new slot
        # 11:15 - 11:45 : video slot
        # 11:15 - 11:45 : video-new slot
        # 9 - 9:15 : focused slot (BOOKED)
        # 9:30 - 9:45: focused slot
        # 10 - 10:15: focused slot
        # 10:30 - 10:45: focused slot
        # 11 - 11:15: focused slot
        # 11:30 - 11:45: focused slot

        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(
            self._get_deleted_slots_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO_NEW_PATIENT, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.VIDEO, physician=self.test_provider.physician
            ),
            0,
        )
        self.assertEqual(
            self._get_booked_appointment_count_by_reason(
                reason=AppointmentReason.FOCUSED_VISIT, physician=self.test_provider.physician
            ),
            1,
        )
        self.assertEqual(self._get_slots_count(physician=self.test_provider.physician), 11)

    def _get_slots_count(self, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            patient__isnull=True,
        ).count()

    def _get_deleted_slots_count_by_reason(self, reason, physician):
        return Appointment.deleted_objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=True,
        ).count()

    def _get_booked_appointment_count_by_reason(self, reason, physician):
        return Appointment.objects.filter(
            physician=physician,
            time_slot_type=SlotType.APPOINTMENT_SLOT,
            source=AppointmentSource.LUCIAN,
            reason=reason,
            patient__isnull=False,
        ).count()

    def _get_first_unbooked_slot_by_reason(self, reason: AppointmentReason, provider: ProviderDetail):
        return (
            Appointment.objects.filter(
                physician=provider.physician,
                source=AppointmentSource.LUCIAN,
                reason=reason,
                patient__isnull=True,
            )
            .order_by("start")
            .first()
        )
