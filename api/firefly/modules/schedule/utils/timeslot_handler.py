import logging
import zoneinfo
from datetime import datetime, timedelta
from math import floor
from typing import List, Optional, Set

from django.db.models.query import QuerySet

from firefly.core.user.models.models import ProviderDetail, User
from firefly.modules.appointment.constants import AppointmentSource, SlotType
from firefly.modules.appointment.models import Appointment
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.schedule.constants import CASE_CATEGORY_APPT_SLOT_UNAVAILABLE, SLOT_SIZE
from firefly.modules.schedule.models import (
    AppointmentSlots,
    AppointmentType,
    PhysicianAppointmentTypeMapping,
    ProviderSchedule,
    Shift,
    ShiftException,
    TimeSlot,
)

logger = logging.getLogger(__name__)


def generate_slots(
    all_dates: List[str],
    shift: Shift,
    log_prefix: str,
    provider: ProviderDetail,
    dry_run_off: Optional[bool] = False,
):
    schedule: Optional[ProviderSchedule] = shift.schedule
    slotsize_timedelta: timedelta = timedelta(minutes=SLOT_SIZE)
    BATCH_SIZE: int = 1000
    slot_objs = []
    if schedule:
        for selected_date in all_dates:
            tz = zoneinfo.ZoneInfo(str(schedule.timezone))
            shift_date = datetime.strptime(selected_date, "%m/%d/%Y").replace(tzinfo=UTC_TIMEZONE).date()
            shift_start: datetime = (
                datetime(
                    year=shift_date.year,
                    month=shift_date.month,
                    day=shift_date.day,
                    hour=shift.start_time.hour,
                    minute=shift.start_time.minute,
                    second=shift.start_time.second,
                ).replace(tzinfo=tz)
            ).astimezone(UTC_TIMEZONE)

            shift_stop: datetime = (
                datetime(
                    year=shift_date.year,
                    month=shift_date.month,
                    day=shift_date.day,
                    hour=shift.stop_time.hour,
                    minute=shift.stop_time.minute,
                    second=shift.stop_time.second,
                ).replace(tzinfo=tz)
            ).astimezone(UTC_TIMEZONE)

            # Find earliest slot within the shift
            # If shift starts at 9:32, the earliest slot should be at 9:35
            # with a slot size of 5 minutes
            s: int = floor(shift_start.minute / SLOT_SIZE)
            s = s + 1 if shift_start.minute % SLOT_SIZE > 0 else s
            slot_minutes: int = (s * SLOT_SIZE) % 60
            slot_hour: int = shift_start.hour if (s * SLOT_SIZE) / 60 < 1 else shift_start.hour + 1
            slot_datetime: datetime = datetime(
                year=shift_date.year,
                month=shift_date.month,
                day=shift_date.day,
                hour=slot_hour,
                minute=slot_minutes,
                second=0,
                tzinfo=UTC_TIMEZONE,
            )
            while slot_datetime >= shift_start and (slot_datetime + slotsize_timedelta) <= shift_stop:
                log_prefix_for_provider_slot: str = f"{log_prefix}: Provider: {provider.pk} Slot: {slot_datetime}"
                logger.info(
                    "%s: Starting",
                    log_prefix_for_provider_slot,
                )
                # Only create slots when one doesn't already exist
                slots: QuerySet[TimeSlot] = TimeSlot.objects.filter(
                    shift__schedule=shift.schedule, period__contains=slot_datetime
                )
                exceptions: QuerySet[ShiftException] = ShiftException.objects.filter(
                    schedule=shift.schedule,
                    period__contains=slot_datetime,
                )
                does_slot_already_exist: bool = slots.exists()
                does_slot_exist_inside_exception: bool = exceptions.exists()
                if does_slot_already_exist is False and does_slot_exist_inside_exception is False:
                    logger.info(
                        "%s: Will create slot",
                        log_prefix_for_provider_slot,
                    )
                    slot_objs.append(
                        TimeSlot(
                            shift=shift,
                            period=(
                                slot_datetime.astimezone(UTC_TIMEZONE),
                                (slot_datetime + slotsize_timedelta).astimezone(UTC_TIMEZONE),
                            ),
                        )
                    )
                else:
                    if does_slot_already_exist:
                        logger.info("%s: Skipping: Already exists", log_prefix_for_provider_slot)
                        # update timeslot to point new shift
                        slot = slots[0]
                        slot.shift = shift
                        slot.save(update_fields=["shift"])
                    if does_slot_exist_inside_exception:
                        logger.info("%s: Skipping: Exception exists", log_prefix_for_provider_slot)
                # Attempt next slot
                slot_datetime = slot_datetime + slotsize_timedelta
            if len(slot_objs) >= BATCH_SIZE and dry_run_off:
                TimeSlot.objects.bulk_create(objs=slot_objs)
                slot_objs = []
        if len(slot_objs) > 0 and dry_run_off:
            TimeSlot.objects.bulk_create(objs=slot_objs)


def generate_appointment_slots(
    all_slots: List[TimeSlot],
    provider: ProviderDetail,
    log_prefix: str,
    dry_run_off: Optional[bool] = False,
):
    # Get all appointment types supported by the provider
    physician_appointment_type_mappings: QuerySet[PhysicianAppointmentTypeMapping] = (
        PhysicianAppointmentTypeMapping.objects.filter(physician=provider.physician)
    )
    appointment_types_supported_by_provider: List[AppointmentType] = [
        mapping.appointment_type for mapping in physician_appointment_type_mappings
    ]
    slots: List[TimeSlot] = _remove_timeslots_with_appointments(
        provider=provider, slots=all_slots, log_prefix=log_prefix
    )
    logger.info("%s: Total slots count : %d", log_prefix, len(slots))
    for appointment_type in appointment_types_supported_by_provider:
        index: int = 0
        assert appointment_type.duration is not None
        slot_per_appt: int = int(appointment_type.duration / SLOT_SIZE)
        while index <= (len(slots) - slot_per_appt):
            create_slot: bool = True
            # check if consecutive time slots available for appointment
            for i in range(slot_per_appt - 1):
                if slots[index + i].period.upper != slots[index + i + 1].period.lower:
                    logger.info("%s: Consecutive slot exists", log_prefix)
                    create_slot = False
                    break
            if create_slot:
                # add appointment slots
                logger.info(
                    "%s: Will create appointment slot for provider %d at %s with reason as %s",
                    log_prefix,
                    provider.pk,
                    slots[index].period.lower,
                    appointment_type.unique_key,
                )
                if dry_run_off:
                    # create appointment slots if not exists
                    # this will not consider appointment deleted or not
                    # apply_appointment_rule will take care of that
                    Appointment.objects.get_or_create(
                        physician=provider.physician,
                        start=slots[index].period.lower,
                        reason=appointment_type.unique_key,
                        time_slot_type=SlotType.APPOINTMENT_SLOT,
                        duration=timedelta(minutes=appointment_type.duration),
                        visible=True,
                        source=AppointmentSource.LUCIAN,
                        status=None,
                    )
                if index < len(slots):
                    assert appointment_type.buffer_time_in_minutes is not None
                    # add buffer time between slots
                    end_time: datetime = (
                        slots[index].period.lower
                        + timedelta(minutes=appointment_type.duration)
                        + timedelta(minutes=appointment_type.buffer_time_in_minutes)
                    )
                    while index < len(slots) and slots[index].period.lower < end_time:
                        index = index + 1
            else:
                # increase index by 1
                index = index + 1


def _remove_timeslots_with_appointments(
    provider: ProviderDetail, slots: List[TimeSlot], log_prefix: str
) -> List[TimeSlot]:
    """
    Removes time slots that are associated with appointments or overlap with appointment times, including
    buffer time. This function iterates over the provided time slots and filters out those that are occupied
    by appointments, as well as any time slots that fall within the buffer time before or after the appointment.

    The function performs the following steps:
    1. It checks if any time slots have associated appointment slots.
    2. For each appointment, it calculates the start and end times, considering both the appointment's
       duration and its buffer time.
    3. Any time slots that overlap with the appointment's scheduled time, including its buffer time, are
       removed from the list.
    4. The function tracks the maximum duration and buffer time of appointments to minimize redundant checks
       when iterating through time slots.
    5. The result is a filtered list of time slots that are free of appointments or buffer overlaps.

    Parameters:
    - provider (ProviderDetail): The provider whose time slots are being checked for appointments.
    - slots (List[TimeSlot]): A list of time slots to be filtered based on appointment availability.
    - log_prefix (str): A prefix to be included in log messages for tracing.

    Returns:
    - List[TimeSlot]: A filtered list of time slots that do not have any appointments or overlap with appointment
      times, including buffer periods.
    """
    filtered_timeslots: List[TimeSlot] = slots
    logger.info("%s: Slots count : %d", log_prefix, len(slots))
    index: int = 0
    while index < len(slots):
        logger.info("%s: Slots id : %d", log_prefix, slots[index].id)
        """
            If time slot have appointment slot associates it will not consider those timeslots plus
            buffer time timeslots
        """
        if slots[index].appointment_slots.count() > 0:
            appointment_slots = AppointmentSlots.objects.filter(slot_id=slots[index].id)
            appointment_types: Set[AppointmentType] = set()
            for appointment_slot in appointment_slots:
                appointment_type = AppointmentType.objects.get(unique_key=appointment_slot.appointment.reason)
                logger.info(
                    "%s: Getting appointment types as %s: duration: %s, buffer_time: %s",
                    log_prefix,
                    appointment_type.unique_key,
                    appointment_type.duration,
                    appointment_type.buffer_time_in_minutes,
                )
                logger.info("%s: Time slot lower period : %s", log_prefix, slots[index].period.lower)
                # set start time slot start time - buffer time between slots
                assert appointment_type.buffer_time_in_minutes is not None
                assert appointment_type.duration is not None
                start_time: datetime = slots[index].period.lower - timedelta(
                    minutes=appointment_type.buffer_time_in_minutes
                )
                # set end time appointment end time + buffer time between slots
                end_time: datetime = slots[index].period.lower + timedelta(
                    minutes=appointment_type.duration + appointment_type.buffer_time_in_minutes
                )
                logger.info("%s: will not consider slots from : %s to %s", log_prefix, start_time, end_time)
                # remove those time slots
                filtered_timeslots = [
                    timeslot
                    for timeslot in filtered_timeslots
                    if not (timeslot.period.lower > start_time and timeslot.period.upper <= end_time)
                ]
                appointment_types.add(appointment_type)
            assert appointment_type.buffer_time_in_minutes is not None
            assert appointment_type.duration is not None
            # Getting the highest duration & buffer time supported by a provider, so that a redundant iteration
            # can be reduced on already removed time slots in filtered_timeslots
            maximum_duration = max(appt_type.duration for appt_type in appointment_types)
            maximum_buffer_time = max(appt_type.buffer_time_in_minutes for appt_type in appointment_types)
            index = index + int((maximum_duration + maximum_buffer_time) / SLOT_SIZE)
        else:
            logger.info("%s: No appointment slots linked for slot_id: %d", log_prefix, slots[index].id)
            index = index + 1

    return filtered_timeslots


def create_case_for_appt_slots_unavailable(
    user: User,
    reason: str,
    state: str,
    log_prefix: str,
):
    logger.info(
        "%s No appointment slots available of type %s for patient %d in state %s",
        log_prefix,
        reason,
        user.id,
        state,
    )

    case_category: CaseCategory = CaseCategory.objects.get(unique_key=CASE_CATEGORY_APPT_SLOT_UNAVAILABLE)
    description = f"No appointment slots available of type {reason}"

    Case.objects.create(
        category=case_category,
        person=user.person,
        description=description,
    )

    logger.info("%s: created appointment slots unavailable case for user %d", log_prefix, user.id)
