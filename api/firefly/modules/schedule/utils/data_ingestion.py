import logging
import zoneinfo
from datetime import date, datetime, time, timedelta
from typing import Any, Dict, List, Optional, TypedDict

from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.db.models.query import QuerySet
from django.utils import timezone
from django_stubs_ext import ValuesQuerySet
from psycopg2.extras import DateTimeTZ<PERSON>ange
from rest_framework import exceptions

from firefly.core.user.models.models import ProviderDetail
from firefly.core.user.utils import get_user_link_to_appointments
from firefly.modules.appointment.constants import (
    ESTABLISHED_PATIENT_APPOINTMENT_TYPES,
    NEW_PATIENT_APPOINTMENT_TYPES,
    SlotType,
)
from firefly.modules.appointment.models import (
    Appointment,
    AppointmentCancelationReason,
    AppointmentStatus,
)
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation
from firefly.modules.firefly_django.constants import NY_TIMEZONE
from firefly.modules.schedule.constants import (
    CASE_CATEGORY_CANCELLED_APPT_BY_PROVIDER,
    SCHEDULE_APPROVAL_CASE_REJECT_MULTIPLE_SCHEDULE_DESCRIPTION,
    SCHEDULE_APPROVAL_CASE_REJECT_SHIFT_EXCEPTION_OVERLAP,
    SCHEDULE_APPROVAL_CASE_REJECT_SHIFT_OVERLAP,
    SCHEDULE_APPROVAL_IMPACTED_APPOINTMENT_DETAILS,
    SCHEDULE_APPROVAL_IMPACTED_SLOT_DETAILS,
)
from firefly.modules.schedule.models import (
    DayOfWeek,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    ReviewStatus,
    ScheduleIngestionJob,
    Shift,
    ShiftException,
    StagingShift,
    StagingShiftException,
)
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)


class ProviderScheduleRow(TypedDict):
    provider_first_name: str
    provider_last_name: str
    day_of_week: str
    start_time: str
    stop_time: str
    effective_start_date: str
    effective_end_date: str


class ProviderVisitMixRow(TypedDict):
    provider_first_name: str
    provider_last_name: str
    day_of_week: str
    percentage_video_slots: str
    percentage_video_new_slots: str


class ProviderShiftExceptionRow(TypedDict):
    provider_first_name: str
    provider_last_name: str
    start_date_time: str
    stop_date_time: str


class ImpactedAppointment(TypedDict):
    id: int
    reason: str
    short_reason: str
    start_time: str
    affected_patient: Optional[int]
    patient_url: Optional[str]


class ScheduleImpactForPhysician(TypedDict):
    physician_name: str
    slot: List[ImpactedAppointment]
    appointment: List[ImpactedAppointment]


class ValidIngestion(TypedDict):
    is_valid: bool
    errors: List[str]


def get_all_impacted_appointments_for_ingestion_job(
    ingestion_job: ScheduleIngestionJob,
    log_prefix: Optional[str] = None,
) -> Dict[int, ScheduleImpactForPhysician]:
    if not log_prefix:
        log_prefix = f"get_all_impacted_appointments_for_ingestion_job: Ingestion job: {ingestion_job.job_name}"
    logger.info("%s: Starting", log_prefix)
    # For each physician
    #   find all active appointments on or after current time
    #   For each appointment
    #       Verify that an active shift exists for the physician
    #       Verify that no active shift exception exists
    # dictionary has physician id as the key and the impact of their
    # change in schedule as the value
    affected_appointments: Dict[int, ScheduleImpactForPhysician] = {}
    # Limit to the next 12 weeks
    # We only allow users to book 8 weeks out
    # See NEXT_AVAILABLE_SLOT_PERIOD_FOR_PLAN_ELECT_PERIOD
    max_appointment_date_to_calculate_impact: datetime = timezone.now() + timedelta(weeks=12)
    appointment: Appointment
    impact_details: str
    impacted_appointment: ImpactedAppointment
    for physician_id in (
        ingestion_job.staging_shifts.all().values_list("schedule__provider__physician", flat=True).distinct()
    ):
        log_prefix_for_physician = f"{log_prefix}: Physician: {physician_id}"
        logger.info("%s: Starting", log_prefix_for_physician)
        upcoming_appts_for_physician: QuerySet[Appointment] = (
            Appointment.objects.filter(
                start__gt=timezone.now(),
                physician_id=physician_id,
                time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
                start__lte=max_appointment_date_to_calculate_impact,
            )
            .exclude(
                status__in=[
                    AppointmentStatus.CANCELLED.value,
                    AppointmentStatus.CHECKED_OUT.value,
                    AppointmentStatus.COMPLETED.value,
                    AppointmentStatus.BILLED.value,
                ]
            )
            .order_by("-start", "-pk")
        )
        logger.info("%s: Found %d appointments", log_prefix_for_physician, upcoming_appts_for_physician.count())
        for appointment in upcoming_appts_for_physician.iterator():
            # appointment.physician is guaranteed to exist since appointments are filtered on physician id
            # the if condition is necessary for mypy
            if appointment.physician:
                physician_name: str = appointment.physician.name_with_credentials
                formatted_appt_time_in_newyork_tz: str = appointment.start.astimezone(NY_TIMEZONE).strftime(
                    "%A, %B %-d %I:%M %p %Z"
                )
                appointment_type: str = ""
                if appointment.patient:
                    appointment_type = "Appointment"
                else:
                    appointment_type = "Appointment Slot"
                log_prefix_for_physician_appointment: str = (
                    f"{log_prefix_for_physician}: {appointment_type}: {appointment.pk} Elation id"
                    f" {appointment.elation_id} Start:{formatted_appt_time_in_newyork_tz}"
                )
                # upstream validations ensure that a day is covered by a single shift
                effective_schedule_for_appointment: Optional[ProviderSchedule] = (
                    ProviderSchedule.objects.filter(
                        effective_period__contains=appointment.start,
                        provider__physician_id=physician_id,
                    )
                    .order_by("-created_at", "-id")
                    .first()
                )
                assert effective_schedule_for_appointment is not None
                appt_start = appointment.start.astimezone(
                    zoneinfo.ZoneInfo(effective_schedule_for_appointment.timezone)
                )
                appt_end = (appointment.start + appointment.duration).astimezone(
                    zoneinfo.ZoneInfo(effective_schedule_for_appointment.timezone)
                )
                appt_start_time = appt_start.strftime("%H:%M:%S")
                appt_end_time = appt_end.strftime("%H:%M:%S")
                day_of_week = appt_start.strftime("%A")
                day_of_week_iso: int = getattr(DayOfWeek, day_of_week.upper()).value
                applicable_shifts: QuerySet[StagingShift] = StagingShift.objects.filter(
                    schedule=effective_schedule_for_appointment,
                    day_of_week=day_of_week_iso,
                    start_time__lte=appt_start_time,
                    stop_time__gte=appt_end_time,
                    effective_period__contains=appointment.start,
                    ingestion_job=ingestion_job,
                )
                applicable_shift_exceptions: QuerySet[StagingShiftException] = StagingShiftException.objects.filter(
                    period__overlap=DateTimeTZRange(appointment.start, appointment.start + appointment.duration),
                    schedule=effective_schedule_for_appointment,
                    ingestion_job=ingestion_job,
                )
                does_shift_exist: bool = applicable_shifts.exists()
                does_shift_exception_exist: bool = applicable_shift_exceptions.exists()
                impact_reason: str = ""
                if not does_shift_exist:
                    impact_reason = "Out of shift"
                if does_shift_exception_exist:
                    impact_reason = "Provider Unavailable"
                if not does_shift_exist or does_shift_exception_exist:
                    if physician_id not in affected_appointments:
                        affected_appointments[physician_id] = {
                            "physician_name": physician_name,
                            "slot": [],
                            "appointment": [],
                        }
                    if does_shift_exception_exist:
                        staging_exception: Optional[StagingShiftException] = applicable_shift_exceptions.first()
                        if staging_exception:
                            logger.info(
                                "%s: Impacted. %s. Exception id: %d",
                                log_prefix_for_physician_appointment,
                                impact_reason,
                                staging_exception.pk,
                            )
                    else:
                        logger.info("%s: Impacted. %s", log_prefix_for_physician_appointment, impact_reason)
                    if not appointment.patient:
                        impact_details = SCHEDULE_APPROVAL_IMPACTED_SLOT_DETAILS.format(
                            appointment_start_time_in_est=formatted_appt_time_in_newyork_tz,
                            impact_reason=impact_reason,
                        )
                        impacted_appointment = {
                            "id": appointment.pk,
                            "reason": impact_details,
                            "short_reason": impact_reason,
                            "start_time": formatted_appt_time_in_newyork_tz,
                            "affected_patient": None,
                            "patient_url": None,
                        }
                        affected_appointments[physician_id]["slot"].append(impacted_appointment)
                    else:
                        impact_details = SCHEDULE_APPROVAL_IMPACTED_APPOINTMENT_DETAILS.format(
                            member_user_id=appointment.patient.pk,
                            appointment_start_time_in_est=formatted_appt_time_in_newyork_tz,
                            impact_reason=impact_reason,
                        )
                        impacted_appointment = {
                            "id": appointment.pk,
                            "reason": impact_details,
                            "short_reason": impact_reason,
                            "start_time": formatted_appt_time_in_newyork_tz,
                            "affected_patient": appointment.patient.pk,
                            "patient_url": get_user_link_to_appointments(patient_id=appointment.patient.pk),
                        }
                        affected_appointments[physician_id]["appointment"].append(impacted_appointment)
                else:
                    logger.info("%s: Not Impacted", log_prefix_for_physician_appointment)
        logger.info("%s: Complete", log_prefix_for_physician)
    logger.info("%s: Complete", log_prefix)
    return affected_appointments


def handle_impacted_appointment_for_ingestion_job(
    ingestion_job: ScheduleIngestionJob,
    log_prefix: Optional[str] = None,
):
    if not log_prefix:
        log_prefix = f"handle_impacted_appointment_for_ingestion_job: Ingestion job: {ingestion_job.job_name}"
    # Step 1: get all impacted appointments
    impacted_appointment_by_physicains: Dict[int, ScheduleImpactForPhysician] = (
        get_all_impacted_appointments_for_ingestion_job(
            ingestion_job=ingestion_job,
            log_prefix=log_prefix,
        )
    )
    for physician_id in impacted_appointment_by_physicains:
        logger.info(
            "%s: started handle impacted slots for physician: %s",
            log_prefix,
            impacted_appointment_by_physicains[physician_id]["physician_name"],
        )
        # Step 2: remove impacted slots
        remove_impacted_slots(
            ingestion_job=ingestion_job,
            impacted_slots=impacted_appointment_by_physicains[physician_id]["slot"],
            log_prefix=log_prefix,
        )
        logger.info(
            "%s: completed handle impacted slots for physician: %s",
            log_prefix,
            impacted_appointment_by_physicains[physician_id]["physician_name"],
        )
        logger.info(
            "%s: started handle impacted appointment for physician: %s",
            log_prefix,
            impacted_appointment_by_physicains[physician_id]["physician_name"],
        )
        # Step 3: create case for impacted appointments
        create_case_for_impacted_appointments(
            impacted_slots=impacted_appointment_by_physicains[physician_id]["appointment"],
            log_prefix=log_prefix,
        )
        logger.info(
            "%s: completed handle impacted appointment for physician: %s",
            log_prefix,
            impacted_appointment_by_physicains[physician_id]["physician_name"],
        )


def remove_impacted_slots(
    ingestion_job: ScheduleIngestionJob,
    impacted_slots: List[ImpactedAppointment],
    log_prefix: Optional[str],
):
    if not log_prefix:
        log_prefix = f"remove_impacted_slots: Ingestion job: {ingestion_job.job_name}"
    slot_ids: List[int] = [slot["id"] for slot in impacted_slots]
    logger.info("%s: will remove impacted slots: %s", log_prefix, ",".join(map(str, slot_ids)))
    impacted_appt_slots: QuerySet[Appointment] = Appointment.objects.filter(id__in=slot_ids)
    for impacted_slot in impacted_appt_slots.iterator():
        try:
            # cancel should delete appointment slots as well
            impacted_slot.cancel(
                canceled_by_system=True,
                cancelation_reason=AppointmentCancelationReason.PROVIDER_UNAVAILABLE,
            )
            if impacted_slot.deleted is None:
                impacted_slot.delete()
            logger.info("%s: removed appointment slots: %d", log_prefix, impacted_slot.pk)
        except Exception:
            logger.exception("%s: Failed to remove appointment slots: %d", log_prefix, impacted_slot.pk)


def create_case_for_impacted_appointments(
    impacted_slots: List[ImpactedAppointment],
    log_prefix: str,
):
    # Find if a case already exists for the cancel appointment - if not exist create
    case_category: CaseCategory = CaseCategory.objects.get(unique_key=CASE_CATEGORY_CANCELLED_APPT_BY_PROVIDER)
    appt_ids: List[int] = [appt["id"] for appt in impacted_slots]
    for appt in Appointment.objects.filter(id__in=appt_ids).iterator():
        reason = next(
            (impacted_slot["reason"] for impacted_slot in impacted_slots if impacted_slot["id"] == appt.id), None
        )
        case_relation: Optional[CaseRelation] = (
            CaseRelation.objects.filter(
                content_type=ContentType.objects.get_for_model(Appointment),
                object_id=appt.id,
                case__category=case_category,
                case__deleted__isnull=True,
            )
            .exclude(
                Q(case__status_category=StatusCategory.COMPLETE) | Q(case__status_category=StatusCategory.DEFERRED)
            )
            .first()
        )
        if case_relation is not None and case_relation.case is not None:
            logger.info("%s: skipping: case already exist for appointment %d", log_prefix, appt.id)
        else:
            assert appt.patient is not None
            appointment_cancellation_case = Case.objects.create(
                category=case_category,
                person=appt.patient.person,
                description=reason,
            )
            CaseRelation.objects.create(
                case=appointment_cancellation_case,
                content_object=appt,
            )
            logger.info("%s: created appointment cancellation case for appointment %d", log_prefix, appt.id)


def remove_staging_data(log_prefix: str):
    logger.info("%s: removing all unlinked or unreviewed staging shift data", log_prefix)
    StagingShift.objects.filter(shift__isnull=True).exclude(
        ingestion_job__review_status__in=[ReviewStatus.REVIEW_PENDING, ReviewStatus.APPROVED]
    ).delete()
    logger.info("%s: completed all unlinked or unreviewed staging shift data", log_prefix)
    logger.info("%s: removing all unlinked or unreviewed staging shift exception data", log_prefix)
    StagingShiftException.objects.filter(shift_exception__isnull=True).exclude(
        ingestion_job__review_status__in=[ReviewStatus.REVIEW_PENDING, ReviewStatus.APPROVED]
    ).delete()
    logger.info("%s: completed all unlinked or unreviewed staging shift exception data", log_prefix)


def reject_shift_data(
    ingestion_job: ScheduleIngestionJob,
    log_prefix: Optional[str] = None,
):
    if not log_prefix:
        log_prefix = f"reject_shift_data: Ingestion job: {ingestion_job.job_name}"
    logger.info("%s: Starting shift rejection", log_prefix)
    staging_shifts_queryset: QuerySet[StagingShift] = StagingShift.objects.filter(ingestion_job=ingestion_job)
    obsolete_staging_shifts_list: ValuesQuerySet[StagingShift, int] = staging_shifts_queryset.values_list(
        "id", flat=True
    )
    if len(obsolete_staging_shifts_list) > 0:
        logger.info(
            "%s: Will remove staging shift data: %s", log_prefix, ",".join(map(str, obsolete_staging_shifts_list))
        )
    logger.info("%s: Deleting redudant rows", log_prefix)
    staging_shifts_queryset.delete()
    logger.info("%s: Completed shift rejection", log_prefix)


def reject_shift_exception_data(
    ingestion_job: ScheduleIngestionJob,
    log_prefix: Optional[str] = None,
):
    if not log_prefix:
        log_prefix = f"reject_shift_exception_data: Ingestion job: {ingestion_job.job_name}"
    logger.info("%s: Starting exception rejection", log_prefix)
    staging_shift_exceptions_queryset: QuerySet[StagingShiftException] = StagingShiftException.objects.filter(
        ingestion_job=ingestion_job,
    )
    obsolete_staging_shift_exceptions_list: ValuesQuerySet[StagingShiftException, int] = (
        staging_shift_exceptions_queryset.values_list("id", flat=True)
    )
    if len(obsolete_staging_shift_exceptions_list) > 0:
        logger.info(
            "%s: Will remove staging shift data: %s",
            log_prefix,
            ",".join(map(str, obsolete_staging_shift_exceptions_list)),
        )
    logger.info("%s: Deleting redudant rows", log_prefix)
    staging_shift_exceptions_queryset.delete()
    logger.info("%s: Completed exception rejection", log_prefix)


def promote_staging_shifts_for_ingestion_job(
    ingestion_job: ScheduleIngestionJob,
    provider: Optional[ProviderDetail] = None,
    log_prefix: Optional[str] = None,
):
    if not log_prefix:
        log_prefix = f"promote_staging_shifts: Ingestion job: {ingestion_job.job_name}"
    staging_shift: StagingShift
    # Keep a track of shifts that were updated for this ingestion job
    # so that the remainder can be nuked
    logger.info("%s: Starting shift promotion", log_prefix)
    updated_shift_ids: List[int] = []
    for staging_shift in ingestion_job.staging_shifts.order_by("-created_at", "-id").iterator():
        if staging_shift.deleted is not None:
            logger.info("%s: Skipping deleted staging shift %d", log_prefix, staging_shift.pk)
            continue
        logger.info("%s: Promoting shift %d", log_prefix, staging_shift.pk)
        shift, _ = Shift.objects.update_or_create(
            day_of_week=staging_shift.day_of_week,
            start_time=staging_shift.start_time,
            stop_time=staging_shift.stop_time,
            effective_period=staging_shift.effective_period,
            schedule=staging_shift.schedule,
            defaults={
                "staging_shift": staging_shift,
            },
        )
        updated_shift_ids.append(shift.pk)
    if len(updated_shift_ids):
        obsolete_rows_queryset = Shift.objects.exclude(
            id__in=updated_shift_ids,
        )
        if provider:
            obsolete_rows_queryset = obsolete_rows_queryset.filter(schedule__provider=provider)
        obsolete_rows: ValuesQuerySet[Shift, int] = obsolete_rows_queryset.values_list("id", flat=True)
        if len(obsolete_rows) > 0:
            logger.info("%s: Found redudant rows: %s", log_prefix, ",".join(map(str, obsolete_rows)))
            logger.info("%s: Deleting redudant rows", log_prefix)
            obsolete_rows_queryset.delete()
    logger.info("%s: Completed shift promotion", log_prefix)


def is_shift_data_changed(ingestion_job: ScheduleIngestionJob):
    class SelectedColumnsForShift(TypedDict):
        day_of_week: int
        start_time: time
        stop_time: time
        effective_period: Any
        schedule_id: int

    new_data: QuerySet[StagingShift] = ingestion_job.staging_shifts.all()
    if ingestion_job.provider:
        new_data = new_data.filter(schedule__provider=ingestion_job.provider)
    new_data_values: ValuesQuerySet[StagingShift, SelectedColumnsForShift] = new_data.values(
        "day_of_week", "start_time", "stop_time", "effective_period", "schedule_id"
    )
    old_data: QuerySet[Shift] = Shift.objects.all()
    if ingestion_job.provider:
        old_data = old_data.filter(schedule__provider=ingestion_job.provider)
    old_data_values: ValuesQuerySet[Shift, SelectedColumnsForShift] = old_data.values(
        "day_of_week", "start_time", "stop_time", "effective_period", "schedule_id"
    )
    data_to_be_deleted = old_data_values.difference(new_data_values)
    data_to_be_added = new_data_values.difference(old_data_values)
    if data_to_be_deleted.count() == 0 and data_to_be_added.count() == 0:
        return False
    return True


def is_shift_exception_data_changed(ingestion_job: ScheduleIngestionJob):
    class SelectedColumnsForShiftException(TypedDict):
        period: Any
        schedule_id: int

    new_data: QuerySet[StagingShiftException] = ingestion_job.staging_shift_exceptions.all()
    new_data_values: ValuesQuerySet[StagingShiftException, SelectedColumnsForShiftException] = new_data.values(
        "period", "schedule_id"
    )
    if ingestion_job.provider:
        new_data = new_data.filter(schedule__provider=ingestion_job.provider)
    old_data: QuerySet[ShiftException] = ShiftException.objects.all()
    if ingestion_job.provider:
        old_data = old_data.filter(schedule__provider=ingestion_job.provider)
    old_data_values: ValuesQuerySet[ShiftException, SelectedColumnsForShiftException] = old_data.values(
        "period", "schedule_id"
    )
    data_to_be_deleted = old_data_values.difference(new_data_values)
    data_to_be_added = new_data_values.difference(old_data_values)
    if data_to_be_deleted.count() == 0 and data_to_be_added.count() == 0:
        return False
    return True


def does_shift_overlap(ingestion_job: ScheduleIngestionJob, log_prefix: str):
    staging_shift_queryset: QuerySet[StagingShift] = ingestion_job.staging_shifts.all()
    for staging_shift in staging_shift_queryset.iterator():
        # 1. get other staging shifts
        other_staging_shifts = StagingShift.objects.filter(
            ingestion_job=ingestion_job,
            schedule_id=staging_shift.schedule_id,
            day_of_week=staging_shift.day_of_week,
            effective_period__overlap=staging_shift.effective_period,
        ).exclude(id=staging_shift.id)
        # 2. Check for overlap with other staging shifts
        for other_staging_shift in other_staging_shifts.iterator():
            if _is_overlapping(
                staging_shift.start_time,
                staging_shift.stop_time,
                other_staging_shift.start_time,
                other_staging_shift.stop_time,
            ):
                logger.info(
                    "%s: shift data overlap id: %d and id: %d", log_prefix, staging_shift.id, other_staging_shift.id
                )
                return True
    return False


def does_shift_exception_overlap(ingestion_job: ScheduleIngestionJob):
    staging_shift_exception_queryset: QuerySet[StagingShiftException] = ingestion_job.staging_shift_exceptions.all()
    for staging_shift_exception in staging_shift_exception_queryset.iterator():
        # check shift exception overlap with other exception
        shift_exception_overlap = StagingShiftException.objects.filter(
            ingestion_job=ingestion_job,
            schedule_id=staging_shift_exception.schedule_id,
            period__overlap=staging_shift_exception.period,
        ).exclude(id=staging_shift_exception.id)
        if shift_exception_overlap.count() > 0:
            return True
    return False


def is_shift_exception_linked_to_multiple_schedule(ingestion_job: ScheduleIngestionJob, log_prefix: str):
    staging_shift_exception_queryset: QuerySet[StagingShiftException] = ingestion_job.staging_shift_exceptions.all()
    for exception in staging_shift_exception_queryset.iterator():
        exception_end_date = (
            exception.schedule.effective_period.upper
            if exception.schedule and exception.schedule.effective_period.upper
            else date.today() + timedelta(days=365)
        )
        if exception.schedule and (
            (exception.period.lower.date() < exception.schedule.effective_period.lower)
            or (exception.period.upper.date() > exception_end_date)
        ):
            logger.info("%s: shift exception: %d linked to multiple schedule", log_prefix, exception.pk)
            return True
    return False


def _is_overlapping(start1, end1, start2, end2):
    # Check if the start of one shift falls within the other shift's time range
    return (start1 < end2) and (start2 < end1)


def promote_staging_shift_exceptions_for_ingestion_job(
    ingestion_job: ScheduleIngestionJob,
    provider: Optional[ProviderDetail] = None,
    log_prefix: Optional[str] = None,
):
    if not log_prefix:
        log_prefix = f"promote_staging_shift_exceptions: Ingestion job: {ingestion_job.job_name}"
    staging_shift_exception: StagingShiftException
    # Keep a track of shift exceptions that were updated for this ingestion job
    # so that the remainder can be nuked
    logger.info("%s: Starting exception promotion", log_prefix)
    updated_shift_exception_ids: List[int] = []
    for staging_shift_exception in ingestion_job.staging_shift_exceptions.order_by("-created_at", "-id").iterator():
        if staging_shift_exception.deleted is not None:
            logger.info("%s: Skipping deleted staging shift exception %d", log_prefix, staging_shift_exception.pk)
            continue
        logger.info("%s: Promoting exception %d", log_prefix, staging_shift_exception.pk)
        shift_exception, _ = ShiftException.objects.update_or_create(
            schedule=staging_shift_exception.schedule,
            period=staging_shift_exception.period,
            defaults={
                "staging_exception": staging_shift_exception,
                "reason": staging_shift_exception.reason,
            },
        )
        updated_shift_exception_ids.append(shift_exception.pk)
    if len(updated_shift_exception_ids):
        obsolete_rows_queryset = ShiftException.objects.exclude(
            id__in=updated_shift_exception_ids,
        )
        if provider:
            obsolete_rows_queryset = obsolete_rows_queryset.filter(schedule__provider=provider)
        obsolete_rows: ValuesQuerySet[ShiftException, int] = obsolete_rows_queryset.values_list("id", flat=True)
        if len(obsolete_rows) > 0:
            logger.info("%s: Found redudant rows: %s", log_prefix, ",".join(map(str, obsolete_rows)))
            logger.info("%s: Deleting redudant rows", log_prefix)
            obsolete_rows_queryset.delete()
    logger.info("%s: Completed exception promotion", log_prefix)


def validate_staging_ingestion_data(ingestion_job: ScheduleIngestionJob, log_prefix: Optional[str] = None):
    if not log_prefix:
        log_prefix = f"validate_staging_shift_data: Ingestion job: {ingestion_job.job_name}"
    has_shift_data_changed: bool = is_shift_data_changed(ingestion_job)
    has_shift_exception_data_changed: bool = is_shift_exception_data_changed(ingestion_job)
    if not (has_shift_data_changed or has_shift_exception_data_changed):
        logger.info("%s: No change in rows found", log_prefix)
        return {"is_valid": True, "errors": []}
    # shift and shift exception can not overlap
    has_shift_overlap: bool = has_shift_data_changed and does_shift_overlap(ingestion_job, log_prefix)
    has_shift_exception_overlap: bool = has_shift_exception_data_changed and does_shift_exception_overlap(ingestion_job)
    has_shift_exception_linked_to_multiple_schedule: bool = (
        has_shift_exception_data_changed and is_shift_exception_linked_to_multiple_schedule(ingestion_job, log_prefix)
    )
    validation_errors = []
    if has_shift_overlap or has_shift_exception_overlap or has_shift_exception_linked_to_multiple_schedule:
        if has_shift_overlap:
            logger.info("%s: Shift data overlapping", log_prefix)
            validation_errors.append(SCHEDULE_APPROVAL_CASE_REJECT_SHIFT_OVERLAP)
        if has_shift_exception_overlap:
            logger.info("%s: Shift exception data overlapping", log_prefix)
            validation_errors.append(SCHEDULE_APPROVAL_CASE_REJECT_SHIFT_EXCEPTION_OVERLAP)
        if has_shift_exception_linked_to_multiple_schedule:
            logger.info("%s: Shift exception linked to multiple schedule", log_prefix)
            validation_errors.append(SCHEDULE_APPROVAL_CASE_REJECT_MULTIPLE_SCHEDULE_DESCRIPTION)
        return {"is_valid": False, "errors": validation_errors}
    return {"is_valid": True, "errors": []}


def get_ingestion_job_impact(ingestion_job: ScheduleIngestionJob, log_prefix: Optional[str] = None):
    if not log_prefix:
        log_prefix = f"get_ingestion_job_impact: Ingestion job: {ingestion_job.job_name}"
    ingestion_validation: ValidIngestion = validate_staging_ingestion_data(
        log_prefix=log_prefix,
        ingestion_job=ingestion_job,
    )

    impacted_appointments_for_physician: ScheduleImpactForPhysician = {
        "physician_name": "",
        "appointment": [],
        "slot": [],
    }
    if ingestion_validation["is_valid"]:
        impacted_appointments = get_all_impacted_appointments_for_ingestion_job(
            ingestion_job=ingestion_job, log_prefix=log_prefix
        )
        if len(impacted_appointments) > 0:
            impacted_appointments_for_physician = next(iter(impacted_appointments.values()))

    return {
        "validation_errors": ingestion_validation["errors"],
        "impacted_appointments": impacted_appointments_for_physician["appointment"],
        "impacted_appointment_slots": impacted_appointments_for_physician["slot"],
    }


def get_schedule_ingestion_job_from_case(case: Case):
    schedule_ingestion_job: Optional[ScheduleIngestionJob] = None
    relations: QuerySet[CaseRelation] = case.relations.filter(
        content_type=ContentType.objects.get_for_model(ScheduleIngestionJob)
    )
    schedule_ingestion_job_relation: Optional[CaseRelation] = relations.first() if relations else None
    if schedule_ingestion_job_relation:
        schedule_ingestion_job = schedule_ingestion_job_relation.content_object
    return schedule_ingestion_job


def promote_staging_schedule_for_ingestion_job(
    ingestion_job: ScheduleIngestionJob, log_prefix: Optional[str] = "promote_staging_schedule_data"
):
    from firefly.modules.schedule.tasks import lucian_slot_generator

    log_prefix = f"{log_prefix}: Ingestion job: {ingestion_job.job_name}"
    ingestion_job_provider_ids_list: List[int] = list(
        ingestion_job.staging_shifts.all().values_list("schedule__provider", flat=True).distinct()
    )
    # TODO: Handle impact of appointment fallout
    promote_staging_shifts_for_ingestion_job(
        ingestion_job=ingestion_job,
        provider=ingestion_job.provider,
        log_prefix=log_prefix,
    )
    promote_staging_shift_exceptions_for_ingestion_job(
        ingestion_job=ingestion_job,
        provider=ingestion_job.provider,
        log_prefix=log_prefix,
    )
    # find all impacted appointments
    handle_impacted_appointment_for_ingestion_job(
        ingestion_job=ingestion_job,
        log_prefix=log_prefix,
    )
    remove_staging_data(log_prefix=log_prefix)
    # this will generate provider slots for next 60 days
    # consume elation appointments
    # generate lucian appointment slots
    # del elation appointment slots for this provider
    lucian_slot_generator.send(
        provider_ids=",".join(map(str, ingestion_job_provider_ids_list)),
        dry_run_off=True,
        log_prefix=log_prefix,
        ingestion_job_id=ingestion_job.id,
    )


def update_visit_mix_for_not_accepting_new_patient_provider(provider: ProviderDetail):
    physician = provider.physician

    if physician:
        PhysicianVisitMixRatio.objects.filter(
            physician_appointment_type__physician=physician,
            physician_appointment_type__appointment_type__unique_key__in=NEW_PATIENT_APPOINTMENT_TYPES,
        ).update(percentage_of_slots=0)
        PhysicianVisitMixRatio.objects.filter(
            physician_appointment_type__physician=physician,
            physician_appointment_type__appointment_type__unique_key__in=ESTABLISHED_PATIENT_APPOINTMENT_TYPES,
        ).update(percentage_of_slots=100)


def validate_shift_data(shifts):
    yesterday = datetime.now().date() - timedelta(days=1)
    current_time = datetime.now().time()
    for shift in shifts:
        if shift.get("is_edited", False):
            if shift["effective_period"]["upper"] < yesterday:
                raise exceptions.ValidationError("Shift end date cannot be in the past")
            elif shift["effective_period"]["upper"] == yesterday and shift["start_time"] <= current_time:
                raise exceptions.ValidationError("Shift end date cannot be in the past")
    return True


def validate_shift_exception_data(shift_exceptions):
    today = datetime.now().date()
    for shift_exception in shift_exceptions:
        if shift_exception.get("is_edited", False):
            if shift_exception["period"]["upper"] < today:
                raise exceptions.ValidationError("Shift exception end date cannot be in the past")
    return True
