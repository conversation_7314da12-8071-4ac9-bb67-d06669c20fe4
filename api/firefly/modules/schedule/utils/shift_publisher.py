import logging
import zoneinfo
from datetime import date, datetime, time, timedelta
from typing import Any, Optional

from django.contrib.contenttypes.models import ContentType
from django.db.models.query import QuerySet
from googleapiclient.errors import HttpError
from rest_framework import status

from firefly.core.alias.models import Alias<PERSON>apping, AliasName
from firefly.core.services.google_calendar.client import GoogleCalendarClient, get_calendar_client
from firefly.core.services.google_calendar.types import CalendarEvent, EventSummary
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.schedule.models import Shift

logger = logging.getLogger(__name__)


def get_timezone_aware_shift_time(shift_time: time, day_of_week: int, provider_timezone: Any, effective_date: date):
    # use a start date of today so that timezone conversion uses conversion
    # find the next date where the day of week matches with the shift
    log_prefix: str = f"get_timezone_aware_shift_time Shift Time: {shift_time} Day of week: {day_of_week} \
        Effective date: {effective_date}: "
    logger.info("%s: Starting", log_prefix)
    start_date: date = date.today()
    while start_date.isoweekday() != day_of_week:
        start_date = start_date + timedelta(days=1)
    tz_aware_shift_time: datetime = datetime.combine(
        start_date,
        time(hour=shift_time.hour, minute=shift_time.minute),
        tzinfo=provider_timezone,
    )
    tz_aware_shift_time = datetime.strptime(
        tz_aware_shift_time.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
    ).replace(tzinfo=provider_timezone)
    # backport back to the date of the shift
    shift_date: date = effective_date
    while shift_date.isoweekday() != day_of_week:
        shift_date = shift_date + timedelta(days=1)
    tz_aware_shift_time = tz_aware_shift_time.replace(year=shift_date.year, month=shift_date.month, day=shift_date.day)
    logger.info("%s: Complete", log_prefix)
    return tz_aware_shift_time


def get_timezone_aware_shift_time_in_utc(
    shift_time: time, day_of_week: int, provider_timezone: Any, effective_date: date
):
    # use a start date of today so that timezone conversion uses conversion
    # find the next date where the day of week matches with the shift
    log_prefix: str = f"get_timezone_aware_shift_time_in_utc Shift Time: {shift_time} Day of week: {day_of_week} \
        Effective date: {effective_date}: "
    logger.info("%s: Starting", log_prefix)
    start_date: date = date.today()
    while start_date.isoweekday() != day_of_week:
        start_date = start_date + timedelta(days=1)
    tz_aware_shift_time: datetime = datetime.combine(
        start_date,
        time(hour=shift_time.hour, minute=shift_time.minute),
        tzinfo=provider_timezone,
    )
    tz_aware_shift_time = datetime.strptime(
        tz_aware_shift_time.strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"
    ).replace(tzinfo=provider_timezone)
    tz_aware_shift_time = tz_aware_shift_time.astimezone(UTC_TIMEZONE)
    # backport back to the date of the shift
    shift_date: date = effective_date
    while shift_date.isoweekday() != day_of_week:
        shift_date = shift_date + timedelta(days=1)
    tz_aware_shift_time = tz_aware_shift_time.replace(year=shift_date.year, month=shift_date.month, day=shift_date.day)
    logger.info("%s: Complete", log_prefix)
    return tz_aware_shift_time


def publish_shift_to_calendar(
    shift: Shift,
    event_generation_range_end_date: Optional[date] = None,
):
    log_prefix: str = f"publish_shift_to_calendar: Shift {shift.pk}:"
    if not (
        shift.schedule
        and shift.schedule.provider
        and shift.schedule.provider.physician
        and shift.schedule.provider.physician.calendar_url
        and shift.start_time
        and shift.stop_time
        and shift.effective_period.lower
    ):
        logger.info("%s: Missing mandatory attributes", log_prefix)
        return
    existing_calendar_event: Optional[str] = AliasMapping.get_alias_id_for_object(
        obj=shift,
        alias_name=AliasName.GOOGLE,
    )
    if existing_calendar_event:
        logger.info("%s: Already published to calendar skipping", log_prefix)
        return
    provider_timezone = zoneinfo.ZoneInfo(shift.schedule.timezone)
    shift_start_time = get_timezone_aware_shift_time(
        shift_time=shift.start_time,
        day_of_week=shift.day_of_week,
        effective_date=shift.effective_period.lower,
        provider_timezone=provider_timezone,
    )
    shift_end_time = get_timezone_aware_shift_time(
        shift_time=shift.stop_time,
        day_of_week=shift.day_of_week,
        effective_date=shift.effective_period.lower,
        provider_timezone=provider_timezone,
    )

    # Find the last ocurrence date, which is the end of the recurrence series
    if event_generation_range_end_date is not None:
        last_ocurrence_date = (
            event_generation_range_end_date
            if shift.effective_period.upper is None
            else min(event_generation_range_end_date, shift.effective_period.upper)
        )
    else:
        # We should get the last ocurrence date from the Shift
        # A Shift can be cofigured without an end date, which means it is an event which recurs indefinitely
        # The default is an infinite series of events
        last_ocurrence_date = None
        if shift.effective_period.upper:
            last_ocurrence_date = shift.effective_period.upper

    # Shifts are defined by a day-of-week and a time range within that day
    recurrence_rule = "RRULE:FREQ=WEEKLY"
    if last_ocurrence_date is not None:
        last_ocurrence_end_time: datetime = shift_end_time.replace(
            year=last_ocurrence_date.year, month=last_ocurrence_date.month, day=last_ocurrence_date.day
        )
        recurrence_rule += f";UNTIL={last_ocurrence_end_time.strftime('%Y%m%dT%H%M%SZ')}"
    calendar_client: GoogleCalendarClient = get_calendar_client()
    event: CalendarEvent = calendar_client.add_event_to_calendar(
        calendar_id=str(shift.schedule.provider.physician.calendar_url),
        start_time=shift_start_time,
        end_time=shift_end_time,
        timezone=shift.schedule.timezone,
        event_summary=EventSummary.SHIFT,
        recurrence_rule=recurrence_rule,
    )
    logger.info("%s: Will published to calendar. Event details: %s", log_prefix, event)
    if event:
        AliasMapping.set_mapping_by_object(
            obj=shift,
            alias_id=event["id"],
            alias_name=AliasName.GOOGLE,
        )
    logger.info("%s: Complete", log_prefix)


def remove_shift_from_calendar(shift: Shift):
    calendar_client: GoogleCalendarClient = get_calendar_client()
    log_prefix: str = f"remove_shift_from_calendar: Shift {shift.pk}:"
    if not (
        shift.schedule
        and shift.schedule.provider
        and shift.schedule.provider.physician
        and shift.schedule.provider.physician.calendar_url
    ):
        logger.info("%s: Missing mandatory attributes", log_prefix)
        return
    shift_google_calendar_alias_queryset: QuerySet[AliasMapping] = AliasMapping.objects.filter(
        content_type=ContentType.objects.get_for_model(Shift),
        object_id=shift.pk,
        alias_name=AliasName.GOOGLE,
    )
    if not shift_google_calendar_alias_queryset.exists():
        logger.info("%s: No event found. Skipping", log_prefix)
        return
    for shift_google_calendar_alias in shift_google_calendar_alias_queryset.iterator():
        try:
            removal_response: str = calendar_client.remove_event_from_calendar(
                calendar_id=str(shift.schedule.provider.physician.calendar_url),
                event_id=shift_google_calendar_alias.alias_id,
            )
            logger.info("%s: Removed event from calendar. Response: %s", log_prefix, str(removal_response))
            shift_google_calendar_alias.delete()
            logger.info("%s: Cleared alias", log_prefix)
        except HttpError as http_error:
            # https://developers.google.com/calendar/api/guides/errors#410_gone
            if http_error.status_code == status.HTTP_410_GONE:
                # event has already been deleted
                shift_google_calendar_alias.delete()
                logger.info("%s: Clearing alias since entry has already been deleted", log_prefix)
            elif http_error.status_code == status.HTTP_404_NOT_FOUND:
                # event was never created
                shift_google_calendar_alias.delete()
                logger.info("%s: Clearing alias since entry was never created", log_prefix)
            else:
                raise http_error
    logger.info("%s: Complete", log_prefix)
