import logging
import zoneinfo
from collections import defaultdict
from datetime import date, datetime, timedelta
from typing import TYPE_CHECKING, DefaultDict, Dict, List, Optional, TypedDict

from django.db.models import Count, DateTimeField, ExpressionWrapper, F, IntegerField, OuterRef, Subquery, TextField
from django.db.models.functions import Cast, TruncDate
from django.db.models.query import QuerySet

from firefly.core.feature.utils import is_flag_active_for_user
from firefly.modules.appointment.constants import (
    NEW_PATIENT_APPOINTMENT_TYPES,
    VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS,
    VISIT_TYPE_MAPPING_FOR_VISIT_TYPES,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.physician.models import Physician
from firefly.modules.schedule.constants import (
    CUSTOM_APPOINTMENT_ADDITIONAL_REASONS,
    SLOT_SIZE,
    SLOTS_NOT_AVAILABLE_REASONS,
    WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5,
)
from firefly.modules.schedule.models import (
    AppointmentSlots,
    AppointmentType,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    Shift,
    ShiftException,
    TimeSlot,
)
from firefly.modules.schedule.utils.data_ingestion import DateTimeTZRange
from firefly.modules.schedule.utils.rule_handler import apply_rules_for_timeslot
from firefly.modules.statemachines.utils import NY_TIMEZONE

if TYPE_CHECKING:
    # Avoid import cycles
    from firefly.core.user.models.models import ProviderDetail

slot_handler_logger: logging.Logger = logging.getLogger(__name__)


class GetAvailableSlotsResponse(TypedDict):
    provider_slot_map: dict[int, List[datetime]]
    reasons_for_unavailability: List[str]


def get_available_slots(
    provider_ids: List[int],
    start_date_time: datetime,
    end_date_time: datetime,
    reason: str,
    log_prefix: str,
    exclude_dates: Optional[List[date]] = None,
    logger: logging.Logger = slot_handler_logger,
    choosen_date: Optional[date] = None,
) -> GetAvailableSlotsResponse:
    from firefly.core.user.models.models import ProviderDetail

    """
    Retrieves the available appointment slots for a given provider within a specified date range
    based on a specific reason (appointment type).

    This function performs several steps to determine the available time slots:
    1. It fetches the provider's details and verifies if the provider has a valid mapping for the given
        appointment reason.
    2. It retrieves all time slots within the given date range.
    4. It filters out time slots that already have appointments scheduled.
    5. It applies the appointment rules (specific to the appointment type) to further refine the list of
        available slots.

    Args:
        provider_ids List[int]: The list of unique identifier of the provider (physician).
        start_date (str): The start date of the range for which available slots are to be retrieved,
            in "YYYY-MM-DD" format.
        end_date (str): The end date of the range for which available slots are to be retrieved,
            in "YYYY-MM-DD" format.
        reason (str): A string representing the appointment reason or type.
            This value is used to determine the appointment type and any associated rules.
        log_prefix (Optional[str]): A string used as a prefix for logging messages,
            to help identify log entries related to this function. Defaults to "get_available_slots".

    Returns:
        List[datetime]: A list of `datetime` objects representing the available slots within the given date range,
            after applying the appointment type rules. If no available slots are found, an empty list is returned.

    Example:
        available_slots = get_available_slots(
            provider_ids=[123],
            start_date="2024-11-01",
            end_date="2024-11-07",
            reason="video"
        )

        This would return a list of `datetime` objects representing available slots for the provider with ID 123
        within the date range from November 1, 2024 to November 7, 2024 for a "video" appointment.
    """
    reasons_for_unavailability: List[str] = []
    # get providers
    providers: QuerySet["ProviderDetail"] = ProviderDetail.objects.filter(user_id__in=provider_ids).prefetch_related(
        "user", "physician"
    )
    # get appointment type
    appointment_type: AppointmentType = AppointmentType.objects.get(unique_key=reason)
    provider_slot_map: dict[int, List[datetime]] = {}
    # get all available time slots for providers
    start_time_of_the_day = datetime(
        start_date_time.year, start_date_time.month, start_date_time.day, 0, 0, 0, tzinfo=NY_TIMEZONE
    )
    slots_mapping: Dict[int, List[TimeSlot]] = _get_all_time_slots_for_providers(
        providers=providers,
        start_date_time=start_time_of_the_day,
        end_date_time=end_date_time,
        log_prefix=log_prefix,
        exclude_dates=exclude_dates,
    )
    all_time_slots = [time_slot for provider_slots in slots_mapping.values() for time_slot in provider_slots]
    # Pre-fetch all relevant appointment slots in a single query
    appointment_slots: QuerySet[AppointmentSlots] = AppointmentSlots.objects.filter(
        slot_id__in=[slot.id for slot in all_time_slots], appointment__patient__isnull=False
    ).select_related("appointment")
    # Pre-fetch appointment types in one query
    appointment_types: QuerySet[AppointmentType] = AppointmentType.objects.filter(
        unique_key__in=[appointment_slot.appointment.reason for appointment_slot in appointment_slots]
    )
    # Convert the querysets to dictionaries for faster access
    appointment_type_dict: Dict[str, AppointmentType] = {apt.unique_key: apt for apt in appointment_types}
    appointment_slot_dict: Dict[int, AppointmentSlots] = {
        appointment_slot.slot_id: appointment_slot for appointment_slot in appointment_slots
    }

    unique_key: str = VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS.get(
        appointment_type.unique_key, appointment_type.unique_key
    )

    visit_mix_ratios = PhysicianVisitMixRatio.objects.filter(
        physician_appointment_type__physician__in=[provider.physician for provider in providers],
        physician_appointment_type__appointment_type__unique_key=unique_key,
    ).select_related("physician_appointment_type__physician")
    visit_mix_dict: DefaultDict[object, defaultdict[int, PhysicianVisitMixRatio]] = defaultdict(
        lambda: defaultdict(PhysicianVisitMixRatio)
    )

    for visit_ratio in visit_mix_ratios.iterator():
        # Extract provider, day_of_week from the visit_ratio object
        provider = visit_ratio.physician_appointment_type.physician
        day_of_week = visit_ratio.day_of_week

        # Populate the dictionary with the provider and day_of_week information
        visit_mix_dict[provider][day_of_week] = visit_ratio

    # exclude custom booked appointment
    excluded_appointments_subquery = (
        EventLog.objects.filter(
            type=EventTypeCodes.APPOINTMENT_SCHEDULED,
        )
        .annotate(target_object_id_int=Cast("target_object_id", IntegerField()))
        .filter(
            target_object_id=Cast(OuterRef("pk"), TextField()),
            metadata__current_appointment__custom_appointment=True,
        )
        .values_list("target_object_id_int", flat=True)
    )

    all_booked_appts: QuerySet[Appointment] = (
        Appointment.objects.filter(
            patient__isnull=False,
            status=AppointmentStatus.SCHEDULED.value,
            physician__in=[provider.physician for provider in providers],
            reason__in=VISIT_TYPE_MAPPING_FOR_VISIT_TYPES[appointment_type.unique_key],
            source=AppointmentSource.LUCIAN,
            time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
            start__date__gte=start_date_time,
            start__date__lte=end_date_time,
        )
        .exclude(pk__in=Subquery(excluded_appointments_subquery))
        .select_related("physician")
    )
    # Pre-fetch related AppointmentType instances in one batch
    appointment_types_data = AppointmentType.objects.filter(
        unique_key__in=[appt.reason for appt in all_booked_appts]
    ).values("unique_key", "duration", "buffer_time_in_minutes")
    booked_slots_dict: DefaultDict[Physician, DefaultDict[date, List[Appointment]]] = defaultdict(
        lambda: defaultdict(list)
    )
    app_type_dict: Dict[str, Dict[str, int]] = {
        apt["unique_key"]: {"duration": apt["duration"], "buffer_time_in_minutes": apt["buffer_time_in_minutes"]}
        for apt in appointment_types_data
    }

    for apt in all_booked_appts:
        if apt.physician:
            app_date = apt.start.date()
            unique_key = apt.reason
            booked_slots_dict[apt.physician][app_date].append(apt)
    time_slots = (
        TimeSlot.objects.filter(
            shift__schedule__provider__in=providers,
            period__startswith__gte=start_date_time,
            period__endswith__lt=end_date_time,
        )
        .annotate(date_only=TruncDate("period__startswith"))
        .values(
            "shift__schedule__provider",  # Group by provider
            "date_only",  # Group by unique date
        )
        .annotate(
            timeslot_count=Count("id", distinct=True)  # Count unique dates
        )
        .order_by("shift__schedule__provider", "date_only")
        .prefetch_related("shift", "schedule")
    )
    # Initialize a defaultdict to hold the desired structure
    provider_timeslot_dict: DefaultDict[int, DefaultDict[date, int]] = defaultdict(lambda: defaultdict(int))
    for record in time_slots:
        provider_id = record["shift__schedule__provider"]
        date_only = record["date_only"]
        timeslot_count = record["timeslot_count"]
        provider_timeslot_dict[provider_id][date_only] = timeslot_count

    for provider_detail in providers:
        is_flag_2_5_active_for_user = is_flag_active_for_user(
            flag_name=WAFFLE_FLAG_ENABLE_DYNAMIC_SCHEDULING_V2_5, user=provider_detail.user
        )
        all_slots: List[TimeSlot] | None = slots_mapping.get(provider_detail.user_id)
        all_available_slots: List[datetime] = []

        if all_slots and provider_detail.physician:
            logger.info("%s: total available slots count for the day: %d", log_prefix, len(all_slots))
            # get available slots after removing appointment and buffer time
            filtered_slots: List[TimeSlot] = _remove_existing_appointment_slots(
                slots=all_slots,
                appointment_type_dict=appointment_type_dict,
                appointment_slot_dict=appointment_slot_dict,
                log_prefix=log_prefix,
                logger=logger,
            )
            filtered_slots_ids = [slot.id for slot in filtered_slots]
            logger.info(
                "%s: total available slots for the day after removing appointment: %d", log_prefix, len(filtered_slots)
            )
            # get available slots
            available_slots: List[datetime] = _get_appointment_slots(
                slots=filtered_slots if is_flag_2_5_active_for_user else all_slots,
                appointment_type=appointment_type,
                filtered_slots=filtered_slots_ids,  # This param can be removed once we deprecate V2 flag
                log_prefix=log_prefix,
                logger=logger,
            )
            logger.info("%s: total available slots for the day : %d", log_prefix, len(available_slots))
            unique_dates = {slot.date() for slot in available_slots}
            is_timeslot_exists = True
            if choosen_date and not (available_slots and choosen_date in unique_dates):
                is_timeslot_exists = False

            # apply appointment rules
            all_available_slots = apply_rules_for_timeslot(
                available_slots=available_slots,
                appointment_type=appointment_type,
                provider=provider_detail,
                visit_mix_dict=visit_mix_dict.get(provider_detail.physician) if visit_mix_dict else None,
                app_type_dict=app_type_dict,
                booked_slots_dict=booked_slots_dict.get(provider_detail.physician) if booked_slots_dict else None,
                provider_timeslot_dict=provider_timeslot_dict.get(provider_detail.user_id)
                if provider_timeslot_dict
                else None,
                log_prefix=log_prefix,
                rule_logger=logger,
            )
            logger.info(
                "%s: total available slots for the day after applying appointment rules: %d",
                log_prefix,
                len(all_available_slots),
            )
            # Consider time slots from the start time provided
            all_available_slots = [slot for slot in all_available_slots if slot >= start_date_time]
            unique_dates = {slot.date() for slot in all_available_slots}
            if (
                not reasons_for_unavailability
                and choosen_date
                and (not all_available_slots or choosen_date not in unique_dates)
                and is_timeslot_exists
            ):
                visit_tye = (
                    "new patient visits" if reason in NEW_PATIENT_APPOINTMENT_TYPES else "established patient visits"
                )
                reasons_for_unavailability.append(SLOTS_NOT_AVAILABLE_REASONS["visit_mix_ratio_acheived"] % visit_tye)
            logger.info(
                "%s: total available slots from the start time %s, after applying appointment rules: %d",
                log_prefix,
                start_date_time,
                len(all_available_slots),
            )
            provider_slot_map[provider_detail.user_id] = sorted(all_available_slots)
            logger.info(
                "%s: Available slots: %s for provider_id: %s",
                log_prefix,
                provider_slot_map[provider_detail.user_id],
                provider_detail.user_id,
            )

    return {
        "provider_slot_map": provider_slot_map,
        "reasons_for_unavailability": reasons_for_unavailability,
    }


def _get_all_time_slots_for_providers(
    providers: QuerySet["ProviderDetail"],  # List of provider IDs
    start_date_time: datetime,
    end_date_time: datetime,
    log_prefix: str,
    exclude_dates: Optional[List[date]] = None,
) -> DefaultDict[int, List[TimeSlot]]:
    """
    Retrieves all time slots for multiple providers within a given date and time range.

    This function queries the database for available time slots associated with the provided providers' schedules
    and filters them to only include those that fall within the specified start and end date-time range.
    The resulting time slots are returned as a dictionary where keys are provider IDs and values are lists
    of `TimeSlot` objects for each provider.

    Args:
        provider_ids (List[int]): A list of provider IDs for whom the time slots are being retrieved.
        start_date_time (datetime): The starting date and time of the range within which time slots are to be retrieved.
        end_date_time (datetime): The ending date and time of the range within which time slots are to be retrieved.
        log_prefix (str): A string used as a prefix for logging messages to identify and track logs related to this
            function.

    Returns:
        Dict[int, List[TimeSlot]]: A dictionary mapping provider IDs to lists of available `TimeSlot` objects
        within the specified date-time range.
    """
    slots_by_provider: DefaultDict[int, List[TimeSlot]] = defaultdict(list)
    exclude_dates = exclude_dates or []
    # Query for time slots for all providers at once
    time_slots = (
        TimeSlot.objects.filter(
            shift__schedule__provider__in=providers,  # Filter by multiple provider IDs
            period__startswith__gte=start_date_time,
            period__endswith__lte=end_date_time,
        )
        .annotate(provider_id=F("shift__schedule__provider__user_id"))
        .annotate(upper_date=TruncDate("period__endswith"))
        .exclude(upper_date__in=exclude_dates)
        .order_by("period")
    )
    for time_slot in time_slots.iterator():
        if time_slot.provider_id:
            slots_by_provider[time_slot.provider_id].append(time_slot)

    return slots_by_provider


def _get_appointment_slots(
    slots: List[TimeSlot],
    appointment_type: AppointmentType,
    filtered_slots: List[int],
    log_prefix: str,
    logger: logging.Logger,
):
    """
    Filters and returns available time slots that can accommodate appointments based on the given appointment type.

    This function takes a list of time slots and determines which slots can be used for the requested appointment
    based on the appointment's duration and buffer time. The function checks if consecutive time slots can
    form a valid appointment slot, considering the specified duration and any buffer times required between
    appointments.

    Args:
        slots (List[TimeSlot]): A list of `TimeSlot` objects representing all available time slots. Each `TimeSlot`
            contains information about the start and end period of the slot.
        appointment_type (AppointmentType): The type of appointment for which slots are being checked. It contains
            information about the appointment's duration and buffer time.
        filtered_slots (List[TimeSlot]): A list of `TimeSlot` objects representing the available time slots after
        removing slots which booked by appointment.
        log_prefix (str): A string used as a prefix for logging messages to help identify and track logs related to this
            function.

    Returns:
        List[datetime]: A list of `datetime` objects representing the available time slots that meet the requirements
            for the given appointment type.
    """
    # Initialize index and compute slot requirements for the appointment
    index: int = 0
    # Calculate the number of slots required for the appointment type (without buffer time)
    slot_per_appt: int = int(appointment_type.duration / SLOT_SIZE)

    # Calculate the number of slots required for the appointment type (including buffer time)
    slots_with_buff: int = int((appointment_type.duration + appointment_type.buffer_time_in_minutes) / SLOT_SIZE)
    logger.info(
        "%s: slots per appointment reuired: %d slots with buff required: %d for appointment type: %s",
        log_prefix,
        slot_per_appt,
        slots_with_buff,
        appointment_type.unique_key,
    )
    available_slots: List[datetime] = []
    # Iterate over each slot in the list to check if a valid time range exists
    while index <= (len(slots) - slot_per_appt):
        slot_possible: bool = True
        # Check if the slots are continuous (no gaps between slots)
        for i in range(slot_per_appt - 1):
            if slots[index + i].period.upper != slots[index + i + 1].period.lower:
                slot_possible = False  # Set to False if slots are not continuous
                break
        if slot_possible:
            # If the slots are continuous, try to find a valid slot
            slot_time = slots[index].period.lower if slots[index].period.lower else None
            is_valid_slots = True
            # Calculate the time limit considering the appointment duration and buffer time
            time_with_buff = slots[index].period.lower + timedelta(
                minutes=appointment_type.duration + appointment_type.buffer_time_in_minutes
            )
            # Save the current index before considering additional slots
            index_before_considering_slot = index
            # Check if there are enough slots that are not filtered out (e.g., not already booked)
            while index < len(slots) and slots[index].period.lower < time_with_buff:
                # below check can be removed once we deprecate v2 flag
                if slots[index].id not in filtered_slots:
                    is_valid_slots = False
                index = index + 1

            # Check if the number of slots with buffer meets the required number
            if index - index_before_considering_slot < slots_with_buff:
                is_valid_slots = False

            # If the slot is valid, add the available slot
            if is_valid_slots and slot_time:
                available_slots.append(slot_time)
        else:
            # Move to the next slot if the current one cannot accommodate the appointment
            index = index + 1
    logger.info("%s: available slots: %s", log_prefix, available_slots)
    return available_slots


def _remove_existing_appointment_slots(
    slots: List[TimeSlot],
    appointment_slot_dict: Dict[int, AppointmentSlots],
    appointment_type_dict: Dict[str, AppointmentType],
    log_prefix: str,
    logger: logging.Logger,
) -> List[TimeSlot]:
    """
    Filters out time slots that are already occupied by existing appointments.

    This function iterates through a list of time slots and checks if any slot is
    already linked to an appointment. If an appointment exists for a slot, it
    calculates the appointment's duration and buffer time, and then removes all
    time slots that fall within the start and end time of that appointment (including
    buffer time). The filtered list of time slots is then returned.

    Parameters:
        slots (List[TimeSlot]): A list of `TimeSlot` objects representing available time slots.
        log_prefix (str): A string prefix for logging purposes, used to trace operations in logs.

    Returns:
        List[TimeSlot]: A filtered list of `TimeSlot` objects that do not overlap with existing appointments.

    """
    filtered_timeslots: List[TimeSlot] = slots
    logger.info("%s: Slots count : %d", log_prefix, len(slots))
    index: int = 0
    while index < len(slots):
        """
            If time slot have appointment slot associates it will not consider those timeslots plus
            buffer time timeslots
        """
        # Check if there's an appointment slot for the current slot
        appointment_slot = appointment_slot_dict.get(slots[index].id)
        if appointment_slot:
            appointment_type: AppointmentType | None = appointment_type_dict.get(appointment_slot.appointment.reason)
            if appointment_type:
                logger.info(
                    "%s: Getting appointment types as %s: duration: %s, buffer_time: %s",
                    log_prefix,
                    appointment_type.unique_key,
                    appointment_type.duration,
                    appointment_type.buffer_time_in_minutes,
                )
                # set start time slot start time - buffer time between slots
                assert appointment_type.buffer_time_in_minutes is not None
                assert appointment_type.duration is not None
                start_time: datetime = slots[index].period.lower - timedelta(
                    minutes=appointment_type.buffer_time_in_minutes
                )
                # set end time appointment end time + buffer time between slots
                end_time: datetime = slots[index].period.lower + timedelta(
                    minutes=appointment_type.duration + appointment_type.buffer_time_in_minutes
                )
                # remove those time slots
                filtered_timeslots = [
                    timeslot
                    for timeslot in filtered_timeslots
                    if not (timeslot.period.lower > start_time and timeslot.period.upper <= end_time)
                ]
                while index < len(slots) and slots[index].period.lower < end_time:
                    index = index + 1
            else:
                index = index + 1
        else:
            index = index + 1

    return filtered_timeslots


def get_addtional_reasons_for_custom_appointment(
    physician_id: int, appointment_date_time: str, reason: str, log_prefix=None
) -> List[str]:
    """
    Returns additional reasons for custom appoinment booking

    Args:
        physician_id: The ID of the physician for whom the appointment will be schedule.
        appointment_date_time: A string representing the start time of the appointment in "YYYY-MM-DDTHH:MM:SSZ" format
        reason: The unique key of the appointment type.
        log_prefix: An optional string to prepend to log messages for easier identification.

    Returns:
        A list of strings, where each string represents an additional reason for custom appointment.
        The list can include reasons:
            Outside provider visit shift
            An appointment exists already at this time
            Provider has an OOO exception during this time
    """
    log_prefix = (
        log_prefix + "get_addtional_reasons_for_custom_appointment: "
        if log_prefix
        else "get_addtional_reasons_for_custom_appointment: "
    )
    appointment_type: AppointmentType = AppointmentType.objects.get(unique_key=reason)
    appointment_start_time: datetime = datetime.strptime(appointment_date_time, "%Y-%m-%dT%H:%M:%SZ")
    appointment_end_time = appointment_start_time + timedelta(minutes=appointment_type.duration)
    additional_reasons_for_custom_appointment: List[str] = []

    # provider schedule
    provider_schedule = ProviderSchedule.objects.filter(
        provider__physician__id=physician_id,
        effective_period__contains=appointment_start_time.date(),
    ).first()
    if not provider_schedule:
        return additional_reasons_for_custom_appointment

    # convert to provider timezone
    appointment_start_time_provider_tz: datetime = appointment_start_time.astimezone(
        zoneinfo.ZoneInfo(provider_schedule.timezone)
    )
    appointment_end_time_provider_tz: datetime = appointment_end_time.astimezone(
        zoneinfo.ZoneInfo(provider_schedule.timezone)
    )
    day_of_week: int = appointment_start_time_provider_tz.isoweekday()

    # verify if appointment time out of shift
    is_shift_exception_exists = ShiftException.objects.filter(
        schedule=provider_schedule,
        period__overlap=DateTimeTZRange(appointment_start_time_provider_tz, appointment_end_time_provider_tz),
    ).exists()
    if is_shift_exception_exists:
        additional_reasons_for_custom_appointment.append(CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["shift_exception_exist"])

    # verify if any existing appointment overlap
    # check utc time as appointment in UTC time
    is_appointment_exists = (
        Appointment.objects.annotate(end=ExpressionWrapper(F("start") + F("duration"), output_field=DateTimeField()))
        .filter(
            patient__isnull=False,
            status=AppointmentStatus.SCHEDULED.value,
            end__gt=appointment_start_time,
            start__lt=appointment_end_time,
            physician__id=physician_id,
        )
        .exists()
    )
    if is_appointment_exists:
        additional_reasons_for_custom_appointment.append(CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"])

    if not is_shift_exception_exists:
        # verify if shift exception overlap
        is_shift_exists = Shift.objects.filter(
            day_of_week=day_of_week,
            schedule=provider_schedule,
            start_time__lte=appointment_start_time_provider_tz.time(),
            stop_time__gte=appointment_end_time_provider_tz.time(),
        ).exists()
        if not is_shift_exists:
            additional_reasons_for_custom_appointment.append(
                CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"]
            )

    return additional_reasons_for_custom_appointment
