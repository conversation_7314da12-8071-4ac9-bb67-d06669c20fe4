import logging
import zoneinfo
from datetime import date, datetime, timedelta
from typing import TYPE_CHECKING, DefaultDict, Dict, List, Optional, TypedDict

from django.db.models import Q
from django.db.models.query import QuerySet
from django.utils import timezone
from psycopg2.extras import DateRange

from firefly.modules.appointment.constants import (
    VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS,
    VISIT_TYPE_MAPPING_FOR_VISIT_TYPES,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.physician.models import Physician
from firefly.modules.schedule.constants import (
    APPOINTMENT_RATIO_CONFIG,
    RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW,
    SLOT_SIZE,
)

if TYPE_CHECKING:
    # Avoid import cycles
    from firefly.core.user.models.models import ProviderDetail
    from firefly.modules.schedule.models import AppointmentType, PhysicianVisitMixRatio


logger = logging.getLogger(__name__)


class AppointmentTypeData(TypedDict):
    duration: int
    buffer: int
    num_booked: int
    percentage: float
    all_visit_mix: Dict[int, float]


class AppointmentTimesData(TypedDict):
    end_time: datetime
    shift_start_time: datetime
    shift_end_time: datetime


def get_appointment_types_for_physician(physician: Physician, appt_time: datetime, is_multi_day: bool = False):
    from firefly.modules.schedule.models import PhysicianVisitMixRatio

    # Get the appointment types with longest duration as the blocks to count visit mix ratios
    longest_duration = 0
    longest_appointment_types = []
    all_appointment_types_data: Dict[str, AppointmentTypeData] = {}
    for appointment_type_mapping in physician.physician_appointment_type_mappings.iterator():
        appt_type = appointment_type_mapping.appointment_type
        appt_type_reason = appt_type.unique_key

        unique_key = (
            VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS[appt_type_reason]
            if appt_type_reason in VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS
            else appt_type_reason
        )
        percentage = APPOINTMENT_RATIO_CONFIG[unique_key] / 100

        # Fetch visit mix ratio for the parent unique key (New / Est / BH / HG)
        visit_mix = PhysicianVisitMixRatio.objects.filter(
            day_of_week=appt_time.isoweekday(),
            physician_appointment_type__physician=physician,
            physician_appointment_type__appointment_type__unique_key=unique_key,
        )
        if visit_mix.count() > 1:
            logger.error(
                "get_appointment_types_for_physician: Multiple visit mix ratios found for appointment type %s",
                unique_key,
            )
        applicable_visit_mix_ratio: Optional[PhysicianVisitMixRatio] = visit_mix.first()
        if applicable_visit_mix_ratio and applicable_visit_mix_ratio.percentage_of_slots is not None:
            percentage = applicable_visit_mix_ratio.percentage_of_slots / 100

        all_visit_mix: Dict[int, float] = {}
        if is_multi_day:
            # By default we send the percentages and mappings of the start date time
            # If it is multiday, lets consider all visit mixes
            for day_visit_mix in appointment_type_mapping.visit_mix_ratios.iterator():
                all_visit_mix[day_visit_mix.day_of_week] = (day_visit_mix.percentage_of_slots or 0) / 100

        all_appointment_types_data[appt_type_reason] = {
            "duration": appt_type.duration,
            "buffer": appt_type.buffer_time_in_minutes,
            "num_booked": 0,
            "percentage": percentage,
            "all_visit_mix": all_visit_mix,
        }

        if percentage == 0 and not is_multi_day:
            continue

        if appt_type.duration > longest_duration:
            longest_duration = appt_type.duration
            longest_appointment_types = [appt_type_reason]
        elif appt_type.duration == longest_duration:
            longest_appointment_types.append(appt_type_reason)

    return all_appointment_types_data, longest_appointment_types


def release_slot_at_given_time(
    appointment_reason: str,
    slot_of_required_type: Appointment,
    log_prefix: Optional[str] = "release_slot_at_given_time",
    dry_run_off: bool = False,
):
    from firefly.modules.appointment.elation import ElationAppointmentSync

    logger.info(
        "%s: %s: will un-delete appointment slot: %d at %s",
        log_prefix,
        appointment_reason,
        slot_of_required_type.pk,
        slot_of_required_type.start,
    )
    if not dry_run_off:
        return
    # Before we undelete the slot, we need to make sure that it is deleted in elation
    if slot_of_required_type.elation_id:
        logger.info(
            "%s: sync deleted appointment to Elation appointment_id: %d elation_id: %d",
            log_prefix,
            slot_of_required_type.pk,
            slot_of_required_type.elation_id,
        )
        try:
            ElationAppointmentSync().sync_appointment_to_elation(
                appointment=slot_of_required_type, log_prefix=log_prefix
            )
        except Exception:
            logger.exception("Failed to prcess appoinment cancellation sync to Elation")
    slot_of_required_type.elation_id = None

    # Appointment slots dont have any status. Thus, it should be cleared when undeleting
    slot_of_required_type.status = None
    slot_of_required_type.deleted = None
    slot_of_required_type.save(update_fields=["elation_id", "status", "deleted"])


def create_slot_at_given_time(
    appointment_reason: str,
    appointment_time: datetime,
    physician: Physician,
    duration: float,
    log_prefix: Optional[str] = "create_slot_at_given_time",
    dry_run_off: bool = False,
):
    logger.info(
        "%s: %s: will create appointment slot at time: %s",
        log_prefix,
        appointment_reason,
        appointment_time,
    )

    if not dry_run_off:
        return

    new_appt_slot: Appointment = Appointment.objects.create(
        physician=physician,
        reason=appointment_reason,
        start=appointment_time,
        source=AppointmentSource.LUCIAN,
        time_slot_type=SlotType.APPOINTMENT_SLOT,
        duration=timedelta(minutes=duration),
        visible=True,
        status=None,
    )
    logger.info(
        "%s: %s: created appointment slot: %d",
        log_prefix,
        appointment_reason,
        new_appt_slot.id,
    )


def create_or_release_slots_for_physician(
    physician: Physician,
    appointment_reason: str,
    all_appointment_types_data: Dict[str, AppointmentTypeData],
    start_date_time: datetime,
    end_date_time: datetime,
    log_prefix: str = "create_or_release_slots_for_physician",
    dry_run_off: bool = False,
):
    from firefly.modules.appointment.utils import (
        does_time_slot_exist_for_times,
        get_overlapping_appointments_for_shift,
    )
    from firefly.modules.schedule.models import ProviderSchedule, Shift

    is_same_day = end_date_time.date() == start_date_time.date()
    appointment_type_data = all_appointment_types_data[appointment_reason]
    if appointment_type_data["percentage"] == 0 and is_same_day:
        logger.info(
            "%s: %s: Visit mix percentage for %s is 0. Skipping", log_prefix, appointment_reason, start_date_time.date()
        )
        return

    date_range = DateRange(
        start_date_time.date(), start_date_time.date() + timedelta(days=1) if is_same_day else end_date_time.date()
    )

    # Get all shifts between these start and end date times, then generate expected time slots
    effective_schedules_for_physician: List[int] = list(
        ProviderSchedule.objects.filter(
            effective_period__overlap=date_range,
            provider=physician.provider,
        ).values_list("id", flat=True)
    )

    shifts_data: QuerySet[Shift] = Shift.objects.filter(
        schedule__in=effective_schedules_for_physician,
        effective_period__overlap=date_range,
    ).order_by("start_time")

    appointment_times: Dict[datetime, AppointmentTimesData] = {}
    appointment_duration = timedelta(minutes=appointment_type_data["duration"])
    appointment_length = appointment_duration + timedelta(minutes=appointment_type_data["buffer"])

    start_date = start_date_time.date()
    # For all shifts get all times at which appointment of this type should exist
    while start_date <= end_date_time.date():
        if not is_same_day:
            if start_date.isoweekday() in appointment_type_data["all_visit_mix"]:
                percentage = appointment_type_data["all_visit_mix"][start_date.isoweekday()]
            elif (
                start_date.isoweekday()
                in all_appointment_types_data[VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS[appointment_reason]][
                    "all_visit_mix"
                ]
            ):
                percentage = all_appointment_types_data[VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS[appointment_reason]][
                    "all_visit_mix"
                ][start_date.isoweekday()]
            else:
                percentage = 0
            # Skip releasing when visit mix for day and type is 0
            if percentage == 0:
                logger.info(
                    "%s: %s: Visit mix percentage for %s is 0. Skipping",
                    log_prefix,
                    appointment_reason,
                    start_date_time.date(),
                )
                start_date += timedelta(days=1)
                continue
        days_shifts = shifts_data.filter(day_of_week=start_date.isoweekday())
        for shift in days_shifts.iterator():
            assert shift.schedule is not None
            tz_supported_by_provider = zoneinfo.ZoneInfo(str(shift.schedule.timezone))
            shift_start_time = (
                datetime(
                    year=start_date.year,
                    month=start_date.month,
                    day=start_date.day,
                    hour=shift.start_time.hour,
                    minute=shift.start_time.minute,
                    second=shift.start_time.second,
                ).replace(tzinfo=tz_supported_by_provider)
            ).astimezone(UTC_TIMEZONE)
            shift_stop_time = (
                datetime(
                    year=start_date.year,
                    month=start_date.month,
                    day=start_date.day,
                    hour=shift.stop_time.hour,
                    minute=shift.stop_time.minute,
                    second=shift.stop_time.second,
                ).replace(tzinfo=tz_supported_by_provider)
            ).astimezone(UTC_TIMEZONE)

            # TODO: round off the shift start time like in generate slots for provider
            # Skipping for now since we have proper start times
            appt_start_time = shift_start_time
            while appt_start_time < end_date_time and appt_start_time + appointment_duration <= shift_stop_time:
                # Check if start or the end time of appointment is within range of the given time
                if appt_start_time >= start_date_time or appt_start_time + appointment_length > start_date_time:
                    appointment_times[appt_start_time] = {
                        "end_time": max(
                            min(appt_start_time + appointment_length, shift_stop_time),
                            appt_start_time + appointment_duration,
                        ),
                        "shift_start_time": shift_start_time,
                        "shift_end_time": shift_stop_time,
                    }
                appt_start_time += appointment_length
        start_date += timedelta(days=1)

    all_appointment_slots: QuerySet[Appointment] = Appointment.all_objects.filter(
        source=AppointmentSource.LUCIAN,
        start__in=list(appointment_times.keys()),
        time_slot_type=SlotType.APPOINTMENT_SLOT,
        physician=physician,
        reason=appointment_reason,
        patient__isnull=True,
    ).exclude(status=AppointmentStatus.CANCELLED.value)

    # For each start time, individually check if they need to be created or undeleted
    for start in appointment_times.keys():
        # If any actual open slot at this time exists, skip release and creation
        if all_appointment_slots.filter(start=start, deleted__isnull=True).exists():
            continue
        # Check for any overlapping booked appointments
        booked_overlapping_appointments = get_overlapping_appointments_for_shift(
            physician,
            Appointment.objects.filter(
                patient__isnull=False,
                time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
            ).exclude(status=AppointmentStatus.CANCELLED.value),
            start,
            appointment_times[start]["end_time"],
            appointment_times[start]["shift_start_time"],
            appointment_times[start]["shift_end_time"],
        )
        if booked_overlapping_appointments.exists():
            logger.info("%s: %s: Found overlapping appointment slot at time: %s", log_prefix, appointment_reason, start)
            continue
        if not does_time_slot_exist_for_times(
            physician, start, start + timedelta(minutes=appointment_type_data["duration"])
        ):
            logger.info(
                "%s: skip slot release as no appointment slots linked, for physician: %d, %s at %s",
                log_prefix,
                physician.pk,
                appointment_reason,
                start,
            )
            continue
        slot_of_required_type: Optional[Appointment] = all_appointment_slots.filter(start=start).order_by("-pk").first()
        if slot_of_required_type and slot_of_required_type.deleted is not None:
            release_slot_at_given_time(appointment_reason, slot_of_required_type, log_prefix, dry_run_off)
        elif not slot_of_required_type:
            create_slot_at_given_time(
                appointment_reason,
                start,
                physician,
                appointment_type_data["duration"],
                log_prefix,
                dry_run_off,
            )


def apply_appointment_capacity(appointment: Appointment, log_prefix: str):
    """
    Maintain visit type mix ratios
    """

    after_slot_release_window: datetime = timezone.now() + timedelta(hours=RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW)

    if appointment.start <= after_slot_release_window:
        # We don't care about making an update here because appointments within this window should be released
        # for any type of booking
        logger.info(
            "%s Skipped apply_appointment_capacity rule for appointment:%d because of release window rule",
            log_prefix,
            appointment.pk,
        )
        return

    # if this appointment not consumed in Lucian, or no physician, do not generate rule
    if appointment.appointment_slots.all().count() == 0 or not appointment.physician:
        logger.info(
            "%s: Skipping apply appointment rule for appointment id: %d as no appointment slots found",
            log_prefix,
            appointment.pk,
        )
        return

    # set start date appointment start date with 00:00:000 time to get all appointment slots
    # for the current day
    start_date = appointment.start.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1) - timedelta(minutes=1)

    # Get the appointment types with longest duration as the blocks to count visit mix ratios
    longest_appointment_types = []
    all_appointment_types_data: Dict[str, AppointmentTypeData] = {}
    assert appointment.physician is not None
    all_appointment_types_data, longest_appointment_types = get_appointment_types_for_physician(
        appointment.physician, appointment.start
    )

    all_appointments_for_the_day: QuerySet[Appointment] = Appointment.objects.filter(
        physician=appointment.physician,
        start__range=(start_date, end_date),
        time_slot_type__in=[SlotType.APPOINTMENT, SlotType.APPOINTMENT_SLOT],
    ).exclude(status=AppointmentStatus.CANCELLED.value)

    total_number_of_appointment_slots: int = (
        all_appointments_for_the_day.filter(Q(Q(reason__in=longest_appointment_types) | Q(patient__isnull=False)))
        .distinct("start")
        .count()
    )

    # Calculate booked slots for all appointment types
    for appt_type_reason in all_appointment_types_data.keys():
        all_appointment_types_data[appt_type_reason]["num_booked"] = all_appointments_for_the_day.filter(
            patient__isnull=False, reason__in=VISIT_TYPE_MAPPING_FOR_VISIT_TYPES[appt_type_reason]
        ).count()

    for appt_type_reason in all_appointment_types_data.keys():
        allowed_slots = round(
            (total_number_of_appointment_slots * all_appointment_types_data[appt_type_reason]["percentage"]), 0
        )
        logger.info(
            "[AppointmentRulesDebug]: %s: For physician %s and appointment type %s: Allowed slots=%s,\
Booked appointments=%s, Percentage=%s, All appointment slots for the day: %s",
            log_prefix,
            appointment.physician,
            appt_type_reason,
            allowed_slots,
            all_appointment_types_data[appt_type_reason]["num_booked"],
            all_appointment_types_data[appt_type_reason]["percentage"],
            total_number_of_appointment_slots,
        )
        if (
            all_appointment_types_data[appt_type_reason]["num_booked"] < allowed_slots
            or all_appointment_types_data[appt_type_reason]["percentage"] == 1
        ):
            # Create or Release deleted slots of this type
            logger.info("%s: %s: will release slots", log_prefix, appt_type_reason)
            create_or_release_slots_for_physician(
                appointment.physician,
                appt_type_reason,
                all_appointment_types_data,
                start_date,
                end_date,
                log_prefix=log_prefix,
                dry_run_off=True,
            )
        else:
            # Delete remaining slots of this type
            logger.info("%s: %s: will delete slots", log_prefix, appt_type_reason)
            open_appt_slots: QuerySet[Appointment] = all_appointments_for_the_day.filter(
                patient__isnull=True,
                reason=appt_type_reason,
                time_slot_type=SlotType.APPOINTMENT_SLOT,
            )
            for slot_of_required_type in open_appt_slots.iterator():
                logger.info("%s: %s: will delete slot: %d", log_prefix, appt_type_reason, slot_of_required_type.id)
                # Do not attempt a bulk update here - signals will be skipped
                slot_of_required_type.delete()


def apply_rules_for_timeslot(
    available_slots: List[datetime],
    appointment_type: "AppointmentType",
    provider: "ProviderDetail",
    visit_mix_dict: DefaultDict[int, "PhysicianVisitMixRatio"] | None,
    app_type_dict: Dict[str, Dict[str, int]] | None,
    booked_slots_dict: DefaultDict[date, List[Appointment]] | None,
    provider_timeslot_dict: DefaultDict[date, int] | None,
    log_prefix: str,
    rule_logger: logging.Logger = logger,
) -> List[datetime]:
    """
    Applies 36 hours rule and visit mix ratio rules to the provided available slots.

    The function first applies a 36-hour rule to filter out slots that are less than 36 hours.
    It then applies a "visit mix ratio" rule to the remaining slots.

    Parameters:
    - available_slots (List[datetime]): A list of available datetime slots to be processed.
    - provider (ProviderDetail): The provider whose time slots are checked for rules.
    - appointment_type (AppointmentType): The type of appointment for which slots are being checked. It contains
        information about the appointment's duration and buffer time.
    - log_prefix (str): A string used to prefix log messages for easier identification.

    Returns:
    - List[datetime]: A list of datetime slots after the rules have been applied.
    """
    logger = rule_logger

    slots_after_rule = []  # This will hold the final list of slots after applying rules.

    # Calculate the cutoff time (36 hours from the current time)
    end_date_time: datetime = timezone.now() + timedelta(hours=RELEASE_SLOTS_WITHIN_HOURS_FROM_NOW)

    # This list will hold the slots that need the "visit mix rule" applied
    slots_to_apply_rule: List[datetime] = []

    # Log the start of the 36 hours rule application
    logger.info("%s: Running 36 hours rule", log_prefix)

    # Loop through all the available slots and segregate them into slots that
    # are either before or after the 36-hour window
    for available_slot in available_slots:
        if available_slot > end_date_time:
            # Slot is more than 36 hours ahead, add it to the list for further processing
            slots_to_apply_rule.append(available_slot)
        else:
            # Slot is within the next 36 hours, add it to the final list
            slots_after_rule.append(available_slot)

    # Log the result of the 36-hour rule application
    logger.info(
        "%s: Slots within 36 hours: %d and slots after 36 hours: %d",
        log_prefix,
        len(slots_after_rule),
        len(slots_to_apply_rule),
    )

    # Log the start of the visit mix ratio rule application
    logger.info("%s: Running visit mix ratio rule", log_prefix)

    # If there are slots to apply the visit mix ratio to, process them
    if len(slots_to_apply_rule) > 0:
        # Apply visit mix ratio rule (assuming the function _apply_visit_mix_ratio is defined elsewhere)
        slots_after_rule = slots_after_rule + _apply_visit_mix_ratio(
            slots=slots_to_apply_rule,
            appointment_type=appointment_type,
            provider=provider,
            visit_mix_dict=visit_mix_dict,
            app_type_dict=app_type_dict,
            booked_slots_dict=booked_slots_dict,
            provider_timeslot_dict=provider_timeslot_dict,
            log_prefix=log_prefix,
            logger=logger,
        )

    # Return the final list of slots after all rules are applied
    return slots_after_rule


def _apply_visit_mix_ratio(
    slots: List[datetime],
    appointment_type: "AppointmentType",
    provider: "ProviderDetail",
    visit_mix_dict: DefaultDict[int, "PhysicianVisitMixRatio"] | None,
    app_type_dict: Dict[str, Dict[str, int]] | None,
    booked_slots_dict: DefaultDict[date, List[Appointment]] | None,
    provider_timeslot_dict: DefaultDict[date, int] | None,
    log_prefix: str,
    logger: logging.Logger = logger,
):
    """
    Applies visit mix ratio rules to the provided available slots.
    Parameters:
    - slots (List[datetime]): A list of available datetime slots to be processed.
    - appointment_type (AppointmentType): The type of appointment for which slots are being checked. It contains
        information about the appointment's duration and buffer time.
    - provider (ProviderDetail): The provider whose time slots are checked for rules.
    - log_prefix (str): A string used to prefix log messages for easier identification.
    Returns:
    - List[datetime]: A list of datetime slots after the rules have been applied.
    """

    log_prefix = log_prefix + ":_apply_visit_mix_ratio: "

    # Log the start of the visit mix rule application
    logger.info("%s: Running visit mix rule application", log_prefix)

    slots_available_after_rule: List[datetime] = []

    # Get the visit mix parent(New / Est / BH / HG) unique key
    unique_key: str = VISIT_TYPE_MAPPING_FOR_VISIT_MIX_RATIOS.get(
        appointment_type.unique_key, appointment_type.unique_key
    )

    # Get all the unique dates from slot list
    unique_dates = set(slot.date() for slot in slots)

    for chosen_date in unique_dates:
        logger.info("%s: Running visit mix rule for date: %s", log_prefix, chosen_date)
        # Get the visit mix ratio for the chosen day
        visit_mix = visit_mix_dict.get(chosen_date.isoweekday()) if visit_mix_dict else None
        if not visit_mix:
            # No configuration for this visit type
            logger.info(
                "%s: Provider %s: No Physician Visit Mix Ratio exists for %s on %s, skipping",
                log_prefix,
                provider.pk,
                unique_key,
                chosen_date,
            )
            continue

        if visit_mix.percentage_of_slots == 0:
            logger.info(
                ("%s: Provider %s: Visit mix ratio is 0 for %s on %s, skipping"),
                log_prefix,
                provider.pk,
                unique_key,
                chosen_date,
            )
            continue

        # Get total time available to conduct visits on the chosen date
        total_time_slots = provider_timeslot_dict.get(chosen_date) if provider_timeslot_dict else None
        total_time_in_mins: float = total_time_slots * SLOT_SIZE if total_time_slots else 0
        logger.info(
            ("%s: Provider %s: Total time for visits on %s for %s: %s"),
            log_prefix,
            provider.pk,
            chosen_date,
            appointment_type.unique_key,
            total_time_in_mins,
        )

        # Get the total mins of booked appts on the chosen date
        total_booked_time_in_mins: float = _get_total_booked_time_in_mins(
            app_type_dict=app_type_dict,
            booked_slots=booked_slots_dict.get(chosen_date) if booked_slots_dict else None,
        )
        logger.info(
            ("%s: Provider %s: Total booked time on %s for %s: %s"),
            log_prefix,
            provider.pk,
            chosen_date,
            appointment_type.unique_key,
            total_booked_time_in_mins,
        )

        # Check slot availability
        if total_time_in_mins <= 0:
            logger.info(
                ("%s: Provider %s: No shifts for visits on %s, skipping"),
                log_prefix,
                provider.pk,
                chosen_date,
            )
            continue

        given_date_slots: List[datetime] = []

        # Calculate the percentage
        achieved_percentage = (total_booked_time_in_mins / total_time_in_mins) * 100

        logger.info(
            ("%s: Provider %s: Achieved percentage on %s is %s for %s"),
            log_prefix,
            provider.pk,
            chosen_date,
            achieved_percentage,
            appointment_type.unique_key,
        )

        if visit_mix.percentage_of_slots > achieved_percentage:
            given_date_slots = _select_all_slots_for_given_date(
                slots=slots,
                selected_date=chosen_date,
                appointment_type=appointment_type,
                provider=provider,
                log_prefix=log_prefix,
                logger=logger,
            )
        else:
            logger.info(
                ("%s: Provider %s: Visit mix difference exceeds or percentage achieved on %s for %s, skipping"),
                log_prefix,
                provider.pk,
                chosen_date,
                appointment_type.unique_key,
            )
            continue

        slots_available_after_rule += given_date_slots

    return slots_available_after_rule


def _get_total_booked_time_in_mins(
    app_type_dict: Dict[str, Dict[str, int]] | None,
    booked_slots: List[Appointment] | None,
):
    """
    Returns the total booked time on the selected date
    Parameters:
    - selected_date (date): The selected date on which we need to calculate total booked time in mins.
    - provider (ProviderDetail): The provider whose booked appointments are checked for.
    - appointment_type (AppointmentType): The type of appointment for which appointments are being checked. It contains
        information about the appointment's duration and buffer time.
    Returns:
    - float: The total time for booked visits on the selected date.
    """

    total_booked_time_in_mins: float = 0

    if booked_slots:
        for booked_appt in booked_slots:
            # Look up the appointment type in the dictionary
            booked_appt_type = (
                app_type_dict.get(booked_appt.reason) if app_type_dict and booked_appt.reason in app_type_dict else None
            )
            if booked_appt_type:
                total_booked_time_in_mins = total_booked_time_in_mins + (
                    booked_appt_type["duration"] + booked_appt_type["buffer_time_in_minutes"]
                )

    return total_booked_time_in_mins


def _select_all_slots_for_given_date(
    slots: List[datetime],
    selected_date: date,
    appointment_type: "AppointmentType",
    provider: "ProviderDetail",
    log_prefix: str,
    logger: logging.Logger,
):
    """
    Extracts all the slots eligible for a given date
    Parameters:
    - slots (List[datetime]): A list of available datetime slots to be processed.
    - selected_date (date): The selected date for which slots needs to be shortlisted.
    - appointment_type (AppointmentType): The type of appointment for which visit mix is checked. It contains
        information about the appointment's duration and buffer time.
    - provider (ProviderDetail): The provider for whom the availability is checked.
    - log_prefix (str): A string used to prefix log messages for easier identification.

    Returns:
    - List(datetime): The slots available post applying the rule for a given date.
    """

    slots_available_after_rule = []

    for slot in slots:
        if slot.date() == selected_date:
            logger.info(
                ("%s: Provider %s: Slot on %s is available post visit mix rule for %s"),
                log_prefix,
                provider.pk,
                slot,
                appointment_type.unique_key,
            )
            slots_available_after_rule.append(slot)

    return slots_available_after_rule
