import zoneinfo

from dateutil.relativedelta import relativedelta
from django.core.validators import RegexValidator
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone
from django_deprecate_fields import deprecate_field

from firefly.core.services.flume.constants import COVERAGE_STATUS_CHOICES, RELATIONSHIP_CHOICES
from firefly.modules.eligibility.constants import EVENT_TYPE_CHOICES
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.insurance.models import EmployerGroup, InsuranceMemberInfo

from .data.zips import BOSTON_AREA_ZIP_CODES, MA_ZIP_CODES

TIMEZONES = []
for tz in sorted(zoneinfo.available_timezones()):
    # Append the timezone name twice to create timezone choice
    TIMEZONES.append((tz, tz))


# DEPRECATED
class EligibilitySubmission(BaseModelV3):
    dob = deprecate_field(models.DateField())

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip = deprecate_field(models.CharField(max_length=5))  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    timezone = deprecate_field(models.CharField(max_length=100, choices=TIMEZONES, default="America/New_York"))  # noqa: TID251

    @property
    def in_MA(self):
        return self.zip in MA_ZIP_CODES

    @property
    def in_boston_area(self):
        return self.zip in BOSTON_AREA_ZIP_CODES

    @property
    def is_18(self):
        user_timezone = zoneinfo.ZoneInfo(self.timezone)
        date = timezone.now().replace(tzinfo=user_timezone).date()
        return relativedelta(date, self.dob).years >= 18

    class Meta(BaseModelV3.Meta):
        db_table = "eligibility_submissions"


# DEPRECATED
class IneligiblePatient(BaseModelV3):
    REASON_CHOICES = (("Insurance", "Insurance"), ("Location", "Location"), ("Age", "Age"))

    phone_regex = deprecate_field(
        RegexValidator(regex=r"^\+?1?\d{9,15}$", message="Phone number format: '+**********'.")
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_number = deprecate_field(models.CharField(validators=[phone_regex], max_length=17, unique=True))  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason = deprecate_field(models.CharField(max_length=10, choices=REASON_CHOICES))  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unrecognized_provider = deprecate_field(models.CharField(max_length=255, blank=True))  # noqa: TID251

    eligibility_submission = deprecate_field(
        models.OneToOneField(
            EligibilitySubmission,
            related_name="ineligible_patient",
            blank=True,
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    insurance_info = deprecate_field(
        models.OneToOneField(
            InsuranceMemberInfo,
            related_name="ineligible_patient",
            blank=True,
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    class Meta(BaseModelV3.Meta):
        db_table = "ineligible_patients"


class EligibilityProcessingStatus:
    SUCCESS = "success"
    FAILED_VALIDATION = "failed_validation"
    FAILED = "failed"


class EligibilitySourceType:
    WEBHOOK = "webhook"
    CSV = "csv"


class TpaVendors:
    VITORI = "vitori"


TPA_VENDOR_CHOICES = [(TpaVendors.VITORI, TpaVendors.VITORI)]


ELIGIBILITY_PROCESSING_STATUS_CHOICES = [
    (EligibilityProcessingStatus.SUCCESS, EligibilityProcessingStatus.SUCCESS),
    (EligibilityProcessingStatus.FAILED, EligibilityProcessingStatus.FAILED),
    (
        EligibilityProcessingStatus.FAILED_VALIDATION,
        EligibilityProcessingStatus.FAILED_VALIDATION,
    ),
]

ELIGIBILITY_SOURCE_TYPE_CHOICES = [
    (EligibilitySourceType.WEBHOOK, EligibilitySourceType.WEBHOOK),
    (EligibilitySourceType.CSV, EligibilitySourceType.CSV),
]


# Record of Flume sending the eligibility CSV through SFTP
class EligibilityRecord(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    block_of_business_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    group = models.ForeignKey(
        EmployerGroup,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="eligibility",
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    employee = models.ForeignKey(
        "user.Person",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="employee_eligibility",
        null=True,
        blank=True,
    )
    member = models.OneToOneField(
        "user.Person",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="member_eligibility",
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    email = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_number = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    member_coverage_product = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_level = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    coverage_status = models.CharField(max_length=255, null=True, blank=True, choices=COVERAGE_STATUS_CHOICES)  # noqa: TID251
    effective_date = models.DateTimeField(null=True, blank=True)
    termination_date = models.DateTimeField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    gender = models.CharField(max_length=25, null=True, blank=True)  # noqa: TID251


# Event Log to capture any Eligibility action (both CSV and Webhook) from Flume
class EligibilityLog(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    person = models.ForeignKey("user.Person", on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event_id = models.CharField(max_length=50)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event_type = models.CharField(max_length=50)  # noqa: TID251
    event_timestamp = models.DateTimeField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event_source = models.CharField(  # noqa: TID251
        max_length=50,
        blank=True,
        null=True,
        choices=ELIGIBILITY_SOURCE_TYPE_CHOICES,
        default=None,
    )

    data = JSONField()
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    processing_status = models.CharField(  # noqa: TID251
        max_length=50,
        blank=True,
        null=True,
        choices=ELIGIBILITY_PROCESSING_STATUS_CHOICES,
        default=None,
    )
    vendor = models.TextField(
        max_length=50,
        blank=True,
        null=True,
        choices=TPA_VENDOR_CHOICES,
        default=None,
    )
    member_id = models.TextField(
        max_length=50,
        blank=True,
        null=True,
    )

    class Meta(BaseModelV3.Meta):
        db_table = "eligibility_logs"


class CoveredMember(BaseModelV3):
    primary_subscriber_person = models.ForeignKey(
        "user.Person",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="covered_by",
        null=True,
        blank=True,
    )
    covered_member_person = models.OneToOneField(
        "user.Person",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="covered_members",
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    relationship = models.CharField(max_length=255, choices=RELATIONSHIP_CHOICES, default="Other")  # noqa: TID251


class EligibilityEvent(BaseModelV3):
    eligibility_log = models.ForeignKey(
        EligibilityLog,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="eligibility_log",
        null=True,
        blank=True,
    )
    event_type = models.TextField(max_length=50, choices=EVENT_TYPE_CHOICES, default="None")
    changed_values = JSONField()
    processing_status = models.TextField(
        max_length=50,
        blank=True,
        null=True,
        choices=ELIGIBILITY_PROCESSING_STATUS_CHOICES,
        default=None,
    )
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    person = models.ForeignKey("user.Person", on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251
    member_id = models.TextField(max_length=50, null=False, blank=False)
