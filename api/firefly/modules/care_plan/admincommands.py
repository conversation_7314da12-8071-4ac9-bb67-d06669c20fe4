from django import forms

from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand


class BackfillCreateCarePlan(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        person_ids = forms.Char<PERSON>ield(help_text="Comma separated list: run on individual users", required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["person_ids"]:
            args.append(["--person_ids", data["person_ids"]])
        return args, {"user": user}


class RemoveTasksFromCarePlan(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        task_title = forms.Char<PERSON>ield(required=True, help_text="Task title")
        care_plan_template_id = forms.IntegerField(required=True)

    def get_command_arguments(self, data, user):
        args = ["-task_title", data["task_title"], "-care_plan_template_id", data["care_plan_template_id"]]
        return args, {"user": user}
