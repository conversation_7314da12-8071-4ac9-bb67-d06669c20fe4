from __future__ import annotations

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.db import models
from django.db.models import Q, QuerySet

from firefly.modules.care_plan.constants import (
    CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT,
    CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT,
    DATA_TRACKING_CHOICES,
)
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.quality.models import MeasureCollection
from firefly.modules.statemachines.mixin import WorkUnitStateMachineMixin
from firefly.modules.tasks.models import TaskCollection, TaskRelation
from firefly.modules.work_units.constants import StatusCategory
from firefly.modules.work_units.models import WorkUnit


class EducationResource(BaseModelV3):
    """
    Store resource title & URL
    """

    title = models.TextField(
        help_text="Resource title",
        null=False,
        blank=False,
        unique=True,
    )
    url = models.TextField(
        help_text="URL mapping with the mentioned title",
        null=False,
        blank=False,
        unique=True,
    )


class CarePlanType(BaseModelV3):
    """A specific type of care plan, such as staying_healthy or appointment."""

    # TODO: Should we limit choices (such as 'staying_healthy', 'appointment')?
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, unique=True, db_index=True)  # noqa: TID251
    description = models.TextField(help_text="Internal-facing description of the purpose of the related Care Plans")

    def __str__(self):
        return f"{self.uid}: CarePlanType({self.pk})"


class CarePlanTemplate(BaseModelV3):
    """A template for a common care plan across patients."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255, help_text="Patient-facing title of the Care Plan")  # noqa: TID251
    notes = models.TextField(blank=True, help_text="Patient-facing notes about the Care Plan")
    type = models.ForeignKey(
        CarePlanType,
        db_index=True,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="care_plan_templates",
    )
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    task_collection = models.ForeignKey(TaskCollection, on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251
    can_be_manually_assigned = models.BooleanField(null=True, blank=True)

    episodic = models.BooleanField(
        null=True,
        blank=True,
        help_text="If true, the template can be assigned multiple times for a member",
    )
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    measure_collection = models.ForeignKey(MeasureCollection, on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251
    is_case_based_care_plan = models.BooleanField(
        null=True,
        blank=True,
        help_text="If true, the care plan will be a case based one",
    )


class CarePlan(WorkUnit, WorkUnitStateMachineMixin):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255)  # noqa: TID251

    patient = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="care_plans", on_delete=models.CASCADE)

    visible_to_patient = models.BooleanField(default=True, db_index=True)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="created_care_plans",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Notes are patient-facing
    notes = models.TextField(blank=True)

    care_plan_tasks = GenericRelation(TaskRelation)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    care_plan_data_tracking = models.CharField(  # noqa: TID251
        max_length=50,
        blank=True,
        null=True,
        choices=DATA_TRACKING_CHOICES,
    )

    education_resources = BaseModelV3ManyToManyField(
        EducationResource,
        related_name="care_plans",
        through="CarePlanEducationResource",
    )
    care_plan_education_resources: QuerySet["CarePlanEducationResource"]

    type = models.ForeignKey(
        CarePlanType,
        db_index=True,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="care_plans",
    )

    autocreated_from = models.ForeignKey(
        CarePlanTemplate,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="care_plans",
    )

    case_relations = GenericRelation("cases.CaseRelation")

    class Meta(WorkUnit.Meta):
        db_table = "care_plans"

    def save(self, *args, **kwargs):
        created = self.pk is None

        # Set the default state machine for newly created care plans
        if created:
            if self.autocreated_from and self.autocreated_from.is_case_based_care_plan:
                self.state_machine_content = CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT
            else:
                self.state_machine_content = CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT

        super(CarePlan, self).save(*args, **kwargs)

    def state_machine_post_action(self, *args):
        from firefly.modules.work_units.utils import WORKUNIT_COMPLETION_STATUSES

        # If a care plan is marked complete and it has open cases or active tasks then:
        # - Delink the remaining open cases.
        # - Delete the pending active tasks.
        if self.status_category == StatusCategory.COMPLETE:
            # Remove all the case relations between cases and care plans to delink cases from care plans.
            case_relations = self.case_relations.exclude(case__status_category__in=WORKUNIT_COMPLETION_STATUSES)
            for case_relation in case_relations.iterator():
                case_relation.delete()

            # Delete all the active tasks
            active_task_relations = self.care_plan_tasks.filter(task__is_complete=False)
            for active_task_relation in active_task_relations.iterator():
                active_task_relation.task.delete()

    @property
    def subject(self):
        return self.patient


class CarePlanEducationResource(BaseModelV3):
    """
    Links resource to care_plans
    """

    education_resource = models.ForeignKey(
        EducationResource, related_name="care_plan_education_resources", on_delete=models.CASCADE
    )

    care_plan = models.ForeignKey(CarePlan, related_name="care_plan_education_resources", on_delete=models.CASCADE)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["education_resource", "care_plan"],
                condition=Q(deleted=None),
                name="care_plan_education_resource_uniq",
            )
        ]
