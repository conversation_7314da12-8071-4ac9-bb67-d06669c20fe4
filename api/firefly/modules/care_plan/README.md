# Care Plans

## Motivation

Care plans provide a structured approach to patient care coordination by organizing tasks, education resources, and quality measures into cohesive treatment workflows. They enable both automated and manual care management, supporting everything from routine wellness programs to complex case-based interventions.

## Business Context

Care plans serve as the primary mechanism for organizing patient care activities across the Firefly platform. They integrate with the task management system, case management workflows, quality measurement programs, and patient education initiatives to provide comprehensive care coordination.

## Core Concepts

### Care Plan Hierarchy
- **CarePlanType**: Defines categories of care (e.g., "staying_healthy_care_plan", "appointment")
- **CarePlanTemplate**: Reusable templates that define standard care workflows
- **CarePlan**: Individual care plan instances assigned to specific patients
- **TaskCollection**: Groups of related tasks that can be assigned to care plans

### Care Plan Types
1. **Standard Care Plans**: Traditional care plans with active/inactive states
2. **Case-Based Care Plans**: Integrated with case management, supporting draft/active states
3. **Episodic Care Plans**: Can be assigned multiple times to the same patient

## Technical Implementation

### Models

#### CarePlan
Core model extending `WorkUnit` with state machine functionality:
- Patient assignment and visibility controls
- Task management through `GenericRelation` to `TaskRelation`
- Integration with cases, education resources, and quality measures
- Automatic state machine configuration based on care plan type

#### CarePlanTemplate
Template factory for creating standardized care plans:
- Links to `CarePlanType` and `TaskCollection`
- Supports episodic and case-based configurations
- Integration with `MeasureCollection` for quality tracking

#### EducationResource
Patient education materials linked to care plans:
- Title and URL storage for educational content
- Many-to-many relationship with care plans through `CarePlanEducationResource`

### State Machine Integration

**Standard Care Plans**:
- States: `active` (IN_PROGRESS), `inactive` (COMPLETE)
- Transitions: active ↔ inactive

**Case-Based Care Plans**:
- States: `draft` (NOT_STARTED), `active` (IN_PROGRESS)
- Transitions: draft → active, active → draft

### API Endpoints

- `GET/POST /care-plan/` - List and create care plans
- `GET/PATCH/DELETE /care-plan/<id>` - Retrieve, update, delete care plans
- `PATCH /care-plan/<id>/action` - State machine actions
- `GET/POST /care-plan/v2/` - Case-based care plans
- `GET /care-plan/templates/` - Available templates

## Business Logic

### Care Plan Assignment
The `assign_care_plan_from_template()` function handles automated care plan creation:
1. Retrieves care plan template by type UID
2. Creates or retrieves existing care plan (respects episodic settings)
3. Processes custom tasks and template-based tasks
4. Sends notifications for non-case-based care plans

### Task Management
- Tasks can be created from `TaskCollection` templates or added manually
- Custom tasks support priority, due dates, and owner group assignment
- Task lifecycle tied to care plan state (completed care plans delete active tasks)

### Case Integration
- Case-based care plans integrate with the case management system
- Care plan completion automatically delinks open cases
- Supports case-specific workflows and state transitions

### Quality Measurement
- Integration with `MeasureCollection` for quality tracking
- Automatic data collection and reporting capabilities
- Support for both manual and automatic data tracking modes

## Configuration

### Creating Care Plan Templates

Care plan templates are configured through the Django admin interface:

1. **Create TaskCollection** (`/admin/tasks/taskcollection/add/`):
   - Title and description are internal-facing
   - Configure associated `TaskCollectionTasks` for template tasks

2. **Create CarePlanType** (`/admin/care_plan/careplantype/add/`):
   - UID: Unique identifier (e.g., "staying_healthy_care_plan")
   - Description: Internal-facing description shown in Lucian

3. **Create CarePlanTemplate** (`/admin/care_plan/careplantemplate/add/`):
   - Title: Patient-facing care plan title
   - Notes: Patient-facing care plan description
   - Type: Link to `CarePlanType`
   - Task Collection: Link to `TaskCollection`
   - Can be manually assigned: Enable for Lucian selection
   - Episodic: Allow multiple assignments to same patient
   - Is case based care plan: Enable case management integration
   - Measure Collection: Link quality measures for tracking

### Education Resources

Configure patient education materials (`/admin/care_plan/educationresource/add/`):
- Title: Resource name
- URL: Link to educational content
- Link to care plans through `CarePlanEducationResource`

## Workflow Integration

### Lucian Interface
- Manual care plan template selection and preview
- Task customization before care plan creation
- Real-time care plan status and task management

### Automation
- Automatic care plan assignment during patient onboarding
- Event-driven care plan creation (e.g., appointment scheduling)
- Integration with Braze for patient notifications

### Frontend Integration
- TypeScript models and API services in CRM
- Case-based care plan modal for provider workflow
- Real-time status updates and action handling