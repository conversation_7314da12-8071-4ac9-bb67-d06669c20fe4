import datetime
import logging
from unittest.mock import patch

from django.conf import settings
from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PatientUserFactory, PersonUserFactory
from firefly.modules.care_plan.api import NewCarePlanListCreateView
from firefly.modules.care_plan.models import EducationResource
from firefly.modules.cases.factories import CaseCategoryFactory, CaseFactory, CaseRelationFactory
from firefly.modules.quality.factories import (
    MeasureCollectionFactory,
    MeasureCollectionMeasureFactory,
    MeasureFactory,
    MeasureReportFactory,
    MeasureReportPopulationFactory,
)
from firefly.modules.quality.models import MeasurePopulationType
from firefly.modules.statemachines.factories import DefaultStateMachineFactory
from firefly.modules.work_units.constants import StatusCategory

from .factories import CarePlanFactory, CarePlanTemplateFactory, CarePlanTypeFactory

logger = logging.getLogger(__name__)


class NewCarePlanTestCase(FireflyTestCase):
    def test_get_patient_care_plans(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        type = CarePlanTypeFactory()
        care_plan = CarePlanFactory(type=type, patient=self.patient)
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)
        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_case_based(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_case_based_is_empty(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = None
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_case_based_false(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = False
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_no_measure_collection(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["measure_collection_measures"].count(0), 0)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        user_url_path = "/care-plan/v2/"
        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["measure_collection_measures"].count(0), 0)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_one_measure_no_reports(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get care plan education resource
        with self.assertNumQueries(7):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_one_measure_with_reports(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(response_measure_report["detail"], measure_report.detail)
            self.assertEqual(response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(response_measure_report["care_gap_status"], measure_report.care_gap_status)

        user_url_path = "/care-plan/v2/"
        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        user_response = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in user_response:
            self.assertEqual(user_response["uri"], measure.uri)
            self.assertEqual(user_response["name"], measure.name)

        user_response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(user_response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(user_response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(user_response_measure_report["detail"], measure_report.detail)
            self.assertEqual(user_response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(user_response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(user_response_measure_report["care_gap_status"], measure_report.care_gap_status)

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_care_plans_same_measure_multiple_reports_diff_patients(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        self.patient1 = PersonUserFactory.create(email="<EMAIL>")
        measure_report_1 = MeasureReportFactory(
            measure=measure,
            person=self.patient1,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report_1.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"]

        self.assertEqual(
            len(response_measure_report),
            1,
        )

        first_response_measure_report = response_measure_report[0]

        for key in first_response_measure_report:
            self.assertEqual(first_response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(first_response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(first_response_measure_report["detail"], measure_report.detail)
            self.assertEqual(first_response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(first_response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(first_response_measure_report["care_gap_status"], measure_report.care_gap_status)

        user_url_path = "/care-plan/v2/"
        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        user_response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in user_response_measure:
            self.assertEqual(user_response_measure["uri"], measure.uri)
            self.assertEqual(user_response_measure["name"], measure.name)

        user_response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"]

        self.assertEqual(
            len(user_response_measure_report),
            1,
        )

        first_user_response_measure_report = user_response_measure_report[0]

        for key in first_user_response_measure_report:
            self.assertEqual(first_user_response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(first_user_response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(first_user_response_measure_report["detail"], measure_report.detail)
            self.assertEqual(first_user_response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(first_user_response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(first_user_response_measure_report["care_gap_status"], measure_report.care_gap_status)

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_multiple_measures(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()

        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        measure_2 = MeasureFactory()
        measure_collection_measures_2 = MeasureCollectionMeasureFactory(
            measure=measure_2, measure_collection=measure_collection
        )
        measure_collection_measures_2.save()
        measure_report_2 = MeasureReportFactory(
            measure=measure_2,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report_2.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            2,
        )

        user_url_path = "/care-plan/v2/"
        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]

        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            2,
        )

        measure_collection_measures_2.delete()

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )

        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]

        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_report_cases(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        state_machine = DefaultStateMachineFactory()
        case_category = CaseCategoryFactory(state_machine_definition=state_machine)
        case = CaseFactory(
            description=None, notes=None, category=case_category, status="Draft", person=self.patient.person
        )
        CaseRelationFactory(content_object=measure_report, case=case)

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(response_measure_report["detail"], measure_report.detail)
            self.assertEqual(response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(response_measure_report["care_gap_status"], measure_report.care_gap_status)
            self.assertEqual(
                len(response_measure_report["cases"]),
                1,
            )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get case relations and cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_report_multiple_cases(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        state_machine = DefaultStateMachineFactory()
        case_category = CaseCategoryFactory(state_machine_definition=state_machine)
        case = CaseFactory(
            description=None, notes=None, category=case_category, status="Draft", person=self.patient.person
        )
        CaseRelationFactory(content_object=measure_report, case=case)

        case_category_1 = CaseCategoryFactory(state_machine_definition=state_machine)
        case_1 = CaseFactory(
            description=None, notes=None, category=case_category_1, status="Draft", person=self.patient.person
        )
        CaseRelationFactory(content_object=measure_report, case=case_1)

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(response_measure_report["detail"], measure_report.detail)
            self.assertEqual(response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(response_measure_report["care_gap_status"], measure_report.care_gap_status)
            self.assertEqual(
                len(response_measure_report["cases"]),
                2,
            )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get case relations and cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_report_no_cases(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        self.patient1 = PersonUserFactory.create(email="<EMAIL>")

        state_machine = DefaultStateMachineFactory()
        case_category = CaseCategoryFactory(state_machine_definition=state_machine)
        case = CaseFactory(description=None, notes=None, category=case_category, status="Draft", person=self.patient1)
        CaseRelationFactory(content_object=measure_report, case=case)

        case_category_1 = CaseCategoryFactory(state_machine_definition=state_machine)
        case_1 = CaseFactory(
            description=None, notes=None, category=case_category_1, status="Draft", person=self.patient1
        )
        CaseRelationFactory(content_object=measure_report, case=case_1)

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["next_action_date"], measure_report.next_action_date)
            self.assertEqual(response_measure_report["is_care_gap"], measure_report.is_care_gap)
            self.assertEqual(response_measure_report["detail"], measure_report.detail)
            self.assertEqual(response_measure_report["last_action"], measure_report.last_action)
            self.assertEqual(response_measure_report["last_action_date"], measure_report.last_action_date)
            self.assertEqual(response_measure_report["care_gap_status"], measure_report.care_gap_status)
            self.assertEqual(
                len(response_measure_report["cases"]),
                0,
            )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get case relations and cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_satisfied_measure(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["care_gap_status"], "Satisfied")

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_excluded_measure(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=0,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=1,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["care_gap_status"], "Excluded")

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_overdue_measure(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=0,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["care_gap_status"], "Overdue")

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_due_soon_measure(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=0,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.next_action_date = (datetime.date.today() + datetime.timedelta(days=5)).strftime("%Y-%m-%d")
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"][0]
        for key in response_measure_report:
            self.assertEqual(response_measure_report["care_gap_status"], "Due Soon")

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get measure populations
        # 1 to get cases related to measure report
        # 1 to get care plan education resource
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_resource_for_another_patient(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        another_patient = PatientUserFactory.create()
        another_care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=another_patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )
        another_care_plan.education_resources.add(resource)
        another_care_plan.save()
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(len(care_plan_json["care_plan_education_resources"]), 0)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_resources(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )
        care_plan.education_resources.add(resource)
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(
            care_plan_json["care_plan_education_resources"][0]["education_resource"]["title"], resource.title
        )
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_no_resources(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(len(care_plan_json["care_plan_education_resources"]), 0)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_multiple_resources(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )
        care_plan.education_resources.add(resource)
        resource_1 = EducationResource.objects.create(
            title="Healthy You Another", url="https://www.fireflyhealth.com/healthy-you-1"
        )
        care_plan.education_resources.add(resource_1)
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(len(care_plan_json["care_plan_education_resources"]), 2)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_with_shared_resources(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )
        care_plan.education_resources.add(resource)
        resource_1 = EducationResource.objects.create(
            title="Healthy You Another", url="https://www.fireflyhealth.com/healthy-you-1"
        )
        care_plan.education_resources.add(resource_1)

        care_plan_template_1 = CarePlanTemplateFactory()
        care_plan_template_1.is_case_based_care_plan = True
        care_plan_template_1.save()
        care_plan_1 = CarePlanFactory(
            type=care_plan_template_1.type,
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_1.education_resources.add(resource)
        care_plan_1.education_resources.add(resource_1)

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(len(care_plan_json["care_plan_education_resources"]), 2)
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 2 to fetch task relations
        # 2 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(8):
            response = view(request)
            response.render()

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_patient_care_plan_status_to_active(self, mock_reminders):
        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=0,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
            status_category=StatusCategory.NOT_STARTED,
            status="draft",
        )

        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "active", "tasks": [], "patient": self.patient.id},
            format="json",
        )
        responseData = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(responseData["status"], "active")
        self.assertEqual(responseData["is_case_based_care_plan"], care_plan_template.is_case_based_care_plan)
        self.assertTrue(responseData["measure_collection_measures"])
        self.assertEqual(len(responseData["care_plan_education_resources"]), 0)
        self.assertEqual(responseData["care_plan_data_tracking"], care_plan.care_plan_data_tracking)
        self.assertTrue(responseData["notes"], care_plan.notes)
        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": self.patient.person.pk,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": care_plan.title},
                }
            ],
        )

    def test_patch_care_plan_with_resource(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {
                "care_plan_education_resources": [
                    {
                        "education_resource": {
                            "title": "Healthy You",
                            "url": "https://www.fireflyhealth.com/healthy-you",
                        },
                    }
                ],
                "patient": self.patient.id,
                "title": "Care Plan Test - Update",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()["care_plan_education_resources"][0]["education_resource"]["title"], "Healthy You"
        )
        self.assertEqual(
            response.json()["care_plan_education_resources"][0]["education_resource"]["url"],
            "https://www.fireflyhealth.com/healthy-you",
        )

    def test_patch_care_plan_with_multiple_resources(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {
                "care_plan_education_resources": [
                    {
                        "education_resource": {
                            "title": "Healthy You",
                            "url": "https://www.fireflyhealth.com/healthy-you",
                        }
                    },
                    {
                        "education_resource": {
                            "title": "Second Resource",
                            "url": "https://www.fireflyhealth.com/healthy-you-second",
                        }
                    },
                ],
                "patient": self.patient.id,
                "title": "Care Plan Test - Update",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()["care_plan_education_resources"]), 2)

    def test_patch_care_plan_with_notes(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {"title": care_plan.title, "notes": "Changing notes", "patient": self.patient.id},
            format="json",
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["notes"], "Changing notes")

    def test_patch_care_plan_with_new_existing_resources(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {
                "care_plan_education_resources": [
                    {
                        "education_resource": {
                            "title": "Healthy You",
                            "url": "https://www.fireflyhealth.com/healthy-you",
                            "id": resource.id,
                        }
                    },
                    {
                        "education_resource": {
                            "title": "Second Resource",
                            "url": "https://www.fireflyhealth.com/healthy-you-second",
                        }
                    },
                ],
                "patient": self.patient.id,
                "title": "Care Plan Test - Update",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()["care_plan_education_resources"]), 2)

    def test_patch_care_plan_modifying_existing_resource(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        resource = EducationResource.objects.create(
            title="Healthy You", url="https://www.fireflyhealth.com/healthy-you"
        )
        care_plan.education_resources.add(resource)
        care_plan.save()

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {
                "care_plan_education_resources": [
                    {
                        "education_resource": {
                            "title": "Healthy You Modify",
                            "url": "https://www.fireflyhealth.com/healthy-you",
                            "id": resource.id,
                        }
                    },
                ],
                "patient": self.patient.id,
                "title": "Care Plan Test - Update",
            },
            format="json",
        )
        # A resource title or URL cannot be updated
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()["care_plan_education_resources"][0]["education_resource"]["title"], resource.title
        )

    def test_get_patient_care_plan_data_tracking(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        care_plan.care_plan_data_tracking = "automatic"
        care_plan.save()
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["care_plan_data_tracking"],
            care_plan.care_plan_data_tracking,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_get_patient_care_plan_data_tracking_empty(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["care_plan_data_tracking"],
            None,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get care plan education resource
        with self.assertNumQueries(6):
            response = view(request)
            response.render()

    def test_patch_care_plan_with_care_plan_data_tracking(self):
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {
                "care_plan_data_tracking": "Automatic",
                "patient": self.patient.id,
                "title": "Care Plan Test - Update",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["care_plan_data_tracking"], "Automatic")

    def test_patch_care_plan_with_multiple_measures(self):
        measure_collection = MeasureCollectionFactory()

        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        self.patient1 = PersonUserFactory.create(email="<EMAIL>")
        measure_2 = MeasureFactory()

        measure_collection_measures_2 = MeasureCollectionMeasureFactory(
            measure=measure_2, measure_collection=measure_collection
        )
        measure_collection_measures_2.save()

        measure_report_2 = MeasureReportFactory(
            measure=measure_2,
            person=self.patient1,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report_2.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.patch(
            "/care-plan/v2/%s" % care_plan.id,
            {"title": care_plan.title, "notes": "Changing notes", "patient": self.patient.id},
            format="json",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["notes"], "Changing notes")
        self.assertEqual(len(response.json()["measure_collection_measures"]), 2)

        for m_c_m in response.json()["measure_collection_measures"]:
            if m_c_m["id"] == measure_collection_measures.id:
                self.assertEqual(len(m_c_m["measure_reports"]), 1)
                self.assertEqual(m_c_m["measure_reports"][0]["id"], measure_report.id)
            else:
                self.assertEqual(len(m_c_m["measure_reports"]), 0)

    def test_patch_care_plan_with_multiple_measures_action(self):
        measure_collection = MeasureCollectionFactory()

        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        self.patient1 = PersonUserFactory.create(email="<EMAIL>")
        measure_2 = MeasureFactory()

        measure_collection_measures_2 = MeasureCollectionMeasureFactory(
            measure=measure_2, measure_collection=measure_collection
        )
        measure_collection_measures_2.save()

        measure_report_2 = MeasureReportFactory(
            measure=measure_2,
            person=self.patient1,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report_2.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
            status_category=StatusCategory.NOT_STARTED,
            status="draft",
        )

        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "active", "tasks": [], "patient": self.patient.id},
            format="json",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()["measure_collection_measures"]), 2)

        for m_c_m in response.json()["measure_collection_measures"]:
            if m_c_m["id"] == measure_collection_measures.id:
                self.assertEqual(len(m_c_m["measure_reports"]), 1)
                self.assertEqual(m_c_m["measure_reports"][0]["id"], measure_report.id)
            else:
                self.assertEqual(len(m_c_m["measure_reports"]), 0)

    def test_get_patient_care_plans_one_measure_with_denom_0(self):
        url_path = f"/care-plan/v2/?patient={self.patient.pk}"

        measure_collection = MeasureCollectionFactory()
        measure = MeasureFactory()
        measure_collection_measures = MeasureCollectionMeasureFactory(
            measure=measure, measure_collection=measure_collection
        )
        measure_collection_measures.save()
        measure_report = MeasureReportFactory(
            measure=measure,
            person=self.patient.person,
            populations=(
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR,
                    count=0,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.NUMERATOR,
                    count=1,
                ),
                MeasureReportPopulationFactory.build(
                    code=MeasurePopulationType.DENOMINATOR_EXCLUSION,
                    count=0,
                ),
            ),
        )
        measure_report.save()

        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        care_plan = CarePlanFactory(
            type=care_plan_template.type,
            patient=self.patient,
            autocreated_from=care_plan_template,
        )

        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        response_measure = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in response_measure:
            self.assertEqual(response_measure["uri"], measure.uri)
            self.assertEqual(response_measure["name"], measure.name)

        response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"]

        self.assertEqual(
            len(response_measure_report),
            0,
        )

        user_url_path = "/care-plan/v2/"
        user_response = self.client.get(user_url_path)
        self.assertEqual(user_response.status_code, 200)
        care_plan_json = [x for x in user_response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(
            care_plan_json["is_case_based_care_plan"],
            care_plan.autocreated_from.is_case_based_care_plan,
        )
        self.assertEqual(
            len(care_plan_json["measure_collection_measures"]),
            1,
        )
        user_response = care_plan_json["measure_collection_measures"][0]["measure"]
        for key in user_response:
            self.assertEqual(user_response["uri"], measure.uri)
            self.assertEqual(user_response["name"], measure.name)

        user_response_measure_report = care_plan_json["measure_collection_measures"][0]["measure_reports"]

        self.assertEqual(
            len(user_response_measure_report),
            0,
        )

        view = NewCarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        # 1 to get care plans
        # 1 to fetch task relations
        # 1 to fetch cases
        # 1 to fetch task relations for cases
        # 1 to get measure collection measures
        # 1 to get measure reports
        # 1 to get care plan education resource
        with self.assertNumQueries(7):
            response = view(request)
            response.render()
