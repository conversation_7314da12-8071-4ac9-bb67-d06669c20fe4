import datetime
import logging
from datetime import timed<PERSON><PERSON>
from unittest import mock
from unittest.mock import patch
from uuid import uuid4

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core import management
from django.utils import timezone
from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import (
    AssigneeGroupFactory,
    PersonUserFactory,
    ProviderDetailFactory,
)
from firefly.core.user.utils import create_update_assignee_group_from_user
from firefly.modules.appointment.constants import AppointmentReason, AppointmentStatus
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.care_plan.api import CarePlanList<PERSON>reateView, CarePlanRetrieveView
from firefly.modules.care_plan.constants import (
    CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT,
    CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT,
)
from firefly.modules.care_plan.management.commands.backfill_create_care_plan import (
    STAYING_HEALTHY_CARE_PLAN_UID,
)
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import CaseRelation
from firefly.modules.features.constants import Features
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.quality.factories import (
    MeasureCollectionFactory,
    MeasureCollectionMeasureFactory,
    MeasureReportFactory,
)
from firefly.modules.tasks.factories import (
    TaskCollectionFactory,
    TaskCollectionTaskFactory,
    TaskFactory,
    TaskRelationFactory,
)
from firefly.modules.tasks.models import Task, TaskRelation
from firefly.modules.work_units.constants import StatusCategory

from .factories import (
    CarePlanFactory,
    CarePlanTemplateFactory,
    CarePlanTypeFactory,
    EducationResourceFactory,
)
from .models import CarePlan, CarePlanTemplate
from .serializers import CarePlanSerializer
from .utils import assign_care_plan_from_template

logger = logging.getLogger(__name__)


class CarePlanFeatureAccessPermissionTestCase(FireflyTestCase):
    def test_care_plan_list_create_view(self):
        # Case 1: Provider doesnt have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = False
        response = self.provider_client.get("/care-plan/")
        self.assertEqual(response.status_code, 403)

        # Case 2: Provider have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = True
        response = self.provider_client.get("/care-plan/")
        self.assertEqual(response.status_code, 200)


class CarePlanTestCase(FireflyTestCase):
    def test_care_plan_retrieve_view(self):
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 2,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(create_response.status_code, 201)

        # Case 1: Provider doesnt have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = False
        response = self.provider_client.get("/care-plan/%s" % create_response.json()["id"])
        self.assertEqual(response.status_code, 403)

        # Case 2: Provider have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = True
        response = self.provider_client.get("/care-plan/%s" % create_response.json()["id"])
        self.assertEqual(response.status_code, 200)

    def test_care_plan_retrieve_view_for_assignee_groups(self):
        create_update_assignee_group_from_user(self.patient)
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 2,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(create_response.status_code, 201)

        # Case 1: Provider doesnt have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = False
        response = self.provider_client.get("/care-plan/%s" % create_response.json()["id"])
        self.assertEqual(response.status_code, 403)

        # Case 2: Provider have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.care_plans.value] = True
        response = self.provider_client.get("/care-plan/%s" % create_response.json()["id"])
        self.assertEqual(response.status_code, 200)

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_care_plan_create_request(self, mock_reminders):
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "Test note.",
        }

        response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(response.status_code, 201)

        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": self.patient.person.pk,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": "Care Plan Test"},
                }
            ],
        )
        # Check saved CarePlan
        care_plan_object = CarePlan.objects.get(id=response.json()["id"])
        self.assertEqual(care_plan_object.patient.id, care_plan_payload.pop("patient"))
        serializer = CarePlanSerializer(instance=care_plan_object).data
        self.assertTrue(care_plan_payload.items() <= serializer.items())

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_care_plan_create_request_with_tasks(self, mock_reminders):
        today = timezone.now()
        yesterday = today - datetime.timedelta(days=1)
        seven_weeks_overdue = today - datetime.timedelta(weeks=7)

        care_plan_title = "One small step for man, one giant leap for mankind."
        task_1_title = "Test with Nested - Task 1"
        task_2_title = "Test with Nested - Task 2"
        provider_task_1 = "Provder task, never marked as complete, always in Firebase"
        provider_task_2 = "Provider task marked as complete & removed from Firebase"

        provider = ProviderDetailFactory.create()
        care_plan_payload = {
            "title": care_plan_title,
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": task_1_title,
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": task_2_title,
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": provider_task_1,
                    "due_date": yesterday,
                    "owner_group": provider.user.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": provider_task_2,
                    "due_date": seven_weeks_overdue,
                    "owner_group": provider.user.assignee_group.pk,
                    "priority": 0,
                },
            ],
        }

        response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")

        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": self.patient.person.pk,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": care_plan_title},
                }
            ],
        )

        self.assertEqual(response.status_code, 201)

        care_plan = CarePlan.objects.get(id=response.json()["id"])

        # Assert care plan and task relations created.
        tasks_relations = TaskRelation.objects.filter(object_id=care_plan.id)
        self.assertEqual(len(tasks_relations), 4)

        self.assertEqual(care_plan.created_by, self.provider)
        self.assertEqual(len(care_plan.care_plan_tasks.all()), len(response.json()["tasks"]))

        # Mark one of the provider tasks as complete
        task_object_to_mark_as_complete = Task.objects.get(title=provider_task_2)
        updated_task_payload = {"is_complete": True}

        self.provider_client.patch(
            "/task/{}".format(task_object_to_mark_as_complete.id),
            updated_task_payload,
            format="json",
        )

        # Ensure task was actually marked as complete
        task_object_to_mark_as_complete.refresh_from_db()
        self.assertEqual(task_object_to_mark_as_complete.is_complete, True)

        # Save care plan to update end point
        # This is effectively a no-op update.
        care_plan_payload = {
            "id": care_plan.id,
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "id": Task.objects.get(title=task_1_title).id,
                    "title": task_1_title,
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=task_2_title).id,
                    "title": task_2_title,
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=provider_task_1).id,
                    "title": provider_task_1,
                    "due_date": yesterday,
                    "owner_group": provider.user.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=provider_task_2).id,
                    "title": provider_task_2,
                    "due_date": seven_weeks_overdue,
                    "owner_group": provider.user.assignee_group.pk,
                    "priority": 0,
                },
            ],
        }

        response = self.provider_client.patch("/care-plan/{}".format(care_plan.id), care_plan_payload, format="json")
        self.assertEqual(response.status_code, 200)

    def test_care_plan_create_request_with_tasks_for_assignee_groups(self):
        today = timezone.now()
        yesterday = today - datetime.timedelta(days=1)
        seven_weeks_overdue = today - datetime.timedelta(weeks=7)
        create_update_assignee_group_from_user(self.patient)

        care_plan_title = "One small step for man, one giant leap for mankind."
        task_1_title = "Test with Nested - Task 1"
        task_2_title = "Test with Nested - Task 2"
        provider_task_1 = "Provder task, never marked as complete, always in Firebase"
        provider_task_2 = "Provider task marked as complete & removed from Firebase"

        provider = ProviderDetailFactory.create()
        create_update_assignee_group_from_user(provider.user)

        care_plan_payload = {
            "title": care_plan_title,
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": task_1_title,
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": task_2_title,
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": provider_task_1,
                    "due_date": yesterday,
                    "owner_group": provider.user.assignee_group_id,
                    "priority": 0,
                },
                {
                    "title": provider_task_2,
                    "due_date": seven_weeks_overdue,
                    "owner_group": provider.user.assignee_group_id,
                    "priority": 0,
                },
            ],
        }

        response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")

        self.assertEqual(response.status_code, 201)

        care_plan = CarePlan.objects.get(id=response.json()["id"])

        # Assert care plan and task relations created.
        tasks_relations = TaskRelation.objects.filter(object_id=care_plan.id)
        self.assertEqual(len(tasks_relations), 4)

        self.assertEqual(care_plan.created_by, self.provider)
        self.assertEqual(len(care_plan.care_plan_tasks.all()), len(response.json()["tasks"]))

        # Mark one of the provider tasks as complete
        task_object_to_mark_as_complete = Task.objects.get(title=provider_task_2)
        updated_task_payload = {"is_complete": True}

        self.provider_client.patch(
            "/task/{}".format(task_object_to_mark_as_complete.id),
            updated_task_payload,
            format="json",
        )

        # Ensure task was actually marked as complete
        task_object_to_mark_as_complete.refresh_from_db()
        self.assertEqual(task_object_to_mark_as_complete.is_complete, True)

        # Save care plan to update end point
        # This is effectively a no-op update.
        care_plan_payload = {
            "id": care_plan.id,
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "id": Task.objects.get(title=task_1_title).id,
                    "title": task_1_title,
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=task_2_title).id,
                    "title": task_2_title,
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=provider_task_1).id,
                    "title": provider_task_1,
                    "due_date": yesterday,
                    "owner_group": provider.user.assignee_group_id,
                    "priority": 0,
                },
                {
                    "id": Task.objects.get(title=provider_task_2).id,
                    "title": provider_task_2,
                    "due_date": seven_weeks_overdue,
                    "owner_group": provider.user.assignee_group_id,
                    "priority": 0,
                },
            ],
        }

        response = self.provider_client.patch("/care-plan/{}".format(care_plan.id), care_plan_payload, format="json")
        self.assertEqual(response.status_code, 200)

    def test_care_plan_create_and_retrieve(self):
        care_plan_type = CarePlanTypeFactory()
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 2,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(create_response.status_code, 201)
        care_plan_id = create_response.json()["id"]
        care_plan = CarePlan.objects.get(id=care_plan_id)
        care_plan.type = care_plan_type
        care_plan.save()
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        from django.core.cache import cache

        cache.clear()
        get_lucian_bot_user.cache_clear()
        ContentType.objects.clear_cache()
        response = self.client.get(f"/care-plan/{care_plan_id}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["type_uid"], care_plan_type.uid)

        uri = f"/care-plan/{care_plan_id}/"
        view = CarePlanRetrieveView.as_view()
        factory = APIRequestFactory()
        request = factory.get(uri, format="json")
        force_authenticate(request, user=self.provider)

        with self.assertNumQueries(10):
            response = view(request, id=care_plan_id)
            response.render()

    def test_care_plan_create_and_update(self):
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 1,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(create_response.status_code, 201)
        self.assertEqual(create_response.json()["updated_by"], None)

        response = self.provider_client.patch(
            "/care-plan/%s" % create_response.json()["id"],
            {"title": "Care Plan Test - Update"},
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # Check updated CarePlan
        care_plan_object = CarePlan.objects.get(id=response.json()["id"])
        self.assertEqual(care_plan_object.title, "Care Plan Test - Update")

        task1 = Task.objects.get(title="Test with Nested - Task 1")
        self.assertEqual(task1.created_by, self.provider)

        task2 = Task.objects.get(title="Test with Nested - Task 2")
        self.assertEqual(task2.created_by, self.provider)

        self.assertEqual(care_plan_object.updated_by, self.provider)

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_care_plan_create_and_update_for_assignee_groups(self, mock_reminders):
        create_update_assignee_group_from_user(self.patient)
        provider = ProviderDetailFactory.create()
        create_update_assignee_group_from_user(provider.user)
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 1,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": self.patient.person.pk,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": "Care Plan Test"},
                }
            ],
        )
        mock_reminders.reset_mock()
        self.assertEqual(create_response.status_code, 201)
        self.assertEqual(create_response.json()["updated_by"], None)
        response = self.provider_client.patch(
            "/care-plan/%s" % create_response.json()["id"],
            {
                "title": "Care Plan Test - Update",
                "tasks": [
                    {
                        "id": Task.objects.get(title="Test with Nested - Task 1").id,
                        "owner_group": provider.user.assignee_group.pk,
                    },
                    {
                        "id": Task.objects.get(title="Test with Nested - Task 2").id,
                        "owner_group": provider.user.assignee_group.pk,
                    },
                ],
            },
            format="json",
        )
        mock_reminders.send.assert_not_called()
        self.assertEqual(response.status_code, 200)

        # Check updated CarePlan
        care_plan_object = CarePlan.objects.get(id=response.json()["id"])
        self.assertEqual(care_plan_object.title, "Care Plan Test - Update")

        task1 = Task.objects.get(title="Test with Nested - Task 1")
        self.assertEqual(task1.created_by, self.provider)
        self.assertEqual(task1.owner_group, provider.user.assignee_group)

        task2 = Task.objects.get(title="Test with Nested - Task 2")
        self.assertEqual(task2.owner_group, provider.user.assignee_group)
        self.assertEqual(task2.created_by, self.provider)

        self.assertEqual(care_plan_object.updated_by, self.provider)

    def test_care_plan_task_create_and_delete(self):
        provider = ProviderDetailFactory.create()

        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        care_plan_payload = {
            "title": "Care Plan Test",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Test with Nested - Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 1,
                },
                {
                    "title": "Test with Nested - Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": "Provider assigned task",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": provider.user.assignee_group.pk,
                    "priority": 2,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        care_plan_object = CarePlan.objects.get(id=create_response.json()["id"])
        self.assertEqual(len(care_plan_object.care_plan_tasks.all()), 3)
        task_1 = create_response.json()["tasks"][0]
        tasks_payload = [{"id": task_1["id"]}]

        # PATCH with only one task and expect the other task gets deleted
        response = self.provider_client.patch(
            "/care-plan/%s" % create_response.json()["id"],
            {"title": "Care Plan Test - Update", "tasks": tasks_payload},
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # Check updated CarePlan
        self.assertEqual(len(care_plan_object.care_plan_tasks.all()), 1)
        self.assertNotEqual(care_plan_object.care_plan_tasks.all()[0], task_1["id"])

        update_response = self.client.patch("/care-plan/task/%s" % task_1["id"], {"is_complete": True}, format="json")

        updated_task = Task.objects.get(id=task_1["id"])
        self.assertEqual(update_response.status_code, 200)
        self.assertEqual(updated_task.is_complete, True)
        self.assertEqual(updated_task.completed_by, self.patient)
        self.assertTrue(updated_task.completed_on is not None)

        # Explicitly (soft) delete a different task
        def matching_tasks_from_response(response):
            return [el for el in response.json()["tasks"] if el["id"] == task_1["id"]]

        response = self.client.get("/care-plan/%s" % care_plan_object.id)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(matching_tasks_from_response(response)), 1)
        response = self.provider_client.delete(f"/task/{task_1['id']}")
        # A GET request should not include that task
        response = self.client.get("/care-plan/%s" % care_plan_object.id)
        self.assertEqual(len(matching_tasks_from_response(response)), 0)

    def test_get_patient_care_plans(self):
        url_path = f"/care-plan/?patient={self.patient.pk}"
        type = CarePlanTypeFactory()
        care_plan = CarePlanFactory(type=type, patient=self.patient)
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)

    def test_get_patient_care_plans_case_based(self):
        url_path = f"/care-plan/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type, patient=self.patient, autocreated_from=care_plan_template
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(care_plan_json["is_case_based_care_plan"], care_plan.autocreated_from.is_case_based_care_plan)

        view = CarePlanListCreateView.as_view()
        factory = APIRequestFactory()
        request = factory.get(url_path, format="json")
        force_authenticate(request, user=self.provider)

        with self.assertNumQueries(4):
            response = view(request)
            response.render()

    def test_get_patient_care_plans_case_based_is_empty(self):
        url_path = f"/care-plan/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = None
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type, patient=self.patient, autocreated_from=care_plan_template
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(care_plan_json["is_case_based_care_plan"], care_plan.autocreated_from.is_case_based_care_plan)

    def test_get_patient_care_plans_case_based_false(self):
        url_path = f"/care-plan/?patient={self.patient.pk}"
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = False
        care_plan_template.save()
        care_plan = CarePlanFactory(
            type=care_plan_template.type, patient=self.patient, autocreated_from=care_plan_template
        )
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        care_plan_json = [x for x in response.json() if x["id"] == care_plan.id][0]
        self.assertEqual(care_plan_json["type_id"], care_plan_template.type.id)
        self.assertEqual(care_plan_json["title"], care_plan.title)
        self.assertEqual(care_plan_json["notes"], care_plan.notes)
        self.assertEqual(care_plan_json["is_case_based_care_plan"], care_plan.autocreated_from.is_case_based_care_plan)

    def test_care_plan_assign_state_machine_on_create(self):
        type = CarePlanTypeFactory()
        care_plan = CarePlanFactory(type=type, patient=self.patient)
        self.assertEqual(care_plan.state_machine_content, CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT)

    def test_care_plan_assign_state_machine_on_create_case_based(self):
        type = CarePlanTypeFactory()
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(type=type, patient=self.patient, autocreated_from=care_plan_template)
        self.assertEqual(care_plan.state_machine_content, CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT)

    def test_care_plan_status_change_for_case_based(self):
        type = CarePlanTypeFactory()
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(type=type, patient=self.patient, autocreated_from=care_plan_template)
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "active", "patient": self.patient.id},
            format="json",
        )
        self.assertEqual(response.json()["status"], "active")
        self.assertEqual(response.json()["status_category"], "in_progress")
        self.assertEqual(care_plan.state_machine_content, CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT)

    def test_care_plan_status_change_for_case_based_draft(self):
        type = CarePlanTypeFactory()
        care_plan_template = CarePlanTemplateFactory()
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()
        care_plan = CarePlanFactory(type=type, patient=self.patient, autocreated_from=care_plan_template)
        care_plan.status = "active"
        care_plan.status_category = "in_progress"
        care_plan.save()
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "draft", "patient": self.patient.id},
            format="json",
        )
        self.assertEqual(response.json()["status"], "draft")
        self.assertEqual(response.json()["status_category"], "not_started")
        self.assertEqual(care_plan.state_machine_content, CASE_BASED_CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT)

    def test_care_plan_complete_with_active_tasks_and_case(self):
        person = self.patient.person
        patient = self.patient

        # Create a care plan with the following relations and assign it to a patient:
        # 1) Incomplete task
        # 2) Completed task
        # 3) Case
        care_plan = CarePlan.objects.create(
            patient=patient, state_machine_content=CARE_PLAN_DEFAULT_STATE_MACHINE_CONTENT
        )

        task = Task.objects.create(patient=patient, person=person, is_complete=False, title="Random task")
        completed_task = Task.objects.create(
            patient=patient, person=person, is_complete=True, title="Another task but complete"
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        TaskRelation.objects.create(task=completed_task, content_object=care_plan, is_parent=True)

        case = CaseFactory(person=person)
        CaseRelation.objects.create(
            object_id=care_plan.id,
            content_type=ContentType.objects.get_for_model(CarePlan),
            case=case,
        )
        self.assertEqual(care_plan.status_category, StatusCategory.IN_PROGRESS)
        # Completed task
        self.assertEqual(care_plan.care_plan_tasks.filter(task__is_complete=True).count(), 1)
        # incomplete task
        self.assertEqual(care_plan.care_plan_tasks.filter(task__is_complete=False).count(), 1)
        # case
        self.assertEqual(
            case.relations.filter(content_type=ContentType.objects.get_for_model(CarePlan)).count(),
            1,
        )

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # one for the care-plan, one for the case

        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(todo_care_plan["content_type"], "careplan")
        self.assertEqual(len(todo_cases), 1)
        todo_case = todo_cases[0]
        self.assertEqual(todo_case["object_id"], case.id)
        self.assertEqual(len(todo_care_plan["content_object"]["tasks"]), 2)

        care_plan_response = self.provider_client.get(
            "/care-plan/%s" % care_plan.id,
            format="json",
        )
        care_plan_response = care_plan_response.json()
        tasks = care_plan_response["tasks"]
        cases = care_plan_response["cases"]
        # pass only the completed task and don't pass cases.
        task = tasks["tasks"][0] if tasks[0]["is_complete"] is True else tasks[1]
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "inactive", "tasks": [task], "patient": patient.id},
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()[0]["message"],
            "care plan tasks are out of date, Cannot perform action `inactive` for care plan %d" % care_plan.id,
        )

        # pass only the completed task and cases.
        task = tasks["tasks"][0] if tasks[0]["is_complete"] is True else tasks[1]
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "inactive", "tasks": [task], "cases": cases, "patient": patient.id},
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()[0]["message"],
            "care plan tasks are out of date, Cannot perform action `inactive` for care plan %d" % care_plan.id,
        )

        # pass only the tasks and don't pass cases.
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "inactive", "tasks": tasks, "patient": patient.id},
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()[0]["message"],
            "care plan cases are out of date, Cannot perform action `inactive` for care plan %d" % care_plan.id,
        )

        # By marking the care plan as inactive the following actions should take place:
        # 1) The incomplete task should be deleted
        # 2) The completed task should be visible under the care plan
        # 3) The case should be delinked from the care plan
        # 4) The care plan should be marked complete
        response = self.provider_client.patch(
            "/care-plan/%s/action" % care_plan.id,
            {"action": "inactive", "tasks": tasks, "cases": cases, "patient": patient.id},
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        care_plan.refresh_from_db()
        self.assertEqual(care_plan.status_category, StatusCategory.COMPLETE)
        # Completed task
        self.assertEqual(care_plan.care_plan_tasks.filter(task__is_complete=True).count(), 1)
        # incomplete task
        self.assertEqual(care_plan.care_plan_tasks.filter(task__is_complete=False).count(), 0)
        # case
        self.assertEqual(
            case.relations.filter(content_type=ContentType.objects.get_for_model(CarePlan)).count(),
            0,
        )

        # The care plan shouldn't be present in the incomplete list.
        # The case should be delinked and be present in the list.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)

        todo_case = body["results"][0]
        self.assertEqual(todo_case["content_type"], "case")
        self.assertEqual(todo_case["object_id"], case.id)

        # The care plan should be present in the completed list and
        # should have only one task under it.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=true")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)

        todo_care_plan = body["results"][0]
        self.assertEqual(todo_care_plan["content_type"], "careplan")
        self.assertEqual(len(todo_care_plan["content_object"]["cases"]), 0)
        self.assertEqual(len(todo_care_plan["content_object"]["tasks"]), 1)


class CarePlanTemplateTestCase(FireflyTestCase):
    def test_get_templates(self):
        CarePlanTemplate.objects.all().delete()
        url_path = "/care-plan/templates/"
        # Given a template which is not manually assignable
        template = CarePlanTemplateFactory(type=None, task_collection=None, can_be_manually_assigned=False)
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 0)
        # Given the template can be manually assigned
        template.can_be_manually_assigned = True
        template.save()
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 1)
        item = data[0]
        self.assertEqual(item["title"], template.title)
        self.assertEqual(item.get("task_collection_pk"), None)
        # Given a template with a type and task_collection
        task_collection = TaskCollectionFactory()
        template = CarePlanTemplateFactory(task_collection=task_collection, can_be_manually_assigned=True)
        response = self.provider_client.get(url_path)
        data = response.json()
        item = next(item for item in data if item["id"] == template.id)
        self.assertEqual(item["title"], template.title)
        self.assertEqual(item["notes"], template.notes)
        self.assertEqual(item["type_description"], template.type.description)
        self.assertEqual(item["task_collection_pk"], task_collection.pk)

    @mock.patch("firefly.modules.tasks.generic_serializers.task_template_to_task")
    def test_get_template_preview(self, mock_task_template_to_task):
        # Given a CarePlanTemplate with no associated TaskCollectionTask objects
        template = CarePlanTemplateFactory(type=None, task_collection=None)
        url_path = f"/care-plan/templates/{template.pk}/preview/"
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 400)
        url_path = f"/care-plan/templates/{template.pk}/preview/?user_id={self.patient.pk}"
        response = self.provider_client.get(url_path)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("task_previews"), [])
        self.assertEqual(response.data.get("type_description"), None)
        # Given the CarePlanTemplate has a type and empty TaskCollectionTask association
        template.type = CarePlanTypeFactory()
        template.task_collection = TaskCollectionFactory()
        template.task_collection.tasks.set([])
        template.save()
        response = self.provider_client.get(url_path)
        self.assertEqual(response.data.get("task_previews"), [])
        self.assertEqual(response.data.get("type_description"), template.type.description)
        # Given there are associated TaskCollectionTask objects
        task_template_1 = TaskCollectionTaskFactory(task_collection=template.task_collection, collection_order=1)
        task_template_2 = TaskCollectionTaskFactory(task_collection=template.task_collection, collection_order=2)
        mock_task_1 = TaskFactory()
        mock_task_2 = TaskFactory(owner_group=None)
        mock_task_template_to_task.side_effect = [mock_task_1, mock_task_2]
        response = self.provider_client.get(url_path)
        self.assertEqual(response.data.get("title"), template.title)
        self.assertEqual(response.data.get("notes"), template.notes)
        task_previews = response.data.get("task_previews")
        self.assertEqual(len(task_previews), 2)
        task_preview_1_data = task_previews[0]
        self.assertEqual(task_preview_1_data["task_template_pk"], task_template_1.pk)
        self.assertEqual(task_preview_1_data["due_date"], mock_task_1.due_date)
        self.assertEqual(task_preview_1_data["title"], mock_task_1.title)
        self.assertEqual(task_preview_1_data["assignee_pk"], mock_task_1.owner_group.pk)
        self.assertEqual(task_preview_1_data["priority"], mock_task_1.priority)
        task_preview_2_data = task_previews[1]
        self.assertEqual(task_preview_2_data["task_template_pk"], task_template_2.pk)
        self.assertEqual(task_preview_2_data["due_date"], mock_task_2.due_date)
        self.assertEqual(task_preview_2_data["title"], mock_task_2.title)
        self.assertEqual(task_preview_2_data["assignee_pk"], None)
        self.assertEqual(task_preview_1_data["priority"], mock_task_2.priority)

    @mock.patch("firefly.modules.care_plan.api.assign_care_plan_from_template")
    def test_assign_template(self, mock_assign_care_plan_from_template):
        mock_assign_care_plan_from_template.return_value = (CarePlanFactory(), True)
        task_collection = TaskCollectionFactory()
        # Given the template can't be manually assigned
        template = CarePlanTemplateFactory(task_collection=task_collection, can_be_manually_assigned=False)
        endpoint_uri = f"/care-plan/templates/{template.pk}/assign/"
        assign_payload = {
            "user_id": self.patient.pk,
        }
        response = self.provider_client.post(
            endpoint_uri,
            assign_payload,
            format="json",
        )
        self.assertEqual(response.status_code, 400)
        # Given the template can be manually assigned
        template.can_be_manually_assigned = True
        template.save()
        response = self.provider_client.post(
            endpoint_uri,
            assign_payload,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        mock_assign_care_plan_from_template.assert_called_once_with(
            care_plan_type_uid=template.type.uid,
            user=self.patient,
            task_override_data={"_custom": []},
            notes=None,
            title=None,
            created_by=self.provider,
        )
        mock_assign_care_plan_from_template.reset_mock()
        # Given a request including overrides
        date_str = "2020-01-01"
        assign_payload = {
            "user_id": self.patient.pk,
            "title": str(uuid4()),
            "notes": str(uuid4()),
            "tasks": [
                {
                    "task_template_pk": "1",
                    "due_date": date_str,
                    "owner_group": self.provider.assignee_group.pk,
                    "priority": "999",
                },
                {
                    "due_date": date_str,
                    "owner_group": self.provider.assignee_group.pk,
                    "priority": 1,
                    "title": "custom title",
                },
            ],
        }
        response = self.provider_client.post(
            endpoint_uri,
            assign_payload,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        expected_date = datetime.datetime.fromisoformat(date_str)
        mock_assign_care_plan_from_template.assert_called_once_with(
            care_plan_type_uid=template.type.uid,
            user=self.patient,
            task_override_data={
                "_custom": [
                    {
                        "due_date": expected_date,
                        "owner_group": self.provider.assignee_group,
                        "priority": 1,
                        "title": "custom title",
                    }
                ],
                1: {
                    "task_template_pk": "1",
                    "due_date": expected_date,
                    "owner_group": self.provider.assignee_group,
                    "priority": 999,
                },
            },
            title=assign_payload["title"],
            notes=assign_payload["notes"],
            created_by=self.provider,
        )
        # Given an existing care plan was returned
        mock_assign_care_plan_from_template.return_value = (CarePlanFactory(), False)
        response = self.provider_client.post(endpoint_uri, {"user_id": self.patient.pk})
        self.assertEqual(response.status_code, 400)


class CarePlanUtilsTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.tasks.models.TaskCollectionTask.get_due_date")
    def test_assign_care_plan_from_template(self, mock_get_due_date):
        test_assignee_group = AssigneeGroupFactory()
        override_due_date = datetime.datetime.strptime("2021-11-11", "%Y-%m-%d").replace(tzinfo=datetime.timezone.utc)
        task_collection_task = TaskCollectionTaskFactory(assignee_group=test_assignee_group)
        task_collection = task_collection_task.task_collection
        mock_get_due_date.return_value = datetime.datetime.now()
        care_plan_template = CarePlanTemplateFactory(task_collection=task_collection)

        def get_care_plans_of_type():
            return CarePlan.objects.filter(patient=self.patient, type=care_plan_template.type)

        self.assertFalse(get_care_plans_of_type().exists())
        self.assertFalse(Task.objects.filter(patient=self.patient, autocreated_from=task_collection_task).exists())
        title = str(uuid4())
        notes = str(uuid4())
        care_plan, created = assign_care_plan_from_template(
            care_plan_type_uid=care_plan_template.type.uid,
            user=self.patient,
            task_override_data={task_collection_task.pk: {"due_date": override_due_date}},
            notes=notes,
            title=title,
        )
        self.assertTrue(created)
        self.assertIsNotNone(care_plan)
        created_care_plan = get_care_plans_of_type().first()
        self.assertEqual(created_care_plan.title, title)
        self.assertEqual(created_care_plan.notes, notes)
        self.assertEqual(created_care_plan.autocreated_from, care_plan_template)
        created_task = Task.objects.get(patient=self.patient, autocreated_from=task_collection_task)
        self.assertEqual(created_task.owner_group, test_assignee_group)
        self.assertEqual(created_task.due_date, override_due_date)
        # Given we try to assign a new care plan of the same type
        original_num_care_plans = get_care_plans_of_type().count()
        care_plan, created = assign_care_plan_from_template(
            care_plan_type_uid=care_plan_template.type.uid, user=self.patient
        )
        self.assertFalse(created)
        self.assertIsNotNone(care_plan)
        new_num_care_plans = get_care_plans_of_type().count()
        self.assertEqual(new_num_care_plans, original_num_care_plans)
        # Set `episodic=True` for template,
        # try to assign a new care plan of the same type.
        # A new care plan should be created of the same type.
        care_plan_template.episodic = True
        care_plan_template.save()

        care_plan, created = assign_care_plan_from_template(
            care_plan_type_uid=care_plan_template.type.uid,
            user=self.patient,
            task_override_data={task_collection_task.pk: {"due_date": override_due_date}},
            notes=notes,
            title=title,
        )
        self.assertTrue(created)
        self.assertIsNotNone(care_plan)
        created_care_plan = get_care_plans_of_type().first()
        self.assertEqual(created_care_plan.title, title)
        self.assertEqual(created_care_plan.notes, notes)
        self.assertEqual(created_care_plan.autocreated_from, care_plan_template)
        created_task = created_care_plan.care_plan_tasks.first().task
        self.assertEqual(created_task.owner_group, test_assignee_group)
        self.assertEqual(created_task.due_date, override_due_date)
        new_num_care_plans = get_care_plans_of_type().count()
        self.assertEqual(new_num_care_plans, original_num_care_plans + 1)
        # Test with an empty set of custom, non-template tasks
        care_plan_template = CarePlanTemplateFactory(task_collection=task_collection)
        care_plan, created = assign_care_plan_from_template(
            care_plan_type_uid=care_plan_template.type.uid,
            user=self.patient,
            task_override_data={
                task_collection_task.pk: {"due_date": override_due_date},
                "_custom": [],
            },
            notes=notes,
            title=title,
        )
        created_template_task = (
            TaskRelation.objects.filter(
                object_id=care_plan.pk, is_parent=True, task__autocreated_from=task_collection_task
            )
            .first()
            .task
        )
        self.assertEqual(created_template_task.title, task_collection_task.title)
        # Test with custom, non-template tasks
        custom_task_data = {
            "title": "custom test title",
            "owner_group": self.patient.assignee_group,
            "due_date": override_due_date,
            "priority": 0,
        }
        care_plan_template = CarePlanTemplateFactory(task_collection=task_collection)
        care_plan, created = assign_care_plan_from_template(
            care_plan_type_uid=care_plan_template.type.uid,
            user=self.patient,
            task_override_data={
                task_collection_task.pk: {"due_date": override_due_date},
                "_custom": [custom_task_data],
            },
            notes=notes,
            title=title,
        )
        created_custom_task = (
            TaskRelation.objects.filter(object_id=care_plan.pk, is_parent=True)
            .exclude(task__autocreated_from=task_collection_task)
            .first()
            .task
        )
        self.assertEqual(created_custom_task.title, custom_task_data["title"])
        self.assertEqual(created_custom_task.owner_group, custom_task_data["owner_group"])
        self.assertEqual(created_custom_task.due_date, custom_task_data["due_date"])
        self.assertEqual(created_custom_task.priority, custom_task_data["priority"])
        created_template_task = (
            TaskRelation.objects.filter(
                object_id=care_plan.pk, is_parent=True, task__autocreated_from=task_collection_task
            )
            .first()
            .task
        )
        self.assertEqual(created_template_task.title, task_collection_task.title)


class BackfillCarePlanStateMachine(FireflyTestCase):
    def setUp(self):
        super().setUp()
        care_plan_payload = {
            "title": "Active care plan",
            "patient": self.patient.pk,
            "notes": "",
            "tasks": [
                {
                    "title": "Task 1",
                    "due_date": "2065-07-21T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
                {
                    "title": "Task 2",
                    "due_date": "2065-07-23T13:45:18Z",
                    "owner_group": self.patient.assignee_group.pk,
                    "priority": 0,
                },
            ],
        }
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        self.assertEqual(create_response.status_code, 201)
        self.care_plan = CarePlan.objects.get(id=create_response.json()["id"])

        # clear state machine fields
        CarePlan.objects.all().update(state_machine_content=None, status=None, action=None)
        self.care_plan.refresh_from_db()


class RemoveTasksFromCarePlan(FireflyTestCase):
    def setUp(self):
        super().setUp()
        care_plan_template = CarePlanTemplateFactory()
        care_plan_1 = CarePlanFactory(autocreated_from=care_plan_template)
        care_plan_2 = CarePlanFactory(autocreated_from=care_plan_template)
        task_1 = TaskFactory(title="Cancer screening")
        TaskRelationFactory(
            task=task_1, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_1.id
        )
        task_2 = TaskFactory(title="Cancer screening 1")
        TaskRelationFactory(
            task=task_2, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_1.id
        )
        task_3 = TaskFactory(title="other")
        TaskRelationFactory(
            task=task_3, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_1.id
        )
        task_4 = TaskFactory(title="Cancer screening")
        TaskRelationFactory(
            task=task_4, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_2.id
        )
        task_5 = TaskFactory(title="Cancer screening 1")
        TaskRelationFactory(
            task=task_5, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_2.id
        )
        task_6 = TaskFactory(title="other")
        TaskRelationFactory(
            task=task_6, content_type=ContentType.objects.get_for_model(CarePlan), object_id=care_plan_2.id
        )
        self.task_1 = task_1
        self.task_2 = task_2
        self.task_3 = task_3
        self.task_4 = task_4
        self.task_5 = task_5
        self.task_6 = task_6
        self.care_plan_template = care_plan_template

    def test_dry_run_mode(self):
        management.call_command(
            "remove_tasks_from_care_plan",
            care_plan_template_id=self.care_plan_template.id,
            task_title=f"{self.task_1.title}",
            dry_run_off=False,
            user=self.provider,
        )
        self.task_1.refresh_from_db()
        self.task_2.refresh_from_db()
        self.task_3.refresh_from_db()
        self.task_4.refresh_from_db()
        self.task_5.refresh_from_db()
        self.task_6.refresh_from_db()
        self.assertIsNone(self.task_1.deleted)
        self.assertIsNone(self.task_2.deleted)
        self.assertIsNone(self.task_3.deleted)
        self.assertIsNone(self.task_4.deleted)
        self.assertIsNone(self.task_5.deleted)
        self.assertIsNone(self.task_6.deleted)

    def test_remove_tasks(self):
        management.call_command(
            "remove_tasks_from_care_plan",
            care_plan_template_id=self.care_plan_template.id,
            task_title=f"{self.task_1.title}",
            dry_run_off=True,
            user=self.provider,
        )
        management.call_command(
            "remove_tasks_from_care_plan",
            care_plan_template_id=self.care_plan_template.id,
            task_title=f"{self.task_2.title}",
            dry_run_off=True,
            user=self.provider,
        )
        self.task_1.refresh_from_db()
        self.task_2.refresh_from_db()
        self.task_3.refresh_from_db()
        self.task_4.refresh_from_db()
        self.task_5.refresh_from_db()
        self.task_6.refresh_from_db()
        self.assertIsNotNone(self.task_1.deleted)
        self.assertIsNotNone(self.task_2.deleted)
        self.assertIsNone(self.task_3.deleted)
        self.assertIsNotNone(self.task_4.deleted)
        self.assertIsNotNone(self.task_5.deleted)
        self.assertIsNone(self.task_6.deleted)


class CreateCarePlanFromMeasureReport(FireflyTestCase):
    def setUp(self):
        super().setUp()

        md_provider = ProviderDetailFactory.create()
        self.patient1 = PersonUserFactory.create(email="<EMAIL>")
        self.patient1.user.onboarding_state.status = OnboardingStatus.SIGNEDUP
        self.patient1.user.onboarding_state.signedup_at = datetime.datetime.now()
        self.patient1.user.onboarding_state.save()
        self.patient1.care_team.add(md_provider)

        AppointmentFactory(
            patient_id=self.patient1.user.pk,
            practice_id=self.practice.id,
            start=(timezone.now() - timedelta(minutes=30)),
            duration="00:15:00",
            physician=self.physician,
            description="A past appointment which should be cancellable",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            status=AppointmentStatus.CHECKED_OUT.value,
            patient_joined_video=True,
        )

        care_plan_type = CarePlanTypeFactory(uid=STAYING_HEALTHY_CARE_PLAN_UID)
        care_plan_template = CarePlanTemplateFactory(type=care_plan_type)

        measure_collection = MeasureCollectionFactory()
        care_plan_template.measure_collection = measure_collection
        care_plan_template.save()

        self.measure_collection_measure = MeasureCollectionMeasureFactory(measure_collection=measure_collection)

    def test_care_plan_assignment(self):
        MeasureReportFactory(measure=self.measure_collection_measure.measure, person=self.patient1, populations=())
        management.call_command("backfill_create_care_plan", dry_run_off=True, user=self.provider)

        care_plan = CarePlan.objects.get(
            patient=self.patient1.user,
            autocreated_from=CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID),
        )

        self.assertIsNotNone(care_plan)

    def test_care_plan_assignment_without_measure_report(self):
        management.call_command("backfill_create_care_plan", dry_run_off=True, user=self.provider)

        care_plan = CarePlan.objects.filter(
            patient=self.patient1.user,
            autocreated_from=CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID),
        )

        self.assertEqual(0, care_plan.count())

    def test_care_plan_assignment_for_selective_person(self):
        selectivePatient = PersonUserFactory.create(email="<EMAIL>")

        MeasureReportFactory(measure=self.measure_collection_measure.measure, person=selectivePatient, populations=())

        person_ids = "%s" % (selectivePatient.id)

        management.call_command(
            "backfill_create_care_plan", dry_run_off=True, person_ids=person_ids, user=self.provider
        )

        care_plan = CarePlan.objects.get(
            patient=selectivePatient.user,
            autocreated_from=CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID),
        )

        self.assertIsNotNone(care_plan)

    def test_care_plan_assignment_dry_run(self):
        MeasureReportFactory(measure=self.measure_collection_measure.measure, person=self.patient1, populations=())
        management.call_command("backfill_create_care_plan", dry_run_off=False, user=self.provider)

        care_plan = CarePlan.objects.filter(
            patient=self.patient1.user,
            autocreated_from=CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID),
        )

        self.assertEqual(0, care_plan.count())


class FetchEducationResources(FireflyTestCase):
    def test_get_education_resources(self):
        education_resource = EducationResourceFactory()
        url_path = "/care-plan/education-resource/"
        response = self.provider_client.get(url_path)

        education_resource_json = [x for x in response.json() if x["id"] == education_resource.id][0]
        self.assertEqual(education_resource_json["url"], education_resource.url)
        self.assertEqual(education_resource_json["title"], education_resource.title)

    def test_get_multiple_education_resources(self):
        EducationResourceFactory()
        EducationResourceFactory()
        url_path = "/care-plan/education-resource/"
        response = self.provider_client.get(url_path)

        self.assertEqual(len(response.json()), 2)
