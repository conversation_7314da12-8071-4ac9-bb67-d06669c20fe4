import logging
from datetime import datetime, timedelta
from unittest import mock

from django.conf import settings
from django.test import override_settings
from django.utils import timezone

from firefly.core.services.braze.management.commands.braze_5_min_reminder import (
    send_5_mins_push_reminder,
)
from firefly.core.services.braze.management.commands.braze_appointment_reminder_notification import (
    send_24_hours_email_reminder,
    send_30_mins_email_reminder,
)
from firefly.core.services.braze.management.commands.braze_ha_notification_reminders import (
    send_health_assessment_reminder,
    send_health_assessment_reminder_2,
)
from firefly.core.services.elation.client.client import ElationClient
from firefly.core.startup.management.commands.run_scheduler import send_unread_message_reminders
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.models.models import User
from firefly.modules.appointment.constants import AppointmentReason, AppointmentStatus
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.appointment.tests.test import _get_primary_care_data
from firefly.modules.chat_message.models import ChatMessageV2, ChatThread
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.factories import FormFactory
from firefly.modules.forms.models import FormSubmission
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program

logger = logging.getLogger(__name__)


class RemindersTestCase(FireflyTestCase):
    @mock.patch(
        "firefly.core.startup.management.commands.run_scheduler.schedule_unread_message_email"  # noqa
    )
    @mock.patch(
        "firefly.core.startup.management.commands.run_scheduler.schedule_unread_message_notification"  # noqa
    )
    def test_send_unread_message_reminders(self, mock_notification, mock_email):
        self.patient.person.to_covered_insurance(actor=self.patient)
        thread = ChatThread.objects.get(patient=self.patient)
        patient2 = PersonUserFactory.create().user
        patient2.person.to_covered_insurance(actor=patient2)
        thread2 = ChatThread.objects.get(patient=patient2)
        ChatMessageV2.objects.create(
            sender=self.provider,
            text="Testing message from provider 1",
            thread=thread,
            uid="test1",
            sent_at=datetime.now() - timedelta(hours=10),
        )
        ChatMessageV2.objects.create(
            sender=self.provider,
            text="Testing message from provider 2",
            thread=thread,
            uid="test2",
            sent_at=datetime.now() - timedelta(hours=9),
        )
        ChatMessageV2.objects.create(
            sender=self.provider,
            text="Testing message from provider 3",
            thread=thread,
            uid="test3",
            sent_at=datetime.now() - timedelta(hours=25),
        )
        ChatMessageV2.objects.create(
            sender=self.provider,
            text="Testing message from provider 4",
            thread=thread,
            uid="test4",
            sent_at=datetime.now() - timedelta(hours=27),
        )
        ChatMessageV2.objects.create(
            sender=self.provider,
            text="Testing message from provider 5",
            thread=thread2,
            uid="test5",
            sent_at=datetime.now() - timedelta(hours=27),
        )
        send_unread_message_reminders()
        self.assertEquals(mock_notification.call_count, 2)
        self.assertEquals(mock_email.call_count, 2)

        # Calling the reminders again should not send any new notifications to members that already received
        # an email notification for a message received within the 24 hr time frame for which the function is
        # called.
        mock_email.reset_mock()
        send_unread_message_reminders()
        self.assertEquals(mock_email.call_count, 0)

    @mock.patch("firefly.core.services.braze.management.commands.braze_ha_notification_reminders.reminders")
    def test_48_send_health_assessment_reminder(self, mock_reminders):
        patient = self.patient
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
        )

        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=48),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)
        mock_reminders.reset_mock()
        send_health_assessment_reminder(appointments)
        mock_reminders.send.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.patient_health_1_reminder_sent_at)

        FormSubmission.objects.create(
            form=form,
            user=patient,
            expired_at=timezone.now(),
        )
        FormSubmission.objects.create(
            form=form,
            user=patient,
            completed_at=timezone.now(),
        )
        mock_reminders.reset_mock()
        send_health_assessment_reminder(appointments)
        mock_reminders.send.assert_not_called()

    @mock.patch("firefly.core.services.braze.management.commands.braze_ha_notification_reminders.reminders")
    def test_25_with_48_send_health_assessment_reminder(self, mock_reminders):
        patient = self.patient
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
        )

        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=25),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)
        mock_reminders.reset_mock()
        send_health_assessment_reminder(appointments)
        mock_reminders.send.assert_not_called()
        appointment.refresh_from_db()
        self.assertIsNone(appointment.patient_health_1_reminder_sent_at)

    @mock.patch("firefly.core.services.braze.management.commands.braze_ha_notification_reminders.reminders")
    def test_1_send_health_assessment_reminder(self, mock_reminders):
        patient = self.patient
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
        )

        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=1),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)

        mock_reminders.reset_mock()
        send_health_assessment_reminder(appointments)
        send_health_assessment_reminder_2(appointments)
        mock_reminders.send.assert_not_called()
        appointment.refresh_from_db()
        self.assertIsNone(appointment.patient_health_1_reminder_sent_at)
        self.assertIsNone(appointment.patient_health_2_reminder_sent_at)

    @mock.patch("firefly.core.services.braze.management.commands.braze_ha_notification_reminders.reminders")
    def test_25_send_health_assessment_reminder(self, mock_reminders):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
        )

        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=25),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)

        mock_reminders.reset_mock()
        send_health_assessment_reminder_2(appointments)
        mock_reminders.send.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.patient_health_2_reminder_sent_at)

        FormSubmission.objects.create(
            form=form,
            user=patient,
            expired_at=timezone.now(),
        )
        FormSubmission.objects.create(
            form=form,
            user=patient,
            completed_at=timezone.now(),
        )
        mock_reminders.reset_mock()
        send_health_assessment_reminder_2(appointments)
        mock_reminders.send.assert_not_called()

    @mock.patch("firefly.core.services.braze.management.commands.braze_ha_notification_reminders.reminders")
    def test_24_with_25_send_health_assessment_reminder(self, mock_reminders):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")
        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
            is_active=True,
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
        )

        form.refresh_from_db()
        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=24),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)

        mock_reminders.reset_mock()
        send_health_assessment_reminder_2(appointments)
        mock_reminders.send.assert_not_called()
        appointment.refresh_from_db()
        self.assertIsNone(appointment.patient_health_2_reminder_sent_at)

    @mock.patch("firefly.core.services.braze.management.commands.braze_appointment_reminder_notification.reminders")
    def test_24_hour_appointment_reminder(self, mock_reminders):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")

        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(hours=24),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)

        mock_reminders.reset_mock()
        send_24_hours_email_reminder(appointments)
        mock_reminders.send.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.patient_24_reminder_sent_at)

        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
            expired_at=timezone.now(),
        )
        FormSubmission.objects.create(
            form=form,
            user=patient,
            completed_at=timezone.now(),
        )
        mock_reminders.reset_mock()
        send_24_hours_email_reminder(appointments)
        mock_reminders.send.assert_not_called()

    @override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True))
    @mock.patch("firefly.core.services.braze.management.commands.braze_appointment_reminder_notification.reminders")
    @mock.patch.object(ElationClient, "put")
    @mock.patch.object(ElationClient, "init_session")
    def test_30_min_appointment_reminder(self, _init_session_mock, update_record_mock, mock_reminders):
        # explicitly connect the model handler since we
        # want to test this behavior
        # In a deployed environment - this gets auto loaded
        # when the files are loaded into memory.
        # appointment/apps.py loads signals.py which connects the listener
        # in a testing environment - it is likely that the files are loaded
        # before the database handle is ready
        ElationAppointmentSync().connect_model_listener()
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")

        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(minutes=30),
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )
        another_appointment = AppointmentFactory.create(
            patient=self.patient,
            start=timezone.now() + timedelta(minutes=30),
            status="Scheduled",
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False).order_by(
            "created_at", "pk"
        )

        mock_reminders.reset_mock()
        mock_reminders.send.side_effect = [
            Exception("I will fail for some reason"),  # let the first one fail
            None,  # and the second one pass without errors
        ]
        update_record_mock.reset_mock()
        with self.assertRaises(Exception):
            send_30_mins_email_reminder(appointments)
        update_record_mock.assert_not_called()  # verify that sending reminders does not trigger an elation sync
        another_appointment.refresh_from_db()
        appointment.refresh_from_db()
        mock_reminders.send.assert_called_once()
        self.assertIsNotNone(another_appointment.patient_30_mins_reminder_sent_at)
        self.assertIsNotNone(appointment.patient_30_mins_reminder_sent_at)

        form = FormFactory(
            uid=FormUID.HEALTH_ASSESSMENT_V3,
            title="Health Review",
            description="Test",
        )

        FormSubmission.objects.create(
            form=form,
            user=patient,
            expired_at=timezone.now(),
        )
        FormSubmission.objects.create(
            form=form,
            user=patient,
            completed_at=timezone.now(),
        )
        mock_reminders.reset_mock()
        send_30_mins_email_reminder(appointments)
        mock_reminders.send.assert_not_called()

    @mock.patch("firefly.core.services.braze.management.commands.braze_5_min_reminder.reminders")
    def test_5_min_appointment_reminder(self, mock_reminders):
        patient = self.patient
        User.objects.get_or_create(first_name="Member", last_name="Guide")

        add_person_to_program(patient.person, ProgramCodes.PRIMARY_CARE)
        practice, pcp = _get_primary_care_data(patient.person)

        # Create appointment
        appointment = AppointmentFactory(
            patient_id=patient.pk,
            practice_id=practice.id,
            physician_id=pcp.id,
            start=timezone.now() + timedelta(minutes=5),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            time_slot_type="appointment_slot",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointments = Appointment.objects.filter(status="Scheduled", patient__isnull=False)

        mock_reminders.reset_mock()
        send_5_mins_push_reminder(appointments)
        mock_reminders.send.assert_called_once()
        appointment.refresh_from_db()
        self.assertIsNotNone(appointment.patient_5_mins_reminder_sent_at)
