from django.conf import settings
from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3


class EducationContentFeedback(BaseModelV3):
    text = models.TextField()
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    category = models.CharField(max_length=255)  # noqa: TID251
    urgency = models.IntegerField(default=0, db_index=True, null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    content_id = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    content_title = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    content_url = models.URLField(null=True, blank=True)
    submitted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        related_name="education_content_feedback",
    )
