import logging

from django.db import models

from firefly.core.user.models import User
from firefly.modules.billing.constants import PracticeSuiteFailures
from firefly.modules.firefly_django.models import BaseModelV3

logger = logging.getLogger(__name__)


class PracticeSuiteInvoice(BaseModelV3):
    """Practice Suite Invoice Excel"""

    file = models.FileField(upload_to="practice-suite-invoice/")
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    file_name = models.CharField(max_length=255, unique=True)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "practice_suite_invoice"


class PracticeSuiteInvoiceItem(BaseModelV3):
    """Line Items for Practice Suite Invoice"""

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    invoice = models.ForeignKey(PracticeSuiteInvoice, on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    user = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251

    # This is the email reported from the invoice, may not match the one in the User model.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    email = models.CharField(max_length=255)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    statement_number = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    mr_number = models.TextField(null=True, blank=True)
    email_sent_at = models.DateTimeField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    failed_reason = models.CharField(max_length=50, null=True, blank=True, choices=PracticeSuiteFailures.choices)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "practice_suite_invoice_items"
        unique_together = (
            "invoice",
            "statement_number",
        )
