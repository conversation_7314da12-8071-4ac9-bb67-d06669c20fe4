from datetime import datetime, timed<PERSON><PERSON>
from unittest import TestCase
from unittest.mock import <PERSON>Mock, patch

from django.core.exceptions import ValidationError
from django.http import HttpRequest
from dns.rdataclass import RdataClass
from dns.rdtypes.ANY.CNAME import CNAME as CNAME_Rtype
from rest_framework.test import APIRequestFactory

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.features.permissions import HasAccessToFeature
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.modules.tenants.factories import (
    ConsentFormFactory,
    ReleaseAnnouncementFactory,
    TenantFactory,
    TenantSecretKeyFactory,
)
from firefly.modules.tenants.middleware import TenancyMiddleware, TenantAccessMiddleware
from firefly.modules.tenants.models import ConsentForm, ReleaseAnnouncement, Tenant
from firefly.modules.tenants.utils import (
    get_tenant_from_member_signup_request,
    get_tenant_key_from_request,
    get_tenants_for_user,
    is_person_in_firefly,
)


class TenantTestCase(FireflyTestCase):
    def test_patient_tenant_list_view(self):
        response = self.client.get("/tenants/")
        patient_tenant_keys = list(map(lambda tenant: tenant.key, self.patient.person.tenants.all()))
        tenant_keys_in_response = list(map(lambda el: el["key"], response.json()["results"]))
        self.assertEqual(patient_tenant_keys, tenant_keys_in_response)
        response = self.provider_client.get("/tenants/")
        self.assertEqual(response.json()["results"], [])

    def test_tenant_consent_form(self):
        tenant: Tenant = TenantFactory.create()
        consent_form: ConsentForm = ConsentFormFactory(
            form_name="Email Policy",
            html_content="<h1>Email policy:-</h1>",
            version="v3",
            key="email",
        )
        tenant.consent_forms.add(consent_form)
        response = self.provider_client.get(f"/providers/me/tenants/config/?tenant_key={tenant.key}")
        self.assertEqual(response.status_code, 200)
        tenant_from_response = response.json()
        self.assertEqual(tenant_from_response["consent_forms"][0]["form_name"], consent_form.form_name)
        self.assertEqual(tenant_from_response["consent_forms"][0]["key"], consent_form.key)
        consent_form.refresh_from_db()
        self.assertEqual(consent_form.tenants.all().count(), 1)
        self.assertEqual(consent_form.tenants.first(), tenant)

        tenant.consent_forms.clear()
        new_consent_form: ConsentForm = ConsentFormFactory(
            version="v2",
        )
        tenant.consent_forms.add(new_consent_form)
        response = self.provider_client.get(f"/providers/me/tenants/config/?tenant_key={tenant.key}")
        self.assertEqual(response.status_code, 200)
        tenant_from_response = response.json()
        self.assertEqual(tenant_from_response["consent_forms"][0]["form_name"], new_consent_form.form_name)
        self.assertEqual(tenant_from_response["consent_forms"][0]["key"], new_consent_form.key)
        consent_form.refresh_from_db()
        self.assertEqual(consent_form.tenants.all().count(), 0)
        self.assertEqual(new_consent_form.tenants.all().count(), 1)
        self.assertIsNone(consent_form.tenants.first())
        self.assertEqual(new_consent_form.tenants.first(), tenant)

        tenant.consent_forms.clear()
        tenant.consent_forms.add(consent_form)
        response = self.provider_client.get(f"/providers/me/tenants/config/?tenant_key={tenant.key}")
        self.assertEqual(response.status_code, 200)
        tenant_from_response = response.json()
        self.assertEqual(tenant_from_response["consent_forms"][0]["form_name"], consent_form.form_name)
        self.assertEqual(tenant_from_response["consent_forms"][0]["key"], consent_form.key)
        consent_form.refresh_from_db()
        self.assertEqual(consent_form.tenants.all().count(), 1)
        self.assertEqual(consent_form.tenants.first(), tenant)

    def test_tenant_release_announcement(self):
        tenant: Tenant = TenantFactory.create()
        release_announcement_1: ReleaseAnnouncement = ReleaseAnnouncementFactory(
            expiration=datetime.now() + timedelta(days=10),
        )
        release_announcement_2: ReleaseAnnouncement = ReleaseAnnouncementFactory(
            key="xyz",
            expiration=datetime.now() - timedelta(days=10),
        )
        tenant.release_announcements.add(release_announcement_1)
        tenant.release_announcements.add(release_announcement_2)
        response = self.client.get(f"/me/tenants/config/?tenant_key={tenant.key}")
        self.assertEqual(response.status_code, 200)
        tenant_from_response = response.json()
        self.assertEqual(len(tenant_from_response["release_announcements"]), 1)
        self.assertEqual(tenant_from_response["release_announcements"][0]["name"], release_announcement_1.name)
        self.assertEqual(tenant_from_response["release_announcements"][0]["key"], release_announcement_1.key)
        self.assertEqual(tenant_from_response["release_announcements"][0]["id"], release_announcement_1.id)


class TenantSecretKeyTestCase(FireflyTestCase):
    def test_new_tenant_no_secrets(self):
        tenant = TenantFactory.create()
        # ZUS_CLIENT_ID and ZUS_CLIENT_SECRET are part of the base test
        self.assertEqual(tenant.aws_secrets_config, {"ZUS_CLIENT_ID": None, "ZUS_CLIENT_SECRET": None})

    def test_new_tenant_with_secrets(self):
        secret1 = TenantSecretKeyFactory.create()
        secret2 = TenantSecretKeyFactory.create()

        tenant = TenantFactory.create()

        self.assertEqual(
            tenant.aws_secrets_config,
            {
                secret1.name: None,
                secret2.name: None,
                "ZUS_CLIENT_ID": None,
                "ZUS_CLIENT_SECRET": None,
            },
        )

    def test_add_new_secret_key(self):
        tenant1 = TenantFactory.create()
        tenant2 = TenantFactory.create()

        secret1 = TenantSecretKeyFactory.create()
        secret2 = TenantSecretKeyFactory.create()

        tenant1.refresh_from_db()
        tenant2.refresh_from_db()

        self.assertEqual(
            tenant1.aws_secrets_config,
            {
                secret1.name: None,
                secret2.name: None,
                "ZUS_CLIENT_ID": None,
                "ZUS_CLIENT_SECRET": None,
            },
        )
        self.assertEqual(
            tenant2.aws_secrets_config,
            {
                secret1.name: None,
                secret2.name: None,
                "ZUS_CLIENT_ID": None,
                "ZUS_CLIENT_SECRET": None,
            },
        )

    def test_modify_existing_secret_key(self):
        secret1 = TenantSecretKeyFactory.create()
        secret2 = TenantSecretKeyFactory.create()

        tenant1 = TenantFactory.create()

        secret1.name = "new_key"
        secret1.save()

        tenant1.refresh_from_db()

        self.assertEqual(
            tenant1.aws_secrets_config,
            {"new_key": None, secret2.name: None, "ZUS_CLIENT_ID": None, "ZUS_CLIENT_SECRET": None},
        )

    def test_delete_secret_key(self):
        secret1 = TenantSecretKeyFactory.create()
        secret2 = TenantSecretKeyFactory.create()

        tenant1 = TenantFactory.create()
        tenant2 = TenantFactory.create()

        secret1.delete()

        tenant1.refresh_from_db()
        tenant2.refresh_from_db()

        self.assertEqual(
            tenant1.aws_secrets_config,
            {secret2.name: None, "ZUS_CLIENT_ID": None, "ZUS_CLIENT_SECRET": None},
        )
        self.assertEqual(
            tenant2.aws_secrets_config,
            {secret2.name: None, "ZUS_CLIENT_ID": None, "ZUS_CLIENT_SECRET": None},
        )

    @patch("firefly.modules.tenants.models.get_aws_secret")
    def test_get_aws_secret_value(self, mock_get_aws_secret):
        secret1 = TenantSecretKeyFactory.create()
        TenantSecretKeyFactory.create()
        mock_get_aws_secret.return_value = "Test String"
        tenant1 = TenantFactory.create()
        secret = tenant1.get_tenant_aws_secret(secret1.name)
        self.assertEqual(secret, "Test String")

    def test_aws_secret_config_validation(self):
        secret1 = TenantSecretKeyFactory.create()
        TenantSecretKeyFactory.create()
        tenant1 = TenantFactory.create()
        with self.assertRaises(ValidationError):
            tenant1.aws_secrets_config["some_new_value"] = None
            # This function is required to be called in order for validation to run
            if tenant1.full_clean():
                tenant1.save()

        with self.assertRaises(ValidationError):
            del tenant1.aws_secrets_config[secret1.name]
            if tenant1.full_clean():
                tenant1.save()


class TenantUtilsTestCase(FireflyTestCase):
    def test_get_tenants_for_user(self):
        self.request = MagicMock()
        self.request.lucian_user_tenant = FIREFLY_TENANT_KEY
        self.request.user = self.provider
        expected_tenant = self.tenant.id

        # Ensure we have no queries traversing self.provider
        self.provider.providerdetail.tenant_id

        with self.assertNumQueries(0):
            self.assertEqual(get_tenants_for_user(self.request), self.provider.providerdetail.tenant_id)
        with self.assertNumQueries(0):
            self.assertEqual(get_tenants_for_user(self.request), expected_tenant)
        self.request.lucian_user_tenant = None
        expected_tenant = self.patient.person.tenants.all().values_list("id", flat=True)[0]
        with self.assertNumQueries(0):
            self.assertEqual(get_tenants_for_user(self.request), expected_tenant)

    def test_is_person_in_firefly(self):
        self.assertTrue(is_person_in_firefly(self.patient.person))
        self.patient.person.tenants.clear()
        self.assertFalse(is_person_in_firefly(self.patient.person))

    def test_get_tenant_key_from_request(self):
        request = MagicMock()
        request.source_is_patient_client = False
        tenant_key = get_tenant_key_from_request(request)
        self.assertEqual(tenant_key, request.user.providerdetail.tenant.key)

        test_tenant_key = "example-tenant-key"
        request.source_is_patient_client = True
        request.lucian_user_tenant = test_tenant_key
        tenant_key = get_tenant_key_from_request(request)
        self.assertEqual(tenant_key, test_tenant_key)

    @patch("firefly.modules.tenants.utils.dns.resolver.resolve")
    def test_get_tenant_from_member_signup_request(self, mocked_resolve):
        # Check the case where the request comes from a Firefly-owned signup domain
        partner_tenant = TenantFactory()
        firefly_partner_domain = f"signup-{partner_tenant.key}.partners.firefly.health"
        request = HttpRequest()
        request.META["HTTP_ORIGIN"] = "https://" + firefly_partner_domain
        result = get_tenant_from_member_signup_request(request)
        self.assertEqual(result, partner_tenant)
        mocked_resolve.assert_not_called()

        # Check the case where the request comes from a domain which is not a Firefly-owned signup domain
        # and there is no valid CNAME record
        #
        # Mock the DNS lookup to return a irrelevant CNAME result (doesn't match a Firefly-owned signup domain)
        mocked_resolve.return_value = [CNAME_Rtype(rdclass=RdataClass.INTERNET, rdtype=1, target="random.aws.com")]
        request = HttpRequest()
        other_domain = "abc.partner.com"
        request.META["HTTP_ORIGIN"] = "https://" + other_domain
        result = get_tenant_from_member_signup_request(request)
        mocked_resolve.assert_called_once_with(other_domain, "CNAME")
        # Expect no match, we should default to using the Firefly Tenant
        firefly_tenant = Tenant.objects.get(key=FIREFLY_TENANT_KEY)
        self.assertEqual(result, firefly_tenant)
        mocked_resolve.reset_mock()

        # Mock the DNS lookup to return an Firefly-owned signup domain
        mocked_resolve.return_value = [
            CNAME_Rtype(
                rdclass=RdataClass.INTERNET,
                rdtype=1,
                target=firefly_partner_domain,
            )
        ]
        result = get_tenant_from_member_signup_request(request)
        mocked_resolve.assert_called_once_with(other_domain, "CNAME")
        self.assertEqual(result, partner_tenant)

    def test_tenant_config_with_query_param(self):
        tenant = TenantFactory()
        response = self.provider_client.get(f"/tenants/config/?tenant_key={tenant.key}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["id"], tenant.id)
        self.assertEqual(response.json()["name"], tenant.name)
        self.assertEqual(response.json()["after_hours_emergency_message"], tenant.after_hours_emergency_message)


class TenancyMiddlewareTestCase(TestCase):
    def test_process_request(self):
        factory = APIRequestFactory()
        request = factory.get("/test/")
        test_tenant_key = "example-tenant-key"
        request.headers = {"lucian-user-tenant": test_tenant_key}
        get_response = MagicMock(return_value=None)
        middleware = TenancyMiddleware(get_response=get_response)
        middleware.process_request(request)
        self.assertEqual(request.lucian_user_tenant, test_tenant_key)
        request.headers = {}
        middleware.process_request(request)
        self.assertEqual(request.lucian_user_tenant, None)


class TenantAccessMiddlewareTestCase(TestCase):
    def test_process_view(self):
        get_response = MagicMock(return_value=None)
        middleware = TenantAccessMiddleware(get_response=get_response)
        # Scenerio 1: If skip_tenant_access_check present and True it should not append HasAccessToFeature
        request = MagicMock()
        request.view_func.cls.skip_tenant_access_check = True
        middleware.process_view(request, request.view_func, None, None)
        self.assertFalse(HasAccessToFeature in request.view_func.cls.permission_classes)
        # Scenerio 2: If skip_tenant_access_check present and False it should append HasAccessToFeature
        request = MagicMock()
        request.view_func.cls.skip_tenant_access_check = False
        middleware.process_view(request, request.view_func, None, None)
        self.assertTrue(HasAccessToFeature in request.view_func.cls.permission_classes)
        # Scenerio 3: If skip_tenant_access_check True and feature_name not present it should not throw execption
        request = MagicMock()
        request.view_func.cls.skip_tenant_access_check = True
        delattr(request.view_func.cls, "feature_name")
        middleware.process_view(request, request.view_func, None, None)
        self.assertFalse(HasAccessToFeature in request.view_func.cls.permission_classes)
        # Scenerio 4: If skip_tenant_access_check not present and feature_name not present it should throw execption
        request = MagicMock()
        delattr(request.view_func.cls, "skip_tenant_access_check")
        delattr(request.view_func.cls, "feature_name")
        self.assertRaisesRegex(
            Exception,
            "feature_name not defined for view",
            lambda: middleware.process_view(request, request.view_func, None, None),
        )
        # Scenerio 5: If skip_tenant_access_check not present and feature_name present it should append
        # HasAccessToFeature
        request = MagicMock()
        delattr(request.view_func.cls, "skip_tenant_access_check")
        request.view_func.cls.feature_name = "test_feature"
        middleware.process_view(request, request.view_func, None, None)
        self.assertTrue(HasAccessToFeature in request.view_func.cls.permission_classes)
        self.assertEqual(request.view_func.cls.permission_classes.count(HasAccessToFeature), 1)
        # reprocess the view to simulate a subsequent request
        # if HasAccessToFeature has already been added to the permission classes
        # this should not feature in the permission classes multiple times
        middleware.process_view(request, request.view_func, None, None)
        self.assertEqual(request.view_func.cls.permission_classes.count(HasAccessToFeature), 1)


class ConsentFormTestCase(FireflyTestCase):
    def test_consent_form(self):
        test_consent_form = self.consent_form

        self.assertEqual(test_consent_form.form_name, "Privacy Policy")
        self.assertEqual(test_consent_form.html_content, "<h1>Test Form</h1>")
        self.assertEqual(test_consent_form.version, "v1")
        self.assertEqual(test_consent_form.key, "consentKey")

    def test_consent_form_api(self):
        ConsentFormFactory(
            form_name="Email Policy",
            html_content="<h1>Email policy:-</h1>",
            version="2021",
            key="email",
        )
        latest_consent_form = ConsentFormFactory(
            form_name="Email Policy",
            html_content="<h1>Email policy:-</h1>",
            version="2022",
            key="email",
        )
        ConsentFormFactory(
            form_name="Email Policy",
            html_content="<h1>Email policy:-</h1>",
            version="2020",
            key="email",
        )

        response = self.client.get("/tenants/consent_form/email/")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data["version"], latest_consent_form.version)
        self.assertEqual(data["key"], latest_consent_form.key)


class UserConsentFormTestCase(FireflyTestCase):
    def test_user_consent_form(self):
        # without any consent form
        response = self.provider_client.get("/tenants/user_agreement/")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 0)
        # with single consent form
        test_consent_form = self.consent_form
        user_consent_payload = {
            "consent_forms": [test_consent_form.id],
        }
        response = self.provider_client.post("/tenants/user_agreement/", user_consent_payload, format="json")
        self.assertEqual(response.status_code, 201)

        response = self.provider_client.get("/tenants/user_agreement/")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data[0]["consent_form"], test_consent_form.id)

        # with multiple consent form
        test_consent_form_1 = ConsentFormFactory(version="v2")
        test_consent_form_2 = ConsentFormFactory(version="v3")
        user_consent_payload = {
            "consent_forms": [test_consent_form_1.id, test_consent_form_2.id],
        }
        response = self.provider_client.post("/tenants/user_agreement/", user_consent_payload, format="json")
        self.assertEqual(response.status_code, 201)

        response = self.provider_client.get("/tenants/user_agreement/")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(len(data), 3)
