# Tenants

## Motivation

The tenants module provides multi-tenant architecture support for Firefly's platform, enabling secure isolation and customization for different network partners and client organizations. It manages tenant-specific configurations, AWS secrets, feature access controls, and branding while maintaining data security and regulatory compliance.

## Business Context

Tenant management is fundamental to Firefly's multi-client architecture, supporting:
- Network partner isolation and customization
- Client-specific branding and user experience
- Feature access control and progressive rollouts
- Secure secrets management for third-party integrations
- Consent form and legal document management
- DNS-based tenant resolution for web signup flows

## Core Concepts

### Tenant Architecture
- **Tenant**: Primary entity representing network partners or client organizations
- **TenantSecretKey**: Registry of valid secret keys for AWS Secrets Manager integration
- **ConsentForm**: Legal documents and consent forms specific to tenants
- **ReleaseAnnouncement**: Tenant-specific feature announcements and updates

### Multi-Tenancy Model
- **User Association**: Users linked to specific tenants through provider details or person relationships
- **Feature Isolation**: Tenant-specific feature access configuration
- **Data Segregation**: Tenant-based data access controls and filtering
- **Branding Customization**: Tenant-specific logos, CSS, and UI elements

## Technical Implementation

### Core Models

**Tenant**: Central tenant configuration and management
- Unique tenant key for DNS and identification
- Feature access configuration via JSON field
- AWS secrets configuration for secure third-party integration
- Branding assets (logos, CSS, signup images)
- Emergency messaging and after-hours configuration

**AWS Secrets Integration**: Scalable secrets management
- Tenant-specific secret paths in AWS Secrets Manager
- Validation against registered `TenantSecretKey` entries
- Cached secret retrieval for performance optimization
- Secure access patterns for third-party API credentials

**Consent and Legal Management**: Tenant-specific legal documents
- Many-to-many relationships with consent forms and release announcements
- Version control and tenant-specific legal requirements
- User consent tracking and compliance management

### Middleware Integration

**TenancyMiddleware**: Request-level tenant identification
- Header-based tenant detection for patient-facing applications
- Provider-based tenant resolution for administrative access
- Source identification for patient vs. provider requests

**TenantAccessMiddleware**: Feature access enforcement
- Automatic feature permission checking for all views
- Integration with feature flags and access controls
- Bypass mechanisms for system-level operations

### DNS and Domain Resolution

**Web Signup Integration**: Multi-domain tenant resolution
- DNS CNAME resolution for partner domains
- Regex-based tenant key extraction from Firefly domains
- Fallback to Firefly tenant for unresolved domains
- Support for both partner-owned and Firefly-hosted domains

## Business Logic

### Tenant Resolution

**Request-Based Tenant Detection**: Multi-source tenant identification
1. **Patient Requests**: Header-based tenant key from patient-facing applications
2. **Provider Requests**: Tenant association through provider details
3. **Web Signup**: DNS-based resolution for partner domains
4. **Fallback**: Default to Firefly tenant for unresolved requests

**Domain Management**: Flexible domain configuration
- Partner-owned domains with CNAME pointing to Firefly infrastructure
- Firefly-hosted subdomains with tenant key extraction
- DNS resolution with fallback mechanisms
- Support for localhost development environments

### AWS Secrets Management

**Scalable Secret Storage**: Tenant-specific secret management
- Centralized secret key registry through `TenantSecretKey`
- Tenant-specific AWS Secrets Manager paths
- Automatic validation and configuration updates
- Cached retrieval for performance optimization

**Secret Access Pattern**:
1. **Register Secret Type**: Create `TenantSecretKey` entry
2. **Configure Tenant Paths**: Update `aws_secrets_config` with AWS paths
3. **Access Secrets**: Use `tenant.get_tenant_aws_secret(key)` method
4. **Validation**: Automatic validation against registered keys

### Feature Access Control

**Tenant-Specific Features**: Granular feature management
- JSON-based feature configuration per tenant
- Integration with feature flags system
- Progressive rollout capabilities
- Override mechanisms for specific tenants

**Permission Integration**: Automatic feature enforcement
- Middleware-based permission checking
- View-level feature requirements
- Bypass mechanisms for system operations
- Integration with user authentication and authorization

## Configuration

### Adding New Tenants

**Tenant Creation Process**:
1. **Create Tenant**: Add tenant with unique key and configuration
2. **Configure Secrets**: Set up AWS secrets paths for required integrations
3. **Feature Access**: Configure tenant-specific feature access
4. **Branding**: Upload logos and configure CSS customization
5. **Legal Documents**: Associate consent forms and release announcements

### AWS Secrets Setup

**Secret Management Workflow**:
1. **Register Secret Type**: Create `TenantSecretKey` with descriptive name
2. **Provision AWS Secrets**: Create tenant-specific secrets in AWS Secrets Manager
3. **Configure Paths**: Update tenant `aws_secrets_config` with secret paths
4. **Validate Access**: Test secret retrieval using `get_tenant_aws_secret()`

**Example Secret Configuration**:
```json
{
  "zus_client_secret": "",
  "external_api_key": ""
}
```

### Domain Configuration

**DNS Setup for Partner Domains**:
1. **CNAME Configuration**: Point partner domain to Firefly infrastructure
2. **Tenant Key Mapping**: Ensure tenant key matches domain configuration
3. **SSL Certificates**: Configure SSL for partner domains
4. **Testing**: Verify domain resolution and tenant detection

## API Integration

**Tenant Context**: Automatic tenant context in all requests
- Middleware-based tenant detection and injection
- Request-level tenant information for business logic
- Integration with authentication and authorization systems

**Feature Enforcement**: Automatic feature access checking
- View-level feature requirements
- Permission class integration
- Bypass mechanisms for system operations