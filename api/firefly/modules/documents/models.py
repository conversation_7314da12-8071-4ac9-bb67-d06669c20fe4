import os

from django.conf import settings
from django.db import models
from django_deprecate_fields import deprecate_field

from firefly.core.user.models import Person, ProviderDetail
from firefly.modules.documents.mixin import BaseFile
from firefly.modules.eligibility.models import CoveredMember
from firefly.modules.firefly_django.models import BaseModelV3


def get_patient_folder(instance, filename):
    user_id = instance.person.user.pk
    folder = f"documents/shared-files/patients/{user_id}"
    return os.path.join(folder, filename)


def get_digital_id_folder(instance, filename):
    folder = f"documents/person/digital-id-card/{instance.person.pk}"
    return os.path.join(folder, filename)


class POCImage(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name="poc")  # noqa: TID251
    provider = models.ForeignKey(ProviderDetail, on_delete=models.CASCADE)
    # Retaining `signature` field to maintain backwards compatibility in old app versions,
    # adding `text_signature` to pivot to
    text_signature = models.TextField(blank=True, default="")
    signature = models.ImageField(null=True, blank=True, upload_to="documents/poc-images/")
    document = models.ImageField(null=True, blank=True, upload_to="documents/poc-images/")


class POCTemplate(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, db_index=True, unique=True)  # noqa: TID251
    description = models.TextField(blank=True, default="")
    template_text = models.TextField()
    pdf_file = models.FileField(null=True, blank=True, upload_to="documents/poc-templates/")


class PatientSharedFile(BaseFile):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    sent_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    person = models.ForeignKey(Person, on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251
    file = models.FileField(upload_to=get_patient_folder)


class PatientDigitalIDCard(BaseModelV3):
    person = models.OneToOneField(
        "user.Person",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="digital_id_card",
        null=True,
        blank=True,
    )
    pdf_file = models.FileField(upload_to=get_digital_id_folder, null=True, blank=True)
    member_info = deprecate_field(models.JSONField(default=list, null=True, blank=True))

    # TODO(Saranya): remove the member_info field and rename related_member_info and member_info
    @property
    def related_member_info(self):
        """
        only for primary subscriber dependent details will be returned.
        """
        member_info_dicts = []

        primary_subscriber_person = CoveredMember.objects.get(
            covered_member_person=self.person
        ).primary_subscriber_person
        covered_members = CoveredMember.objects.filter(primary_subscriber_person=primary_subscriber_person)
        covered_members = covered_members.exclude(covered_member_person=self.person)
        for covered_member in covered_members:
            person = covered_member.covered_member_person
            member_info_dicts.append(
                {
                    "pk": person.pk,
                    "first_name": person.first_name,
                    "last_name": person.last_name,
                    "tpa_id": person.insurance_info.member_id,
                }
            )
        return member_info_dicts
