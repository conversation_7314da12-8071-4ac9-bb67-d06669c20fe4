import os
from datetime import datetime
from unittest import mock

from django.contrib.contenttypes.models import ContentType
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.management import call_command
from django.test import override_settings

from firefly.core.alias.models import AliasMapping, get_content_type
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.models import User
from firefly.core.user.models.models import Person
from firefly.modules.accumulators.models import Accumulators
from firefly.modules.documents.models import PatientSharedFile
from firefly.modules.eligibility.models import CoveredMember
from firefly.modules.insurance.constants import ContractAttributionType, ContractPMPMType, EmployerName
from firefly.modules.insurance.factories import ContractFactory
from firefly.modules.insurance.models import Employer, InsurancePayer, InsurancePlan
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER, FIREFLY_PLAN
from firefly.modules.programs.constants import BenefitProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program

from .models import PatientDigitalIDCard


@mock.patch("firefly.modules.documents.api.send_poc_selection_slack_notification")
@mock.patch("firefly.modules.documents.utils.reminders")
@mock.patch("firefly.core.services.braze.client.BrazeClient.__init__")
@override_settings(POC_PROVIDER_NPI=123)
@override_settings(BRAZE={"POC_CONFIRMATION": "test_campaign_id"})
class POCV2TestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.patient.person.insurance_info.member_id = "123456789-00"
        self.patient.person.insurance_info.save()
        self.physician.npi = 123
        self.physician.save()

    def test_poc_update_v2(self, mock_init, mock_reminders, mock_send_poc_selection_slack_notification):
        user_id = self.patient.pk
        mock_init.return_value = None
        response = self.client.post("/documents/poc/")
        self.assertEqual(response.status_code, 204)
        mock_reminders.send.assert_not_called()
        mock_send_poc_selection_slack_notification.assert_called_once_with(user_id, "")

        dummy_provider_id = "999"
        response = self.client.post("/documents/poc/", {"provider": dummy_provider_id}, format="json")
        mock_send_poc_selection_slack_notification.assert_called_with(user_id, dummy_provider_id)


class PatientToProviderFileSharingTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.user_id = self.patient.pk
        self.person_id = self.patient.person.pk
        self.provider_id = self.provider.pk
        self.file = SimpleUploadedFile("testfile.pdf", b"file_content", content_type="application/pdf")
        self.data = {
            "file": self.file,
            "title": "Sample file",
            "description": "File description",
        }
        response = self.provider_client.post(
            f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/", self.data
        )
        self.assertEqual(response.status_code, 201)
        self.file_id = response.json()["id"]

    def test_patient_download_file(self):
        response = self.client.get(f"/documents/patients/{self.user_id}/files/{self.file_id}/")
        self.assertEqual(response.status_code, 200)
        file_data = response.json()
        self.assertEqual(file_data["title"], self.data["title"])
        self.assertEqual(file_data["description"], self.data["description"])
        self.assertEqual(file_data["person"], self.person_id)

    def test_patient_get_file_stub(self):
        response = self.client.get(f"/documents/patients/{self.user_id}/files/{self.file_id}/stub/")
        self.assertEqual(response.status_code, 200)
        file_stub = response.json()
        self.assertEqual(file_stub["title"], self.data["title"])
        self.assertEqual(file_stub["description"], self.data["description"])
        self.assertEqual(file_stub["person"], self.person_id)

    def test_patient_access(self):
        response = self.client.get(f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/", self.data)
        self.assertEqual(response.status_code, 403)
        response = self.client.get(
            f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/{self.file_id}/",
            self.data,
        )
        self.assertEqual(response.status_code, 403)
        response = self.client.get(
            f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/{self.file_id}/stub/",
            self.data,
        )
        self.assertEqual(response.status_code, 403)

    def test_other_patient_no_access(self):
        user = User.objects.create(email="<EMAIL>")
        payer, _ = InsurancePayer.objects.get_or_create(name="Aetna")
        patient_create_payload = {
            "first_name": "Pickle",
            "last_name": "Rick",
            "phone_number": "**********",
            "preferred_name": "Care",
            "sex": "Male",
            "gender": ["Male"],
            "pronouns": "He/Him/His",
            "dob": "1992-11-21",
            "created_from": "app",
            "patient_referral_program": "",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": payer.id,
                "member_id": "MA12312312",
            },
            "consent_forms": [self.consent_form.id],
        }
        client = FireflyTestCase.get_member_client(member=user)
        response = client.post("/bff/app/signup/member-id-confirmation/", patient_create_payload, format="json")
        other_person = Person.objects.get(user=user)
        shared_file = PatientSharedFile.objects.create(file=self.file, person=other_person)
        response = self.client.get(f"/documents/patients/{self.user_id}/files/{shared_file.pk}/")
        self.assertEqual(response.status_code, 403)
        response = self.client.get(f"/documents/patients/{other_person.pk}/files/{shared_file.pk}/")
        self.assertEqual(response.status_code, 403)
        response = self.client.get(f"/documents/patients/{self.user_id}/files/{shared_file.pk}/stub/")
        self.assertEqual(response.status_code, 403)
        response = self.client.get(f"/documents/patients/{other_person.pk}/files/{shared_file.pk}/stub/")
        self.assertEqual(response.status_code, 403)

    def tearDown(self):
        super().tearDown()
        os.remove(f"documents/shared-files/patients/{self.user_id}/testfile.pdf")


class ProviderToProviderFileSharingTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.user_id = self.patient.pk
        self.person_id = self.patient.person.pk
        self.provider_id = self.provider.pk
        file = SimpleUploadedFile("testfile.pdf", b"file_content", content_type="application/pdf")
        self.data = {
            "file": file,
            "title": "Sample file",
            "description": "File description",
        }
        response = self.client.post(f"/documents/patients/{self.user_id}/files/", self.data)
        self.assertEqual(response.status_code, 201)
        self.file_id = response.json()["id"]

    def test_provider_download_file(self):
        response = self.provider_client.get(
            f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/{self.file_id}/"
        )
        self.assertEqual(response.status_code, 200)
        file_data = response.json()
        self.assertEqual(file_data["title"], self.data["title"])
        self.assertEqual(file_data["description"], self.data["description"])
        self.assertEqual(file_data["person"], self.person_id)

    def test_provider_get_file_stub(self):
        response = self.provider_client.get(
            f"/documents/providers/{self.provider_id}/patients/{self.user_id}/files/{self.file_id}/stub/"
        )
        self.assertEqual(response.status_code, 200)
        file_stub = response.json()
        self.assertEqual(file_stub["title"], self.data["title"])
        self.assertEqual(file_stub["description"], self.data["description"])
        self.assertEqual(file_stub["person"], self.person_id)

    def test_provider_access(self):
        response = self.provider_client.get(f"/documents/patients/{self.user_id}/files/{self.file_id}/", self.data)
        self.assertEqual(response.status_code, 403)
        response = self.provider_client.get(f"/documents/patients/{self.user_id}/files/{self.file_id}/stub/", self.data)
        self.assertEqual(response.status_code, 403)

    def tearDown(self):
        super().tearDown()
        os.remove(f"documents/shared-files/patients/{self.user_id}/testfile.pdf")


class PatientDigitalIDCardTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.idexx_employer, _ = Employer.objects.get_or_create(name=EmployerName.IDEXX)
        ContractFactory(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": self.idexx_employer.id,
                "allowable_group_ids": ["123123"],
                "is_coverage_program_enrollment_enabled": True,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.AUTO_ATTRIBUTED,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.idexx_employer),
            contracted_entity=self.idexx_employer,
        )
        self.firefly_payer, _ = InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        self.aetna_payer, _ = InsurancePayer.objects.get_or_create(name="Aetna")
        AliasMapping.all_objects.get_or_create(
            object_id=self.idexx_employer.id,
            alias_name="Flume",
            content_type=ContentType.objects.get_for_model(Employer),
            alias_id="FF101",
        )
        InsurancePlan.objects.get_or_create(name=FIREFLY_PLAN)

    def test_get_digital_id_card(self):
        # Call to Digitial ID API by user's without covered member will return 400
        self.person = self.patient.person
        self.person.insurance_info.member_id = "123456789-00"
        self.person_id = self.person.pk
        self.user_id = self.patient.pk
        Accumulators.objects.create(member=self.person, deductible=100, out_of_pocket=5)
        self.person.insurance_info.save()
        response = self.client.get(f"/documents/patients/{self.user_id}/digital-id-card/")
        self.assertEqual(response.status_code, 400)
        self.idexx_employer, _ = Employer.objects.get_or_create(name=EmployerName.IDEXX)
        # Since sonly DOB matches, person will be not be merged on signed up and case will not be created
        call_command(
            "parse_fake_flume_data_from_data_pipeline",
            user=self.provider,
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone="**********",
            member_id="FF12312312-00",
            employer_id=self.idexx_employer.id,
            effective_start_date=datetime.now().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        Accumulators.objects.create(member=existing_person, deductible=100, out_of_pocket=5)
        user = User.objects.create(email="<EMAIL>")
        existing_person.user = user
        existing_person.save()
        client = FireflyTestCase.get_member_client(member=user)
        response = client.get(f"/documents/patients/{user.id}/digital-id-card/")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["person"], existing_person.pk)
        self.assertEqual(data["first_name"], existing_person.first_name)
        self.assertEqual(data["last_name"], existing_person.last_name)
        self.assertEqual(data["member_id"], existing_person.insurance_info.member_id)
        self.assertEqual(data["group_number"], existing_person.insurance_info.group_number)
        self.assertEqual(data["employer"], existing_person.employer.name)
        self.assertEqual(data["deductible"], existing_person.accumulators.deductible)
        self.assertEqual(data["out_of_pocket"], existing_person.accumulators.out_of_pocket)
        self.assertEqual(data["related_member_info"], [])
        # create_dependents
        call_command(
            "parse_fake_flume_data_from_data_pipeline",
            user=self.provider,
            dependents=True,
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone="**********",
            member_id="FF12312312-00",
            employer_id=self.idexx_employer.id,
            effective_start_date=datetime.now().strftime("%Y-%m-%d"),
        )
        primary_subscriber_person = CoveredMember.objects.get(
            covered_member_person=existing_person
        ).primary_subscriber_person
        dependent = (
            CoveredMember.objects.filter(primary_subscriber_person=primary_subscriber_person)
            .exclude(covered_member_person=primary_subscriber_person)
            .first()
            .covered_member_person
        )
        Accumulators.objects.create(member=dependent, deductible=100, out_of_pocket=5)
        response = client.get(f"/documents/patients/{user.id}/digital-id-card/")
        data = response.json()
        self.assertEqual(len(data["related_member_info"]), 1)
        related_member_info = data["related_member_info"]
        self.assertEqual(related_member_info[0]["pk"], dependent.pk)
        self.assertEqual(related_member_info[0]["first_name"], dependent.first_name)
        self.assertEqual(related_member_info[0]["last_name"], dependent.last_name)
        # dependents
        dependent_patient_digital_id_card = PatientDigitalIDCard.objects.get(person=dependent)
        self.assertEqual(len(dependent_patient_digital_id_card.related_member_info), 1)
        related_member_info = dependent_patient_digital_id_card.related_member_info[0]
        self.assertEqual(related_member_info["pk"], existing_person.pk)
        self.assertEqual(related_member_info["first_name"], existing_person.first_name)
        self.assertEqual(related_member_info["last_name"], existing_person.last_name)

    def test_digital_id_card_backfill(self):
        # create a coverage user and delete the digital ID card.
        benefit_person = PersonUserFactory()
        ProgramEnrollment.objects.update_or_create(
            person=benefit_person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (datetime.now(), None),
                "status": BenefitProgramStatus.ENROLLED,
            },
        )
        CoveredMember.objects.create(covered_member_person=benefit_person, primary_subscriber_person=benefit_person)
        pcp_person = PersonUserFactory()
        add_person_to_program(person=pcp_person, program_uid=ProgramCodes.PRIMARY_CARE)
        call_command(
            "backfill_digital_id_card",
            user=self.provider,
            dry_run_off=False,
        )
        self.assertFalse(PatientDigitalIDCard.objects.filter(person=benefit_person).exists())
        self.assertFalse(PatientDigitalIDCard.objects.filter(person=pcp_person).exists())
        call_command(
            "backfill_digital_id_card",
            user=self.provider,
            dry_run_off=True,
        )
        self.assertTrue(PatientDigitalIDCard.objects.filter(person=benefit_person).exists())
        self.assertFalse(PatientDigitalIDCard.objects.filter(person=pcp_person).exists())
