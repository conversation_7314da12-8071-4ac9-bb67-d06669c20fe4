import shortuuid
from django.conf import settings
from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3


def _get_shortuuid():
    return shortuuid.uuid()


class Content(BaseModelV3):
    """A editable piece of text(markdown) shown to users."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=31, db_index=True, unique=True, default=_get_shortuuid)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over Cha<PERSON><PERSON>ield
    title = models.CharField(max_length=255)  # noqa: TID251

    # Markdown formatted
    text = models.TextField(blank=True, default="")

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="created_content",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # TODO: Add image/media
    # TODO: Add type or tags
