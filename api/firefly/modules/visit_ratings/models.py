from django.conf import settings
from django.db import models
from django_deprecate_fields import deprecate_field

from firefly.modules.appointment.models import Appointment
from firefly.modules.facts.mixin import BaseFact
from firefly.modules.firefly_django.models import BaseModelV3


# DEPRECATED: Do not use
class VideoVisitRating(BaseModelV3):
    rater = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(settings.AUTH_USER_MODEL, related_name="rater", on_delete=models.SET_NULL, null=True)  # noqa: TID251
    )
    appointment = deprecate_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(Appointment, related_name="appointment", on_delete=models.SET_NULL, null=True)  # noqa: TID251
    )
    rating = deprecate_field(models.IntegerField(null=True, blank=True))
    patient_couldnt_hear = deprecate_field(models.Bo<PERSON>anField(default=False))
    clinician_couldnt_hear = deprecate_field(models.BooleanField(default=False))
    audio_breaking_up = deprecate_field(models.BooleanField(default=False))
    audio_was_delayed = deprecate_field(models.BooleanField(default=False))
    patient_couldnt_see = deprecate_field(models.BooleanField(default=False))
    clinician_couldnt_see = deprecate_field(models.BooleanField(default=False))
    video_breaking_up = deprecate_field(models.BooleanField(default=False))
    video_was_delayed = deprecate_field(models.BooleanField(default=False))
    patient_trouble_connecting = deprecate_field(models.BooleanField(default=False))
    clinician_trouble_connecting = deprecate_field(models.BooleanField(default=False))
    call_dropped_unexpectedly = deprecate_field(models.BooleanField(default=False))
    comment = deprecate_field(models.TextField(null=True, blank=True))

    class Meta(BaseModelV3.Meta):
        db_table = "video_visit_rating"
        verbose_name_plural = "Video Visit Ratings"


class VisitDurationRating(BaseFact):
    class Meta(BaseFact.Meta):
        verbose_name_plural = "Visit Duration Ratings"


class VisitDurationRatingDetail(BaseModelV3):
    rater = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="rater_details",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
    )
    appointment = models.ForeignKey(
        Appointment,
        related_name="appointment_details",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
    )
    visit_duration = models.ForeignKey(
        VisitDurationRating,
        related_name="visit_duration_rating",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
    )

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "Visit Duration Rating Details"
