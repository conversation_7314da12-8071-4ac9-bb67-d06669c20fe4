import logging
import os
import uuid
from typing import Literal, Union

from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

from firefly.modules.file_transfer.utils import (
    download_file_from_sftp,
    get_checksum,
    upload_file_to_sftp,
)
from firefly.modules.firefly_django.models import BaseModelV3

logger = logging.getLogger(__name__)

Direction = Union[Literal["inbound"], Literal["outbound"]]


def get_connection_folder(instance, filename):
    return os.path.join(
        "file_transfer",
        "connections",
        str(instance.connection_id),
        instance.direction(),
        str(instance.uuid),
        filename,
    )


class AuthenticationMethod(models.TextChoices):
    PASSWORD = "password", _("Password")  # pragma: allowlist secret
    SSH = "ssh", _("SSH")

    def __eq__(self, other: object) -> bool:
        return str(self) == str(other)


class TransferStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    IN_PROGRESS = "in-progress", _("In progress")
    COMPLETE = "complete", _("Complete")
    FAILED = "failed", _("Failed")

    def __eq__(self, other: object) -> bool:
        return str(self) == str(other)


class Connection(BaseModelV3):
    """
    Connection represents information and credentials required to establish an
    SFTP connection.
    """

    title = models.TextField(
        help_text="Human-readable name for the connection. Must be unique.",
        unique=True,
    )
    # In general, these fields should roughly match the arguments needed by
    # paramiko.SSHClient.connect. All secrets should be stored in AWS Secrets
    # Manager, not the database.
    host = models.TextField(
        help_text="The server to connect to",
    )
    port = models.PositiveIntegerField(
        help_text="The server port to connect to",
        validators=[MinValueValidator(1), MaxValueValidator(65535)],
    )
    authentication_method = models.TextField(
        help_text="Determines the credentials that must be provided to log in.",
        choices=AuthenticationMethod.choices,
    )
    username = models.TextField(
        help_text="The username to authenticate as. Required if authentication method is Password.",
        null=True,
        blank=True,
    )
    password_secret_name = models.TextField(
        help_text=(
            "Name of AWS Secrets Manager secret containing password. Required if authentication method is Password."
        ),
        null=True,
        blank=True,
    )
    private_key_secret_name = models.TextField(
        help_text=(
            "Name of AWS Secrets Manager secret containing private key to use for authentication. "
            + "Required if authentication method is SSH."
        ),
        null=True,
        blank=True,
    )
    private_key_passphrase_secret_name = models.TextField(
        help_text=("Name of AWS Secrets Manager secret containing passphrase used for decrypting private " + "keys."),
        null=True,
        blank=True,
    )
    base_path = models.TextField(
        help_text="The base path on the remote connection where files are hosted.",
        null=True,
        blank=True,
    )

    def __str__(self):
        return f"{self.title} ({self.pk})"


class FileTransferMixin(models.Model):
    """
    FileTransferMixin represents fields that are common to all file transfer
    operations.
    """

    # UUID gives us a unique identifier for the file that we can mint before the
    # model instance is created, unlike the integer sequence primary key which
    # only exists after the row is inserted into the database.
    uuid = models.UUIDField(
        unique=True,
        default=uuid.uuid4,
        editable=False,
    )
    connection = models.ForeignKey(
        help_text="The connection over which this file should be transferred.",
        to=Connection,
        on_delete=models.PROTECT,  # Conservative approach until we decide how to handle removed connections.  # noqa
    )
    file = models.FileField(
        help_text="The file to be transferred.",
        upload_to=get_connection_folder,
    )
    name = models.TextField(
        help_text="The name of the file to be transferred.",
        null=True,
        blank=True,
    )
    status = models.TextField(
        choices=TransferStatus.choices,
        default=TransferStatus.INITIATED,
        editable=False,
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    checksum = models.CharField(max_length=36, editable=False, null=True, blank=True)  # noqa: TID251

    def direction(self) -> Direction:
        raise NotImplementedError('"direction" method not defined')

    class Meta:
        abstract = True


class Download(BaseModelV3, FileTransferMixin):
    """
    Download represents an operation to transfer a file from a remote connection
    to Lucian.
    """

    source_path = models.TextField(
        help_text="The path on the remote connection from which the file should be downloaded.",
        null=True,
        blank=True,
    )

    def direction(self) -> Direction:
        return "inbound"

    def save(self, keep_deleted=False, **kwargs):
        super().save(keep_deleted, **kwargs)
        if self.name and not self.file and self.status == TransferStatus.INITIATED:
            download_file_from_sftp(self, self.name)

    class Meta:
        pass


class Upload(BaseModelV3, FileTransferMixin):
    """
    Upload represents an operation to transfer a file from Lucian to a remote
    connection.
    """

    destination_path = models.TextField(
        help_text="The path on the remote connection to which the file should be uploaded.",
        null=True,
        blank=True,
    )
    dry_run = models.BooleanField(
        help_text="when set to true, file will not be uploaded to remote server", default=False, null=True, blank=True
    )

    _verify_size = True

    @property
    def verify_size(self):
        return self._verify_size

    @verify_size.setter
    def verify_size(self, value):
        self._verify_size = value

    def direction(self) -> Direction:
        return "outbound"

    def save(self, keep_deleted=False, **kwargs):
        super().save(keep_deleted, **kwargs)
        if self.status == TransferStatus.INITIATED:
            if self.file:
                if not self.name:
                    self.name = os.path.basename(self.file.name)
                if not self.checksum:
                    checksum = get_checksum(self.file)
                    self.checksum = checksum
            upload_file_to_sftp(self, verify_size=self._verify_size)
            self.save()

    class Meta:
        pass
