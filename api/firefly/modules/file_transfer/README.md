# File Transfer

## Motivation

The File Transfer module provides secure SFTP-based file exchange capabilities for healthcare data integration with external systems. It enables automated file uploads and downloads with comprehensive security controls, audit trails, and error handling while supporting various authentication methods and ensuring data integrity through checksum validation and transfer status tracking.

## Business Context

File transfer capabilities serve critical healthcare integration functions:
- **Healthcare Data Exchange**: Secure file transfer with insurance payers, TPAs, and healthcare partners
- **Claims Processing**: Automated file exchange for insurance claims and eligibility data
- **Regulatory Reporting**: Secure transmission of compliance reports and healthcare data
- **Partner Integration**: File-based data exchange with external healthcare systems
- **Data Pipeline Support**: Automated file ingestion for data processing workflows
- **Audit and Compliance**: Complete transfer audit trails for regulatory requirements

The system ensures secure, reliable file exchange while maintaining strict healthcare data security standards and providing comprehensive monitoring and error handling capabilities.

## Core Concepts

### SFTP Connection Management
- **Secure Authentication**: Support for SSH key-based and password authentication
- **Connection Pooling**: Reusable connection configurations for multiple file operations
- **Credential Security**: AWS Secrets Manager integration for secure credential storage
- **Multi-Protocol Support**: SFTP protocol with various SSH key types (RSA, DSS, ECDSA, Ed25519)

### Bidirectional File Transfer
- **Download Operations**: Automated file retrieval from remote SFTP servers to S3 storage
- **Upload Operations**: Secure file transmission from local storage to remote SFTP servers
- **Batch Processing**: Bulk file operations with recursive directory support
- **Transfer Status Tracking**: Real-time status monitoring for all file operations

### Data Integrity and Security
- **Checksum Validation**: MD5 checksum verification for file integrity
- **Transfer Verification**: Size verification and confirmation for upload operations
- **Secure Storage**: File organization with UUID-based unique identifiers
- **Access Control**: Permission-based access to connections and file operations

### File Organization and Management
- **Structured Storage**: Organized file storage by connection, direction, and unique identifiers
- **File Archiving**: Automated file archiving with timestamp-based naming
- **Remote Directory Navigation**: Recursive directory listing and navigation capabilities
- **File Metadata**: Comprehensive file information including checksums and transfer status

## Technical Implementation

### Core Models

**Connection**: SFTP connection configuration and credentials
- `title`: Human-readable connection name (unique identifier)
- `host`: SFTP server hostname or IP address
- `port`: Server port (1-65535 range validation)
- `username`: Authentication username
- `authentication_method`: Password or SSH key authentication
- `aws_secret_name`: AWS Secrets Manager secret reference for credentials
- **Security**: All sensitive credentials stored in AWS Secrets Manager

**FileTransferMixin**: Common file transfer functionality
- `uuid`: Unique identifier for file operations (generated before database insertion)
- `connection`: Foreign key to Connection model with PROTECT constraint
- `file`: FileField with organized upload path structure
- `name`: Transfer filename (auto-populated from file if not provided)
- `status`: Transfer status tracking (Initiated, In Progress, Complete, Failed)
- `checksum`: MD5 checksum for file integrity verification

**Download**: Inbound file transfer operations
- `source_path`: Remote directory path for file retrieval
- **Auto-Download**: Automatic file download on model save when status is Initiated
- **Direction**: Returns "inbound" for file organization

**Upload**: Outbound file transfer operations
- `destination_path`: Remote directory path for file placement
- `dry_run`: Test mode flag to prevent actual file transfer
- `verify_size`: Size verification control for upload confirmation
- **Auto-Upload**: Automatic file upload on model save with checksum calculation

### SFTP Client Management

**Secure Connection Handling** (`get_sftp_client`):
- **Context Manager**: Automatic connection cleanup and resource management
- **Multi-Key Support**: RSA, DSS, ECDSA, and Ed25519 SSH key types
- **Credential Resolution**: AWS Secrets Manager integration for secure credential access
- **Error Handling**: Comprehensive connection error management and logging

**Authentication Methods**:
- **SSH Key Authentication**: Private key-based authentication with multiple key formats
- **Password Authentication**: Username/password authentication with secure storage
- **Key Format Support**: PEM and OpenSSH private key format handling
- **Auto-Accept Policy**: Automatic host key acceptance for known servers

### File Operations

**Download Operations** (`download_file_from_sftp`):
- **Status Tracking**: Automatic status updates throughout download process
- **Error Handling**: Comprehensive error capture and status reporting
- **File Storage**: Organized storage with connection-based directory structure
- **Integrity Verification**: Checksum calculation and validation

**Upload Operations** (`upload_file_to_sftp`):
- **Size Verification**: Optional file size confirmation after upload
- **Directory Creation**: Automatic remote directory creation if needed
- **Dry Run Support**: Test mode for upload validation without actual transfer
- **Progress Tracking**: Status updates throughout upload process

**Bulk Operations** (`download_files_from_sftp`):
- **Recursive Processing**: Complete directory tree traversal and file discovery
- **Selective Processing**: Denylist support for excluding specific files or patterns
- **Checksum Generation**: Optional checksum calculation for file verification
- **Batch Efficiency**: Optimized processing for large file sets

### Utility Functions

**File Management**:
- `archive_files_in_sftp()`: Automated file archiving with timestamp naming
- `clean_filename()`: Filename sanitization using Django's slugify
- `get_checksum()`: MD5 checksum calculation for file integrity
- `get_remote_contents()`: Remote directory listing and file discovery

**Directory Operations**:
- **Remote Navigation**: Change directory and create missing directories
- **Content Listing**: Recursive directory content discovery
- **File Filtering**: Support for file type and pattern filtering
- **Path Management**: Cross-platform path handling and normalization

## Current Implementation

### Active Features
- Complete SFTP connection management with secure credential storage
- Bidirectional file transfer with automatic status tracking
- Multi-authentication method support (SSH keys and passwords)
- File integrity verification through checksum validation
- Bulk file operations with recursive directory processing
- Comprehensive error handling and audit trail maintenance

### Security Features
- **AWS Secrets Manager**: Secure credential storage and retrieval
- **SSH Key Support**: Multiple SSH key formats with secure key handling
- **Access Control**: Permission-based connection and file access
- **Audit Trail**: Complete transfer history with status and error tracking

### Integration Points
- **AWS S3**: File storage integration for downloaded files
- **AWS Secrets Manager**: Secure credential management
- **Django Admin**: Web-based connection and transfer management
- **Management Commands**: Automated file operations and monitoring

## Management Commands

### File Transfer Operations
```bash
# Download all files from SFTP connections
python manage.py pull_files_from_sftp

# Download files from specific connection
python manage.py pull_files_from_sftp --connection_id 123

# List remote directory contents
python manage.py list_remote_contents --connection_id 123 --directory /data
```

## Admin Commands

### Web-Based Management
- **ListRemoteContents**: Browse remote SFTP directory contents through admin interface
- **Connection Management**: Create and manage SFTP connections with credential configuration
- **Transfer Monitoring**: View and manage file transfer operations with status tracking

### Form Parameters
- `connection_id`: Target SFTP connection for operations
- `directory`: Remote directory path for content listing

## API Integration

### Model-Based Operations
- **Automatic Transfers**: File transfers triggered by model save operations
- **Status Tracking**: Real-time transfer status updates and error reporting
- **Batch Processing**: Efficient handling of multiple file operations
- **Error Recovery**: Comprehensive error handling with detailed logging

### File Organization
```
file_transfer/
├── connections/
│   └── {connection_id}/
│       ├── inbound/
│       │   └── {uuid}/
│       │       └── {filename}
│       └── outbound/
│           └── {uuid}/
│               └── {filename}
```

## Admin Interface

### Connection Management
- **ConnectionAdmin**: SFTP connection configuration with search capabilities
- **Credential Security**: AWS Secrets Manager integration for secure credential storage
- **Connection Testing**: Validation of connection parameters and authentication

### Transfer Management
- **DownloadAdmin**: Download operation management with permission-based filtering
- **UploadAdmin**: Upload operation management with status tracking
- **Permission Control**: Group-based access control for connection visibility

### Security Features
- **Group Permissions**: Role-based access to specific connections
- **Object-Level Permissions**: Fine-grained access control for file operations
- **Audit Trail**: Complete transfer history with user attribution

## Configuration

### Authentication Methods
- **SSH**: SSH key-based authentication with multiple key format support
- **Password**: Username/password authentication with secure storage

### Transfer Status Options
- **Initiated**: Transfer operation created but not started
- **In Progress**: Transfer operation currently executing
- **Complete**: Transfer operation completed successfully
- **Failed**: Transfer operation failed with error details

## Testing

- **Connection Testing**: SFTP connection validation and authentication testing
- **Transfer Testing**: Upload and download operation validation
- **Security Testing**: Credential handling and access control verification
- **Error Handling**: Transfer failure scenarios and recovery testing
- **Permission Testing**: Group-based access control validation

## Error Handling

- **Connection Errors**: Comprehensive SFTP connection error handling
- **Transfer Errors**: File transfer failure detection and reporting
- **Authentication Errors**: Credential validation and error reporting
- **Network Errors**: Network connectivity and timeout handling
- **File System Errors**: Remote and local file system error management
