# Generated by Django 4.2.13 on 2024-08-19 05:12

import uuid

import django.core.validators
import django.db.models.deletion
import pgtrigger.compiler
import pgtrigger.migrations
from django.conf import settings
from django.db import migrations, models

import firefly.modules.file_transfer.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("pghistory", "0003_auto_20201023_1636"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Connection",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "title",
                    models.TextField(help_text="Human-readable name for the connection. Must be unique.", unique=True),
                ),
                ("host", models.TextField(help_text="The server to connect to")),
                (
                    "port",
                    models.PositiveIntegerField(
                        help_text="The server port to connect to",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(65535),
                        ],
                    ),
                ),
                (
                    "authentication_method",
                    models.TextField(
                        choices=[("password", "Password"), ("ssh", "SSH")],
                        help_text="Determines the credentials that must be provided to log in.",
                    ),
                ),
                (
                    "username",
                    models.TextField(
                        blank=True,
                        help_text="The username to authenticate as. Required if authentication method is Password.",
                        null=True,
                    ),
                ),
                (
                    "password_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing password. "
                            "Required if authentication method is Password."
                        ),
                        null=True,
                    ),
                ),
                (
                    "private_key_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing private key to use for authentication. "
                            "Required if authentication method is SSH."
                        ),
                        null=True,
                    ),
                ),
                (
                    "private_key_passphrase_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing passphrase used for decrypting private keys."
                        ),
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "base_path",
                    models.TextField(
                        blank=True,
                        help_text="The base path on the remote connection where files are hosted.",
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": None,
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Download",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "file",
                    models.FileField(
                        help_text="The file to be transferred.",
                        upload_to=firefly.modules.file_transfer.models.get_connection_folder,
                    ),
                ),
                (
                    "source_path",
                    models.TextField(
                        blank=True,
                        help_text="The path on the remote connection from which the file should be downloaded.",
                        null=True,
                    ),
                ),
                (
                    "connection",
                    models.ForeignKey(
                        help_text="The connection over which this file should be transferred.",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="file_transfer.connection",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("checksum", models.CharField(blank=True, editable=False, max_length=36, null=True)),
                ("name", models.TextField(blank=True, help_text="The name of the file to be transferred.", null=True)),
                (
                    "status",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("initiated", "Initiated"),
                            ("in-progress", "In progress"),
                            ("complete", "Complete"),
                            ("failed", "Failed"),
                        ],
                        default="initiated",
                        editable=False,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": None,
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Upload",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "file",
                    models.FileField(
                        help_text="The file to be transferred.",
                        upload_to=firefly.modules.file_transfer.models.get_connection_folder,
                    ),
                ),
                (
                    "destination_path",
                    models.TextField(
                        blank=True,
                        help_text="The path on the remote connection to which the file should be uploaded.",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("initiated", "Initiated"),
                            ("in-progress", "In progress"),
                            ("complete", "Complete"),
                            ("failed", "Failed"),
                        ],
                        default="initiated",
                        editable=False,
                        null=True,
                    ),
                ),
                (
                    "connection",
                    models.ForeignKey(
                        help_text="The connection over which this file should be transferred.",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="file_transfer.connection",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("checksum", models.CharField(blank=True, editable=False, max_length=36, null=True)),
                ("name", models.TextField(blank=True, help_text="The name of the file to be transferred.", null=True)),
                (
                    "dry_run",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        help_text="when set to true, file will not be uploaded to remote server",
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": None,
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ConnectionEvent",
            fields=[
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("pgh_id", models.AutoField(primary_key=True, serialize=False)),
                ("pgh_created_at", models.DateTimeField(auto_now_add=True)),
                ("pgh_label", models.TextField(help_text="The event label.")),
                ("title", models.TextField(help_text="Human-readable name for the connection. Must be unique.")),
                ("host", models.TextField(help_text="The server to connect to")),
                (
                    "port",
                    models.PositiveIntegerField(
                        help_text="The server port to connect to",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(65535),
                        ],
                    ),
                ),
                (
                    "authentication_method",
                    models.TextField(
                        choices=[("password", "Password"), ("ssh", "SSH")],
                        help_text="Determines the credentials that must be provided to log in.",
                    ),
                ),
                (
                    "username",
                    models.TextField(
                        blank=True,
                        help_text="The username to authenticate as. Required if authentication method is Password.",
                        null=True,
                    ),
                ),
                (
                    "password_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing password. "
                            "Required if authentication method is Password."
                        ),
                        null=True,
                    ),
                ),
                (
                    "private_key_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing private key to use for authentication. "
                            "Required if authentication method is SSH."
                        ),
                        null=True,
                    ),
                ),
                (
                    "private_key_passphrase_secret_name",
                    models.TextField(
                        blank=True,
                        help_text=(
                            "Name of AWS Secrets Manager secret containing passphrase used for decrypting private keys."
                        ),
                        null=True,
                    ),
                ),
                ("id", models.IntegerField()),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "pgh_context",
                    models.ForeignKey(
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="pghistory.context",
                    ),
                ),
                (
                    "pgh_obj",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="pgh_events",
                        to="file_transfer.connection",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "base_path",
                    models.TextField(
                        blank=True,
                        help_text="The base path on the remote connection where files are hosted.",
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DownloadEvent",
            fields=[
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("pgh_id", models.AutoField(primary_key=True, serialize=False)),
                ("pgh_created_at", models.DateTimeField(auto_now_add=True)),
                ("pgh_label", models.TextField(help_text="The event label.")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "file",
                    models.FileField(
                        help_text="The file to be transferred.",
                        upload_to=firefly.modules.file_transfer.models.get_connection_folder,
                    ),
                ),
                (
                    "source_path",
                    models.TextField(
                        blank=True,
                        help_text="The path on the remote connection from which the file should be downloaded.",
                        null=True,
                    ),
                ),
                ("id", models.IntegerField()),
                (
                    "connection",
                    models.ForeignKey(
                        db_constraint=False,
                        help_text="The connection over which this file should be transferred.",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to="file_transfer.connection",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "pgh_context",
                    models.ForeignKey(
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="pghistory.context",
                    ),
                ),
                (
                    "pgh_obj",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="pgh_events",
                        to="file_transfer.download",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("checksum", models.CharField(blank=True, editable=False, max_length=36, null=True)),
                ("name", models.TextField(blank=True, help_text="The name of the file to be transferred.", null=True)),
                (
                    "status",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("initiated", "Initiated"),
                            ("in-progress", "In progress"),
                            ("complete", "Complete"),
                            ("failed", "Failed"),
                        ],
                        default="initiated",
                        editable=False,
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="UploadEvent",
            fields=[
                ("deleted", models.DateTimeField(editable=False, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("pgh_id", models.AutoField(primary_key=True, serialize=False)),
                ("pgh_created_at", models.DateTimeField(auto_now_add=True)),
                ("pgh_label", models.TextField(help_text="The event label.")),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "file",
                    models.FileField(
                        help_text="The file to be transferred.",
                        upload_to=firefly.modules.file_transfer.models.get_connection_folder,
                    ),
                ),
                (
                    "destination_path",
                    models.TextField(
                        blank=True,
                        help_text="The path on the remote connection to which the file should be uploaded.",
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("initiated", "Initiated"),
                            ("in-progress", "In progress"),
                            ("complete", "Complete"),
                            ("failed", "Failed"),
                        ],
                        default="initiated",
                        editable=False,
                        null=True,
                    ),
                ),
                ("id", models.IntegerField()),
                (
                    "connection",
                    models.ForeignKey(
                        db_constraint=False,
                        help_text="The connection over which this file should be transferred.",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to="file_transfer.connection",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "pgh_context",
                    models.ForeignKey(
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="pghistory.context",
                    ),
                ),
                (
                    "pgh_obj",
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="pgh_events",
                        to="file_transfer.upload",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        related_query_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("checksum", models.CharField(blank=True, editable=False, max_length=36, null=True)),
                ("name", models.TextField(blank=True, help_text="The name of the file to be transferred.", null=True)),
                (
                    "dry_run",
                    models.BooleanField(
                        blank=True,
                        default=False,
                        help_text="when set to true, file will not be uploaded to remote server",
                        null=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="connection",
            trigger=pgtrigger.compiler.Trigger(
                name="aud_connection",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func="\n        IF (nullif(CURRENT_SETTING('pghistory.context_metadata'), '') IS NULL) THEN\n            RAISE EXCEPTION 'context is required for any changes to the model';\n        END IF;\n        IF (CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user' IS NULL) THEN\n            RAISE EXCEPTION 'user key in context is required for any changes to the model';\n        END IF;\n    \n            IF (TG_OP='INSERT') THEN\n                NEW.created_by_id = coalesce(\n                    nullif(CURRENT_SETTING('pghistory.context_metadata', true), ''),\n                    '{}'\n                )::json->>'user';\n                NEW.created_at = now();\n            ELSE\n                IF OLD.created_by_id IS NOT NULL THEN\n                    NEW.created_by_id = OLD.created_by_id;\n                END IF;\n                IF OLD.created_at IS NOT NULL THEN\n                    NEW.created_at = OLD.created_at;\n                END IF;\n            END IF;\n            NEW.updated_by_id = CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user';\n            NEW.updated_at = now();\n        RETURN NEW;",  # noqa: E501
                    hash="27b65f2cfc7c2a8a70e9a12f89b8cb93d236170c",  # pragma: allowlist secret
                    operation="UPDATE OR INSERT",
                    pgid="pgtrigger_aud_connection_21cbb",
                    table="file_transfer_connection",
                    when="BEFORE",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="connection",
            trigger=pgtrigger.compiler.Trigger(
                name="insert_insert",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func='INSERT INTO "file_transfer_connectionevent" ("authentication_method", "base_path", "created_at", "created_by_id", "deleted", "host", "id", "password_secret_name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "port", "private_key_passphrase_secret_name", "private_key_secret_name", "title", "updated_at", "updated_by_id", "username") VALUES (NEW."authentication_method", NEW."base_path", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."host", NEW."id", NEW."password_secret_name", _pgh_attach_context(), NOW(), \'insert\', NEW."id", NEW."port", NEW."private_key_passphrase_secret_name", NEW."private_key_secret_name", NEW."title", NEW."updated_at", NEW."updated_by_id", NEW."username"); RETURN NULL;',  # noqa: E501
                    hash="46becdb90b20e22e6847a18291bfdce240d09539",  # pragma: allowlist secret
                    operation="INSERT",
                    pgid="pgtrigger_insert_insert_19faa",
                    table="file_transfer_connection",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="connection",
            trigger=pgtrigger.compiler.Trigger(
                name="update_update",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    condition="WHEN (OLD.* IS DISTINCT FROM NEW.*)",
                    func='INSERT INTO "file_transfer_connectionevent" ("authentication_method", "base_path", "created_at", "created_by_id", "deleted", "host", "id", "password_secret_name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "port", "private_key_passphrase_secret_name", "private_key_secret_name", "title", "updated_at", "updated_by_id", "username") VALUES (NEW."authentication_method", NEW."base_path", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."host", NEW."id", NEW."password_secret_name", _pgh_attach_context(), NOW(), \'update\', NEW."id", NEW."port", NEW."private_key_passphrase_secret_name", NEW."private_key_secret_name", NEW."title", NEW."updated_at", NEW."updated_by_id", NEW."username"); RETURN NULL;',  # noqa: E501
                    hash="c9ebb09b80bc72110b1c02558b8efd47bf800e9e",  # pragma: allowlist secret
                    operation="UPDATE",
                    pgid="pgtrigger_update_update_3fdb7",
                    table="file_transfer_connection",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="download",
            trigger=pgtrigger.compiler.Trigger(
                name="aud_download",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func="\n        IF (nullif(CURRENT_SETTING('pghistory.context_metadata'), '') IS NULL) THEN\n            RAISE EXCEPTION 'context is required for any changes to the model';\n        END IF;\n        IF (CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user' IS NULL) THEN\n            RAISE EXCEPTION 'user key in context is required for any changes to the model';\n        END IF;\n    \n            IF (TG_OP='INSERT') THEN\n                NEW.created_by_id = coalesce(\n                    nullif(CURRENT_SETTING('pghistory.context_metadata', true), ''),\n                    '{}'\n                )::json->>'user';\n                NEW.created_at = now();\n            ELSE\n                IF OLD.created_by_id IS NOT NULL THEN\n                    NEW.created_by_id = OLD.created_by_id;\n                END IF;\n                IF OLD.created_at IS NOT NULL THEN\n                    NEW.created_at = OLD.created_at;\n                END IF;\n            END IF;\n            NEW.updated_by_id = CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user';\n            NEW.updated_at = now();\n        RETURN NEW;",  # noqa: E501
                    hash="dc5ffb31ec756de601588634c7c8f0dd04735027",  # pragma: allowlist secret
                    operation="UPDATE OR INSERT",
                    pgid="pgtrigger_aud_download_c58f8",
                    table="file_transfer_download",
                    when="BEFORE",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="download",
            trigger=pgtrigger.compiler.Trigger(
                name="insert_insert",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func='INSERT INTO "file_transfer_downloadevent" ("checksum", "connection_id", "created_at", "created_by_id", "deleted", "file", "id", "name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "source_path", "status", "updated_at", "updated_by_id", "uuid") VALUES (NEW."checksum", NEW."connection_id", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."file", NEW."id", NEW."name", _pgh_attach_context(), NOW(), \'insert\', NEW."id", NEW."source_path", NEW."status", NEW."updated_at", NEW."updated_by_id", NEW."uuid"); RETURN NULL;',  # noqa: E501
                    hash="4a937146a44401820f4e0755c58c659982859291",  # pragma: allowlist secret
                    operation="INSERT",
                    pgid="pgtrigger_insert_insert_eaa93",
                    table="file_transfer_download",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="download",
            trigger=pgtrigger.compiler.Trigger(
                name="update_update",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    condition="WHEN (OLD.* IS DISTINCT FROM NEW.*)",
                    func='INSERT INTO "file_transfer_downloadevent" ("checksum", "connection_id", "created_at", "created_by_id", "deleted", "file", "id", "name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "source_path", "status", "updated_at", "updated_by_id", "uuid") VALUES (NEW."checksum", NEW."connection_id", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."file", NEW."id", NEW."name", _pgh_attach_context(), NOW(), \'update\', NEW."id", NEW."source_path", NEW."status", NEW."updated_at", NEW."updated_by_id", NEW."uuid"); RETURN NULL;',  # noqa: E501
                    hash="495f5c4823c1a22effd431bb00cf15e9baabc2fe",  # pragma: allowlist secret
                    operation="UPDATE",
                    pgid="pgtrigger_update_update_74913",
                    table="file_transfer_download",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="upload",
            trigger=pgtrigger.compiler.Trigger(
                name="aud_upload",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func="\n        IF (nullif(CURRENT_SETTING('pghistory.context_metadata'), '') IS NULL) THEN\n            RAISE EXCEPTION 'context is required for any changes to the model';\n        END IF;\n        IF (CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user' IS NULL) THEN\n            RAISE EXCEPTION 'user key in context is required for any changes to the model';\n        END IF;\n    \n            IF (TG_OP='INSERT') THEN\n                NEW.created_by_id = coalesce(\n                    nullif(CURRENT_SETTING('pghistory.context_metadata', true), ''),\n                    '{}'\n                )::json->>'user';\n                NEW.created_at = now();\n            ELSE\n                IF OLD.created_by_id IS NOT NULL THEN\n                    NEW.created_by_id = OLD.created_by_id;\n                END IF;\n                IF OLD.created_at IS NOT NULL THEN\n                    NEW.created_at = OLD.created_at;\n                END IF;\n            END IF;\n            NEW.updated_by_id = CURRENT_SETTING('pghistory.context_metadata', true)::json->>'user';\n            NEW.updated_at = now();\n        RETURN NEW;",  # noqa: E501
                    hash="36b85753b6c6821a5a167af591df9c2ce4a1fce6",  # pragma: allowlist secret
                    operation="UPDATE OR INSERT",
                    pgid="pgtrigger_aud_upload_e3263",
                    table="file_transfer_upload",
                    when="BEFORE",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="upload",
            trigger=pgtrigger.compiler.Trigger(
                name="insert_insert",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    func='INSERT INTO "file_transfer_uploadevent" ("checksum", "connection_id", "created_at", "created_by_id", "deleted", "destination_path", "dry_run", "file", "id", "name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "status", "updated_at", "updated_by_id", "uuid") VALUES (NEW."checksum", NEW."connection_id", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."destination_path", NEW."dry_run", NEW."file", NEW."id", NEW."name", _pgh_attach_context(), NOW(), \'insert\', NEW."id", NEW."status", NEW."updated_at", NEW."updated_by_id", NEW."uuid"); RETURN NULL;',  # noqa: E501
                    hash="ad6e3810f369cfccc49992d40c6a28f76bfa5534",  # pragma: allowlist secret
                    operation="INSERT",
                    pgid="pgtrigger_insert_insert_5b5af",
                    table="file_transfer_upload",
                    when="AFTER",
                ),
            ),
        ),
        pgtrigger.migrations.AddTrigger(
            model_name="upload",
            trigger=pgtrigger.compiler.Trigger(
                name="update_update",
                sql=pgtrigger.compiler.UpsertTriggerSql(
                    condition="WHEN (OLD.* IS DISTINCT FROM NEW.*)",
                    func='INSERT INTO "file_transfer_uploadevent" ("checksum", "connection_id", "created_at", "created_by_id", "deleted", "destination_path", "dry_run", "file", "id", "name", "pgh_context_id", "pgh_created_at", "pgh_label", "pgh_obj_id", "status", "updated_at", "updated_by_id", "uuid") VALUES (NEW."checksum", NEW."connection_id", NEW."created_at", NEW."created_by_id", NEW."deleted", NEW."destination_path", NEW."dry_run", NEW."file", NEW."id", NEW."name", _pgh_attach_context(), NOW(), \'update\', NEW."id", NEW."status", NEW."updated_at", NEW."updated_by_id", NEW."uuid"); RETURN NULL;',  # noqa: E501
                    hash="36507fc44522642057ef9f3467b5bbd2b92961d4",  # pragma: allowlist secret
                    operation="UPDATE",
                    pgid="pgtrigger_update_update_639d0",
                    table="file_transfer_upload",
                    when="AFTER",
                ),
            ),
        ),
    ]
