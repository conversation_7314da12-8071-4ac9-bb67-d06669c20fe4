# DevOps

## Motivation

The DevOps module provides comprehensive infrastructure automation and security management capabilities for Firefly's healthcare platform. It automates staff onboarding and offboarding processes, manages access control across multiple systems, monitors security vulnerabilities, and ensures compliance with healthcare security standards through automated workflows and audit capabilities.

## Business Context

DevOps automation serves critical operational and security functions:
- **Staff Lifecycle Management**: Automated onboarding and offboarding processes for consistent access control
- **Security Compliance**: Automated vulnerability management and security monitoring
- **Access Control Governance**: Role-based access management across 20+ enterprise systems
- **Audit and Compliance**: Comprehensive access auditing for healthcare regulatory requirements
- **Operational Efficiency**: Reduced manual processes and human error in critical security operations
- **Risk Management**: Proactive vulnerability detection and remediation workflows

The system ensures secure, compliant operations while reducing administrative overhead and maintaining strict security standards required for healthcare environments.

## Core Concepts

### Role-Based Access Control (RBAC)
- **Predefined Roles**: 15+ organizational roles with specific permission sets
- **System Integration**: Access management across AWS, Google, Slack, Jira, Elation, and more
- **Permission Inheritance**: Hierarchical permission structures with role-based defaults
- **Automated Provisioning**: Jira-driven access request and approval workflows

### Staff Lifecycle Automation
- **Onboarding Automation**: Automatic access provisioning based on role assignments
- **Offboarding Automation**: Systematic access removal with audit trails
- **Jira Integration**: Service desk workflows for access request management
- **Compliance Tracking**: Complete audit trails for access changes

### Security and Vulnerability Management
- **Snyk Integration**: Automated vulnerability scanning and ticket creation
- **Jira Workflow**: Security issue tracking and resolution management
- **SLA Management**: Risk-based response time requirements
- **Compliance Monitoring**: Continuous security posture assessment

### Access Auditing and Compliance
- **Multi-System Auditing**: Automated access verification across all integrated systems
- **Compliance Reporting**: Regular access reviews and audit reports
- **Anomaly Detection**: Identification of unauthorized or excessive access
- **Manual Audit Support**: Structured processes for systems requiring manual review

## Technical Implementation

### Access Control Framework

**System Definitions**: Comprehensive system integration mapping
- **20+ Enterprise Systems**: AWS, Google Workspace, Slack, Jira, GitHub, Elation, Snowflake, etc.
- **Permission Mapping**: System-specific permission levels and access types
- **Jira Field Integration**: Custom fields for access request automation
- **Component Mapping**: Jira component assignment for request routing

**Role Definitions**: Organizational role-based permission sets
- **Technical Roles**: Engineer, Designer, Analytics Engineer, IT
- **Clinical Roles**: Nurse Practitioner, Physician, Health Guide, Behavioral Health
- **Operations Roles**: Operations Associate/Manager, Network Manager
- **Business Roles**: Product Manager, Marketing, Sales, Legal, SLT

### Staff Lifecycle Management

**Onboarding Process** (`process_new_staff`):
- **Jira Query**: Identifies new staff requests requiring access provisioning
- **Role Resolution**: Maps Jira role fields to internal permission sets
- **Access Request Creation**: Generates system-specific access requests in Jira
- **Workflow Automation**: Links access requests to staff onboarding tickets
- **Production-Only**: Safety controls prevent execution in non-production environments

**Offboarding Process** (`offboard_staff`):
- **Departure Detection**: Identifies staff with upcoming departure dates
- **Access Inventory**: Catalogs all current access across integrated systems
- **Removal Requests**: Creates systematic access removal tickets
- **Audit Trail**: Maintains complete record of access removal activities

### Vulnerability Management

**Snyk Integration** (`create_tickets_for_open_vulnerabilities`):
- **Project Scanning**: Automated vulnerability scanning across code repositories
- **Jira Ticket Creation**: Automatic security issue ticket generation
- **SLA Enforcement**: Risk-based response time requirements (Critical: 30 days, High: 30 days, Medium: 60 days, Low: 90 days)
- **Ticket Lifecycle**: Automatic reopening of resolved tickets for recurring vulnerabilities
- **Project Filtering**: Excludes infrastructure and container projects from automated ticketing

### Access Auditing System

**Multi-System Auditing** (`audit_access_requests`):
- **API Integration**: Direct system API access for user enumeration where possible
- **Manual Audit Support**: Structured processes for systems without API access
- **Compliance Verification**: Validates access against approved Jira requests
- **Anomaly Reporting**: Identifies unauthorized or excessive access permissions
- **Audit Trail**: Complete access history and approval documentation

### Database Monitoring Integration

**Datadog Database Monitoring** (`datadog_database_monitoring_ddl`):
- **User Provisioning**: Creates Datadog monitoring user with appropriate permissions
- **Query Optimization**: Custom explain plan function for multi-query statement handling
- **pghistory Integration**: Handles context-setting queries for audit trail compatibility
- **Performance Monitoring**: Enables comprehensive database performance tracking

## Current Implementation

### Active Features
- Comprehensive role-based access control across 20+ enterprise systems
- Automated staff onboarding and offboarding with Jira integration
- Continuous vulnerability management with Snyk and Jira workflows
- Multi-system access auditing with API and manual audit support
- Database monitoring integration with Datadog
- Production safety controls and audit trail maintenance

### System Integrations
- **Identity Management**: Auth0, JumpCloud, Google Workspace
- **Development Tools**: GitHub, AWS, Datadog, Snowflake, Looker
- **Clinical Systems**: Elation, TalkDesk, Practice Suite
- **Business Tools**: Slack, Jira, Box, 1Password, Fivetran
- **Monitoring**: Snyk, Datadog, OpsGenie

### Security Features
- **Production-Only Execution**: Critical operations restricted to production environment
- **Audit Trail Maintenance**: Complete access change documentation
- **Role-Based Permissions**: Granular access control based on organizational roles
- **Vulnerability SLA Management**: Risk-based response time enforcement

## Management Commands

### Staff Lifecycle Management
```bash
# Process new staff onboarding (production only)
python manage.py process_new_staff

# Process staff offboarding (production only)
python manage.py offboard_staff
```

### Security and Compliance
```bash
# Audit access requests across all systems
python manage.py audit_access_requests

# Create Jira tickets for open vulnerabilities
python manage.py create_tickets_for_open_vulnerabilities

# Setup Datadog database monitoring
python manage.py datadog_database_monitoring_ddl
```

## Admin Commands

### Web-Based Management
- **AuditAccessRequests**: Trigger access auditing through admin interface
- **CreateTicketsForOpenVulnerabilities**: Manual vulnerability ticket creation
- **DatadogDatabaseMonitoringDdl**: Database monitoring setup
- **OffboardStaff**: Manual staff offboarding trigger
- **ProcessNewStaff**: Manual staff onboarding trigger

## Role Definitions

### Technical Roles
- **Engineer**: Full development access (AWS, GitHub, Datadog, Elation)
- **Designer**: Design-focused access (GitHub Design, standard tools)
- **Analytics Engineer**: Data platform access (Snowflake, Looker, Fivetran)
- **IT**: Administrative access across core systems

### Clinical Roles
- **Nurse Practitioner**: Clinical system access with prescribing privileges
- **Physician**: Full clinical access and supervision capabilities
- **Health Guide**: Patient engagement and care coordination tools
- **Behavioral Health**: Mental health-specific clinical access

### Operations and Business Roles
- **Operations Associate/Manager**: Insurance portals and customer service tools
- **Product Manager**: Standard business tools access
- **Marketing/Sales**: Communication and business development tools
- **Legal/SLT**: Executive and legal access requirements

## Security and Compliance

### Vulnerability Management SLAs
- **Critical/High Severity**: 30-day resolution requirement
- **Medium Severity**: 60-day resolution requirement
- **Low Severity**: 90-day resolution requirement
- **Risk Stratification**: 1-week initial assessment requirement

### Access Control Governance
- **Jira-Driven Workflows**: All access changes tracked through service desk
- **Role-Based Provisioning**: Consistent access based on organizational roles
- **Regular Auditing**: Automated and manual access verification processes
- **Compliance Documentation**: Complete audit trails for regulatory requirements

## Testing

- **Access Control Testing**: Validation of role-based permission assignments
- **Integration Testing**: Verification of Jira and external system integrations
- **Security Testing**: Vulnerability management workflow validation
- **Audit Testing**: Access auditing process verification and compliance validation
