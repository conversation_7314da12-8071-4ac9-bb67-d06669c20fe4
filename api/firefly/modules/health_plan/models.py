import json
import os

from django.db import models
from django.db.models.constraints import UniqueConstraint
from jsonschema import validate

from firefly.modules.facts.models import ServiceCategory
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.insurance.models import Contract

_dir_path = os.path.dirname(os.path.realpath(__file__))
PHARMACY_COPY_TEXT_SCHEMA = json.load(open(_dir_path + "/pharmacy_copy_text_schema.json", "r"))


def get_documents_folder(instance, filename):
    return os.path.join("contracts", str(instance.health_plan_configuration.contract.pk), "documents", filename)


def get_logos_folder(instance, filename):
    return os.path.join("contracts", str(instance.health_plan_configuration.contract.pk), "logos", filename)


class HealthPlanConfiguration(BaseModelV3):
    def save(self, *args, **kwargs):
        validate(self.pharmacy_copy_text, PHARMACY_COPY_TEXT_SCHEMA)
        super().save(*args, **kwargs)

    contract = models.ForeignKey(Contract, on_delete=models.CASCADE)
    pbm_name = models.TextField(null=True, blank=True)
    pbm_url = models.URLField(null=True, blank=True)
    # ID card member responsibilities
    deductible_firefly_inn = models.TextField(null=True, blank=True)
    deductible_oon = models.TextField(null=True, blank=True)
    deductible_self_inn = models.TextField(null=True, blank=True)
    doctor_visit_firefly_inn = models.TextField(null=True, blank=True)
    doctor_visit_oon = models.TextField(null=True, blank=True)
    doctor_visit_self_inn = models.TextField(null=True, blank=True)
    er_visit_firefly_inn = models.TextField(null=True, blank=True)
    er_visit_oon = models.TextField(null=True, blank=True)
    er_visit_self_inn = models.TextField(null=True, blank=True)
    oop_max_firefly_inn = models.TextField(null=True, blank=True)
    oop_max_oon = models.TextField(null=True, blank=True)
    oop_max_self_inn = models.TextField(null=True, blank=True)
    specialist_visit_firefly_inn = models.TextField(null=True, blank=True)
    specialist_visit_oon = models.TextField(null=True, blank=True)
    specialist_visit_self_inn = models.TextField(null=True, blank=True)
    # ID card pharmacy benefits info
    rx_bin = models.TextField(null=True, blank=True)
    rx_group = models.TextField(null=True, blank=True)
    rx_pcn = models.TextField(null=True, blank=True)
    # Wavier
    waiver_disclaimer = models.BooleanField(default=False, null=True, blank=True)
    pharmacy_copy_text = models.JSONField(default=list, null=True, blank=True)
    pharmacy_disclaimer = models.TextField(
        null=True, blank=True, help_text="Text to be shown in the pharmacy section of the Id card"
    )
    network_id = models.TextField(null=True, blank=True, help_text="Text/number to be shown as Cigna # in the Id card")

    def __str__(self):
        return self.contract.name


class HealthPlanServiceCategory(BaseModelV3):
    health_plan = models.ForeignKey(
        HealthPlanConfiguration, on_delete=models.CASCADE, related_name="health_plan_service_categories"
    )
    service_category = models.ForeignKey(
        ServiceCategory, on_delete=models.CASCADE, related_name="health_plan_service_categories"
    )

    class Meta(BaseModelV3.Meta):
        constraints = [UniqueConstraint(fields=["health_plan", "service_category"], name="unique_mapping")]

    def __str__(self):
        return f"{self.health_plan} - {self.health_plan.contract.name} - {self.service_category}"


class HealthPlanServiceCategoryBenefitInfo(BaseModelV3):
    health_plan_service_category = models.ForeignKey(
        HealthPlanServiceCategory, on_delete=models.CASCADE, related_name="health_plan_service_category_benefit_info"
    )
    plan_year = models.TextField()
    # Waiver coinsurance is a percentage value that can go up to 100%
    waiver_coinsurance = models.DecimalField(max_digits=5, decimal_places=2)
    # Waiver copay is a currency value
    waiver_copay = models.DecimalField(max_digits=6, decimal_places=2)
    # In network coinsurance is a percentage value that can go up to 100%
    inn_coinsurance = models.DecimalField(max_digits=5, decimal_places=2)
    # In network copay is a currency value
    inn_copay = models.DecimalField(max_digits=6, decimal_places=2)
    # Out of network coinsurance is a percentage value that can go up to 100%
    oon_coinsurance = models.DecimalField(max_digits=5, decimal_places=2)
    # Out of network copay is a currency value
    oon_copay = models.DecimalField(max_digits=6, decimal_places=2)

    class Meta(BaseModelV3.Meta):
        constraints = [
            UniqueConstraint(fields=["health_plan_service_category", "plan_year"], name="plan_year_unique_mapping")
        ]


class HealthPlanDocument(BaseModelV3):
    name = models.TextField()
    file = models.FileField(null=True, blank=True, upload_to=get_documents_folder)
    url = models.URLField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    health_plan_configuration = models.ForeignKey(
        HealthPlanConfiguration,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="documents",
    )


class PointSolution(BaseModelV3):
    name = models.TextField()
    logo = models.ImageField(null=True, blank=True, upload_to=get_logos_folder)
    url = models.URLField(null=True, blank=True)
    summary = models.TextField(null=True, blank=True)
    health_plan_configuration = models.ForeignKey(
        HealthPlanConfiguration,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="point_solutions",
    )
