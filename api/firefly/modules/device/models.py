import logging

from django.conf import settings
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from firefly.core.user.models.models import User
from firefly.modules.firefly_django.models import BaseModelV3

from .constants import COLOR_SCHEMES, OTA_PROVIDERS

logger = logging.getLogger(__name__)


class UserDevice(BaseModelV3):
    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=False, blank=False, related_name="userdevices")
    # DO NOT COPY-PASTE: Prefer TextField over Char<PERSON>ield
    device_unique_id = models.CharField(db_index=True, max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    device_type = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    platform = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251

    permissions = JSONField(default=dict)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    biometric_capability = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    font_scaling = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    color_scheme = models.CharField(max_length=8, null=True, choices=COLOR_SCHEMES)  # noqa: TID251

    # Encrypted Refresh Token incase of biometric enrolled
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    refresh_token = models.CharField(max_length=4096, null=True, blank=True)  # noqa: TID251

    # Timezone that the device was last used in
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    device_timezone = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    os_version = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    app_version = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    ota_provider = models.TextField(null=True, blank=True, choices=OTA_PROVIDERS)
    ota_release_version = models.TextField(null=True, blank=True)

    # Firebase token used by the device
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    device_token = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    device_push_enabled = models.BooleanField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    apns_token = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    @property
    def enrolled_biometric_auth(self):
        if (
            hasattr(self, "biometric_auth")
            and hasattr(self.biometric_auth, "deleted")
            and self.biometric_auth.deleted is None
        ):
            return True
        return False

    @property
    def enrolled_pin_auth(self):
        if hasattr(self, "pin_auth") and hasattr(self.pin_auth, "deleted") and self.pin_auth.deleted is None:
            return True
        return False

    class Meta(BaseModelV3.Meta):
        db_table = "device_userdevice"
        verbose_name_plural = "user devices"
        unique_together = ("user", "device_unique_id")


class BiometricAuth(BaseModelV3):
    id = models.AutoField(primary_key=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    public_key = models.CharField(max_length=4096, null=False, blank=False)  # noqa: TID251
    user_device = models.OneToOneField(
        UserDevice, related_name="biometric_auth", blank=False, null=False, on_delete=models.CASCADE
    )

    class Meta(BaseModelV3.Meta):
        db_table = "device_biometricauth"
        verbose_name_plural = "biometric auths"


class PinAuth(BaseModelV3):
    id = models.AutoField(primary_key=True)
    user_device = models.OneToOneField(
        UserDevice, related_name="pin_auth", blank=False, null=False, on_delete=models.CASCADE
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    encrypted_pin = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "device_pinauth"
        verbose_name_plural = "user_mobile_device_pin_auths"
