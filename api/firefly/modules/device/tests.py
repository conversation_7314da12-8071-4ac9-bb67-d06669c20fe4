import logging
from copy import deepcopy

from faker import Faker

from firefly.core.tests.test_case import FireflyTestCase

logger = logging.getLogger(__name__)

device_unique_id = "0BF347AA-C7C8-42E9-ACC4-FCB5F20ECF21"

mandatory_device_attr = {
    "device_unique_id": device_unique_id,
    "device_type": "iPhone 6",
    "platform": "iOS",
}

optional_device_attr_with_expo_ota_fields = {
    "permissions": {"alerts": "off", "notifications": "on"},
    "biometric_capability": "some biometric string",
    "font_scaling": "1.0",
    "color_scheme": "light",
    "device_timezone": "RandomTimeZone",
    "os_version": "2.4",
    "app_version": "0.0.0",
    "ota_release_version": "0.0.0",
    "ota_provider": "expo",
    "device_token": "token",
    "device_push_enabled": "True",
}


class DeviceCreateTestCase(FireflyTestCase):
    def compareMandatoryAttr(self, attr, expected_data=mandatory_device_attr):
        self.assertEqual(attr["device_unique_id"], expected_data["device_unique_id"])
        self.assertEqual(attr["device_type"], expected_data["device_type"])
        self.assertEqual(attr["platform"], expected_data["platform"])

    def compareOptionalAttrWithOTAFields(self, attr, expected_data=optional_device_attr_with_expo_ota_fields):
        self.assertEqual(attr["permissions"], expected_data["permissions"])
        self.assertEqual(attr["biometric_capability"], expected_data["biometric_capability"])
        self.assertEqual(attr["font_scaling"], expected_data["font_scaling"])
        self.assertEqual(attr["color_scheme"], expected_data["color_scheme"])
        self.assertEqual(attr["device_timezone"], expected_data["device_timezone"])
        self.assertEqual(attr["os_version"], expected_data["os_version"])
        self.assertEqual(attr["app_version"], expected_data["app_version"])
        self.assertEqual(attr["ota_release_version"], expected_data["ota_release_version"])
        self.assertEqual(attr["ota_provider"], expected_data["ota_provider"])
        self.assertIsNone(attr.get("codepush_release"))
        self.assertEqual(attr["device_token"], expected_data["device_token"])
        self.assertEqual(attr["device_push_enabled"], True)

    def test_device_create(self):
        # Test complete device creation, POST
        full_dict = {}
        full_dict.update(mandatory_device_attr)
        full_dict.update(optional_device_attr_with_expo_ota_fields)
        response = self.client.post("/device/", full_dict, format="json")
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.compareMandatoryAttr(resp)
        self.compareOptionalAttrWithOTAFields(resp)

        # Test null os_version
        full_dict["device_unique_id"] = "0BF347AA-C7C8-42E9-ACC4-FCB5F20ECF22"
        del full_dict["os_version"]
        response = self.client.post("/device/", full_dict, format="json")
        self.assertEqual(response.status_code, 201)

    def test_device_create_update(self):
        # Test minimal device creation, POST
        response = self.client.post("/device/", mandatory_device_attr, format="json")
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.compareMandatoryAttr(resp)

        # Prove Get returns correctly
        response = self.client.get(f"/device/{device_unique_id}/", format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.compareMandatoryAttr(resp)
        self.assertIsNotNone(resp["id"])
        self.assertIsNotNone(resp["user"])
        self.assertIsNotNone(resp["enrolled_pin_auth"])
        self.assertIsNotNone(resp["enrolled_biometric_auth"])

        # Prove Put returns correctly
        full_dict = {}
        full_dict.update(mandatory_device_attr)
        full_dict.update(optional_device_attr_with_expo_ota_fields)
        response = self.client.put(f"/device/{device_unique_id}/", full_dict, format="json")
        self.assertEqual(response.status_code, 200)
        response = self.client.get(f"/device/{device_unique_id}/", format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.compareMandatoryAttr(resp)
        self.compareOptionalAttrWithOTAFields(resp)

        # Verify token updates work
        token = "new_token"
        full_dict.update(
            {
                "device_token": token,
            }
        )
        response = self.client.put(f"/device/{device_unique_id}/", full_dict, format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.assertEqual(resp["device_token"], token)

        # Ensure Duplicates aren't allowed
        response = self.client.post("/device/", mandatory_device_attr, format="json")
        self.assertEqual(response.status_code, 400)

    def test_device_updates_with_ota_fields(self):
        device_unique_id_for_test = Faker().uuid4()
        mandatory_device_attr_for_test = deepcopy(mandatory_device_attr)
        mandatory_device_attr_for_test["device_unique_id"] = device_unique_id_for_test
        # Test minimal device creation, POST
        response = self.client.post("/device/", mandatory_device_attr_for_test, format="json")
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.compareMandatoryAttr(resp, expected_data=mandatory_device_attr_for_test)

        # Prove Get returns correctly
        response = self.client.get(f"/device/{device_unique_id_for_test}/", format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.compareMandatoryAttr(resp, expected_data=mandatory_device_attr_for_test)
        self.assertIsNotNone(resp["id"])
        self.assertIsNotNone(resp["user"])
        self.assertIsNotNone(resp["enrolled_pin_auth"])
        self.assertIsNotNone(resp["enrolled_biometric_auth"])

        # Prove Put returns correctly
        full_dict = {}
        full_dict.update(mandatory_device_attr_for_test)
        full_dict.update(optional_device_attr_with_expo_ota_fields)
        response = self.client.put(f"/device/{device_unique_id_for_test}/", full_dict, format="json")
        self.assertEqual(response.status_code, 200)
        response = self.client.get(f"/device/{device_unique_id_for_test}/", format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.compareMandatoryAttr(resp, expected_data=mandatory_device_attr_for_test)
        self.compareOptionalAttrWithOTAFields(resp, optional_device_attr_with_expo_ota_fields)

        # Verify token updates work
        token = "new_token"
        full_dict.update(
            {
                "device_token": token,
            }
        )
        response = self.client.put(f"/device/{device_unique_id_for_test}/", full_dict, format="json")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.assertEqual(resp["device_token"], token)

        # Ensure Duplicates aren't allowed
        response = self.client.post("/device/", mandatory_device_attr_for_test, format="json")
        self.assertEqual(response.status_code, 400)


class MinAppVersionsTestCase(FireflyTestCase):
    def test_get_min_app_versions(self):
        data = self.client.get("/device/min-versions/").json()
        self.assertIn("ios", data)
        self.assertIn("android", data)
