from __future__ import annotations

import inspect
import logging
from typing import TYPE_CHECKING, List, Optional

from dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import FieldDoesNotExist, ValidationError
from django.db import models
from django.db.models import Q, QuerySet

from firefly.core.assignment.models import AssignableMixin
from firefly.core.services.open_ai.case_summary_generation import generate_case_summary
from firefly.core.user.models import Person
from firefly.modules.cases.case_relation_side_effects import CaseRelationSideEffects
from firefly.modules.cases.case_side_effects import CaseSideEffects
from firefly.modules.cases.constants import (
    CASE_STATUS,
    CASE_TRANSITIONS,
    CaseStatus,
)
from firefly.modules.cases.tasks import create_case_summary_for_case
from firefly.modules.chat_message.models import ChatMessageV2
from firefly.modules.events.models import EventLog
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import PreSaveValidationError, SaveHandlersMixin
from firefly.modules.statemachines.mixin import StatusCategory, WorkUnitStateMachineMixin
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.models import Task, TaskCollection, TaskRelation
from firefly.modules.work_units.models import WorkUnit
from firefly.modules.work_units.utils import WORKUNIT_COMPLETION_STATUSES
from firefly.modules.worklists.constants import SUB_CATEGORY_CHOICES

if TYPE_CHECKING:
    # These imports are required for typechecking
    from firefly.modules.forms.models import Form  # noqa

logger = logging.getLogger(__name__)


class CaseCategory(BaseModelV3, DirtyFieldsMixin, AssignableMixin):
    """
    Represents a type of Case.
    """

    # Display name for the category.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(unique=True, max_length=255)  # noqa: TID251

    # Display name for chips and other indicators.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    short_title = models.CharField(unique=True, max_length=25, null=True, blank=True)  # noqa: TID251

    # A human-readable, understandable key that the code and analytics can use to refer to
    # this category (versus the Title, which could be changed for legibility, or the ID, which
    # is not intuitive and which varies across the databases).
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(  # noqa: TID251
        unique=True,
        max_length=255,
        null=True,
        blank=True,
    )

    # Member facing name for the category.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    member_facing_title = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
        help_text="Member facing name for the category. This name will be displayed in app and marketing website.",
    )

    # Can be used to auto-populate Case.description
    description = models.TextField(blank=True, null=True)
    # Can be used to auto-populate Case.notes
    notes = models.TextField(blank=True, null=True)

    # If true, the category is deprecated. It cannot be selected when creating new cases, but remains visible on
    # existing cases.
    is_deprecated = models.BooleanField(default=False, null=True)
    # If true, designates that this Case and its statuses, notes, etc.
    # may be shown to the member
    is_member_facing = models.BooleanField(default=False, null=True, blank=True)
    # If true, the category is public-facing and can be selected on form intake
    is_public_facing = models.BooleanField(
        default=False, null=True, blank=True, help_text="Used on marketing pages for intake"
    )

    # Should Lucian allow editing an external description?
    # This is separate config than is_member_facing because for some Categories
    # we show the member some information but not necesarilly this manually-editable title
    uses_external_description = models.BooleanField(default=False, null=True, blank=True)
    # If true, the category is only used programmatically, and cannot be selected by humans in the user interface.
    is_system_only = models.BooleanField(blank=True, null=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    parent = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=True,
        choices=SUB_CATEGORY_CHOICES,
    )

    # Represents the operational group that is primarily responsible for working cases of this category.
    zone = models.TextField(
        choices=[
            ("Clinical", "Clinical"),
            ("Member", "Member"),
            ("Network", "Network"),
        ],
        null=True,
        blank=True,
    )

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    task_collection = models.ForeignKey(TaskCollection, on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251

    # State machine that should apply to cases of this category. When creating a
    # new case, this content is copied to the case, so that future updates to
    # the state machine do not affect and potentially break earlier cases.
    state_machine_definition = models.ForeignKey(
        StateMachineDefinition,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )

    # Number of days within which a case of this category is expected to be resolved.
    expected_time_to_resolution_days = models.PositiveIntegerField(
        null=True,
        blank=True,
    )

    # If true, indicates that a case of this category should be considered
    # evidence of care provided to the patient. This is distinct from zone =
    # "Clinical" and is primarily used for determining "active" patients for
    # analytics such as calculating cost-to-serve.
    is_evidence_of_care = models.BooleanField(
        help_text="Should a case of this category be considered evidence of care provided to a patient?",
        blank=True,
        null=True,
    )

    # Contains corresponding education chassis tag mapped to the CaseCategory
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    education_chassis_tag = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # define reverse accessors
    relation_templates: QuerySet["CaseRelationTemplate"]
    form_set: QuerySet["Form"]

    class Meta(BaseModelV3.Meta):
        ordering = ["title"]
        verbose_name_plural = "Case categories"

    def __str__(self):
        return f"CaseCategory({self.id}) - {self.title}"

    # TODO:
    # - template: TaskCollection representing the standard operating procedure for this type of
    #   case.
    # - service level objective: Expected turnaround time for this type of case.
    # - assignment: To whom this type of case should be routed.


class Tag(BaseModelV3):
    # Unique keys for valid case tags
    HIGH_RISK = "high_risk"
    CARE_GAP = "care_gap"
    MEMBER_FACING = "member_facing"
    # A human-readable, understandable key that the code and analytics can use to refer to
    # this category
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    display_name = models.CharField(max_length=255, null=False, blank=False)  # noqa: TID251

    def __str__(self):
        return self.display_name

    class Meta(BaseModelV3.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["unique_key"], condition=Q(deleted=None), name="cases_case_tag_unique_key_uniq"
            ),
            models.UniqueConstraint(
                fields=["display_name"], condition=Q(deleted=None), name="cases_case_tag_display_name_uniq"
            ),
        ]


class Case(SaveHandlersMixin, WorkUnit, WorkUnitStateMachineMixin, CaseSideEffects, DirtyFieldsMixin):
    """
    Represents a categorized request that we work to fulfill.
    """

    # Subject of the case, usually the patient, member, or prospect who made the initial request.
    person = models.ForeignKey(
        Person,
        related_name="cases",
        # On delete, do nothing for now; we'll ensure referential integrity once the backfill of
        # Person is fully validated.
        # TODO: Set to CASCADE.
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        # Can be null for now, until the backfill is complete and verified.
        # TODO: Remove.
        null=True,
        blank=True,
    )

    # Type of case, usually categorized by the operator responding to the request.
    category = models.ForeignKey(
        CaseCategory,
        on_delete=models.RESTRICT,
        related_name="cases",
    )

    # Used to give more context about the case and differentiate from similar
    # cases at a glance. This should only be visible to internal staff working
    # the case.
    description = models.TextField(blank=True, null=True)
    # More in-depth content associated with the case
    notes = models.TextField(blank=True, null=True)

    # Description of the case that may be shown to members.
    external_description = models.TextField(blank=True, null=True)

    # Tasks related to the case. A TaskRelation with is_parent=True is a child of this case and
    # represents work done in service of fulfilling the request.
    task_relations = GenericRelation(TaskRelation)

    # Generated by system
    is_proposed = models.BooleanField(null=True, blank=True)

    # Event logs related to the case.
    event_logs = GenericRelation(
        EventLog,
        content_type_field="target_content_type",
        object_id_field="target_object_id",
    )

    tags = BaseModelV3ManyToManyField(Tag, related_name="cases", blank=True, through="CaseTags")
    current_summary = models.OneToOneField(
        "cases.CaseSummary",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="current_for_case",
    )

    # Limits the fields analyzed by DirtyFieldsMixin, to improve performance
    FIELDS_TO_CHECK = ["category"]

    # The following properties are required to add status to Case.
    # The status is set based on the status of child tasks.
    @property
    def state_machine_states_with_categories(self):
        return CASE_STATUS if self.auto_complete else self.state_machine_content.get("state_with_categories")

    @property
    def state_machine_initial_state(self):
        return CaseStatus.IN_PROGRESS if self.auto_complete else self.state_machine_content.get("initial_state")

    @property
    def state_machine_transitions(self):
        return CASE_TRANSITIONS if self.auto_complete else self.state_machine_content.get("transitions")

    @property
    def subject(self):
        return self.person

    # If the case has state_machine_content, then the case will be active even if all the tasks are marked as done.
    # Only way to complete would be by changing the status to complete.
    # If the case doesn't have state_machine_content then it would be closed when all the tasks are marked as done.
    @property
    def auto_complete(self):
        return self.state_machine_content is None

    # define reverse accessors
    summaries: QuerySet["CaseSummary"]
    relations: QuerySet["CaseRelation"]
    case_tags: QuerySet["CaseTags"]

    # TODO:
    # - due date: Based on CaseCategory service level objective.
    # - owner: Who is responsible for the resolution of the overall case, not just individual
    #   tasks.

    class Meta(WorkUnit.Meta):
        abstract = False

    def pre_save_validation(self, changed):
        # Raising an exception is case is being closed with open tasks
        if changed("action") and self.get_status_category_from_action(self.action) in (
            StatusCategory.COMPLETE,
            StatusCategory.DEFERRED,
        ):
            existing_open_tasks = Task.objects.filter(
                relations__content_type=ContentType.objects.get_for_model(Case),
                relations__object_id=self.id,
                is_complete=False,
            )
            if existing_open_tasks.count() > 0:
                raise PreSaveValidationError("Case with open tasks cannot be closed")

    def save(self, *args, **kwargs):
        from firefly.modules.cases.utils import update_case_owner_group

        created = self.pk is None

        category_is_dirty = False
        existing_category_id = None
        existing_category = None
        new_category = self.category
        if self.is_dirty(check_relationship=True):
            dirty_fields = self.get_dirty_fields(check_relationship=True)
            if "category" in dirty_fields.keys():
                category_is_dirty = True
                existing_category_id = dirty_fields["category"]
        if existing_category_id:
            existing_category = CaseCategory.objects.get(id=existing_category_id)

        # Get the state_machine_content from case category
        if created and self.category.state_machine_definition:
            self.state_machine_content = self.category.state_machine_definition.content

        super(Case, self).save(*args, **kwargs)

        if created and self.person is not None:
            update_case_owner_group(self, self.person)

        # Find objects associated with this Case's Category, and instantiate them as Relations.
        # The currently-known set of content types requires a Person. If we want to make this
        # more generic, we'd have to associate the Case Relation Template with the attributes
        # it needs passed in.
        from firefly.modules.cases.utils import update_case_relations_for_new_case_category

        if category_is_dirty and self.person is not None:
            update_case_relations_for_new_case_category(self, existing_category, new_category)

        # Whenever a case is created, check if the category has is_member_facing as true
        # If it is true then add a MemberFacing Tag and case mapping so that it will be displayed in CRM
        if created and self.category.is_member_facing:
            # Find the member facing tag
            member_facing_tag: Optional[Tag] = Tag.objects.filter(unique_key=Tag.MEMBER_FACING).first()
            if member_facing_tag is not None:
                self.tags.add(member_facing_tag)

    def add_new_relation(self, relation_data: dict):
        from firefly.modules.cases.utils import (
            move_case_to_initial_state,
        )
        from firefly.modules.phone_calls.models import PhoneCall

        case_relation, _created = CaseRelation.objects.get_or_create(
            case=self,
            content_type=relation_data["content_type"],
            object_id=relation_data["object_id"],
            defaults={**relation_data},
        )
        # if case is inactive then re-open it and set to initial state
        chat_message_content_type = ContentType.objects.get_for_model(ChatMessageV2)
        phone_call_content_type = ContentType.objects.get_for_model(PhoneCall)
        if self.status_category in WORKUNIT_COMPLETION_STATUSES and case_relation.content_type in (
            chat_message_content_type,
            phone_call_content_type,
        ):
            move_case_to_initial_state(self)


class CaseSummary(SaveHandlersMixin, BaseModelV3):
    case = models.ForeignKey(Case, related_name="summaries", on_delete=models.CASCADE)
    content = models.TextField(blank=True, null=True)
    # `version` helps us track the most recent and relevant Case Summary
    # as new summaries are generated for the same Case, e.g. after a new message has been linked
    version = models.IntegerField(blank=True, null=True)

    @classmethod
    def create_for_case(self, case):
        """
        Create a new CaseSummary with content generated by OpenAI
        and update the corresponding Case
        """
        content = generate_case_summary(case)
        if content is None:
            # There is no relevant summary e.g. if there are too few Messages for a summary to make sense
            # If so, the Case should no longer have a current summary
            # A new one might be set in the future e.g. if another Message were linked
            case.current_summary = None
            case.save(update_fields=["current_summary"])
            logger.info("CaseRelation %s cleared current_summary from empty content")
            return None
        # Auto-incrementing version numbers are handled by pre_save_mutation
        summary = self.objects.create(case=case, content=content)
        case.current_summary = summary
        case.save(update_fields=["current_summary"])
        logger.info("For Case %s, created CaseSummary %s", case, summary)
        return summary

    def pre_save_mutation(self, changed):
        # On create, manage setting/incrementing the version number
        if not changed("id"):
            return
        self.version = 0
        # Because we depend on the db to pick the correct next version number
        # there is a potential for race conditions here
        # (picking the same next version # -> fail the uniqueness constraint)
        # In the future we may want to consider something like locking these rows
        max_version_summary = (
            CaseSummary.objects.filter(case=self.case).order_by("-version").values_list("version", flat=True).first()
        )
        if max_version_summary is not None:
            self.version = max_version_summary + 1

    class Meta:
        verbose_name_plural = "Case summaries"
        constraints = [
            models.UniqueConstraint(
                fields=["case_id", "version"],
                condition=Q(Q(deleted=None) & ~Q(version=None)),
                name="casesummary_version_unique",
            )
        ]


class CaseSummaryReview(BaseModelV3):
    case_summary = models.ForeignKey(CaseSummary, on_delete=models.CASCADE, related_name="reviews")
    provider_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    score = models.IntegerField(blank=True, null=True)
    content = models.TextField(blank=True, null=True)


class CaseRelation(SaveHandlersMixin, BaseModelV3):
    """
    Many-to-many relation between a Case and another object.

    A Case is a highly generalized type, but can be configured to handle highly
    specialized workflows. Some of these workflows require additional structured
    data. Rather than extend the Case model with workflow-specific fields,
    CaseRelation allows us to embed specialized objects into individual Case
    objects. These embedded objects can then be rendered using a generic
    framework in the Case user interface.

    For example, Cases are used to track the lifecycle of Steerages. We would
    not want to add Steerage-specific fields to the Case model. Instead, we use
    CaseRelations to extend Cases with Steerage data.

    Not all relations between Cases and other objects need to be handled using
    CaseRelation. For example, it may be valid for another model to have a
    foreign key to Case. CaseRelation should be used to extend the Case data
    model for specific workflows, and to take advantage of the generic framework
    for rendering embedded objects in the Case user interface.

    There are three models for which CaseRelation has a special meaning. The two
    communication-related models, ChatMessageV2 and PhoneCall, are not rendered
    as nested objects, but are instead linked to cases in the chat thread. The
    CarePlan model is not rendered as a nested object, but is instead treated as
    a parent of the related Case.
    """

    case = models.ForeignKey(Case, related_name="relations", on_delete=models.CASCADE)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        # Before adding a model here, please read the above docstring for this
        # class. If you do not need to render the object in the Case UI,
        # reconsider whether you need a CaseRelation. Otherwise, you will need
        # to update CaseRelation serializers and the Case UI framework to handle
        # your additional model class.
        limit_choices_to=(
            models.Q(app_label="appointment", model__in=["appointment"])
            | models.Q(app_label="care_plan", model__in=["careplan"])
            | models.Q(app_label="cases", model__in=["case"])
            | models.Q(app_label="chat_message", model__in=["chatmessagev2"])
            | models.Q(app_label="forms", model__in=["formsubmission"])
            | models.Q(app_label="onboarding", model__in=["onboardingstate"])
            | models.Q(app_label="phone_calls", model__in=["phonecall"])
            | models.Q(app_label="quality", model__in=["measurereport"])
            | models.Q(app_label="referral", model__in=["priorauthorization", "steerage", "waiver"])
            | models.Q(app_label="schedule", model__in=["scheduleingestionjob"])
        ),
    )
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        # Re-trigger Case summarization when the set of linked Messages changes
        if not (changed("id") or changed("deleted")):
            return
        if isinstance(self.content_object, ChatMessageV2):
            create_case_summary_for_case.send(self.case_id, self.id)

    class Meta(BaseModelV3.Meta):
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
        ]


class CaseRelationTemplate(BaseModelV3, CaseRelationSideEffects):
    """
    Types of objects that should be related to a Case upon creation, as
    specified by the Case Category.
    """

    def validate_has_person(value):
        try:
            contentType = ContentType.objects.get(id=value)
            contentType.model_class()._meta.get_field("person")
        except FieldDoesNotExist:
            raise ValidationError([{"message": "Content Type should have a Person attribute."}])

    # There is an assumption currently that content types associated will have a Person
    # attribute; if this becomes not true in the future, we'd have to genericize how we
    # pass arguments into the ContentType on creation.
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, validators=[validate_has_person])
    case_category = models.ForeignKey(CaseCategory, related_name="relation_templates", on_delete=models.CASCADE)

    # This should be a string corresponding to the name of a function inside CaseRelationSideEffects.
    # That function should be annotated to take three arguments: self, a Case, and an object of
    # type `self.content_type`. This function will be run after an object of type `content_type`
    # has been instantiated.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    on_after_creation = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    # This should be a string corresponding to the name of a function inside CaseRelationSideEffects.
    # That function should be annotated to take three arguments: self, a Case, and an object of
    # type `self.content_type`. This function will be run after an object of type `content_type`.
    # THIS WILL BE RUN ONCE FOR EACH RELATION OF THE SAME CONTENT TYPE.
    # Ex: If there are two steerages associated, and steerage is defined as a content type in this class,
    # then we will run this function for each steerage.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    on_category_change = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    def clean(self):
        # The follwing ensures that if we are specifying an on_after_creation/on_category_change functions,
        # then its parameters match what we're expecting to pass into it. Namely, the created
        # case as well as the created related object, whose type is specified by content_type.
        functions = [self.on_after_creation, self.on_category_change]
        for function_name in functions:
            if function_name is not None:
                # First, ensure that this function exists and is resolvable from within the context
                # of a CaseRelationTemplate.
                try:
                    function = getattr(CaseRelationTemplate, function_name)
                except Exception:
                    raise ValidationError(
                        [{"message": f"Case Relation Template function {function_name} should be callable"}]
                    )
                # Next, ensure it has the right signature. Use "inspect" to get the elements of the signature,
                # then look at the annotation of the second and third parameters (self being the first) to ensure
                # they match expectations.
                signature = inspect.signature(function)
                keys = list(signature.parameters.keys())

                case_parameter = signature.parameters[keys[1]]
                if case_parameter.annotation != Case.__name__:
                    raise ValidationError([{"message": f"Case should be the second parameter of {function_name}"}])

                content_type_parameter = signature.parameters[keys[2]]
                if content_type_parameter.annotation != self.content_type.model_class().__name__:
                    raise ValidationError(
                        [{"message": f"Third parameter of {function_name} should match the content_type"}]
                    )

    def save(self, *args, **kwargs):
        # Full clean calls both clean_fields() and clean()
        self.full_clean()
        super(CaseRelationTemplate, self).save(*args, **kwargs)


class CaseTags(BaseModelV3):
    case = models.ForeignKey(
        Case,
        related_name="case_tags",
        on_delete=models.CASCADE,
    )

    tag = models.ForeignKey(
        Tag,
        related_name="case_tags",
        on_delete=models.CASCADE,
    )

    class Meta:
        verbose_name_plural: str = "Case tags"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["case", "tag"],
                condition=Q(deleted=None),
                name="case_tag_uniq",
            )
        ]
