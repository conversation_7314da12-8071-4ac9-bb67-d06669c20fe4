from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.programs.models import Program


class SignupReasons(BaseModelV3):
    id = models.AutoField(primary_key=True)
    # DO NOT COPY-PASTE: Prefer TextField over <PERSON><PERSON><PERSON>ield
    label = models.CharField(max_length=150)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    subtext = models.CharField(max_length=150, null=True, blank=True)  # noqa: TID251
    sort_order = models.BigIntegerField()
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    # DO NOT COPY-PASTE: Prefer TextField over Cha<PERSON><PERSON>ield
    onboarding_welcome_message = models.CharField(max_length=400, null=True, blank=True)  # noqa: TID251
    # Connecting a SignupReasons to a Program triggers enrollment in that program
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    program = models.ForeignKey(Program, related_name="sigup_reason", on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251

    # Created a computed alias to work with front-end QuesionView compoent
    @property
    def value(self):
        return self.id

    class Meta(BaseModelV3.Meta):
        ordering = ["sort_order"]
        db_table = "signup_reasons"
        verbose_name_plural = "Signup Reasons"
