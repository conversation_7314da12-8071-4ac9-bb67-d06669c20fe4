from django.conf import settings
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from firefly.modules.firefly_django.models import BaseModelV3

from .postsave import POSTSAVE_HOOKS


def run_dataevent_postsave_hooks(instance):
    for postsave_hook in POSTSAVE_HOOKS:
        instance = postsave_hook(instance) or instance
        # `or instance` lets the hook return None without breaking anything


class DataEvent(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    user = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="user", on_delete=models.SET_NULL, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    properties = JSONField(null=True, blank=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        run_dataevent_postsave_hooks(self)

    class Meta(BaseModelV3.Meta):
        db_table = "data_event"
        verbose_name_plural = "Data Events"
