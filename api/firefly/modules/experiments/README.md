# Experiments

The Experiments module provides a simple A/B testing framework for randomized experiments. It allows assignment of subjects to different variants and tracks these assignments for analysis.

## Overview

This module implements a basic randomized experiment system where subjects (typically users) are randomly assigned to different variants of an experiment. The system ensures consistent assignment - once a subject is assigned to a variant, they remain in that variant for the duration of the experiment.

## Business Logic

The experimentation process works as follows:

1. **Experiment Creation**: Define an experiment with a name and time period
2. **Variant Definition**: Create different variants (treatment groups) for the experiment
3. **Subject Assignment**: When a subject encounters the experiment, they are randomly assigned to a variant
4. **Consistent Assignment**: Subsequent requests for the same subject return the same variant assignment

## Core Models

### Experiment
Main experiment configuration:
- **name**: Machine-readable unique identifier for the experiment
- **period**: DateTimeRange defining when the experiment runs
- **is_active**: Boolean flag controlling whether new assignments are made

### Variant
Treatment groups within an experiment:
- **experiment**: Foreign key to the parent experiment
- **name**: Machine-readable identifier for the variant
- **Unique constraint**: (experiment, name) must be unique

### Assignment
Subject assignment to variants:
- **variant**: Foreign key to the assigned variant
- **subject_uri**: URI identifying the subject (e.g., "https://lucian.firefly.health/patients/{user_id}")
- **Unique constraint**: (variant, subject_uri) prevents duplicate assignments

## Core Functionality

### Assignment Algorithm
The `Experiment.assign()` method implements the core assignment logic:

1. **Active Check**: Returns `None` if experiment is not active
2. **Existing Assignment**: Returns existing assignment if subject already assigned
3. **Variant Validation**: Raises `MissingVariantsException` if no variants exist
4. **Random Assignment**: Uses `random.choice()` for equal probability assignment across variants
5. **Assignment Creation**: Creates and returns new `Assignment` record

### Subject URI Format
Subjects are identified using URI format:
- **Pattern**: `https://lucian.firefly.health/patients/{user_id}`
- **Purpose**: Provides unique, resolvable identifier for subjects
- **Flexibility**: Allows for different subject types beyond just users

### Assignment Consistency
The system ensures assignment consistency through:
- **Database Constraints**: Unique constraint on (variant, subject_uri)
- **Idempotent Assignment**: Multiple calls return same assignment
- **Persistent Storage**: Assignments survive across sessions

## API Endpoints

### Experiment Assignment
- `GET /api/experiments/{experiment_name}/assign/` - Assign authenticated user to experiment variant

**Response Format:**
```json
{
  "id": 123,
  "subject_uri": "https://lucian.firefly.health/patients/456",
  "variant": {
    "id": 789,
    "name": "treatment_a"
  }
}
```

**Behavior:**
- Returns existing assignment if user already assigned
- Returns 404 if experiment is not active
- Requires patient authentication (`IsPatient` permission)

## Usage Example

### Creating an Experiment
```python
# Create experiment
experiment = Experiment.objects.create(
    name="onboarding_flow_test",
    period=DateTimeRange(start_date, end_date),
    is_active=True
)

# Create variants
control = Variant.objects.create(
    experiment=experiment,
    name="control"
)
treatment = Variant.objects.create(
    experiment=experiment,
    name="new_flow"
)

# Assign user to experiment
assignment = experiment.assign("https://lucian.firefly.health/patients/123")
print(f"User assigned to: {assignment.variant.name}")
```

### Client-Side Usage
```javascript
// Get experiment assignment for current user
fetch('/api/experiments/onboarding_flow_test/assign/')
  .then(response => response.json())
  .then(assignment => {
    if (assignment.variant.name === 'new_flow') {
      // Show new onboarding flow
      showNewOnboardingFlow();
    } else {
      // Show control flow
      showControlFlow();
    }
  });
```

## Key Features

### Equal Probability Assignment
- All variants have equal probability of assignment
- Uses Python's `random.choice()` for selection
- No support for weighted assignment in current implementation

### Assignment Persistence
- Assignments are stored in database
- Users remain in same variant across sessions
- No automatic reassignment or variant switching

### Experiment Control
- `is_active` flag controls new assignments
- Inactive experiments return `None` for new assignment requests
- Existing assignments remain valid even when experiment is inactive
