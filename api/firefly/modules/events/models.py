from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from firefly.modules.firefly_django.models import BaseModelV3


# This is not an exhaustive list
# e.g. we have computed event types like `reminder_...`
class EventTypeCodes:
    APPOINTMENT_SCHEDULED = "appointment_scheduled"
    APPOINTMENT_COMPLETED = "appointment_completed"
    APPOINTMENT_CANCELLED = "appointment_cancelled"
    APPOINTMENT_PUSH_REMINDER_SENT = "appointment_push_reminder_sent"
    FOCUSED_APPOINTMENT_SCHEDULED = "focused_appointment_scheduled"
    PATIENT_CREATED = "patient_created"
    PROGRAM_ENROLLED = "program_enrolled"
    PROGRAM_UNENROLLED = "program_unenrolled"
    COMMUNICATION_SENT = "communication_sent"
    ATTRIBUTION_STATE_CHANGE = "attribution_state_change"
    INSURANCE_PLAN_REVIEW_STATE_CHANGE = "insurance_plan_review_state_change"
    MAILER_STATUS_CHANGE = "mailer_status_change"
    MED_REFILL_NOTIFICATION_SENT = "med_refill_notification_sent"
    HEALTH_PLAN_SURVEY_SENT = "health_plan_survey_sent"
    ASSIGNMENT_BY_POD = "assignment_by_pod"


class EventLog(BaseModelV3):
    """Tracks occurrences of business events."""

    # String keyword determining the type of event, such as ONBOARDING_CARE_PLAN_CREATED
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(max_length=127, db_index=True)  # noqa: TID251

    # Primary affected user, if any
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    user = models.ForeignKey(settings.AUTH_USER_MODEL, blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    target_object_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)  # noqa: TID251

    target_content_type = models.ForeignKey(
        ContentType,
        blank=True,
        null=True,
        related_name="target",
        on_delete=models.CASCADE,
        db_index=True,
    )

    target = GenericForeignKey("target_content_type", "target_object_id")

    # Extra metadata about the event.
    metadata = JSONField(default=dict)

    class Meta(BaseModelV3.Meta):
        indexes = [
            models.Index(fields=["type", "user"]),
            models.Index(fields=["type", "target_object_id", "target_content_type"]),
            models.Index(fields=["user", "type", "target_object_id", "target_content_type"]),
        ]


class EventType(BaseModelV3):
    """Captures different types of tasks that may be created"""

    # Eventually will want to make this a foreignkey from EventLog type
    # See EventTypeCodes
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(max_length=127, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    short_name = models.CharField(max_length=127, db_index=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    description = models.CharField(max_length=255, null=True)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "event_type"

    def __str__(self):
        return self.type


class EventDateProperty(BaseModelV3):
    """Event date maps events to dates"""

    type = models.ForeignKey(EventType, on_delete=models.CASCADE, db_index=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    short_name = models.CharField(max_length=127)  # noqa: TID251
    reference_object_type_id = models.ForeignKey(
        ContentType, blank=True, null=True, on_delete=models.CASCADE, db_index=True
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reference_object_column = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "event_date"

    def __str__(self):
        return f"{self.type}: {self.reference_object_type_id}: {self.reference_object_column}"
