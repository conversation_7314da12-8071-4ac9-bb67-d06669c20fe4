from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from django.conf import settings
from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.test import override_settings
from django.utils import timezone
from django.utils import timezone as django_timezone
from pandas.tseries.offsets import BDay

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.models import User
from firefly.modules.appointment.constants import AppointmentReason
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.care_plan.factories import CarePlanTemplateFactory, CarePlanTypeFactory
from firefly.modules.care_plan.management.commands.backfill_create_care_plan import STAYING_HEALTHY_CARE_PLAN_UID
from firefly.modules.care_plan.models import CarePlan, CarePlanTemplate, CarePlanType
from firefly.modules.care_plan.utils import (
    get_or_create_care_plan_of_type,
)
from firefly.modules.device.models import UserDevice
from firefly.modules.events.models import <PERSON><PERSON><PERSON><PERSON>roperty, EventLog, EventType, EventTypeCodes
from firefly.modules.forms.factories import FormFactory
from firefly.modules.forms.models import FormSubmission
from firefly.modules.tasks.models import CarePlanTask, Task, TaskCollection

from .factories import PushNotificationEventFactory
from .signals import FIRST_APPOINTMENT_TASK_COLLECTION_TITLE, REPEAT_APPOINTMENT_TASK_COLLECTION_TITLE

TASK_AUTOMATION = {
    "HEALTH_GUIDE": "**********",
    "PRACTICE_ADMIN": "**********",
    "LUCIAN_BOT": "**********",
}


class EventLogApiTestCase(FireflyTestCase):
    def test_get_response(self):
        # Given an event pointing to a User target
        blah_event = EventLog.objects.create(
            user=self.patient, type="blah", target=self.patient, metadata={"timestamp": str(django_timezone.now())}
        )
        response = self.provider_client.get(
            f"/events/logs/?user={self.patient.id}",
            format="json",
        )
        events = response.json()
        for event in events:
            if event["type"] == "blah":
                self.assertEqual(event["id"], blah_event.id)
                self.assertEqual(event["target_json"], {})
        # Given an appointment cancellation event
        appointment = AppointmentFactory()
        event = EventLog.objects.create(
            user=self.patient,
            type=EventTypeCodes.APPOINTMENT_CANCELLED,
            target=appointment,
        )
        response = self.provider_client.get(
            f"/events/logs/?user={self.patient.id}?type={EventTypeCodes.APPOINTMENT_CANCELLED}",
            format="json",
        )
        event_in_response = [el for el in response.json() if el["id"] == event.id][0]
        self.assertEqual(event_in_response["target_json"]["id"], appointment.id)

    def test_type_filter(self):
        EventLog.objects.all().delete()
        # Given 2 events with different types
        push_notification_event = PushNotificationEventFactory.create(
            user=self.patient,
            message_text="Testing",
            sent_by_provider_id=self.provider.id,
            message_title="title",
        )
        other_event = EventLog.objects.create(user=self.patient, type="blah")
        # Given an API request with no filter param
        response = self.provider_client.get(
            "/events/logs/",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [other_event.id, push_notification_event.id],
        )
        # Given an API request filtering for one type
        response = self.provider_client.get(
            f"/events/logs/?type={push_notification_event.type}",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [push_notification_event.id],
        )
        # Given an API request filtering for the other type
        response = self.provider_client.get(
            f"/events/logs/?type={other_event.type}",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [other_event.id],
        )
        # Given an API request filtering for a type that doesn't match either event
        response = self.provider_client.get(
            "/events/logs/?type=not_a_real_type",
            format="json",
        )
        self.assertEqual(
            response.json(),
            [],
        )

    def test_user_filter(self):
        # Given 2 events with different users
        user_2 = PersonUserFactory().user
        user_3 = PersonUserFactory().user
        EventLog.objects.all().delete()
        user_1_event = PushNotificationEventFactory.create(
            user=self.patient,
            message_text="Testing 1",
            sent_by_provider_id=self.provider.id,
            message_title="title",
        )
        user_2_event = PushNotificationEventFactory.create(
            user=user_2,
            message_text="Testing 2",
            sent_by_provider_id=self.provider.id,
            message_title="title",
        )
        # Given an API request with no filter param
        response = self.provider_client.get(
            "/events/logs/",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [
                user_2_event.id,
                user_1_event.id,
            ],
        )
        # Given an API request filtering for one user
        response = self.provider_client.get(
            f"/events/logs/?user={user_1_event.user.pk}",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [user_1_event.id],
        )
        # Given an API request filtering for the other user
        response = self.provider_client.get(
            f"/events/logs/?user={user_2_event.user.pk}",
            format="json",
        )
        self.assertEqual(
            list(map(lambda item: item["id"], response.json())),
            [user_2_event.id],
        )
        # Given an API request filtering for a user that doesn't match either event
        response = self.provider_client.get(
            f"/events/logs/?user={user_3.pk}",
            format="json",
        )
        self.assertEqual(
            response.json(),
            [],
        )


class EventLogFactoriesTestcase(FireflyTestCase):
    def test_push_notification_factory(self):
        message_text = "Hi this is a test"
        message_title = "Title"
        reason_description = "Reason"
        event_log = PushNotificationEventFactory.create(
            user=self.patient,
            message_text=message_text,
            sent_by_provider_id=self.provider.id,
            message_title=message_title,
            reason_description=reason_description,
        )
        self.assertEqual(event_log.metadata["communication_type"], "push_notification")
        self.assertEqual(event_log.metadata["message_title"], message_title)
        self.assertEqual(event_log.metadata["message_text"], message_text)
        self.assertEqual(event_log.metadata["reason_description"], reason_description)
        self.assertEqual(event_log.metadata["sent_by_provider_id"], self.provider.id)
        self.assertEqual(event_log.user, self.patient)
        self.assertEqual(event_log.type, EventTypeCodes.COMMUNICATION_SENT)
        self.assertIsNotNone(event_log.metadata["timestamp"])


class EventTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.lucian_bot = self.create_provider(email="<EMAIL>", phone_number=TASK_AUTOMATION["LUCIAN_BOT"])
        self.health_guide = self.create_provider(
            email="<EMAIL>", phone_number=TASK_AUTOMATION["HEALTH_GUIDE"]
        )
        self.practice_admin = self.create_provider(
            email="<EMAIL>", phone_number=TASK_AUTOMATION["PRACTICE_ADMIN"]
        )

        onboarding_care_plan_type, _ = CarePlanType.objects.get_or_create(uid="onboarding")
        covid_care_plan_type, _ = CarePlanType.objects.get_or_create(uid="covid19")

        patient_created_at_property, _ = EventDateProperty.objects.get_or_create(
            short_name="patient_created_at",
            type=EventType.objects.get(type="patient_created"),
            reference_object_type_id=ContentType.objects.get_for_model(User),
            reference_object_column="created_at",
        )

        appointment_start_property, _ = EventDateProperty.objects.get_or_create(
            short_name="appointment_start",
            type=EventType.objects.get(type=EventTypeCodes.APPOINTMENT_SCHEDULED),
            reference_object_type_id=ContentType.objects.get_for_model(Appointment),
            reference_object_column="start",
        )

        self.onboarding_care_plan_template, _ = CarePlanTemplate.objects.get_or_create(
            type=onboarding_care_plan_type, title="Getting Started with Firefly"
        )

        self.onboarding_task_collection, _ = TaskCollection.objects.get_or_create(title="Patient Onboarding")

        self.health_review_form = FormFactory()

        self.onboarding_task_collection.tasks.create(
            title="Complete this onboarding form",
            event_date_property=patient_created_at_property,
            assign_to_patient=True,
            days_offset=14,
        )

        self.first_appointment_task_collection, _ = TaskCollection.objects.get_or_create(
            title=FIRST_APPOINTMENT_TASK_COLLECTION_TITLE
        )

        self.first_appointment_task_collection.tasks.create(
            title="Run patient eligibility check",
            assignee_role=Group.objects.get(name="Provider"),
            days_offset=0,
        )

        self.first_appointment_task_collection.tasks.create(
            title=(
                "Complete pre-visit questionnaire (about 2 min): https://fireflyhealth.formstack.com/forms/new_patient"
            ),
            assignee_role=Group.objects.get(name="Provider"),
            days_offset=0,
            event_date_property=appointment_start_property,
        )

        self.first_appointment_task_collection.tasks.create(
            title="Complete health review questionnaire",
            event_date_property=appointment_start_property,
            assign_to_patient=True,
            days_offset=14,
            form=self.health_review_form,
        )

        self.repeat_appointment_task_collection, _ = TaskCollection.objects.get_or_create(
            title=REPEAT_APPOINTMENT_TASK_COLLECTION_TITLE
        )

        self.repeat_appointment_task_collection.tasks.create(
            title="Repeat appointment check",
            assignee_role=Group.objects.get(name="Provider"),
            days_offset=0,
        )

        self.covid_care_plan_template, _ = CarePlanTemplate.objects.get_or_create(
            type=covid_care_plan_type, title="COVID-19"
        )

        self.covid_task_collection, _ = TaskCollection.objects.get_or_create(title="COVID-19")

    @override_settings(TASK_AUTOMATION=TASK_AUTOMATION)
    def test_patient_onboarding_tasks(self, *args):
        """Test creating form submission from autocreated task."""
        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        appt = Appointment(
            time_slot_type="appointment_slot",
            start=ten_days_from_now,
            description="Test test",
            elation_id=1,
            status="Scheduled",
            physician=self.physician,
        )
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        person = PersonUserFactory(user__email="<EMAIL>", first_name="Test")
        auth_user = person.user

        # Attach the patient to the appointmet slot.
        appt.patient = auth_user
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        form_submission = FormSubmission.objects.filter(user=person.user, form=self.health_review_form).first()

        self.assertIsNotNone(form_submission)
        self.assertEqual(form_submission.task_relations.all().count(), 1)

    @override_settings(TASK_AUTOMATION=TASK_AUTOMATION)
    def test_create_appointment_for_patient_directly_in_elation(self, *args):
        """Test creating multiple appointments."""
        person = PersonUserFactory()
        auth_user = person.user

        lucian_bot = self.lucian_bot

        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        appt = Appointment(
            patient=auth_user,
            time_slot_type="appointment",
            start=ten_days_from_now,
            description="Test test",
            elation_id=1,
            status="Scheduled",
            physician=self.physician,
        )
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        # check that appointment related care plan tasks exist.
        # We created 2 Appointment tasks in setUp above,
        # so there should be 2 total tasks in the care plan.
        care_plan = auth_user.care_plans.get(title=self.onboarding_care_plan_template.title)
        self.assertEqual(care_plan.care_plan_tasks.count(), 2)

        # Ensure that auto-created tasks are linked to appointment.
        self.assertEqual(
            Task.objects.filter(
                relations__content_type=ContentType.objects.get_for_model(Appointment),
                relations__object_id=appt.id,
            ).count(),
            3,
        )

        cp2 = CarePlanTask.get_one_from_care_plan(
            care_plan,
            filters={
                "title": (
                    "Complete pre-visit questionnaire (about 2 min): "
                    "https://fireflyhealth.formstack.com/forms/new_patient"
                )
            },
        )
        self.assertEqual(cp2.created_by, lucian_bot)
        self.assertEqual(cp2.due_date, (appt.start + BDay(0)).to_pydatetime())

        # Create another appointment and confirm that only the task for
        # repeat appointments are created
        three_days_from_now = timezone.now() + timedelta(days=3)
        appt = None
        with self.captureOnCommitCallbacks(execute=True):
            appt = Appointment.objects.create(
                patient=auth_user,
                time_slot_type="appointment",
                start=three_days_from_now,
                description="Test test",
                elation_id=2,
                status="Scheduled",
                physician=self.physician,
            )

        care_plan.refresh_from_db()
        self.assertEqual(care_plan.care_plan_tasks.count(), 3)

    @override_settings(TASK_AUTOMATION=TASK_AUTOMATION)
    def test_create_appointment_slot_then_assign_to_patient(self, *args):
        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        appt = Appointment(
            time_slot_type="appointment_slot",
            start=ten_days_from_now,
            description="Test test",
            elation_id=1,
            status="Scheduled",
            physician=self.physician,
        )
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        person = PersonUserFactory()
        auth_user = person.user

        lucian_bot = self.lucian_bot

        # Attach the patient to the appointmet slot.
        appt.patient = auth_user
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        # check that appointment related care plan tasks exist
        care_plan = auth_user.care_plans.last()
        self.assertEqual(care_plan.care_plan_tasks.count(), 2)

        # Now confirm due dates are being set correctly
        cpt_eligiblity = CarePlanTask.get_one_from_care_plan(
            care_plan, filters={"title": "Run patient eligibility check"}
        )
        self.assertEqual(cpt_eligiblity.created_by, lucian_bot)

        # Create another appointment and confirm that only the task for
        # repeat appointments are created
        three_days_from_now = timezone.now() + timedelta(days=3)
        appt = None
        with self.captureOnCommitCallbacks(execute=True):
            appt = Appointment.objects.create(
                patient=auth_user,
                time_slot_type="appointment",
                start=three_days_from_now,
                description="Test test",
                elation_id=2,
                status="Scheduled",
                physician=self.physician,
            )

        self.assertEqual(care_plan.care_plan_tasks.count(), 3)
        self.assertFalse(None in [t.task.source_type for t in care_plan.care_plan_tasks.all()])

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_get_or_create_care_plan_of_type(self, mock_reminders):
        # create appointment slot
        person = PersonUserFactory()
        auth_user = person.user
        ten_days_from_now = timezone.now() + timedelta(days=10)
        appt = Appointment(
            time_slot_type="appointment_slot",
            start=ten_days_from_now,
            description="Test test",
            elation_id=1,
            status="Scheduled",
            physician=self.physician,
        )
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()

        luci_bot = self.lucian_bot

        careplan_type = CarePlanType.objects.get(uid="onboarding")

        # Attach the patient to the appointmet slot.
        appt.patient = auth_user
        with self.captureOnCommitCallbacks(execute=True):
            appt.save()
        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": auth_user.person.id,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": "Getting Started with Firefly"},
                }
            ],
        )
        care_plan = auth_user.care_plans.get(type__uid="onboarding")
        self.assertIsNotNone(care_plan)

        # create a second of the same care plan template
        CarePlan.objects.create(
            patient=auth_user,
            type=careplan_type,
            title="Getting Started with Firefly",
            created_by=luci_bot,
        )
        mock_reminders.send.assert_called_once_with(
            settings.BRAZE["NEW_CARE_PLAN_NOTIFICATION"],
            [
                {
                    "external_user_id": auth_user.person.id,
                    "send_to_existing_only": True,
                    "trigger_properties": {"care_plan_name": "Getting Started with Firefly"},
                }
            ],
        )
        # check that appointment related care plan tasks exist
        self.assertEqual(care_plan.care_plan_tasks.count(), 2)

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_get_or_create_care_plan_of_type_no_notifications(self, mock_reminders):
        person = PersonUserFactory()

        care_plan_type = CarePlanTypeFactory(uid=STAYING_HEALTHY_CARE_PLAN_UID)
        CarePlanTemplateFactory(type=care_plan_type)

        care_plan_template = CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID)
        care_plan_template.is_case_based_care_plan = True
        care_plan_template.save()

        get_or_create_care_plan_of_type(
            patient=person.user,
            care_plan_template=care_plan_template,
        )
        mock_reminders.send.assert_not_called()

    @patch("firefly.modules.care_plan.utils.reminders")
    def test_get_or_create_care_plan_of_type_for_notifications(self, mock_reminders):
        person = PersonUserFactory()

        care_plan_type = CarePlanTypeFactory(uid=STAYING_HEALTHY_CARE_PLAN_UID)
        CarePlanTemplateFactory(type=care_plan_type)

        care_plan_template = CarePlanTemplate.objects.get(type__uid=STAYING_HEALTHY_CARE_PLAN_UID)
        care_plan_template.is_case_based_care_plan = False
        care_plan_template.save()

        get_or_create_care_plan_of_type(
            patient=person.user,
            care_plan_template=care_plan_template,
        )
        mock_reminders.send.assert_called_once()

    def test_send_appointment_in_local_timezone(self, *args):
        CHICAGO_TIMEZONE = "America/Chicago"
        person = PersonUserFactory()
        auth_user = person.user
        UserDevice.objects.create(
            user_id=auth_user.pk,
            device_unique_id="uniqueDeviceID",
            device_type="Android 10",
            platform="iOS",
            biometric_capability="some biometric string",
            font_scaling="1.0",
            color_scheme="light",
            device_timezone=CHICAGO_TIMEZONE,
            os_version="2.4",
            app_version="0.0.0",
            device_token="RandomToken2",
            device_push_enabled="False",
        )
        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        with self.captureOnCommitCallbacks(execute=True):
            Appointment.objects.create(
                patient_id=auth_user.pk,
                time_slot_type="appointment_slot",
                start=ten_days_from_now,
                description="Test test",
                elation_id=1,
                status="Scheduled",
                physician=self.physician,
                reason=AppointmentReason.VIDEO,
                duration="00:15:00",
            )
        self.assertEqual(EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED).count(), 1)

    def test_send_appointment_in_local_timezone_with_multiple_devices(self, *args):
        # If multiple timezone info is obtained from devices
        # should use the earliest time
        LA_TIMEZONE = "America/Los_Angeles"
        CHICAGO_TIMEZONE = "America/Chicago"
        person = PersonUserFactory()
        auth_user = person.user
        UserDevice.objects.create(
            user_id=auth_user.pk,
            device_unique_id="uniqueDeviceID",
            device_type="Android 10",
            platform="iOS",
            biometric_capability="some biometric string",
            font_scaling="1.0",
            color_scheme="light",
            device_timezone=CHICAGO_TIMEZONE,
            os_version="2.4",
            app_version="0.0.0",
            device_token="RandomToken2",
            device_push_enabled="False",
        )
        UserDevice.objects.create(
            user_id=auth_user.pk,
            device_unique_id="uniqueDeviceID2",
            device_type="Android 10",
            platform="iOS",
            biometric_capability="some biometric string",
            font_scaling="1.0",
            color_scheme="light",
            device_timezone=LA_TIMEZONE,
            os_version="2.4",
            app_version="0.0.0",
            device_token="RandomToken2",
            device_push_enabled="False",
        )
        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        with self.captureOnCommitCallbacks(execute=True):
            Appointment.objects.create(
                patient_id=auth_user.pk,
                time_slot_type="appointment_slot",
                start=ten_days_from_now,
                description="Test test",
                elation_id=1,
                status="Scheduled",
                physician=self.physician,
                reason=AppointmentReason.VIDEO,
                duration="00:15:00",
            )
        self.assertEqual(EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED).count(), 1)

    def test_send_appointment_in_local_timezone_with_no_timezone_from_device(self, *args):
        # If no timezone info can be obtained from devices
        # should fallback to Newyork timezone
        person = PersonUserFactory()
        auth_user = person.user
        UserDevice.objects.create(
            user_id=auth_user.pk,
            device_unique_id="uniqueDeviceID",
            device_type="Android 10",
            platform="iOS",
            biometric_capability="some biometric string",
            font_scaling="1.0",
            color_scheme="light",
            os_version="2.4",
            app_version="0.0.0",
            device_token="RandomToken2",
            device_push_enabled="False",
        )
        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        with self.captureOnCommitCallbacks(execute=True):
            Appointment.objects.create(
                patient_id=auth_user.pk,
                time_slot_type="appointment_slot",
                start=ten_days_from_now,
                description="Test test",
                elation_id=1,
                status="Scheduled",
                physician=self.physician,
                reason=AppointmentReason.VIDEO,
                duration="00:15:00",
            )
        self.assertEqual(EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED).count(), 1)

    def test_send_appointment_in_local_timezone_with_tz_from_person(self, *args):
        # If no timezone info can be obtained from devices
        # should fallback to default timezone
        person = PersonUserFactory()
        auth_user = person.user
        UserDevice.objects.create(
            user_id=auth_user.pk,
            device_unique_id="uniqueDeviceID",
            device_type="Android 10",
            platform="iOS",
            biometric_capability="some biometric string",
            font_scaling="1.0",
            color_scheme="light",
            os_version="2.4",
            app_version="0.0.0",
            device_token="RandomToken2",
            device_push_enabled="False",
        )

        # create appointment
        ten_days_from_now = timezone.now() + timedelta(days=10)
        with self.captureOnCommitCallbacks(execute=True):
            Appointment.objects.create(
                patient_id=auth_user.pk,
                time_slot_type="appointment_slot",
                start=ten_days_from_now,
                description="Test test",
                elation_id=1,
                status="Scheduled",
                physician=self.physician,
                reason=AppointmentReason.VIDEO,
                duration="00:15:00",
            )

        self.assertEqual(EventLog.objects.filter(type=EventTypeCodes.APPOINTMENT_SCHEDULED).count(), 1)
