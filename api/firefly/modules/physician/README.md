# Physician Management

## Motivation

The physician module manages healthcare provider information, licensing, and state-based practice restrictions within the Firefly platform. It integrates with Elation for provider data synchronization while maintaining internal state licensing controls, care team assignments, and attribution tracking for clinical operations.

## Business Context

Physician management is essential for Firefly's clinical operations, handling:
- Provider licensing and state practice restrictions for regulatory compliance
- Integration with Elation EMR for provider data synchronization
- Care team assignments and patient-provider relationships
- Attribution tracking for insurance and billing purposes
- State-based filtering for patient access and task assignment
- Calendar integration and scheduling coordination

## Core Concepts

### Provider Hierarchy
- **Physician**: Licensed healthcare providers synced from El<PERSON> (MDs, NPs, Behavioral Health Specialists, Health Guides)
- **Provider**: Broader category including all Lucian users (physicians, practice admin, ops, onboarding guides)
- **Practicing States**: State licensing information for regulatory compliance
- **License Pending States**: States where licensing is in progress

### State-Based Access Control
- **Licensed States**: States where physician can actively practice and see patients
- **Administrative Override**: Admin users can access all states regardless of licensing
- **Patient Filtering**: Automatic filtering based on patient location and provider licensing
- **Task Assignment**: State-based task routing and provider availability

## Technical Implementation

### Core Models

**Physician**: Provider information and licensing management
- Elation integration with automatic synchronization
- NPI (National Provider Identifier) tracking for billing and attribution
- Specialty and practice association for care team organization
- Calendar URL integration for scheduling systems

**State Relationships**: Many-to-many licensing tracking
- `practicing_states`: Current active licenses for patient care
- `license_pending_in_states`: States with pending licensing applications
- Default state assignment (Massachusetts) for new providers

**Elation Integration**: Bidirectional synchronization
- Automatic provider data sync from Elation EMR
- Practice association and physician assignment
- User ID mapping for authentication and access control

### State Filtering Logic

**Frontend State Management**: Automatic state filtering on application load
```javascript
const filteredStates = !isAdmin(providers, userId)
  ? practicingStates.map(state => state.abbreviation)
  : [] // Admin sees all states
```

**Patient Access Control**: State-based patient visibility
- Physicians only see patients in their licensed states
- Administrative users have unrestricted access
- Search functionality can access out-of-state patients with warnings

**Task and Message Filtering**: Provider-specific content filtering
- Tasks filtered by assignee's practicing states, not patient location
- Message routing based on provider licensing and availability
- Care team assignments respect state licensing requirements

## Business Logic

### Attribution and NPI Management

**Attributable Provider Tracking**: NPI-based attribution for insurance and billing
- Cached NPI list for performance optimization
- Role-based filtering (MDs and NPs only for attribution)
- Integration with external portals for member attribution
- Support for non-physician Firefly NPIs through configuration

**Provider Role Management**: Role-based access and functionality
- MD and NP roles for primary attribution
- Behavioral Health Specialists for mental health services
- Health Guides for care coordination and support
- Practice administrators for operational oversight

### Care Team Integration

**Patient Assignment Logic**: State-based care team formation
- Primary physician assignment based on patient state and provider licensing
- Care team template matching with state licensing validation
- Panel size tracking for provider capacity management
- Automatic care team updates based on licensing changes

**Provider Availability**: Dynamic provider selection
- In-state providers for direct patient care
- Covering team for out-of-state or unavailable providers
- Administrative override for emergency or special circumstances
- Integration with scheduling and appointment systems

### Elation Synchronization

**Data Sync Process**: Bidirectional provider data management
- Automatic physician data sync from Elation EMR
- Practice association updates and physician assignments
- User ID mapping for authentication integration
- NPI and specialty information synchronization

**Change Detection**: Provider data change management
- Automatic NPI change detection and downstream updates
- Integration with change data capture for external systems
- Provider status updates and deactivation handling

## Configuration

### State Licensing Management

**Practicing States Configuration**:
- Many-to-many relationship with State model
- Default Massachusetts assignment for new providers
- License pending state tracking for compliance
- Administrative permissions for state licensing updates

**Permission Management**:
- `STATE_LICENSING_PERMISSIONS_GROUP` for licensing updates
- Provider-specific practicing state modification rights
- Administrative override capabilities for emergency access

### Elation Integration Setup

**Provider Synchronization**:
- Elation physician endpoint integration
- Field mapping for provider attributes (name, NPI, specialty)
- Practice association and user ID synchronization
- Automatic relationship updates for practice assignments

### Frontend Integration

**State Filtering Components**:
- Automatic state filtering on application load
- Provider-specific patient list filtering
- Task assignment based on provider licensing
- Warning systems for out-of-state patient access

## API Endpoints

- `GET /physician/` - List active physicians with provider details
- `GET /physician/<id>/` - Retrieve specific physician information
- `GET/PUT /physician/<id>/practicing-states/` - Manage practicing states
- Integration with provider and care team endpoints
