from typing import List

from django.db import models
from django.db.models import Q

from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.practice.models import Practice
from firefly.modules.states.models import State


def get_default_state():
    default_state, _ = State.objects.get_or_create(
        abbreviation="MA", defaults={"name": "Massachusetts", "can_service": True}
    )
    return [default_state]


class Physician(SaveHandlersMixin, BaseModelV3):
    """
    API Reference - https://docs.elationhealth.com/reference#the-physician-object
    """

    elation_id = models.BigIntegerField(unique=True)

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    practice = models.ForeignKey(Practice, related_name="physicians", null=True, on_delete=models.SET_NULL)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over Char<PERSON>ield
    first_name = models.CharField(max_length=255)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(max_length=255)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    specialty = models.CharField(max_length=255, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npi = models.CharField(max_length=255, blank=True)  # noqa: TID251

    practicing_states = BaseModelV3ManyToManyField(
        State,
        related_name="state_providers",
        blank=True,
        default=get_default_state,
        through="PhysicianPracticingStates",
    )

    license_pending_in_states = BaseModelV3ManyToManyField(
        State,
        related_name="license_pending_state_providers",
        blank=True,
        through="PhysicianLicensePendingInStates",
    )

    elation_user_id = models.BigIntegerField(unique=False, null=True, blank=True)

    needs_lucian_access = models.BooleanField(default=True, null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    calendar_url = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    @property
    def practicing_states_list(self):
        return "\n".join([p.abbreviation for p in self.practicing_states.all()])

    @property
    def license_pending_in_states_list(self):
        return "\n".join([state.abbreviation for state in self.license_pending_in_states.all()])

    @property
    def name_with_credentials(self):
        """Get name with credentials, e.g. 'Jane Doe, MD'."""
        name = f"{self.first_name.title()} {self.last_name.title()}"
        if self.provider.title == "":
            return name
        else:
            return f"{name}, {self.provider.title}"

    class Meta(BaseModelV3.Meta):
        db_table = "physicians"

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_provider_change

        if not changed("id") and changed("npi"):
            zus_handle_provider_change.send(user_id=self.provider.user.id)


class PhysicianPracticingStates(BaseModelV3):
    physician = models.ForeignKey(
        Physician,
        related_name="physician_practicing_states",
        on_delete=models.CASCADE,
    )
    state = models.ForeignKey(
        State,
        related_name="physician_practicing_states",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "physicians_practicing_states"
        verbose_name_plural = "Physician Practicing States"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["physician", "state"], condition=Q(deleted=None), name="physicians_practicing_states_uniq"
            )
        ]


class PhysicianLicensePendingInStates(BaseModelV3):
    physician = models.ForeignKey(
        Physician,
        related_name="physician_license_pending_states",
        on_delete=models.CASCADE,
    )
    state = models.ForeignKey(
        State,
        related_name="physician_license_pending_states",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "physicians_license_pending_in_states"
        verbose_name_plural = "Physician License Pending In States"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["physician", "state"],
                condition=Q(deleted=None),
                name="physicians_license_pending_in_states_uniq",
            )
        ]
