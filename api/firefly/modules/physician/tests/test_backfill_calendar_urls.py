import mock
from django.core import management

from firefly.core.services.google_calendar.client import GoogleCalendarClient
from firefly.core.services.google_calendar.factories import CalendarFactory
from firefly.core.services.google_calendar.types import Calendar
from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.physician.models import Physician


@mock.patch.object(GoogleCalendarClient, "_get_service")
@mock.patch.object(GoogleCalendarClient, "create_calendar")
class BackfillCalendarURLestCase(FireflyTestCase):
    def test_backfill_calendar_url(self, create_calendar_mock, _get_service_mock):
        physician_without_calendar_url: Physician = Physician.objects.first()
        self.assertIsNone(physician_without_calendar_url.calendar_url)
        calendar: Calendar = CalendarFactory.create()
        # three physicians are created in the base test case
        create_calendar_mock.side_effect = [calendar, calendar, calendar]
        management.call_command(
            "backfill_calendar_urls",
            user=self.provider,
            dry_run_off=True,
        )
        physician_without_calendar_url.refresh_from_db()
        self.assertEqual(physician_without_calendar_url.calendar_url, calendar["id"])

    def test_backfill_calendar_url_with_dry_run(self, create_calendar_mock, _get_service_mock):
        physician_without_calendar_url: Physician = Physician.objects.first()
        self.assertIsNone(physician_without_calendar_url.calendar_url)
        calendar: Calendar = CalendarFactory.create()
        # three physicians are created in the base test case
        create_calendar_mock.side_effect = [calendar, calendar, calendar]
        management.call_command(
            "backfill_calendar_urls",
            user=self.provider,
            dry_run_off=False,
        )
        physician_without_calendar_url.refresh_from_db()
        self.assertIsNone(physician_without_calendar_url.calendar_url)

    def test_backfill_calendar_url_with_physician_param(self, create_calendar_mock, _get_service_mock):
        physician_without_calendar_url: Physician = Physician.objects.first()
        another_physician_without_calendar_url: Physician = Physician.objects.last()
        self.assertIsNone(physician_without_calendar_url.calendar_url)
        self.assertIsNone(another_physician_without_calendar_url.calendar_url)
        calendar: Calendar = CalendarFactory.create()
        # three physicians are created in the base test case
        create_calendar_mock.side_effect = [calendar, calendar, calendar]
        management.call_command(
            "backfill_calendar_urls",
            user=self.provider,
            dry_run_off=True,
            physician_id=physician_without_calendar_url.pk,
        )
        physician_without_calendar_url.refresh_from_db()
        self.assertEqual(physician_without_calendar_url.calendar_url, calendar["id"])
        self.assertIsNone(another_physician_without_calendar_url.calendar_url)
