from django.conf import settings
from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3


class TextMacro(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over <PERSON><PERSON><PERSON>ield
    name = models.CharField(max_length=63)  # noqa: TID251
    template = models.TextField()

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="text_macros",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
