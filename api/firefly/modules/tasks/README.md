# Tasks

The Tasks module manages work items, assignments, and workflow tracking across clinical and operational teams. It provides task creation, assignment, priority management, and integration with care plans and case management.

## Overview

This module handles all task-related functionality including task templates, assignment logic, priority-based routing, and integration with Slack notifications for urgent tasks. Tasks can be standalone work items or part of larger care plan workflows.

## Business Logic

The task management workflow works as follows:

1. **Task Creation**: Tasks created from templates, cases, or direct assignment
2. **Priority Assessment**: Tasks assigned priority levels affecting routing and notifications
3. **Team Assignment**: Tasks routed to appropriate teams or individuals
4. **Progress Tracking**: Task completion status and metadata management
5. **Notification Triggers**: Urgent tasks trigger Slack alerts and escalation

## Core Task Types

### Standard Tasks
Regular work items with clear completion criteria:
- **Assignment**: Direct assignment to users or groups
- **Due Dates**: Time-based completion requirements
- **Priority Levels**: Normal, high, urgent priority classification
- **Status Tracking**: Open, in progress, completed states

### Virtual Tasks
**Legacy Pattern - Avoid for New Development**

Virtual Tasks (`source_type="virtual"`) track multi-step workflows:
- **Completion Tracking**: `is_complete` tracks member-facing work completion
- **Metadata State**: Additional system state in `metadata` field
- **Processing Status**: `is_processing` flag for backend work completion
- **Complex Workflows**: Multi-party completion requirements

#### Virtual Task Example: Provider of Choice (POC)
```python
# Member completes POC form
virtual_task.is_complete = True  # Member work done

# But task remains visible until insurance verification
virtual_task.metadata = {"is_processing": True}

# Once ops team completes insurance work
virtual_task.metadata = {"is_processing": False}  # Task finally considered done
```

#### Deprecation Notice
Virtual Tasks are legacy patterns. For new multi-step workflows, use:
- **Case Management**: Structured case workflows
- **State Machines**: StateMachineDefinition for complex flows
- **Direct Models**: Reference specific objects rather than generic tasks

## Task Templates and Assignment

### TaskCollection System
Task templates provide factory-pattern task creation:

#### Core Components
- **TaskCollectionTask**: Individual task template with assignment and timing rules
- **TaskCollection**: Groups multiple task templates for batch creation
- **Template Application**: `create_care_plan_tasks_from_templates` applies templates to users

#### Template Configuration
```python
# Task template defines:
task_template = TaskCollectionTask(
    assignee_group=clinical_team,
    due_date_offset=timedelta(days=7),
    priority=TaskPriority.NORMAL,
    form=health_assessment_form
)

# Grouped in collection
task_collection = TaskCollection(
    name="Onboarding Tasks",
    tasks=[task_template, ...]
)
```

### Automated Task Assignment
Tasks are often assigned automatically rather than explicitly in code:

#### Event-Driven Assignment
```python
# Template connected to event and form
# When event occurs, task assigned to patient
# See: firefly.modules.events.create_initial_care_plan

# Example: Health Assessment task assignment
event_trigger → task_template → automatic_assignment
```

#### Template-Based Creation
If you don't see explicit task assignment in code, check for:
- **TaskCollection templates** being applied
- **Event-driven triggers** creating tasks
- **Care plan workflows** generating task sets

## Care Plan Integration

### TaskCollection + CarePlan Association
Task collections can be linked to clinical care plans:
- **CarePlanTemplate**: Clinical care plan definition
- **TaskCollection**: Associated task templates
- **Workflow**: Care plan creation triggers task assignment

### Integration Benefits
- **Clinical Context**: Tasks linked to specific care plans
- **Coordinated Care**: Multiple tasks for comprehensive care
- **Progress Tracking**: Care plan completion via task completion

*See Care Plan module README for detailed care plan information.*

## Task Filtering and Routing

### Provider State-Based Filtering
Task filtering logic based on provider practicing states:

#### Frontend Implementation
Location: `src/components/Tasks/TasksSidebar`
- **renderInStateProviders**: Clinicians with practicing state restrictions
- **renderOtherProviders**: Providers without state restrictions

#### Filtering Logic
```python
# Tasks filtered by provider practicing state, not patient state
if provider.practicing_states:
    # Show tasks for providers in same state as default
    # Unless provider defaulted to "All" (shows all physicians)
    filtered_tasks = tasks.filter(assignee__practicing_states=default_state)
else:
    # "Other" category: providers without Physician objects
    # Not bound by practicing_states restrictions
    filtered_tasks = tasks.filter(assignee__physician__isnull=True)
```

#### Categories
- **In-State Providers**: Clinicians with practicing state matching patient location
- **All Physicians**: When provider selects "All" option
- **Other Providers**: Non-physician staff without state restrictions

## Priority Management and Alerts

### Priority Levels
- **Normal**: Standard workflow tasks
- **High**: Elevated priority, faster response expected
- **Urgent (`!!`)**: Immediate attention required, triggers Slack alerts

### Urgent Task Routing
```python
# Urgent task assignment triggers Slack notifications
if task.priority == TaskPriority.URGENT:
    if task.owner_group == "Clinician Group":
        # Tag NP, MD, HG from care team
        notify_care_team(task)
    else:
        # Tag specific assignee (unless self-assigned)
        notify_assignee(task)
```

### Alert Triggers
Urgent tasks include:
- **Chat Messages**: Urgent messages from chat window
- **Form Responses**: Positive suicide ideation indicators
- **Clinical Alerts**: High-priority clinical situations

## Usage Examples

### Creating Task from Template
```python
from firefly.modules.tasks.utils import create_care_plan_tasks_from_templates

# Apply task collection to users
create_care_plan_tasks_from_templates(
    task_collection=onboarding_tasks,
    users=[patient_user],
    care_plan=patient_care_plan
)
```

### Manual Task Creation
```python
from firefly.modules.tasks.models import Task

# Create urgent task with Slack notification
urgent_task = Task.objects.create(
    title="Critical Lab Review",
    priority=TaskPriority.URGENT,
    owner_group=clinical_group,
    person=patient,
    due_date=timezone.now() + timedelta(hours=2)
)
# Slack alert sent automatically via post_save signal
```

### Virtual Task Management
```python
# Legacy virtual task pattern (avoid for new development)
virtual_task = Task.objects.create(
    source_type="virtual",
    is_complete=False,
    metadata={"is_processing": True}
)

# Member completes their part
virtual_task.is_complete = True
virtual_task.save()

# System completes backend processing
virtual_task.metadata = {"is_processing": False}
virtual_task.save()  # Now considered fully complete
```

## Integration Points

### Slack Notifications
- **Urgent Tasks**: Automatic Slack alerts for high-priority tasks
- **Care Team Routing**: Notifications to appropriate clinical staff
- **Response Tracking**: Timestamps and escalation management

### Case Management
- **Case-Generated Tasks**: Tasks created from case workflows
- **Task-Case Linking**: Tasks associated with specific cases
- **Workflow Integration**: Tasks as part of larger case resolution

### Care Plans
- **Template Association**: Task collections linked to care plan templates
- **Progress Tracking**: Care plan completion via task completion
- **Clinical Workflows**: Tasks as part of structured care delivery
