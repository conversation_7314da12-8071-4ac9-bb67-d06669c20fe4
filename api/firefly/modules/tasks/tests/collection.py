import datetime
import logging
import uuid
from unittest import mock

from django.conf import settings
from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core import management
from freezegun import freeze_time

from firefly.core.feature.testutils import override_switch
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP, DUPLICATE_ACCOUNT, NP_ROLE, PRACTICE_ADMIN_ROLE
from firefly.core.user.factories import PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import AssigneeGroup, AssigneeGroupUser
from firefly.core.user.utils import create_update_assignee_group_from_user
from firefly.modules.care_plan.factories import CarePlanFactory, CarePlanTemplateFactory
from firefly.modules.care_plan.models import CarePlan, CarePlanType
from firefly.modules.care_teams.models import CareTeamTemplate
from firefly.modules.cases.constants import CaseCategoryUniqueIndentifiers
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.cases.utils import auto_close_case
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.factories import FormFactory
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.states.factories import StateFactory
from firefly.modules.tasks.collections import (
    add_onboarding_journey_virtual_tasks_to_patient,
    create_care_plan_tasks_from_templates,
    create_case_for_no_care_team,
    create_tasks_from_task_template,
    create_tasks_from_templates,
    create_tasks_if_not_exist,
    handle_care_team_availability,
    task_template_to_task,
)
from firefly.modules.tasks.constants import SWITCH_NO_CARE_TEAM_NOTIFICATION, TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.factories import TaskCollectionTaskFactory
from firefly.modules.tasks.models import SOURCE_TYPES, Task, TaskCollection, TaskCollectionTask, TaskRelation
from firefly.modules.tasks.tests.util import assign_to_tenant_without_task_feature_access

from ...care_teams.factories import CareTeamTemplateFactory

logger = logging.getLogger(__name__)


class TaskCollectionTaskTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.tasks.collections.create_case_for_no_care_team")
    def test_handle_care_team_availability(self, mock_create_case_for_no_care_team):
        CareTeamTemplate.objects.all().delete()
        mock_create_case_for_no_care_team.reset_mock()
        # With an available care team
        state = StateFactory()
        self.patient.person.insurance_info.state = state.abbreviation
        self.patient.person.insurance_info.save()
        care_team_template = CareTeamTemplateFactory()
        primary_physician = ProviderDetailFactory(is_taking_new_patients=True).physician
        care_team_template.primary_physician = primary_physician
        care_team_template.save()
        primary_physician.practicing_states.add(state.id)
        self.patient.person.insurance_info.state = state.abbreviation
        self.patient.person.insurance_info.save()
        self.patient.person.insurance_info.refresh_from_db()
        # No need to create a Case
        handle_care_team_availability(self.patient)
        mock_create_case_for_no_care_team.assert_not_called()
        # When the provider is not accepting new patients
        # the Case should be created, even though they are licensed correctly
        primary_physician.provider.is_taking_new_patients = False
        primary_physician.provider.save()
        handle_care_team_availability(self.patient)
        mock_create_case_for_no_care_team.assert_called_once()

    @mock.patch("firefly.modules.cases.models.Case.meet_your_care_team_notification")
    def test_case_for_no_care_team(self, mock_meet_your_care_team_notification):
        STATE_MACHINE_CONTENT = {
            "initial_state": "New",
            "state_with_categories": [
                {
                    "category": "not_started",
                    "due_date": [{"days": 0, "use_business_days": True}],
                    "state": {"ignore_invalid_triggers": None, "name": "New"},
                },
                {"category": "in_progress", "state": {"ignore_invalid_triggers": None, "name": "In Progress"}},
                {"category": "deferred", "state": {"ignore_invalid_triggers": None, "name": "Will Not Do"}},
                {
                    "category": "complete",
                    "state": {
                        "ignore_invalid_triggers": None,
                        "on_enter": "meet_your_care_team_notification",
                        "name": "Done",
                    },
                },
            ],
            "transitions": [
                {"dest": "Done", "source": "*", "system_action": "system_close", "trigger": "auto_close"},
                {"dest": "Done", "source": "*", "trigger": "Done"},
                {"dest": "Will Not Do", "source": ["New", "In Progress", "Done"], "trigger": "Will Not Do"},
                {"dest": "Will Not Do", "source": "*", "system_action": "system_deferred", "trigger": "deferred"},
                {"dest": "In Progress", "source": ["New", "Done"], "trigger": "In Progress"},
                {"dest": "New", "source": "*", "system_action": "system_reopen", "trigger": "reopened"},
            ],
        }
        state_machine, _ = StateMachineDefinition.objects.get_or_create(
            content=STATE_MACHINE_CONTENT, title="No Care Team Notification"
        )
        task_collection = TaskCollection.objects.get(title="Patient Onboarding")
        CaseCategory.objects.get_or_create(
            unique_key=CaseCategoryUniqueIndentifiers.NO_CARE_TEAMS,
            title="No Care Teams Available",
            task_collection=task_collection,
            description="Assign some care team manually to the patient",
            state_machine_definition=state_machine,
        )
        care_coordinator_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["care_coordinator"]
        assignee_object, _ = AssigneeGroup.objects.get_or_create(
            unique_key=care_coordinator_unique_key, name="Care Coordinator"
        )
        AssigneeGroupUser.objects.get_or_create(group=assignee_object, user=self.provider)
        TaskCollectionTask.objects.get_or_create(
            task_collection=task_collection, uid=TaskCollectionTaskUniqueIdentifiers.NO_CARE_TEAM, title="Task title"
        )
        # For the first time, it creates a case with that person
        no_care_team_case = create_case_for_no_care_team(self.patient)
        self.assertIsNotNone(no_care_team_case)
        response = self.provider_client.get(f"/providers/me/cases/{no_care_team_case.id}")
        response_json = response.json()
        self.assertEqual(response_json["description"], "Assign some care team manually to the patient")
        self.assertEqual(len(response_json["tasks"]), 1)
        assigned_task = response_json["tasks"][0]
        self.assertEqual(assigned_task["template"], TaskCollectionTaskUniqueIdentifiers.NO_CARE_TEAM)
        self.assertEqual(assigned_task["patient"], self.patient.pk)

        # For the second time, if we are calling the same function again, case shouldn't be created
        case = create_case_for_no_care_team(self.patient)
        self.assertIsNone(case)

        # close all the task attached to no care team case
        tasks = Task.objects.filter(
            relations__content_type=ContentType.objects.get_for_model(Case),
            relations__object_id=no_care_team_case.id,
            is_complete=False,
        )
        tasks.update(is_complete=True)
        # Close the no care team case and make sure side effect is triggered.
        case_closed = auto_close_case(id=no_care_team_case.id)
        self.assertTrue(case_closed)
        mock_meet_your_care_team_notification.assert_called_once()

        # Case 3: When there's no case category, return none
        case_category = CaseCategory.objects.get(unique_key=CaseCategoryUniqueIndentifiers.NO_CARE_TEAMS)
        # delete linked cases to case category, before deleting case category
        Case.objects.filter(category=case_category).delete()
        case_category.delete()
        case = create_case_for_no_care_team(self.patient)
        self.assertIsNone(case)

    @override_switch(SWITCH_NO_CARE_TEAM_NOTIFICATION, active=True)
    @mock.patch("firefly.modules.cases.case_side_effects.reminders")
    def test_no_care_team_notification(self, mock_reminders):
        task_templates = TaskCollectionTask.objects.filter(uid=TaskCollectionTaskUniqueIdentifiers.MEET_YOUR_CARE_TEAM)
        person = PersonUserFactory()
        create_tasks_if_not_exist(person.user, task_templates)
        case = CaseFactory(person=person)
        case.meet_your_care_team_notification()
        mock_reminders.send.assert_called_with(
            settings.BRAZE["MEET_YOUR_CARE_TEAM_PUSH_NOTIFICATION"],
            [
                {
                    "external_user_id": person.id,
                    "send_to_existing_only": True,
                }
            ],
        )

    @mock.patch("firefly.modules.tasks.models.TaskCollectionTask.get_due_date")
    def test_create_care_plan_tasks_from_templates(self, mock_get_due_date):
        task_collection = TaskCollection.objects.create(
            title=uuid.uuid1(),
        )
        mock_get_due_date.return_value = datetime.datetime.now()
        care_plan_type = CarePlanType.objects.create(description="test")
        form, _ = Form.objects.get_or_create(uid=FormUID.SEGMENTATION_CAPTURE)
        task_template_1 = TaskCollectionTask.objects.create(
            task_collection=task_collection, form=form, title="Test title 1"
        )
        task_template_2 = TaskCollectionTask.objects.create(
            task_collection=task_collection, form=form, title="Test title 2"
        )
        # Given that the patient doesn't already have this form / task assigned
        Task.objects.filter(patient=self.patient).delete()
        FormSubmission.objects.filter(user=self.patient, form=form).delete()
        create_care_plan_tasks_from_templates(
            task_collection=task_collection,
            users=[self.patient],
            care_plan_type=care_plan_type,
        )
        # A Task, FormSubmission are created for the patient
        self.assertTrue(FormSubmission.objects.filter(user=self.patient, form=form).exists())
        self.assertTrue(Task.objects.filter(patient=self.patient, autocreated_from=task_template_1).exists())
        self.assertTrue(Task.objects.filter(patient=self.patient, autocreated_from=task_template_2).exists())
        # Test task assignment with overrides
        # Exclude the 2nd task
        patient = PersonUserFactory().user
        Task.objects.filter(patient=patient).delete()
        FormSubmission.objects.filter(user=patient, form=form).delete()
        create_care_plan_tasks_from_templates(
            task_collection=task_collection,
            users=[patient],
            care_plan_type=care_plan_type,
            task_override_data={task_template_1.pk: {}},
        )
        self.assertTrue(FormSubmission.objects.filter(user=patient, form=form).exists())
        self.assertTrue(Task.objects.filter(patient=patient, autocreated_from=task_template_1).exists())
        self.assertFalse(Task.objects.filter(patient=patient, autocreated_from=task_template_2).exists())
        # Exclude all tasks
        patient = PersonUserFactory().user
        Task.objects.filter(patient=patient).delete()
        FormSubmission.objects.filter(user=patient, form=form).delete()
        create_care_plan_tasks_from_templates(
            task_collection=task_collection,
            users=[patient],
            care_plan_type=care_plan_type,
            task_override_data={},
        )
        self.assertFalse(FormSubmission.objects.filter(user=patient, form=form).exists())
        self.assertFalse(Task.objects.filter(patient=patient, autocreated_from=task_template_1).exists())
        self.assertFalse(Task.objects.filter(patient=patient, autocreated_from=task_template_2).exists())

    def test_add_onboarding_journey_virtual_tasks_to_patient(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        task_collection, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")
        task_template = TaskCollectionTask.objects.create(
            title="Test task collection task",
            task_collection=task_collection,
            days_offset=0,
        )
        add_onboarding_journey_virtual_tasks_to_patient(self.patient)
        self.assertTrue(Task.objects.filter(patient=self.patient, autocreated_from=task_template).exists())
        add_onboarding_journey_virtual_tasks_to_patient(self.patient)
        self.assertEqual(len(Task.objects.filter(patient=self.patient, autocreated_from=task_template)), 1)

    def test_add_onboarding_journey_virtual_tasks_to_benefit_member(self):
        # If the person is only in BENEFIT, they should not assigned any onboarding template task.
        person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.BENEFIT)
        task_collection, _ = TaskCollection.objects.get_or_create(title="Onboarding Journey")
        task_template_1 = TaskCollectionTask.objects.create(
            title="Test task collection task",
            task_collection=task_collection,
            days_offset=0,
            uid="Test uid",
        )
        task_template_2, _ = TaskCollectionTask.objects.get_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR,
        )
        task_template_2.days_offset = 0
        task_template_2.save(update_fields=["days_offset"])

        add_onboarding_journey_virtual_tasks_to_patient(person.user)
        self.assertFalse(Task.objects.filter(patient=person.user, autocreated_from=task_template_1).exists())
        self.assertFalse(Task.objects.filter(patient=person.user, autocreated_from=task_template_2).exists())

        # If the person is both in PRIMARY_CARE and BENEFIT, they should be assigned all Onboarding Journey tasks
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        add_onboarding_journey_virtual_tasks_to_patient(person.user)
        self.assertTrue(Task.objects.filter(patient=person.user, autocreated_from=task_template_1).exists())
        self.assertTrue(Task.objects.filter(patient=person.user, autocreated_from=task_template_2).exists())
        add_onboarding_journey_virtual_tasks_to_patient(person.user)
        self.assertEqual(len(Task.objects.filter(patient=person.user, autocreated_from=task_template_2)), 1)

    def test_get_task_collection_tasks(self):
        task_collection = TaskCollection.objects.create(title=uuid.uuid1())
        TaskCollectionTask.objects.create(
            title="Test task collection task 1",
            task_collection=task_collection,
            collection_order=0,
            days_offset=0,
            source_type=SOURCE_TYPES["virtual"],
        )
        TaskCollectionTask.objects.create(
            title="Test task collection task 2",
            task_collection=task_collection,
            collection_order=1,
            days_offset=0,
            source_type=SOURCE_TYPES["formsubmission"],
        )

        def get_response():
            return self.provider_client.get("/task/templates/")

        response = get_response()
        self.assertEqual(response.status_code, 200)
        # 7 task templates are created in test_cases.py setup, so total 9.
        self.assertEqual(len(response.json()), 9)
        response = self.provider_client.get(f"/task/templates/?source_type={SOURCE_TYPES['virtual']}")
        self.assertEqual(response.status_code, 200)
        # 4 task templates are created in test_cases.py setup, so total 5.
        self.assertEqual(len(response.json()), 5)

        # Test Tenant permissions
        assign_to_tenant_without_task_feature_access(self.provider.providerdetail)
        self.assertEqual(get_response().status_code, 403)

    def test_assign_task_from_template(self):
        task_collection = TaskCollection.objects.create(title=uuid.uuid1())
        template = TaskCollectionTask.objects.create(
            title="Test task collection task 1",
            task_collection=task_collection,
            collection_order=0,
            days_offset=0,
            source_type=SOURCE_TYPES["virtual"],
        )

        def get_response():
            return self.provider_client.post(f"/task/templates/{template.pk}/patients/{self.patient.pk}/")

        response = get_response()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["title"], template.title)
        self.assertTrue(self.patient.person.tasks.filter(autocreated_from=template).exists())

        # Test Tenant permissions
        assign_to_tenant_without_task_feature_access(self.provider.providerdetail)
        self.assertEqual(get_response().status_code, 403)

    def test_get_real_assignee_with_assignee_role(self):
        # Given a task template configured to be assigned to an NP
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        np_group, _ = Group.objects.get_or_create(name=NP_ROLE)
        task = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False)
        # Given the patient has an NP in their care team
        np_provider_on_care_team = ProviderDetailFactory.create()
        np_group.user_set.add(np_provider_on_care_team.user)
        self.patient.person.care_team.add(np_provider_on_care_team)
        self.assertEqual(task.get_real_assignee(patient=self.patient), np_provider_on_care_team.user.assignee_group)
        # Given a task template configured with a non-care-team group
        pa_provider = ProviderDetailFactory.create()
        pa_group, _ = Group.objects.get_or_create(name=PRACTICE_ADMIN_ROLE)
        pa_group.user_set.add(pa_provider.user)
        task.assignee_role = pa_group
        task.save()
        self.assertEqual(task.get_real_assignee(patient=self.patient), pa_provider.user.assignee_group)

    def test_get_real_assignee_group_with_assignee_role(self):
        # Given a task template configured to be assigned to an NP
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        np_group, _ = Group.objects.get_or_create(name=NP_ROLE)
        task = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False)
        # Given the patient has an NP in their care team
        np_provider_on_care_team = ProviderDetailFactory.create()
        create_update_assignee_group_from_user(np_provider_on_care_team.user)
        np_group.user_set.add(np_provider_on_care_team.user)
        self.patient.person.care_team.add(np_provider_on_care_team)
        self.assertEqual(
            task.get_real_assignee(patient=self.patient),
            np_provider_on_care_team.user.assignee_group,
        )
        # Given a task template configured with a non-care-team group
        pa_provider = ProviderDetailFactory.create()
        pa_group, _ = Group.objects.get_or_create(name=PRACTICE_ADMIN_ROLE)
        pa_group.user_set.add(pa_provider.user)
        create_update_assignee_group_from_user(pa_provider.user)
        task.assignee_role = pa_group
        task.save()
        self.assertEqual(task.get_real_assignee(patient=self.patient), pa_provider.user.assignee_group)

    def test_get_due_date(self):
        # Test no date configuration
        task_template = TaskCollectionTaskFactory(
            fixed_date=None, days_offset=None, event_date_property=None, fixed_month_day=None
        )
        self.assertEqual(task_template.get_due_date(), None)
        # Test fixed_date
        fixed_date = datetime.datetime.strptime("2021-01-11", "%Y-%m-%d").replace(tzinfo=datetime.timezone.utc)
        task_template = TaskCollectionTaskFactory(fixed_date=fixed_date)
        self.assertEqual(task_template.get_due_date(), fixed_date)
        # Test fixed_month_day
        month = 11
        day = 24
        year = 2021
        fixed_month_day = datetime.datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d").replace(
            tzinfo=datetime.timezone.utc
        )
        task_template = TaskCollectionTaskFactory(fixed_date=None, fixed_month_day=fixed_month_day)
        # Expect that, whether or not the month/day within this year has already passed
        # we always choose the current year's instance of that month/day
        with freeze_time(f"{year - 1}-{month - 1}-{day - 1} 13:00:00"):
            date = task_template.get_due_date()
            self.assertEqual(date.day, day)
            self.assertEqual(date.month, month)
            self.assertEqual(date.year, year - 1)
        with freeze_time(f"{year + 2}-{month}-{day + 1} 13:00:00"):
            date = task_template.get_due_date()
            self.assertEqual(date.day, day)
            self.assertEqual(date.month, month)
            self.assertEqual(date.year, year + 2)
        # Test days_offset
        beginning_of_year = datetime.datetime.strptime("2021-01-01", "%Y-%m-%d").replace(tzinfo=datetime.timezone.utc)
        with freeze_time(beginning_of_year):
            task_template = TaskCollectionTaskFactory(fixed_date=None, fixed_month_day=None, days_offset=10)
            expected_date_with_offset = datetime.datetime.strptime("2021-01-15", "%Y-%m-%d").replace(
                tzinfo=datetime.timezone.utc
            )
            self.assertEqual(task_template.get_due_date(as_of=beginning_of_year), expected_date_with_offset)
            # When configured to use calendar days, not business days, for the offset
            task_template.days_offset_uses_calendar_days = True
            task_template.save()
            expected_date_with_offset = datetime.datetime.strptime("2021-01-11", "%Y-%m-%d").replace(
                tzinfo=datetime.timezone.utc
            )
            self.assertEqual(task_template.get_due_date(as_of=beginning_of_year), expected_date_with_offset)

    def test_task_template_to_task(self):
        template_due_date = datetime.datetime.strptime("2021-11-11", "%Y-%m-%d").replace(tzinfo=datetime.timezone.utc)
        task_template = TaskCollectionTaskFactory(
            days_offset=0,
            assign_to_patient=False,
            assignee_group=self.provider.assignee_group,
            fixed_date=template_due_date,
            priority=1,
        )
        task = task_template_to_task(task_template, patient=self.patient)
        self.assertIsNone(task.pk)
        self.assertEqual(task.patient_id, self.patient.pk)
        self.assertEqual(task.owner_group_id, self.provider.assignee_group.pk)
        self.assertEqual(task.due_date, template_due_date)
        self.assertEqual(task.priority, 1)
        # Given that overrides for the template's data are passed
        override_title = f"Test Title {uuid.uuid1()}"
        override_assignee = self.patient
        override_due_date = datetime.datetime.strptime("2022-01-11", "%Y-%m-%d").replace(tzinfo=datetime.timezone.utc)
        override_data = {
            "owner_group": override_assignee.assignee_group,
            "due_date": override_due_date,
            "title": override_title,
            "priority": task_template.priority + 1,
        }
        task = task_template_to_task(task_template, patient=self.patient, override_data=override_data)
        self.assertEqual(task.patient_id, self.patient.pk)
        self.assertEqual(task.owner_group_id, override_assignee.assignee_group.pk)
        self.assertEqual(task.due_date, override_due_date)
        self.assertEqual(task.title, override_title)
        self.assertEqual(task.priority, override_data["priority"])

    def test_create_tasks_from_templates(self):
        form = FormFactory()
        np_group, _ = Group.objects.get_or_create(name=NP_ROLE)

        # For a Form's Task Collection Task
        task_template_1 = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False, form=form)
        task_template_2 = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False, form=form)

        form_submission_query = FormSubmission.objects.filter(user=self.patient, form=form)
        form_submissions_by_patient = {
            form_submission.user_id: form_submission for form_submission in form_submission_query
        }

        create_tasks_from_templates(
            task_templates=[task_template_1, task_template_2],
            users=[self.patient],
            event=None,
            relation_objects_by_patient=form_submissions_by_patient,
            relation_objects=[self.provider],
        )
        new_tasks_from_template_1 = Task.objects.filter(patient=self.patient, autocreated_from=task_template_1)
        self.assertEqual(len(new_tasks_from_template_1), 1)
        new_task_from_template_1 = new_tasks_from_template_1[0]
        self.assertEqual(new_task_from_template_1.patient, self.patient)
        self.assertEqual(new_task_from_template_1.created_by.phone_number, settings.TASK_AUTOMATION["LUCIAN_BOT"])
        self.assertEqual(new_task_from_template_1.source_type, SOURCE_TYPES["formsubmission"])
        form_submission = FormSubmission.objects.get(
            user=self.patient,
            form=form,
        )
        self.assertTrue(
            TaskRelation.objects.filter(
                object_id=form_submission.id, is_parent=True, task=new_task_from_template_1
            ).exists()
        )
        self.assertTrue(
            TaskRelation.objects.filter(
                object_id=self.provider.id, is_parent=False, task=new_task_from_template_1
            ).exists()
        )
        new_tasks_from_template_2 = Task.objects.filter(patient=self.patient, autocreated_from=task_template_2)
        self.assertEqual(len(new_tasks_from_template_2), 1)
        new_task_from_template_2 = new_tasks_from_template_2[0]
        self.assertTrue(
            TaskRelation.objects.filter(
                object_id=form_submission.id, is_parent=True, task=new_task_from_template_2
            ).exists()
        )
        self.assertTrue(
            TaskRelation.objects.filter(
                object_id=self.provider.id, is_parent=False, task=new_task_from_template_2
            ).exists()
        )

        # For a Care Plan's Task Collection Task
        care_plan_template = CarePlanTemplateFactory()
        care_plan = CarePlanFactory(patient=self.patient, type=care_plan_template.type)
        care_plan_query = CarePlan.objects.filter(patient=self.patient, type=care_plan_template.type)
        care_plans_by_patient = {care_plan.patient_id: care_plan for care_plan in care_plan_query}
        task_template = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False, form=None)
        create_tasks_from_templates(
            task_templates=[task_template],
            users=[self.patient],
            event=None,
            relation_objects_by_patient=care_plans_by_patient,
        )
        new_tasks = Task.objects.filter(patient=self.patient, autocreated_from=task_template)
        self.assertEqual(len(new_tasks), 1)
        new_task = new_tasks[0]
        self.assertEqual(new_task.patient, self.patient)
        self.assertEqual(new_task.created_by.phone_number, settings.TASK_AUTOMATION["LUCIAN_BOT"])
        self.assertEqual(new_task.source_type, SOURCE_TYPES["careplan"])
        self.assertTrue(TaskRelation.objects.filter(object_id=care_plan.id, is_parent=True, task=new_task).exists())

        # Test multiple templates
        task_template_1 = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False, form=None)
        task_template_2 = TaskCollectionTaskFactory(assignee_role=np_group, assign_to_patient=False, form=None)
        create_tasks_from_templates(
            task_templates=[task_template_1, task_template_2],
            users=[self.patient],
            event=None,
            relation_objects_by_patient=care_plans_by_patient,
        )
        new_tasks = Task.objects.filter(patient=self.patient, autocreated_from__in=[task_template_1, task_template_2])
        self.assertEqual(len(new_tasks), 2)
        task_template_1_task = [task for task in new_tasks if task.autocreated_from == task_template_1][0]
        self.assertEqual(
            TaskRelation.objects.filter(object_id=care_plan.id, is_parent=True, task=task_template_1_task).count(),
            1,
        )
        task_template_2_task = [task for task in new_tasks if task.autocreated_from == task_template_2][0]
        self.assertEqual(
            TaskRelation.objects.filter(object_id=care_plan.id, is_parent=True, task=task_template_2_task).count(),
            1,
        )

    def test_create_tasks_from_task_template_with_form(self):
        form = FormFactory()
        # For a Form's Task Collection Task
        task_template_1 = TaskCollectionTaskFactory(assign_to_patient=True, form=form)

        create_tasks_from_task_template(task_template_1, self.patient)

        form_submission_query = FormSubmission.objects.filter(user=self.patient, form=form)

        self.assertEquals(form_submission_query.count(), 1)

        form_submission = form_submission_query.all()[0]

        # There should be one task for the form assignment
        self.assertEquals(
            TaskRelation.objects.filter(
                content_type=ContentType.objects.get_for_model(FormSubmission),
                object_id=form_submission.id,
                is_parent=True,
            ).count(),
            1,
        )

    def test_create_tasks_from_task_template_with_form_no_tasks(self):
        form = FormFactory()

        # For a Form's Task Collection Task
        task_template_1 = TaskCollectionTaskFactory(assign_to_patient=True, form=form)

        create_tasks_from_task_template(task_template_1, self.patient)

        form_submission_query = FormSubmission.objects.filter(user=self.patient, form=form)

        self.assertEquals(form_submission_query.count(), 1)

        form_submission = form_submission_query.all()[0]

        # There should be one task for the form assignment
        self.assertEquals(
            TaskRelation.objects.filter(
                content_type=ContentType.objects.get_for_model(FormSubmission),
                object_id=form_submission.id,
                is_parent=True,
            ).count(),
            1,
        )

    def test_backfill_insurance_task_for_users_with_duplicate_account(self):
        task_collection = TaskCollection.objects.create(
            title=uuid.uuid1(),
        )
        TaskCollectionTask.objects.update_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE,
            defaults={
                "title": "Insurance Information",
                "task_collection": task_collection,
            },
        )
        category, _ = CaseCategory.objects.get_or_create(unique_key=DUPLICATE_ACCOUNT)
        person_1 = PersonUserFactory.create()
        person_2 = PersonUserFactory.create()
        person_3 = PersonUserFactory.create()

        Case.objects.create(person=person_1, category=category)
        Case.objects.create(person=person_2, category=category)
        Case.objects.create(person=person_3, category=category)

        existing_tasks = Task.objects.filter(title="Insurance Information")
        self.assertEqual(existing_tasks.count(), 0)

        management.call_command(
            "backfill_insurance_task_to_users_with_duplicate_account", user=self.provider, dry_run_off=True
        )

        existing_tasks = Task.objects.filter(title="Insurance Information")
        self.assertEqual(existing_tasks.count(), 3)

        Task.objects.get(title="Insurance Information", person=person_3).delete()

        existing_tasks = Task.objects.filter(
            title="Insurance Information",
        )
        self.assertEqual(existing_tasks.count(), 2)

        person_4 = PersonUserFactory.create()

        Case.objects.create(person=person_4, category=category)
        management.call_command(
            "backfill_insurance_task_to_users_with_duplicate_account", user=self.provider, dry_run_off=True
        )

        existing_tasks = Task.objects.filter(title="Insurance Information")
        self.assertEqual(existing_tasks.count(), 4)

    def test_add_onboarding_journey_virtual_tasks_to_patient_with_quick_health_check_form_assigned(self):
        """
        If an user has onboarding task assigned, segmentation task assignment should be skipped as part of
        add_onboarding_journey_virtual_tasks_to_patient. This test ensure, segmentation forms
        are not reassigned for users with onboarding form
        """
        person = PersonUserFactory()
        onboarding_task_template = TaskCollectionTask.objects.get(
            uid=TaskCollectionTaskUniqueIdentifiers.ONBOARDING_ASSESSMENT
        )
        create_tasks_from_task_template(
            onboarding_task_template,
            person.user,
        )
        self.assertTrue(
            person.user.related_tasks.filter(autocreated_from=onboarding_task_template)
            .exclude(deleted__isnull=False)
            .exists()
        )
        onboarding_task = (
            person.user.related_tasks.filter(autocreated_from=onboarding_task_template)
            .exclude(deleted__isnull=False)
            .first()
        )
        onboarding_task.is_complete = True
        onboarding_task.save()
        segmentation_task_template = TaskCollectionTask.objects.get(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE
        )
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        self.assertFalse(
            person.user.related_tasks.filter(autocreated_from=segmentation_task_template)
            .exclude(deleted__isnull=False)
            .exists()
        )
        add_onboarding_journey_virtual_tasks_to_patient(person.user)
        self.assertFalse(
            person.user.related_tasks.filter(autocreated_from=segmentation_task_template)
            .exclude(deleted__isnull=False)
            .exists()
        )
