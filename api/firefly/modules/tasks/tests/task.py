import datetime
import logging
import uuid
from unittest import mock
from unittest.mock import patch

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core import management
from django.utils import timezone
from rest_framework.test import APIRequestFactory, force_authenticate

import firefly.modules.tasks.tasks as TaskTasks
from firefly.core.services.opsgenie.utils import get_team_list
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PatientUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import Assignee<PERSON><PERSON>, AssigneeGroupUser, Person
from firefly.modules.appointment.constants import AppointmentReason, AppointmentStatus
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.care_plan.factories import CarePlanFactory
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation
from firefly.modules.chat_message.models import ChatMessageV2, ChatThread
from firefly.modules.features.constants import Features
from firefly.modules.forms.factories import FormFactory
from firefly.modules.forms.models import Form, FormQuestion, FormSubmission
from firefly.modules.insurance.factories import InsuranceMemberInfoFactory
from firefly.modules.insurance.models import InsuranceMemberInfo, InsurancePayer
from firefly.modules.phone_calls.models import PhoneCall
from firefly.modules.programs.primary_care.models import PrimaryCareProgramInfo
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.states.models import State
from firefly.modules.tasks.api import TaskListViewV2
from firefly.modules.tasks.collections import task_template_to_task
from firefly.modules.tasks.factories import TaskFactory
from firefly.modules.tasks.models import (
    SOURCE_TYPES,
    Task,
    TaskCollection,
    TaskCollectionTask,
    TaskRelation,
)
from firefly.modules.work_units.constants import StatusCategory

from .util import assign_to_tenant_without_task_feature_access

logger = logging.getLogger(__name__)


def update_or_create_assignee_group(provider):
    assignee_group, created = AssigneeGroup.objects.update_or_create(
        name=provider.first_name + "_" + provider.last_name
    )

    if created:
        if provider.user:
            provider.user.assignee_group = assignee_group
            provider.user.save()
            AssigneeGroupUser.objects.update_or_create(group=assignee_group, user=provider.user)
        else:
            provider.assignee_group = assignee_group
            provider.save()
            AssigneeGroupUser.objects.update_or_create(group=assignee_group, user=provider)


class FeatureAccessPermissionTestCase(FireflyTestCase):
    def test_care_plan_list_create_view(self):
        care_plan_payload = {"title": "Care Plan Test", "patient": self.patient.id, "notes": ""}
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        care_plan_id = create_response.json()["id"]

        task_payload = {
            "title": "become a pickle",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "careplan", "object_id": care_plan_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)
        task_id = response.json()["id"]

        # Case 1: Provider doesn't have access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.tasks.value] = False
        response = self.provider_client.get(f"/care-plan/task/{task_id}")
        self.assertEqual(response.status_code, 403)

        # Case 2: Provider has access to care plan feature
        self.provider.providerdetail.tenant.feature_access_config[Features.tasks.value] = True
        response = self.provider_client.get(f"/care-plan/task/{task_id}")
        self.assertEqual(response.status_code, 200)

        provider = ProviderDetailFactory.create()
        update_or_create_assignee_group(provider)

        task_payload = {
            "title": "become a pickle",
            "owner_group": provider.user.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "careplan", "object_id": care_plan_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)
        task_id = response.json()["id"]

        response = self.provider_client.get(f"/care-plan/task/{task_id}")
        self.assertEqual(response.status_code, 200)

        self.assertEqual(response.json()["owner_group"], provider.user.assignee_group.id)

        provider_user_group = ProviderDetailFactory.create()
        update_or_create_assignee_group(provider_user_group)

        task_payload = {
            "title": "become a pickle",
            "owner_group": provider_user_group.user.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "careplan", "object_id": care_plan_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)
        task_id = response.json()["id"]

        response = self.provider_client.get(f"/care-plan/task/{task_id}")
        self.assertEqual(response.status_code, 200)

        self.assertEqual(response.json()["owner_group"], provider_user_group.user.assignee_group.id)


class TaskTestCase(FireflyTestCase):
    def test_get_all_tasks(self):
        response = self.provider_client.get(f"/task/v2/?patient={self.patient.pk}")
        self.assertEqual(response.status_code, 200)
        tasks = response.json()["results"]
        self.assertEqual(len(tasks), 3)
        # the care user has 3 onboarding task assigned on creation of user.
        task_collection = TaskCollection.objects.create(title=uuid.uuid1())
        task_template_1 = TaskCollectionTask.objects.create(
            title="Test task collection task 1",
            task_collection=task_collection,
            collection_order=0,
            days_offset=0,
        )
        task_1 = task_template_to_task(task_template_1, patient=self.patient)
        self.assertIsNone(task_1.pk)
        task_1.save()
        task_template_2 = TaskCollectionTask.objects.create(
            title="Test task collection task 2",
            task_collection=task_collection,
            collection_order=1,
            days_offset=0,
        )
        task_2 = task_template_to_task(task_template_2, patient=self.patient)
        task_2.save()
        response = self.provider_client.get(f"/task/v2/?patient={self.patient.pk}")
        self.assertEqual(response.status_code, 200)
        tasks = response.json()["results"]
        self.assertEqual(len(tasks), 5)
        self.assertEqual(tasks[0]["collection_id"], task_collection.id)
        self.assertEqual(tasks[0]["title"], task_1.title)

    def test_task_list_view_v2(self):
        # Create tasks of all different types.
        response = self.client.get(f"/task/v2/?patient={self.patient.pk}")
        self.assertEqual(response.status_code, 200)
        tasks = response.json()["results"]
        self.assertEqual(len(tasks), 3)
        # 3 task already exists for patient
        # 5 tasks are created in below code
        # total - 8 task should be returned by task v2 API

        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )
        chat_message = ChatMessageV2.objects.create(
            thread=thread,
            sender=self.patient,
            text="Hi, I would like some help",
            sent_at=datetime.datetime(2020, 6, 2),
            uid=uuid.uuid4(),
        )

        # Message case task
        category = CaseCategory.objects.create(title="Mystery")
        case = Case.objects.create(person=self.patient.person, category=category)
        CaseRelation.objects.create(case=case, content_object=chat_message)
        task = Task.objects.create(
            patient=self.patient,
            title="Case task",
            owner_group=self.patient.assignee_group,
        )
        TaskRelation.objects.create(task=task, content_object=case, is_parent=True)

        # Phone call case task
        phone_call = PhoneCall.objects.create(user=self.patient, direction="INBOUND", called_at=datetime.datetime.now())
        case = Case.objects.create(person=self.patient.person, category=category)
        CaseRelation.objects.create(case=case, content_object=phone_call)
        task = Task.objects.create(
            patient=self.patient,
            title="Phone call task",
            owner_group=self.patient.assignee_group,
        )
        TaskRelation.objects.create(task=task, content_object=case, is_parent=True)

        # Care plan task
        care_plan = CarePlanFactory.create()
        task = Task.objects.create(
            patient=self.patient,
            title="Care plan task",
            owner_group=self.patient.assignee_group,
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)

        # Form task with appointment
        form = FormFactory()
        form_submission = FormSubmission.objects.create(form=form, user=self.patient)
        task = Task.objects.create(
            patient=self.patient,
            title="Form task",
            owner_group=self.patient.assignee_group,
        )
        TaskRelation.objects.create(task=task, content_object=form_submission, is_parent=True)
        appointment = AppointmentFactory.create()
        physician = appointment.physician
        state_ma = State.objects.get(abbreviation="MA")
        state_nh = State.objects.get(abbreviation="NH")
        physician.practicing_states.set(
            [
                state_ma,
                state_nh,
            ]
        )
        TaskRelation.objects.create(task=task, content_object=appointment, is_parent=False)

        # Insurance task
        insurance = InsuranceMemberInfoFactory.create()
        task = Task.objects.create(
            patient=self.patient,
            title="Insurance task",
            owner_group=self.patient.assignee_group,
        )
        TaskRelation.objects.create(task=task, content_object=insurance, is_parent=True)

        response = self.client.get(f"/task/v2/?patient={self.patient.pk}")
        self.assertEqual(response.status_code, 200)
        tasks = response.json()["results"]
        self.assertEqual(len(tasks), 8)
        physician.practicing_states.set(
            [
                state_ma,
            ]
        )

        def get_response():
            return self.client.get(f"/task/v2/?patient={self.patient.pk}")

        response = get_response()
        self.assertEqual(response.status_code, 200)
        tasks = response.json()["results"]
        self.assertEqual(len(tasks), 8)

    def test_care_plan_task_create_and_update(self):
        care_plan_payload = {"title": "Care Plan Test", "patient": self.patient.id, "notes": ""}
        create_response = self.provider_client.post("/care-plan/", care_plan_payload, format="json")
        care_plan_id = create_response.json()["id"]

        task_payload = {
            "title": "become a pickle",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "careplan", "object_id": care_plan_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "become a pickle")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, self.patient.assignee_group.id)
        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2019-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

        # Check created task relation
        task_relation_object = TaskRelation.objects.get(id=response.json()["relations"][0]["id"])
        self.assertEqual(task_relation_object.content_type.model, "careplan")
        self.assertEqual(task_relation_object.object_id, care_plan_id)
        # Check task relation content object same as care plan object
        self.assertEqual(task_relation_object.content_object.id, care_plan_id)

        # Update task
        provider = ProviderDetailFactory.create()
        update_or_create_assignee_group(provider)
        updated_task_payload = {
            "title": "get out of pickle",
            "owner_group": provider.user.assignee_group.id,
            "due_date": "2019-10-10T12:12:12",
            "priority": 0,
        }
        update_response = self.provider_client.patch(
            "/task/{}".format(task_object.id), updated_task_payload, format="json"
        )
        self.assertEqual(update_response.status_code, 200)
        updated_task_object = Task.objects.get(id=task_object.id)
        self.assertEqual(updated_task_object.title, "get out of pickle")
        self.assertEqual(updated_task_object.owner_group.id, provider.user.assignee_group.id)
        self.assertEqual(updated_task_object.updated_by.id, self.provider.id)
        self.assertEqual(updated_task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2019-10-10T12:12:12")

    def test_case_task_create_request(self):
        patient = self.patient
        category = CaseCategory.objects.create(title="Mystery")
        payload = {
            "user": patient.pk,
            "person": patient.person.pk,
            "category": category.pk,
            "tasks": [
                {
                    "title": "A Test Case",
                    "owner_group": patient.assignee_group.pk,
                    "due_date": "2022-01-01",
                    "priority": 1,
                },
            ],
        }

        create_response = self.provider_client.post("/providers/me/cases/", payload, format="json")
        case_id = create_response.json()["id"]
        task_payload = {
            "title": "A cases task",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2021-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "case", "object_id": case_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "A cases task")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, self.patient.assignee_group.id)
        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2021-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

        # Check created task relation
        task_relation_object = TaskRelation.objects.get(id=response.json()["relations"][0]["id"])
        self.assertEqual(task_relation_object.content_type.model, "case")
        self.assertEqual(task_relation_object.object_id, case_id)
        # Check task relation content object same as case object
        self.assertEqual(task_relation_object.content_object.id, case_id)

        provider = ProviderDetailFactory.create()
        update_or_create_assignee_group(provider)
        payload = {
            "user": patient.pk,
            "person": patient.person.pk,
            "category": category.pk,
            "tasks": [
                {
                    "title": "A Test Case",
                    "owner_group": provider.user.assignee_group.pk,
                    "due_date": "2022-01-01",
                    "priority": 1,
                },
            ],
        }

        create_response = self.provider_client.post("/providers/me/cases/", payload, format="json")
        case_id = create_response.json()["id"]
        task_payload = {
            "title": "A cases task",
            "owner_group": provider.user.assignee_group.id,
            "due_date": "2021-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "case", "object_id": case_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "A cases task")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, provider.user.assignee_group.id)

        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2021-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

    def test_form_submission_task_create_request(self):
        form = Form.objects.create(uid="test")
        new_questions = FormQuestion.objects.bulk_create(
            [
                FormQuestion(uid="test1", type="test", position=0),
            ]
        )

        form.sections.first().questions.add(*new_questions)
        submission_payload = {
            "user": self.patient.id,
            "form": form.id,
            "data": "{}",
        }
        create_response = self.provider_client.post("/form/submission/", submission_payload, format="json")
        form_submission_id = create_response.json()["id"]
        task_payload = {
            "title": "A form submission task",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2021-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "formsubmission", "object_id": form_submission_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "A form submission task")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, self.patient.assignee_group.id)
        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2021-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

        # Check created task relation
        task_relation_object = TaskRelation.objects.get(id=response.json()["relations"][0]["id"])
        self.assertEqual(task_relation_object.content_type.model, "formsubmission")
        self.assertEqual(task_relation_object.object_id, form_submission_id)
        # Check task relation content object same as form submission object
        self.assertEqual(task_relation_object.content_object.id, form_submission_id)

    def test_appointment_task_create_request(self):
        patient = self.patient
        person = patient.person
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        practice = PrimaryCareProgramInfo.objects.filter(person=person).first().caregiver_practice
        appointment = Appointment.objects.create(
            patient_id=patient.pk,
            practice_id=practice.id,
            start=(timezone.now() + datetime.timedelta(hours=24)),
            duration="00:15:00",
            description="A future appointment",
            reason=AppointmentReason.VIDEO,
            elation_id=9999999,
            status=AppointmentStatus.SCHEDULED.value,
        )
        appointment_id = appointment.id
        task_payload = {
            "title": "A appointment task",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2021-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "appointment", "object_id": appointment_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "A appointment task")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, self.patient.assignee_group.id)
        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2021-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

        # Check created task relation
        task_relation_object = TaskRelation.objects.get(id=response.json()["relations"][0]["id"])
        self.assertEqual(task_relation_object.content_type.model, "appointment")
        self.assertEqual(task_relation_object.object_id, appointment_id)
        # Check task relation content object same as appointment object
        self.assertEqual(task_relation_object.content_object.id, appointment_id)

    def test_insurance_info_task_create_request(self):
        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()
        insurance_info = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer, member_id="00011", group_number="11234"
        )
        insurance_info_id = insurance_info.id
        task_payload = {
            "title": "A insurance task",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2021-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "insurancememberinfo", "object_id": insurance_info_id}],
        }

        response = self.provider_client.post("/task/", task_payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Check created task
        task_object = Task.objects.get(id=response.json()["id"])
        self.assertEqual(task_object.title, "A insurance task")
        self.assertEqual(task_object.patient.id, self.patient.id)
        self.assertEqual(task_object.created_by.id, self.provider.id)
        self.assertEqual(task_object.owner_group.id, self.patient.assignee_group.id)
        self.assertEqual(task_object.due_date.strftime("%Y-%m-%dT%H:%M:%S"), "2021-09-09T12:12:12")
        self.assertEqual(task_object.is_complete, False)

        # Check created task relation
        task_relation_object = TaskRelation.objects.get(id=response.json()["relations"][0]["id"])
        self.assertEqual(task_relation_object.content_type.model, "insurancememberinfo")
        self.assertEqual(task_relation_object.object_id, insurance_info_id)
        # Check task relation content object same as insurance_info object
        self.assertEqual(task_relation_object.content_object.id, insurance_info_id)

    def test_completed_by(self):
        # create task
        task_payload = {
            "title": "tell others i am a pickle",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
        }
        response = self.provider_client.post("/task/", task_payload, format="json")
        self.assertEqual(response.status_code, 201)
        task_object = Task.objects.get(id=response.json()["id"])

        self.assertEqual(task_object.is_complete, False)
        self.assertEqual(task_object.completed_by, None)

        task_object.set_attr("is_complete", True, self.provider)
        task_object.save()
        self.assertEqual(task_object.is_complete, True)
        self.assertEqual(task_object.completed_by_id, self.provider.id)

    def test_invalid_task_relation_fk(self):
        INVALID_FK = 10000
        invalid_fk_task_payload = {
            "title": "tell others i am a pickle",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "chatmessagev2", "object_id": INVALID_FK}],
        }
        response = self.provider_client.post("/task/", invalid_fk_task_payload, format="json")
        self.assertEqual(response.status_code, 400)

    def test_provider_complete_task(self):
        task = TaskFactory.create()
        self.assertEqual(task.is_complete, False)
        self.assertEqual(task.completed_by, None)
        self.assertEqual(task.completed_on, None)

        update_response = self.provider_client.patch("/task/{}".format(task.id), {"is_complete": True}, format="json")

        updated_task = Task.objects.get(id=task.id)
        self.assertEqual(update_response.status_code, 200)
        self.assertEqual(updated_task.is_complete, True)
        self.assertEqual(updated_task.completed_by, self.provider)
        self.assertTrue(updated_task.completed_on is not None)

    def test_case_save_person_sync(self):
        patient = PatientUserFactory.create()
        person = Person.objects.create(user=patient)
        task = Task.objects.create(patient=patient)
        self.assertEqual(task.person.pk, person.pk)

        # On update, person should be synced with new user.
        other_patient = PatientUserFactory.create()
        other_person = Person.objects.create(user=other_patient)

        task.patient = other_patient
        task.save()

        self.assertEqual(task.person.pk, other_person.pk)

    def test_status_category_on_complete(self):
        # create task
        task_payload = {
            "title": "tell others i am a pickle",
            "owner_group": self.patient.assignee_group.id,
            "due_date": "2019-09-09T12:12:12",
            "patient": self.patient.id,
        }
        response = self.provider_client.post("/task/", task_payload, format="json")
        self.assertEqual(response.status_code, 201)
        task_object = Task.objects.get(id=response.json()["id"])

        self.assertEqual(task_object.is_complete, False)
        self.assertEqual(task_object.status_category, StatusCategory.NOT_STARTED)

        # mark the task as complete
        task_payload = {"is_complete": True}
        response = self.provider_client.patch(f"/task/{task_object.id}", task_payload, format="json")
        self.assertEqual(response.status_code, 200)
        task_object.refresh_from_db()

        self.assertEqual(task_object.is_complete, True)
        self.assertEqual(task_object.status_category, StatusCategory.COMPLETE)

        # mark the task as incomplete
        task_payload = {"is_complete": False}
        response = self.provider_client.patch(f"/task/{task_object.id}", task_payload, format="json")
        self.assertEqual(response.status_code, 200)
        task_object.refresh_from_db()

        self.assertEqual(task_object.is_complete, False)
        self.assertEqual(task_object.status_category, StatusCategory.NOT_STARTED)

    def test_task_retrieve_update_view(self):
        task = TaskFactory()

        def get_response():
            return self.provider_client.get(f"/task/v2/{task.id}/")

        response = get_response()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["id"], task.id)

        # Test Tenant permissions
        assign_to_tenant_without_task_feature_access(self.provider.providerdetail)
        self.assertEqual(get_response().status_code, 403)

    def test_task_list_queries(self):
        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )
        category = CaseCategory.objects.create(title="Mystery")
        task_collection = TaskCollection.objects.create(title=uuid.uuid1())
        task_collection_task = TaskCollectionTask.objects.create(
            title="Test task collection task 1",
            task_collection=task_collection,
            collection_order=0,
            days_offset=0,
        )
        for _ in range(3):
            chat_message = ChatMessageV2.objects.create(
                thread=thread,
                sender=self.patient,
                text="Hi, I would like some help",
                sent_at=datetime.datetime(2020, 6, 2),
                uid=uuid.uuid4(),
            )
            for _ in range(3):
                case = Case.objects.create(person=self.patient.person, category=category)
                CaseRelation.objects.create(case=case, content_object=chat_message)
                phone_call = PhoneCall.objects.create(
                    user=self.patient, direction="INBOUND", called_at=datetime.datetime.now()
                )
                CaseRelation.objects.create(case=case, content_object=phone_call)
                for _ in range(3):
                    task = Task.objects.create(
                        patient=self.patient,
                        title="Case task",
                    )
                    TaskRelation.objects.create(task=task, content_object=case, is_parent=True)
            care_plan = CarePlanFactory.create()
            for _ in range(3):
                task = Task.objects.create(
                    patient=self.patient,
                    title="Care plan task",
                    owner_group=self.patient.assignee_group,
                )
                TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
            form = FormFactory()
            form_submission = FormSubmission.objects.create(form=form, user=self.patient)
            for _ in range(3):
                task = Task.objects.create(
                    patient=self.patient,
                    title="Form task",
                    owner_group=self.patient.assignee_group,
                    autocreated_from=task_collection_task,
                )
                TaskRelation.objects.create(task=task, content_object=form_submission, is_parent=True)
                for _ in range(3):
                    appointment = AppointmentFactory.create()
                    TaskRelation.objects.create(task=task, content_object=appointment, is_parent=False)
            insurance = InsuranceMemberInfoFactory.create()
            for _ in range(3):
                task = Task.objects.create(
                    patient=self.patient,
                    title="Insurance task",
                    owner_group=self.patient.assignee_group,
                )
                TaskRelation.objects.create(task=task, content_object=insurance, is_parent=True)

        # Normally, we'd use self.provider_client.get(...) to test this, going through the client
        # issues some additional queries unrelated to data fetching. To isolate just the queries we
        # care about, we access the view directly.
        view = TaskListViewV2.as_view()
        factory = APIRequestFactory()
        request = factory.get("/task/v2/", format="json")
        force_authenticate(request, user=self.provider)

        # Ensure cache is cleared for test isolation.
        ContentType.objects.clear_cache()

        # Here's how the queries add up:
        #   3 to find perms of requesting user
        #   1 to fetch tenant
        #   1 to fetch user_person
        #   1 to fetch user_person_tenants
        #   1 to fetch tasks
        #   1 to fetch task_relations
        #   1 to fetch content_type
        with self.assertNumQueries(9):
            response = view(request)
            response.render()

            self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
            body = response.data
            self.assertEqual(body["count"], 57)

    def test_task_v2_filter(self):
        # user has 3 task assigned
        response = self.provider_client.get("/task/v2/")
        self.assertEqual(response.status_code, 200)
        body = response.json()
        self.assertEqual(body["count"], 3)
        # source type filter
        TaskFactory(
            source_type=SOURCE_TYPES["formsubmission"],
            owner_group=self.patient.assignee_group,
            patient=self.patient,
            is_complete=False,
        )
        TaskFactory(
            source_type=SOURCE_TYPES["case"],
            owner_group=self.patient.assignee_group,
            patient=self.patient,
            is_complete=False,
        )
        TaskFactory(
            source_type=SOURCE_TYPES["careplan"],
            owner_group=self.patient.assignee_group,
            patient=self.patient,
            is_complete=False,
        )
        response = self.provider_client.get("/task/v2/", {"source_type__in": "careplan,formsubmission"})
        self.assertEqual(response.status_code, 200)
        body = response.json()
        self.assertEqual(body["count"], 3)
        for task in body["results"]:
            self.assertIn(task["source_type"], ["formsubmission", "careplan"])
        # is_complete filter
        TaskFactory(
            source_type=SOURCE_TYPES["careplan"],
            owner_group=self.patient.assignee_group,
            patient=self.patient,
            is_complete=True,
        )
        response = self.provider_client.get("/task/v2/", {"is_complete": False})
        self.assertEqual(response.status_code, 200)
        body = response.json()
        self.assertEqual(body["count"], 6)
        for task in body["results"]:
            self.assertNotEquals(task["is_complete"], True)
        # source type with is_complete filter
        response = self.provider_client.get(
            "/task/v2/", {"source_type__in": "careplan,formsubmission", "is_complete": False}
        )
        self.assertEqual(response.status_code, 200)
        body = response.json()
        self.assertEqual(body["count"], 3)
        for task in body["results"]:
            self.assertIn(task["source_type"], ["formsubmission", "careplan"])
            self.assertNotEquals(task["is_complete"], True)
        # without filter
        response = self.provider_client.get("/task/v2/")
        self.assertEqual(response.status_code, 200)
        body = response.json()
        self.assertEqual(body["count"], 7)


class TaskCommandTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.tasks.management.commands.priority_task_opsgenie_alert.create_new_alert")
    @mock.patch(
        "firefly.modules.tasks.management.commands.priority_task_opsgenie_alert.is_over_threshold",
        return_value=True,
    )
    def test_priority_task_opsgenie_alert(self, mock_is_over_threshold, mock_create_new_alert):
        clinician_group = ProviderDetailFactory.create(first_name="On-Call", last_name="Clinician")

        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )

        message_1 = ChatMessageV2.objects.create(
            thread=thread,
            text="Testing message for patient",
            sender=self.patient,
            uid="test1",
        )

        # Given there is a provider on the patient's care team to receive the message
        self.patient.person.care_team.add(self.provider.providerdetail)

        # create urgent case task
        category = CaseCategory.objects.create(title="Mystery")
        case = Case.objects.create(person=self.patient.person, category=category)
        CaseRelation.objects.create(
            case=case,
            content_object=message_1,
        )
        task = Task.objects.create(
            title="Urgent Message Task",
            owner_group=clinician_group.user.assignee_group,
            due_date="2019-09-09T12:12:12",
            patient=self.patient,
            priority=2,
            source_type=SOURCE_TYPES["case"],
        )
        TaskRelation.objects.create(
            task=task,
            content_object=case,
            is_parent=True,
        )

        case_links = settings.LUCIAN_BASE_URL + f"/cases/{case.id}"
        team_list = get_team_list([settings.OPSGENIE_TEAM["LUCIAN_OPERATIONS"]])

        management.call_command("priority_task_opsgenie_alert", user=self.provider)
        mock_create_new_alert.assert_called_once_with(
            api_key=settings.OPSGENIE["API_KEY"]["LUCIAN_OPERATIONS"],
            message="Overdue L2 Tasks",
            alias=f"L2 Tasks [{case.id}]",
            description=case_links,
            responders=team_list,
            visible_to=team_list,
            priority="P2",
        )

    @patch("firefly.modules.tasks.tasks.get_slack_ids")
    @patch.object(TaskTasks, "send_slack_alert")
    def test_api_create_task_slack_alerting(self, mock_send_slack_alert, mock_get_slack_ids):
        assignee = ProviderDetailFactory.create(first_name="On-Call", last_name="Clinician")
        mock_get_slack_ids.return_value = "SLACKID123"
        self.patient.person.care_team.add(assignee)

        case = CaseFactory()
        payload = {
            "title": "An urgent task",
            "priority": 2,
            "owner_group": assignee.user.assignee_group.id,
            "due_date": "2099-09-09T12:12:12",
            "patient": self.patient.id,
            "relations": [{"content_type": "case", "object_id": case.id}],
        }

        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/task/", payload, format="json")

        self.assertEqual(response.status_code, 201)
        mock_send_slack_alert.send.assert_called_once_with(task_id=response.json()["id"])

    @mock.patch("firefly.modules.tasks.management.commands.priority_task_opsgenie_alert.create_new_alert")
    @mock.patch(
        "firefly.modules.tasks.management.commands.priority_task_opsgenie_alert.is_over_threshold",
        return_value=True,
    )
    def test_priority_task_opsgenie_alert_for_deleted_chat_message(self, mock_is_over_threshold, mock_create_new_alert):
        clinician_group = ProviderDetailFactory.create(first_name="On-Call", last_name="Clinician")

        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )

        message_1 = ChatMessageV2.objects.create(
            thread=thread,
            text="Testing message for patient",
            sender=self.patient,
            uid="test1",
        )

        message_2 = ChatMessageV2.objects.create(
            thread=thread,
            text="Testing message for patient 2",
            sender=self.patient,
            uid="test2",
        )

        # Given there is a provider on the patient's care team to receive the message
        self.patient.person.care_team.add(self.provider.providerdetail)

        # create urgent case task
        category = CaseCategory.objects.create(title="Mystery")
        case = Case.objects.create(person=self.patient.person, category=category)
        case_2 = Case.objects.create(person=self.patient.person, category=category)
        CaseRelation.objects.create(
            case=case,
            content_object=message_1,
        )
        CaseRelation.objects.create(
            case=case_2,
            content_object=message_2,
        )

        task = Task.objects.create(
            title="Urgent Message Task",
            owner_group=clinician_group.user.assignee_group,
            due_date="2019-09-09T12:12:12",
            patient=self.patient,
            priority=2,
            source_type=SOURCE_TYPES["case"],
        )
        TaskRelation.objects.create(
            task=task,
            content_object=case,
            is_parent=True,
        )

        task_2 = Task.objects.create(
            title="Urgent Message Task 2",
            owner_group=clinician_group.user.assignee_group,
            due_date="2019-09-10T12:12:12",
            patient=self.patient,
            priority=2,
            source_type=SOURCE_TYPES["case"],
        )
        TaskRelation.objects.create(
            task=task_2,
            content_object=case_2,
            is_parent=True,
        )

        message_2.delete()
        self.assertIsNotNone(message_2.deleted)

        management.call_command("priority_task_opsgenie_alert", user=self.provider)
        mock_create_new_alert.assert_called_once()
