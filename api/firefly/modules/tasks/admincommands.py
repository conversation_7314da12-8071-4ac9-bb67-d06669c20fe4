from django import forms

from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand


class PriorityTaskOpsgenieAlert(FireflyAdminCommand):
    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class BackfillInsuranceTaskToUsersWithDuplicateAccount(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        return [], {"user": user}


class AssignSegmentationTaskToUsers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        user_ids = forms.CharField(
            required=True,
            label="Comma-separated User IDs",
        )

    def get_command_arguments(self, data, user):
        opts = [
            "--user-ids",
            data["user_ids"],
        ]
        return opts, {"user": user}


class BackfillInsuranceTaskSourceType(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        return [], {"user": user}


class BackfillExpiredUserWithInsuranceTask(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        user_ids = forms.CharField(
            required=False,
            label="Comma-separated User IDs",
        )

    def get_command_arguments(self, data, user):
        opts = [
            "--user-ids",
            data["user_ids"],
        ]
        return opts, {"user": user}
