import logging
from datetime import date, datetime, timedelta
from typing import Callable, List, Optional, Union

from django.conf import settings
from django.contrib.auth.models import Group
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models, transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Q
from django.utils import timezone
from django_deprecate_fields import deprecate_field
from pandas.tseries.offsets import BDay

from firefly.core.assignment.utils import AssignmentScheme, get_assignee_group
from firefly.core.user.constants import CARE_TEAM_GROUPS
from firefly.modules.cases.constants import CaseActions, CaseStatus
from firefly.modules.events.models import EventDateProperty, EventType
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import PreSaveValidationError, SaveHandlersMixin
from firefly.modules.forms.models import Form
from firefly.modules.work_units.constants import StatusCategory
from firefly.modules.work_units.models import WorkUnit

logger = logging.getLogger(__name__)


# helper functions for creating different types of care plan tasks
class CarePlanTask:
    @classmethod
    def create(cls, care_plan, validated_data):
        new_task = Task.objects.create(source_type="careplan", **validated_data)
        TaskRelation.objects.create(task=new_task, content_object=care_plan, is_parent=True)
        return new_task

    @classmethod
    def get_one_from_care_plan(cls, care_plan, filters):
        related_task_ids = [t.task.id for t in care_plan.care_plan_tasks.all()]
        return Task.objects.get(id__in=related_task_ids, **filters)

    @classmethod
    def get_all_from_care_plan(cls, care_plan):
        related_task_ids = [t.task.id for t in care_plan.care_plan_tasks.all()]
        return Task.objects.filter(id__in=related_task_ids).all()


# See README for more information on Virtual tasks
SOURCE_TYPES = {
    "careplan": "careplan",
    "case": "case",
    "chatmessagev2": "chatmessagev2",
    "formsubmission": "formsubmission",
    "insuranceupload": "insuranceupload",
    "virtual": "virtual",
}

SOURCE_TYPE_CHOICES = (
    (SOURCE_TYPES["careplan"], "Care Plan"),
    (SOURCE_TYPES["case"], "Case"),
    (SOURCE_TYPES["chatmessagev2"], "Chat Message (V2)"),
    (SOURCE_TYPES["formsubmission"], "Form Submission"),
    (SOURCE_TYPES["insuranceupload"], "Insurance Upload"),
    (SOURCE_TYPES["virtual"], "Virtual"),
)

CONTENT_TYPES, CONTENT_LABELS = zip(*SOURCE_TYPE_CHOICES)


def call_side_effect_fn(previous, current, fn: Callable):
    if current.id:
        # Before a side-effect fn is actually triggered by on-commit
        # the `current` object passed to it as an argument
        # will not have the most recent data altered by the commit
        # e.g. pghistory data for created_by
        # Refresh now before calling downstream code
        current.refresh_from_db()
    fn(previous, current)


def send_slack_alert_side_effect(previous: Union["Task", None], current: "Task"):
    """
    If a task is high priority, send a Slack alert on creation and reassignment.
    """

    from .tasks import needs_slack_alert, send_slack_alert

    # Needs an alert now, and
    # a) didn't need it before (e.g. if this is a new Task or the priority has changed)
    # b) needed it before, but for a different person
    if needs_slack_alert(current) and (
        previous is None or not needs_slack_alert(previous) or previous.owner_group != current.owner_group
    ):
        return send_slack_alert.send(task_id=current.id)

    return None


class Task(SaveHandlersMixin, WorkUnit):
    """
    A generic task model.
    """

    CONTENT_TYPES = CONTENT_TYPES

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255, blank=True)  # noqa: TID251

    # The type of the object originating this task.
    # Should match the content_type of the task relation with `is_parent`.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    source_type = models.CharField(max_length=63, choices=SOURCE_TYPE_CHOICES, null=True, blank=True, db_index=True)  # noqa: TID251

    # TODO(nikhil): rename this from patient to user
    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        # TODO (Franklin): rename related_tasks to `tasks` after migration is complete
        related_name="related_tasks",
        on_delete=models.CASCADE,
        default=None,
        null=True,
        blank=True,
    )

    person = models.ForeignKey(
        "user.Person",
        related_name="tasks",
        # On delete, do nothing for now; we'll ensure referential integrity once the backfill of
        # Person is fully validated.
        # TODO: Set to CASCADE.
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        # Can be null for now, until the backfill is complete and verified.
        # TODO: Remove.
        null=True,
        blank=True,
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="created_tasks",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    assignee = deprecate_field(
        models.ForeignKey(
            settings.AUTH_USER_MODEL,
            related_name="assigned_tasks",
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
            null=True,
            blank=True,
        )
    )

    due_date = models.DateTimeField(null=True, blank=True, db_index=True)

    is_complete = models.BooleanField(default=False, db_index=True)

    completed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="completed_tasks",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
    )

    completed_on = models.DateTimeField(null=True, blank=True)

    autocreated_from = models.ForeignKey(
        "TaskCollectionTask",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        blank=True,
        null=True,
        related_name="created_tasks",
    )

    # Maps to the number of "!" we show in Lucian
    priority = models.IntegerField(default=0, db_index=True)

    metadata = JSONField(null=True, blank=True)
    # Lucian users can create Tasks meant to "forward" a message to another provider. These are "RE:..." tasks
    is_forwarding_message = models.BooleanField(default=False, null=True, blank=True)

    @property
    def subject(self):
        return self.person

    # TODO: Refactor this magical interface or pull logic into serializer
    def set_attr(self, attr, value, request_user=None):
        if attr == "owner_group":
            self.owner_group_id = value
        elif attr == "is_complete":
            self.set_is_complete(value, request_user)
        else:
            setattr(self, attr, value)

    def set_is_complete(self, new_status, completed_by):
        if new_status:
            self.completed_by = completed_by
            self.completed_on = datetime.now()
        else:
            self.completed_by = None
            self.completed_on = None
        self.is_complete = new_status

    def queue_post_save_side_effects(self) -> List[Callable]:
        """
        Queue up side effects to execute on commit.
        """
        return [send_slack_alert_side_effect]

    def pre_save_validation(self, changed):
        from firefly.modules.cases.models import Case

        if changed("is_complete") and self.source_type == "case":
            case_content_type = ContentType.objects.get_for_model(Case)
            # task relation is added only after a task is created so we can only validate updates to the task
            # once the task is created and task relation exists
            if self.pk and self.relations:
                task_relations = self.relations.filter(content_type=case_content_type)
                if task_relations is not None:
                    for task_relation in task_relations.iterator():
                        case = task_relation.content_object
                        if case.status_category in (StatusCategory.COMPLETE, StatusCategory.DEFERRED):
                            raise PreSaveValidationError("Task cannot be updated on a closed case")

    def pre_save_work_unit_mutation(self):
        # Get the status category from is_complete field
        self.status_category = StatusCategory.COMPLETE if self.is_complete else StatusCategory.NOT_STARTED

    def post_save_update_case_fields(self):
        from firefly.modules.cases.models import Case

        if self.source_type == "case":
            case_content_type = ContentType.objects.get_for_model(Case)
            task_relation = self.relations.filter(content_type=case_content_type).first()

            if task_relation is not None:
                case = task_relation.content_object

                # Update status and status category
                if case.auto_complete:
                    action = None
                    if (
                        case.task_relations.filter(task__status_category=StatusCategory.NOT_STARTED).count() == 0
                        and case.status == CaseStatus.IN_PROGRESS
                    ):
                        action = CaseActions.CLOSE
                    elif (
                        case.task_relations.filter(task__status_category=StatusCategory.NOT_STARTED).count() > 0
                        and case.status == CaseStatus.CLOSED
                    ):
                        action = CaseActions.REOPEN

                    if action:
                        case.action = action
                        case.save()

    def save(self, *args, **kwargs):
        try:
            user = self.patient
            if hasattr(user, "person"):
                person = user.person
                self.person = person

        except Exception:
            logger.exception("Could not sync person on case %s", self)
        post_save_side_effects_fns: List[Callable] = self.queue_post_save_side_effects()
        self.pre_save_work_unit_mutation()
        # Get a reference to the object before it was saved
        # so that post-save side-effects can compare pre-save to post-save
        previous: Optional[Task] = None
        if self.pk is not None and self.deleted is None:
            previous = Task.all_objects.get(pk=self.pk)
            if previous is not None and previous.deleted is not None:
                previous = None
        super().save(*args, **kwargs)
        for fn in post_save_side_effects_fns:
            transaction.on_commit(lambda: call_side_effect_fn(previous=previous, current=self, fn=fn))
        self.post_save_update_case_fields()

    class Meta(WorkUnit.Meta):
        db_table = "tasks"
        ordering = ["due_date"]


class TaskRelation(SaveHandlersMixin, BaseModelV3):
    """
    Links tasks to objects, like care_plans, messages
    """

    task = models.ForeignKey(Task, related_name="relations", on_delete=models.CASCADE)
    # is_parent is True if this Task Relation was the first one created
    # primary will be used for analytics
    is_parent = models.BooleanField(default=True)

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    # Below the mandatory fields for generic relation
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()

    class Meta(BaseModelV3.Meta):
        db_table = "task_relations"
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
        ]

    def clean(self):
        self.content_type.get_object_for_this_type(pk=self.object_id)

    def pre_save_validation(self, changed):
        from firefly.modules.cases.models import Case

        if (
            changed("task") or changed("object_id") or changed("content_type")
        ) and self.content_type.model == ContentType.objects.get_for_model(Case).model:
            case = self.content_object
            if case.status_category in (StatusCategory.COMPLETE, StatusCategory.DEFERRED):
                raise PreSaveValidationError("Task cannot be created on a closed case")

    def save(self, *args, **kwargs):
        self.clean()
        super(TaskRelation, self).save(*args, **kwargs)


class TaskCollection(BaseModelV3):
    """
    Represents collections of tasks
    """

    # these title and description will be patient-facing at first, then will probably become
    # used for internal context in the long-run
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255)  # noqa: TID251
    description = models.TextField(blank=True, default="")
    event_type = BaseModelV3ManyToManyField(
        EventType,
        related_name="task_collections",
        blank=True,
        through="TaskCollectionEventType",
    )

    class Meta(BaseModelV3.Meta):
        db_table = "task_collections"

    def __str__(self):
        return f"{self.title}: TaskCollection ({self.pk})"


class TaskCollectionEventType(BaseModelV3):
    taskcollection = models.ForeignKey(
        TaskCollection,
        related_name="taskcollection_eventtype",
        on_delete=models.CASCADE,
    )

    eventtype = models.ForeignKey(
        EventType,
        related_name="taskcollection_eventtype",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "task_collections_event_type"
        verbose_name_plural: str = "Task collection event types"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["taskcollection", "eventtype"],
                condition=Q(deleted=None),
                name="task_collection_event_type_uniq",
            )
        ]


class TaskCollectionTask(BaseModelV3):
    """
    Represents an individual task template.
    """

    task_collection = models.ForeignKey(TaskCollection, related_name="tasks", on_delete=models.CASCADE)
    # See TaskCollectionTaskUniqueIdentifiers
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=31, db_index=True, unique=True, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255)  # noqa: TID251

    # There are three ways of picking task assignees.
    # 1) A fixed assignee group
    # 2) A care team role
    # 3) Patient assigned
    # 4) TODO: A user related to a relevant object
    assignee = deprecate_field(
        models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, blank=True, null=True)
    )  # use assignee_group instead
    assignee_group = models.ForeignKey(
        "user.AssigneeGroup",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="+",
    )
    assign_to_patient = models.BooleanField(default=False)
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    assignee_role = models.ForeignKey(Group, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    # Templated tasks have the following modes for determining due date:
    # 1) Fixed date (every instantiated task will have the same due date)
    # 2) Offset from now (in either calendar days or business days)
    # 3) Offset from an event date
    #      The template stores a pointer to a date property of a relevant object
    #      (e.g. appointment date, user signup date) and an offset from that date.
    # 4) The n-th day of some month (e.g. the {4th} of {October})
    days_offset = models.IntegerField(null=True, blank=True)
    # Should offsets be calculated in calendar days, vs. the default business days?
    days_offset_uses_calendar_days = models.BooleanField(null=True, blank=True)
    event_date_property = models.ForeignKey(EventDateProperty, on_delete=models.CASCADE, null=True, blank=True)
    fixed_date = models.DateTimeField(null=True, blank=True)
    # To address due date mode #4:
    # fixed_day_month is a DateTime (year/month/day + time)
    # but in calculations we should ignore everything but month/day
    # and just select the current year's instance of that month/day
    fixed_month_day = models.DateTimeField(
        null=True, blank=True, verbose_name="Make due on this month/day of the current year"
    )

    priority = models.IntegerField(null=True, blank=True, default=0)

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    form = models.ForeignKey(Form, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    # TODO: Deprecate these fields.
    send_push_notification = models.BooleanField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    push_notification_text = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    collection_order = models.IntegerField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    source_type = models.CharField(max_length=63, choices=SOURCE_TYPE_CHOICES, null=True, blank=True, db_index=True)  # noqa: TID251

    metadata = JSONField(null=True, blank=True)

    # TODO: this model should store an assignment_scheme, like WorklistItem
    # so that we can use get_assignee in a cleaner way
    def get_real_assignee(self, patient=None):
        default_assignee_group = None
        group = self.assignee_role
        assignment_scheme = AssignmentScheme.ASSIGN_TO_NONE

        if self.assignee_group is not None:
            assignment_scheme = AssignmentScheme.ASSIGN_TO_DEFAULT
            default_assignee_group = self.assignee_group
        elif self.assign_to_patient:
            assignment_scheme = AssignmentScheme.ASSIGN_TO_DEFAULT
            default_assignee_group = patient.assignee_group
        elif self.assignee_role:
            group = self.assignee_role
            if group.name in CARE_TEAM_GROUPS:
                assignment_scheme = AssignmentScheme.ASSIGN_TO_ROLE_IN_CARE_TEAM
            else:
                assignment_scheme = AssignmentScheme.ASSIGN_TO_GROUP_ROUND_ROBIN

        return get_assignee_group(
            person=patient.person,
            group=group,
            assignment_scheme=assignment_scheme,
            object_id=self.id,
            target_object=self,
            default_assignee_group=default_assignee_group,
        )

    def get_due_date(self, event=None, as_of=None):
        """
        Get the due date specified by this task template, as of a certain date.
        This is either a fixed date
        or some number of days offset from another date (as_of or today)
        """
        if self.fixed_date:
            return self.fixed_date

        if self.fixed_month_day is not None:
            # Find the current year's instance of the month/day
            start_date = as_of or timezone.now()
            return date(start_date.year, self.fixed_month_day.month, self.fixed_month_day.day)

        if self.days_offset is None:
            return None

        if event and self.event_date_property:
            # Offset from an event date.
            # TODO: Don't assume that the content type is the event property name.
            event_object = getattr(event, self.event_date_property.reference_object_type_id.name)
            relative_date = getattr(event_object, self.event_date_property.reference_object_column)
        else:
            relative_date = as_of or timezone.now()

        # TODO: Should be able to configure rounding up or control elsewhere.
        date_after_offset: datetime = timezone.now()
        if self.days_offset_uses_calendar_days:
            date_after_offset = relative_date + timedelta(days=self.days_offset)
        else:
            date_after_offset = (relative_date + BDay(self.days_offset)).to_pydatetime()
        return max(timezone.now(), date_after_offset)

    class Meta(BaseModelV3.Meta):
        db_table = "task_collection_tasks"


class AutomatedTask(BaseModelV3):
    """Keeps track of tasks that were automatically created"""

    task = models.OneToOneField(Task, on_delete=models.CASCADE)
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    task_collection_task = models.ForeignKey(TaskCollectionTask, on_delete=models.SET_NULL, null=True)  # noqa: TID251
