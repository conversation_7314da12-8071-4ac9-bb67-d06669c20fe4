import logging
import os
import uuid
from typing import Type

from crontab import CronSlices
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.core import checks
from django.core.exceptions import ValidationError
from django.db import connection, models
from django.db.models import Q
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _

from firefly.modules.data_pipelines.utils import PipelineManager, delete_pipeline_schedule, schedule_pipeline
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin

logger = logging.getLogger(__name__)


def get_pipeline_folder(instance, filename):
    return os.path.join(
        "pipeline",
        str(instance.uuid),
        filename,
    )


def validate_schedule(value):
    if value and not CronSlices.is_valid(value):
        raise ValidationError([{"message": f"{value} is not a valid Unix cron schedule format"}])


def validate_handler(value):
    import firefly.modules.data_pipelines.pipelines as pipelines_module

    try:
        handler = getattr(pipelines_module, value)
        if not issubclass(handler, pipelines_module.PipelineV2):
            raise
    except Exception:
        raise ValidationError([{"message": f"`{value}` is not a valid pipeline handler function"}])


class PipelineRunStatus(models.TextChoices):
    INITIATED = "initiated", _("Initiated")
    IN_PROGRESS = "in-progress", _("In progress")
    SUCCEEDED = "succeeded", _("Succeeded")
    FAILED = "failed", _("Failed")
    RESOLVED = "resolved", _("Resolved")

    def __eq__(self, other: object) -> bool:
        return str(self) == str(other)


class PipelineDefinition(SaveHandlersMixin, BaseModelV3):
    """
    Defines a data pipeline, a job that transforms some data, that may be run on
    a schedule or on demand.
    """

    name = models.TextField(help_text="Human readable name.", unique=True)
    description = models.TextField(help_text="Human readable documentation.")
    # The `cron_job_unique_name` field is used to identify any cron jobs associated with this pipeline definition in
    # cronitor and kubernetes. It is automatically generated based on the definition's initial name and should be unique
    # and human-readable. This field SHOULD NOT be used to hardcode references to this pipeline definition in our code.
    cron_job_unique_name = models.TextField(
        help_text="Human readable, read-only cron job identifier.", unique=True, null=True, blank=True
    )
    schedule = models.TextField(
        help_text="Schedule in Unix cron format.", validators=[validate_schedule], null=True, blank=True
    )
    handler = models.TextField(help_text="Program to invoke when the pipeline runs.", validators=[validate_handler])
    kwargs = models.JSONField(help_text="Arguments to be passed to the handler.", default=dict, blank=True)

    def __str__(self):
        return f"{self.name} ({self.pk})"

    @classmethod
    def check(cls, **kwargs):
        """
        System check (runs at startup) to ensure that PipelineDefinitions are
        still referencing valid handlers.
        """

        errors = super().check(**kwargs)

        # In the event migrations have not yet instantiated this model, there is
        # no need to perform the check.
        if cls._meta.db_table not in connection.introspection.table_names():
            return errors

        for pipeline_definition in cls.objects.distinct("handler").iterator():
            try:
                validate_handler(pipeline_definition.handler)
            except ValidationError:
                errors.append(
                    checks.Error(
                        "PipelineDefinition found referencing invalid handler",
                        hint=f"Make sure the handler class '{pipeline_definition.handler}' is defined",
                        obj=pipeline_definition,
                        id="data_pipelines.E001",
                    )
                )

        return errors

    def pre_save_mutation(self, changed):
        if not self.cron_job_unique_name:
            name = slugify(self.name)
            self.cron_job_unique_name = name[:47] if len(name) > 47 else name

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        # Only delete the cron job if the pipeline had a schedule to begin with when the pipeline was deleted, or if the
        # schedule was removed
        if (self.schedule and self.deleted) or (not self.schedule and changed("schedule")):
            delete_pipeline_schedule(self)
        elif self.schedule:
            schedule_pipeline(self)

    def save(self, keep_deleted=False, **kwargs):
        import firefly.modules.data_pipelines.pipelines as pipelines_module

        self.full_clean()
        Pipeline = getattr(pipelines_module, self.handler)
        Pipeline.validate(self.kwargs)
        super().save(keep_deleted=keep_deleted, **kwargs)


class PipelineDefinitionArgument(BaseModelV3):
    """
    Represents a keyword argument of a PipelineDefinition whose value is a
    database object.
    """

    pipeline_definition = models.ForeignKey(
        help_text="Pipeline definition to which the argument applies.",
        to=PipelineDefinition,
        on_delete=models.CASCADE,
        related_name="relational_kwargs",
    )
    key = models.TextField(help_text="Machine-readable identifier for the argument.")
    value_content_type = models.ForeignKey(
        help_text="Type of argument value.",
        to=ContentType,
        on_delete=models.CASCADE,
        # Expand this list as needed.
        limit_choices_to=models.Q(app_label="file_transfer", model="connection")
        | models.Q(app_label="data_pipelines", model="pipelinedefinition"),
    )
    value_object_id = models.PositiveIntegerField(help_text="Primary key of argument value.")
    value_content_object = GenericForeignKey("value_content_type", "value_object_id")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["pipeline_definition", "key"], condition=Q(deleted=None), name="pipeline_definition_key_unique"
            )
        ]


class PipelineRun(SaveHandlersMixin, BaseModelV3):
    """
    Each pipeline execution status and details to
    restart the pipeline again in case of failure will be store here.
    """

    uuid = models.UUIDField(
        help_text="Universally unique identifier for the run.", unique=True, default=uuid.uuid4, editable=False
    )
    status = models.TextField(
        help_text="Whether the run succeeded, failed, etc.",
        choices=PipelineRunStatus.choices,
        default=PipelineRunStatus.INITIATED,
    )
    pipeline_definition = models.ForeignKey(
        help_text="Type of pipeline represented by this run.",
        to=PipelineDefinition,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    kwargs = models.JSONField(
        help_text=(
            "Arguments passed to the handler. "
            "If null, will be inherited from the pipeline definition kwargs and relational_kwargs."
        ),
        null=True,
        blank=True,
    )
    logs = models.JSONField(help_text="Semi-structured log output of the run.", null=True, blank=True)

    # DEPRECATED: Use pipeline_definition instead.
    pipeline = models.TextField(null=True, blank=True)
    # DEPRECATED: Use outputs instead.
    file = models.FileField(upload_to=get_pipeline_folder, null=True, blank=True)

    def pre_save_mutation(self, changed):
        from firefly.modules.data_pipelines.serializers import GenericPipelineDefinitionArgumentSerializer
        from firefly.modules.data_pipelines.utils import update_kwargs

        if self._state.adding:
            relational_kwargs = {
                kwarg.key: GenericPipelineDefinitionArgumentSerializer(instance=kwarg).data
                for kwarg in self.pipeline_definition.relational_kwargs.iterator()
            }
            kwargs = update_kwargs({}, self.pipeline_definition.kwargs, relational_kwargs, self.kwargs or {})
            self.kwargs = kwargs

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        if self.pipeline_definition is None:
            return
        if self.status == PipelineRunStatus.INITIATED:
            from firefly.modules.data_pipelines.tasks import process_pipeline_run

            process_pipeline_run.send(pipeline_run_id=self.id)

    def run(self):
        import firefly.modules.data_pipelines.pipelines as pipelines_module
        from firefly.modules.data_pipelines.serializers import GenericPipelineDefinitionArgumentSerializer

        pipeline_manager = PipelineManager(pipeline_run=self)
        try:
            Pipeline: Type[pipelines_module.PipelineV2] = getattr(pipelines_module, self.pipeline_definition.handler)
            kwargs = {}
            for key, val in self.kwargs.items():
                serializer = GenericPipelineDefinitionArgumentSerializer(data=val, model=PipelineDefinitionArgument)
                kwargs[key] = serializer.validated_data.value_content_object if serializer.is_valid() else val
            Pipeline(**kwargs).run(manager=pipeline_manager)
        except Exception as e:
            # Normally, we don't want to explicitly log exceptions, since Datadog will automatically parse the exception
            # along with the log. We change that rule here however because we also want these logs and exceptions to be
            # visible to our Data Ops team in Django Admin.
            pipeline_manager.exception("Failed to run pipeline (%s): %s", e.__class__.__name__, str(e))
            raise e
        finally:
            if not self.logs:
                self.logs = {}
            self.logs.update(pipeline_manager.get_logs())
            self.save(update_fields=["logs"])


class PipelineRunOutput(BaseModelV3):
    """
    Represents a structured output from a pipeline run.
    """

    pipeline_run = models.ForeignKey(
        help_text="Pipeline run to which this output applies.",
        to=PipelineRun,
        on_delete=models.CASCADE,
        related_name="outputs",
    )
    key = models.TextField(help_text="Machine-readable identifier for the output.")
    value_content_type = models.ForeignKey(
        help_text="Type of output value.",
        to=ContentType,
        on_delete=models.CASCADE,
        # Expand this list as needed.
        limit_choices_to=(
            models.Q(app_label="file_transfer", model__in=["download", "upload"])
            | models.Q(app_label="data_pipelines", model__in=["pipeline_run_file_output"])
        ),
    )
    value_object_id = models.PositiveIntegerField(help_text="Primary key of output value.")
    value_content_object = GenericForeignKey("value_content_type", "value_object_id")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["pipeline_run", "key"], condition=Q(deleted=None), name="pipeline_run_key_unique"
            ),
        ]


class PipelineRunFileOutput(BaseModelV3):
    """
    Represents a generic file output of a pipeline run.
    """

    uuid = models.UUIDField(
        help_text="Universally unique identifier for the file output.", unique=True, default=uuid.uuid4, editable=False
    )
    file = models.FileField(upload_to=get_pipeline_folder)


class PipelineRunFileInput(SaveHandlersMixin, BaseModelV3):
    """
    Represents a generic file input to a `PipelineRun`. When a `PipelineRunFileInput` object is created, it triggers
    the corresponding `PipelineRun` defined by its `pipeline_definition` argument. This can be used to trigger new
    `PipelineRun objects` from existing runs by outputting a `PipelineRunFileInput`.
    """

    uuid = models.UUIDField(
        help_text="Universally unique identifier for the file input.", unique=True, default=uuid.uuid4, editable=False
    )
    file = models.FileField(upload_to=get_pipeline_folder, help_text="File used as input to trigger the PipelineRun.")
    filename = models.TextField(help_text="The name of the file used as input.", null=True, blank=True)
    pipeline_definition = models.ForeignKey(
        PipelineDefinition, on_delete=models.CASCADE, help_text="PipelineDefinition used to trigger the PipelineRun"
    )
    file_kwarg_key = models.TextField(help_text="Key for file input in the PipelineDefinition kwargs")
    kwargs = models.JSONField(
        help_text="Additional arguments passed to the PipelineDefinition kwargs.", null=True, blank=True
    )

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.data_pipelines.serializers import GenericPipelineDefinitionArgumentSerializer

        file_kwarg = GenericPipelineDefinitionArgumentSerializer(instance=self).data
        pipeline_definition_kwargs = {
            self.file_kwarg_key: file_kwarg,
            **(self.kwargs or {}),
        }
        PipelineRun.objects.create(pipeline_definition=self.pipeline_definition, kwargs=pipeline_definition_kwargs)
