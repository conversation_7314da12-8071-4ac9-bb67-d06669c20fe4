# Data Pipelines

The data pipelines module provides a robust, auditable framework for securely exchanging data with third-party systems including carrier partners, employers, vendors, and internal data processing workflows. It enables automated data transformation, validation, and transfer while maintaining comprehensive audit trails and error handling.

## Overview

Data pipelines serve as the primary integration layer for external data exchange at Firefly. They handle critical business processes including:
- Insurance eligibility file processing from carriers
- Quality reporting data uploads to partners
- Member roster synchronization
- Referral data transmission to vendors
- Internal data transformation and migration workflows

## Pipeline Architecture

- **PipelineDefinition**: Configurable pipeline templates with scheduling and validation
- **PipelineRun**: Individual execution instances with status tracking and logging
- **PipelineStage**: Modular processing components that can be chained together
- **Connection**: Secure SFTP/file transfer configurations for external systems

## Pipeline Types

1. **SFTP Download Pipelines**: Download files from external servers and process them
2. **Upload Pipelines**: Generate data and upload to external systems
3. **Transform Pipelines**: Process files between different formats (Excel to CSV, etc.)
4. **Database Integration Pipelines**: Load external data into Lucian database
5. **Multi-stage Pipelines**: Complex workflows combining multiple operations

## Technical Implementation

### Pipeline Stages

Pipeline stages are modular components that process data streams sequentially. Each stage receives a binary IO stream as input, processes the data, and returns a new binary IO stream as output.

#### Buffered IO Stream Processing

Pipeline stages use buffered IO streams for memory-efficient processing of large files. The abstract `PipelineStage` class provides a framework for:

- **Input Stream Handling**: Stages receive a `BinaryIO` input stream from the previous stage
- **Output Stream Creation**: Each stage creates a new `BytesIO` output stream for processed data
- **Buffered Processing**: Data is processed in chunks to handle large files without memory issues
- **Stream Chaining**: Output from one stage becomes input for the next stage in the pipeline
- **Resource Management**: Automatic cleanup of streams after pipeline execution

The `execute_chunks()` method provides a standard implementation for buffered processing:

```python
def execute_chunks(self, chunk_processor_func, chunk_size: int = 8192) -> BinaryIO:
    """
    Process input stream in chunks and write to output stream.
    """
    if self.input_stream:
        while True:
            chunk = self.input_stream.read(chunk_size)
            if not chunk:
                break
            processed_chunk = chunk_processor_func(chunk)
            self.output_stream.write(processed_chunk)

    self.output_stream.seek(0)
    return self.output_stream
```

#### Stages Without Input or Output

Some pipeline stages don't follow the standard input/output stream pattern:

- **Source Stages**: Generate data without requiring input (e.g., `DownloadStage`, `SnowflakeStage`)
- **Sink Stages**: Consume data without producing output (e.g., `UploadStage`, `LucianDBStage`)
- **Side Effect Stages**: Perform actions based on pipeline state (e.g., status updates, notifications)

**Source Stage Example**:
```python
class DataSourceStage(PipelineStage):
    def execute(self) -> BinaryIO:
        # Generate data without input stream
        data = self.fetch_data_from_external_source()
        self.output_stream.write(data)
        self.output_stream.seek(0)
        return self.output_stream
```

**Sink Stage Example**:
```python
class DataSinkStage(PipelineStage):
    def execute(self) -> BinaryIO:
        # Process input stream without producing output
        if self.input_stream:
            data = self.input_stream.read()
            self.save_data_to_destination(data)

        # Return empty stream
        return self.output_stream
```

## Code Examples

### Creating a Custom Pipeline Stage

```python
from firefly.modules.data_pipelines.stages import PipelineStage

class FirstStage(PipelineStage):
    def __init__(self, some_param: str):
        super().__init__()
        self.some_param = some_param

    def execute(self) -> BinaryIO:
        # Process data and write to output stream
        data = f"Processed data with {self.some_param}\n"
        self.output_stream.write(data.encode())
        self.output_stream.seek(0)
        return self.output_stream
```

### Creating a Custom Pipeline

```python
from firefly.modules.data_pipelines.pipelines import PipelineV2

class CustomPipeline(PipelineV2):
    schema = "custom_pipeline_schema"

    def __init__(self, *, param1: str, param2: int, **kwargs):
        super().__init__(**kwargs)
        self.stages = [
            FirstStage(some_param=param1),
            # Add more stages as needed
        ]
```

### Pipeline Schema Definition

```json
{
  "type": "object",
  "properties": {
    "param1": {
      "type": "string",
      "description": "Description of parameter 1"
    },
    "param2": {
      "type": "integer",
      "description": "Description of parameter 2",
      "default": 10
    }
  },
  "required": ["param1"]
}
```

### Running a Pipeline

```python
from firefly.modules.data_pipelines.models import PipelineDefinition, PipelineRun

# Create pipeline run
pipeline_def = PipelineDefinition.objects.get(name="My Custom Pipeline")
pipeline_run = PipelineRun.objects.create(
    pipeline_definition=pipeline_def,
    kwargs={"param1": "test_value", "param2": 20}
)

# Execute pipeline
pipeline_run.run()
```

## Core Stage Types

**DownloadStage**: Downloads files from SFTP servers
- Handles secure connections and file retrieval
- Automatic retry logic and error handling
- Integration with Django Upload/Download models

**UploadStage**: Uploads processed data to external systems
- SFTP upload with verification
- Dry-run capability for testing
- Automatic file size validation

**LucianDBStage**: Loads data into Django models
- Configurable field mapping from external data
- Bulk update/create operations with error handling
- Support for complex data transformations

**ExcelToCSVStage**: Converts Excel files to CSV format
- Tab-specific processing with configurable headers
- Custom delimiter and column selection
- Password-protected file support

**S3UploadStage**: Uploads files to internal S3 storage
- Automatic path generation and file naming
- Integration with AWS services
- Backup and archival capabilities

**SnowflakeStage**: Executes SQL queries in Snowflake and converts results to CSV
- Configurable warehouse and delimiter settings
- Direct database query execution
- Support for mock data in testing environments

## Current Production Pipelines

**SFTPDownloadToLucianDBPipeline**: Downloads files from SFTP and loads into database
- Used for insurance eligibility files and member rosters
- Configurable field mapping and data validation
- Automatic error handling and retry logic

**MHReferralsUploadPipeline**: Generates and uploads referral data
- Creates CSV files from Django models
- Updates transmission status after successful upload
- Integration with vendor SFTP systems

**TransformExcelToS3Pipeline**: Processes Excel files to CSV format
- Handles password-protected archives
- Tab-specific processing with custom formatting
- Automatic file naming and S3 storage

**SnowflakeToCSVUploadPipeline**: Exports Snowflake data to external systems
- Direct database query execution
- CSV formatting and SFTP upload
- Quality reporting and analytics data transfer

**ReadFileToLucianDBPipeline**: Processes archived files and loads into database
- ZIP file extraction with password support
- Configurable field mapping for database integration
- Support for complex data transformations

**ReadFileToS3Pipeline**: Extracts files from archives and uploads to S3
- Password-protected archive handling
- Automatic file naming and path generation
- Integration with AWS services

## Pipeline Definitions and Scheduling

**PipelineDefinition Model**:
- Configurable pipeline templates with JSON schema validation
- Cron-based scheduling with automatic job management
- Integration with Kubernetes and Cronitor for monitoring
- Support for both Looker-based and direct database pipelines

**PipelineRun Model**:
- Individual execution tracking with status management
- Comprehensive logging and error capture
- File input/output tracking with S3 integration
- Retry and rerun capabilities for failed executions

## Pipeline Execution and Management

### Pipeline Execution Flow
1. **Initialization**: Pipeline definition validation and parameter setup
2. **Stage Execution**: Sequential processing through configured stages
3. **Error Handling**: Automatic retry logic and failure notification
4. **Cleanup**: Resource cleanup and status updates
5. **Audit Logging**: Comprehensive execution tracking and file management

### File Processing
- **Buffered Streaming**: Memory-efficient processing of large files
- **Format Conversion**: Excel to CSV, JSON transformation, data validation
- **Archive Handling**: ZIP file extraction with password support
- **Data Mapping**: Configurable field mapping for database integration

### Connection Management
- **SFTP Connections**: Secure file transfer with credential management
- **Database Connections**: Snowflake, PostgreSQL integration
- **S3 Integration**: File storage and archival capabilities
- **Retry Logic**: Automatic reconnection and error recovery

## Configuration

Pipeline definitions are configured through Django admin or management commands with JSON schema validation. Pipeline arguments support multiple data types including primitives, model references, file references, and JSON objects. Scheduling is handled through Kubernetes cron jobs with Cronitor monitoring.

## Management Commands

**run_pipeline_v2**: Execute pipeline definitions by ID
- Supports all configured pipeline types
- Parameter validation and error handling
- Integration with scheduling system

**rerun_pipeline**: Re-execute failed or completed pipeline runs
- Rerun existing pipeline runs by ID
- Preserves original execution context and parameters
- Useful for debugging and recovery scenarios

**redeploy_data_jobs**: Update scheduled pipeline jobs
- Synchronizes pipeline definitions with Kubernetes cron jobs
- Updates Cronitor monitoring configuration
- Handles job deletion and recreation

**create_pipeline_definitions_from_json**: Bulk pipeline creation
- Import pipeline definitions from JSON configuration
- Validation and error reporting
- Support for complex pipeline hierarchies

## Pipeline Execution

Pipeline execution is handled through the Dramatiq task queue system:

1. **Pipeline Creation**: Create `PipelineRun` instance with status "initiated"
2. **Task Queuing**: Submit to `data_pipeline_run` queue for processing
3. **Execution**: Sequential stage processing with comprehensive logging
4. **Status Updates**: Real-time status tracking (initiated → in-progress → succeeded/failed)
5. **Cleanup**: Resource cleanup and final status reporting

## Error Handling and Recovery

Failed pipelines can be automatically retried with configurable retry limits and backoff strategies. Manual intervention is supported with access to original files and execution parameters for step-by-step debugging and reprocessing.

## Development and Testing

### Local Development

**SFTP Testing**: Dedicated staging environment on `sftp.firefly.health` with isolated folder structure for safe testing without production impact.

**Dry Run Mode**: Test pipeline execution without external effects - upload stages create database entries without SFTP transfer, download stages use local file simulation.

### Pipeline Development Workflow

1. **Schema Definition**: Create JSON schema for pipeline parameters
2. **Stage Implementation**: Develop and test individual pipeline stages
3. **Pipeline Assembly**: Combine stages into complete pipeline workflow
4. **Testing**: Local testing with dry run and staging SFTP
5. **Deployment**: Production deployment with monitoring and alerting

### Best Practices

- **Stage Design**: Create reusable, modular stages with single responsibility
- **Configuration Management**: Use pipeline definitions for all parameters, avoid hardcoded values
- **Monitoring**: Comprehensive observability with real-time status tracking and alerting
