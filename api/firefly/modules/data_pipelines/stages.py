import abc
import codecs
import csv
import os
import re
from datetime import datetime
from io import BytesIO
from typing import BinaryIO, Optional

import boto3
import pandas as pd
import pyzipper
from boto3.s3.transfer import TransferConfig
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import File

import firefly.modules.data_pipelines.helpers as helpers_module
from firefly.core.alias.models import AliasMapping
from firefly.core.services.aws_secrets_manager.utils import get_aws_secret
from firefly.core.services.flume.parsers import parse_eligibility_v2
from firefly.core.services.snowflake.utils import sql_query_cursor
from firefly.modules.data_pipelines.models import PipelineRunFileInput
from firefly.modules.data_pipelines.types import PipelineRunFileInputPropsListType, RemoteFileToLucianDBMappingType
from firefly.modules.data_pipelines.utils import PipelineManager
from firefly.modules.file_transfer.models import Connection, Download, Upload
from firefly.modules.file_transfer.utils import get_filenames_and_checksums_from_sftp
from firefly.modules.firefly_django.utils import lazy_bulk_fetch_objects
from firefly.modules.referral.constants import MemorialHermannConfig
from firefly.modules.referral.utils.referral_utils import fetch_mh_referrals_list, update_mh_referrals_status_to_success

BUCKET = settings.AWS_S3_BUCKET_NAME
BUFFER_SIZE = 2048


# ======================================================================================================================
# BASE PIPELINE STAGES
# --------------------
# These are our abstract data pipeline stages and base pipeline stages. As we migrate from using `Pipeline` to
# `PipelineV2`, we should refrain from creating new, custom pipeline stages. Long-term, we may have more complicated
# needs that need to be addressed, but we should always try to use just these base stages wherever possible.
# ======================================================================================================================


class PipelineStage(abc.ABC):
    """
    Abstract stage in data pipeline to read from input stream, process the data and then write to
    output stream in a buffered fashion. Pipeline stages should be strung together in a Pipeline
    such that the input stream for a stage is the output stream from the previous stage.
    """

    def __init__(self):
        self.input_stream: Optional[BinaryIO] = None
        self.output_stream: BinaryIO
        self.dry_run: bool

    def run(self, manager: PipelineManager, input_stream: Optional[BinaryIO] = None, dry_run: bool = False) -> BinaryIO:
        """
        Wrapper method to run pipeline processing, then cleanup.
        """
        self.setup(manager, input_stream, dry_run)
        try:
            return self.execute()
        finally:
            self.cleanup()

    def setup(self, manager: PipelineManager, input_stream: Optional[BinaryIO], dry_run: bool = False):
        """
        Hook to set up input and output data streams.
        """
        if input_stream:
            self.input_stream = input_stream
            # Create empty output stream to write to
        self.output_stream = BytesIO()
        self.manager = manager
        self.dry_run = dry_run

    @abc.abstractmethod
    def execute(self) -> BinaryIO:
        """
        The main entrypoint for processing the data. Should either implement a custom function or
        call a pre-built implementation such as `execute_chunks`.
        """
        pass

    def execute_chunks(self) -> BinaryIO:
        """
        Read buffered input stream and process data in chunks, writing each chunk to the buffered
        output stream. If needed, this can be called from the main `execute` entrypoint.
        """
        if self.input_stream:
            while True:
                data = self.input_stream.read(BUFFER_SIZE)
                if not data:
                    break
                data = self.process(data)
                # Write processed chunk of data to buffered output stream
                if data:
                    self.output_stream.write(data)
        return self.output_stream

    def process(self, chunk: bytes) -> bytes:
        """
        Hook to process chunk of input stream. This is the main entrypoint for processing a chunk of
        data in a buffered stream. Should be designed to process data in parts.
        """
        return chunk

    def cleanup(self):
        """
        Cleanup step at the end of data processing. Used to close input stream and seek to beginning
        of output stream before it is passed onto the next step.
        """
        if self.input_stream:
            self.input_stream.close()
        if self.output_stream:
            self.output_stream.seek(0)

    def write_input_to_output(self):
        """
        Helper method to write data from the input stream to the output stream.
        """
        if self.input_stream:
            self.input_stream.seek(0)
        if self.output_stream:
            self.output_stream.write(self.input_stream.read())


class S3Stage(PipelineStage, abc.ABC):
    """
    Abstract stage for transferring files to and from S3. Typically, interactions with S3 should be
    handled instead by a `Download` or `Upload` object.
    """

    def __init__(self):
        super().__init__()
        self.transfer_config = TransferConfig()
        self.s3 = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_session_token=settings.AWS_SESSION_TOKEN,
            region_name=settings.AWS_REGION,
        )


class S3UploadStage(S3Stage):
    """
    Pipeline stage to upload streamed data to AWS S3 file. Can optionally define a destination path
    on a remote server to which the file will ultimately be uploaded, which will be used for
    defining the filepath in S3. Note that typically, these types of interactions with S3 should be
    handled instead by an `Upload` object.
    """

    def __init__(self, destination_path: Optional[str] = None, filename: Optional[str] = None):
        super().__init__()
        self.destination_path = destination_path
        self.filename = filename

    def execute(self) -> BinaryIO:
        if self.input_stream:
            # Get filepath to upload
            filepath = os.path.join(self.destination_path, self.filename or "") if self.destination_path else None
            # Upload input stream to S3 file
            self.write_input_to_output()
            self.input_stream.seek(0)
            self.s3.upload_fileobj(self.input_stream, BUCKET, filepath or self.filename, Config=self.transfer_config)
        return self.output_stream


class UploadStage(PipelineStage):
    """
    Pipeline stage to upload streamed data to an SFTP server and save uploaded data to file in S3.
    Data is created as an `Upload` object in Django, which automatically handles saving to S3 and
    pushing to the SFTP server when saved. This should be the primary stage used to transfer data
    from an SFTP server to Django.
    """

    def __init__(
        self,
        connection: Optional[Connection],
        destination_path: Optional[str] = None,
        verify_size: bool = True,
        filename: Optional[str] = None,
    ):
        super().__init__()
        self.connection = connection
        self.destination_path = destination_path
        self.verify_size = verify_size
        self.filename = filename

    def execute(self) -> BinaryIO:
        if self.input_stream and self.connection:
            input_file = File(self.input_stream, name=self.filename)
            upload = Upload.objects.create(
                connection=self.connection,
                destination_path=self.destination_path,
                file=input_file,
                name=self.filename,
                dry_run=self.dry_run,
                # verify_size is added as a property to Upload; mypy is unable to infer this property
                verify_size=self.verify_size,  # type: ignore[misc]
            )
            self.manager.add_output_object(key="upload", output=upload)
            self.write_input_to_output()
        return self.output_stream


class DownloadStage(PipelineStage):
    """
    Pipeline stage to download streamed data from an SFTP server and save downloaded data to file in
    S3. Data is created as a `Download` object in Django, which automatically handles pulling from
    the SFTP server and saving to S3 when saved. This should be the primary stage used to transfer
    data from an SFTP server to Django.
    """

    def __init__(
        self, connection: Optional[Connection], source_path: Optional[str] = None, filename: Optional[str] = None
    ):
        super().__init__()
        self.connection = connection
        self.source_path = source_path
        self.filename = filename

    def execute(self) -> BinaryIO:
        self.manager.info("Starting execution of download stage. Source path: %s", self.source_path)
        if self.connection:
            input_name = self.filename if self.filename else ""
            source_path, filename = os.path.split(input_name)
            download = Download.objects.create(
                connection=self.connection, source_path=source_path or self.source_path, name=filename
            )
            self.manager.add_output_object(key="download", output=download)
            self.output_stream = download.file.open()
        self.manager.info("Completed download stage. Source path: %s", self.source_path)
        return self.output_stream


class MultiDownloadStage(PipelineStage):
    def __init__(
        self, connection: Connection, pipeline_run_file_input_props: PipelineRunFileInputPropsListType, **kwargs
    ):
        super().__init__()
        self.connection = connection
        self.pipeline_run_file_input_props = pipeline_run_file_input_props
        self.kwargs = kwargs

    def execute(self) -> BinaryIO:
        files = get_filenames_and_checksums_from_sftp(self.connection)
        files_not_found = set()
        for props in self.pipeline_run_file_input_props:
            filename = props["source_filename"]
            filename_helper = props["source_filename_helper"]
            filename_helper_kwargs = props.get("source_filename_helper_kwargs")
            source_path = props.get("source_path", "")
            file_kwarg_key = props["pipeline_definition_file_kwarg_key"]
            pipeline_definition_key = props["pipeline_definition_key"]
            pipeline_definition_kwargs = props.get("pipeline_definition_kwargs")
            filename_pattern = self._get_filename_pattern(filename, filename_helper, filename_helper_kwargs)
            file_found = False
            for file in files.values():
                path, name = os.path.split(file)
                pipeline_definition = self.kwargs.get(pipeline_definition_key)
                if path == source_path and re.match(filename_pattern, name) and pipeline_definition:
                    file_found = True
                    download = Download.objects.create(connection=self.connection, source_path=source_path, name=name)
                    self.manager.add_output_object(key="download", output=download)
                    PipelineRunFileInput.objects.create(
                        file=download.file,
                        filename=name,
                        file_kwarg_key=file_kwarg_key,
                        pipeline_definition=pipeline_definition,
                        kwargs=pipeline_definition_kwargs,
                    )
            if not file_found:
                files_not_found.add(filename_pattern)
        if files_not_found:
            raise FileNotFoundError(f"Could not find files matching patterns {files_not_found}")
        return self.output_stream

    @staticmethod
    def _get_filename_pattern(filename: str, filename_helper: str, filename_helper_kwargs: Optional[dict]):
        filename_helper_kwargs = filename_helper_kwargs or {}
        filename_helper_func = getattr(helpers_module, filename_helper)
        return filename_helper_func(filename, **filename_helper_kwargs)


class ConvertQuerySetToCSVStreamStage(PipelineStage, abc.ABC):
    """
    Pipeline stage to convert a data query (for example, the results of a Django queryset or a
    Snowflake SQL query) into a CSV or similar delimited file. This file is output as a stream that
    can be passed to the next pipeline stage for processing.
    """

    def __init__(self, delimiter: str = ",", max_objects: int = 50):
        super().__init__()
        self.delimiter = delimiter
        self.max_objects = max_objects

    def sql_cursor_to_csv(self, cursor):
        """
        Convert the results managed by a SQL cursor to a CSV stream.
        """
        field_names = [col.name for col in cursor.description]
        self._to_csv(cursor, field_names, batched=False)

    def django_queryset_to_csv(self, queryset, order_by, field_names):
        """
        Convert the results managed by a Django queryset to a CSV stream.
        """
        fetcher = lazy_bulk_fetch_objects(self.max_objects, queryset.count(), lambda: queryset.order_by(order_by))
        self._to_csv(fetcher, field_names)

    def _to_csv(self, data, field_names, batched=True):
        stream_writer = codecs.getwriter("utf-8")
        stream = stream_writer(self.output_stream)
        writer = csv.DictWriter(stream, delimiter=self.delimiter, fieldnames=field_names)
        writer.writeheader()
        for batch in data:
            batch = list(batch) if batched else [batch]
            writer.writerows(batch)


class SnowflakeStage(ConvertQuerySetToCSVStreamStage):
    """
    Pipeline stage to execute a SQL query in Snowflake and parse the results into a CSV or similar
    delimited file. Takes a SQL query as input, but also allows selecting the delimiter and
    Snowflake warehouse to use.
    """

    def __init__(self, query: str, warehouse: str = "LUCIAN", delimiter: str = ",", mock_fields: Optional[list] = None):
        super().__init__(delimiter)
        self.query = query
        self.warehouse = warehouse
        self.mock_fields = mock_fields

    def execute(self) -> BinaryIO:
        if not self.mock_fields:
            with sql_query_cursor(self.query, warehouse=self.warehouse) as cursor:
                self.sql_cursor_to_csv(cursor)
        else:
            import uuid

            data = [{field_name: str(uuid.uuid4()) for field_name in self.mock_fields} for _ in range(10)]
            self._to_csv(data, self.mock_fields, batched=False)
        return self.output_stream


class LucianDBStage(PipelineStage):
    def __init__(self, model_mapping: RemoteFileToLucianDBMappingType, delimiter: str = ","):
        super().__init__()
        self.model_mapping: RemoteFileToLucianDBMappingType = model_mapping
        self.delimiter = delimiter

    def execute(self) -> BinaryIO:
        chunk_size = 100
        failed_rows = []
        if self.input_stream:
            with pd.read_csv(
                self.input_stream, sep=self.delimiter, chunksize=chunk_size, dtype=str, encoding="ISO-8859-1"
            ) as reader:
                for i, chunk in enumerate(reader):
                    self.manager.info("Started processing chunk number %s", i)
                    if i == 0:
                        chunk.columns = chunk.columns.str.strip()
                    chunk = chunk.fillna("")
                    for j, row in chunk.iterrows():
                        row_number = j + 1
                        try:
                            self.manager.info("Started processing row number %s", row_number)
                            self.process(row)
                            self.manager.info("Completed processing row number %s", row_number)
                        except Exception as e:
                            failed_rows.append(row_number)
                            self.manager.exception("Exception in row number %d: %s", row_number, e)
            self.write_input_to_output()
        if failed_rows:
            # Fail overall pipeline if at least one row was not processed properly
            raise Exception(f"Failed to process rows {failed_rows}")
        return self.output_stream

    def process(self, row):
        row = row.replace({"": None})
        for model_mapping in self.model_mapping:
            Model = apps.get_model(model_mapping["app"], model_mapping["model"])
            uniques = {}
            for field, args in model_mapping["unique_fields"].items():
                value = self._get_field(row, args)
                if value:
                    uniques[field] = value
            defaults = {}
            for field, args in model_mapping["default_fields"].items():
                value = self._get_field(row, args)
                if value:
                    defaults[field] = value
            Model.objects.update_or_create(**uniques, defaults=defaults)

    @staticmethod
    def _get_field(row, args):
        source_field = args.get("source_field")
        source_field_list = args.get("source_field_list")
        default_value = args.get("default_value")
        related_app = args.get("related_app")
        related_model = args.get("related_model")
        related_lookup_key = args.get("related_lookup_key")
        related_lookup_value = args.get("related_lookup_value")
        alias_name = args.get("alias_name")
        field_type = args.get("field_type")
        date_format = args.get("date_format")
        # The model field is an alias mapping
        if source_field and related_app and related_model and alias_name:
            RelatedModel = apps.get_model(related_app, related_model)
            alias_mapping = AliasMapping.get_alias_mapping_for_alias(
                alias_id=row[source_field],
                alias_name=alias_name,
                content_type=ContentType.objects.get_for_model(RelatedModel),
            )
            return alias_mapping.content_object
        # The model field exists in the CSV
        elif source_field:
            # Format boolean
            if field_type == "boolean":
                value = row[source_field].lower() if isinstance(row[source_field], str) else row[source_field]
                return value in (1, "true", "t")
            # Format date to ISO
            elif date_format:
                return datetime.strptime(row[source_field], date_format)
            return row[source_field]
        # The model field is an array of field values. Only include fields from the CSV if a value exists.
        elif source_field_list:
            return [row[source_field] for source_field in source_field_list if row[source_field]]
        # The model field is a foreign key
        elif related_app and related_model and related_lookup_key and related_lookup_value:
            RelatedModel = apps.get_model(related_app, related_model)
            return RelatedModel.objects.get(**{related_lookup_key: row[related_lookup_value]})
        # The model field is not in the CSV, but has a default value
        elif default_value:
            return default_value
        return None


class EligibilityStage(LucianDBStage):
    def __init__(self, delimiter: str = ","):
        super().__init__([], delimiter)

    def process(self, row):
        row = row.replace({"": None})
        parse_eligibility_v2(row)


class ExcelToCSVStage(PipelineStage):
    def __init__(
        self, tab: str, header_rows: list[int], delimiter: str = ",", output_columns: Optional[list[str]] = None
    ):
        super().__init__()
        self.tab = tab
        self.header_rows = header_rows
        self.delimiter = delimiter
        self.output_columns = output_columns

    def execute(self) -> BinaryIO:
        if self.input_stream:
            df = pd.read_excel(self.input_stream, sheet_name=self.tab, header=self.header_rows)
            df.columns = self.output_columns or df.columns.values
            df.to_csv(self.output_stream, sep=self.delimiter)
        return self.output_stream


class ReadFileStage(PipelineStage):
    def __init__(
        self,
        file: PipelineRunFileInput,
        input_filename: Optional[str] = None,
        zip_password_secret_name: Optional[str] = None,
    ):
        super().__init__()
        self.file = file.file
        self.input_filename = input_filename
        self.zip_password = self._get_zip_password(zip_password_secret_name)

    def execute(self) -> BinaryIO:
        # Check if zip file
        stream: BinaryIO
        if pyzipper.is_zipfile(self.file) and self.input_filename:
            with pyzipper.AESZipFile(self.file, "r") as zipper:
                stream = zipper.open(self.input_filename, pwd=self.zip_password)
        else:
            stream = self.file.open()
        self.output_stream = stream
        return self.output_stream

    @staticmethod
    def _get_zip_password(password_secret_name):
        password = get_aws_secret(password_secret_name) if password_secret_name else None
        return password.encode() if password else None


# ======================================================================================================================
# CUSTOM PIPELINE STAGES
# ----------------------
# These pipeline stages should be deprecated and replaced with base stages if possible. Known exceptions to this include
# claims and eligibility data ingestion.
# ======================================================================================================================


class MHReferralsCSV(ConvertQuerySetToCSVStreamStage):
    """
    Generates an output stream containing all the referrals that need to be sent to the MH
    """

    def __init__(self):
        super().__init__()

    def execute(self):
        referrals = fetch_mh_referrals_list()
        try:
            self._to_csv(referrals, MemorialHermannConfig.CSV_HEADERS, False)
        except Exception as e:
            self.manager.exception("MH referrals: Failed to convert referrals into CSV")
            raise e
        return self.output_stream


class MHReferralsUpdateTransmissionStatus(ConvertQuerySetToCSVStreamStage):
    """
    Updates all the refrerrals which are in_transmission to success
    """

    def execute(self):
        update_mh_referrals_status_to_success()
        return


class HeartbeatStage(PipelineStage):
    def execute(self) -> BinaryIO:
        self.manager.info("heartbeat")
        return self.output_stream
