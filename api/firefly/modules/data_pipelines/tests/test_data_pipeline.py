import zipfile
from io import BytesIO
from unittest import mock

import pandas as pd
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.data_pipelines.factories import PipelineRunFactory, PipelineRunFileInputFactory
from firefly.modules.data_pipelines.models import PipelineRunFileOutput
from firefly.modules.data_pipelines.stages import (
    DownloadStage,
    ExcelToCSVStage,
    ReadFileStage,
    S3UploadStage,
    UploadStage,
)
from firefly.modules.data_pipelines.tests.pipeline_mock import MockS3Client, MockSFTPClient
from firefly.modules.data_pipelines.utils import PipelineManager
from firefly.modules.file_transfer.factories import ConnectionFactory
from firefly.modules.file_transfer.models import AuthenticationMethod, Connection, Download, TransferStatus, Upload
from firefly.modules.file_transfer.utils import get_filenames_and_checksums_from_sftp


class DataPipelineStagesTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports
        from firefly.core.tests.utils import reset_context_to_luci_user

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.connection = Connection.objects.create(
            title="test",
            host="localhost",
            port=22,
            authentication_method=AuthenticationMethod.PASSWORD,
            username="testuser",
            password_secret_name="password",  # pragma: allowlist secret
        )
        cls.connection_bcbs = Connection.objects.create(
            title="test 2",
            host="localhost",
            port=22,
            authentication_method=AuthenticationMethod.PASSWORD,
            username="testuser",
            password_secret_name="password",  # pragma: allowlist secret
        )
        cls.pipeline_run = PipelineRunFactory()
        cls.manager = PipelineManager(pipeline_run=cls.pipeline_run)

        # Cache file content to avoid repeated file reads
        cls._cached_excel_files = {}
        excel_files = [
            "bcbs_test_quality_report.xlsx",
            "bcbs_test_quality_report.xls",
            "bcbs_test_quality_report_2024.xlsx",
        ]
        for filename in excel_files:
            with open(f"firefly/modules/data_pipelines/fixtures/{filename}", "rb") as f:
                cls._cached_excel_files[filename] = f.read()

        # Cache zip file content
        with open("firefly/modules/data_pipelines/fixtures/zip_test_file.zip", "rb") as f:
            cls._cached_zip_content = f.read()

    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.file = None
        self._refresh_file()

    @mock.patch("firefly.modules.data_pipelines.stages.TransferConfig")
    @mock.patch("firefly.modules.data_pipelines.stages.boto3.client")
    def test_s3_upload_stage(self, mock_s3, _mock_transfer_config):
        mock_s3.return_value = MockS3Client("s3")
        stage = S3UploadStage(destination_path="/test/", filename=self.file.name)
        stream = self.file.open(mode="rb")
        self._assert_files_match(stage, stream)

    @mock.patch("firefly.modules.file_transfer.utils.get_sftp_client")
    def test_upload_stage(self, mock_sftp_client):
        mock_sftp_client.return_value.__enter__.return_value = MockSFTPClient()
        stage = UploadStage(connection=self.connection, filename=self.file.name)
        with self.file.open(mode="rb") as stream:
            output_stream = stage.run(input_stream=stream, manager=self.manager)
        self._refresh_file()
        with self.file.open(mode="rb") as original_file:
            output_read = output_stream.read()
            original_read = original_file.read()
            self.assertEqual(output_read, original_read)
        uploads = Upload.objects.filter(file__endswith=self.file.name)
        self.assertEqual(uploads.count(), 1)
        upload = uploads.first()
        output_stream.seek(0)
        with upload.file.open(mode="rb") as upload_file:
            self.assertEqual(output_stream.read(), upload_file.read())

    @mock.patch("firefly.modules.file_transfer.utils.upload_file")
    def test_upload_without_verify_stage(self, mock_upload):
        stage = UploadStage(connection=self.connection, verify_size=False, filename=self.file.name)
        with self.file.open(mode="rb") as stream:
            output_stream = stage.run(input_stream=stream, manager=self.manager)
        self._refresh_file()
        with self.file.open(mode="rb") as original_file:
            output_read = output_stream.read()
            original_read = original_file.read()
            self.assertEqual(output_read, original_read)
        uploads = Upload.objects.filter(file__endswith=self.file.name)
        self.assertEqual(uploads.count(), 1)
        upload = uploads.first()
        output_stream.seek(0)
        mock_upload.assert_called_with(
            upload.file, upload.name, upload.destination_path, upload.connection, upload.dry_run, False
        )
        with upload.file.open(mode="rb") as upload_file:
            self.assertEqual(output_stream.read(), upload_file.read())

    @mock.patch("firefly.modules.file_transfer.utils.get_sftp_client")
    def test_download_stage(self, mock_sftp_client):
        mock_sftp_client.return_value.__enter__.return_value = MockSFTPClient()
        stage = DownloadStage(connection=self.connection, filename=self.file.name)
        output_stream = stage.run(manager=self.manager)
        with self.file.open(mode="rb") as original_file:
            self.assertTrue(output_stream.read().startswith(original_file.read()))
        downloads = Download.objects.filter(file__endswith=self.file.name)
        self.assertEqual(downloads.count(), 1)
        download = downloads.first()
        output_stream.seek(0)
        with download.file.open(mode="rb") as download_file:
            self.assertTrue(output_stream.read().startswith(download_file.read()))

    def _refresh_file(self):
        if not self.file or not self.file.readable():
            self.file = SimpleUploadedFile("upload.pdf", b"content", content_type="application/pdf")
        assert self.file.readable()

    def _assert_files_match(self, stage, input_stream):
        output_stream = stage.run(input_stream=input_stream, manager=self.manager)
        self._refresh_file()
        with self.file.open(mode="rb") as original_file:
            self.assertTrue(output_stream.read().startswith(original_file.read()))
        output_stream.close()

    def test_read_file_stage(self):
        with open("firefly/modules/data_pipelines/fixtures/zip_test_file.zip", "rb") as test_zip:
            content_file = ContentFile(test_zip.read())
        pipeline_run_file_input = PipelineRunFileInputFactory()
        pipeline_run_file_input.file.save("zip_test_file.zip", content_file)
        stage = ReadFileStage(file=pipeline_run_file_input, input_filename="test_file.txt")
        output_stream = stage.run(manager=self.manager)
        with zipfile.ZipFile("firefly/modules/data_pipelines/fixtures/zip_test_file.zip", "r") as original:
            test_file = original.read("test_file.txt")
            self.assertEqual(test_file, output_stream.read())

    def test_excel_to_csv_stage(self):
        # HbA1c outcome measure
        df = self._assert_excel_to_csv_stage_runs("bcbs_test_quality_report.xlsx", "HbA1c Control", [6])
        self.assertEqual(df["Member ID"].iloc[0], "hba1c some id")
        # HbA1c outcome measure with xls file
        df = self._assert_excel_to_csv_stage_runs("bcbs_test_quality_report.xls", "HbA1c Control", [6])
        self.assertEqual(df["Member ID"].iloc[0], "hba1c some id")
        # Blood pressure outcome measure
        df = self._assert_excel_to_csv_stage_runs("bcbs_test_quality_report.xlsx", "Blood Pressure Control", [6])
        self.assertEqual(df["Member ID"].iloc[0], "bp some id")
        # Blood pressure outcome measure current year
        df = self._assert_excel_to_csv_stage_runs("bcbs_test_quality_report_2024.xlsx", "Blood Pressure Control", [7])
        self.assertEqual(df["Member ID"].iloc[0], "bp some id")
        # Colorectal cancer screening
        df = self._assert_excel_to_csv_stage_runs("bcbs_test_quality_report.xlsx", "Colorectal Cancer Screening", [7])
        self.assertEqual(df["Member ID"].iloc[0], "some id")
        # Hypertension management
        output_columns = [
            "PCT",
            "Ref Circ",
            "PCP Number",
            "PCP NPI",
            "PCP Name",
            "Member ID",
            "Member Name",
            "Member DOB",
            "Member Gender",
            "Diabetic",
            "Denominator",
            "Hypertension Identification 1 Date of Service",
            "Hypertension Identification 1 Servicing Provider ID",
            "Hypertension Identification 1 Servicing Provider Name",
            "Hypertension Identification 1 Primary Diagnosis Code",
            "Hypertension Identification 1 Secondary Diagnosis Code",
            "Hypertension Identification 1 Procedure Code",
            "Hypertension Identification 2 Date of Service",
            "Hypertension Identification 2 Servicing Provider ID",
            "Hypertension Identification 2 Servicing Provider Name",
            "Hypertension Identification 2 Primary Diagnosis Code",
            "Hypertension Identification 2 Secondary Diagnosis Code",
            "Hypertension Identification 2 Procedure Code",
            "Outpatient Visit Date of Service",
            "Outpatient Visit Servicing Provider ID",
            "Outpatient Visit Servicing Provider Name",
            "Outpatient Visit Primary Diagnosis Code",
            "Outpatient Visit Secondary Diagnosis Code",
            "Outpatient Visit Procedure Code",
        ]
        df = self._assert_excel_to_csv_stage_runs(
            "bcbs_test_quality_report.xlsx", "Hypertension Management", [6, 7], output_columns
        )
        self.assertEqual(df["Hypertension Identification 1 Servicing Provider Name"].iloc[0], "some provider 1")
        self.assertEqual(df["Hypertension Identification 2 Servicing Provider Name"].iloc[0], "some provider 2")
        self.assertEqual(df["Outpatient Visit Servicing Provider Name"].iloc[0], "some provider 3")
        self.assertEqual(df["Member Name"].iloc[0], "some user")

    def _assert_excel_to_csv_stage_runs(self, filename, tab, header_rows, output_columns=None):
        with open(f"firefly/modules/data_pipelines/fixtures/{filename}", "rb") as test_excel:
            stream = BytesIO(test_excel.read())
            stage = ExcelToCSVStage(tab=tab, header_rows=header_rows, output_columns=output_columns)
            output_stream = stage.run(input_stream=stream, manager=self.manager)
            self.assertIsNotNone(output_stream)
            return pd.read_csv(output_stream)

    @mock.patch("firefly.modules.data_pipelines.stages.TransferConfig")
    @mock.patch("firefly.modules.data_pipelines.stages.boto3.client")
    @mock.patch("firefly.modules.file_transfer.utils.get_sftp_client")
    def test_file_extension_denylist(self, mock_sftp_client, mock_s3, _mock_transfer_config):
        mock_sftp_client.return_value.__enter__.return_value = MockSFTPClient(with_directories=True)
        mock_s3.return_value = MockS3Client("s3")
        # Verify that both pdf files are not returned if the denylist does not exclude them
        files = get_filenames_and_checksums_from_sftp(connection=self.connection, denylist=[".pdf"])
        self.assertEqual(files, {})
        # Reset mocks
        mock_sftp_client.return_value.__enter__.return_value = MockSFTPClient()
        mock_s3.return_value = MockS3Client("s3")
        files = get_filenames_and_checksums_from_sftp(connection=self.connection, denylist=[".txt"])
        self.assertNotEqual(files, {})
        self.assertEqual(len(files), 1)


class PipelineManagerTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports
        from firefly.core.tests.utils import reset_context_to_luci_user

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.pipeline_run = PipelineRunFactory()
        cls.manager = PipelineManager(pipeline_run=cls.pipeline_run)
        cls.connection = ConnectionFactory()

    def setUp(self):
        super().setUp()
        # Use class-level objects for tests, but create fresh manager for each test
        # to avoid log accumulation between tests
        self.pipeline_run = self.__class__.pipeline_run
        self.manager = PipelineManager(pipeline_run=self.pipeline_run)
        self.connection = self.__class__.connection

    def test_debug(self):
        self.manager.debug("Debug log test")
        expected_messages = ["Debug log test"]
        self.assert_logs(expected_messages, "debug")
        self.manager.debug("Debug log test %d with args (%s)", 1, "test")
        expected_messages = ["Debug log test", "Debug log test 1 with args (test)"]
        self.assert_logs(expected_messages, "debug")

    def test_info(self):
        self.manager.info("Info log test")
        expected_messages = ["Info log test"]
        self.assert_logs(expected_messages, "info")
        self.manager.info("Info log test %d with args (%s)", 1, "test")
        expected_messages = ["Info log test", "Info log test 1 with args (test)"]
        self.assert_logs(expected_messages, "info")

    def test_warning(self):
        self.manager.warning("Warning log test")
        expected_messages = ["Warning log test"]
        self.assert_logs(expected_messages, "warning")
        self.manager.warning("Warning log test %d with args (%s)", 1, "test")
        expected_messages = ["Warning log test", "Warning log test 1 with args (test)"]
        self.assert_logs(expected_messages, "warning")

    def test_error(self):
        self.manager.error("Error log test")
        expected_messages = ["Error log test"]
        self.assert_logs(expected_messages, "error")
        self.manager.error("Error log test %d with args (%s)", 1, "test")
        expected_messages = ["Error log test", "Error log test 1 with args (test)"]
        self.assert_logs(expected_messages, "error")

    def test_exception(self):
        self.manager.exception("Exception log test")
        expected_messages = ["Exception log test"]
        self.assert_logs(expected_messages, "exception")
        self.manager.exception("Exception log test %d with args (%s)", 1, "test")
        expected_messages = ["Exception log test", "Exception log test 1 with args (test)"]
        self.assert_logs(expected_messages, "exception")

    def assert_logs(self, expected_messages, level):
        logs = self.manager.get_logs()
        pipeline_definition_name = self.pipeline_run.pipeline_definition.name
        pipeline_run_id = self.pipeline_run.pk
        handler = self.pipeline_run.pipeline_definition.handler
        prefix = f"[DataPipeline][{pipeline_definition_name}][PipelineRun {pipeline_run_id}][Handler {handler}]"
        expected_logs = {"logs": [{"level": level, "message": f"{prefix} {msg}"} for msg in expected_messages]}
        self.assertDictEqual(logs, expected_logs)

    @mock.patch("firefly.modules.file_transfer.models.upload_file_to_sftp")
    @mock.patch("firefly.modules.file_transfer.models.download_file_from_sftp")
    def test_add_output(self, _mock_download_file_from_sftp, _mock_upload_file_to_sftp):
        # Add a single upload file
        upload_1 = self._add_upload()
        self.assertEqual(self.pipeline_run.outputs.count(), 1)
        output_1 = self.pipeline_run.outputs.first()
        self.assertEqual(output_1.value_content_object, upload_1)
        self.assertEqual(output_1.key, "upload")
        # Add a second upload file, which should increment the key
        upload_2 = self._add_upload()
        self.assertEqual(self.pipeline_run.outputs.count(), 2)
        output_2 = self.pipeline_run.outputs.filter(key="upload-2")
        self.assertTrue(output_2.exists())
        self.assertEqual(output_2.first().value_content_object, upload_2)
        # Add a single download file
        download_1 = self._add_download()
        self.assertEqual(self.pipeline_run.outputs.count(), 3)
        output_3 = self.pipeline_run.outputs.last()
        self.assertEqual(output_3.value_content_object, download_1)
        self.assertEqual(output_3.key, "download")
        # Add a download upload file, which should increment the key
        download_2 = self._add_download()
        self.assertEqual(self.pipeline_run.outputs.count(), 4)
        output_4 = self.pipeline_run.outputs.filter(key="download-2")
        self.assertTrue(output_4.exists())
        self.assertEqual(output_4.first().value_content_object, download_2)

    def test_add_output_stream(self):
        # Add a single file stream
        stream = BytesIO(b"content")
        self.manager.add_output_stream(key="stream", filename="file.txt", stream=stream)
        self.assertEqual(self.pipeline_run.outputs.count(), 1)
        output_1 = self.pipeline_run.outputs.first()
        self.assertEqual(output_1.value_content_type, ContentType.objects.get_for_model(PipelineRunFileOutput))
        self.assertEqual(output_1.key, "stream")

    def _add_upload(self):
        file = SimpleUploadedFile("upload.pdf", b"content", content_type="application/pdf")
        upload = Upload.objects.create(
            connection=self.connection, file=file, name="upload.pdf", status=TransferStatus.COMPLETE
        )
        self.manager.add_output_object(key="upload", output=upload)
        return upload

    def _add_download(self):
        file = SimpleUploadedFile("download.pdf", b"content", content_type="application/pdf")
        download = Download.objects.create(
            connection=self.connection, file=file, name="download.pdf", status=TransferStatus.COMPLETE
        )
        self.manager.add_output_object(key="download", output=download)
        return download
