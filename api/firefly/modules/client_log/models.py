from django.conf import settings
from django.db import models

from firefly.modules.firefly_django.models import BaseModelV3


class ClientLog(BaseModelV3):
    """
    This model is DEPRECATED. Client logs and other telemetry should be sent
    from the client directly to Datadog, not through the API.
    """

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.Char<PERSON>ield(max_length=100, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    url_to = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    url_from = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    action = models.TextField(null=True, blank=True)

    stack_error = models.TextField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    environment = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    platform = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # Android or iOS
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    os = models.CharField(max_length=15, null=True, blank=True, db_index=True)  # noqa: TID251
    # Android or iOS version. Some Android version strings can be long.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    os_version = models.CharField(max_length=31, null=True, blank=True, db_index=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_model = models.CharField(max_length=63, null=True, blank=True, db_index=True)  # noqa: TID251

    # Firefly app version.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    app_version = models.CharField(max_length=15, null=True, blank=True, db_index=True)  # noqa: TID251

    # Device Unique Identifier (currently does not persist through uninstalls...)
    # iOS: https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor
    # Android: https://developer.android.com/reference/android/provider/Settings.Secure.html#ANDROID_ID
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    device_uid = models.CharField(max_length=177, null=True, blank=True, db_index=True)  # noqa: TID251

    class Meta:
        db_table = "client_logs"


class EmergencyCallLog(BaseModelV3):
    patient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    class Meta:
        db_table = "emergency_call_logs"
