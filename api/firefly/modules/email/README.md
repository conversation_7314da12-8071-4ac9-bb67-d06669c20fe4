# Email Module

The Email module provides comprehensive email communication capabilities for Firefly Health, serving as the central hub for member, provider, and employer communications. This module is transitioning from traditional email workflows to structured portal-based interactions to improve efficiency and data quality.

## Strategic Overview

### Vision & Business Context
The email system is evolving to reduce unstructured email volume by **siphoning off use cases into designated portals**. Currently, 44% of incoming <NAME_EMAIL> are from members, with 18% from providers (mostly member-related). The strategy prioritizes the **Member Portal** as the primary interface for member communications.

### Member Portal
The Member Portal serves as the centralized hub for members to:
- Ask health plan questions through structured forms
- Join video visits (current capability)
- View plan usage and claims (future enhancement)
- Submit secure attachments with malware scanning

All new member requests are routed through the portal or mobile app using structured questions to gather sufficient information for quick resolution.

## Architecture Overview

### System Components
- **Web Portal**: Hosted on marketing website with intake forms
- **Case Management**: Automatic case creation from form submissions
- **Email Processing**: Bidirectional Gmail API integration
- **Attachment Handling**: S3-based storage with malware scanning
- **State Machine**: Automated status transitions

### Data Flow
1. **Inbound**: GCP Pub/Sub → Gmail API → Email Processing → Case Creation/Linking
2. **Outbound**: Case Actions → Email Queue → Gmail API → Delivery
3. **Attachments**: Upload → Quarantine Bucket → Malware Scan → Attachment Bucket

## Member Portal & Form Processing

### Form Categories
The system supports structured case categories based on top member inquiry types:

| Internal Title | Public Facing Title |
|---|---|
| FFHP Member Billing/Claim Question | Billing & Claims |
| FFHP Member Eligibility/Benefits Question | Benefits & Eligibility |
| FFHP Member Initiated Network Status Request | Network & Provider Access |
| FFHP Member Care Key Question | Care Keys & Referrals |
| Uncategorized | Other |

### Form Submission Workflow
1. Member completes secure web form with attachments
2. Attachments uploaded to quarantine bucket via presigned S3 URLs
3. Case automatically created with appropriate category
4. Confirmation email sent with case ID
5. Malware scanning completed on attachments
6. Case appears in operational worklist

## Email Processing

### Outbound Email
Outbound emails are sent via Gmail API and triggered in two scenarios:
1. **Form Submissions**: Automatic confirmation emails from marketing website
2. **Case Communications**: Manual emails from Lucian cases for verified addresses

**Processing Queues:**
- `email_publisher`: Sends email to recipients
- `update_email_details`: Updates email metadata from Gmail API response

### Outbound Email Attachments
The system handles two types of outbound attachments:

1. **Form Attachments**: User-submitted attachments from marketing website are uploaded to quarantine bucket via S3 POST upload URL generated by API. These undergo malware scanning before being accessible.

2. **Lucian Attachments**: Attachments sent from Lucian cases are uploaded directly to the attachment bucket without scanning, as they originate from trusted internal sources.

### Outbound Email Templates

#### Confirmation Email (Form Submission)
```
From: <EMAIL>
Subject: Your Secure Message with Firefly Health [Case ID: #12345]

Hi [Customer First Name],
Thanks for your message! We've received it and created case #12345 for you.
Our team will respond within [X] business days. You can reply directly to this email for any follow-ups related to this case.
```

#### Security Alert (Direct Email)
```
Subject: Action Required: Please Complete Firefly Health's Secure Message Form

For your privacy and security, we respond to conversations that begin through our online form process or via chat through the Firefly Health app.
To get started, please fill out the online form here or sign in to the Firefly Health app.
```

### Inbound Email
Inbound emails are processed through GCP Pub/Sub notifications sent to the Lucian endpoint `email/gcp-pub-sub/email-notification/` with GCP service account authentication.

**Processing Flow:**
1. **Notification Receipt**: GCP Pub/Sub sends notification with history ID
2. **Partial Synchronization**: Uses Gmail API partial sync instead of history API due to timing inconsistencies
3. **Message Processing**: Retrieves new messages with `messageAdded` history type
4. **Case Linking**: Links email to existing case if thread and email address match exactly
5. **Auto-Response**: Sends form redirect for unlinked emails

**Processing Queues:**
- `email_receiver`: Initial message processing and storage
- `inbound_email_processing`: Attachment handling and case linking
- `inbound_email_attachment_status`: Syncs attachment status from S3

**Attachment Processing:**
The `inbound_email_processing` queue triggers the `FetchGmailAttachment` Lambda function to handle attachments. The Lambda fetches attachments from Gmail API and uploads them to the Quarantine bucket.

**Important**: The Lucian API does not have access to the Quarantine bucket to read malware scanning status. This is an intentional security decision since the quarantine bucket contains potentially malicious files. Attachment status is synced from the Attachment bucket using the `inbound_email_attachment_status` queue.

**Case Linking Logic:**
- Email links to case if: thread exists in case AND email address matches person email exactly
- Otherwise: automatic reply sent directing user to secure form

### Email Subject Line Standards

**Member-Initiated (Web Form):**
```
Your Secure Message with Firefly Health [Case: #12345]
```

**Firefly-Initiated (Lucian):**
```
Secure Message from Firefly Health [Case: #12345]
```

## Security & Compliance

### Legal Disclaimers
All emails include required legal footer:
```
Replies to this email will not be encrypted by Firefly Health. Please reference your email service provider's transmission encryption protocols prior to replying with any sensitive information.

Note: Our system only accepts PNG, JPG, and PDF file attachments. A total file size limit of 15MB applies to all emails in the thread combined.
```

### TLS Enforcement
- **Outbound**: Google Workspace enforces TLS for outbound emails
- **Inbound**: Gmail provides baseline security scanning
- **Attachments**: File extension restrictions (PNG, JPG, PDF only)

### SPAM Handling
- **Detection**: Tech alerts triggered for SPAM-flagged emails
- **Review Process**: Manual review of sender addresses in system
- **Action**: Process genuine emails, ignore confirmed SPAM

## Attachment Processing

### File Upload Architecture
**Two-Bucket System:**
1. **Quarantine Bucket**: Initial upload location for scanning
2. **Attachment Bucket**: Clean files after malware scanning

**Supported File Types:** PNG, JPG/JPEG, PDF only

### Form Attachment Upload
```javascript
// Frontend upload process
async function uploadFile(attachment) {
  // Get presigned URL from Lambda
  const response = await fetch("API_GATEWAY_URL", {
    method: "POST",
    body: JSON.stringify({ fileName, fileType })
  });
  const { uploadURL, fileKey } = await response.json();

  // Upload to S3
  await fetch(uploadURL, {
    method: "PUT",
    body: file,
    headers: { "Content-Type": fileType }
  });

  uploaded_attachments.push(fileKey);
}
```

### AWS Lambda Implementation
```python
# Presigned URL generation
import boto3
import uuid

def lambda_handler(event, context):
    body = json.loads(event["body"])
    file_name = body.get("fileName")
    file_type = body.get("fileType")

    unique_key = f"inbound/email-attachments/{uuid.uuid4()}-{file_name}"

    params = {
        "Bucket": "quarantine-bucket",
        "Key": unique_key,
        "ContentType": file_type,
        "ExpiresIn": 60
    }

    upload_url = s3.generate_presigned_url("put_object", Params=params)
    return {"uploadURL": upload_url, "fileKey": unique_key}
```

## Malware Scanning

### AWS GuardDuty Integration
All inbound attachments undergo malware scanning using **AWS GuardDuty Malware Protection for S3**:

1. **Upload**: Files uploaded to Quarantine bucket
2. **Scanning**: GuardDuty scans objects automatically
3. **Tagging**: `GuardDutyMalwareScanStatus` tag added with scan results
4. **Event Processing**: EventBridge monitors CloudWatch for scan completion
5. **File Movement**: `MoveScannedObjects` Lambda moves clean files to Attachment bucket

**Scan Results:**
- `no_threat_found`: File moved to Attachment bucket
- `threat_found`: File quarantined, case flagged for review

**Status Synchronization:**
- Attachment status synced via `inbound_email_attachment_status` queue
- Updates `EmailAttachment` model with scan results
- Lucian API accesses only Attachment bucket (security isolation)

### Security Architecture
- **Quarantine Bucket**: Restricted access, potential malware isolation
- **Attachment Bucket**: Clean files, accessible by Lucian API
- **Access Control**: Intentional separation prevents API exposure to quarantined files

## Case Management Integration

### State Machine Integration

#### Current Implementation
The system automatically updates case status based on email interactions using existing case actions:

**Implemented Status Transitions:**
- **Outbound Email Sent**: Case moves to `WAITINGFORREPLY` status when ops sends email
- **Inbound Email Received**: Case moves to `USERREPLIED` status when member replies
- **Case Reopening**: Closed cases reopen automatically if member replies within 7 days

**Code Implementation:**
```python
# When ops sends email from case
case.action = CaseSystemAction.WAITINGFORREPLY

# When member replies to case
case.action = CaseSystemAction.USERREPLIED

# 7-day reopening logic
if case.status_category == StatusCategory.COMPLETE and timezone.now() > case.updated_at + timedelta(days=7):
    # Don't link email to case, send form redirect instead
```

### CRM Capabilities

#### Required Components
- **Web Portal**: Marketing website integration for intake forms
- **Case Creation**: Automatic case generation from form submissions
- **Email Integration**: Bidirectional communication with case threading
- **Attachment Management**: Secure file handling with malware scanning
- **Status Automation**: State machine-driven workflow progression

#### Form Success Screen
```
Got it! Here's what's next…
• A member of our team will review your information
• We'll follow up within [XX] business days
• Questions? Reply to the receipt email we just sent or call (************* (M-F, 7am-7pm ET)
```

## Development & Testing

### Local Testing Setup
For inbound email testing, expose local API via ngrok:

1. **Install ngrok**: Follow [ngrok installation guide](https://ngrok.com/downloads/mac-os)
2. **Expose endpoint**: `ngrok http <PORT_NUMBER>`
3. **Update GCP**: Configure [pubsub subscription](https://console.cloud.google.com/cloudpubsub/subscription/edit/gmail-notification-subscription?inv=1&invt=AbxzcQ&project=fireflyhealth-development)
4. **Update authentication**: Modify audience in `firefly/modules/email/authentication.py`
5. **Restart containers**: `make restart`
6. **Start watcher**: Run [Gmail inbox watcher](http://localhost:8084/admin/fireflyadmincommand/fireflyadmincommand/run/watchgmailinbox)

**Note**: Outbound email works without local changes. DEV and local environments share inbox, so local testing may process DEV emails.

## Implementation Architecture

### Current Architecture
The email system uses **AWS Lambda** for attachment processing with the following components:

### Infrastructure Components

#### AWS Lambda Functions
- **FetchGmailAttachment**: Processes inbound email attachments
- **MoveScannedObjects**: Moves clean files from quarantine to attachment bucket

#### S3 Bucket Architecture
```
Quarantine Bucket (Restricted Access)
├── inbound/email-attachments/
└── form-attachments/

Attachment Bucket (API Access)
├── clean/email-attachments/
└── clean/form-attachments/
```

### Security Implementation

#### Authentication & Authorization
- **GCP Service Account**: Signs requests to Lucian endpoints
- **IAM Roles**: Lambda execution with minimal required permissions

## Technical Specifications

### Form Constraints
- **Character Limit**: 1000 characters for open text fields
- **File Size**: 15MB total limit per email thread (implemented)
- **File Types**: PNG, JPG, PDF only
- **Upload Method**: Presigned S3 URLs with 60-second expiration

### Email Configuration
- **From Address**: `LUCIAN_EMAIL_ADDRESS` (environment-specific)
- **Business Hours**: Monday-Friday, 7am-7pm ET
- **Support Phone**: (*************
- **Response SLA**: [X] business days (configurable)

### Queue Processing
- **email_publisher**: Outbound email delivery
- **update_email_details**: Gmail API metadata sync
- **email_receiver**: Inbound message processing
- **inbound_email_processing**: Attachment and case linking
- **inbound_email_attachment_status**: S3 status synchronization

## Monitoring & Observability

### Current Monitoring
- **Email Processing**: Dramatiq queue monitoring for email delivery and processing
- **Attachment Status**: S3 bucket status synchronization tracking
- **Case Linking**: Email-to-case relationship success rates

### Planned Monitoring
- **SPAM Detection**: Tech alerts for flagged emails
- **Malware Detection**: Threat found in attachments
- **API Failures**: Lambda errors

## Open Questions & Future Enhancements

### Immediate Considerations
- **Environment Isolation**: Separate environment for attachment processing
- **Email Blocking**: Capability to block specific senders
- **Terraform Integration**: Infrastructure as code for Lambda deployment
- **File Size Limits**: Specific MB limits for attachment threads

### Planned Enhancements
- **Provider Portal**: Extend structured forms to provider communications
- **Employer Portal**: Dedicated interface for employer inquiries
- **Enhanced Security**: Additional malware scanning vendors
- **Performance Optimization**: Reduce processing latency

### Technical Debt
- **Swivel Chair Operations**: Manual processes until full portal migration
- **Shared Inbox**: DEV/local environment separation
- **Error Handling**: Enhanced retry mechanisms and failure recovery

## Use Cases & Workflows

### Use Case 1: Member Form Submission
**Scenario**: Member submits case from marketing website

| Step | Action | Expected Result |
|------|--------|----------------|
| 1 | Member visits form URL and completes submission | Automatic confirmation email sent |
| 2 | Search for case in Lucian cases dropdown | Person created with case in Todo's tab |
| 3 | Ops replies to member from case | Email sent, case moves to "Waiting for Reply" |
| 4 | Member replies from personal email | Reply appears in case, status becomes "User Replied" |

### Use Case 2: Ops-Initiated Email
**Scenario**: Health Ops team initiates email from Lucian

| Step | Action | Expected Result |
|------|--------|----------------|
| 1 | Create user profile with personal email | Person profile created |
| 2 | Create case from health plan category | External Email option available |
| 3 | Send email from case | Email sent, status "Waiting for Reply" |
| 4 | Member replies from personal inbox | Reply appears in case, status "User Replied" |

### Use Case 3: New Email Conversation
**Scenario**: Member sends fresh email to Lucian address

| Step | Action | Expected Result |
|------|--------|----------------|
| 1 | Member sends new <NAME_EMAIL> | Automatic reply directing to form submission |

### Use Case 4: Closed Case Reply (< 7 Days)
**Scenario**: Member replies to recently closed case

| Step | Action | Expected Result |
|------|--------|----------------|
| 1 | Close case | Case status updated to closed |
| 2 | Member replies within 7 days | Case reopens with "User Replied" status |

### Use Case 5: Closed Case Reply (> 7 Days)
**Scenario**: Member replies to old closed case

| Step | Action | Expected Result |
|------|--------|----------------|
| 1 | Member replies after 7+ days | Automatic form redirect, case remains closed |

## Future Enhancements

### Provider Portal
The Provider Portal will extend structured communication capabilities to provider interactions, reducing friction while maintaining clinician verification requirements. Key features will include:

- **Provider-specific intake forms** with magic link authentication
- **Practice-based case management** supporting multiple subject types
- **Streamlined workflows** integrated with existing case management

### Case Subject Expansion
Future development will expand case support beyond person entities to include vendors, providers, and employees, requiring database model changes and updated operational workflows.

## Related Documentation
- **Infrastructure**: `firefly-infra/modules/aws-s3-malware-scanning`
- **Gmail API**: [Google Workspace Gmail API Guide](https://developers.google.com/workspace/gmail/api/guides/sync#partial)
- **Case Management**: `api/firefly/modules/cases/README.md`
- **Form Processing**: `api/firefly/modules/forms/README.md`
- **Provider Portal**: Future documentation for provider-specific workflows
