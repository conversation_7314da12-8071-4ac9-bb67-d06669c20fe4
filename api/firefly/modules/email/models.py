from typing import ClassVar

from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON><PERSON><PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django.db.models.functions import Coalesce
from django_deprecate_fields import deprecate_field as django_deprecate_field

from firefly.modules.email.constants import (
    EMAIL_ATTACHMENT_STATUS_CHOICES,
    EMAIL_STATUS_CHOICES,
    EMAIL_TYPE_CHOICES,
    EmailAttachmentStatus,
    EmailStatus,
    EmailType,
)
from firefly.modules.email.utils import santize_html
from firefly.modules.firefly_django.models import BaseModelV3, SafeDeleteManagerWithBulkUpdate
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin


class EmailManager(SafeDeleteManagerWithBulkUpdate["Email"]):
    # The email is order by email_sent_at field which is from gmail API response,
    # if case the get message API from gmail fails sorting will be done using the created at field
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .annotate(effective_date=Coalesce("email_sent_at", "created_at"))
            .order_by("effective_date")
        )


class Email(SaveHandlersMixin, BaseModelV3):
    objects: ClassVar[EmailManager] = EmailManager()
    # Refer https://developers.google.com/gmail/api/reference/rest/v1/users.messages#Message for field definitions
    subject = models.TextField(
        help_text="Subject of the email.",
    )
    message = models.TextField(
        help_text="Message from the email.",
    )
    from_address = models.TextField(
        help_text="from address from the email.",
    )
    to_address = ArrayField(
        size=255,
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        base_field=models.CharField(max_length=255),  # noqa: TID251
        help_text="to addresses from the email.",
    )
    cc_address = ArrayField(
        null=True,
        blank=True,
        size=255,
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        base_field=models.CharField(max_length=255),  # noqa: TID251
        help_text="cc addresses from the email.",
    )
    status = models.TextField(
        help_text="Status of the email (In Progress, Success, Failed)",
        choices=EMAIL_STATUS_CHOICES,
        db_index=True,
        default=EmailStatus.NOT_STARTED,
    )
    retry = models.IntegerField(help_text="Retry count of email when there is failure.", default=0)
    raw_response = models.TextField(
        null=True,
        blank=True,
        help_text="Raw Response of the email from gmail API.",
    )
    snippet = models.TextField(
        null=True,
        blank=True,
        help_text="A short part of the message text.",
    )
    response = models.TextField(
        null=True,
        blank=True,
        help_text="Parsed Response of the email.",
    )
    message_id = models.TextField(
        null=True,
        blank=True,
        max_length=255,
        help_text="Message ID of the email from gmail API.",
    )
    header = models.TextField(
        null=True,
        blank=True,
        help_text="Header of the email from gmail API.",
    )
    thread_id = models.TextField(
        null=True,
        blank=True,
        max_length=255,
        help_text="Thread ID of the email from gmail API. The thread ID will be same across message from same thread",
    )
    history_id = models.TextField(
        null=True,
        blank=True,
        max_length=255,
        help_text="History ID of the email from gmail API.",
    )
    label_ids = models.TextField(
        null=True,
        blank=True,
        help_text="Label ID of the email from gmail API. (INBOX, UNREAD)",
    )
    header_message_id = models.TextField(
        null=True,
        blank=True,
        help_text="Message ID from the header. This is different then message_id and will be used to reply in threads",
    )
    email_type = models.TextField(help_text="Inbound or Outbound email", choices=EMAIL_TYPE_CHOICES, db_index=True)
    sent_by_staff_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="sent_by_staff_user",
    )
    email_sent_at = models.DateTimeField(
        default=None, null=True, blank=True, help_text="Internal Date field from Gmail get message API"
    )
    error = models.TextField(null=True, blank=True, help_text="Error message to indicate why the email failed")

    # All CaseRelations with a Email as the content_object
    case_relations = GenericRelation("cases.CaseRelation")

    def pre_save_mutation(self, changed):
        log_prefix: str = "[email_pre_save_mutation]: "
        if changed(field_name="message", debug=False, log_prefix=log_prefix):
            self.message = santize_html(self.message)
        if changed(field_name="snippet", debug=False, log_prefix=log_prefix):
            self.snippet = santize_html(self.snippet)
        if changed(field_name="response", debug=False, log_prefix=log_prefix):
            self.response = santize_html(self.response)

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        # After saving the email, if the status is Pending, send a task to
        # Dramatiq to send email asynchronously. This accomplishes two
        # things:
        #
        # 1. Send Email when email object is first created in pending status.
        # 2. If a email fails, it can be moved back into Pending status to start
        #    the process over again.
        #
        # The Dramatiq actor that processes this task should gracefully handle
        # the scenario in which multiple tasks are created for the same job.
        log_prefix: str = "[email_post_save_side_effect]:"
        if (
            changed(field_name="status", debug=True, log_prefix=log_prefix)
            and self.status == EmailStatus.PENDING
            # to check if the object is not created, get_old_value_for_changed_field will return if object is created
            and get_old_value_for_changed_field("id")
            and get_old_value_for_changed_field("status") != self.status
        ):
            from .tasks import async_email_publisher, async_trigger_inbound_email_processing
            # TODO: Add a restriction for not triggering the outbound email from success status.

            if self.email_type == EmailType.OUTBOUND:
                async_email_publisher.send(self.id, log_prefix=log_prefix)
            if self.email_type == EmailType.INBOUND:
                async_trigger_inbound_email_processing.send(self.id, log_prefix=log_prefix)

    class Meta(BaseModelV3.Meta):
        db_table = "email"


class EmailAttachment(SaveHandlersMixin, BaseModelV3):
    """An attachment (image, PDF) in a Email."""

    file = django_deprecate_field(models.FileField(upload_to="email-attachment/"))
    # File key can be null when file upload to S3 has failed.
    # It could fail if the user uploads unsupported file type from email client
    # The file will be uploaded to bucket A and after scanning file will be moved to bucket B.
    # Lucian will have access only to bucket B.
    # If we cannot generate the signed URL using the file key,
    # it could mean the file is not present in the bucket B and could be malware.
    # the API should return that there is attachment in the message but it's a malware or not processed but system,
    # since it didn;t meet our file requirements
    file_key = models.TextField(
        help_text="Unique Key of the object in S3 bucket. Can be used to get signed URL to access the object",
        null=True,
        blank=True,
    )
    email = models.ForeignKey(Email, on_delete=models.CASCADE, related_name="attachments")
    file_name = models.TextField(
        help_text="File Name from gmail API",
    )
    attachment_id = models.TextField(
        help_text="ID of the attachment from gmail API",
        null=True,
        blank=True,
    )
    status = models.TextField(
        help_text="Status of the email attachment (Not Started, In Progress, Success, Failed)",
        choices=EMAIL_ATTACHMENT_STATUS_CHOICES,
        default=EmailAttachmentStatus.NOT_STARTED,
        null=True,
    )
    # This field will have error message like - "Error uploading the attachments", "Unsupported Extension",
    # "More than 3 attachment"
    error = models.TextField(
        null=True, blank=True, help_text="Error message to indicate why the email attachment failed"
    )

    class Meta(BaseModelV3.Meta):
        db_table = "email_attachment"

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        # After saving the email attachment, if the status is Pending, send a task to
        # Dramatiq to check if email attachment is moved from quarantine bucket to email attachment bucket.
        #
        # The Dramatiq actor that processes this task should gracefully handle
        # the scenario in which multiple tasks are created for the same job.
        log_prefix: str = "[email_attachment_post_save_side_effect]:"
        if (
            changed(field_name="status", debug=True, log_prefix=log_prefix)
            and self.status == EmailAttachmentStatus.PENDING
            # to check if the object is not created, get_old_value_for_changed_field will return if object is created
            and get_old_value_for_changed_field("id")
            and get_old_value_for_changed_field("status") != self.status
        ):
            from .tasks import async_inbound_email_attachment_status

            # TODO: Add a restriction for not triggering the attachment status from  FAILED
            async_inbound_email_attachment_status.send_with_options(args=(self.id, log_prefix), delay=6000)
