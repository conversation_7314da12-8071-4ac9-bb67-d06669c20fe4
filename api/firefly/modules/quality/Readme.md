# Clinical Quality Management System

## Motivation

This is Firefly Health's technical approach to supporting clinical quality management. The intended audience includes analysts and engineers who are implementing clinical quality measures and related product changes. It is not intended to define operational processes or workflows, but should define the technology that supports them.

## Concepts

Clinical quality management is a massive domain, but we will attempt to distill some of the essential concepts here. A full glossary of terms can be found at https://ecqi.healthit.gov/glossary.

A clinical quality measure quantifies how we deliver care. We hold ourselves accountable to measures to deliver on the "healthcare…that's twice as good" objective of our mission. Payors such as Blue Cross Blue Shield (BCBS) also hold us accountable and incentivize higher quality with better payments.

### Anatomy of a Measure

A measure score is typically a rate, expressed as a percentage, of the number of patients for whom we met a certain target process or outcome (the numerator population) out of the number of patients we expect should meet that target (the denominator population). Almost every measure has exclusion criteria that can remove certain patients from the denominator. Performance is typically evaluated for a defined measurement period such as a calendar year.

The underlying sources of data used to calculate a measure must be trustworthy, so they typically fall into one of two categories: (1) administrative claims data, or (2) medical records. Claims are common because they are available to payors and are highly structured with detailed coding from standard code systems for diagnoses (ICD-10) and procedures (CPT). Medical records are now almost always electronic and may have structured coding for problems (SNOMED), lab results (LOINC), and medications (NDC); however, some documents such as faxes are unstructured and must be coded by a human operator.

When a patient is in the denominator but not the numerator, that is said to represent a care gap; it implies that we must take some action to move them into the numerator. Closing a gap is rarely a single step, but a multi-step process conditional upon clinical decisions.

### Prior Art

Measures have become increasingly standardized over time. The National Committee for Quality Assurance (NCQA) is the steward of the Healthcare Effectiveness Data and Information Set (HEDIS) which defines measures in great detail, including how to statistically verify the accuracy of an organization's reported performance, typically through an independent certified auditor.

The Centers for Medicare & Medicaid Services (CMS) have also adopted electronic measure specifications that build on open standards including Clinical Quality Language (CQL) and HL7 Fast Healthcare Interoperability Resources (FHIR). These measures have considerable overlap with HEDIS measures and are the basis for the Merit-based Incentive Payment System (MIPS) that you may see referenced in Elation and which EHRs are required to implement in order to be certified. An example of an electronic measure specification can be found at https://ecqi.healthit.gov/ecqm/ec/2023/cms130v11.

## Global Design Assumptions

Data accuracy and freshness are the two most important characteristics of a quality management system. Our stakeholders need data they can trust, and it needs to be refreshed daily to make timely operational decisions. It does not need to be updated in near-real time.

## Technical Implementation

### Measure Definitions

https://docs.google.com/document/d/1CrYmTlorsRPv6-CM7xIIkJgZGhhLE2GAZbozRDQ2-d4/edit

Based on the measure definitions provided by the BCBS specifications, we have implemented SQL queries in our dbt model. These queries are responsible for performing the necessary calculations for the measures. Our daily dbt job do measure calculation based on SQL and writes measure report into Snowflake DB

### Lucian Model

#### Measures

Measure object contains measure details. This contains below fields:
- **Uri**: A unique identifier for the measure, represented by a URI (e.g., http://lucian.firefly.health/measures/2023/COL)
- **Name**: The actual name of the measure (e.g., Colorectal Cancer Screening (BCBS))

**Measure config**: Measure configuration is a unique file for each measure that defines different conditions and sets the measure status based on those conditions. For example, if there is a gap in the measure (Denominator: 1, Numerator: 0, Denominator Exclusion: 0), the measure status is set as 'pending_review'. Similarly, if the next action date is due, the measure status is set as 'missing_a1c'.

**Work unit config**: Work unit configuration is defined in a JSON format and specifies the actions to be taken based on the measure status. The work unit config is leveraged to implement configuration-based actions. For example, an action for the 'pending_review' status can be defined as:

```json
{
    "grace_period": 90,
    "content_object": {"category_id": 118},
    "unique_per_person": true
}
```

The following configurations are currently in use:

- **Content_type**: Specifies the type of content (e.g., Case or Form). By default, the content_type is set as Case.
- **Content_object**: Defines the case or form content object. It includes details such as case category ID, default assignee, default due date, and form UID. Based on this config, a case is created or a form is assigned.
- **Unique_per_person**: This config determines whether a unique case is created per person. If set to true and two different measures are configured with the same case category, a single case is created per person. If set to false, two cases are created (one for each measure).
- **Grace_period**: This config is used to determine whether a closed case or completed form falls within the grace period. If a case is closed within this period and reappears in the next sync job, it will be reopened, otherwise, create a new case. For forms, if a form is completed within this grace period, a new form will not be assigned. This prevents the creation of duplicate cases or forms.

#### Measure report

Each measure generates a unique measure report for each person. So measure reports have a one-to-one relation with the measure and person model. The measure report contains the following fields:

- **Generated at**: Timestamp indicating when the measure report was generated.
- **Period**: Measurement period for the measure. For BCBS measures, the measurement period is a calendar year (1st Jan - 31st Dec), while internal measures may have different rollover measurement periods.
- **Detail**: A text field providing additional details for the report (e.g., "Out of range on 16-06").
- **Next Action Date**: For internal measures, this field specifies the next action date for the rollover, typically set every 6 months.
- **Lucian care gap synced at**: Timestamp indicating the last synchronization time of the care gap cron job for this measure. This information is used to generate incremental measure reports for the care gap job and helps with auto-recovery in case of cron job failures.
- **Work unit config**: Same as measure config.

#### Measure report population

Each measure report has a different population. As of now, we have mainly 3 different measure populations Denominator, Numerator, and Denominator Exclusion.

### Cron Jobs

#### Sync_measure_reports

The Sync_measure_reports function is responsible for synchronizing incremental measure reports between the snowflake 'lucian_measure_report_new' table and the lucian 'measure_report' table. This process involves creating, updating, or deleting rows in the lucian 'measure_report' table based on the row_action field. Additionally, it updates the measure report population based on the denominator, numerator, and denominator exclusion count.

**Process:**
1. **Get incremental measure report from 'lucian_measure_report_new' table**: Retrieve the incremental measure report data from the 'lucian_measure_report_new' table. Apply filters based on the measure_ids to obtain the relevant reports.
2. **Process rows based on row_action**: For each measure report in the incremental data, check the row_action field to determine the required action: create or update, or delete.
   - If row action 'create_or_update': If row already exist it update data. If row not exist create new row and ensure that the relevant fields, such as measure_id, person_id, period, detail, next_action_date are populated correctly.
   - If the row_action is 'delete': Delete the corresponding row from the lucian 'measure_report' table.
3. **Update measure report population**: Based on the denominator, numerator, and denominator exclusion count for each measure report it creates measure population rows. If a new measure report is created or an existing one is updated, update the population fields accordingly.

This cron job scheduled to run daily at 12:30 UTC time

#### Create_work_for_care_gaps

The create_work_for_care_gaps function responsible for create care gap work unit. The steps involved in processing incremental changes, including handling deleted reports, determining measure configuration status, taking appropriate actions based on work unit configuration, managing cases and forms, and closing outdated cases.

**Process:**
1. **Get incremental measure report from the 'lucian_measure_report' table**: Retrieve the incremental measure report data from the 'lucian_measure_report' table. Apply filters based on the measure_ids to obtain the relevant reports.
2. **Handle deleted measure reports**: Check if any measure reports have been marked as deleted. If a measure report is deleted: Defer all related cases associated with that measure report. Move all open case tasks to the "done" status.
3. **Determine measure configuration status**: For each non-deleted measure report, retrieve the corresponding measure configuration status. The measure configuration status represents the current state of the measure and defines the required actions.
4. **Take action based on work unit configuration**: Based on the measure configuration status, determine the appropriate action to be taken.
   - If the content_type in work unit configuration is case: Create a new case and assign relevant details, such as case description and case relation. If a case already exists for the measure and the case description has changed, update the case description. If the case is manually closed and the case updated date is within the grace period, reopen the case. Otherwise, create a new case.
   - If the content_type in work unit configuration is form: Check if the form has already been assigned to the user or completed within the grace period. If not, assign a new form to the user and create a member task for the form.
5. **Close outdated cases**: If the measure configuration status has changed or the gap has been closed: If there is no open tasks automatically close cases associated with the previous status.

This cron job scheduled to run daily at 13:30 UTC time

#### Sync_elation_care_gaps

This cron job sync care gaps into Elation and creates clinical reminders based on the care gap definition. It takes measure_uri and limit as i/p params.
Currently this job runs daily at 14:00 UTC time

## How to Release New Measure

This section provides step-by-step instructions for implementing new quality measures in the system.

### STEP 1: Create Measure config
- Create a file under the measure_configs directory for a new measure
- Add different statuses you want to apply for this measure
- update model.py, config.py file with new measure config
- Sample PR: https://github.com/fireflyhealth/lucian/pull/6726

### STEP 2: Create measure in Lucian
- Go to the measures section in the admin panel(https://lucian-admin.i.firefly.health/admin/quality/measure/)
- Click on add a measure
- Add the below details
  - URI: unique measure URI(Ex: http://lucian.firefly.health/measures/2023/BCS)
  - measure config: select measure config which is applicable for this measure
- Then click on save to create a new measure

### STEP 3: Create Elation Care gap
- Get the Care gap definition from the Elation team ([Contact person]: Lauren Hill)
- Then iterate locally on what options make sense to us and validate valuesets in snowflake
  Some sample Query to check in snowflake:
  ```sql
  select vs.name, vs.uri,vsc.code,vsc.system from raw.lucian_public.code_systems_valueset as vs join "RAW"."LUCIAN_PUBLIC"."CODE_SYSTEMS_VALUESETCODE" as vsc on vsc.value_set_id=vs.id where code='Z90.13'

  select vs.name, vs.uri,vsc.code,vsc.system from raw.lucian_public.code_systems_valueset as vs join "RAW"."LUCIAN_PUBLIC"."CODE_SYSTEMS_VALUESETCODE" as vsc on vsc.value_set_id=vs.id where vs.name like '%Mammography%' and vsc.system='http://www.ama-assn.org/go/cpt'
  ```
Once we are ready with the care gap definition:
- Go to the Elation care gap admin command(https://lucian-admin.i.firefly.health/admin/fireflyadmincommand/fireflyadmincommand/run/elationcaregaps)
- Select Resources as the definition
- Select Operation as create
- In options add the below JSON
  `["--quality-program", "FIREFLY1", "--definition", "{Replace with your elation care gap defination}]`
- Then click run to create a care gap definition
- It will return Json get id from that JSON
- Then go back to your created measure and update the Elation care gap definition id with this id

### STEP 4: Create Care gap cases
- Create a state machine definition from the admin panel using the below details
  - content: which includes all statuses and state transition for your case
  - Title: name for this state machine definition
  - Ex: https://lucian-admin.i.firefly.health/admin/statemachines/statemachinedefinition/22/change/
- Create a new case category from the admin panel with a newly created state machine definition
  - Ex: https://lucian-admin.i.firefly.health/admin/cases/casecategory/102/change/
- Update work unit config
  - Work unit config contains config to create cases based on status set during measure config
  - You can provide case category, assignee details, due_date, etc
  - Example: https://lucian-admin.i.firefly.health/admin/quality/measure/3/change/

### STEP 5: Create a care gap worklist
- Go to the worklists admin panel
- click on add worklists
- Then select below details:
  - Description: Description for your worklists(Ex:Clinops/General/Care Gaps)
  - Looker Title: title of your worklists(Ex:Care Gaps)
  - Looker field list: This need to provide dummy array(Ex: ["test"])
  - Looker src pk field: DUmmy data( Ex: test)
  - Due date field: case.due_date
  - Work unit query config: Add all case category filter(Ex: {"query_params": "category_id=98"})
  - Omission config: Select which suitable
  - Reopen config: Select which suitable
  - Active: True
  - Category: Inside which category do You want to show your worklist(Verify in the lucian worklists tab)
  - Sub Category: Under which sub-category do you want to show this worklist
  - Name: same as Looker title
  - Tenant: Firefly Health
- Each time you add a new measure you have to update the Work unit query config to include a new case category(Ex: {"query_params": "category_id=98|category_id=102"})

### STEP 6: Update the cron job to include a new measure
- sync-cron contains all our cron job update sync_measure_reports and create_work_for_care_gaps measure_ids param to include new measure id
- Sample PR: https://github.com/fireflyhealth/lucian/pull/6741

## References

- Data flow: https://whimsical.com/quality-management-LzwVKomBkxcXKK14swFBEN