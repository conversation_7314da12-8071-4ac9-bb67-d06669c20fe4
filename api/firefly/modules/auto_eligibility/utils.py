import logging
import uuid
from datetime import date, datetime, timedelta
from typing import TYPE_CHECKING, Optional

import waffle
from lxml import etree as ET
from zeep import helpers

if TYPE_CHECKING:
    from firefly.modules.appointment.models import Appointment

from firefly.core.services.auto_eligibility_service.models import AutoEligibilityRequestLog, AutoEligibilityResponseLog
from firefly.core.services.flume.constants import CoverageStatus
from firefly.core.user.matching import merge_persons
from firefly.core.user.models.models import Person, User
from firefly.core.user.serializers import UserSerializer
from firefly.core.user.utils import close_duplicate_account_case, create_duplicate_account_case
from firefly.modules.auto_eligibility.client import TrizettoClient
from firefly.modules.auto_eligibility.constants import (
    FIREFLY_PROVIDER_LAST_NAME,
    FIREFLY_PROVIDER_NPI,
    ORGANIZATION_NAME,
    TRIZETTO_POST_URL,
    TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH,
)
from firefly.modules.eligibility.models import Covered<PERSON>ember, EligibilityRecord
from firefly.modules.eligibility.utils import get_primary_subscriber_from_member_key
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.insurance.models import InsuranceMemberInfo
from firefly.modules.insurance.utils import (
    close_appointment_insurance_outreach_case,
    close_outreach_insurance_case,
    create_appointment_insurance_outreach_case,
    create_outreach_insurance_case,
    is_hmo_user_without_firefly_pcp,
    is_insurance_active_for_appointment,
    is_insurance_expired,
    is_payer_anthem_bcbs,
    perform_outreach_for_insurance_visit_prep_case,
    update_anthem_bcbs_payer,
)
from firefly.modules.onboarding.statemachine.utils import update_insurance_task_to_complete_processing
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import person_is_in_program

logger = logging.getLogger(__name__)


# ======================================================================================================================
# NEW ENTRY POINTS
# ======================================================================================================================


def get_and_update_eligibility(person, requested_by_id):
    if person.insurance_info and person.insurance_info.insurance_payer:
        return _autocheck_eligibility(person=person, requested_by_user_id=requested_by_id)
    return False


# ======================================================================================================================
# OLD ENTRY POINTS
# ======================================================================================================================


# TODO: Do not use this method. Use `check_eligibility` instead. This method will be deprecated soon once most of the
#  logic is moved to side-effects.
def check_eligibility_for_patient(
    user_id: int,
    requested_by_id: int,
    is_visit_prep_case_creation_enabled: Optional[bool] = False,
    appointment_date: Optional[str] = None,
    appointment_campaign: Optional[str] = None,
    appointment: Optional["Appointment"] = None,
):
    from firefly.modules.auto_eligibility.tasks import send_eligibility_campaign

    try:
        user = User.objects.get(id=user_id)
        eligibility_status = get_and_update_eligibility(user.person, requested_by_id)
        # TODO: Move to side-effect on InsuranceMemberInfo
        insurance_member_info: InsuranceMemberInfo = user.person.insurance_info
        if is_insurance_expired(insurance_member_info) or is_hmo_user_without_firefly_pcp(insurance_member_info):
            if is_visit_prep_case_creation_enabled:
                create_appointment_insurance_outreach_case(person=user.person, appointment_date=appointment_date)
            if appointment_campaign is not None:
                send_eligibility_campaign(
                    user=user, appointment_campaign=appointment_campaign, appointment_date=appointment_date
                )
                perform_outreach_for_insurance_visit_prep_case(user.person)
        elif is_insurance_active_for_appointment(insurance_member_info):
            close_appointment_insurance_outreach_case(user.person)
        # eligibility_status is False or None
        if eligibility_status is not True:
            create_outreach_insurance_case(user.person)
        # Autoclose if we get an active coverage eligibility
        elif eligibility_status is True:
            close_outreach_insurance_case(user.person)
    except Exception:
        # TODO: How do we handle this silent failing going forward?
        # Failures in auto eligibility should not be handled so that we do not end up making an absurd number of
        # api calls if trizetto is down
        logger.exception("check_eligibility_for_patient: Failed at autochecking insurance for user: %d.", user_id)


# TODO: Do not use this method. Use `check_eligibility` instead. This method will be deprecated soon once most of the
#  logic is moved to side-effects.
def update_eligibility_for_patient(user_id: int, requested_by_user_id: int) -> Optional[bool]:
    person: Person = Person.objects.get(user__pk=user_id)
    # TODO: Move to side-effect on InsuranceMemberInfo
    if is_payer_anthem_bcbs(person):
        update_anthem_bcbs_payer(person)
    return get_and_update_eligibility(person, requested_by_user_id)


def get_latest_eligibility(user):
    data = None
    try:
        # Get eligibility status from EligibilityRecord
        eligibility_status = _get_eligibility_status_based_on_eligibility_record(user.person)
        data = {
            "latest_response_time": datetime.now(),
            "is_eligible": eligibility_status,
            "rejections": "",
            "requested_by_user": {},
        }
    except (EligibilityRecord.DoesNotExist, ValueError):
        response_logs = AutoEligibilityResponseLog.objects.filter(patient_id=user.pk).order_by("-id")
        request_logs = AutoEligibilityRequestLog.objects.filter(patient_id=user.pk).order_by("-id")
        if response_logs.exists():
            response_log: AutoEligibilityResponseLog = response_logs.first()
            data = {
                "latest_response_time": response_log.created_at,
                "is_eligible": response_log.is_eligible,
                "rejections": response_log.rejections,
                "requested_by_user": {},
            }
            request_log = AutoEligibilityRequestLog.objects.get(ticket=response_log.ticket)
            if request_log and request_log.requested_by_user_id:
                requested_by = User.objects.get(pk=request_log.requested_by_user_id)
                data["requested_by_user"] = UserSerializer(requested_by).data
        elif request_logs.exists():
            raise AutoEligibilityResponseLog.DoesNotExist()
    return data


# ======================================================================================================================
# CORE AUTO ELIGIBILITY METHODS
# ======================================================================================================================


def _autocheck_eligibility(person, requested_by_user_id) -> Optional[bool]:
    eligibility_status: bool = False
    # Check if we accept current insurance plan in member's state of residence
    # if not then skip eligibility check in trizetto and return eligibility as false
    if (
        person.insurance_info.insurance_plan is not None
        and person.insurance_info.insurance_plan.firefly_accepted_states.exists()
        and not person.insurance_info.insurance_plan.firefly_accepted_states.filter(
            abbreviation=person.insurance_info.patient_address["state"]
        ).exists()
    ):
        return False
    # Get eligibility status from Flume eligibility file for Firefly members
    try:
        if person.insurance_info.insurance_payer.name == FIREFLY_PAYER:
            return _get_health_plan_eligibility_status(person)
    except Exception:
        # TODO: In other places, we raise the exception if there is an error. Here however, we return "false". Why?
        return False
    if (
        EligibilityRecord.objects.filter(member=person).last() is None
        and person.insurance_info.insurance_payer.name != FIREFLY_PAYER
    ) or (EligibilityRecord.objects.filter(member=person).exists() and not eligibility_status):
        payer_params = _construct_payer_param_from_insurance_payer(person.insurance_info.insurance_payer)
        patient_params = _construct_patient_param(person=person)
        # If there was an eligibility check made in the last 2 weeks and the member's insurance info hasn't been updated
        # since then, use that Trizetto response instead
        window = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=14)
        response = AutoEligibilityResponseLog.objects.filter(person_id=person.pk, created_at__gte=window).order_by(
            "-created_at"
        )
        luci = get_lucian_bot_user()
        churned_program_enrollment = ProgramEnrollment.objects.filter(
            program__uid=ProgramCodes.PRIMARY_CARE,
            person=person,
            period__endswith__isnull=True,
            status=PrimaryCareProgramStatus.CHURNED,
        ).first()
        last_churned_at: datetime = datetime.now()
        if churned_program_enrollment and churned_program_enrollment.period and churned_program_enrollment.period.lower:
            last_churned_at = churned_program_enrollment.period.lower
        if response.exists() and person.insurance_info.updated_at < window and requested_by_user_id is luci.pk:
            eligibility_status = response.last().is_eligible  # type: ignore
        elif churned_program_enrollment and person.insurance_info.updated_at < last_churned_at:
            # If the user is churned/discharged
            # and the member's insurance info hasn't been updated since then,
            # return ineligible
            eligibility_status = False
        else:
            ticket = uuid.uuid4()
            response_dict = _submit_eligibility_request(
                ticket=ticket,
                patient=patient_params,
                provider=_construct_provider_param(),
                service=_construct_service_param(),
                payer=payer_params,
                requested_by_user_id=requested_by_user_id,
            )
            if response_dict is not None:
                eligibility_status = _handle_eligibility_response(
                    ticket=ticket,
                    response_dict=response_dict,
                    patient_params=patient_params,
                    payer_params=payer_params,
                    person=person,
                )
    return eligibility_status


def _submit_eligibility_request(ticket, patient, provider, service, payer, requested_by_user_id):
    """
    Submit a single eligibility request to a specific payer
    """
    if waffle.switch_is_active(TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH):
        return None
    client = TrizettoClient()
    if waffle.switch_is_active(TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH):
        return {}
    inquiry = {
        "ResponseDataType": "Xml",
        "Parameters": {
            "MyNameValue": [
                {"Name": "GediPayerID", "Value": payer["payer_id"]},
                {"Name": "NPI", "Value": provider["provider_npi"]},
                {"Name": "ProviderID", "Value": provider["provider_npi"]},
                {"Name": "ProviderLastName", "Value": provider["provider_last_name"]},
                {"Name": "OrganizationName", "Value": provider["provider_organization_name"]},
                {"Name": "InsuranceNum", "Value": patient["patient_insurance_number"]},
            ]
        },
    }
    # Patient is the primary insurance holder
    if not patient["dependent"]:
        inquiry["Parameters"]["MyNameValue"].append(
            {"Name": "InsuredFirstName", "Value": patient["patient_first_name"]}
        )
        inquiry["Parameters"]["MyNameValue"].append({"Name": "InsuredLastName", "Value": patient["patient_last_name"]})
        inquiry["Parameters"]["MyNameValue"].append({"Name": "InsuredDob", "Value": patient["patient_dob"]})
        inquiry["Parameters"]["MyNameValue"].append({"Name": "InsuredGender", "Value": patient["patient_gender"]})
        inquiry["Parameters"]["MyNameValue"].append(
            {"Name": "InsuredState", "Value": patient["patient_address_state"]},
        )
    # Patient is a dependent, need primary insurance holder info
    else:
        # TODO - parameter check primary got passed
        inquiry["Parameters"]["MyNameValue"].append(
            {"Name": "InsuredFirstName", "Value": patient["primary_first_name"]}
        )
        inquiry["Parameters"]["MyNameValue"].append({"Name": "InsuredLastName", "Value": patient["primary_last_name"]})
        inquiry["Parameters"]["MyNameValue"].append({"Name": "InsuredDob", "Value": patient["primary_dob"]})
        inquiry["Parameters"]["MyNameValue"].append(
            {"Name": "DependentFirstName", "Value": patient["patient_first_name"]}
        )
        inquiry["Parameters"]["MyNameValue"].append(
            {"Name": "DependentLastName", "Value": patient["patient_last_name"]}
        )
        inquiry["Parameters"]["MyNameValue"].append({"Name": "DependentDob", "Value": patient["patient_dob"]})
        inquiry["Parameters"]["MyNameValue"].append({"Name": "DependentGender", "Value": patient["patient_gender"]})
    inquiry["Parameters"]["MyNameValue"].append({"Name": "ServiceTypeCode", "Value": service["service_types"][0]})
    xml_node = client.create_request(inquiry)
    _log_eligibility_check_request(
        TRIZETTO_POST_URL, ticket, patient, provider, service, payer, xml_node, requested_by_user_id
    )
    # TODO - check for unable to communicate and retry it
    response = client.get_response(inquiry)

    return response


def _log_eligibility_check_request(url, ticket, patient, provider, service, payer, xml_node, requested_by_user_id):
    from firefly.core.services.auto_eligibility_service.models import AutoEligibilityRequestLog

    trigger_type = (
        AutoEligibilityRequestLog.TRIGGER_AUTO
        if requested_by_user_id == get_lucian_bot_user().pk
        else AutoEligibilityRequestLog.TRIGGER_LUCIAN
    )
    aereqlg = AutoEligibilityRequestLog(
        endpoint=url,
        ticket=ticket,
        retry=1,
        patient_id=patient["patient_id"],
        person_id=patient["person_id"],
        patient_is_dependent=patient["dependent"],
        patient_insurance_number=patient["patient_insurance_number"],
        patient_first_name=patient["patient_first_name"],
        patient_last_name=patient["patient_last_name"],
        patient_dob=datetime.strptime(patient["patient_dob"], "%Y%m%d").date(),
        patient_gender=patient["patient_gender"],
        patient_address_state=patient["patient_address_state"],
        service_start=datetime.strptime(service["service_start"], "%Y%m%d").date(),
        service_end=datetime.strptime(service["service_end"], "%Y%m%d").date(),
        service_type=service["service_types"][0],
        payer_code=payer["payer_id"],
        provider_last_name=provider["provider_last_name"],
        provider_npi=provider["provider_npi"],
        provider_organization_npi=provider["provider_organization_npi"],
        request_json={"patient": patient, "provider": provider, "service": service, "payer": payer},
        requested_by_user_id=requested_by_user_id,
        raw_request=ET.tostring(xml_node, pretty_print=True, xml_declaration=True, encoding="utf-8").decode(),
        trigger_type=trigger_type,
    )
    if patient["dependent"]:
        aereqlg.primary_first_name = patient["primary_first_name"]
        aereqlg.primary_last_name = patient["primary_last_name"]
        aereqlg.primary_dob = datetime.strptime(patient["primary_dob"], "%Y%m%d").date()
    aereqlg.save()


def _handle_eligibility_response(ticket, response_dict, payer_params, patient_params, person: Person) -> bool:
    """Parses response and makes updates to insurance member info
    Returns a boolean for eligibility status

    Parameters
    ----------
    response_dict : dict
        with key "ResponseAsXml" containing XML string
    payer_params : dict
    patient_params : dict
    person : Person
    """

    serialized_response = helpers.serialize_object(response_dict)
    response_log = AutoEligibilityResponseLog(
        ticket=ticket,
        retry=1,
        payer_code=payer_params["payer_id"],
        raw_response=serialized_response,
        response_json=serialized_response,
        patient_id=patient_params["patient_id"],
        person_id=patient_params["person_id"],
    )
    response_log.save()
    if response_log.has_error_in_parsing:
        raise Exception("Unable to parse response")

    return bool(response_log.is_eligible)


# ======================================================================================================================
# CONSTRUCTOR METHODS
# ======================================================================================================================


def _construct_payer_param_from_insurance_payer(payer):
    # Check to see if how many id's for this payer
    if len(payer.payer_codes) == 0:
        raise Exception(f"no payer codes defined in db for payer {payer.pk}")
    # only 1 so use what elation gave us, given they probably know best
    elif len(payer.payer_codes) == 1:
        return {"payer_id": payer.elation_payer_code}
    # TOOO - if multiple payers, then loop through them all, but , not synchronously
    elif len(payer.payer_codes) > 1:
        return {"payer_id": payer.payer_codes}


def _construct_patient_param(person):
    insurance_info = person.insurance_info
    patient = {
        "dependent": bool(
            insurance_info.is_primary_subscriber is False and insurance_info.primary_last_name is not None
        ),
        "patient_insurance_number": insurance_info.member_id,
        "patient_first_name": person.first_name.upper(),
        "patient_last_name": person.last_name.upper(),
        "patient_dob": person.dob.strftime("%Y%m%d"),
        "patient_gender": ("M" if (person.sex.upper() == "MALE") else "F"),
        "patient_address_state": insurance_info.state,
        "patient_id": person.user.id,
        "person_id": person.id,
    }
    if bool(
        insurance_info.is_primary_subscriber is False
        and insurance_info.primary_first_name != ""
        and insurance_info.primary_last_name != ""
        and insurance_info.primary_dob is not None
    ):
        patient["primary_first_name"] = insurance_info.primary_first_name.upper()
        patient["primary_last_name"] = insurance_info.primary_last_name.upper()
        patient["primary_dob"] = insurance_info.primary_dob.strftime("%Y%m%d")
    else:
        patient["primary_first_name"] = ""
        patient["primary_last_name"] = ""
        patient["primary_dob"] = ""
    return patient


def _construct_provider_param():
    return {
        "provider_last_name": FIREFLY_PROVIDER_LAST_NAME,
        "provider_npi": FIREFLY_PROVIDER_NPI,
        "provider_organization_name": ORGANIZATION_NAME,
        "provider_organization_npi": FIREFLY_PROVIDER_NPI,
    }


def _construct_service_param():
    return {
        "service_start": date.today().strftime("%Y%m%d"),
        "service_end": date.today().strftime("%Y%m%d"),
        "service_types": ["30"],
    }


# ======================================================================================================================
# HELPER METHODS
# ======================================================================================================================


def _get_health_plan_eligibility_status(person):
    member_id = person.insurance_info.member_id
    primary_subscriber = None
    if not member_id:
        raise Exception("Member ID not found")
    # Get primary subscriber associated with member ID. In the case of a member ID that comes from an image of
    # an ID card, the ID will always be for the primary subscriber, not the actual member, so we need to
    # identify actual member based on their name and DOB.
    primary_subscriber = get_primary_subscriber_from_member_key(member_id)
    if primary_subscriber is None:
        raise Exception("primary_subscriber not found")
    if person == primary_subscriber:
        # Account has already been linked to eligibility data
        return _get_eligibility_status_based_on_eligibility_record(person)
    # Get covered members without a User, where DOB matches and where both DOB and name match
    dob_matches = set()
    name_matches = set()
    covered_members = CoveredMember.objects.filter(primary_subscriber_person=primary_subscriber)
    # covered_members of eligibility record
    for covered_member in covered_members.iterator():
        member = covered_member.covered_member_person
        if not member.user and member.dob == person.dob:
            dob_matches.add(member)
            if member.first_name == person.first_name and member.last_name == person.last_name:
                name_matches.add(member)
        # member has signed up and merged already
        elif member.user and member == person:
            return _get_eligibility_status_based_on_eligibility_record(member)
    # If member ID for primary subscriber matches (i.e. these members are in the same family), and the DOB
    # matches and is unique for this member, or both DOB and name match, then automatically merge
    match = dob_matches.pop() if len(dob_matches) == 1 else name_matches.pop() if len(name_matches) == 1 else None
    if match:
        merge_person_results = merge_persons(
            src_person_id=match.pk,
            dest_person_id=person.pk,
            src_person_email=match.email,
            dest_person_email=person.email,
        )
        close_duplicate_account_case(person, merge_person_results=merge_person_results)
        update_insurance_task_to_complete_processing(person)
        return _get_eligibility_status_based_on_eligibility_record(person)
    # No matching member was found, this may be a duplicate account. Create duplicate account case.
    duplicate_data = {
        "create_case": True,
        "duplicate_person": primary_subscriber,  # If no match found, default to primary subscriber as duplicate person
    }
    create_duplicate_account_case(duplicate_data, person, None)
    return False


def _get_eligibility_status_based_on_eligibility_record(person):
    if person_is_in_program(person, ProgramCodes.BENEFIT):
        try:
            eligibility_record = EligibilityRecord.objects.get(member=person)

            insurance_info = person.insurance_info
            insurance_info.coverage_start = eligibility_record.effective_date
            insurance_info.coverage_end = eligibility_record.termination_date
            insurance_info.save(update_fields=["coverage_start", "coverage_end"])

            if eligibility_record.coverage_status in CoverageStatus.ACTIVES:
                if (
                    eligibility_record.termination_date is None
                    or eligibility_record.termination_date.date() > date.today()
                ):
                    return True
                else:
                    logger.warning(
                        "Eligibility validation: Person %s has active coverage_status '%s' "
                        "but termination_date %s is in the past",
                        person.id,
                        eligibility_record.coverage_status,
                        eligibility_record.termination_date,
                    )
                    return False
            else:
                # If coverage_status is not active, check if termination_date is in the past
                if (
                    eligibility_record.termination_date is not None
                    and eligibility_record.termination_date.date() <= date.today()
                ):
                    logger.info(
                        "Eligibility validation: Person %s has inactive coverage_status '%s' "
                        "with termination_date %s in the past",
                        person.id,
                        eligibility_record.coverage_status,
                        eligibility_record.termination_date,
                    )
                    return False
                # Otherwise, return False as the coverage is not active
                logger.warning(
                    "Eligibility validation: Person %s has inactive coverage_status '%s' "
                    "but termination_date %s is not in the past",
                    person.id,
                    eligibility_record.coverage_status,
                    eligibility_record.termination_date,
                )
                return False
        except EligibilityRecord.DoesNotExist:
            logger.warning("Eligibility validation: No eligibility record found for person %s", person.id)
            return False

    raise ValueError("Person %s is not in Benefit program", person.id)
