# Auto Eligibility

The Auto Eligibility module provides automated insurance eligibility verification through Trizetto clearinghouse integration. It verifies member coverage, checks if Firefly is the primary care provider, and handles eligibility-related workflows.

## Overview

This module automates insurance eligibility verification by integrating with Trizetto clearinghouse to check member coverage status. It determines whether members are eligible for services, if Firefly is their designated PCP, and triggers appropriate workflows based on eligibility results.

## Business Logic

The eligibility verification process works as follows:

1. **Triggered Verification**: Eligibility checks are triggered by insurance plan reviews, appointment scheduling, or manual requests
2. **Trizetto Integration**: Requests are sent to Trizetto clearinghouse using SOAP API
3. **Response Processing**: XML responses are parsed to extract eligibility status, PCP information, and coverage details
4. **Workflow Actions**: Based on results, the system may create cases, send notifications, or update member records

## Core Models

### AutoEligibilityRequestLog
Tracks eligibility verification requests:
- **patient_id/person_id**: Member being verified
- **patient_insurance_number**: Member's insurance ID
- **patient_insurance_name**: Insurance plan name
- **patient_first_name/patient_last_name**: Member demographics
- **trigger_type**: What triggered the verification (manual, auto, etc.)
- **payer_code**: Insurance payer identifier
- **ticket**: Unique request identifier

### AutoEligibilityResponseLog
Stores eligibility verification responses:
- **ticket**: Links to the request
- **is_eligible**: Whether member has active coverage
- **firefly_is_pcp**: Whether Firefly is designated as PCP
- **pcp_npi**: NPI of the designated primary care provider
- **pcp_name**: Name of the designated PCP
- **response_json**: Parsed eligibility data
- **raw_response**: Original XML response from Trizetto
- **rejections**: Array of rejection reasons if verification failed

## Verification Triggers

### Automated Triggers
The system automatically triggers eligibility checks in these scenarios:

- **Insurance Plan Review**: When insurance plans are reviewed and approved
- **Appointment Scheduling**: Before appointment confirmation (48-hour and 24-hour campaigns)
- **PCP Updates**: When primary care provider information changes
- **New Member Onboarding**: For members who signed up within the last 30 days

### Manual Triggers
- **Provider Requests**: Healthcare providers can manually request eligibility verification
- **Administrative Reviews**: Staff can trigger verification for specific cases

## Trizetto Integration

### SOAP API Client
The module uses a SOAP client to communicate with Trizetto:
- **WSDL URL**: Configured Trizetto web service endpoint
- **Authentication**: Username/password authentication via SOAP headers
- **Timeout Configuration**: 10-second timeout for requests
- **Rate Limiting**: Configurable rate limits to respect Trizetto's API limits

### Request Structure
Eligibility requests include:
- **Patient Information**: Demographics and insurance details
- **Provider Information**: Firefly provider details
- **Service Information**: Service codes being verified
- **Payer Information**: Insurance payer details

### Response Processing
XML responses are parsed to extract:
- **Eligibility Status**: Active/inactive coverage
- **PCP Information**: Designated primary care provider
- **Coverage Details**: Plan information and effective dates
- **Rejection Reasons**: Detailed error information if verification fails

## Workflow Actions

### Eligibility Results Processing
Based on verification results, the system takes different actions:

#### Eligible Members
- **Firefly as PCP**: No additional action required
- **Different PCP**: May create tasks for PCP update (HMO plans)
- **Coverage Verification**: Updates member eligibility status

#### Ineligible Members
- **Expired Insurance**: Triggers Braze email and push notifications
- **Plan Not Accepted**: Creates cases for insurance review
- **Missing Information**: Generates tasks for data collection

### Notification Campaigns
The system sends targeted notifications based on eligibility status:

#### Expired Insurance Notifications
- **48-hour Campaign**: Email and push notification about expired insurance
- **24-hour Campaign**: Follow-up push notification with appointment details

#### HMO PCP Attribution
- **48-hour Campaign**: Email and push notification for BCBS MA HMO members
- **24-hour Campaign**: Follow-up push notification about PCP requirements

## Technical Implementation

### Rate Limiting
- **Mutex Lock**: Prevents concurrent eligibility checks for the same operation
- **Configurable Limits**: Rate limits can be adjusted via settings
- **Queue Management**: Uses dedicated "eligibility-check" queue for processing

### Error Handling
- **Retry Logic**: Built-in retry mechanisms for failed requests
- **Graceful Degradation**: System continues to function if Trizetto is unavailable
- **Logging**: Comprehensive logging for debugging and monitoring

### Integration Points
- **Insurance Module**: Updates member insurance information
- **Cases Module**: Creates cases for insurance issues
- **Tasks Module**: Generates PCP update tasks
- **Braze Integration**: Sends targeted member communications
- **Appointment System**: Verifies eligibility before appointments

## Key Functions

### Core Eligibility Functions
- **`get_and_update_eligibility()`**: Main function to check and update member eligibility
- **`check_eligibility_for_patient()`**: Comprehensive eligibility check with workflow actions
- **`update_eligibility_for_patient()`**: Updates eligibility status and handles PCP changes

### Trizetto Client Functions
- **`TrizettoClient.get_response()`**: Sends SOAP request to Trizetto and returns response
- **`parse_payer_response()`**: Parses XML response into structured data
- **`_construct_patient_param()`**: Builds patient parameters for Trizetto request

### Workflow Functions
- **`send_eligibility_campaign()`**: Sends targeted notifications based on eligibility status
- **`is_firefly_the_pcp()`**: Determines if Firefly is the designated PCP
- **`_get_health_plan_eligibility_status()`**: Checks Firefly member eligibility

## Configuration

### Trizetto Settings
- **TRIZETTO_USER**: Username for Trizetto authentication
- **TRIZETTO_PASSWORD**: Password for Trizetto authentication
- **TRIZETTO_WSDL_URL**: Trizetto web service endpoint
- **AUTO_ELIGIBILITY_CHECK_ON_PLAN_REVIEW_RATE_LIMIT**: Rate limiting configuration

### Feature Flags
- **TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH**: Disables Trizetto integration when active

## Dramatiq Tasks

### Background Processing
- **`check_eligibility_of_valid_plan`**: Triggered by insurance state machine transitions
- **`check_eligibility_for_patients_associated_with_plan`**: Bulk verification for plan members
- **`check_eligibility_for_patient`**: Individual patient verification with rate limiting
- **`check_pcp_updated_for_patient`**: Handles PCP updates and task management

## Use Cases

### Pre-Appointment Verification
1. Member schedules appointment
2. System triggers eligibility check 48 hours before appointment
3. If insurance expired, sends notification campaigns
4. If HMO without Firefly PCP, sends PCP attribution notifications

### Insurance Plan Review
1. Insurance plan gets reviewed and approved
2. System finds all members with that plan (signed up in last 30 days)
3. Triggers eligibility verification for each member
4. Creates appropriate cases or tasks based on results

### PCP Management
1. Member's PCP information changes
2. System re-verifies eligibility
3. For HMO plans without Firefly PCP, creates or updates PCP tasks
4. Ensures member has appropriate primary care attribution
