import json
import logging
import os
import uuid
from datetime import date, datetime, timedelta
from unittest import mock
from unittest.mock import patch

import pandas as pd
import pytest
from django.conf import settings
from django.contrib.auth.models import Group
from dramatiq.rate_limits.backends import StubBackend
from lxml import etree as ET

from firefly.core.alias.models import <PERSON>as<PERSON>ap<PERSON>, AliasName
from firefly.core.assignment.constants import AssignmentScheme
from firefly.core.feature.testutils import override_switch
from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.roles.models import Role
from firefly.core.services.auto_eligibility_service.models import AutoEligibilityRequestLog, AutoEligibilityResponseLog
from firefly.core.services.flume.constants import CoverageStatus
from firefly.core.services.flume.parsers import parse_eligibility_v2
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP, MD_ROLE
from firefly.core.user.factories import <PERSON><PERSON><PERSON>ser<PERSON>actory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import <PERSON>sign<PERSON><PERSON><PERSON>, Person
from firefly.modules.appointment.constants import Appointment<PERSON>ampaign
from firefly.modules.auto_eligibility import tasks
from firefly.modules.auto_eligibility.constants import (
    ELATION_PAYER_CODES,
    TRIZETTO_ERRORS,
    TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH,
    RejectionMessage,
)
from firefly.modules.auto_eligibility.parser import (
    get_pcp_npi_from_response,
    is_firefly_the_pcp,
    is_subscriber_or_dependent,
    parse_date_of_service,
    parse_payer_response,
    pcp_name_from_response,
)
from firefly.modules.auto_eligibility.utils import (
    _autocheck_eligibility,
    _construct_patient_param,
    _construct_payer_param_from_insurance_payer,
    _construct_provider_param,
    _construct_service_param,
    _get_eligibility_status_based_on_eligibility_record,
    _handle_eligibility_response,
    _submit_eligibility_request,
    get_and_update_eligibility,
)
from firefly.modules.cases.constants import (
    INSURANCE_OUTREACH,
    INSURANCE_PLAN_NEEDS_REVIEW,
    INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY,
    CaseStatus,
)
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.cases.utils import get_open_case
from firefly.modules.eligibility.models import EligibilityRecord
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.insurance.factories import InsuranceMemberInfoFactory, InsurancePayerFactory
from firefly.modules.insurance.models import (
    PAYER_BCBS_MA,
    Employer,
    InsuranceMemberInfo,
    InsurancePayer,
    InsurancePlan,
)
from firefly.modules.insurance.utils import create_outreach_insurance_case
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.physician.factories import PhysicianFactory
from firefly.modules.physician.utils import get_npi_list_of_attributable_providers
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER, FIREFLY_PLAN
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, remove_person_from_program
from firefly.modules.quality.constants import CaseSystemAction
from firefly.modules.statemachines.contants import StateMachineSystemAction
from firefly.modules.statemachines.insurance_plan_review.mixin import InsurancePlanReviewTransitionMetadata
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.statemachines.utils import convert_date_to_end_of_working_est
from firefly.modules.states.models import State
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.factories import TaskCollectionFactory, TaskCollectionTaskFactory
from firefly.modules.tasks.models import Task, TaskCollection, TaskCollectionTask
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)

TEST_ELIGIBILITY_RESPONSE_WITH_SUBSCRIBER = """
<eligibilityresponse>
<subscriber>
<relationship>
</relationship>
</subscriber>
</eligibilityresponse>
"""

TEST_ELIGIBILITY_RESPONSE_WITH_DEPENDENT = """
<eligibilityresponse>
<dependent>
<relationship>
</relationship>
</dependent>
</eligibilityresponse>
"""

TEST_ELIGIBILITY_RESPONSE_WITH_BOTH = """
<eligibilityresponse>
<subscriber>
<relationship>
</relationship>
</subscriber>

<dependent>
<relationship>
</relationship>
</dependent>
</eligibilityresponse>
"""


class AutoEligibilityTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp()
        get_npi_list_of_attributable_providers.cache_clear()

    def test_subscriber_or_dependent_subscriber(self):
        self.assertEqual(
            is_subscriber_or_dependent(ET.fromstring(TEST_ELIGIBILITY_RESPONSE_WITH_SUBSCRIBER)),
            "subscriber",
        )

    def test_subscriber_or_dependent_both(self):
        self.assertEqual(
            is_subscriber_or_dependent(ET.fromstring(TEST_ELIGIBILITY_RESPONSE_WITH_BOTH)),
            "dependent",
        )

    def test_subscriber_or_dependent_dependent(self):
        self.assertEqual(
            is_subscriber_or_dependent(ET.fromstring(TEST_ELIGIBILITY_RESPONSE_WITH_DEPENDENT)),
            "dependent",
        )

    def test_construct_patient_param_primary(self):
        person = PersonUserFactory.create()
        person.insurance_info.is_primary_subscriber = True

        patient_info = _construct_patient_param(person)

        self.assertFalse(patient_info["dependent"])
        self.assertEqual(patient_info["primary_last_name"], "")

    def test_construct_patient_param_dependent(self):
        #
        person = PersonUserFactory.create()
        person.insurance_info.is_primary_subscriber = False
        person.insurance_info.primary_first_name = "Test"
        person.insurance_info.primary_last_name = "Primary"
        person.insurance_info.primary_dob = datetime.strptime("19541213", "%Y%m%d")

        patient_info = _construct_patient_param(person)

        self.assertTrue(patient_info["dependent"])
        self.assertNotEqual(patient_info["primary_last_name"], "")

    def test_construct_patient_param_dependent_nolastname(self):
        #
        person = PersonUserFactory.create()
        person.insurance_info.is_primary_subscriber = False

        patient_info = _construct_patient_param(person)

        self.assertFalse(patient_info["dependent"])
        self.assertEqual(patient_info["primary_last_name"], "")

    def test_parser_with_active_coverage(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        parsed_response = {}
        with open(fixture_file, "r") as file:
            response_dict["ResponseAsXml"] = file.read()
            parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, response_dict)
        self.assertTrue(parsed_response["eligible"])
        self.assertEqual(parsed_response["coverage_start"], "2018-07-01")

    def test_parser_with_none_dict(self):
        parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, None)
        self.assertIsNone(parsed_response)

    def test_parser_with_empty_dict(self):
        parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, {})
        self.assertIsNone(parsed_response)

    def test_parse_date_of_service_unknown_payer(self):
        response_string = """
            <eligibilityresponse>
            <payername>Not A Real Payer</payername>
            <subscriber>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan Begin</datequalifier>
            <date-of-service>20190909</date-of-service>
            </date>
            </subscriber></eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], None)

    def test_parse_date_of_service_tufts_subscriber(self):
        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <subscriber>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan Begin</datequalifier>
            <date-of-service>20190909</date-of-service>
            </date>
            </subscriber>
            </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2018-08-08")

        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <subscriber>
            <date>
            <datequalifier>Plan</datequalifier>
            <date-of-service>20240930</date-of-service>
            </date>
            </subscriber>
            </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2024-09-30")

        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <subscriber>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan</datequalifier>
            <date-of-service>20240930</date-of-service>
            </date>
            </subscriber>
            </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2018-08-08")

    def test_parse_date_of_service_tufts_dependent(self):
        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <dependent>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan Begin</datequalifier>
            <date-of-service>20190909</date-of-service>
            </date>
            </dependent
            ></eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2018-08-08")

        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <dependent>
            <date>
            <datequalifier>Plan</datequalifier>
            <date-of-service>20240930</date-of-service>
            </date>
            </dependent
            ></eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2024-09-30")

        response_string = """
            <eligibilityresponse>
            <payername>Tufts Health Plan</payername>
            <dependent>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan</datequalifier>
            <date-of-service>20240930</date-of-service>
            </date>
            </dependent>
            </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2018-08-08")

    def test_parse_date_of_service_cho_subscriber(self):
        response_string = """
            <eligibilityresponse>
            <payername>MAINE COMMUNITY HEALTH OPTIONS</payername>
            <subscriber>
            <date>
            <datequalifier>Plan</datequalifier>
            <date-of-service>20240101-20241231</date-of-service>
            </date>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20240101-20241231</date-of-service>
            </date>
            </subscriber></eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2024-01-01")

    def test_parse_date_of_service_cho_plan(self):
        response_string = """
        <eligibilityresponse>
        <payername>MAINE COMMUNITY HEALTH OPTIONS</payername>
        <subscriber>
            <benefit>
                <info>Inactive</info>
                <servicetype>Health Benefit Plan Coverage</servicetype>
                <servicetypecode>30</servicetypecode>
                <datequalifier>Plan</datequalifier>
                <date-of-service>20240401-20241231</date-of-service>
            </benefit>
        </subscriber>
        </eligibilityresponse>"""
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2024-04-01")

    def test_parse_date_of_service_aetna(self):
        response_string = """
            <eligibilityresponse>
            <payername>AETNA INC</payername>
            <dependent>
            <date>
            <datequalifier>Eligibility</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
            <date>
            <datequalifier>Plan Begin</datequalifier>
            <date-of-service>20190909</date-of-service>
            </date>
            </dependent>
            </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2019-09-09")

    def test_parse_date_of_service_health_plans(self):
        response_string = """
        <eligibilityresponse>
            <payername>HEALTH PLANS INC</payername>
            <dependent>
                <date>
                    <datequalifier>Eligibility</datequalifier>
                    <date-of-service>20180808</date-of-service>
                </date>
                <date>
                    <datequalifier>Plan Begin</datequalifier>
                    <date-of-service>20190909</date-of-service>
                </date>
            </dependent>
        </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2019-09-09")

    def test_parse_date_of_service_bcbs_ma(self):
        response_string = """
        <eligibilityresponse>
        <payername>BLUE CROSS BLUE SHIELD OF MASSACHUSETTS</payername>
        <subscriber>
        <date>
        <datequalifier>Eligibility Begin</datequalifier>
        <date-of-service>20110101</date-of-service>
        </date>
        <benefit>
            <info>Active Coverage</info>
            <servicetype>Health Benefit Plan Coverage</servicetype>
            <servicetypecode>90</servicetypecode>
            <insurancetype>Preferred Provider Organization (PPO)</insurancetype>
            <insurancetypecode>PR</insurancetypecode>
            <plancoveragedescription>CW COMMUNITY CHOICE</plancoveragedescription>
            <date>
            <datequalifier>Bad Date</datequalifier>
            <date-of-service>20190909</date-of-service>
            </date>
        </benefit>
         <benefit>
            <info>Active Coverage</info>
            <servicetype>Health Benefit Plan Coverage</servicetype>
            <servicetypecode>30</servicetypecode>
            <insurancetype>Preferred Provider Organization (PPO)</insurancetype>
            <insurancetypecode>PR</insurancetypecode>
            <plancoveragedescription>CW COMMUNITY CHOICE</plancoveragedescription>
            <date>
            <datequalifier>Primary Care Provider</datequalifier>
            <date-of-service>20180808</date-of-service>
            </date>
        </benefit>
        </subscriber>
        </eligibilityresponse>"""
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2018-08-08")

    def test_parse_date_of_service_harvard_pilgrim(self):
        response_string = """
        <eligibilityresponse>
            <infosource>
                <payername>HARVARD PILGRIM HEALTH CARE</payername>
            </infosource>
            <benefit>
                <info>Active Coverage</info>
                <servicetype>Health Benefit Plan Coverage</servicetype>
                <servicetypecode>30</servicetypecode>
                <datequalifier>Eligibility</datequalifier>
                <date-of-service>20200701-99991231</date-of-service>
            </benefit>
        </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2020-07-01")

    def test_parse_date_of_service_unicare(self):
        response_string = """
        <eligibilityresponse>
            <infosource>
                <payername>UNICARE</payername>
            </infosource>
            <dependent>
                <date>
                    <datequalifier>Plan</datequalifier>
                    <date-of-service>20070101-99991231</date-of-service>
                </date>
                <benefit>
                    <info>Benefit Disclaimer</info>
                    <message>Test</message>
                </benefit>
            </dependent>
        </eligibilityresponse>
        """
        xml = ET.fromstring(response_string)
        result = parse_date_of_service(response_xml_root=xml)
        self.assertEqual(result["coverage_start"], "2007-01-01")
        self.assertEqual(result["coverage_end"], "9999-12-31")

    def test_parser_without_active_coverage(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_without_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        parsed_response = {}
        with open(fixture_file, "r") as file:
            response_dict["ResponseAsXml"] = file.read()
            parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, response_dict)

        self.assertFalse(parsed_response["eligible"])
        # Verify that coverage dates are not set if there isnt an active eligibility
        # This is necessary since downstream side-effects (in insurance member info)
        # assume the presence of a coverage date to indicate that the member can have
        # active coverage
        self.assertIsNone(parsed_response["coverage_start"])
        self.assertIsNone(parsed_response["coverage_end"])

    def test_parser_primary_unknown_to_payer(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_unknown_by_payer.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        parsed_response = {}
        with open(fixture_file, "r") as file:
            response_dict["ResponseAsXml"] = file.read()
            parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, response_dict)

        self.assertFalse(parsed_response["eligible"])

    def test_parser_subscriber_unknown_to_payer(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "subscriber_unknown_by_payer.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        parsed_response = {}
        with open(fixture_file, "r") as file:
            response_dict["ResponseAsXml"] = file.read()
            parsed_response = parse_payer_response("000", {"patient_last_name": "TestPatient"}, response_dict)

        self.assertFalse(parsed_response["eligible"])
        self.assertEqual(
            parsed_response["rejections"]["000"],
            ["Invalid/Missing Subscriber/Insured ID"],
        )

    def test_pcp_name_from_response(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = pcp_name_from_response(xml)
        self.assertEqual(result, "MARIA PRO-RISQUEZ")
        xml = ET.fromstring("<test></test>")
        result = pcp_name_from_response(xml)
        self.assertEqual(result, None)

    def test_partial_pcp_name_from_response(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage_with_partial_pcp_name.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = pcp_name_from_response(xml)
        self.assertEqual(result, "PRO-RISQUEZ")
        xml = ET.fromstring("<test></test>")
        result = pcp_name_from_response(xml)
        self.assertEqual(result, None)

    def test_pcp_name_from_multiple_nodes_from_response(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage_with_multiple_pcp.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = pcp_name_from_response(xml)
        self.assertEqual(result, "PRO-RISQUEZ")
        xml = ET.fromstring("<test></test>")
        result = pcp_name_from_response(xml)
        self.assertEqual(result, None)

    def test_pcp_npi_from_response(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = get_pcp_npi_from_response(xml)
        self.assertEqual(result, "**********")
        xml = ET.fromstring("<test></test>")
        result = get_pcp_npi_from_response(xml)
        self.assertEqual(result, None)
        fixture_filename = "primary_without_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = get_pcp_npi_from_response(xml)
        self.assertEqual(result, None)
        fixture_filename = "primary_with_active_coverage_bcbs_with_firefly.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = get_pcp_npi_from_response(xml)
        self.assertEqual(result, None)

    def test_is_firefly_pcp(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        file_contents = open(fixture_file, "r").read()
        xml = ET.fromstring(file_contents)
        result = get_pcp_npi_from_response(xml)
        self.assertEqual(result, "**********")
        is_firefly_pcp = is_firefly_the_pcp(pcp_npi=result)
        self.assertFalse(is_firefly_pcp)
        # Verify that the is_firefly_pcp gets set when the pcp_npi points to a firefly physician
        np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        np_physician = PhysicianFactory.create(npi="**********")
        ProviderDetailFactory(
            internal_role=np_role,
            physician=np_physician,
        )
        get_npi_list_of_attributable_providers.cache_clear()
        is_firefly_pcp = is_firefly_the_pcp(pcp_npi=result)
        self.assertTrue(is_firefly_pcp)

    def test_last_eligibility_time(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        with open(fixture_file, "r") as f:
            response_dict["ResponseAsXml"] = f.read()
        person: Person = PersonUserFactory.create()
        ticket_id: str = str(uuid.uuid4())
        patient = _construct_patient_param(person)
        _handle_eligibility_response(
            ticket=ticket_id,
            patient_params=patient,
            payer_params={"payer_id": "04293"},
            response_dict=response_dict,
            person=person,
        )
        count = AutoEligibilityResponseLog.objects.filter(patient_id=person.user.id).count()
        self.assertEqual(count, 1)
        eligibility_response_log: AutoEligibilityResponseLog = AutoEligibilityResponseLog.objects.filter(
            patient_id=person.user.id
        ).first()
        eligibility_response_log_dict: dict = vars(eligibility_response_log)
        ignored_fields = [
            "_state",
            "id",
            "created_at",
            "created_by_id",
            "updated_at",
            "updated_by_id",
            "response_json",
            "deleted",
            "html_response_code",
            "raw_response",
        ]
        for field in ignored_fields:
            eligibility_response_log_dict.pop(field)
        self.assertEqual(
            eligibility_response_log_dict,
            {
                "plan_coverage_description": "EPO",
                "payer_code": "04293",
                "ticket": ticket_id,
                "insurance_type": "Point of Service (POS)",
                "coverage_end": None,
                "coverage_start": date(2018, 7, 1),
                "firefly_is_pcp": False,
                "is_eligible": True,
                "patient_id": person.user.pk,
                "payer_name": None,
                "person_id": person.pk,
                "rejections": [RejectionMessage.PLAN_NOT_IN_LUCIAN],
                "retry": 1,
                "pcp_npi": "**********",
                "pcp_name": "MARIA PRO-RISQUEZ",
                "has_error_in_parsing": False,
            },
        )

    @mock.patch("firefly.modules.auto_eligibility.utils._log_eligibility_check_request")
    @mock.patch("firefly.modules.auto_eligibility.utils.TrizettoClient")
    def test_submit_trizetto_rt_eligibility_request(
        self,
        mock_trizetto_client,
        mock_log_eligibility_check_request,
    ):
        mock_get_response = mock_trizetto_client.return_value.get_response
        person = PersonUserFactory.create()
        self.assertEqual(AutoEligibilityResponseLog.objects.filter(patient_id=person.user.id).count(), 0)
        self.assertEqual(AutoEligibilityRequestLog.objects.filter(patient_id=person.user.id).count(), 0)
        _submit_eligibility_request(
            ticket=uuid.uuid4(),
            patient=_construct_patient_param(person),
            provider=_construct_provider_param(),
            service=_construct_service_param(),
            payer={"payer_id": "04293"},
            requested_by_user_id=self.provider.pk,
        )
        mock_get_response.assert_called_once()
        mock_log_eligibility_check_request.assert_called_once()

    @override_switch(TRIZETTO_REQUESTS_DISABLED_WAFFLE_SWITCH, active=True)
    def test_submit_eligibility_request_trizetto_disabled(self):
        self.assertIsNone(
            _submit_eligibility_request(
                ticket=None,
                patient=None,
                provider=None,
                service=None,
                payer=None,
                requested_by_user_id=None,
            )
        )

    def test_rejection_messages(self):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "rejected_request.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        response_dict = {}
        with open(fixture_file, "r") as file:
            response_dict["ResponseAsXml"] = file.read()
        # Test mismatched name
        person_1 = PersonUserFactory.create(
            first_name="FIRST", last_name="PATIENT", dob=date(year=1992, month=6, day=20), sex="Male"
        )
        parsed_response = parse_payer_response("000", _construct_patient_param(person_1), response_dict)
        self.assertFalse(parsed_response["eligible"])
        self.assertEqual(
            parsed_response["rejections"]["000"],
            [f"{RejectionMessage.NAME_MISMATCH}: Invalid/Missing Subscriber/Insured ID"],
        )
        # Test mismatched DOB
        person_2 = PersonUserFactory.create(
            first_name="TEST", last_name="PATIENT", dob=date(year=1952, month=4, day=27), sex="Male"
        )
        parsed_response = parse_payer_response("000", _construct_patient_param(person_2), response_dict)
        self.assertFalse(parsed_response["eligible"])
        self.assertEqual(
            parsed_response["rejections"]["000"],
            [f"{RejectionMessage.DOB_MISMATCH}: Invalid/Missing Subscriber/Insured ID"],
        )
        # Test mismatched gender
        person_2 = PersonUserFactory.create(
            first_name="TEST", last_name="PATIENT", dob=date(year=1992, month=6, day=20), sex="Female"
        )
        parsed_response = parse_payer_response("000", _construct_patient_param(person_2), response_dict)
        self.assertFalse(parsed_response["eligible"])
        self.assertEqual(
            parsed_response["rejections"]["000"],
            [f"{RejectionMessage.GENDER_MISMATCH}: Invalid/Missing Subscriber/Insured ID"],
        )


class TasksTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.insurance.utils.update_contract_and_attribution")
    @mock.patch("firefly.modules.insurance.utils.handle_invalid_insurance")
    @mock.patch("firefly.modules.statemachines.coverage.callbacks.create_new_insurance_case")
    @mock.patch("firefly.modules.auto_eligibility.utils._autocheck_eligibility")
    def test_check_eligibility_of_valid_plan(
        self,
        mock_autocheck_eligibility,
        mock_create_new_insurance_case,
        mock_handle_invalid_insurance,
        mock_attribution,
    ):
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW, title=INSURANCE_PLAN_NEEDS_REVIEW)
        self.patient.person.attribution.attribution_requirement_type = "POC"
        self.patient.person.attribution.save()
        # Given the insurance is in a processing state
        onboarding_state = self.patient.onboarding_state
        onboarding_state.to_signedup()
        # Given the eligibility check returned a negative
        mock_autocheck_eligibility.return_value = False
        with self.captureOnCommitCallbacks(execute=True):
            self.patient.person.insurance_info.coverage_start = date(year=2025, month=1, day=1)
            self.patient.person.insurance_info.coverage_end = date(year=2025, month=2, day=1)
            self.patient.person.insurance_info.save(update_fields=["coverage_start", "coverage_end"])
            tasks.check_eligibility_of_valid_plan(
                person_id=self.patient.person.id, requested_by_user_id=self.patient.pk
            )
        mock_create_new_insurance_case.assert_not_called()
        mock_handle_invalid_insurance.assert_called_once()
        mock_create_new_insurance_case.reset_mock()
        mock_handle_invalid_insurance.reset_mock()
        # Given the eligibility check returned a positive
        mock_autocheck_eligibility.return_value = True
        mock_create_new_insurance_case.reset_mock()
        mock_handle_invalid_insurance.reset_mock()
        # Given we get an error from Trizetto
        mock_autocheck_eligibility.return_value = False
        initial_coverage_start_date = date(year=2024, month=1, day=1)
        initial_coverage_end_date = date(year=2024, month=2, day=1)
        with self.captureOnCommitCallbacks(execute=True):
            self.patient.person.insurance_info.coverage_start = initial_coverage_start_date
            self.patient.person.insurance_info.coverage_end = initial_coverage_end_date
            self.patient.person.insurance_info.save(update_fields=["coverage_start", "coverage_end"])
            tasks.check_eligibility_of_valid_plan(
                person_id=self.patient.person.id, requested_by_user_id=self.patient.pk
            )
        self.assertEqual(self.patient.person.insurance_info.coverage_start, initial_coverage_start_date)
        self.assertEqual(self.patient.person.insurance_info.coverage_end, initial_coverage_end_date)
        mock_create_new_insurance_case.assert_not_called()
        mock_handle_invalid_insurance.assert_called_once()
        mock_create_new_insurance_case.reset_mock()
        mock_handle_invalid_insurance.reset_mock()
        # Given we don't find an attribution type
        with self.captureOnCommitCallbacks(execute=True):
            AutoEligibilityResponseLog.objects.create(
                person_id=self.patient.person.pk, response_json="", is_eligible=False, rejections=[TRIZETTO_ERRORS[0]]
            )
            tasks.check_eligibility_of_valid_plan(
                person_id=self.patient.person.id, requested_by_user_id=self.patient.pk
            )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(self.patient.person.insurance_info.coverage_start, initial_coverage_start_date)
        self.assertEqual(self.patient.person.insurance_info.coverage_end, initial_coverage_end_date)
        mock_create_new_insurance_case.assert_called_once()
        mock_handle_invalid_insurance.assert_not_called()
        mock_create_new_insurance_case.reset_mock()
        mock_handle_invalid_insurance.reset_mock()
        with self.captureOnCommitCallbacks(execute=True):
            AutoEligibilityResponseLog.objects.create(
                person_id=self.patient.person.pk, response_json="", is_eligible=False
            )
            tasks.check_eligibility_of_valid_plan(
                person_id=self.patient.person.id, requested_by_user_id=self.patient.pk
            )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(self.patient.person.insurance_info.coverage_start, initial_coverage_start_date)
        self.assertEqual(self.patient.person.insurance_info.coverage_end, initial_coverage_end_date)
        mock_create_new_insurance_case.assert_not_called()
        mock_handle_invalid_insurance.assert_not_called()
        mock_create_new_insurance_case.reset_mock()
        mock_handle_invalid_insurance.reset_mock()
        # Given we found an attribution type
        mock_autocheck_eligibility.return_value = True
        mock_create_new_insurance_case.reset_mock()
        onboarding_state.refresh_from_db()
        self.assertEqual(onboarding_state.status, OnboardingStatus.SIGNEDUP)
        with self.captureOnCommitCallbacks(execute=True):
            self.patient.person.insurance_info.coverage_start = date(year=2025, month=1, day=1)
            self.patient.person.insurance_info.coverage_end = None
            self.patient.person.insurance_info.save(update_fields=["coverage_start", "coverage_end"])
            tasks.check_eligibility_of_valid_plan(
                person_id=self.patient.person.id, requested_by_user_id=self.patient.pk
            )
        mock_create_new_insurance_case.assert_not_called()
        onboarding_state.refresh_from_db()
        self.assertEqual(onboarding_state.status, OnboardingStatus.MEMBER)

    @mock.patch("firefly.modules.auto_eligibility.utils._autocheck_eligibility")
    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex")
    def test_check_eligibility_for_patient(self, mock_mutex, mock_autocheck_eligibility):
        self.patient.person.attribution.attribution_requirement_type = "POC"
        self.patient.person.attribution.save()
        insurance_plan: InsurancePlan = self.patient.person.insurance_info.insurance_plan
        self.assertIsNotNone(insurance_plan)
        mock_mutex.return_value = StubBackend()
        # Scenario
        #   Patient is associated with an insurance plan
        #   check_eligibility_for_patient is invoked
        # Expected behaviour
        #   elibility check and attribution update is invoked for patient
        person = PersonUserFactory.create()
        person.insurance_info.insurance_plan = insurance_plan
        person.insurance_info.save()
        with self.captureOnCommitCallbacks(execute=True):
            tasks.check_eligibility_for_patient(
                user_id=person.user.id,
                requested_by_user_id=person.user.id,
            )
        self.assertEqual(mock_autocheck_eligibility.call_count, 1)
        # Scenario
        #   Patient is associated with an insurance plan
        #   but isnt associated with a payer
        #   check_eligibility_for_patient is invoked
        # Expected behaviour
        #   elibility check and attribution update is not invoked for patient
        mock_autocheck_eligibility.reset_mock()
        person = PersonUserFactory.create()
        person.insurance_info.insurance_plan = insurance_plan
        person.insurance_info.insurance_payer = None
        person.insurance_info.save()
        with self.captureOnCommitCallbacks(execute=True):
            tasks.check_eligibility_for_patient(
                user_id=person.user.id,
                requested_by_user_id=person.user.id,
            )
        self.assertEqual(mock_autocheck_eligibility.call_count, 0)
        # Scenario
        #   Patient is associated with an insurance plan
        #   check_eligibility_for_patient is invoked
        #   Eligibility check functions error out for all patients
        # Expected behaviour
        #   elibility check is invoked for patient
        #   and does not raise error
        #   attribution update is not invoked
        mock_autocheck_eligibility.reset_mock()
        person.insurance_info = InsuranceMemberInfoFactory.create()
        person.save()
        self.assertEqual(mock_autocheck_eligibility.call_count, 0)
        mock_autocheck_eligibility.side_effect = Exception()
        with self.captureOnCommitCallbacks(execute=True):
            tasks.check_eligibility_for_patient(
                user_id=person.user.id,
                requested_by_user_id=person.user.id,
            )
        self.assertEqual(mock_autocheck_eligibility.call_count, 1)

    @mock.patch("firefly.modules.auto_eligibility.utils.get_and_update_eligibility")
    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex")
    def test_check_eligibility_for_eligible_patient(self, mock_mutex, mock_eligibility_check):
        mock_mutex.return_value = StubBackend()
        billing_and_insurance_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["billing_and_insurance"]
        AssigneeGroup.objects.get_or_create(name="Billing and Insurance", unique_key=billing_and_insurance_unique_key)
        person = PersonUserFactory.create()

        state_machine_content = {
            "initial_state": "New",
            "state_with_categories": [
                {"state": {"name": "New"}, "category": "not_started"},
                {"state": {"name": "Done"}, "category": "complete"},
            ],
            "transitions": [
                {"dest": "New", "source": "*", "trigger": "reopened", "system_action": StateMachineSystemAction.REOPEN},
                {"dest": "Done", "source": "*", "trigger": "Done"},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "Done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(title="Test", content=state_machine_content)
        category, _ = CaseCategory.objects.get_or_create(
            title="Insurance - Outreach",
            unique_key=INSURANCE_OUTREACH,
            state_machine_definition=state_machine_definition,
        )
        # Scenario
        #   update_eligibility_and_attribution_for_patient returns that the patient is eligible
        #   check_eligibility_for_patient is invoked
        # Expected behaviour
        #   close insurance outreach case
        mock_eligibility_check.return_value = True
        create_outreach_insurance_case(person)
        tasks.check_eligibility_for_patient(user_id=person.user.id, requested_by_user_id=person.user.id)
        self.assertEqual(mock_eligibility_check.call_count, 1)
        mock_eligibility_check.assert_has_calls([mock.call(person, person.user.id)])
        insurance_outreach_cases = get_open_case(person, INSURANCE_OUTREACH)
        self.assertEqual(insurance_outreach_cases.count(), 0)

    @mock.patch("firefly.modules.auto_eligibility.utils.get_and_update_eligibility")
    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex")
    def test_check_eligibility_for_patient_with_is_visit_prep_case_creation_enabled(
        self, mock_mutex, mock_eligibility_check
    ):
        mock_mutex.return_value = StubBackend()
        state_machine_content = {
            "initial_state": "New",
            "state_with_categories": [
                {"state": {"name": "New"}, "category": "not_started"},
                {"state": {"name": "Done"}, "category": "complete"},
            ],
            "transitions": [
                {"dest": "New", "source": "*", "trigger": "reopened", "system_action": StateMachineSystemAction.REOPEN},
                {"dest": "Done", "source": "*", "trigger": "Done"},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "Done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Test",
            content=state_machine_content,
        )
        case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY,
        )
        case_category.state_machine_definition = state_machine_definition
        case_category.save()
        person = PersonUserFactory.create()
        old_date = (date.today() - timedelta(days=5)).strftime("%Y-%m-%d")
        future_date = (date.today() + timedelta(days=5)).strftime("%Y-%m-%d")
        mock_eligibility_check.return_value = {"eligibility_status": None, "attribution_type": None}
        # scenerio 1: is_visit_prep_case_creation_enabled = False
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
        )
        appointment_insurance_outreach_cases = person.cases.filter(category=case_category)
        self.assertEqual(appointment_insurance_outreach_cases.count(), 0)
        # scenerio 2: is_visit_prep_case_creation_enabled = True and Insurance expired
        # payer not BCBS_MA
        payer = InsurancePayerFactory(name="test_payer", pmpm_eligible=False)
        person.insurance_info = InsuranceMemberInfoFactory(coverage_end=old_date, insurance_payer=payer)
        person.save()
        appointment_date = (date.today() + timedelta(days=1)).strftime("%m/%d/%Y %I:%M %p")
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
            appointment_date=appointment_date,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertIsNotNone(appointment_insurance_outreach_case)
        self.assertEqual(appointment_insurance_outreach_case.description, "test_payer")
        self.assertEqual(appointment_insurance_outreach_case.due_date, convert_date_to_end_of_working_est(date.today()))
        _clear_cases(case_category=case_category, person=person)
        # with payer BCBS_MA
        payer = InsurancePayerFactory(name=PAYER_BCBS_MA, pmpm_eligible=False)
        person.insurance_info = InsuranceMemberInfoFactory(coverage_end=old_date, insurance_payer=payer)
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
            appointment_date=appointment_date,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category, status="New")
        self.assertIsNotNone(appointment_insurance_outreach_case)
        self.assertEqual(appointment_insurance_outreach_case.description, str(PAYER_BCBS_MA))
        self.assertEqual(
            appointment_insurance_outreach_case.due_date,
            convert_date_to_end_of_working_est(datetime.strptime(appointment_date, "%m/%d/%Y %I:%M %p").date()),
        )
        # delete all appointment_insurance_outreach_cases
        _clear_cases(case_category=case_category, person=person)
        # scenerio 3: is_visit_prep_case_creation_enabled = True and Insurance active, plan_type = HMO and
        # not firfly pcp
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_cases = person.cases.filter(category=case_category)
        self.assertEqual(appointment_insurance_outreach_cases.count(), 1)
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        appointment_insurance_outreach_case.action = "Done"
        appointment_insurance_outreach_case.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_cases = person.cases.filter(category=case_category)
        self.assertEqual(appointment_insurance_outreach_cases.count(), 1)
        _clear_cases(case_category=case_category, person=person)
        # scenerio 4: is_visit_prep_case_creation_enabled = True and Insurance pending and plan_type = HMO and
        # firfly pcp
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=None,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=True,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_cases = person.cases.filter(category=case_category)
        self.assertEqual(appointment_insurance_outreach_cases.count(), 0)
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        # scenerio 5: is_visit_prep_case_creation_enabled = True and Insurance active and plan_type = HMO and
        # firfly pcp
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=True,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, "Done")
        self.assertEqual(appointment_insurance_outreach_case.status_category, StatusCategory.COMPLETE)
        # scenerio 6: is_visit_prep_case_creation_enabled = True and Insurance active and plan_type = HMO and
        # firfly pcp=False
        appointment_insurance_outreach_case.action = CaseSystemAction.REOPEN
        appointment_insurance_outreach_case.save()
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, "New")
        # scenerio 7: is_visit_prep_case_creation_enabled = True and Insurance active and plan_type = PPO
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_PPO,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, "Done")
        self.assertEqual(appointment_insurance_outreach_case.status_category, StatusCategory.COMPLETE)
        # scenerio 8: is_visit_prep_case_creation_enabled = False
        appointment_insurance_outreach_case.action = CaseSystemAction.REOPEN
        appointment_insurance_outreach_case.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, "Done")
        self.assertEqual(appointment_insurance_outreach_case.status_category, StatusCategory.COMPLETE)
        # scenario 9: is_visit_prep_case_creation_enabled = True and Insurance active and plan_type = HMO and
        # medicare payer
        appointment_insurance_outreach_case.action = CaseSystemAction.REOPEN
        appointment_insurance_outreach_case.save()
        payer = InsurancePayerFactory(
            name="Medicare", elation_payer_code=ELATION_PAYER_CODES["Medicare"], pmpm_eligible=False
        )
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=future_date,
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
            insurance_payer=payer,
        )
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, "Done")
        self.assertEqual(appointment_insurance_outreach_case.status_category, StatusCategory.COMPLETE)

    @mock.patch("firefly.modules.auto_eligibility.tasks.reminders")
    @mock.patch("firefly.modules.auto_eligibility.utils.get_and_update_eligibility")
    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex")
    def test_check_eligibility_for_patient_with_appointment_campaign(
        self, mock_mutex, mock_eligibility_check, mock_reminders
    ):
        mock_mutex.return_value = StubBackend()
        state_machine_content = {
            "initial_state": "New",
            "state_with_categories": [
                {"state": {"name": "New"}, "category": "not_started"},
                {"state": {"name": "Automated Outreach 1"}, "category": "in_progress"},
                {"state": {"name": "Automated Outreach 2"}, "category": "in_progress"},
                {"state": {"name": "Done"}, "category": "complete"},
            ],
            "transitions": [
                {"dest": "Done", "source": "*", "trigger": "Done"},
                {
                    "trigger": "auto_close",
                    "source": "*",
                    "dest": "Done",
                    "system_action": StateMachineSystemAction.CLOSE,
                },
                {"dest": "Automated Outreach 1", "source": "*", "trigger": "Automated Outreach 1"},
                {"dest": "Automated Outreach 2", "source": "Automated Outreach 1", "trigger": "Automated Outreach 2"},
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Test",
            content=state_machine_content,
        )
        case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=INSURANCE_VISIT_PREP_CATEGORY_UNIQUE_KEY,
        )
        case_category.state_machine_definition = state_machine_definition
        case_category.save()
        person = PersonUserFactory.create()
        mock_eligibility_check.return_value = {"eligibility_status": None, "attribution_type": None}
        appointment_date = (date.today() + timedelta(days=5)).strftime("%m/%d/%Y %I:%M %p")
        old_date = date.today() - timedelta(days=5)
        # scenerio 1 insurance expired and campaign before 48 hours
        payer = InsurancePayerFactory(name=PAYER_BCBS_MA, pmpm_eligible=False)
        person.insurance_info = InsuranceMemberInfoFactory(coverage_end=old_date, insurance_payer=payer)
        person.save()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            appointment_date=appointment_date,
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            is_visit_prep_case_creation_enabled=True,
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, CaseStatus.AUTOMATED_OUTREACH_1)
        self.assertEqual(mock_reminders.send.call_count, 2)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_EMAIL"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                        }
                    ],
                )
            ]
        )
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_PUSH_48_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                        }
                    ],
                )
            ]
        )
        # scenerio 2 insurance expired and campaign before 24 hours
        mock_reminders.reset_mock()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            appointment_date=appointment_date,
            appointment_campaign=str(AppointmentCampaign.BEFORE_24_HOURS_CAMPAIGN),
            is_visit_prep_case_creation_enabled=True,
        )
        self.assertEqual(mock_reminders.send.call_count, 1)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_PUSH_24_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"appointment_date_and_time": appointment_date},
                        }
                    ],
                )
            ]
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, CaseStatus.AUTOMATED_OUTREACH_2)
        appointment_insurance_outreach_case.delete()
        # scenerio 3 HMO user without firefly pcp and campaign before 48 hours
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=(date.today() + timedelta(days=5)),
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
            insurance_payer=payer,
        )
        person.save()
        md_group, _ = Group.objects.get_or_create(name=MD_ROLE)
        md_provider = ProviderDetailFactory(first_name="Test", last_name="MD")
        md_group.user_set.add(md_provider.user)
        person.care_team.add(md_provider)
        provider_name = f"{md_provider.first_name} {md_provider.last_name}"
        AliasMapping.set_mapping_by_object(
            alias_name=AliasName.BCBS,
            obj=md_provider,
            alias_id="bcbs_id_for_provider",
        )
        mock_reminders.reset_mock()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            appointment_date=appointment_date,
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            is_visit_prep_case_creation_enabled=True,
        )
        self.assertEqual(mock_reminders.send.call_count, 2)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_EMAIL"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {
                                "appointment_time": appointment_date,
                                "pcp_id": "bcbs_id_for_provider",
                                "md_name": provider_name,
                            },
                        }
                    ],
                )
            ]
        )
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_PUSH_48_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"md_name": provider_name},
                        }
                    ],
                )
            ]
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, CaseStatus.AUTOMATED_OUTREACH_1)
        # scenerio 4 HMO user without firefly pcp and campaign before 24 hours
        mock_reminders.reset_mock()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            appointment_date=appointment_date,
            appointment_campaign=str(AppointmentCampaign.BEFORE_24_HOURS_CAMPAIGN),
            is_visit_prep_case_creation_enabled=True,
        )
        self.assertEqual(mock_reminders.send.call_count, 1)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_PUSH_24_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"md_name": provider_name},
                        }
                    ],
                )
            ]
        )
        appointment_insurance_outreach_case = person.cases.get(category=case_category)
        self.assertEqual(appointment_insurance_outreach_case.status, CaseStatus.AUTOMATED_OUTREACH_2)
        # scenerio 5
        payer = InsurancePayerFactory(name="test payer", pmpm_eligible=False)
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=(date.today() + timedelta(days=5)),
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
            insurance_payer=payer,
        )
        person.save()
        md_group, _ = Group.objects.get_or_create(name=MD_ROLE)
        md_provider = ProviderDetailFactory(first_name="Test", last_name="MD")
        md_group.user_set.add(md_provider.user)
        person.care_team.add(md_provider)
        provider_name = f"{md_provider.first_name} {md_provider.last_name}"
        AliasMapping.set_mapping_by_object(
            alias_name=AliasName.BCBS,
            obj=md_provider,
            alias_id="bcbs_id_for_provider",
        )
        mock_reminders.reset_mock()
        tasks.check_eligibility_for_patient(
            user_id=person.user.id,
            requested_by_user_id=person.user.id,
            appointment_date=appointment_date,
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            is_visit_prep_case_creation_enabled=True,
        )
        mock_reminders.send.assert_not_called()

    @mock.patch("firefly.modules.auto_eligibility.tasks.update_eligibility_for_patient")
    @mock.patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex")
    def test_check_pcp_update_for_eligible_patient(self, mock_mutex, mock_eligibility_check):
        mock_mutex.return_value = StubBackend()
        task_title = "Update PCP Task"
        task_collection = TaskCollectionFactory()
        task_collection_task = TaskCollectionTaskFactory(
            task_collection=task_collection,
            title=task_title,
            uid=TaskCollectionTaskUniqueIdentifiers.UPDATE_PCP,
        )
        pcp_task = Task.objects.create(
            title=task_title, patient=self.patient, autocreated_from=task_collection_task, is_complete=True
        )

        # Scenario 1
        #   update pcp task is closed
        #   check_pcp_updated_for_patient is invoked
        #   is_firefly_pcp is True
        # Expected behaviour
        #   update pcp task is still closed

        insurance_info = self.patient.person.insurance_info
        insurance_info.insurance_payer = InsurancePayerFactory(name=PAYER_BCBS_MA)
        insurance_info.plan_type = insurance_info.PLAN_TYPE_HMO
        insurance_info.is_firefly_pcp = True
        insurance_info.save()
        tasks.check_pcp_updated_for_patient(
            user_id=self.patient.id,
            requested_by_user_id=get_lucian_bot_user().pk,
        )
        mock_eligibility_check.assert_called_once_with(
            user_id=self.patient.id,
            requested_by_user_id=get_lucian_bot_user().pk,
        )
        mock_eligibility_check.reset_mock()
        pcp_task.refresh_from_db()
        self.assertTrue(pcp_task.is_complete)

        # Scenario 2
        #   update pcp task is closed
        #   check_pcp_updated_for_patient is invoked
        #   is_firefly_pcp is False
        # Expected behaviour
        #   update pcp task needs to be reopened

        insurance_info.is_firefly_pcp = False
        insurance_info.save()
        tasks.check_pcp_updated_for_patient(
            user_id=self.patient.id,
            requested_by_user_id=get_lucian_bot_user().pk,
        )
        mock_eligibility_check.assert_called_once_with(
            user_id=self.patient.id,
            requested_by_user_id=get_lucian_bot_user().pk,
        )
        pcp_task.refresh_from_db()
        self.assertFalse(pcp_task.is_complete)

    @mock.patch("firefly.modules.auto_eligibility.tasks.reminders")
    def test_send_eligibility_campaign(self, mock_reminders):
        person = PersonUserFactory.create()
        appointment_date = (date.today() + timedelta(days=5)).strftime("%m/%d/%Y %I:%M %p")
        old_date = date.today() - timedelta(days=5)
        # scenerio 1 insurance expired and campaign before 48 hours
        payer = InsurancePayerFactory(name=PAYER_BCBS_MA, pmpm_eligible=False)
        person.insurance_info = InsuranceMemberInfoFactory(coverage_end=old_date, insurance_payer=payer)
        person.save()
        tasks.send_eligibility_campaign(
            user=person.user,
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            appointment_date=appointment_date,
        )
        self.assertEqual(mock_reminders.send.call_count, 2)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_EMAIL"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                        }
                    ],
                )
            ]
        )
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_PUSH_48_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                        }
                    ],
                )
            ]
        )
        # scenerio 2 insurance expired and campaign before 24 hours
        mock_reminders.reset_mock()
        tasks.send_eligibility_campaign(
            user=person.user,
            appointment_campaign=str(AppointmentCampaign.BEFORE_24_HOURS_CAMPAIGN),
            appointment_date=appointment_date,
        )
        self.assertEqual(mock_reminders.send.call_count, 1)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["EXPIRED_INSURANCE_PUSH_24_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"appointment_date_and_time": appointment_date},
                        }
                    ],
                )
            ]
        )
        # scenerio 3 HMO user without firefly pcp and campaign before 48 hours
        person.insurance_info = InsuranceMemberInfoFactory(
            coverage_end=(date.today() + timedelta(days=5)),
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO,
            is_firefly_pcp=False,
            insurance_payer=payer,
        )
        person.save()
        md_group, _ = Group.objects.get_or_create(name=MD_ROLE)
        md_provider = ProviderDetailFactory(first_name="Test", last_name="MD")
        md_group.user_set.add(md_provider.user)
        person.care_team.add(md_provider)
        provider_name = f"{md_provider.first_name} {md_provider.last_name}"
        AliasMapping.set_mapping_by_object(
            alias_name=AliasName.BCBS,
            obj=md_provider,
            alias_id="bcbs_id_for_provider",
        )
        mock_reminders.reset_mock()
        tasks.send_eligibility_campaign(
            user=person.user,
            appointment_campaign=str(AppointmentCampaign.BEFORE_48_HOURS_CAMPAIGN),
            appointment_date=appointment_date,
        )
        self.assertEqual(mock_reminders.send.call_count, 2)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_EMAIL"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {
                                "appointment_time": appointment_date,
                                "pcp_id": "bcbs_id_for_provider",
                                "md_name": provider_name,
                            },
                        }
                    ],
                )
            ]
        )
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_PUSH_48_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"md_name": provider_name},
                        }
                    ],
                )
            ]
        )
        # scenerio 4 HMO user without firefly pcp and campaign before 24 hours
        mock_reminders.reset_mock()
        tasks.send_eligibility_campaign(
            user=person.user,
            appointment_campaign=str(AppointmentCampaign.BEFORE_24_HOURS_CAMPAIGN),
            appointment_date=appointment_date,
        )
        self.assertEqual(mock_reminders.send.call_count, 1)
        mock_reminders.send.assert_has_calls(
            [
                mock.call(
                    settings.BRAZE["HMO_USER_PUSH_24_HOURS"],
                    [
                        {
                            "external_user_id": person.pk,
                            "send_to_existing_only": True,
                            "trigger_properties": {"md_name": provider_name},
                        }
                    ],
                )
            ]
        )


class UtilsTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.ma_state, _ = State.objects.update_or_create(
            abbreviation="MA", defaults={"name": "MA", "can_service": True}
        )
        State.objects.update_or_create(abbreviation="NY", defaults={"name": "NY", "can_service": True})
        self.nh_state, _ = State.objects.update_or_create(
            abbreviation="NH", defaults={"name": "NH", "can_service": True}
        )
        State.objects.update_or_create(abbreviation="ME", defaults={"name": "ME", "can_service": True})
        State.objects.update_or_create(abbreviation="CA", defaults={"name": "CA", "can_service": False})

    @mock.patch("firefly.modules.insurance.utils.create_insurance_plan_review_case")
    @mock.patch("firefly.modules.insurance.utils.handle_invalid_insurance")
    @mock.patch("firefly.modules.insurance.utils.update_contract_and_attribution")
    @patch("firefly.modules.auto_eligibility.utils._log_eligibility_check_request")
    @mock.patch("firefly.modules.auto_eligibility.utils.TrizettoClient")
    def test_get_and_update_eligibility_handle_invalid_insurance(
        self, mock_client, mock_log_request, mock_attribution, mock_invalid_insurance, mock_review_case
    ):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        active = os.path.join(fixture_dir, "primary_with_active_coverage.xml")
        inactive = os.path.join(fixture_dir, "primary_without_active_coverage.xml")
        active_response_without_rejection = open(active, "r").read()
        inactive_response_without_rejection = open(inactive, "r").read()
        response_with_rejection = """
        <eligibilityresponse>
          <infosource>
            <transactionid>6fd06e59dc4eb0ac1fc8af675b508c</transactionid>
            <payername>ALLWAYS HEALTH PLAN</payername>
            <payerid>10377</payerid>
          </infosource>
          <inforeceiver>
            <providername>
              <last>GREENBERG</last>
            </providername>
            <npi>**********</npi>
            <providersecondaryid>00000006634</providersecondaryid>
          </inforeceiver>
          <subscriber>
            <trace_number>38916556757</trace_number>
            <trace_id>9MEDDATACO</trace_id>
            <trace_number>**********</trace_number>
            <trace_id>99Trizetto</trace_id>
            <patientname>
              <first>TEST</first>
              <last>PATIENT</last>
            </patientname>
            <patientid>77510170201</patientid>
            <sex>F</sex>
            <date-of-birth>19711113</date-of-birth>
            <rejection>
              <rejectreason>Unable to Respond at Current Time</rejectreason>
              <followupaction>Try again later</followupaction>
            </rejection>
          </subscriber>
        </eligibilityresponse>
        """
        person = PersonUserFactory()
        person.attribution.attribution_requirement_type = "POC"
        person.attribution.save()
        insurance_info = person.insurance_info
        # With valid response and active coverage, handle_invalid_insurance should not be called
        start_date = date(year=2018, month=7, day=1)
        end_date = None
        mock_client.return_value.get_response.return_value = {"ResponseAsXml": active_response_without_rejection}
        with self.captureOnCommitCallbacks(execute=True):
            get_and_update_eligibility(person, person.user.pk)
        mock_invalid_insurance.assert_not_called()
        mock_attribution.assert_called_once()
        insurance_info.refresh_from_db()
        self.assertEqual(insurance_info.coverage_start, start_date)
        self.assertEqual(insurance_info.coverage_end, end_date)
        # With valid response and inactive coverage, handle_invalid_insurance should be called
        mock_invalid_insurance.reset_mock()
        mock_attribution.reset_mock()
        start_date = date(year=2020, month=3, day=6)
        end_date = date(year=2020, month=4, day=30)
        mock_client.return_value.get_response.return_value = {"ResponseAsXml": inactive_response_without_rejection}
        with self.captureOnCommitCallbacks(execute=True):
            get_and_update_eligibility(person, person.user.pk)
        mock_invalid_insurance.assert_called_once_with(person, None, None)
        mock_attribution.assert_called_once()
        insurance_info.refresh_from_db()
        self.assertEqual(insurance_info.coverage_start, None)
        self.assertEqual(insurance_info.coverage_end, None)
        # With invalid response, handle_invalid_insurance should not be called
        mock_invalid_insurance.reset_mock()
        mock_attribution.reset_mock()
        insurance_info.refresh_from_db()
        mock_client.return_value.get_response.return_value = {"ResponseAsXml": response_with_rejection}
        with self.captureOnCommitCallbacks(execute=True):
            get_and_update_eligibility(person, person.user.pk)
        mock_invalid_insurance.assert_not_called()
        mock_attribution.assert_not_called()

    def test_handle_eligibility_response(self):
        state_machine_content = {
            "state_with_categories": [
                {
                    "state": {
                        "name": "Draft",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "not_started",
                    "due_date": [{"days": 7, "use_business_days": True}],
                },
                {
                    "state": {
                        "name": "Not Accepted",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "deferred",
                },
                {
                    "state": {
                        "name": "Accepted",
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
            ],
            "initial_state": "Draft",
            "transitions": [
                {
                    "dest": "Draft",
                    "source": ["Not Accepted", "Accepted"],
                    "trigger": "Draft",
                },
                {"dest": "Not Accepted", "source": ["Draft", "Accepted"], "trigger": "Not Accepted"},
                {"dest": "Accepted", "source": ["Draft", "Not Accepted"], "trigger": "Accepted"},
                {
                    "dest": "Accepted",
                    "source": ["Draft", "Not Accepted"],
                    "trigger": "auto_close",
                    "system_action": "system_close",
                },
            ],
        }
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Insurance Plan Needs Review",
            content=state_machine_content,
        )
        ticket_id = "123"
        eligibility_responses = AutoEligibilityResponseLog.objects.filter(ticket=ticket_id)
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()
        billing_and_insurance_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["billing_and_insurance"]
        billing_and_insurance_assignee_group, _ = AssigneeGroup.objects.get_or_create(
            unique_key=billing_and_insurance_unique_key
        )
        insurance_plan_review_case_category, _ = CaseCategory.objects.get_or_create(
            state_machine_definition=state_machine_definition,
            unique_key=INSURANCE_PLAN_NEEDS_REVIEW,
            assignment_scheme=AssignmentScheme.ASSIGN_TO_DEFAULT,
            default_assignee_owner_group=billing_and_insurance_assignee_group,
        )
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()
        self.assertIsNone(self.patient.person.insurance_info.pcp_npi)
        self.assertIsNone(self.patient.person.insurance_info.is_firefly_pcp)
        self.assertIsNone(self.patient.person.insurance_info.firefly_pcp_at)
        self.assertIsNone(self.patient.person.insurance_info.pcp_name)
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_payer)
        self.assertIsNone(self.patient.person.insurance_info.coverage_start)
        self.assertIsNone(self.patient.person.insurance_info.coverage_end)
        self.assertEqual(eligibility_responses.count(), 0)
        with self.captureOnCommitCallbacks(execute=True):
            result = _handle_eligibility_response(
                ticket=ticket_id,
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
            self.assertEqual(result, True)
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(self.patient.person.insurance_info.pcp_npi, "**********")
        self.assertEqual(self.patient.person.insurance_info.pcp_name, "MARIA PRO-RISQUEZ")
        self.assertFalse(self.patient.person.insurance_info.is_firefly_pcp)
        self.assertIsNone(self.patient.person.insurance_info.firefly_pcp_at)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_plan)
        self.assertEqual(self.patient.person.insurance_info.plan_description, "EPO")
        self.assertEqual(self.patient.person.insurance_info.insurance_plan.name, "EPO")
        self.assertEqual(self.patient.person.insurance_info.insurance_plan.review_state, "UNREVIEWED")
        self.assertEqual(self.patient.person.insurance_info.coverage_start, date(2018, 7, 1))
        self.assertIsNone(self.patient.person.insurance_info.coverage_end)
        self.assertEqual(eligibility_responses.count(), 1)
        eligibility_response = eligibility_responses.first()
        self.assertEqual(eligibility_response.payer_code, "04293")
        self.assertFalse(eligibility_response.firefly_is_pcp)
        self.assertEqual(eligibility_response.insurance_type, "Point of Service (POS)")
        self.assertEqual(eligibility_response.plan_coverage_description, "EPO")
        self.assertEqual(eligibility_response.pcp_npi, "**********")
        self.assertEqual(eligibility_response.coverage_start, date(2018, 7, 1))
        self.assertIsNone(eligibility_response.coverage_end)
        self.assertFalse(eligibility_response.has_error_in_parsing)
        # Verify if a review case is created, when insurance plan is created
        insurance_plan_review_open_cases = Case.objects.filter(
            person=self.patient.person, category=insurance_plan_review_case_category, status="Draft"
        )
        self.assertEqual(insurance_plan_review_open_cases.count(), 1)
        insurance_plan_review_open_case = insurance_plan_review_open_cases.first()
        self.assertIsNotNone(insurance_plan_review_open_case.due_date)
        self.assertEqual(insurance_plan_review_open_case.owner_group, billing_and_insurance_assignee_group)
        insurance_plan_review_open_case.status = "Accepted"
        insurance_plan_review_open_case.save()
        self.patient.person.insurance_info.insurance_plan.review_state = "REVIEWED"
        self.patient.person.insurance_info.insurance_plan.save()
        self.assertEqual(self.patient.person.insurance_info.insurance_plan.review_state, "REVIEWED")
        # Verify that the is_firefly_pcp gets set when the pcp_npi points to a firefly physician
        np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        np_physician = PhysicianFactory.create(npi="**********")
        ProviderDetailFactory(
            internal_role=np_role,
            physician=np_physician,
        )
        get_npi_list_of_attributable_providers.cache_clear()
        with self.captureOnCommitCallbacks(execute=True):
            result = _handle_eligibility_response(
                ticket=ticket_id,
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
            self.assertEqual(result, True)
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(self.patient.person.insurance_info.pcp_npi, "**********")
        self.assertEqual(self.patient.person.insurance_info.pcp_name, "MARIA PRO-RISQUEZ")
        self.assertTrue(self.patient.person.insurance_info.is_firefly_pcp)
        self.assertIsNotNone(self.patient.person.insurance_info.firefly_pcp_at)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_plan)
        self.assertEqual(self.patient.person.insurance_info.plan_description, "EPO")
        self.assertEqual(self.patient.person.insurance_info.insurance_plan.name, "EPO")
        self.assertEqual(eligibility_responses.count(), 2)
        # Since the plan is accepted, new case should not be created
        self.assertEqual(insurance_plan_review_open_cases.count(), 0)

    @mock.patch("firefly.core.services.auto_eligibility_service.models.parse_payer_response", return_value=None)
    def test_handle_eligibility_response_functions(self, mock_parse_payer_response):
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()
        with self.assertRaises(Exception):
            _handle_eligibility_response(
                ticket=uuid.uuid4(),
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
        mock_parse_payer_response.assert_called_once()

    def test_handle_eligibility_response_with_case_insensitive_plan(self):
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW, title=INSURANCE_PLAN_NEEDS_REVIEW)
        insurance_plan = InsurancePlan.objects.create(
            name="EPO",
            insurance_payer=self.patient.person.insurance_info.insurance_payer,
        )
        insurance_plan_count = InsurancePlan.objects.count()

        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../core/user/api/coverage/fixtures"))
        fixture_filename = "primary_with_active_coverage_case_insensitive_plan.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()

        self.assertIsNone(self.patient.person.insurance_info.pcp_name)
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_payer)
        with self.captureOnCommitCallbacks(execute=True):
            AutoEligibilityResponseLog.objects.create(
                person_id=self.patient.person.pk, response_json="", is_eligible=False, plan_coverage_description="epo"
            )
            result = _handle_eligibility_response(
                ticket="123",
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(result, True)
        self.assertEqual(self.patient.person.insurance_info.pcp_name, "MARIA PRO-RISQUEZ")
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_plan)
        self.assertEqual(self.patient.person.insurance_info.plan_description, "epo")
        self.assertEqual(self.patient.person.insurance_info.insurance_plan.name, insurance_plan.name)
        # No new insurance plans were created
        self.assertEqual(InsurancePlan.objects.count(), insurance_plan_count)

    def test_handle_eligibility_response_without_payer(self):
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.plan_description = None
        self.patient.person.insurance_info.insurance_payer = None
        self.patient.person.insurance_info.save()

        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_active_coverage.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()
        self.assertIsNone(self.patient.person.insurance_info.pcp_name)
        self.assertIsNone(self.patient.person.insurance_info.plan_description)
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertIsNone(self.patient.person.insurance_info.insurance_payer)
        with self.captureOnCommitCallbacks(execute=True):
            AutoEligibilityResponseLog.objects.create(
                person_id=self.patient.person.pk, response_json="", is_eligible=False, plan_coverage_description="EPO"
            )
            result = _handle_eligibility_response(
                ticket="123",
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(result, True)
        self.assertEqual(self.patient.person.insurance_info.pcp_name, "MARIA PRO-RISQUEZ")
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertEqual(self.patient.person.insurance_info.plan_description, "EPO")
        self.assertIsNone(self.patient.person.insurance_info.insurance_payer)
        # Verify that if the plan changes - with no PCP selected - the pcp name field gets removed
        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "./fixtures"))
        fixture_filename = "primary_with_inactive_coverage_harvard.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()
        with self.captureOnCommitCallbacks(execute=True):
            result = _handle_eligibility_response(
                ticket="123",
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
        self.assertEqual(result, False)
        self.patient.person.insurance_info.refresh_from_db()
        self.assertIsNone(self.patient.person.insurance_info.pcp_name)

    def test_handle_eligibility_response_without_plan_description(self):
        self.patient.person.insurance_info.insurance_plan = None
        self.patient.person.insurance_info.save()

        fixture_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../core/user/api/coverage/fixtures"))
        fixture_filename = "primary_with_active_coverage_wo_plan.xml"
        fixture_file = os.path.join(fixture_dir, fixture_filename)
        xml = open(fixture_file, "r").read()
        self.assertIsNone(self.patient.person.insurance_info.pcp_name)
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_payer)
        with self.captureOnCommitCallbacks(execute=True):
            AutoEligibilityResponseLog.objects.create(
                person_id=self.patient.person.pk, response_json="", is_eligible=False, plan_coverage_description=None
            )
            result = _handle_eligibility_response(
                ticket="123",
                response_dict={"ResponseAsXml": xml},
                payer_params={"payer_id": "04293"},
                patient_params=_construct_patient_param(self.patient.person),
                person=self.patient.person,
            )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(result, True)
        self.assertEqual(self.patient.person.insurance_info.pcp_name, "MARIA PRO-RISQUEZ")
        self.assertIsNone(self.patient.person.insurance_info.plan_description)
        self.assertIsNone(self.patient.person.insurance_info.insurance_plan)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_payer)

    def test_construct_payer_param_from_insurance_payer(self):
        # With a single payer code
        payer = InsurancePayerFactory(elation_payer_code="789", payer_codes=["789"])
        payer.save()
        self.assertEqual(_construct_payer_param_from_insurance_payer(payer), {"payer_id": "789"})
        # With multiple payer codes
        payer.payer_codes = ["456", "789"]
        payer.save()
        self.assertEqual(
            _construct_payer_param_from_insurance_payer(payer),
            {"payer_id": ["456", "789"]},
        )
        payer.save()
        # With no payer codes
        payer.payer_codes = []
        payer.save()
        with pytest.raises(Exception) as excinfo:
            _construct_payer_param_from_insurance_payer(payer)
        self.assertEqual(str(excinfo.value), f"no payer codes defined in db for payer {payer.pk}")

    @patch("firefly.modules.auto_eligibility.utils._submit_eligibility_request")
    @patch("firefly.modules.auto_eligibility.utils._handle_eligibility_response")
    def test_autocheck_eligibility(self, mock_handle_eligibility_response, mock_submit_eligibility_request):
        mock_submit_eligibility_request.return_value = None
        person = PersonUserFactory()
        person.insurance_info.state = "MA"
        person.insurance_info.save()
        requested_by_user_id = person.user.id
        eligibility_status = _autocheck_eligibility(person, requested_by_user_id)
        self.assertEqual(eligibility_status, False)
        mock_submit_eligibility_request.return_value = "<>"
        mock_handle_eligibility_response.return_value = False
        eligibility_status = _autocheck_eligibility(person, requested_by_user_id)
        self.assertEqual(eligibility_status, False)
        mock_handle_eligibility_response.return_value = True
        eligibility_status = _autocheck_eligibility(person, requested_by_user_id)
        self.assertEqual(eligibility_status, True)
        person.insurance_info.insurance_plan.firefly_accepted_states.set([self.nh_state.pk])
        person.insurance_info.insurance_plan.firefly_accepted_states.set([self.ma_state.pk])
        eligibility_status = _autocheck_eligibility(person, requested_by_user_id)
        self.assertEqual(eligibility_status, True)
        person.insurance_info.state = "ME"
        person.insurance_info.save()
        eligibility_status = _autocheck_eligibility(person, requested_by_user_id)
        self.assertEqual(eligibility_status, False)

    @patch("firefly.modules.auto_eligibility.utils._submit_eligibility_request", return_value={"eligible": True})
    @patch("firefly.core.services.auto_eligibility_service.models.parse_payer_response")
    def test_autocheck_eligibility_within_two_weeks_of_last_check(
        self, mock_parse_payer_response, mock_submit_eligibility_request
    ):
        payer_response = {
            "eligible": False,
            "firefly_is_pcp": False,
            "pcp_name": None,
            "pcp_npi": None,
            "eligible_services": [],
            "eligible_payers": {"12345": []},
            "rejections": {"12345": ["Unable to Respond at Current Time"]},
            "insurance_type": None,
            "plan_coverage_description": None,
            "coverage_start": "2022-01-01",
            "coverage_end": "2023-12-31",
        }
        mock_parse_payer_response.return_value = payer_response
        # Simulate that insurance info was last updated/created for test users 1 month ago
        last_month = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=30)
        requested_by_user_id = get_lucian_bot_user().pk
        # Create test user with insurance last updated 1 month ago and no AutoEligibilityResponseLog objects. This
        # user should trigger a Trizetto check when calling `autocheck_eligibility`.
        person_1 = PersonUserFactory()
        person_1.insurance_info.updated_at = last_month
        eligibility_status_1 = _autocheck_eligibility(person_1, requested_by_user_id)
        self.assertEqual(eligibility_status_1, False)
        mock_submit_eligibility_request.assert_called_once()
        mock_submit_eligibility_request.reset_mock()
        # Create test user with insurance last updated 1 month ago and an AutoEligibilityResponseLog object. This
        # user should not trigger a Trizetto check when calling `autocheck_eligibility`.
        person_2 = PersonUserFactory()
        person_2.insurance_info.updated_at = last_month
        payer_response["eligible"] = True
        AutoEligibilityResponseLog.objects.create(
            person_id=person_2.id,
            coverage_start=date.today() - timedelta(days=7),
            coverage_end=date.today() + timedelta(days=365),
            payer_code="12345",
            response_json=json.dumps(payer_response),
            is_eligible=True,
        )
        eligibility_status_2 = _autocheck_eligibility(person_2, requested_by_user_id)
        self.assertEqual(eligibility_status_2, True)
        mock_submit_eligibility_request.assert_not_called()
        mock_submit_eligibility_request.reset_mock()
        # Update insurance info and rerun `autocheck_eligibility`. This time, this should trigger a Trizetto check.
        person_2.insurance_info.updated_at = datetime.now(tz=UTC_TIMEZONE)
        payer_response["eligible"] = False
        eligibility_status_2 = _autocheck_eligibility(person_2, requested_by_user_id)
        self.assertEqual(eligibility_status_2, False)
        mock_submit_eligibility_request.assert_called_once()

    @patch("firefly.modules.auto_eligibility.utils._submit_eligibility_request")
    @patch("firefly.modules.auto_eligibility.utils._handle_eligibility_response")
    @patch("firefly.core.services.auto_eligibility_service.models.parse_payer_response")
    def test_autocheck_eligibility_for_churned_user(
        self, mock_parse_payer_response, mock_handle_eligibility_response, mock_submit_eligibility_request
    ):
        # Scenario:
        # Person added insurance info, got churned, we skip the eligibility check and return False
        # Person updates their insurance info, we run the eligibility check
        person_1 = PersonUserFactory()
        payer_response = {
            "eligible": True,
            "firefly_is_pcp": False,
            "pcp_name": None,
            "pcp_npi": None,
            "eligible_services": [],
            "eligible_payers": {"12345": []},
            "rejections": {"12345": []},
            "insurance_type": None,
            "plan_coverage_description": None,
            "coverage_start": "2022-01-01",
            "coverage_end": "2023-12-31",
        }
        mock_parse_payer_response.return_value = payer_response
        with self.captureOnCommitCallbacks(execute=True):
            response_log = AutoEligibilityResponseLog.objects.create(
                person_id=person_1.id,
                payer_code="12345",
                response_json={},
                is_eligible=True,
                firefly_is_pcp=False,
                pcp_name=None,
                pcp_npi=None,
                insurance_type=None,
                plan_coverage_description=None,
                coverage_start="2022-01-01",
                coverage_end="2023-12-31",
            )
        mock_handle_eligibility_response.return_value = response_log

        add_person_to_program(person=person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_enrollment = ProgramEnrollment.objects.filter(
            program__uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            period__endswith__isnull=True,
        )
        self.assertEqual(program_enrollment.first().status, PrimaryCareProgramStatus.NOT_ESTABLISHED)

        requested_by_user_id = person_1.user.id
        eligibility_status = _autocheck_eligibility(person_1, requested_by_user_id)
        self.assertTrue(eligibility_status)
        mock_submit_eligibility_request.assert_called_once()
        mock_submit_eligibility_request.reset_mock()

        last_month = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=30)
        person_1.insurance_info.updated_at = last_month
        remove_person_from_program(person_1, ProgramCodes.PRIMARY_CARE)
        self.assertEqual(program_enrollment.first().status, PrimaryCareProgramStatus.CHURNED)

        eligibility_status = _autocheck_eligibility(person_1, requested_by_user_id)
        self.assertFalse(eligibility_status)
        mock_submit_eligibility_request.assert_not_called()
        mock_submit_eligibility_request.reset_mock()

        person_1.insurance_info.updated_at = datetime.now(tz=UTC_TIMEZONE)
        eligibility_status = _autocheck_eligibility(person_1, requested_by_user_id)
        self.assertTrue(eligibility_status)
        mock_submit_eligibility_request.assert_called_once()
        mock_submit_eligibility_request.reset_mock()

    @patch("firefly.modules.auto_eligibility.utils._submit_eligibility_request")
    @patch("firefly.core.services.auto_eligibility_service.models.parse_payer_response")
    def test_autocheck_eligibility_when_parsed_response_is_none(
        self, mock_parse_payer_response, mock_submit_eligibility_request
    ):
        mock_parse_payer_response.return_value = None
        person_1 = PersonUserFactory()

        add_person_to_program(person=person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        program_enrollment = ProgramEnrollment.objects.filter(
            program__uid=ProgramCodes.PRIMARY_CARE,
            person=person_1,
            period__endswith__isnull=True,
        )
        self.assertEqual(program_enrollment.first().status, PrimaryCareProgramStatus.NOT_ESTABLISHED)

        requested_by_user_id = person_1.user.id
        with self.assertRaises(Exception):
            _autocheck_eligibility(person_1, requested_by_user_id)

    def test_autocheck_eligibility_for_health_plan_user(self):
        task_collection, _ = TaskCollection.objects.get_or_create(
            title=TaskCollectionTaskUniqueIdentifiers.INSURANCE,
        )
        TaskCollectionTask.objects.update_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE,
            defaults={"task_collection": task_collection},
        )
        firefly_payer, _ = InsurancePayer.objects.get_or_create(
            name=FIREFLY_PAYER,
            defaults={"firefly_accepted": True, "pmpm_eligible": True},
        )
        firefly_plan, _ = InsurancePlan.objects.get_or_create(
            name=FIREFLY_PLAN,
            defaults={
                "firefly_accepted": True,
                "insurance_payer": firefly_payer,
                "review_state": InsurancePlanReviewTransitionMetadata.States.REVIEWED,
            },
        )
        rows = pd.DataFrame(
            [
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF101",
                    "LocationID": "FF101-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "123124689",
                    "EmployeeFirstName": "Mark",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Martin",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1980-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": None,
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-00",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "0",
                    "MemberSSN": "123124689",
                    "MemberFirstName": "Mark",
                    "MemberMiddleInitial": "",
                    "MemberLastName": "Martin",
                    "MemberGender": "Male",
                    "MemberDateOfBirth": "1980-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Self",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2022-10-01 04:00:39.865 +0000 UTC",
                },
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF101",
                    "LocationID": "FF101-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "123124689",
                    "EmployeeFirstName": "Mark",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Martin",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1980-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": "",
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-01",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "1",
                    "MemberSSN": "",
                    "MemberFirstName": "Misty",
                    "MemberMiddleInitial": None,
                    "MemberLastName": "Martin",
                    "MemberGender": "Female",
                    "MemberDateOfBirth": "2000-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Child",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2023-03-14 04:00:44.351 +0000 UTC",
                },
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF100",
                    "LocationID": "FF100-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "012345678",
                    "EmployeeFirstName": "Indiana",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Jones",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1990-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": None,
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-00",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "0",
                    "MemberSSN": "012345678",
                    "MemberFirstName": "Indiana",
                    "MemberMiddleInitial": "",
                    "MemberLastName": "Jones",
                    "MemberGender": "Male",
                    "MemberDateOfBirth": "1990-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Self",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2022-11-11 04:00:39.865 +0000 UTC",
                },
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF100",
                    "LocationID": "FF100-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "987654321",
                    "EmployeeFirstName": "Luke",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Skywalker",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1984-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": None,
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-00",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "0",
                    "MemberSSN": "987654321",
                    "MemberFirstName": "Luke",
                    "MemberMiddleInitial": "",
                    "MemberLastName": "Skywalker",
                    "MemberGender": "Male",
                    "MemberDateOfBirth": "1984-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Self",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2022-11-11 04:00:39.865 +0000 UTC",
                },
            ]
        )

        self.employer, _ = Employer.objects.get_or_create(name="IDEXX")
        AliasMapping.set_mapping_by_object(self.employer, AliasName.FLUME, "FF101")
        for i, row in rows.iterrows():
            parse_eligibility_v2(row)
        eligibility_dependant_person = Person.objects.get(first_name="Misty", last_name="Martin")
        eligibility_person_1 = Person.objects.get(first_name="Mark", last_name="Martin")
        eligibility_person_2 = Person.objects.get(first_name="Indiana", last_name="Jones")
        eligibility_person_3 = Person.objects.get(first_name="Luke", last_name="Skywalker")
        person_2 = PersonUserFactory(first_name="Indy", last_name="Jones", dob=date(1990, 1, 1))
        person_3 = PersonUserFactory(first_name="Luke", last_name="Skywalker", dob=date(1984, 1, 1))
        person_4 = PersonUserFactory(first_name="The", last_name="Dude", dob=date(1999, 1, 1))
        dependant_user = PatientUserFactory(first_name="Misty", last_name="Martin")
        eligibility_dependant_person.user = dependant_user
        eligibility_dependant_person.save()
        eligibility_dependant_person.refresh_from_db()

        # Simulate Firefly insurance ID card upload
        person_2.insurance_info.insurance_payer = firefly_payer
        person_2.insurance_info.insurance_plan = firefly_plan
        person_2.insurance_info.member_id = "**********-00"
        person_2.insurance_info.save()
        person_3.insurance_info.insurance_payer = firefly_payer
        person_3.insurance_info.insurance_plan = firefly_plan
        person_3.insurance_info.member_id = "**********-00"
        person_3.insurance_info.save()
        person_4.insurance_info.insurance_payer = firefly_payer
        person_4.insurance_info.insurance_plan = firefly_plan
        person_4.insurance_info.member_id = "FF10000003-00"
        person_4.insurance_info.save()
        self.assertNotEqual(eligibility_person_2, person_2)
        self.assertNotEqual(eligibility_person_3, person_3)
        eligibility_status_1 = _autocheck_eligibility(eligibility_person_1, self.patient.id)
        self.assertTrue(eligibility_status_1)
        eligibility_status_dependant = _autocheck_eligibility(eligibility_dependant_person, self.patient.id)
        employee_identifier = AliasMapping.get_alias_id_for_object(
            obj=eligibility_dependant_person,
            alias_name=AliasName.EMPLOYEE_IDENTIFIER,
            pk=eligibility_dependant_person.pk,
        )
        self.assertTrue(eligibility_status_dependant)
        self.assertEqual(employee_identifier, "123124689")

        eligibility_status_2 = _autocheck_eligibility(person_2, self.patient.id)
        self.assertTrue(eligibility_status_2)
        eligibility_status_3 = _autocheck_eligibility(person_3, self.patient.id)
        self.assertTrue(eligibility_status_3)
        eligibility_status_4 = _autocheck_eligibility(person_4, self.patient.id)
        self.assertFalse(eligibility_status_4)

    @patch("firefly.modules.auto_eligibility.utils._submit_eligibility_request")
    @patch("firefly.modules.auto_eligibility.utils._handle_eligibility_response")
    def test_autocheck_eligibility_for_expired_health_plan_user_with_new_care_based_insurance(
        self, mock_handle_eligibility_response, mock_submit_eligibility_request
    ):
        task_collection, _ = TaskCollection.objects.get_or_create(
            title=TaskCollectionTaskUniqueIdentifiers.INSURANCE,
        )
        TaskCollectionTask.objects.update_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.INSURANCE,
            defaults={"task_collection": task_collection},
        )
        firefly_payer, _ = InsurancePayer.objects.get_or_create(
            name=FIREFLY_PAYER,
            defaults={"firefly_accepted": True, "pmpm_eligible": True},
        )
        firefly_plan, _ = InsurancePlan.objects.get_or_create(
            name=FIREFLY_PLAN,
            defaults={
                "firefly_accepted": True,
                "insurance_payer": firefly_payer,
                "review_state": InsurancePlanReviewTransitionMetadata.States.REVIEWED,
            },
        )
        rows = pd.DataFrame(
            [
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF100",
                    "LocationID": "FF100-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "012345678",
                    "EmployeeFirstName": "Indiana",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Jones",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1990-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": None,
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-00",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "0",
                    "MemberSSN": "012345678",
                    "MemberFirstName": "Indiana",
                    "MemberMiddleInitial": "",
                    "MemberLastName": "Jones",
                    "MemberGender": "Male",
                    "MemberDateOfBirth": "1990-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Self",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2022-11-11 04:00:39.865 +0000 UTC",
                },
                {
                    "BlockOfBusinessID": "PFIRE",
                    "GroupID": "FF100",
                    "LocationID": "FF100-2",
                    "PlanID": "PFIRE Plan1",
                    "Email": "<EMAIL>",
                    "EmployeeKey": "**********-00",
                    "EmployeeSSN": "987654321",
                    "EmployeeFirstName": "Luke",
                    "EmployeeMiddleInitial": None,
                    "EmployeeLastName": "Skywalker",
                    "EmployeeGender": "Male",
                    "EmployeeDateOfBirth": "1984-01-01",
                    "EmployeeAddress1": "501 Fairbury Lane",
                    "EmployeeAddress2": None,
                    "EmployeeAddress3": None,
                    "EmployeeCity": "Blue Rapids",
                    "EmployeeState": "KS",
                    "EmployeeZipCode": "66411",
                    "MemberKey": "**********-00",
                    "MemberID": "**********",
                    "MemberSequenceNumber": "0",
                    "MemberSSN": "987654321",
                    "MemberFirstName": "Luke",
                    "MemberMiddleInitial": "",
                    "MemberLastName": "Skywalker",
                    "MemberGender": "Male",
                    "MemberDateOfBirth": "1984-01-01",
                    "MemberAddress1": "501 Fairbury Lane",
                    "MemberAddress2": None,
                    "MemberAddress3": None,
                    "MemberCity": "Blue Rapids",
                    "MemberState": "KS",
                    "MemberZipCode": "66411",
                    "Relationship": "Self",
                    "MemberCoverageProduct": "MM",
                    "CoverageLevel": "ECH",
                    "MemberEffectiveDate": "2021-07-01",
                    "MemberTerminationDate": None,
                    "Phone": None,
                    "MemberCoverageStatus": "Active",
                    "ChangeTime": "2022-11-11 04:00:39.865 +0000 UTC",
                },
            ]
        )

        self.employer, _ = Employer.objects.get_or_create(name="IDEXX")
        AliasMapping.set_mapping_by_object(self.employer, AliasName.FLUME, "FF101")
        for i, row in rows.iterrows():
            parse_eligibility_v2(row)
        eligibility_person = Person.objects.get(first_name="Indiana", last_name="Jones")
        person = PersonUserFactory(first_name="Indy", last_name="Jones", dob=date(1990, 1, 1))
        # Simulate Firefly insurance ID card upload
        person.insurance_info.insurance_payer = firefly_payer
        person.insurance_info.insurance_plan = firefly_plan
        person.insurance_info.member_id = "**********-00"
        person.insurance_info.save()
        self.assertNotEqual(eligibility_person, person)
        eligibility_status = _autocheck_eligibility(person, self.patient.id)
        self.assertTrue(eligibility_status)
        # expire the firefly insurance and update the insurance payer to non firefly insurance.
        eligibility_record = EligibilityRecord.objects.get(member=person)
        eligibility_record.termination_date = datetime.now(tz=UTC_TIMEZONE) - timedelta(days=3)
        eligibility_record.coverage_status = CoverageStatus.TERMINATED
        eligibility_record.save()
        person.insurance_info.insurance_payer = InsurancePayerFactory()
        person.insurance_info.save()
        mock_submit_eligibility_request.return_value = "<>"
        mock_handle_eligibility_response.return_value = False
        eligibility_status = _autocheck_eligibility(person, self.patient.id)
        self.assertEqual(eligibility_status, False)
        mock_handle_eligibility_response.return_value = True
        eligibility_status = _autocheck_eligibility(person, self.patient.id)
        self.assertEqual(eligibility_status, True)


def _clear_cases(case_category: CaseCategory, person: Person):
    # delete all appointment_insurance_outreach_cases
    for case in person.cases.filter(category=case_category):
        case.delete()


class EligibilityStatusTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    def test_get_eligibility_status_person_not_in_benefit_program(self, mock_person_is_in_program):
        # Test case 6: Person not in Benefit program (should raise ValueError)
        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = False

        with self.assertRaises(ValueError):
            _get_eligibility_status_based_on_eligibility_record(person)

        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)

    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    @mock.patch("firefly.modules.auto_eligibility.utils.EligibilityRecord.objects.get")
    def test_get_eligibility_status_no_eligibility_record(self, mock_get, mock_person_is_in_program):
        # Test case 5: Person with no eligibility record (should return False)
        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = True
        mock_get.side_effect = EligibilityRecord.DoesNotExist

        result = _get_eligibility_status_based_on_eligibility_record(person)

        self.assertFalse(result)
        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)
        mock_get.assert_called_once_with(member=person)

    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    @mock.patch("firefly.modules.auto_eligibility.utils.logger")
    def test_get_eligibility_status_active_coverage_future_termination(self, mock_logger, mock_person_is_in_program):
        # Test case 1: Person with active coverage status and future termination date (should return True)
        from firefly.core.services.flume.constants import CoverageStatus

        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = True

        # Create eligibility record with active status and future termination date
        future_date = date.today() + timedelta(days=30)
        eligibility_record = EligibilityRecord.objects.create(
            member=person,
            effective_date=date.today() - timedelta(days=30),
            termination_date=datetime.combine(future_date, datetime.min.time()),
            coverage_status=CoverageStatus.ACTIVE,
        )

        result = _get_eligibility_status_based_on_eligibility_record(person)

        self.assertTrue(result)
        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)
        mock_logger.warning.assert_not_called()
        mock_logger.info.assert_not_called()

        # Clean up
        eligibility_record.delete()

    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    @mock.patch("firefly.modules.auto_eligibility.utils.logger")
    def test_get_eligibility_status_active_coverage_past_termination(self, mock_logger, mock_person_is_in_program):
        # Test case 2: Person with active coverage status but past termination date (should return False)
        from firefly.core.services.flume.constants import CoverageStatus

        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = True

        # Create eligibility record with active status but past termination date
        past_date = date.today() - timedelta(days=1)
        eligibility_record = EligibilityRecord.objects.create(
            member=person,
            effective_date=date.today() - timedelta(days=30),
            termination_date=datetime.combine(past_date, datetime.min.time()),
            coverage_status=CoverageStatus.ACTIVE,
        )

        result = _get_eligibility_status_based_on_eligibility_record(person)

        self.assertFalse(result)
        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)
        mock_logger.warning.assert_called_once()
        mock_logger.info.assert_not_called()

        # Clean up
        eligibility_record.delete()

    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    @mock.patch("firefly.modules.auto_eligibility.utils.logger")
    def test_get_eligibility_status_inactive_coverage_past_termination(self, mock_logger, mock_person_is_in_program):
        # Test case 3: Person with inactive coverage status and past termination date (should return False)
        from firefly.core.services.flume.constants import CoverageStatus

        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = True

        # Create eligibility record with inactive status and past termination date
        past_date = date.today() - timedelta(days=1)
        eligibility_record = EligibilityRecord.objects.create(
            member=person,
            effective_date=date.today() - timedelta(days=30),
            termination_date=datetime.combine(past_date, datetime.min.time()),
            coverage_status=CoverageStatus.TERMINATED,
        )

        result = _get_eligibility_status_based_on_eligibility_record(person)

        self.assertFalse(result)
        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)
        mock_logger.warning.assert_not_called()
        mock_logger.info.assert_called_once()

        # Clean up
        eligibility_record.delete()

    @mock.patch("firefly.modules.auto_eligibility.utils.person_is_in_program")
    @mock.patch("firefly.modules.auto_eligibility.utils.logger")
    def test_get_eligibility_status_inactive_coverage_future_termination(self, mock_logger, mock_person_is_in_program):
        # Test case 4: Person with inactive coverage status but future termination date (should return False)
        from firefly.core.services.flume.constants import CoverageStatus

        person = PersonUserFactory.create()
        mock_person_is_in_program.return_value = True

        # Create eligibility record with inactive status but future termination date
        future_date = date.today() + timedelta(days=30)
        eligibility_record = EligibilityRecord.objects.create(
            member=person,
            effective_date=date.today() - timedelta(days=30),
            termination_date=datetime.combine(future_date, datetime.min.time()),
            coverage_status=CoverageStatus.TERMINATED,
        )

        result = _get_eligibility_status_based_on_eligibility_record(person)

        self.assertFalse(result)
        mock_person_is_in_program.assert_called_once_with(person, ProgramCodes.BENEFIT)
        mock_logger.warning.assert_called_once()
        mock_logger.info.assert_not_called()

        # Clean up
        eligibility_record.delete()
