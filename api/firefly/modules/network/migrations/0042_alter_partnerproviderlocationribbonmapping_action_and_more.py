# Generated by Django 4.2.20 on 2025-05-23 10:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("network", "0041_alter_partnership_partnership_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="partnerproviderlocationribbonmapping",
            name="action",
            field=models.CharField(choices=[("added", "Added"), ("removed", "Removed")], max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationribbonmapping",
            name="location_uuid",
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationribbonmapping",
            name="provider_location",
            field=models.OneToOneField(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ribbon_mapping",
                to="network.partnerproviderlocation",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="partnerproviderlocationribbonmappingevent",
            name="action",
            field=models.CharField(choices=[("added", "Added"), ("removed", "Removed")], max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationribbonmappingevent",
            name="location_uuid",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationribbonmappingevent",
            name="provider_location",
            field=models.ForeignKey(
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                related_query_name="+",
                to="network.partnerproviderlocation",
            ),
        ),
    ]
