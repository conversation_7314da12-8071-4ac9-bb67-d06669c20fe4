# Generated by Django 4.2.20 on 2025-05-26 13:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("network", "0045_deprecate_partner_location"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="partnerlocation",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name="partnerlocation",
            name="import_job_name",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="partnerlocationevent",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name="partnerlocationevent",
            name="import_job_name",
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerprovider",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="partnerprovider",
            name="import_job_name",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderevent",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderevent",
            name="import_job_name",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocation",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocation",
            name="import_job_name",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationevent",
            name="has_exact_match_in_ribbon",
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationevent",
            name="import_job_name",
            field=models.CharField(max_length=255, null=True),
        ),
    ]
