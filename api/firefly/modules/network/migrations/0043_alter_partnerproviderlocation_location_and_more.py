# Generated by Django 4.2.20 on 2025-05-23 10:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("network", "0042_alter_partnerproviderlocationribbonmapping_action_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="partnerproviderlocation",
            name="location",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="provider_locations",
                to="network.partnerlocation",
            ),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocation",
            name="provider",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="provider_locations",
                to="network.partnerprovider",
            ),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationevent",
            name="location",
            field=models.ForeignKey(
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                related_query_name="+",
                to="network.partnerlocation",
            ),
        ),
        migrations.AlterField(
            model_name="partnerproviderlocationevent",
            name="provider",
            field=models.ForeignKey(
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                related_query_name="+",
                to="network.partnerprovider",
            ),
        ),
    ]
