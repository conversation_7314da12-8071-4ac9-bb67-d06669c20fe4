# Generated by Django 4.2.20 on 2025-05-26 13:00

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("network", "0044_deprecate_partner_provider"),
    ]

    operations = [
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="partnerlocation",
            name="address_line_1",
            field=models.Cha<PERSON><PERSON><PERSON>(max_length=255, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="partnerlocation",
            name="care_org_name",
            field=models.Char<PERSON><PERSON>(max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="partnerlocation",
            name="zip_code",
            field=models.Cha<PERSON><PERSON><PERSON>(max_length=5, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="partnerlocationevent",
            name="address_line_1",
            field=models.Char<PERSON><PERSON>(max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="partnerlocationevent",
            name="care_org_name",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=255, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="partnerlocationevent",
            name="zip_code",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=5, null=True),
        ),
    ]
