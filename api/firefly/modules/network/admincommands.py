import sys

from django import forms
from django.db.models import Q

from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand
from firefly.modules.network.models import (
    AgreementTypeConfig,
    FireflyNearbyCoverage,
    Partnership,
    PartnershipType,
    ProviderRecommendationAnalysis,
)


class IngestRawPartnershipRecords(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class MarkPartnershipRecordsUnprocessed(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        mark_provider_partnership_unprocessed = forms.BooleanField(required=False)
        mark_facility_partnership_unprocessed = forms.BooleanField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data.get("mark_provider_partnership_unprocessed"):
            args.append("--mark_provider_partnership_unprocessed")
        if data.get("mark_facility_partnership_unprocessed"):
            args.append("--mark_facility_partnership_unprocessed")
        return args, {"user": user}


class LinkPartnershipRecordsWithCuratedProviders(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["limit"]:
            args.append("--limit")
            args.append(data["limit"])
        if data["offset"]:
            args.append("--offset")
            args.append(data["offset"])
        return args, {"user": user}


class UpdateRibbonData(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        s3_file_name = forms.CharField(required=True)
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = ["--s3_file_name", data["s3_file_name"]]
        return args, {"user": user}


class IngestCuratedProviderRanking(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        RANK_TYPE_CHOICES = [
            ("Uprank", "Uprank"),
            ("Downrank", "Downrank"),
        ]
        with_dry_run_option = True
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)
        csv_key = forms.CharField(required=True)
        rank_type = forms.ChoiceField(
            choices=RANK_TYPE_CHOICES,
            required=True,
            help_text="Whether you want to downrank or uprank providers",
        )

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        args.append("--rank_type")
        args.append(data["rank_type"])
        if data["limit"]:
            args.append("--limit")
            args.append(data["limit"])
        if data["offset"]:
            args.append("--offset")
            args.append(data["offset"])
        return args, {"user": user}


class UploadCsvFileToS3(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        CSV_TYPE = [
            ("all_curated_providers", "all_curated_providers"),
            ("all_partnership_contract", "all_partnership_contract"),
            ("all_partnership_insurance_state_mapping", "all_partnership_insurance_state_mapping"),
            ("all_partnerships", "all_partnerships"),
            ("all_services_specialty_location_type", "all_services_specialty_location_type"),
            ("all_clinical_focus_areas_specialty", "all_clinical_focus_areas_specialty"),
            ("downranking", "downranking"),
            ("ffnb_prospects_list", "ffnb_prospects_list"),
            ("ingest_latitude_longitude", "ingest_latitude_longitude"),
            ("ribbon_edits", "ribbon_edits"),
            ("specialty_group_mappings", "specialty_group_mappings"),
            ("upranking", "upranking"),
            ("provider_recommendation_analysis_list", "provider_recommendation_analysis_list"),
            ("all_service_cpt_code_mapping", "all_service_cpt_code_mapping"),
        ]
        with_dry_run_option = True
        csv_type = forms.ChoiceField(choices=CSV_TYPE, required=True)
        csv = forms.FileField(widget=forms.ClearableFileInput, required=True)

        def clean_csv(self):
            data = self.cleaned_data["csv"]
            if data:
                sys.stdin = data.file
            return data

    def get_command_arguments(self, data, user):
        args = [
            "--csv",
            "-",
            "-csv_type",
            data["csv_type"],
        ]
        return args, {"user": user}


class IngestPartnershipsAndServices(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        limit = forms.IntegerField(required=False)
        offset = forms.IntegerField(required=False)
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        if data["limit"]:
            args.append("--limit")
            args.append(data["limit"])
        if data["offset"]:
            args.append("--offset")
            args.append(data["offset"])
        return args, {"user": user}


class BackfillLatitudeLongitudeForCuratedProvider(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        limit = forms.IntegerField(required=True)

    def get_command_arguments(self, data, user):
        args = []
        if data["limit"]:
            args.append("--limit")
            args.append(data["limit"])
        return args, {"user": user}


class DeleteObsoletePartnershipRecordsAndIngestCuratedProvider(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {"user": user}


class DeleteAllObsoletePartnershipMappingData(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        return args, {"user": user}


class IngestCuratedProviderLatitudeLongitude(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class IngestSpecialtyGroupServiceMapping(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class IngestSpecialtyGroupClinicalFocusAreaMapping(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class IngestPartnershipContractMapping(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class IngestInsurancePartnershipMapping(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}


class IngestFfnbProspectList(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        target_name = forms.CharField(required=True)
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        if data["target_name"]:
            args.append("--target_name")
            args.append(data["target_name"])
        return args, {"user": user}


class PartnershipMultipleChoiceField(forms.ModelMultipleChoiceField):
    def label_from_instance(self, obj):
        return f"{obj.partner_name}"


class FireflyNearbyCoverageAnalysis(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

            import_job_name_choices = FireflyNearbyCoverage.objects.values_list("import_job_name", flat=True).distinct()
            self.fields["import_job_name"].choices = [(job_name, job_name) for job_name in import_job_name_choices]

        import_job_name = forms.ChoiceField(
            choices=[],  # Initialize with an empty list; choices will be set in __init__
            required=True,
            help_text="Select job name for which you need to do coverage analysis",
        )

        distance_in_miles = forms.FloatField(
            required=True,
            help_text="Add distance in Miles for the FFNB in person analysis",
        )

        unprocessed_rows_only = forms.BooleanField(
            required=False,
            help_text="Only run coverage analysis for unprocessed rows",
        )

        included_partnerships = PartnershipMultipleChoiceField(
            required=False,
            queryset=Partnership.objects.filter(is_active=False)
            .filter(
                Q(
                    Q(partnership_type=PartnershipType.URGENT_RETAIL_CARE)
                    & Q(agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
                )
                | Q(
                    Q(partnership_type=PartnershipType.IN_HOME_SERVICES)
                    & Q(agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
                )
                | Q(partnership_type=PartnershipType.LAB)
            )
            .all(),
            help_text="Include partnerships for coverage analysis which are not active",
            widget=forms.SelectMultiple,
        )
        excluded_partnerships = PartnershipMultipleChoiceField(
            required=False,
            queryset=Partnership.objects.filter(is_active=True)
            .filter(
                Q(
                    Q(partnership_type=PartnershipType.URGENT_RETAIL_CARE)
                    & Q(agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
                )
                | Q(
                    Q(partnership_type=PartnershipType.IN_HOME_SERVICES)
                    & Q(agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
                )
                | Q(partnership_type=PartnershipType.LAB)
            )
            .all(),
            help_text="Exclude partnerships for coverage analysis which are active",
            widget=forms.SelectMultiple,
        )

    def get_command_arguments(self, data, user):
        args = ["--import_job_name", data["import_job_name"]]
        if data["distance_in_miles"]:
            args.append("--distance_in_miles")
            args.append(data["distance_in_miles"])
        if data["unprocessed_rows_only"]:
            args.append("--unprocessed_rows_only")
            args.append(data["unprocessed_rows_only"])
        if data["excluded_partnerships"]:
            args.append("--excluded_partnerships")
            [args.append(obj.id) for obj in data["excluded_partnerships"]]
        if data["included_partnerships"]:
            args.append("--included_partnerships")
            [args.append(obj.id) for obj in data["included_partnerships"]]
        return args, {"user": user}


class UploadFfnbPartnerGeojson(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        pass

    def get_command_arguments(self, data, user):
        return [], {"user": user}


class RefreshProviderSearchCache(FireflyAdminCommand):
    class form(forms.Form):
        pass

    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class TalonAuthenticationValidation(FireflyAdminCommand):
    class form(forms.Form):
        pass

    def get_command_arguments(self, data, user):
        return [], {
            "user": user,
        }


class IngestProviderDetailsForRecommendationAnalysis(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        target_name = forms.CharField(required=True)
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        if data["target_name"]:
            args.append("--target_name")
            args.append(data["target_name"])
        return args, {"user": user}


class InitiateProviderRecommendationAnalysis(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

            import_job_name_choices = ProviderRecommendationAnalysis.objects.values_list(
                "import_job_name", flat=True
            ).distinct()
            self.fields["import_job_name"].choices = [(job_name, job_name) for job_name in import_job_name_choices]

        import_job_name = forms.ChoiceField(
            choices=[],  # Initialize with an empty list; choices will be set in __init__
            required=True,
            help_text="Select job name for which you need to do analysis",
        )
        unprocessed_rows_only = forms.BooleanField(
            required=False,
            help_text="Only run analysis for unprocessed rows",
        )

    def get_command_arguments(self, data, user):
        args = ["--import_job_name", data["import_job_name"]]
        if data["unprocessed_rows_only"]:
            args.append("--unprocessed_rows_only")
            args.append(data["unprocessed_rows_only"])
        return args, {"user": user}


class IngestServicesCptCodeMappings(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        csv_key = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["--csv_key", data["csv_key"]]
        return args, {"user": user}
