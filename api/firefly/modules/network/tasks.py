import logging
from typing import List, Optional

import dramatiq
from django.apps import apps
from django.conf import settings
from django.contrib.gis.geos import Point
from django.db.models import QuerySet
from geopy.location import Location
from rest_framework import status

from firefly.core.services.dramatiq.constants import (
    RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
    RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
from firefly.core.services.dramatiq.utils import get_rate_limiter, get_window_rate_limiter
from firefly.core.services.geocoder import Geocoder
from firefly.modules.facts.models import ZipCodeCoordinate
from firefly.modules.network.models import (
    CuratedProvider,
    FireflyNearbyCoverage,
    PartnershipRecord,
    ProviderRecommendationAnalysis,
    TaxIdentifier,
    get_unique_fields_for_care_org_level_partnership_records,
    get_unique_fields_for_provider_at_care_org_level_partnership_records,
)
from firefly.modules.network.utils.utils import (
    ClinicalFocusAreaSpecialtyRow,
    CuratedProviderPartnershipRow,
    CuratedProviderRankingRow,
    FFNBProspectListRow,
    IngestCuratedProviderLatitudeLongitudeRow,
    PartnershipContractRow,
    PartnershipInsurancePayerRow,
    PartnershipWithServicesRow,
    ProviderRecommendationAnalysisRow,
    ServiceCPTCodeRow,
    ServicesSpecialtyLocationTypeRow,
    create_ffnb_partner_geojson,
    delete_obsolete_records,
    process_clinical_focus_area_with_specialty_group,
    process_curated_provider_latitude_longitude_row,
    process_curated_provider_ranking_row,
    process_ffnb_coverage_analysis,
    process_linking_partnership_records_to_curated_provider,
    process_partner_rows_with_contract,
    process_partnership_record_batch,
    process_partnership_rows_with_insurance,
    process_partnership_rows_with_services_and_clinical_focus_areas,
    process_provider_recommendation_analysis,
    process_service_cpt_code_mapping,
    process_services_rows_with_specialty_group_location_type,
    update_ribbon_location_data_entries,
    upload_geojson_to_s3,
)

logger = logging.getLogger(__name__)


# This is for ingesting the curated provider rankings from csv to the curated provider and ranking table
@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_curated_provider_ranking_rows_async(
    rows: List[CuratedProviderRankingRow], rank_change: int, log_prefix: str
):
    logger.info("%s: Started processing batch", log_prefix)
    if not rows:
        logger.info("%s: No rows to process", log_prefix)
        return
    for row in rows:
        try:
            process_curated_provider_ranking_row(row, rank_change, log_prefix)
        except Exception:
            logger.exception("%s: Failed to process row %s", log_prefix, row)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def delete_obsolete_partnership_records_and_link_curated_providers_async(active_import_job_name: str):
    # use concurrency limiter instead of window rate limiter
    # to only allow one concurrent invocation
    with get_rate_limiter(
        key="delete_obsolete_partnership_records_and_link_curated_provider_mutex",
        limit=1,
    ).acquire():
        log_prefix = "delete_obsolete_partnership_records_and_link_curated_provider"
        logger.info(
            "%s: Started deleting obsolete partnership records %s",
            log_prefix,
            active_import_job_name,
        )
        delete_obsolete_partnership_records_and_link_curated_providers(
            active_import_job_name=active_import_job_name,
        )


def delete_obsolete_partnership_records_and_link_curated_providers(
    active_import_job_name: str,
):
    delete_obsolete_records(
        active_import_job_name=active_import_job_name,
        Model=PartnershipRecord,
    )
    publish_linking_partnership_records_to_curated_provider(dry_run_off=True)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def delete_obsolete_partnership_mapping_data_async(active_import_job_name: str, app_name: str, model_name: str):
    # use concurrency limiter instead of window rate limiter
    # to only allow one concurrent invocation
    with get_rate_limiter(
        key="delete_obsolete_partnership_data_mutex",
        limit=1,
    ).acquire():
        log_prefix = "delete_obsolete_partnership_data"
        logger.info(
            "%s: Started deleting obsolete partnership data %s",
            log_prefix,
            active_import_job_name,
        )
        model = apps.get_model(app_name, model_name)
        delete_obsolete_records(
            active_import_job_name=active_import_job_name,
            Model=model,
        )


def publish_linking_partnership_records_to_curated_provider(
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    dry_run_off: bool = False,
):
    batch_size = 500
    log_prefix: str = "link_partnership_records_to_curated_provider"
    partnership_records_queryset: QuerySet[PartnershipRecord] = PartnershipRecord.objects.order_by(
        "-created_at",
        "-pk",
    )
    if limit is not None:
        if offset is None:
            offset = 0
        last_index = offset + limit
        logger.info("%s: Limiting to %s - %s", log_prefix, offset, last_index)
        partnership_records_queryset = partnership_records_queryset[offset:last_index]

    partnership_record_ids_batch: List[int] = []
    for partnership_record in partnership_records_queryset.iterator():
        partnership_record_ids_batch.append(partnership_record.id)
        if len(partnership_record_ids_batch) >= batch_size and dry_run_off:
            logger.info(
                "%s: Publishing the Linking partnership records to curated provider batch",
                log_prefix,
            )
            process_linking_partnership_records_to_curated_provider_async.send(partnership_record_ids_batch)
            partnership_record_ids_batch = []

    # Process remaining partnership records
    if len(partnership_record_ids_batch) > 0 and dry_run_off:
        logger.info(
            "%s: Publishing remaining rows for Linking partnership records to curated provider batch",
            log_prefix,
        )
        process_linking_partnership_records_to_curated_provider_async.send(partnership_record_ids_batch)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_linking_partnership_records_to_curated_provider_async(partnership_records_ids: List[int]):
    if not partnership_records_ids:
        return

    partnership_records = PartnershipRecord.objects.filter(id__in=partnership_records_ids).all()

    if not partnership_records:
        logger.error(
            "process_linking_partnership_records_to_curated_provider: Partnership records not found for ids: %s",
            partnership_records_ids,
        )
        return

    for partnership_record in partnership_records:
        process_linking_partnership_records_to_curated_provider(partnership_record=partnership_record, dry_run_off=True)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
def update_ribbon_location_data_entries_async(
    log_prefix: str,
    s3_file_name: str,
    dry_run_off: bool,
):
    with get_window_rate_limiter(
        key="update_ribbon_location_data_entries_mutex",
        limit_per_window=6,
        window_in_seconds=1,
    ).acquire():
        update_ribbon_location_data_entries(
            dry_run_off=dry_run_off,
            log_prefix=log_prefix,
            s3_file_name=s3_file_name,
        )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_partnership_rows_with_services_and_clinical_focus_areas_async(
    batch: List[PartnershipWithServicesRow], log_prefix: str
):
    process_partnership_rows_with_services_and_clinical_focus_areas(batch, log_prefix)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_partnership_rows_with_insurance_async(
    batch: List[PartnershipInsurancePayerRow], log_prefix: str, import_job_name: str
):
    process_partnership_rows_with_insurance(batch, log_prefix, import_job_name)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_curated_provider_ingestion_csv_async(
    curated_provider_rows: List[CuratedProviderPartnershipRow], import_job_name: str, log_prefix: str
):
    partnership_record_with_only_facility_level_data: List[CuratedProviderPartnershipRow] = []
    partnership_record_with_provider_data: List[CuratedProviderPartnershipRow] = []

    if not curated_provider_rows:
        return

    try:
        for row in curated_provider_rows:
            # if npi is null or an empty string - store partnership data for care_org
            if not row["npi"]:
                partnership_record_with_only_facility_level_data.append(row)
            else:
                partnership_record_with_provider_data.append(row)

        # Process the partnership records rows
        if len(partnership_record_with_only_facility_level_data) > 0:
            logger.info("%s: Processing care org partnership records batch", log_prefix)
            process_partnership_record_rows_with_only_facility_data(
                batch=partnership_record_with_only_facility_level_data,
                log_prefix=log_prefix,
                import_job_name=import_job_name,
            )
        if len(partnership_record_with_provider_data) > 0:
            logger.info("%s: Processing provider partnership records batch", log_prefix)
            process_partnership_record_rows_with_provider_and_facility_data(
                batch=partnership_record_with_provider_data, log_prefix=log_prefix, import_job_name=import_job_name
            )

    except Exception:
        logger.exception("%s Failed", log_prefix)


def process_partnership_record_rows_with_provider_and_facility_data(
    batch: List[CuratedProviderPartnershipRow], log_prefix: str, import_job_name: str
):
    match_fields: List[str] = get_unique_fields_for_provider_at_care_org_level_partnership_records()
    process_partnership_record_batch(
        batch=batch, match_fields=match_fields, log_prefix=log_prefix, import_job_name=import_job_name
    )


def process_partnership_record_rows_with_only_facility_data(
    batch: List[CuratedProviderPartnershipRow], log_prefix: str, import_job_name: str
):
    match_fields: List[str] = get_unique_fields_for_care_org_level_partnership_records()
    process_partnership_record_batch(
        batch=batch, match_fields=match_fields, log_prefix=log_prefix, import_job_name=import_job_name
    )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
def geocode_curated_provider_async(curated_provider_id):
    with get_window_rate_limiter(
        key="geocode_curated_provider_mutex",
        limit_per_window=6,
        window_in_seconds=1,
    ).acquire():
        curated_provider = CuratedProvider.objects.get(id=curated_provider_id)
        geocode_curated_provider(curated_provider)


def geocode_curated_provider(curated_provider: CuratedProvider):
    log_prefix = "backfill_latitude_longitude_for_curated_provider"
    latitude = None
    longitude = None
    try:
        if curated_provider.address_line_1 and curated_provider.zip_code:
            latitude, longitude = Geocoder.geocode_address(
                curated_provider.address_line_1,
                curated_provider.address_line_2,
                curated_provider.city,
                curated_provider.state.abbreviation if curated_provider.state else "",
                curated_provider.zip_code,
                log_prefix,
            )
        elif curated_provider.zip_code:
            location: Optional[Location] = Geocoder.geocode_zipcode(curated_provider.zip_code, log_prefix)
            if location and location.latitude is not None and location.longitude is not None:
                latitude = location.latitude
                longitude = location.longitude
        else:
            logger.error(
                "%s: Address line and zipcode not found for the curated provider %s",
                log_prefix,
                curated_provider.id,
            )
            return

        if latitude is None or longitude is None:
            logger.error(
                "%s: Latitude and longitude not found for the curated provider %s",
                log_prefix,
                curated_provider.id,
            )
            return

        curated_provider.latitude = latitude
        curated_provider.longitude = longitude
        if latitude and longitude:
            curated_provider.point = Point(
                x=float(longitude),
                y=float(latitude),
                srid=4326,
            )
        curated_provider.save(update_fields=["latitude", "longitude", "point"])

    except Exception:
        logger.exception(
            "%s: Could not parse address for curated provider %s",
            log_prefix,
            curated_provider.id,
        )


# This is for ingesting the curated providers latitude and longitude details
@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_curated_provider_latitude_longitude_rows_async(
    batch: List[IngestCuratedProviderLatitudeLongitudeRow], log_prefix: str
):
    logger.info("%s: Started processing batch", log_prefix)
    if not batch:
        logger.info("%s: No rows to process", log_prefix)
        return
    for row in batch:
        try:
            process_curated_provider_latitude_longitude_row(row, log_prefix)
        except Exception:
            logger.exception("%s: Failed to process row %s", log_prefix, row)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_services_rows_with_specialty_group_location_type_async(
    batch: List[ServicesSpecialtyLocationTypeRow], log_prefix: str, import_job_name: str
):
    process_services_rows_with_specialty_group_location_type(batch, log_prefix, import_job_name)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_clinical_focus_area_with_specialty_group_async(
    batch: List[ClinicalFocusAreaSpecialtyRow], log_prefix: str, import_job_name: str
):
    process_clinical_focus_area_with_specialty_group(batch, log_prefix, import_job_name)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_partner_rows_with_contract_async(
    batch: List[PartnershipContractRow], log_prefix: str, import_job_name: str
):
    process_partner_rows_with_contract(batch, log_prefix, import_job_name)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
def geocode_zip_code_and_store_coordinates_async(zip_code: str, logger_prefix: str):
    with get_rate_limiter(
        key="process_ffnb_coverage_analysis_mutex",
        limit=1,
    ).acquire():
        try:
            # Call the SmartyStreet API for GeoCoding
            location: Optional[Location] = Geocoder.geocode_zipcode(zip_code, logger_prefix)
            if location and location.latitude is not None and location.longitude is not None:
                latitude = location.latitude
                longitude = location.longitude

                # Store as a ZipCodeCoordinates
                ZipCodeCoordinate.objects.get_or_create(
                    zip_code=zip_code,
                    defaults={
                        "latitude": latitude,
                        "longitude": longitude,
                        "point": Point(
                            x=float(longitude),
                            y=float(latitude),
                            srid=4326,
                        ),
                    },
                )
        except Exception:
            logger.exception("%s: SKIPPING: Smarty street not able to geocode the zipcode: %s", logger_prefix, zip_code)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_ffnb_prospect_list(
    ffnb_prospect_rows: List[FFNBProspectListRow], import_job_name: str, target_name: str, log_prefix: str
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data

    firefly_nearby_coverage_objects: List[FireflyNearbyCoverage] = []
    for ffnb_prospect_row in ffnb_prospect_rows:
        try:
            zip_code: str = (
                str(ffnb_prospect_row.get("zip_code", "")).strip() if ffnb_prospect_row.get("zip_code") else ""
            )
            member_count_str = ffnb_prospect_row.get("member_count", 0)
            member_count = int(member_count_str) if member_count_str else 0

            if not zip_code or member_count is None or not target_name or len(zip_code) != 5:
                logger.info("%s: Invalid Data, Skipping row %s", log_prefix, ffnb_prospect_row)
                continue

            # Check if the zip_code exist in the ZipCodeCoordinates for coverage analysis
            zip_code_coordinates = ZipCodeCoordinate.objects.filter(zip_code=zip_code).first()

            # Only geocode the zip code in prod environments
            if not zip_code_coordinates and settings.SMARTY_STREETS_GEOCODE_ENABLED is True:
                geocode_zip_code_and_store_coordinates_async.send(zip_code, log_prefix)

            firefly_nearby_coverage_objects.append(
                FireflyNearbyCoverage(
                    zip_code=zip_code,
                    target_name=target_name,
                    member_count=member_count,
                    is_processed=False,
                    import_job_name=import_job_name,
                )
            )
        except Exception:
            logger.exception("%s: Failed to process the row: %s", log_prefix, ffnb_prospect_row)

    bulk_upsert_data(
        objects=firefly_nearby_coverage_objects,
        Model=FireflyNearbyCoverage,
        match_fields=["zip_code", "target_name", "import_job_name"],
        update_fields=["member_count", "is_processed"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_ffnb_coverage_analysis_async(
    ffnb_coverage_ids: List[int],
    distance_in_miles: float,
    log_prefix: str,
    included_partnership_ids: List[int],
    excluded_partnerships_ids: List[int],
):
    # use concurrency limiter instead of window rate limiter
    # to only allow one concurrent invocation
    with get_rate_limiter(
        key="process_ffnb_coverage_analysis_mutex",
        limit=1,
    ).acquire():
        process_ffnb_coverage_analysis(
            ffnb_coverage_ids=ffnb_coverage_ids,
            distance_in_miles=distance_in_miles,
            log_prefix=log_prefix,
            included_partnership_ids=included_partnership_ids,
            excluded_partnerships_ids=excluded_partnerships_ids,
        )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
def process_provider_recommendation_analysis_async(
    provider_recommendation_analysis_ids: List[int],
    import_job_name: str,
    log_prefix: str,
):
    # use concurrency limiter instead of window rate limiter
    # to only allow one concurrent invocation
    with get_rate_limiter(
        key="process_provider_recommendation_analysis_mutex",
        limit=1,
    ).acquire():
        process_provider_recommendation_analysis(
            provider_recommendation_analysis_ids=provider_recommendation_analysis_ids,
            import_job_name=import_job_name,
            log_prefix=log_prefix,
        )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def upload_ffnb_partner_geojson_async(file_path: str, log_prefix: str):
    geojson_data = create_ffnb_partner_geojson(log_prefix=log_prefix)

    upload_geojson_to_s3(geojson_data, file_path, log_prefix)


@dramatiq.actor(queue_name="directory_backfill", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def update_tin_details_for_tax_identifier(tin: str):
    from firefly.core.services.ribbon.client import get_tin_details
    from firefly.core.services.ribbon.types import RibbonTINDetails

    try:
        tax_identifer: TaxIdentifier = TaxIdentifier.objects.get(tin=tin)
    except TaxIdentifier.DoesNotExist:
        logger.exception("[StoreTINs] No Tax Identifier found for TIN %s", tin)

    try:
        logger.info("[StoreTINs] Retrieving TIN details for ID %s", tin)
        tin_details: RibbonTINDetails
        response_status: int
        tin_details, response_status = get_tin_details(tin_id=tin)
        assert response_status == status.HTTP_200_OK

        tax_identifer.name = tin_details["name"] or None
        tax_identifer.legal_name = tin_details["legal_name"] or None
        tax_identifer.address = tin_details["address"] or None
        tax_identifer.confirmed = tin_details["tin_confirmed"]
        tax_identifer.save()
    except Exception:
        logger.exception("[StoreTINs] Could not retrieve TIN information for ID %s", tin)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_provider_recommendation_analysis_list(
    provider_recommendation_analysis_rows: List[ProviderRecommendationAnalysisRow],
    import_job_name: str,
    target_name: str,
    log_prefix: str,
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data

    provider_recommendation_analysis_objects: List[ProviderRecommendationAnalysis] = []
    for provider_recommendation_analysis_row in provider_recommendation_analysis_rows:
        try:
            zip_code: str = (
                str(provider_recommendation_analysis_row.get("zip_code", "")).strip()
                if provider_recommendation_analysis_row.get("zip_code")
                else ""
            )
            npi = provider_recommendation_analysis_row.get("npi", None)

            if not zip_code or not npi or not target_name or len(zip_code) != 5:
                logger.info("%s: Invalid Data, Skipping row %s", log_prefix, provider_recommendation_analysis_row)
                continue

            provider_recommendation_analysis_objects.append(
                ProviderRecommendationAnalysis(
                    zip_code=zip_code,
                    target_name=target_name,
                    npi=npi,
                    is_processed=False,
                    import_job_name=import_job_name,
                )
            )
        except Exception:
            logger.exception("%s: Failed to process the row: %s", log_prefix, provider_recommendation_analysis_row)

    bulk_upsert_data(
        objects=provider_recommendation_analysis_objects,
        Model=ProviderRecommendationAnalysis,
        match_fields=["npi", "import_job_name"],
        update_fields=["zip_code", "is_processed", "target_name"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
)
def process_service_cpt_code_mapping_async(batch: List[ServiceCPTCodeRow], log_prefix: str, import_job_name: str):
    process_service_cpt_code_mapping(batch, log_prefix, import_job_name)
