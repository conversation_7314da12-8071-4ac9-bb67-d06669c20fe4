import factory
import factory.django
from faker import Faker
from faker.generator import random

from firefly.core.services.elation.constants import SEX_CHOICES
from firefly.modules.insurance.factories import ContractFactory, InsurancePayerFactory
from firefly.modules.states.models import State

from . import models

gender = [value for key, value in (SEX_CHOICES)]

agreement_type_choices = [value for key, value in models.AGREEMENT_TYPE_CHOICES]

partnership_level_choices = [value for key, value in models.PARTNERSHIP_TYPE_CHOICES]

ranking_level_choices = [value for key, value in models.CURATED_PROVIDER_RANKING_LEVEL_CHOICES]

ranking_reason_choices = [value for key, value in models.CURATED_PROVIDER_RANKING_REASON_CHOICES]

insurance_plan_type_choices = [value for key, value in models.PartnershipInsurancePayer.PLAN_TYPE_CHOICES]


def random_state(*args, **kwargs):
    fake = Faker()
    abbreviation = fake.state_abbr()
    state, _ = State.objects.get_or_create(
        abbreviation=abbreviation,
        defaults={"abbreviation": abbreviation, "can_service": True},
    )
    return state


def random_state_abbreviation(*args, **kwargs):
    fake = Faker()
    abbreviation = fake.state_abbr()
    state, _ = State.objects.get_or_create(
        abbreviation=abbreviation,
        defaults={"abbreviation": abbreviation, "can_service": True},
    )
    return state.abbreviation


class CuratedProviderFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.CuratedProvider

    npi = factory.Faker("pydecimal", left_digits=10, right_digits=0, positive=True)
    care_org_name = factory.Sequence(lambda n: f"CareOrg{n}")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("street_address")
    city = factory.Faker("city")
    state = state = factory.LazyAttribute(random_state)
    zip_code = factory.Faker("postcode")
    sanitized_care_org_name = None
    latitude = 40.7128
    longitude = -74.0060


class PartnershipFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Partnership

    agreement_type = random.choice(agreement_type_choices)
    can_accept_care_members = random.choice([True, False])
    can_accept_coverage_members = random.choice([True, False])
    can_accept_care_and_coverage_members = random.choice([True, False])
    partner_name = factory.Faker("name")
    is_active = True


class CuratedProviderPartnershipFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.CuratedProviderPartnership

    curated_provider = factory.SubFactory(CuratedProviderFactory)
    partnership = factory.SubFactory(PartnershipFactory)


class PartnershipRecordFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PartnershipRecord

    npi = factory.Faker("pydecimal", left_digits=10, right_digits=0, positive=True)
    care_org_name = factory.Sequence(lambda n: f"CareOrg{n}")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("street_address")
    city = factory.Faker("city")
    state = factory.LazyAttribute(random_state)
    zip_code = factory.Faker("postcode")
    partner_name = factory.Sequence(lambda n: f"Partner{n}")
    partnership_level = random.choice(partnership_level_choices)
    agreement_type = random.choice(agreement_type_choices)
    can_accept_care_members = random.choice([True, False])
    can_accept_coverage_members = random.choice([True, False])
    can_accept_care_and_coverage_members = random.choice([True, False])
    is_tagged_as_partner = random.choice([True, False])
    is_processed = random.choice([True, False])


class PartnershipContractFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PartnershipContract

    partnership = factory.SubFactory(PartnershipFactory)
    contract = factory.SubFactory(ContractFactory)
    is_primary_subscriber_only = random.choice([True, False])


class PartnershipInsurancePayerFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PartnershipInsurancePayer

    partnership = factory.SubFactory(PartnershipFactory)
    insurance_payer = factory.SubFactory(InsurancePayerFactory)
    state = state = factory.LazyAttribute(random_state)
    insurance_plan_type = random.choice(insurance_plan_type_choices)


class ContactInformationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.ContactInformation

    curated_provider = factory.SubFactory(CuratedProviderFactory)
    phone = factory.Faker("phone_number")
    fax = factory.Faker("phone_number")
    is_verified = random.choice([True, False])
    specialty_group = None


class AvailabilityFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Availability

    curated_provider = factory.SubFactory(CuratedProviderFactory)
    number_of_days_till_next_availability = factory.Faker("pyint")
    added_at = factory.Faker("date")
    does_provider_exist = True
    specialty_group = None


class RankingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Ranking

    curated_provider = factory.SubFactory(CuratedProviderFactory)
    ranking_level = random.choice(ranking_level_choices)
    reason = random.choice(ranking_reason_choices)
    rank_change = random.choice([-1, 0, 1])


class RibbonSpecialtyFactory(factory.DictFactory):
    taxonomy_code = factory.Faker("word")
    display = factory.Faker("word")


class RibbonAddressFactory(factory.DictFactory):
    zip = factory.Faker("postcode")
    city = factory.Faker("city")
    state = factory.LazyAttribute(random_state_abbreviation)
    street = factory.Faker("address")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = factory.Faker("building_number")


class FireflySpecialtyGroupFactory(factory.DictFactory):
    id = factory.Faker("pyint")
    label = factory.Faker("word")


class RibbonCostIndexFactory(factory.DictFactory):
    efficiency_index = random.choice([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
    ribbon_cost_score = random.choice([1, 2, 3, 4, 5])


class RibbonQualityIndexFactory(factory.DictFactory):
    outcomes_index = random.choice([1, 2, 3, 4, 5])


class RibbonAggregateScoreFactory(factory.DictFactory):
    cost = factory.SubFactory(RibbonCostIndexFactory)
    quality = factory.SubFactory(RibbonQualityIndexFactory)


class RibbonPerformanceDataFactory(factory.DictFactory):
    aggregate = factory.SubFactory(RibbonAggregateScoreFactory)


class RibbonLocationFactory(factory.DictFactory):
    uuid = factory.Faker("word")
    name = factory.Faker("company")
    latitude = factory.Faker("pyfloat")
    longitude = factory.Faker("pyfloat")
    address = factory.Faker("address")
    address_details = factory.SubFactory(RibbonAddressFactory)
    phone_numbers = None
    faxes = None
    location_types = None
    distance = 10
    insurances = None
    availability = None
    confidence = random.choice([1, 2, 3, 4, 5])
    specialty_groups = factory.List([factory.SubFactory(FireflySpecialtyGroupFactory) for _ in range(2)])
    network_partner_agreement_type = None
    is_verified = None
    department_phone_numbers = None
    department_faxes = None
    curated_provider_ranking = None
    curated_provider_ranking_reason = None
    tins = factory.List([factory.Faker("uuid4"), "dupe1", "dupe1"])


class RibbonProviderFactory(factory.DictFactory):
    first_name = factory.Faker("first_name")
    middle_name = factory.Faker("word")
    last_name = factory.Faker("last_name")
    npi = str(factory.Faker("pydecimal", left_digits=10, right_digits=0, positive=True))
    age = 30
    gender = factory.Faker("word")
    specialties = factory.List([factory.SubFactory(RibbonSpecialtyFactory) for _ in range(2)])
    insurances = None
    locations = factory.List([factory.SubFactory(RibbonLocationFactory)])
    performance = factory.SubFactory(RibbonPerformanceDataFactory)
    languages = ["English", "Spanish", "Korean"]
    ratings_count = random.choice([1, 2, 3, 4, 5])
    ratings_avg = random.choice([1, 2, 3, 4, 5])
    procedures = None
    specialty_groups = None
    availability = None
    in_network = True


class RibbonDataEditRowFactory(factory.DictFactory):
    uuid = factory.Faker("uuid4")
    name = factory.Faker("first_name")
    updated_name = factory.Faker("last_name")
