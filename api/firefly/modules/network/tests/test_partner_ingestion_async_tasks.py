from dramatiq.rate_limits.backends import StubBackend
from faker import Faker
from mock import patch

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.network.tasks import (
    delete_obsolete_partnership_records_and_link_curated_providers_async,
)


class DeleteRedundantPartnershipRecordsAsyncTestCase(FireflyTestCase):
    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    @patch("firefly.modules.network.tasks.delete_obsolete_partnership_records_and_link_curated_providers")
    def test_delete_obsolete_partnership_records_async(
        self,
        delete_obsolete_partnership_records_mock,
        backend_mock,
    ):
        active_import_job_name: str = Faker().pystr()
        delete_obsolete_partnership_records_and_link_curated_providers_async(
            active_import_job_name=active_import_job_name,
        )
        delete_obsolete_partnership_records_mock.assert_called_once_with(
            active_import_job_name=active_import_job_name,
        )
