from copy import deepcopy
from random import randint
from typing import Union
from unittest import mock
from unittest.mock import <PERSON><PERSON><PERSON>, Mock

from dramatiq.rate_limits.backends import StubBackend
from faker import Faker

from firefly.core.alias.models import <PERSON>asMapping, AliasName
from firefly.core.services.ribbon.types import RibbonLocation, RibbonProvider, RibbonProviderResponse
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import get_mock_redis
from firefly.core.user.factories import PersonUserFactory
from firefly.modules.facts.factories import LocationTypeFactory, SpecialtyFactory, SpecialtyGroupFactory
from firefly.modules.facts.models import LocationType, Specialty, SpecialtyGroup
from firefly.modules.insurance.factories import InsurancePayerFactory, InsurancePlanFactory, NetworkFactory
from firefly.modules.network.constants import CompositeScoreConfig
from firefly.modules.network.factories import (
    CuratedProviderFactory,
    RankingFactory,
    RibbonLocationFactory,
    RibbonProviderFactory,
)
from firefly.modules.network.models import (
    AgreementTypeConfig,
    CuratedProvider,
    <PERSON><PERSON>tedProviderRankingLevelConfig,
    CuratedProviderRankingReasonConfig,
)
from firefly.modules.network.utils.composite_score_utils import (
    get_facility_composite_score,
    get_facility_recommendation_status,
    get_firefly_recommendation_factors,
    get_provider_composite_score,
    get_provider_recommendation_status,
    get_recommendation_reason_labels,
    get_scaled_ribbon_cost_score,
    get_scaled_ribbon_quality_score,
    get_scaled_ribbon_user_rating,
    get_scaled_talon_cost_score,
    get_scaled_talon_expertise_score,
)
from firefly.modules.network.utils.utils import (
    clear_cache_data,
)
from firefly.modules.referral.constants import (
    MemberServiceLevel,
    RecommendationReasonLabel,
)
from firefly.modules.referral.factories import SearchAvailabilityFactory
from firefly.modules.referral.models import RecommendationStatus
from firefly.modules.referral.utils.provider_search_utils import (
    FireflyRecommendationFactors,
    PricePercentiles,
    ProcessedRibbonSearchResult,
    RibbonRecommendationFactors,
    process_ribbon_results,
)


def get_best_firefly_recommendation_factors():
    return {
        "cost_score_out_of_five": get_scaled_ribbon_cost_score(1),
        "quality_score_out_of_five": get_scaled_ribbon_quality_score(5),
        "average_rating_out_of_five": get_scaled_ribbon_user_rating(10),
        "number_of_ratings": 10,
        "expertise_score_out_of_five": None,
    }


def get_best_firefly_recommendation_factors_for_facility():
    return {
        "cost_score_out_of_five": get_scaled_talon_cost_score(
            10, PricePercentiles(p_0=50, p_25=100, p_50=200, p_75=300)
        ),
        "quality_score_out_of_five": get_scaled_ribbon_quality_score(5),
        "average_rating_out_of_five": get_scaled_ribbon_user_rating(10),
        "number_of_ratings": 10,
        "expertise_score_out_of_five": get_scaled_talon_expertise_score(1.0),
    }


def get_worst_firefly_recommendation_factors():
    return {
        "cost_score_out_of_five": get_scaled_ribbon_cost_score(10),
        "quality_score_out_of_five": get_scaled_ribbon_quality_score(1),
        "average_rating_out_of_five": get_scaled_ribbon_user_rating(1),
        "number_of_ratings": 1,
        "expertise_score_out_of_five": None,
    }


def get_worst_firefly_recommendation_factors_for_facility():
    return {
        "cost_score_out_of_five": get_scaled_talon_cost_score(
            300, PricePercentiles(p_0=50, p_25=100, p_50=200, p_75=300)
        ),
        "quality_score_out_of_five": get_scaled_ribbon_quality_score(1),
        "average_rating_out_of_five": get_scaled_ribbon_user_rating(1),
        "number_of_ratings": 1,
        "expertise_score_out_of_five": get_scaled_talon_expertise_score(0.1),
    }


def get_all_none_firefly_recommendation_factors():
    return {
        "cost_score_out_of_five": None,
        "quality_score_out_of_five": get_scaled_ribbon_quality_score(None),
        "average_rating_out_of_five": get_scaled_ribbon_user_rating(None),
        "number_of_ratings": None,
        "expertise_score_out_of_five": None,
    }


class RibbonScoreScalingTestCase(FireflyTestCase):
    def test_get_scaled_cost_score(self):
        # Case 1
        # When ribbon score is None, the scaled score should return 2.5
        self.assertEqual(get_scaled_ribbon_cost_score(None), None)

        # Case 2
        # When ribbon score is 1, the scaled score should return 5
        self.assertEqual(get_scaled_ribbon_cost_score(1), 5)

        # Case 3
        # When ribbon score is 10, the scaled score should return 1
        self.assertEqual(get_scaled_ribbon_cost_score(10), 0.5)

    def test_get_scaled_quality_score(self):
        # Case 1
        # When ribbon score is None, the scaled score should return 2.5
        self.assertEqual(get_scaled_ribbon_quality_score(None), None)

        # Case 2
        # When ribbon score is 1, the scaled score should return 1
        self.assertEqual(get_scaled_ribbon_quality_score(1), 1)

        # Case 3
        # When ribbon score is 5, the scaled score should return 5
        self.assertEqual(get_scaled_ribbon_quality_score(5), 5)

    def test_get_scaled_user_rating(self):
        # Case 1
        # When ribbon score is None, the scaled score should return 2.5
        self.assertEqual(get_scaled_ribbon_user_rating(None), None)

        # Case 2
        # When ribbon score is 1, the scaled score should return 5
        self.assertEqual(get_scaled_ribbon_user_rating(1), 0.5)

        # Case 3
        # When ribbon score is 10, the scaled score should return 1
        self.assertEqual(get_scaled_ribbon_user_rating(10), 5)


class FireflyRecommendationFactorsTestCase(FireflyTestCase):
    def test_firefly_recommendation_factors_without_downraking_and_partnership(self):
        # Case 1
        #   Passing the best ribbon recommendation factor data
        # Result
        #   Will return the scaled firefly recommendation factor
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 1,
            "quality_score": 5,
            "average_rating": 10,
            "number_of_ratings": 10,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": get_scaled_ribbon_cost_score(ribbon_recommendation_factors["cost_score"]),
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(ribbon_recommendation_factors, None, None, None, None),
            expected_firefly_recommendation_factors,
        )

        # Case 2
        #   Passing the worst ribbon recommendation factor data
        # Result
        #   Will return the scaled firefly recommendation factor
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 10,
            "quality_score": 1,
            "average_rating": 1,
            "number_of_ratings": 1,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": get_scaled_ribbon_cost_score(ribbon_recommendation_factors["cost_score"]),
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(ribbon_recommendation_factors, None, None, None, None),
            expected_firefly_recommendation_factors,
        )

        # Case 3
        #   Passing the None ribbon recommendation factor data
        # Result
        #   Will return the scaled (default) firefly recommendation factor
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": None,
            "quality_score": None,
            "average_rating": None,
            "number_of_ratings": None,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": get_scaled_ribbon_cost_score(ribbon_recommendation_factors["cost_score"]),
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": 0,
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(ribbon_recommendation_factors, None, None, None, None),
            expected_firefly_recommendation_factors,
        )

    def test_firefly_recommendation_factors_with_partnership(self):
        # Case 1
        #   Passing the best ribbon recommendation factor data with provider being direct partner
        # Result
        #   Will return the scaled firefly recommendation factor with cost score set to default partner cost index
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 1,
            "quality_score": 5,
            "average_rating": 10,
            "number_of_ratings": 10,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": CompositeScoreConfig.DIRECT_PARTNER_COST_INDEX,
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(
                ribbon_recommendation_factors, None, None, AgreementTypeConfig.DIRECT_CONTRACT, None
            ),
            expected_firefly_recommendation_factors,
        )

    def test_firefly_recommendation_factors_with_partnership_and_downranking(self):
        # Case 1
        #   Passing the best ribbon recommendation factor data with ranking as negative and reason as high cost
        # Result
        #   Will return the scaled firefly recommendation factor and cost score will be used as HIGH COST index
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 1,
            "quality_score": 5,
            "average_rating": 10,
            "number_of_ratings": 10,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": CompositeScoreConfig.HIGH_COST_CURATED_PROVIDER_COST_INDEX,
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(
                ribbon_recommendation_factors,
                -1,
                CuratedProviderRankingReasonConfig.HIGH_COST,
                AgreementTypeConfig.DIRECT_CONTRACT,
                None,
            ),
            expected_firefly_recommendation_factors,
        )

        # Case 2
        #   Passing the best ribbon recommendation factor data with ranking as negative and reason as not High Cost
        # Result
        #   Will return the scaled firefly recommendation factor and cost score will be used as partner cost index
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 1,
            "quality_score": 5,
            "average_rating": 10,
            "number_of_ratings": 10,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": CompositeScoreConfig.DIRECT_PARTNER_COST_INDEX,
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        faker = Faker()
        self.assertEqual(
            get_firefly_recommendation_factors(
                ribbon_recommendation_factors, -1, faker.name(), AgreementTypeConfig.DIRECT_CONTRACT, None
            ),
            expected_firefly_recommendation_factors,
        )

        # Case 3
        #   Passing the best ribbon recommendation factor data with ranking as positive and reason as high cost
        # Result
        #   Will return the scaled firefly recommendation factor and cost score will be used as partner cost index
        ribbon_recommendation_factors: RibbonRecommendationFactors = {
            "cost_score": 1,
            "quality_score": 5,
            "average_rating": 10,
            "number_of_ratings": 10,
        }
        expected_firefly_recommendation_factors: FireflyRecommendationFactors = {
            "cost_score_out_of_five": CompositeScoreConfig.DIRECT_PARTNER_COST_INDEX,
            "quality_score_out_of_five": get_scaled_ribbon_quality_score(
                ribbon_recommendation_factors["quality_score"]
            ),
            "average_rating_out_of_five": get_scaled_ribbon_user_rating(
                ribbon_recommendation_factors["average_rating"]
            ),
            "number_of_ratings": ribbon_recommendation_factors["number_of_ratings"],
            "expertise_score_out_of_five": None,
        }
        self.assertEqual(
            get_firefly_recommendation_factors(
                ribbon_recommendation_factors,
                1,
                CuratedProviderRankingReasonConfig.HIGH_COST,
                AgreementTypeConfig.DIRECT_CONTRACT,
                None,
            ),
            expected_firefly_recommendation_factors,
        )


class CompositeScoreTestCase(FireflyTestCase):
    def test_get_provider_composite_score(self):
        # Case 1
        #  send best firefly recommendation factor for HIGH TCoC segment
        # Result
        #   Should get composite score based on provider weights
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()

        expected_compostite_score_for_high_tcoc: float = (
            firefly_recommendation_factors["cost_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["cost_score"]
            + firefly_recommendation_factors["quality_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["quality_score"]
            + firefly_recommendation_factors["average_rating_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["user_rating"]
        )

        self.assertEqual(
            get_provider_composite_score(firefly_recommendation_factors),
            expected_compostite_score_for_high_tcoc,
        )

        # Case 2
        #  send best firefly recommendation factor for LOW TCoC segment
        # Result
        #   Should get composite score based on provider weights
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()

        expected_compostite_score_for_low_tcoc: float = (
            firefly_recommendation_factors["cost_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["cost_score"]
            + firefly_recommendation_factors["quality_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["quality_score"]
            + firefly_recommendation_factors["average_rating_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["user_rating"]
        )

        self.assertEqual(
            get_provider_composite_score(firefly_recommendation_factors),
            expected_compostite_score_for_low_tcoc,
        )
        # Case 3
        #  send best firefly recommendation factor for COMMODITY TCoC segment
        # Result
        #   Should get composite score based on provider weights
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()

        expected_compostite_score_for_commodity_tcoc: float = (
            firefly_recommendation_factors["cost_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["cost_score"]
            + firefly_recommendation_factors["quality_score_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["quality_score"]
            + firefly_recommendation_factors["average_rating_out_of_five"]
            * CompositeScoreConfig.PROVIDER_WEIGHTS["user_rating"]
        )

        self.assertEqual(
            get_provider_composite_score(firefly_recommendation_factors),
            expected_compostite_score_for_commodity_tcoc,
        )

    def test_get_facility_composite_score(self):
        # Case 1
        #  send best firefly recommendation factor
        # Result
        #   Should get composite score based on  weights
        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_best_firefly_recommendation_factors_for_facility()
        )

        expected_composite_score: float = (
            firefly_recommendation_factors["cost_score_out_of_five"]
            * CompositeScoreConfig.FACILITY_WEIGHTS["cost_score"]
            + firefly_recommendation_factors["expertise_score_out_of_five"]
            * CompositeScoreConfig.FACILITY_WEIGHTS["expertise_score"]
        )

        self.assertEqual(get_facility_composite_score(firefly_recommendation_factors), expected_composite_score)

        #  Case 2
        #  send worst firefly recommendation factor
        # Result
        #   Should get composite score based on  weights
        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_worst_firefly_recommendation_factors_for_facility()
        )

        expected_composite_score: float = (
            firefly_recommendation_factors["cost_score_out_of_five"]
            * CompositeScoreConfig.FACILITY_WEIGHTS["cost_score"]
            + firefly_recommendation_factors["expertise_score_out_of_five"]
            * CompositeScoreConfig.FACILITY_WEIGHTS["expertise_score"]
        )

        self.assertEqual(get_facility_composite_score(firefly_recommendation_factors), expected_composite_score)


class ProviderRecommendationStatusTestCase(FireflyTestCase):
    def test_provider_recommendation_status(self):
        # Case 1
        #   Get the provider recommendation status for best recommendation factors
        # Result
        #   The recommendation status should be recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.RECOMMENDED,
        )

        # Case 2
        #   Get the provider recommendation status for worst recommendation factors
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_worst_firefly_recommendation_factors()
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 3
        #   Get the provider recommendation status for None recommendation factors
        # Result
        #   The recommendation status should be neutral

        firefly_recommendation_factors: FireflyRecommendationFactors = get_all_none_firefly_recommendation_factors()
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NEUTRAL,
        )

        # Case 4
        #   Get the provider recommendation status for bad cost but good quality and rating
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["cost_score_out_of_five"] = 1
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 5
        #   Get the provider recommendation status for bad quality but good cost and rating
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["quality_score_out_of_five"] = 1
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 6
        #   Get the provider recommendation status for bad avg rating but good quality and cost
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["average_rating_out_of_five"] = 1
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 7
        #   Get the provider recommendation status for bad avg rating and
        #   number of ratings are less then MIN_NUMBER_OF_USER_RATINGS but good quality and cost
        # Result
        #   The recommendation status should be recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["average_rating_out_of_five"] = 1
        firefly_recommendation_factors["number_of_ratings"] = 5
        self.assertEqual(
            get_provider_recommendation_status(
                get_provider_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.RECOMMENDED,
        )

        # Case 8
        #   Composite score between RECOMMENDATION_COMPOSITE_SCORE and MIN_COMPOSITE_SCORE
        # Result
        #   The recommendation status should be neutral

        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["average_rating_out_of_five"] = 1
        firefly_recommendation_factors["number_of_ratings"] = 5
        self.assertEqual(
            get_provider_recommendation_status(
                2.75,
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NEUTRAL,
        )

    def test_facility_recommendation_status(self):
        # Case 1
        #   Get the facility recommendation status for best recommendation factors
        # Result
        #   The recommendation status should be recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_best_firefly_recommendation_factors_for_facility()
        )
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.RECOMMENDED,
        )

        # Case 2
        #   Get the facility recommendation status for worst recommendation factors
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_worst_firefly_recommendation_factors_for_facility()
        )
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 3
        #   Get the facility recommendation status for None recommendation factors
        # Result
        #   The recommendation status should be neutral

        firefly_recommendation_factors: FireflyRecommendationFactors = get_all_none_firefly_recommendation_factors()
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NEUTRAL,
        )

        # Case 4
        #   Get the facility recommendation status for bad cost but good expertise
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_best_firefly_recommendation_factors_for_facility()
        )
        firefly_recommendation_factors["cost_score_out_of_five"] = get_scaled_talon_cost_score(
            300, PricePercentiles(p_0=50, p_25=100, p_50=200, p_75=300)
        )
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 5
        #   Get the facility recommendation status for bad expertise but good cost
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_best_firefly_recommendation_factors_for_facility()
        )
        firefly_recommendation_factors["expertise_score_out_of_five"] = get_scaled_talon_expertise_score(0.1)
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.NOT_RECOMMENDED,
        )

        # Case 6
        #   Get the facility recommendation status when expertise_score is none
        # Result
        #   The recommendation status should be not recommended

        firefly_recommendation_factors: FireflyRecommendationFactors = (
            get_best_firefly_recommendation_factors_for_facility()
        )
        firefly_recommendation_factors["expertise_score_out_of_five"] = get_scaled_talon_expertise_score(None)
        self.assertEqual(
            get_facility_recommendation_status(
                get_facility_composite_score(firefly_recommendation_factors),
                firefly_recommendation_factors,
            ),
            RecommendationStatus.RECOMMENDED,
        )


class RecommendationReasonLabelTestCase(FireflyTestCase):
    def test_recommended_reason_label(self):
        # Case 1
        #   Get recommendation reason for best recommendation factor where the provider is downranked
        #   due to High Cost
        # Result
        #   The reason label should be High Cost
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        firefly_recommendation_factors["cost_score_out_of_five"] = (
            CompositeScoreConfig.HIGH_COST_CURATED_PROVIDER_COST_INDEX
        )
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
                composite_score=1.5,
            ),
            RecommendationReasonLabel.POOR + " " + RecommendationReasonLabel.COST,
        )

        # Case 2
        #   Get recommendation reason for best recommendation factor and provider not downranked
        # Result
        #   The reason label should be Great Cost, Quality, Ratings
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.RECOMMENDED,
                composite_score=5,
            ),
            RecommendationReasonLabel.GREAT
            + " "
            + RecommendationReasonLabel.COST
            + ", "
            + RecommendationReasonLabel.QUALITY
            + ", "
            + RecommendationReasonLabel.RATINGS,
        )

        # Case 3
        #   Get recommendation reason for average recommended provider
        # Result
        #   The reason label should be Good Cost, Quality, Ratings
        firefly_recommendation_factors: FireflyRecommendationFactors = get_best_firefly_recommendation_factors()
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.RECOMMENDED,
                composite_score=3.5,
            ),
            RecommendationReasonLabel.GOOD
            + " "
            + RecommendationReasonLabel.COST
            + ", "
            + RecommendationReasonLabel.QUALITY
            + ", "
            + RecommendationReasonLabel.RATINGS,
        )

        # Case 3
        #   Get recommendation reason for worst recommendation factor and provider not downranked
        #   and number of ratings greater then MIN_NUMBER_OF_USER_RATINGS
        # Result
        #   The reason label should be Poor Cost, Quality, Ratings
        firefly_recommendation_factors: FireflyRecommendationFactors = get_worst_firefly_recommendation_factors()
        firefly_recommendation_factors["number_of_ratings"] = 15
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
                composite_score=1.5,
            ),
            RecommendationReasonLabel.POOR
            + " "
            + RecommendationReasonLabel.COST
            + ", "
            + RecommendationReasonLabel.QUALITY
            + ", "
            + RecommendationReasonLabel.RATINGS,
        )

        # Case 4
        #   Get recommendation reason for None recommendation factor and provider not downranked
        # Result
        #   The reason label should be empty
        firefly_recommendation_factors: FireflyRecommendationFactors = get_all_none_firefly_recommendation_factors()
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.NEUTRAL,
                composite_score=2.75,
            ),
            "",
        )

        # Case 5
        #   Get recommendation reason for worst recommendation factor and provider not downranked
        #   Number of ranking are less then MIN_NUMBER_OF_USER_RATINGS
        # Result
        #   The reason label should Poor Cost, Quality
        firefly_recommendation_factors: FireflyRecommendationFactors = get_worst_firefly_recommendation_factors()
        self.assertEqual(
            get_recommendation_reason_labels(
                firefly_recommendation_factors=firefly_recommendation_factors,
                recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
                composite_score=1.5,
            ),
            RecommendationReasonLabel.POOR
            + " "
            + RecommendationReasonLabel.COST
            + ", "
            + RecommendationReasonLabel.QUALITY,
        )


@mock.patch("firefly.modules.referral.utils.provider_search_utils.get_provider_search_results")
class ProviderSearchResultSortingTestCase(FireflyTestCase):
    def test_get_sorted_list_based_on_recommendation(
        self,
        get_provider_search_results_mock,
    ):
        # Scenario
        #   Create three providers with status recommended, not recommended and netural with same disctance as 10Miles
        # Result
        #   Providers should be sorted as  recommended, netural and not recommended

        providers = []
        provider_location: RibbonLocation = RibbonLocationFactory.create()

        neutral_provider: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NEUTRAL,
            ratings_count=None,
            ratings_avg=None,
        )
        neutral_provider["performance"] = None
        providers.append(neutral_provider)

        recommended_provider: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.RECOMMENDED,
            ratings_count=50,
            ratings_avg=10,
        )
        recommended_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        recommended_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        providers.append(recommended_provider)

        not_recommended_provider: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
            ratings_count=50,
            ratings_avg=3,
        )
        not_recommended_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 10
        not_recommended_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 1
        providers.append(not_recommended_provider)

        # Mock the ribbon response with the above providers
        ribbon_response: RibbonProviderResponse = {
            "parameters": {"total_count": 2, "sort_by": "distance", "page": 1, "page_size": 1},
            "data": deepcopy(providers),
        }
        processed_results: ProcessedRibbonSearchResult = process_ribbon_results(
            vendor_results=ribbon_response,
            insurance_uids=[],
            member_service_level=MemberServiceLevel.CARE,
        )

        # Assert the order of the sorted providers
        self.assertEqual(processed_results["search_results"][0]["npi"], recommended_provider["npi"])
        self.assertEqual(processed_results["search_results"][1]["npi"], neutral_provider["npi"])
        self.assertEqual(processed_results["search_results"][2]["npi"], not_recommended_provider["npi"])

    def test_get_sorted_list_based_on_recommendation_and_distance(
        self,
        get_provider_search_results_mock,
    ):
        # Scenario
        #   Create 6 providers with status recommended, not recommended and netural with different disctance
        # Result
        #   Providers should be sorted as recommended, netural and not recommended and
        #   within the same status it should sort nearest first

        providers = []
        provider_location: RibbonLocation = RibbonLocationFactory.create()

        provider_location["distance"] = 5
        neutral_provider_5_mile: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NEUTRAL,
            ratings_count=None,
            ratings_avg=None,
        )
        neutral_provider_5_mile["performance"] = None
        providers.append(neutral_provider_5_mile)

        provider_location["distance"] = 10
        neutral_provider_10_mile: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NEUTRAL,
            ratings_count=None,
            ratings_avg=None,
        )
        neutral_provider_10_mile["performance"] = None
        providers.append(neutral_provider_10_mile)

        provider_location["distance"] = 5
        recommended_provider_5_mile: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.RECOMMENDED,
            ratings_count=50,
            ratings_avg=10,
        )
        recommended_provider_5_mile["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        recommended_provider_5_mile["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        providers.append(recommended_provider_5_mile)

        provider_location["distance"] = 10
        recommended_provider_10_mile: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.RECOMMENDED,
            ratings_count=50,
            ratings_avg=10,
        )
        recommended_provider_10_mile["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        recommended_provider_10_mile["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        providers.append(recommended_provider_10_mile)

        provider_location["distance"] = 5
        not_recommended_provider_5_miles: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
            ratings_count=50,
            ratings_avg=3,
        )
        not_recommended_provider_5_miles["performance"]["aggregate"]["cost"]["efficiency_index"] = 10
        not_recommended_provider_5_miles["performance"]["aggregate"]["quality"]["outcomes_index"] = 1
        providers.append(not_recommended_provider_5_miles)

        provider_location["distance"] = 10
        not_recommended_provider_10_miles: RibbonProvider = RibbonProviderFactory.create(
            locations=[provider_location],
            recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
            ratings_count=50,
            ratings_avg=3,
        )
        not_recommended_provider_10_miles["performance"]["aggregate"]["cost"]["efficiency_index"] = 10
        not_recommended_provider_10_miles["performance"]["aggregate"]["quality"]["outcomes_index"] = 1
        providers.append(not_recommended_provider_10_miles)

        # Mock the ribbon response with the above providers
        ribbon_response: RibbonProviderResponse = {
            "parameters": {"total_count": 2, "sort_by": "distance", "page": 1, "page_size": 1},
            "data": deepcopy(providers),
        }
        processed_results: ProcessedRibbonSearchResult = process_ribbon_results(
            vendor_results=ribbon_response,
            insurance_uids=[],
            member_service_level=MemberServiceLevel.CARE,
        )

        # Assert the order of the sorted providers
        self.assertEqual(processed_results["search_results"][0]["npi"], recommended_provider_5_mile["npi"])
        self.assertEqual(processed_results["search_results"][1]["npi"], recommended_provider_10_mile["npi"])
        self.assertEqual(processed_results["search_results"][2]["npi"], neutral_provider_5_mile["npi"])
        self.assertEqual(processed_results["search_results"][3]["npi"], neutral_provider_10_mile["npi"])
        self.assertEqual(processed_results["search_results"][4]["npi"], not_recommended_provider_5_miles["npi"])
        self.assertEqual(processed_results["search_results"][5]["npi"], not_recommended_provider_10_miles["npi"])


@mock.patch(
    "firefly.modules.network.utils.utils.get_redis_cache",
)
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
class CompositeScoreInReferralSearchTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        clear_cache_data()
        mock_redis_obj = get_mock_redis()
        # binding a side_effect of a MagicMock instance with redis methods
        mock_redis_method = MagicMock()
        mock_redis_method.get = Mock(side_effect=mock_redis_obj.get)
        mock_redis_method.set = Mock(side_effect=mock_redis_obj.set)
        mock_redis_method.flushall = Mock(side_effect=mock_redis_obj.flushall)
        self.mock_redis_method = mock_redis_method
        self.mock_redis_method.flushall()

        # Intentionally mock this as BCBS Blue based on real ribbon data.
        # This allows us to sanity check ribbon and our mocks against the real thing
        # by turning off the test flag temporarily
        self.payer = InsurancePayerFactory.create(name="BCBS MSA", payer_codes=["64222"])
        self.plan = InsurancePlanFactory.create(
            name="0750 - HMO BLUE NEW ENGLAND DEDUCTIBLE", insurance_payer=self.payer
        )
        self.network = NetworkFactory.create(name="Blue Cross Blue Shield of Massachusetts - Blue New England - HMO")
        self.plan.networks.add(self.network)
        self.plan.save()

        self.network_alias = AliasMapping.set_mapping_by_object(
            obj=self.network,
            alias_name=AliasName.RIBBON,
            alias_id="09acf90b-503d-4d78-ade2-da3b5dab12a2",
        )

        self.referral_patient = PersonUserFactory.create()
        self.referral_patient.insurance_info.insurance_payer = self.payer
        self.referral_patient.insurance_info.plan_description = self.plan.name
        self.referral_patient.insurance_info.insurance_plan = self.plan
        fake = Faker()
        self.zipcode = "%05d" % randint(1, 99999)
        self.street_address = fake.street_address()
        self.street_address_2 = fake.street_address()
        self.city = fake.city()
        self.address = (
            f"{self.street_address} "
            f"{self.street_address_2} "
            f"{self.city} "
            f"{self.referral_patient.insurance_info.state} "
            f"{self.zipcode} "
        )
        self.referral_patient.insurance_info.street_address = self.street_address
        self.referral_patient.insurance_info.street_address_2 = self.street_address_2
        self.referral_patient.insurance_info.city = self.city
        self.referral_patient.insurance_info.zipcode = self.zipcode
        self.referral_patient.insurance_info.save()
        self.referral_patient.insurance_info.refresh_from_db()

        self.min_location_confidence = 3
        self.page = 1
        self.page_size = 25
        self.distance = 10

        self.specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create()
        self.specialty: Specialty = SpecialtyFactory.create(specialty_groups=(self.specialty_group,))
        self.specialty.save()
        self.location_type: LocationType = LocationTypeFactory.create()

        self.specialty_alias = AliasMapping.set_mapping_by_object(
            obj=self.specialty,
            alias_name=AliasName.RIBBON,
            alias_id="1de33770-eb1c-47fa-ab3e-f9a4ab924d9d",
        )

    def get_search_attrs(self):
        return {
            "person": self.referral_patient.user.person.id,
            "min_location_confidence": self.min_location_confidence,
            "distance": self.distance,
            "page": self.page,
            "page_size": self.page_size,
        }

    def get_location_search_attrs(self):
        location_type_id: Union[int, None] = None
        if self.location_type is not None:
            location_type_id = self.location_type.id
        search_attrs = self.get_search_attrs()
        search_attrs["location_type"] = location_type_id
        return search_attrs

    def get_specialty_group_search_attrs(self):
        search_attrs = self.get_search_attrs()
        search_attrs["specialty_group"] = self.specialty_group.id
        return search_attrs

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_composite_score_in_specialty_search(
        self,
        ribbon_call,
        mutex_mock,
        get_redis_cache_mock,
    ):
        ribbon_specialty_search_results = {
            "parameters": {"total_count": 2},
            "data": [],
        }

        best_provider: RibbonProvider = RibbonProviderFactory.create(
            npi="**********",
            ratings_count=15,
            ratings_avg=5,
        )
        best_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        best_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        ribbon_specialty_search_results["data"].append(best_provider)

        worst_provider: RibbonProvider = RibbonProviderFactory.create(
            npi="**********",
            ratings_count=1,
            ratings_avg=1,
        )
        worst_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 10
        worst_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 1
        ribbon_specialty_search_results["data"].append(worst_provider)

        get_redis_cache_mock.return_value = self.mock_redis_method

        request_params = self.get_specialty_group_search_attrs()
        search_availability = SearchAvailabilityFactory.create()
        request_params["search_availability_id"] = search_availability.id

        ribbon_call.return_value = (deepcopy(ribbon_specialty_search_results), "200"), ""
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        results = response.json()["results"]

        # Assert that composite score and firefly score present (We have seprate test cases to match data)
        self.assertEqual(best_provider["npi"], results[0]["npi"])
        self.assertEqual(worst_provider["npi"], results[1]["npi"])

        self.assertIsNotNone(results[0]["composite_score"])
        self.assertIsNotNone(results[1]["composite_score"])
        self.assertIsNotNone(results[0]["firefly_recommendation_factors"])
        self.assertIsNotNone(results[1]["firefly_recommendation_factors"])

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_composite_score_in_specialty_search_with_multiple_location_downranking(
        self,
        ribbon_call,
        mutex_mock,
        get_redis_cache_mock,
    ):
        # Case
        #   Create one provider - with multiple locations
        # Result
        #  The firefly recommedation cost score should be set to 1 for the first provider location
        ribbon_specialty_search_results = {
            "parameters": {"total_count": 2},
            "data": [],
        }

        first_provider: RibbonProvider = RibbonProviderFactory.create(
            npi="**********",
            ratings_count=15,
            ratings_avg=5,
            locations=[RibbonLocationFactory.create(), RibbonLocationFactory.create()],
        )
        first_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        first_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        ribbon_specialty_search_results["data"].append(first_provider)

        # First create curaterd provider and partnership
        curated_provider: CuratedProvider = CuratedProviderFactory.create(
            npi=None,
            care_org_name=first_provider["locations"][0]["name"],
            zip_code=first_provider["locations"][0]["address_details"]["zip"],
        )

        RankingFactory(
            curated_provider=curated_provider,
            reason=CuratedProviderRankingReasonConfig.HIGH_COST,
            rank_change=-1,
            ranking_level=CuratedProviderRankingLevelConfig.CARE_ORG,
        )

        get_redis_cache_mock.return_value = self.mock_redis_method

        request_params = self.get_specialty_group_search_attrs()
        search_availability = SearchAvailabilityFactory.create()
        request_params["search_availability_id"] = search_availability.id

        ribbon_call.return_value = (deepcopy(ribbon_specialty_search_results), "200"), ""
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        results = response.json()["results"]

        # Assert that composite score and firefly score present (We have seprate test cases to match data)
        self.assertEqual(first_provider["npi"], results[0]["npi"])

        # The 0th location should downranked to first
        self.assertEqual(first_provider["locations"][0]["name"], results[1]["locations"][0]["name"])
        # The defalut downranked cost score should be set
        self.assertEqual(
            results[1]["firefly_recommendation_factors"]["cost_score_out_of_five"],
            CompositeScoreConfig.HIGH_COST_CURATED_PROVIDER_COST_INDEX,
        )


class TestGetScaledTalonExpertiseScore(FireflyTestCase):
    def test_none_input(self):
        self.assertIsNone(get_scaled_talon_expertise_score(None))

    def test_invalid_input_below_0(self):
        self.assertIsNone(get_scaled_talon_expertise_score(-0.1))

    def test_invalid_input_above_1(self):
        self.assertIsNone(get_scaled_talon_expertise_score(1.1))

    def test_valid_input_boundary_0(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.0), 1)

    def test_valid_input_boundary_1(self):
        self.assertEqual(get_scaled_talon_expertise_score(1.0), 5)

    def test_valid_input_within_range_1(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.25), 1)

    def test_valid_input_within_range_2(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.6), 2)

    def test_valid_input_within_range_3(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.75), 3)

    def test_valid_input_within_range_4(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.85), 4)

    def test_valid_input_within_range_5(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.95), 5)

    def test_valid_input_at_0_5(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.5), 2)

    def test_valid_input_at_0_7(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.7), 3)

    def test_valid_input_at_0_8(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.8), 4)

    def test_valid_input_at_0_9(self):
        self.assertEqual(get_scaled_talon_expertise_score(0.9), 5)
