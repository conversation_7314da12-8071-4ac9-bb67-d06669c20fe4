from typing import Optional

from faker.generator import random

from firefly.modules.network.factories import CuratedProviderFactory, PartnershipFactory, PartnershipRecordFactory
from firefly.modules.network.models import (
    CuratedProvider,
    CuratedProviderPartnership,
    CuratedProviderType,
    Partnership,
    PartnershipRecord,
    PartnershipTypeConfig,
)
from firefly.modules.network.tests.curated_provider_utils import BaseCuratedProvidersTestCase
from firefly.modules.network.utils.utils import process_linking_partnership_records_to_curated_provider


class TestLinkingProviderPartnership(BaseCuratedProvidersTestCase):
    def test_linking_provider_partnership(self):
        provider_partnership: PartnershipRecord = PartnershipRecordFactory(
            partnership_level=PartnershipTypeConfig.PROVIDER_CARE_ORG,
        )
        curated_provider: CuratedProvider = CuratedProviderFactory(
            npi=provider_partnership.npi,
            care_org_name=provider_partnership.care_org_name,
            zip_code=provider_partnership.zip_code,
        )

        partnership: Partnership = PartnershipFactory(
            partner_name=provider_partnership.partner_name,
            agreement_type=provider_partnership.agreement_type,
            can_accept_care_members=provider_partnership.can_accept_care_members,
            can_accept_coverage_members=provider_partnership.can_accept_coverage_members,
            can_accept_care_and_coverage_members=provider_partnership.can_accept_care_and_coverage_members,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=curated_provider,
            partnership=partnership,
            partnership_level=provider_partnership.partnership_level,
        )

        process_linking_partnership_records_to_curated_provider(
            partnership_record=provider_partnership,
            dry_run_off=True,
        )
        provider_partnership.refresh_from_db()
        self.assertEqual(provider_partnership.curated_provider, curated_provider)
        self.assertEqual(
            provider_partnership.curated_provider.curatedprovider_partnerships.first().partnership, partnership
        )

    def test_linking_provider_partnership_with_dry_run(self):
        partnership_record: PartnershipRecord = PartnershipRecordFactory(
            partnership_level=PartnershipTypeConfig.PROVIDER_CARE_ORG,
        )
        curated_provider: CuratedProvider = CuratedProviderFactory(
            npi=partnership_record.npi,
            care_org_name=partnership_record.care_org_name,
            zip_code=partnership_record.zip_code,
        )

        partnership: Partnership = PartnershipFactory(
            partner_name=partnership_record.partner_name,
            agreement_type=partnership_record.agreement_type,
            can_accept_care_members=partnership_record.can_accept_care_members,
            can_accept_coverage_members=partnership_record.can_accept_coverage_members,
            can_accept_care_and_coverage_members=partnership_record.can_accept_care_and_coverage_members,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=curated_provider,
            partnership=partnership,
            partnership_level=partnership_record.partnership_level,
        )
        process_linking_partnership_records_to_curated_provider(
            partnership_record=partnership_record,
            dry_run_off=False,
        )
        partnership_record.refresh_from_db()
        self.assertIsNone(partnership_record.curated_provider)

    def test_linking_facility_partnership(self):
        facility_partnership_record: PartnershipRecord = PartnershipRecordFactory(
            npi=None,
            partnership_level=PartnershipTypeConfig.CARE_ORG,
        )
        curated_provider: CuratedProvider = CuratedProviderFactory(
            npi=facility_partnership_record.npi,
            care_org_name=facility_partnership_record.care_org_name,
            zip_code=facility_partnership_record.zip_code,
        )

        partnership: Partnership = PartnershipFactory(
            partner_name=facility_partnership_record.partner_name,
            agreement_type=facility_partnership_record.agreement_type,
            can_accept_care_members=facility_partnership_record.can_accept_care_members,
            can_accept_coverage_members=facility_partnership_record.can_accept_coverage_members,
            can_accept_care_and_coverage_members=facility_partnership_record.can_accept_care_and_coverage_members,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=curated_provider,
            partnership=partnership,
            partnership_level=facility_partnership_record.partnership_level,
        )

        process_linking_partnership_records_to_curated_provider(
            partnership_record=facility_partnership_record,
            dry_run_off=True,
        )
        facility_partnership_record.refresh_from_db()
        self.assertEqual(facility_partnership_record.curated_provider, curated_provider)
        self.assertEqual(
            facility_partnership_record.curated_provider.curatedprovider_partnerships.first().partnership, partnership
        )

    def test_linking_multiple_facility_partnership(self):
        facility_partnership_record: PartnershipRecord = PartnershipRecordFactory(
            partnership_level=PartnershipTypeConfig.CARE_ORG,
        )
        curated_provider: CuratedProvider = CuratedProviderFactory(
            npi=None,
            care_org_name=facility_partnership_record.care_org_name,
            zip_code=facility_partnership_record.zip_code,
        )
        partnership: Partnership = PartnershipFactory(
            partner_name=facility_partnership_record.partner_name,
            agreement_type=facility_partnership_record.agreement_type,
            can_accept_care_members=facility_partnership_record.can_accept_care_members,
            can_accept_coverage_members=facility_partnership_record.can_accept_coverage_members,
            can_accept_care_and_coverage_members=facility_partnership_record.can_accept_care_and_coverage_members,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=curated_provider,
            partnership=partnership,
            partnership_level=facility_partnership_record.partnership_level,
        )

        another_facility_partnership: PartnershipRecord = PartnershipRecordFactory(
            partnership_level=PartnershipTypeConfig.CARE_ORG,
            care_org_name=facility_partnership_record.care_org_name,
            zip_code=facility_partnership_record.zip_code,
            partner_name=facility_partnership_record.partner_name,
            agreement_type=facility_partnership_record.agreement_type,
            can_accept_care_members=facility_partnership_record.can_accept_care_members,
            can_accept_coverage_members=facility_partnership_record.can_accept_coverage_members,
            can_accept_care_and_coverage_members=facility_partnership_record.can_accept_care_and_coverage_members,
        )
        for partnership in [facility_partnership_record, another_facility_partnership]:
            process_linking_partnership_records_to_curated_provider(
                partnership_record=partnership,
                dry_run_off=True,
            )
            partnership.refresh_from_db()
            self.assertEqual(partnership.curated_provider, curated_provider)
            self.assertIsNotNone(facility_partnership_record.curated_provider.curatedprovider_partnerships.first())

    def test_linking_provider_partnership_to_new_curated_provider(self):
        # Scenario 1
        #   Linking curated provider for a new partnership record
        # Result
        #   It should create a curated provider, curated provider partnership objects and
        #   curated provider should be linked with the PartnershipRecord

        partnership: Partnership = PartnershipFactory()
        partnership_record: PartnershipRecord = PartnershipRecordFactory(
            partnership_level=PartnershipTypeConfig.PROVIDER_CARE_ORG,
            partner_name=partnership.partner_name,
            agreement_type=partnership.agreement_type,
            can_accept_care_members=partnership.can_accept_care_members,
            can_accept_coverage_members=partnership.can_accept_coverage_members,
            can_accept_care_and_coverage_members=partnership.can_accept_care_and_coverage_members,
        )
        process_linking_partnership_records_to_curated_provider(
            partnership_record=partnership_record,
            dry_run_off=True,
        )
        partnership_record.refresh_from_db()
        self.assertIsNotNone(partnership_record.curated_provider)
        curated_provider: CuratedProvider = partnership_record.curated_provider
        curatedprovider_partnerships: Optional[CuratedProviderPartnership] = (
            curated_provider.curatedprovider_partnerships.first()
        )

        # Assert every data
        self.assertIsNotNone(curatedprovider_partnerships.partnership)
        self.assertEqual(curated_provider.npi, partnership_record.npi)
        self.assertEqual(curated_provider.care_org_name, partnership_record.care_org_name)
        self.assertEqual(curated_provider.zip_code, partnership_record.zip_code)
        self.assertEqual(curated_provider.city, partnership_record.city)
        self.assertEqual(curated_provider.address_line_1, partnership_record.address_line_1)
        self.assertEqual(curated_provider.address_line_2, partnership_record.address_line_2)
        self.assertEqual(curated_provider.state, partnership_record.state)

        self.assertEqual(curatedprovider_partnerships.partnership_level, partnership_record.partnership_level)

        if curatedprovider_partnerships is not None:
            self.assertEqual(curatedprovider_partnerships.partnership.agreement_type, partnership_record.agreement_type)
            self.assertEqual(curatedprovider_partnerships.partnership.partner_name, partnership_record.partner_name)
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_members,
                partnership_record.can_accept_care_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_coverage_members,
                partnership_record.can_accept_coverage_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_and_coverage_members,
                partnership_record.can_accept_care_and_coverage_members,
            )

        # Scenario 2
        #   Update the same partnership data and call the link_partnership_record_to_curated_provider
        # Result
        #   The data values should be updated in the Curated provider and partnership
        partnership_record.address_line_1 = "123 address"
        partnership_record.provider_type = CuratedProviderType.IN_HOME
        partnership_record.first_name = "first_name"
        partnership_record.last_name = "last_name"
        partnership_record.save()

        process_linking_partnership_records_to_curated_provider(
            partnership_record=partnership_record,
            dry_run_off=True,
        )
        partnership_record.refresh_from_db()
        self.assertIsNotNone(partnership_record.curated_provider)
        curated_provider: CuratedProvider = partnership_record.curated_provider
        curatedprovider_partnerships: Optional[CuratedProviderPartnership] = (
            curated_provider.curatedprovider_partnerships.first()
        )

        # Assert every data
        self.assertIsNotNone(curatedprovider_partnerships.partnership)
        self.assertEqual(curated_provider.npi, partnership_record.npi)
        self.assertEqual(curated_provider.provider_type, partnership_record.provider_type)
        self.assertEqual(curated_provider.first_name, partnership_record.first_name)
        self.assertEqual(curated_provider.last_name, partnership_record.last_name)
        self.assertEqual(curated_provider.care_org_name, partnership_record.care_org_name)
        self.assertEqual(curated_provider.zip_code, partnership_record.zip_code)
        self.assertEqual(curated_provider.city, partnership_record.city)
        self.assertEqual(curated_provider.address_line_1, partnership_record.address_line_1)
        self.assertEqual(curated_provider.address_line_2, partnership_record.address_line_2)
        self.assertEqual(curated_provider.state, partnership_record.state)
        self.assertEqual(curatedprovider_partnerships.partnership_level, partnership_record.partnership_level)

        if curatedprovider_partnerships is not None:
            self.assertEqual(curatedprovider_partnerships.partnership.agreement_type, partnership_record.agreement_type)
            self.assertEqual(curatedprovider_partnerships.partnership.partner_name, partnership_record.partner_name)
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_members,
                partnership_record.can_accept_care_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_coverage_members,
                partnership_record.can_accept_coverage_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_and_coverage_members,
                partnership_record.can_accept_care_and_coverage_members,
            )

        # Scenario 3
        #   Update the partnership level on partnership record
        # Result
        #   The partnership level should get updated on curated provider partnership level
        partnership_level = random.choice([PartnershipTypeConfig.CARE_ORG, PartnershipTypeConfig.PROVIDER_CARE_ORG])
        partnership_record.partnership_level = partnership_level
        if partnership_level == PartnershipTypeConfig.CARE_ORG:
            partnership_record.npi = None
        partnership_record.save(update_fields=["partnership_level", "npi"])

        process_linking_partnership_records_to_curated_provider(
            partnership_record=partnership_record,
            dry_run_off=True,
        )
        partnership_record.refresh_from_db()
        self.assertIsNotNone(partnership_record.curated_provider)
        curated_provider: CuratedProvider = partnership_record.curated_provider
        curatedprovider_partnerships: Optional[CuratedProviderPartnership] = (
            curated_provider.curatedprovider_partnerships.first()
        )

        # Assert every data
        self.assertIsNotNone(curatedprovider_partnerships.partnership)
        self.assertEqual(curated_provider.npi, partnership_record.npi)
        self.assertEqual(curated_provider.care_org_name, partnership_record.care_org_name)
        self.assertEqual(curated_provider.zip_code, partnership_record.zip_code)
        self.assertEqual(curated_provider.city, partnership_record.city)
        self.assertEqual(curated_provider.address_line_1, partnership_record.address_line_1)
        self.assertEqual(curated_provider.address_line_2, partnership_record.address_line_2)
        self.assertEqual(curated_provider.state, partnership_record.state)

        if curatedprovider_partnerships is not None:
            self.assertEqual(curatedprovider_partnerships.partnership_level, partnership_level)
            self.assertEqual(curatedprovider_partnerships.partnership.agreement_type, partnership_record.agreement_type)
            self.assertEqual(curatedprovider_partnerships.partnership.partner_name, partnership_record.partner_name)
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_members,
                partnership_record.can_accept_care_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_coverage_members,
                partnership_record.can_accept_coverage_members,
            )
            self.assertEqual(
                curatedprovider_partnerships.partnership.can_accept_care_and_coverage_members,
                partnership_record.can_accept_care_and_coverage_members,
            )

    def test_linking_provider_partnership_duplicate_providers(self):
        # Case 1
        #   Create Partnership record at care org level and call linking method
        # Result
        #   It should create the CuratedProvider and CuratedProviderPartnership

        provider_partnership: PartnershipRecord = PartnershipRecordFactory(
            npi=None,
            partnership_level=PartnershipTypeConfig.CARE_ORG,
        )
        PartnershipFactory(
            partner_name=provider_partnership.partner_name,
            agreement_type=provider_partnership.agreement_type,
            can_accept_care_members=provider_partnership.can_accept_care_members,
            can_accept_coverage_members=provider_partnership.can_accept_coverage_members,
            can_accept_care_and_coverage_members=provider_partnership.can_accept_care_and_coverage_members,
        )

        self.assertEqual(CuratedProvider.objects.count(), 0)
        self.assertEqual(CuratedProviderPartnership.objects.count(), 0)
        self.assertEqual(PartnershipRecord.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.filter(curated_provider__isnull=True).count(), 1)

        process_linking_partnership_records_to_curated_provider(
            partnership_record=provider_partnership,
            dry_run_off=True,
        )

        self.assertEqual(CuratedProvider.objects.count(), 1)
        self.assertEqual(CuratedProvider.objects.filter(npi__isnull=True).count(), 1)
        self.assertEqual(CuratedProviderPartnership.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.filter(curated_provider__isnull=False).count(), 1)

        # Case 2
        #   Use same partnership record and update NPI as Blank("") - call the linking
        # Result
        #   It should not create another CuratedProvider and CuratedProviderPartnership
        provider_partnership.npi = ""
        provider_partnership.save()

        # Assert nothing changes before and after
        self.assertEqual(CuratedProvider.objects.count(), 1)
        self.assertEqual(CuratedProvider.objects.filter(npi__isnull=True).count(), 1)
        self.assertEqual(CuratedProviderPartnership.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.filter(curated_provider__isnull=False).count(), 1)

        process_linking_partnership_records_to_curated_provider(
            partnership_record=provider_partnership,
            dry_run_off=True,
        )

        self.assertEqual(CuratedProvider.objects.count(), 1)
        self.assertEqual(CuratedProvider.objects.filter(npi__isnull=True).count(), 1)
        self.assertEqual(CuratedProviderPartnership.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.count(), 1)
        self.assertEqual(PartnershipRecord.objects.filter(curated_provider__isnull=False).count(), 1)
