import csv
import datetime
import json
import logging
import operator
from functools import reduce
from threading import Lock
from typing import Dict, List, Optional, Set, cast

import boto3
from callee import Iterable
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.geos import Point
from django.contrib.gis.measure import Distance as DistanceMeasure
from django.db.models import Prefetch, Q, QuerySet
from rest_framework import status
from typing_extensions import TypedDict

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.services.aws_s3.utils import get_csv_lines
from firefly.core.services.dramatiq.utils import get_rate_limiter
from firefly.core.services.ribbon.client import (
    get_location,
    update_location_attributes,
)
from firefly.core.services.ribbon.types import (
    LocationAvailability,
    RibbonLocation,
    RibbonLocationResponse,
    RibbonProvider,
)
from firefly.modules.code_systems.models import CPTCode
from firefly.modules.facts.models import (
    ClinicalFocusA<PERSON>,
    LocationType,
    Specialty,
    ZipCodeCoordinate,
)
from firefly.modules.firefly_django.cache import get_redis_cache
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.insurance.models import Contract, InsurancePayer, InsurancePlan, Network
from firefly.modules.mailers.utils import zip_header_row
from firefly.modules.network.constants import (
    CACHE_FUNCTION_CONCURRENCY_LIMIT,
    DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
    LOCATION_TYPE_CACHE_MUTEX_NAME,
    NETWORK_ALIAS_ID_CACHE_MUTEX_NAME,
    NETWORK_CACHE_MUTEX_NAME,
    PROCEDURE_CACHE_MUTEX_NAME,
    SPECIALTY_CACHE_MUTEX_NAME,
    SPECIALTY_TO_ALIAS_ID_CACHE_MUTEX_NAME,
)
from firefly.modules.network.models import (
    AgreementTypeConfig,
    Availability,
    CuratedProvider,
    CuratedProviderPartnership,
    CuratedProviderType,
    FireflyNearbyCoverage,
    Partnership,
    PartnershipContract,
    PartnershipInsurancePayer,
    PartnershipRecord,
    PartnershipServicingStateExclusion,
    PartnershipType,
    PartnershipTypeConfig,
    ProviderRecommendationAnalysis,
    Ranking,
)
from firefly.modules.programs.benefit.constants import FIREFLY_PLAN
from firefly.modules.referral.constants import (
    MemberServiceLevel,
    ProviderSearchConfig,
    SystemSuggestedProviderConfig,
)
from firefly.modules.states.models import State

logger = logging.getLogger(__name__)

ALIAS_TO_SPECIALTY_MAP: Dict[str, Specialty] = {}
SPECIALTY_ID_TO_ALIAS_MAP: Dict[str, str] = {}
LOCATION_TYPE_LABEL_TO_LOCATION_MAP: Dict[str, LocationType] = {}
ALIAS_TO_PROCEDURE_CODE_MAP: Dict[str, List[CPTCode]] = {}
ALIAS_TO_INSURANCE_NETWORK_MAP: Dict[str, Network] = {}
INSURANCE_NETWORK_TO_ALIAS_MAP: Dict[str, str] = {}

IS_ALIAS_TO_SPECIALTY_MAP_POPULATED: bool = False
IS_LOCATION_TYPE_LABEL_TO_LOCATION_MAP_POPULATED: bool = False
IS_ALIAS_TO_PROCEDURE_CODE_MAP_POPULATED: bool = False
IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED: bool = False
IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED: bool = False
IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED: bool = False


class ProximityFilter(TypedDict):
    # Filter providers available within a certain distance
    # from a given location
    radius_in_miles: float
    # Longitude of the address that we need nearby providers for
    address_longitude: float
    # Latitude of the address that we need nearby providers for
    address_latitude: float


class PractitionerMetadata(TypedDict):
    all_provider_details: List[RibbonProvider]
    unique_care_orgs: Set[int]
    unique_locations: Set[int]
    departments: Dict[str, Optional[str]]


class RibbonDataEditRow(TypedDict):
    uuid: Optional[str]
    name: Optional[str]
    updated_name: Optional[str]


class SpecialtyGroupMappingRow(TypedDict):
    specialty_group_label: Optional[str]
    specialty_label: Optional[str]


class CuratedProviderPartnershipRow(TypedDict):
    partner_name: Optional[str]
    partnership_level: Optional[str]
    npi: Optional[str]
    care_org_name: Optional[str]
    provider_first_name: Optional[str]
    provider_last_name: Optional[str]
    address_line_1: Optional[str]
    address_line_2: Optional[str]
    city: Optional[str]
    state: Optional[str]
    zipcode: Optional[str]
    provider_type: str


class FFNBProspectListRow(TypedDict):
    zip_code: str
    member_count: int


PROVIDER_TYPE_MAPPING = {
    "in_person": "In Person",
    "in_home": "In Home",
    "virtual": "Virtual",
    "at_home_test_kit": "At Home Test Kit",
}


class PartnershipWithServicesRow(TypedDict):
    partner_name: Optional[str]
    agreement_type: str
    can_accept_care_members: str
    can_accept_coverage_members: str
    can_accept_care_and_coverage_members: str
    services: Optional[str]
    clinical_focus_areas: Optional[str]
    is_valid_for_all_zipcodes: str
    servicing_state_exclusions: Optional[str]
    partnership_type: Optional[str]
    is_active: str


class PartnershipInsurancePayerRow(TypedDict):
    partner_name: Optional[str]
    insurance_payer: str
    insurance_type: str
    states: str
    group_number: Optional[str]


class CuratedProviderRankingRow(TypedDict):
    npi: Optional[str]
    care_org_name: Optional[str]
    address_line_1: Optional[str]
    address_line_2: Optional[str]
    city: Optional[str]
    state: Optional[str]
    zipcode: Optional[str]
    ranking_level: Optional[str]
    reason: Optional[str]


class ServicesSpecialtyLocationTypeRow(TypedDict):
    service_category_ordering: Optional[str]
    service_category: Optional[str]
    services: str
    sanitized_services: str
    specialty_group: Optional[str]
    location_type: Optional[str]


class ClinicalFocusAreaSpecialtyRow(TypedDict):
    service_category_ordering: Optional[str]
    service_category: Optional[str]
    specialty_group: Optional[str]
    clinical_focus_area: Optional[str]
    sanitized_clinical_focus_area: Optional[str]


class ServiceCPTCodeRow(TypedDict):
    service_category_ordering: Optional[str]
    service_category: Optional[str]
    service: str
    cpt_code: str
    sanitized_service: str


class PartnershipContractRow(TypedDict):
    partner_name: Optional[str]
    contract: Optional[str]
    is_primary_subscriber_only: str


class IngestCuratedProviderLatitudeLongitudeRow(TypedDict):
    npi: Optional[str]
    care_org_name: Optional[str]
    address_line_1: Optional[str]
    zip_code: Optional[str]
    latitude: Optional[str]
    longitude: Optional[str]


class ProviderRecommendationAnalysisRow(TypedDict):
    npi: str
    zip_code: str


def get_hash_key_for_object_by_unique_fields(instance: BaseModelV3, unique_fields: List[str]) -> str:
    """
    Creates a hash key for an object which is just a concatenation of its unique fields
    This is useful to uniquely identify an object before it has been stored in the database.
    (Objects that are stored in the database - can be uniquely identified by their pk field)
    """
    hash_key: str = ""
    for field in unique_fields:
        # Default for primitive types
        field_value = getattr(instance, field, None)
        # if the field represents another model: use the primary key of that object instead
        if isinstance(field_value, BaseModelV3) and hasattr(field_value, "pk"):
            field_value = field_value.pk
        hash_key = f"{hash_key}_{field_value}"
    return hash_key


def get_partner_service_level_agreement_filter(
    member_service_level: MemberServiceLevel,
) -> Q:
    member_service_level_agreement_filter: Q
    # The member_service_level would either be care, coverage or care+coverage
    if member_service_level == MemberServiceLevel.CARE:
        member_service_level_agreement_filter = Q(partnership__can_accept_care_members=True)
    if member_service_level == MemberServiceLevel.COVERAGE:
        member_service_level_agreement_filter = Q(partnership__can_accept_coverage_members=True)
    if member_service_level == MemberServiceLevel.CARE_AND_COVERAGE:
        member_service_level_agreement_filter = Q(partnership__can_accept_care_and_coverage_members=True)

    return member_service_level_agreement_filter


def clear_cache_data() -> None:
    global ALIAS_TO_SPECIALTY_MAP
    global LOCATION_TYPE_LABEL_TO_LOCATION_MAP
    global ALIAS_TO_PROCEDURE_CODE_MAP
    global ALIAS_TO_INSURANCE_NETWORK_MAP
    global INSURANCE_NETWORK_TO_ALIAS_MAP
    global IS_ALIAS_TO_SPECIALTY_MAP_POPULATED
    global IS_LOCATION_TYPE_LABEL_TO_LOCATION_MAP_POPULATED
    global IS_ALIAS_TO_PROCEDURE_CODE_MAP_POPULATED
    global IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED
    global IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED
    global IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED

    ALIAS_TO_SPECIALTY_MAP = {}
    LOCATION_TYPE_LABEL_TO_LOCATION_MAP = {}
    ALIAS_TO_PROCEDURE_CODE_MAP = {}
    ALIAS_TO_INSURANCE_NETWORK_MAP = {}
    INSURANCE_NETWORK_TO_ALIAS_MAP = {}
    IS_ALIAS_TO_SPECIALTY_MAP_POPULATED = False
    IS_LOCATION_TYPE_LABEL_TO_LOCATION_MAP_POPULATED = False
    IS_ALIAS_TO_PROCEDURE_CODE_MAP_POPULATED = False
    IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED = False
    IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED = False
    IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED = False


def populate_alias_to_specialty_map_in_redis():
    """
    Populates the alias to specialty map in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global ALIAS_TO_SPECIALTY_MAP

    redis_cache = get_redis_cache()
    with get_rate_limiter(
        key=SPECIALTY_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        logger.info("Setting up specialty cache in redis")
        ALIAS_TO_SPECIALTY_MAP = {}
        alias_mapping_objs_for_specialties = AliasMapping.objects.filter(
            alias_name=AliasName.RIBBON,
            content_type=ContentType.objects.get_for_model(Specialty),
        )
        alias_mapping_obj: AliasMapping
        for alias_mapping_obj in alias_mapping_objs_for_specialties.iterator():
            if alias_mapping_obj.content_object is not None and isinstance(alias_mapping_obj.content_object, Specialty):
                ALIAS_TO_SPECIALTY_MAP[alias_mapping_obj.alias_id] = cast(Specialty, alias_mapping_obj.content_object)
        redis_cache.set(
            key=SPECIALTY_CACHE_MUTEX_NAME,
            value=ALIAS_TO_SPECIALTY_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def get_cached_specialty_from_alias_id(specialty_alias_id: str) -> Optional[Specialty]:
    # On the first invocation - pulls the cache from redis and populates in local
    # memcache of the instance. Subsequent reads occur from the local memcache
    global ALIAS_TO_SPECIALTY_MAP
    global IS_ALIAS_TO_SPECIALTY_MAP_POPULATED

    redis_cache = get_redis_cache()
    # if the local memcache is not populated - this is the first invocation
    if IS_ALIAS_TO_SPECIALTY_MAP_POPULATED is False:
        # This function is invoked by multiple threads. Limit concurrency to 1 by acquiring a mutex
        with Lock():
            # If the local memcache is not populated - even by the time the current
            # thread acquires the lock - pull from redis
            # The subsequent check is required since multiple threads might be stuck on the
            # lock acquisition step.
            if IS_ALIAS_TO_SPECIALTY_MAP_POPULATED is False:
                logger.info("Attempting to set up alias -> specialty map from redis")
                ALIAS_TO_SPECIALTY_MAP = redis_cache.get(
                    key=SPECIALTY_CACHE_MUTEX_NAME,
                    default=None,
                )
                if ALIAS_TO_SPECIALTY_MAP is None:
                    logger.error("Cache not setup in worker nodes")
                    populate_alias_to_specialty_map_in_redis()
                IS_ALIAS_TO_SPECIALTY_MAP_POPULATED = True

    return ALIAS_TO_SPECIALTY_MAP.get(specialty_alias_id)


def populate_location_type_label_to_location_type_cache_in_redis():
    """
    Populates the location type label to location type map in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global LOCATION_TYPE_LABEL_TO_LOCATION_MAP
    redis_cache = get_redis_cache()
    with get_rate_limiter(
        key=LOCATION_TYPE_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        logger.info("Setting up location type cache")
        LOCATION_TYPE_LABEL_TO_LOCATION_MAP = {}
        location_types = LocationType.objects.all()
        location_type: LocationType
        for location_type in location_types.iterator():
            LOCATION_TYPE_LABEL_TO_LOCATION_MAP[location_type.label] = location_type
        redis_cache.set(
            key=LOCATION_TYPE_CACHE_MUTEX_NAME,
            value=LOCATION_TYPE_LABEL_TO_LOCATION_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def populate_ribbon_alias_to_cpt_code_cache_in_redis():
    """
    Populates the map from ribbon alias id to cpt code objects in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global ALIAS_TO_PROCEDURE_CODE_MAP
    redis_cache = get_redis_cache()
    with get_rate_limiter(
        key=PROCEDURE_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        ALIAS_TO_PROCEDURE_CODE_MAP = {}
        logger.info("Setting up procedure code cache")
        alias_mapping_objs_for_procedure_codes = AliasMapping.objects.filter(
            alias_name=AliasName.RIBBON_PROCEDURE,
            content_type=ContentType.objects.get_for_model(CPTCode),
        )
        alias_mapping_obj: AliasMapping
        for alias_mapping_obj in alias_mapping_objs_for_procedure_codes.iterator():
            if ALIAS_TO_PROCEDURE_CODE_MAP.get(alias_mapping_obj.alias_id, None) is None:
                ALIAS_TO_PROCEDURE_CODE_MAP[alias_mapping_obj.alias_id] = []
            if alias_mapping_obj.content_object is not None and isinstance(alias_mapping_obj.content_object, CPTCode):
                ALIAS_TO_PROCEDURE_CODE_MAP[alias_mapping_obj.alias_id].append(
                    cast(CPTCode, alias_mapping_obj.content_object)
                )
        redis_cache.set(
            key=PROCEDURE_CACHE_MUTEX_NAME,
            value=ALIAS_TO_PROCEDURE_CODE_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def populate_ribbon_alias_to_insurance_network_cache_in_redis():
    """
    Populates the map from ribbon network alias id to insurance network objects in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global ALIAS_TO_INSURANCE_NETWORK_MAP
    redis_cache = get_redis_cache()

    with get_rate_limiter(
        key=NETWORK_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        logger.info("Attempting to set up alias -> network map from redis")
        ALIAS_TO_INSURANCE_NETWORK_MAP = {}
        alias_mapping_objs_for_networks = AliasMapping.objects.filter(
            alias_name=AliasName.RIBBON,
            content_type=ContentType.objects.get_for_model(Network),
        )
        alias_mapping_obj: AliasMapping
        for alias_mapping_obj in alias_mapping_objs_for_networks.iterator():
            if alias_mapping_obj.content_object is not None and isinstance(alias_mapping_obj.content_object, Network):
                ALIAS_TO_INSURANCE_NETWORK_MAP[alias_mapping_obj.alias_id] = cast(
                    Network, alias_mapping_obj.content_object
                )
        redis_cache.set(
            key=NETWORK_CACHE_MUTEX_NAME,
            value=ALIAS_TO_INSURANCE_NETWORK_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def get_cached_insurance_network_from_alias_id(ribbon_network_uuid: str) -> Optional[Network]:
    # On the first invocation - pulls the cache from redis and populates in local
    # memcache of the instance. Subsequent reads occur from the local memcache
    # This approach is more efficient than using an LRU cache since it eliminates the
    # possibility of cache misses
    # Firefly cares about a finite list of insurance networks. When Ribbon presents data
    # about an insurance network - we look up to see if it is in the list of networks
    # we care about. A LRU based cache would hit the database everytime we see a new network.
    # A dictionary based cache - only sets up the cache with insurance networks we know about.
    global ALIAS_TO_INSURANCE_NETWORK_MAP
    global IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED
    redis_cache = get_redis_cache()

    # if the local memcache is not populated - this is the first invocation
    if IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED is False:
        # This function is invoked by multiple threads. Limit concurrency to 1 by acquiring a mutex
        with Lock():
            # If the local memcache is not populated - even by the time the current
            # thread acquires the lock - pull from redis
            # The subsequent check is required since multiple threads might be stuck on the
            # lock acquisition step.
            if IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED is False:
                logger.info("Attempting to set up alias -> network map from redis")
                ALIAS_TO_INSURANCE_NETWORK_MAP = redis_cache.get(
                    key=NETWORK_CACHE_MUTEX_NAME,
                    default=None,
                )
                if ALIAS_TO_INSURANCE_NETWORK_MAP is None:
                    logger.error("Cache not setup in worker nodes")
                    populate_ribbon_alias_to_insurance_network_cache_in_redis()
                IS_ALIAS_TO_INSURANCE_NETWORK_MAP_POPULATED = True

    return ALIAS_TO_INSURANCE_NETWORK_MAP.get(ribbon_network_uuid)


def populate_insurance_network_cache_to_ribbon_alias_in_redis():
    """
    Populates the map from Network object -> Ribbon insurance network alias mappings in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global INSURANCE_NETWORK_TO_ALIAS_MAP
    global ALIAS_TO_INSURANCE_NETWORK_MAP
    redis_cache = get_redis_cache()

    with get_rate_limiter(
        key=NETWORK_ALIAS_ID_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        logger.info("Setting up network -> alias cache")
        INSURANCE_NETWORK_TO_ALIAS_MAP = {}
        # Set up the network-alias cache and reverse it to populate the alias-network cache
        get_cached_insurance_network_from_alias_id(ribbon_network_uuid="non-existent")
        for (
            ribbon_network_alias_in_cache,
            network_in_cache,
        ) in ALIAS_TO_INSURANCE_NETWORK_MAP.items():
            INSURANCE_NETWORK_TO_ALIAS_MAP[str(network_in_cache.pk)] = ribbon_network_alias_in_cache
        redis_cache.set(
            key=NETWORK_ALIAS_ID_CACHE_MUTEX_NAME,
            value=INSURANCE_NETWORK_TO_ALIAS_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def fetch_ribbon_insurance_network_ids_for_insurance_plan(insurance_plan: InsurancePlan) -> Optional[List[str]]:
    networks = list(insurance_plan.networks.all())

    if not networks or len(networks) < 1:
        return []

    network_uids = []

    for network in networks:
        network_uid = get_cached_alias_id_from_insurance_network(network=network)
        if not network_uid:
            return []
        network_uids.append(network_uid)

    return network_uids


def get_cached_alias_id_from_insurance_network(network: Network) -> Optional[str]:
    # On the first invocation - pulls the cache from redis and populates in local
    # memcache of the instance. Subsequent reads occur from the local memcache
    global INSURANCE_NETWORK_TO_ALIAS_MAP
    global IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED
    redis_cache = get_redis_cache()

    # if the local memcache is not populated - this is the first invocation
    if IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED is False:
        # This function is invoked by multiple threads. Limit concurrency to 1 by acquiring a mutex
        with Lock():
            # If the local memcache is not populated - even by the time the current
            # thread acquires the lock - pull from redis
            # The subsequent check is required since multiple threads might be stuck on the
            # lock acquisition step.
            logger.info("Attempting to set up network -> alias map from redis")
            if IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED is False:
                INSURANCE_NETWORK_TO_ALIAS_MAP = redis_cache.get(
                    key=NETWORK_ALIAS_ID_CACHE_MUTEX_NAME,
                    default=None,
                )
                if INSURANCE_NETWORK_TO_ALIAS_MAP is None:
                    logger.error("Cache not setup in worker nodes")
                    populate_insurance_network_cache_to_ribbon_alias_in_redis()
        IS_INSURANCE_NETWORK_TO_ALIAS_MAP_POPULATED = True

    return INSURANCE_NETWORK_TO_ALIAS_MAP.get(str(network.pk))


def populate_specialty_cache_to_ribbon_alias_in_redis():
    """
    Populates the map from Specialty id -> Ribbon specialty alias mappings in the redis cache
    Is run in cron and uses a mutex to prevent concurrent writes
    """
    global ALIAS_TO_SPECIALTY_MAP
    global SPECIALTY_ID_TO_ALIAS_MAP
    redis_cache = get_redis_cache()

    with get_rate_limiter(
        key=SPECIALTY_TO_ALIAS_ID_CACHE_MUTEX_NAME,
        limit=CACHE_FUNCTION_CONCURRENCY_LIMIT,
    ).acquire():
        logger.info("Setting up specialty -> alias cache")
        SPECIALTY_ID_TO_ALIAS_MAP = {}
        # Set up the aliasid->specialty cache and reverse it to populate the specialty->alias cache
        get_cached_specialty_from_alias_id(specialty_alias_id="non-existent")
        for (
            ribbon_alias_in_cache,
            specialty_in_cache,
        ) in ALIAS_TO_SPECIALTY_MAP.items():
            SPECIALTY_ID_TO_ALIAS_MAP[str(specialty_in_cache.pk)] = ribbon_alias_in_cache
        redis_cache.set(
            key=SPECIALTY_TO_ALIAS_ID_CACHE_MUTEX_NAME,
            value=SPECIALTY_ID_TO_ALIAS_MAP,
            timeout=DEFAULT_DURATION_FOR_CACHED_NETWORK_DATA_IN_SECONDS,
        )


def get_cached_alias_id_from_specialty(specialty: Specialty) -> Optional[str]:
    # On the first invocation - pulls the cache from redis and populates in local
    # memcache of the instance. Subsequent reads occur from the local memcache
    global SPECIALTY_ID_TO_ALIAS_MAP
    global IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED
    redis_cache = get_redis_cache()

    # if the local memcache is not populated - this is the first invocation
    if IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED is False:
        # This function is invoked by multiple threads. Limit concurrency to 1 by acquiring a mutex
        with Lock():
            # If the local memcache is not populated - even by the time the current
            # thread acquires the lock - pull from redis
            # The subsequent check is required since multiple threads might be stuck on the
            # lock acquisition step.
            if IS_ALIAS_TO_SPECIALTY_MAP_POPULATED is False:
                logger.info("Attempting to set up specialty -> alias map from redis")
                SPECIALTY_ID_TO_ALIAS_MAP = redis_cache.get(
                    key=SPECIALTY_TO_ALIAS_ID_CACHE_MUTEX_NAME,
                    default=None,
                )
                if SPECIALTY_ID_TO_ALIAS_MAP is None:
                    logger.error("Cache not setup in worker nodes")
                    populate_specialty_cache_to_ribbon_alias_in_redis()
        IS_SPECIALTY_ID_TO_ALIAS_MAP_POPULATED = True

    return SPECIALTY_ID_TO_ALIAS_MAP.get(str(specialty.pk))


def fetch_curated_providers_for_steerage_provider(
    npi: Optional[str], care_org_name: Optional[str], zip_code: Optional[str], specialty_group_id: Optional[int]
) -> Optional[LocationAvailability]:
    if not zip_code or (not npi and not care_org_name):
        return None

    filter_clauses = []
    # Add filter that availability is present
    filter_clauses.append(
        (Q(number_of_days_till_next_availability__isnull=False) | Q(does_provider_exist__isnull=False))
    )

    # Add curated provider filter for zipcode
    filter_clauses.append((Q(curated_provider__zip_code=zip_code)))

    # If NPI is present then Match NPI
    if npi:
        filter_clauses.append((Q(curated_provider__npi=npi)))

    # If care_org name is present then match care_org name
    if care_org_name:
        filter_clauses.append((Q(curated_provider__care_org_name=care_org_name)))

    availabilities: QuerySet[Availability] = Availability.objects.filter(reduce(operator.and_, filter_clauses))

    steerage_provider_availability: Optional[Availability] = None

    if not availabilities:
        return None

    for availability in availabilities:
        # This will handle the match for provider level availability
        # If provider level availability is present then prefer it over other
        if npi and not availability.specialty_group:
            steerage_provider_availability = availability
            break
        # This will handle the match for department level availability
        if npi and availability.specialty_group_id == specialty_group_id:
            steerage_provider_availability = availability
        # This will handle the match for availability at care_org level
        if not npi and not availability.specialty_group:
            steerage_provider_availability = availability

    if steerage_provider_availability:
        return {
            "added_on": steerage_provider_availability.added_at.strftime("%Y-%m-%d")
            if steerage_provider_availability.added_at
            else None,
            "days_till_next_availability": steerage_provider_availability.number_of_days_till_next_availability,
            "specialty_group": specialty_group_id,
            "does_provider_exist": steerage_provider_availability.does_provider_exist,
        }

    return None


def process_linking_partnership_records_to_curated_provider(
    partnership_record: PartnershipRecord,
    dry_run_off: bool,
):
    log_prefix: str = (
        f"process_linking_partnership_records_to_curated_provider: PartnershipRecord {partnership_record.pk}"
    )
    npi_for_search: Optional[str] = None
    if partnership_record.partnership_level == PartnershipTypeConfig.PROVIDER_CARE_ORG:
        npi_for_search = str(partnership_record.npi)
    existing_curated_providers_queryset: QuerySet[CuratedProvider] = CuratedProvider.objects.filter(
        care_org_name=partnership_record.care_org_name,
        zip_code=partnership_record.zip_code,
    ).order_by(
        "-created_at",
        "-pk",
    )
    if npi_for_search is not None and len(npi_for_search.strip()) > 0:
        npi_for_search = npi_for_search.strip()
        existing_curated_providers_queryset = existing_curated_providers_queryset.filter(
            npi=npi_for_search,
        )
    else:
        npi_for_search = None
        existing_curated_providers_queryset = existing_curated_providers_queryset.filter(
            (Q(npi__exact="") | Q(npi__isnull=True))
        )

    curated_provider: Optional[CuratedProvider] = existing_curated_providers_queryset.first()

    logger.info("%s: Updating or creating curated providers", log_prefix)
    if dry_run_off:
        curated_provider, _ = CuratedProvider.objects.update_or_create(
            npi=npi_for_search if npi_for_search else None,
            care_org_name=partnership_record.care_org_name,
            zip_code=partnership_record.zip_code,
            defaults={
                "address_line_1": partnership_record.address_line_1,
                "address_line_2": partnership_record.address_line_2,
                "city": partnership_record.city,
                "state": partnership_record.state,
                "provider_type": partnership_record.provider_type,
                "first_name": partnership_record.first_name,
                "last_name": partnership_record.last_name,
            },
        )

    if curated_provider:
        if dry_run_off:
            logger.info("%s: Creating or Updating partnerships for curated provider", log_prefix)
            partnership_record.curated_provider = curated_provider
            partnership_record.save(update_fields=["curated_provider"])

            partnership = Partnership.objects.filter(partner_name=partnership_record.partner_name).first()

            if partnership:
                # Link curated provider and partnerships
                CuratedProviderPartnership.objects.update_or_create(
                    partnership=partnership,
                    curated_provider=curated_provider,
                    defaults={"partnership_level": partnership_record.partnership_level},
                )
            else:
                logger.error(
                    "%s: Failed Mapping partnership, partnership not found for the partner name %s",
                    log_prefix,
                    partnership_record.partner_name,
                )
    else:
        logger.error("%s: Cannot create curated partnership mapping, curated provider not found", log_prefix)


def delete_obsolete_records(
    active_import_job_name: str,
    Model,
):
    log_prefix: str = "delete_obsolete_records"
    obsolete_rows_queryset = Model.objects.exclude(
        import_job_name=active_import_job_name,
    )
    obsolete_rows: List[int] = obsolete_rows_queryset.values_list("id", flat=True)
    if len(obsolete_rows) > 0:
        logger.info("%s: Deleting redundant row ids: %s", log_prefix, ",".join(map(str, obsolete_rows)))
        obsolete_rows_queryset.delete()


def update_ribbon_location_data_entries(
    s3_file_name: str,
    log_prefix: str = "update_ribbon_location_data_entries",
    dry_run_off: bool = True,
):
    logger.info(
        "%s: Reading csv %s",
        log_prefix,
        s3_file_name,
    )
    row_number: int = 1
    csv_reader = csv.reader(get_csv_lines(key=s3_file_name))
    header_row: List[str] = []
    for row in csv_reader:
        try:
            logger.info("%s: Processing row %d", log_prefix, row_number)
            if row_number == 1:
                header_row = row
            else:
                row_dict: RibbonDataEditRow = zip_header_row([header.lower() for header in header_row], row)
                logger.info("%s: Processing row: %s", log_prefix, row_dict)
                expected_location_name: Optional[str] = row_dict.get("name")
                location_uuid: Optional[str] = row_dict.get("uuid")
                updated_location_name: Optional[str] = row_dict.get("updated_name")
                if expected_location_name and location_uuid and updated_location_name:
                    update_ribbon_location_entry(
                        dry_run_off=dry_run_off,
                        expected_location_name=expected_location_name,
                        location_uuid=location_uuid,
                        updated_location_name=updated_location_name,
                    )
        except Exception:
            logger.exception("%s: Failed to process %s", log_prefix, row_dict)
        row_number = row_number + 1


def update_ribbon_location_entry(
    location_uuid: str,
    expected_location_name: str,
    updated_location_name: str,
    log_prefix: str = "update_ribbon_location_data_entry",
    dry_run_off: bool = True,
):
    result: RibbonLocationResponse
    response_status: int
    result, response_status = get_location(
        location_uuid=location_uuid,
    )
    assert response_status == status.HTTP_200_OK
    location_data: Optional[RibbonLocation] = result["data"][0]
    if location_data is not None:
        location_name_from_ribbon: Optional[str] = location_data["name"]
        if location_name_from_ribbon == updated_location_name:
            logger.info(
                "%s: Location: %s. No update needed.",
                log_prefix,
                location_uuid,
            )
        elif location_name_from_ribbon == expected_location_name:
            logger.info(
                "%s: Updating ribbon location %s from %s to %s",
                log_prefix,
                location_uuid,
                location_name_from_ribbon,
                updated_location_name,
            )
            if dry_run_off:
                result, response_status = update_location_attributes(
                    location_uuid=location_uuid,
                    updated_name=updated_location_name,
                )
                assert response_status == status.HTTP_200_OK
        else:
            logger.info(
                "%s: Skipping update. Name does not match. %s vs %s",
                log_prefix,
                location_name_from_ribbon,
                expected_location_name,
            )


def process_curated_provider_ranking_row(row: CuratedProviderRankingRow, rank_change: int, log_prefix: str):
    # Either npi will be present or care_org_name will present
    npi: Optional[str] = str(row.get("npi", "")).strip() if row.get("npi") else None
    care_org_name: str = str(row.get("care_org_name", "")).strip()
    address_line_1: str = str(row.get("address_line_1", "")).strip()
    address_line_2: str = str(row.get("address_line_2", "")).strip()
    city: str = str(row.get("city", "")).strip()
    state_abb: str = str(row.get("state", "")).strip()
    state: Optional[State] = None
    if state_abb is not None and len(state_abb) > 0:
        state = State.objects.filter(abbreviation=state_abb.upper()).first()
    if state is None:
        logger.error("%s: No state found for %s. Row: %s", log_prefix, state_abb, row)
    zip_code: str = str(row.get("zipcode", "")).strip()
    if zip_code is not None:
        zip_code = zip_code.strip()[:5]
    ranking_reason = str(row.get("reason", "")).strip()

    ranking_level: str = str(row.get("ranking_level", "")).strip()
    if ranking_level != "":
        ranking_level = ranking_level.lower()

    # if npi and care_org name is not present then log it and skip this row
    if not npi and not care_org_name:
        logger.error("%s: NPI and Care_org_name is not present for the provider Row: %s", log_prefix, row)
        return

    if not zip_code:
        logger.error("%s: Zipcode is not present for the provider Row: %s", log_prefix, row)
        return
    if not ranking_level or not ranking_reason:
        logger.error("%s: Ranking level or Ranking reason is not present for the provider Row: %s", log_prefix, row)
        return

    curated_provider = CuratedProvider.objects.filter(
        npi=npi,
        care_org_name=care_org_name,
        zip_code=zip_code,
    ).first()
    if not curated_provider:
        # Create curated provider
        curated_provider = CuratedProvider.objects.create(
            npi=npi,
            care_org_name=care_org_name,
            zip_code=zip_code,
            address_line_1=address_line_1,
            address_line_2=address_line_2,
            city=city,
            state=state,
        )

    Ranking.objects.get_or_create(
        curated_provider=curated_provider,
        reason=ranking_reason,
        ranking_level=ranking_level,
        defaults={
            "rank_change": rank_change,
        },
    )


def process_partnership_rows_with_services_and_clinical_focus_areas(
    batch: List[PartnershipWithServicesRow], log_prefix: str
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data
    from firefly.modules.referral.models import PartnershipClinicalFocusArea, PartnershipService, Service

    partnerships: List[Partnership] = []

    # First, create all the Partnerships
    for row in batch:
        partner_name: str = str(row.get("partner_name", "")).strip()
        agreement_type: str = str(row.get("agreement_type", "")).strip()
        if agreement_type is not None:
            agreement_type = agreement_type.lower()
        can_accept_care_members: bool = row.get("can_accept_care_members", "FALSE").lower() == "true"
        can_accept_coverage_members: bool = row.get("can_accept_coverage_members", "FALSE").lower() == "true"
        can_accept_care_and_coverage_members: bool = (
            row.get("can_accept_care_and_coverage_members", "FALSE").lower() == "true"
        )
        is_valid_for_all_zipcodes: bool = row.get("is_valid_for_all_zipcodes", "FALSE").lower() == "true"
        partnership_type: str = str(row.get("partnership_type", "")).strip()

        if partnership_type not in PartnershipType.values:
            logger.info("%s: SKIPPING Partnership Type not found for the partner_name %s", log_prefix, partner_name)
            continue

        is_active: bool = row.get("is_active", "TRUE").lower() == "true"

        partnerships.append(
            Partnership(
                partner_name=partner_name,
                agreement_type=agreement_type,
                can_accept_care_members=can_accept_care_members,
                can_accept_coverage_members=can_accept_coverage_members,
                can_accept_care_and_coverage_members=can_accept_care_and_coverage_members,
                is_valid_for_all_zipcodes=is_valid_for_all_zipcodes,
                partnership_type=partnership_type,
                is_active=is_active,
            )
        )
    _: Iterable[Partnership] = bulk_upsert_data(
        objects=partnerships,
        Model=Partnership,
        match_fields=["partner_name"],
        update_fields=[
            "partner_name",
            "can_accept_care_members",
            "can_accept_coverage_members",
            "can_accept_care_and_coverage_members",
            "agreement_type",
            "is_valid_for_all_zipcodes",
            "partnership_type",
            "is_active",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )

    # Secondly, create all the partnership and services mapping
    partnership_services_mapping_objects: List[PartnershipService] = []
    partnership_clinical_focus_area_mapping_objects: List[PartnershipClinicalFocusArea] = []
    # Thirdly, create all the partnership excluded states mapping
    partnership_excluded_mapping_object: List[PartnershipServicingStateExclusion] = []
    for row in batch:
        partner_name = str(row.get("partner_name", "")).strip()
        row_partnership_services = str(row.get("services", "")).strip()
        row_partnership_clinical_focus_areas = str(row.get("clinical_focus_areas", "")).strip()
        row_excluded_states = str(row.get("servicing_state_exclusions", "")).strip()
        if not row_partnership_services and not row_partnership_clinical_focus_areas:
            continue
        partnership = Partnership.objects.filter(partner_name=partner_name).first()
        if not partnership:
            logger.info("%s: Partnership not found for the partner_name %s", log_prefix, partner_name)
            continue

        # The sheet will contain the comma-separated services, so split these comma-separated services into an array
        new_partnership_services = [s.strip() for s in row_partnership_services.split(",") if s.strip()]
        new_partnership_clinical_focus_areas = [
            s.strip() for s in row_partnership_clinical_focus_areas.split(",") if s.strip()
        ]
        if len(new_partnership_services) <= 0 and len(new_partnership_clinical_focus_areas) <= 0:
            continue

        # Get list of services currently associated with this partnership in the system
        current_partnership_services = set(
            PartnershipService.objects.filter(partnership=partnership).values_list(
                "service__sanitized_description", flat=True
            )
        )

        current_partnerships_clinical_focus_areas = set(
            PartnershipClinicalFocusArea.objects.filter(partnership=partnership).values_list(
                "clinical_focus_area__sanitized_label", flat=True
            )
        )

        new_partnership_services_set = set(new_partnership_services)
        new_partnership_clinical_focus_areas_set = set(new_partnership_clinical_focus_areas)

        # Calculate the services to add (present in new_services but not in current_services)
        partnership_services_to_add = new_partnership_services_set - current_partnership_services
        partnership_clinical_focus_areas_to_add = (
            new_partnership_clinical_focus_areas_set - current_partnerships_clinical_focus_areas
        )

        # Calculate the services to delete (present in current_services but not in new_services)
        partnership_services_to_delete = current_partnership_services - new_partnership_services_set
        partnerships_clinical_focus_areas_to_delete = (
            current_partnerships_clinical_focus_areas - new_partnership_clinical_focus_areas_set
        )

        # Delete old services
        if partnership_services_to_delete:
            logger.info(
                "%s: Deleting old partnership and service mappings for partner name %s , ids %s",
                log_prefix,
                partner_name,
                partnership_services_to_delete,
            )
            PartnershipService.objects.filter(
                partnership=partnership, service__sanitized_description__in=partnership_services_to_delete
            ).delete()

        if partnerships_clinical_focus_areas_to_delete:
            logger.info(
                "%s: Deleting old partnership and clinical focus area mappings for partner name %s , ids %s",
                log_prefix,
                partner_name,
                partnerships_clinical_focus_areas_to_delete,
            )
            PartnershipClinicalFocusArea.objects.filter(
                partnership=partnership,
                clinical_focus_area__sanitized_label__in=partnerships_clinical_focus_areas_to_delete,
            ).delete()

        # Create new partnerships services mapping objects
        for partnership_service_name in partnership_services_to_add:
            service = Service.objects.filter(sanitized_description=partnership_service_name).first()
            if not service:
                logger.info(
                    "%s: Service not found for the partnership %s , service %s",
                    log_prefix,
                    partner_name,
                    partnership_service_name,
                )
                continue
            partnership_services_mapping_objects.append(PartnershipService(partnership=partnership, service=service))

        for partnership_clinical_focus_area_name in partnership_clinical_focus_areas_to_add:
            clinical_focus_area = ClinicalFocusArea.objects.filter(
                sanitized_label=partnership_clinical_focus_area_name
            ).first()
            if not clinical_focus_area:
                logger.info(
                    "%s: Clinical Focus Area not found for the partnership %s , clinical focus area %s",
                    log_prefix,
                    partner_name,
                    partnership_clinical_focus_area_name,
                )
                continue
            partnership_clinical_focus_area_mapping_objects.append(
                PartnershipClinicalFocusArea(partnership=partnership, clinical_focus_area=clinical_focus_area)
            )

        # Thirdly, update all the State Exclusion mappings

        # The sheet will contain the comma-separated excluded states so split these comma-separated states into an array
        new_partnership_excluded_states = [s.strip() for s in row_excluded_states.split(",") if s.strip()]
        if len(new_partnership_excluded_states) <= 0:
            continue

        # Get list of excluded states currently associated with this partnership in the system
        current_partnership_excluded_states = set(
            PartnershipServicingStateExclusion.objects.filter(partnership=partnership).values_list(
                "state__abbreviation", flat=True
            )
        )

        new_partnership_excluded_states_set = set(new_partnership_excluded_states)

        # Calculate the states to add (present in new states but not in current states)
        partnership_excluded_states_to_add = new_partnership_excluded_states_set - current_partnership_excluded_states

        # Calculate the states to delete (present in current states but not in new states)
        partnership_excluded_states_to_delete = (
            current_partnership_excluded_states - new_partnership_excluded_states_set
        )

        # Delete old excluded states
        if partnership_excluded_states_to_delete:
            logger.info(
                "%s: Deleting old partnership and excluded states mappings for partner name %s , ids %s",
                log_prefix,
                partner_name,
                partnership_excluded_states_to_delete,
            )
            PartnershipServicingStateExclusion.objects.filter(
                partnership=partnership, state__abbreviation__in=partnership_excluded_states_to_delete
            ).delete()

        # Create new partnerships excluded state mapping objects
        for excluded_states_abb in partnership_excluded_states_to_add:
            excluded_state = State.objects.filter(abbreviation=excluded_states_abb).first()
            if not excluded_state:
                logger.info(
                    "%s: Excluded State not found for the partnership %s , State %s",
                    log_prefix,
                    partner_name,
                    excluded_states_abb,
                )
                continue
            partnership_excluded_mapping_object.append(
                PartnershipServicingStateExclusion(partnership=partnership, state=excluded_state)
            )

    # Bulk upsert the PartnershipService objects
    _ = bulk_upsert_data(
        objects=partnership_services_mapping_objects,
        Model=PartnershipService,
        match_fields=["partnership", "service"],
        update_fields=[
            "partnership",
            "service",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )

    # Bulk upsert the PartnershipClinicalFocusArea objects
    _ = bulk_upsert_data(
        objects=partnership_clinical_focus_area_mapping_objects,
        Model=PartnershipClinicalFocusArea,
        match_fields=["partnership", "clinical_focus_area"],
        update_fields=[
            "partnership",
            "clinical_focus_area",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )

    # Bulk upsert the PartnershipServicingStateExclusion objects
    _ = bulk_upsert_data(
        objects=partnership_excluded_mapping_object,
        Model=PartnershipServicingStateExclusion,
        match_fields=["partnership", "state"],
        update_fields=[
            "partnership",
            "state",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_partnership_record_batch(
    batch: List[CuratedProviderPartnershipRow], match_fields: List[str], log_prefix: str, import_job_name: str
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data

    partnership_records: List[PartnershipRecord] = []
    all_partnerships: QuerySet[Partnership] = Partnership.objects.all()
    for row in batch:
        partner_name: str = str(row.get("partner_name", "")).strip()
        filtered_partnership: Optional[Partnership] = None
        for partnership in all_partnerships:
            if partnership.partner_name == partner_name:
                filtered_partnership = partnership
                break

        if not filtered_partnership:
            logger.error("%s: Partner not found for Partner name %s SKIPPING", log_prefix, partner_name)
            continue
        partnership_records.append(
            create_partnership_record_object(
                row=row, partnership=filtered_partnership, log_prefix=log_prefix, import_job_name=import_job_name
            )
        )

    _: Iterable[PartnershipRecord] = bulk_upsert_data(
        objects=partnership_records,
        Model=PartnershipRecord,
        match_fields=match_fields,
        update_fields=[
            "address_line_2",
            "city",
            "state",
            "is_tagged_as_partner",
            "is_processed",
            "import_job_name",
            "partner_name",
            "partnership_level",
            "provider_type",
            "first_name",
            "last_name",
            "can_accept_care_members",
            "can_accept_coverage_members",
            "can_accept_care_and_coverage_members",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_services_rows_with_specialty_group_location_type(
    batch: List[ServicesSpecialtyLocationTypeRow], log_prefix: str, import_job_name: str
):
    from firefly.modules.facts.models import SpecialtyGroup
    from firefly.modules.firefly_django.utils import bulk_upsert_data
    from firefly.modules.referral.models import Service, ServiceSpecialtyGroupLocationType, ServiceType

    service_mappings: List[ServiceSpecialtyGroupLocationType] = []

    for row in batch:
        service_category_ordering: int | None = (
            int(row.get("service_category_ordering", "")) if row.get("service_category_ordering", "") else None  # type: ignore
        )
        service_type_name: str = str(row.get("service_category", "")).strip()

        specialty_group_name = str(row.get("specialty_group", "")).strip()
        location_type_name = str(row.get("location_type", "")).strip()

        service_name = str(row.get("services", "")).strip()

        if not specialty_group_name and not location_type_name:
            logger.error("%s: Specialty Group and Location Type not present for row %s SKIPPING", log_prefix, row)
            continue

        if not service_name:
            logger.error("%s: Service not present for row %s SKIPPING", log_prefix, row)
            continue

        if service_category_ordering is None:
            logger.error("%s: Service Category Ordering not present for row %s SKIPPING", log_prefix, row)
            continue

        if not service_type_name:
            logger.error("%s: Service Category not present for row %s SKIPPING", log_prefix, row)
            continue

        # Fetch the location type/specialty group
        filtered_specialty_group: Optional[SpecialtyGroup] = None
        filtered_location_type: Optional[LocationType] = None
        if specialty_group_name:
            try:
                filtered_specialty_group = SpecialtyGroup.objects.get(label=specialty_group_name)
            except SpecialtyGroup.DoesNotExist:
                logger.error("%s: Specialty Group %s not found SKIPPING", log_prefix, specialty_group_name)
                continue
        elif location_type_name:
            try:
                filtered_location_type = LocationType.objects.get(label=location_type_name)
            except LocationType.DoesNotExist:
                logger.error("%s: Location Type %s not found SKIPPING", log_prefix, location_type_name)
                continue

        # Fetch the service type
        filtered_service_type, _ = ServiceType.objects.get_or_create(
            description=service_type_name, ordering=service_category_ordering
        )

        if not filtered_service_type:
            logger.error(
                "%s: Service Type not found for Service Category name %s SKIPPING", log_prefix, service_type_name
            )
            continue

        # Fetch the related service
        filtered_service, __ = Service.objects.get_or_create(description=service_name, category=filtered_service_type)

        if not filtered_service:
            logger.error(
                "%s: Service %s not found for Service Category name %s SKIPPING",
                log_prefix,
                service_name,
                service_type_name,
            )
            continue

        service_mappings.append(
            ServiceSpecialtyGroupLocationType(
                service=filtered_service,
                specialty_group=filtered_specialty_group if filtered_specialty_group else None,
                location_type=filtered_location_type if filtered_location_type else None,
                import_job_name=import_job_name,
            )
        )

    # create mapping based on service type/service name/specialty group/location type
    bulk_upsert_data(
        objects=service_mappings,
        Model=ServiceSpecialtyGroupLocationType,
        match_fields=[
            "service",
            "specialty_group",
            "location_type",
        ],
        update_fields=["service", "specialty_group", "location_type", "import_job_name"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_clinical_focus_area_with_specialty_group(
    batch: List[ClinicalFocusAreaSpecialtyRow], log_prefix: str, import_job_name: str
):
    from firefly.modules.facts.models import SpecialtyGroup
    from firefly.modules.firefly_django.utils import bulk_upsert_data
    from firefly.modules.referral.models import ClinicalFocusAreaSpecialtyGroup, ServiceType

    clinical_focus_area_mappings: List[ClinicalFocusAreaSpecialtyGroup] = []

    for row in batch:
        service_category_ordering: int | None = (
            int(row.get("service_category_ordering", "")) if row.get("service_category_ordering", "") else None  # type: ignore
        )
        service_type_name: str = str(row.get("service_category", "")).strip()

        specialty_group_name = str(row.get("specialty_group", "")).strip()

        clinical_focus_area_name = str(row.get("clinical_focus_area", "")).strip()

        if not specialty_group_name:
            logger.error("%s: Specialty Group not present for row %s SKIPPING", log_prefix, row)
            continue

        if not clinical_focus_area_name:
            logger.error("%s: Clinical Focus Area name not present for row %s SKIPPING", log_prefix, row)
            continue

        if service_category_ordering is None:
            logger.error("%s: Service Category Ordering not present for row %s SKIPPING", log_prefix, row)
            continue

        if not service_type_name:
            logger.error("%s: Service Category not present for row %s SKIPPING", log_prefix, row)
            continue

        # Fetch the location type/specialty group
        filtered_specialty_group: Optional[SpecialtyGroup] = None
        try:
            filtered_specialty_group = SpecialtyGroup.objects.get(label=specialty_group_name)
        except SpecialtyGroup.DoesNotExist:
            logger.error("%s: Specialty Group %s not found SKIPPING", log_prefix, specialty_group_name)
            continue

        # Fetch the service type
        filtered_service_type, _ = ServiceType.objects.get_or_create(
            description=service_type_name, ordering=service_category_ordering
        )

        if not filtered_service_type:
            logger.error(
                "%s: Service Type not found for Service Category name %s SKIPPING", log_prefix, service_type_name
            )
            continue

        # Fetch the related service
        filtered_clinical_focus_area, __ = ClinicalFocusArea.objects.update_or_create(
            label=clinical_focus_area_name, defaults={"category": filtered_service_type}
        )

        if not filtered_clinical_focus_area:
            logger.error(
                "%s: Clinical Focus Area %s not found for Service Category name %s SKIPPING",
                log_prefix,
                clinical_focus_area_name,
                service_type_name,
            )
            continue

        clinical_focus_area_mappings.append(
            ClinicalFocusAreaSpecialtyGroup(
                clinical_focus_area=filtered_clinical_focus_area,
                specialty_group=filtered_specialty_group,
                import_job_name=import_job_name,
            )
        )

    # create mapping based on service type/clinical focus area name/specialty group
    bulk_upsert_data(
        objects=clinical_focus_area_mappings,
        Model=ClinicalFocusAreaSpecialtyGroup,
        match_fields=[
            "clinical_focus_area",
            "specialty_group",
        ],
        update_fields=["clinical_focus_area", "specialty_group", "import_job_name"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_partner_rows_with_contract(batch: List[PartnershipContractRow], log_prefix: str, import_job_name: str):
    from firefly.modules.firefly_django.utils import bulk_upsert_data

    partnership_contract_mappings: List[PartnershipContract] = []

    # Fetching all partnerships and then filtering in loop to save individual query N times in the loop
    all_partnerships: QuerySet[Partnership] = Partnership.objects.all()

    for row in batch:
        partner_name: str = str(row.get("partner_name", "")).strip()
        contract_name: str = str(row.get("contract", "")).strip()
        is_primary_subscriber_only: str = str(row.get("is_primary_subscriber_only", "")).strip()

        # Fetch the contract first
        if not contract_name:
            logger.error("%s: Contract not present in row %s SKIPPING", log_prefix, row)
            continue
        contract: Optional[Contract] = Contract.objects.filter(name=contract_name).first()
        if not contract:
            logger.error("%s: Contract not found %s SKIPPING", log_prefix, row)
            continue

        is_primary_subscriber_only_converted: bool = True if is_primary_subscriber_only == "TRUE" else False

        filtered_partnership: Optional[Partnership] = None
        for partnership in all_partnerships:
            if partnership.partner_name == partner_name:
                filtered_partnership = partnership
                break

        if not filtered_partnership:
            logger.error("%s: Partnership not found for Partner name %s SKIPPING", log_prefix, partner_name)
            continue

        partnership_contract_mappings.append(
            PartnershipContract(
                partnership=partnership,
                contract=contract,
                is_primary_subscriber_only=is_primary_subscriber_only_converted,
                import_job_name=import_job_name,
            )
        )
    bulk_upsert_data(
        objects=partnership_contract_mappings,
        Model=PartnershipContract,
        match_fields=[
            "partnership",
            "contract",
        ],
        update_fields=["partnership", "contract", "import_job_name"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def create_partnership_record_object(
    row: CuratedProviderPartnershipRow, partnership: Partnership, log_prefix: str, import_job_name: str
) -> PartnershipRecord:
    # Either npi will be present or care_org_name will present
    npi: str = str(row.get("npi", "")).strip()
    care_org_name: str = str(row.get("care_org_name", "")).strip()
    first_name: str = str(row.get("provider_first_name", "")).strip()
    last_name: str = str(row.get("provider_last_name", "")).strip()
    address_line_1: str = str(row.get("address_line_1", "")).strip()
    address_line_2: str = str(row.get("address_line_2", "")).strip()
    city: str = str(row.get("city", "")).strip()
    state_abb: str = str(row.get("state", "")).strip()
    state: Optional[State] = None
    if state_abb is not None and len(state_abb) > 0:
        state = State.objects.filter(abbreviation=state_abb.upper()).first()
    if state is None:
        logger.error("%s: No state found for %s. Row: %s", log_prefix, state_abb, row)
    zip_code: str = str(row.get("zipcode", "")).strip()
    if zip_code is not None:
        zip_code = zip_code.strip()[:5]
    partner_name: str = str(row.get("partner_name", "")).strip()

    partnership_level: str = str(row.get("partnership_level", "")).strip()
    if partnership_level != "":
        partnership_level = partnership_level.lower()
    provider_type = row.get("provider_type", "")
    formatted_provider_type = PROVIDER_TYPE_MAPPING.get(provider_type, None)
    return PartnershipRecord(
        npi=npi,
        care_org_name=care_org_name,
        address_line_1=address_line_1,
        address_line_2=address_line_2,
        city=city,
        state=state,
        zip_code=zip_code,
        partner_name=partner_name,
        agreement_type=partnership.agreement_type,
        partnership_level=partnership_level,
        can_accept_care_members=partnership.can_accept_care_members,
        can_accept_coverage_members=partnership.can_accept_coverage_members,
        can_accept_care_and_coverage_members=partnership.can_accept_care_and_coverage_members,
        is_tagged_as_partner=False,
        is_processed=False,
        import_job_name=import_job_name,
        provider_type=formatted_provider_type,
        first_name=first_name,
        last_name=last_name,
    )


def validate_headers(HeaderColumnNames, headers):
    try:
        expected_headers = set(HeaderColumnNames.__annotations__.keys())
        expected_headers_sorted = sorted(expected_headers)

        # Validate headers rows are same as expected
        actual_headers = set([header.lower() for header in headers])
        actual_headers_sorted = sorted(actual_headers)

        if expected_headers_sorted != actual_headers_sorted:
            logger.error(
                "Headers mismatch. Expected: %s, Actual: %s",
                expected_headers,
                actual_headers,
            )
            return False
        return True
    except Exception:
        logger.exception("Error while validating headers: %s", headers)


def process_curated_provider_latitude_longitude_row(row: IngestCuratedProviderLatitudeLongitudeRow, log_prefix: str):
    # Either npi will be present or care_org_name will present
    npi: Optional[str] = str(row.get("npi", "")).strip() if row.get("npi") else None
    care_org_name: str = str(row.get("care_org_name", "")).strip()
    address_line_1: str | None = str(row.get("address_line_1", "")).strip() if row.get("address_line_1") else None
    zip_code: str = str(row.get("zip_code", "")).strip()
    if zip_code is not None:
        zip_code = zip_code.strip()[:5]

    latitude: str | None = str(row.get("latitude", "")).strip() if row.get("latitude") else None
    longitude: str | None = str(row.get("longitude", "")).strip() if row.get("longitude") else None

    # if npi and care_org name is not present then log it and skip this row
    if not npi and not care_org_name:
        logger.error("%s: NPI and Care_org_name is not present for the provider Row: %s", log_prefix, row)
        return

    if not zip_code:
        logger.error("%s: Zipcode is not present for the provider Row: %s", log_prefix, row)
        return
    if not latitude or not longitude:
        logger.error("%s: Lat/Long is not present for the provider Row: %s", log_prefix, row)
        return

    curated_provider_queryset = CuratedProvider.objects.filter(
        care_org_name=care_org_name,
        zip_code=zip_code,
    )
    if npi:
        curated_provider_queryset.filter(npi=npi)
    if address_line_1:
        curated_provider_queryset.filter(address_line_1=address_line_1)

    curated_provider = curated_provider_queryset.first()

    if not curated_provider:
        logger.error("%s: Curated Provider not found for the row: %s", log_prefix, row)
        return

    curated_provider.latitude = latitude
    curated_provider.longitude = longitude
    curated_provider.point = Point(
        x=float(longitude),
        y=float(latitude),
        srid=4326,
    )
    curated_provider.save(update_fields=["latitude", "longitude", "point"])


def process_partnership_rows_with_insurance(
    batch: List[PartnershipInsurancePayerRow], log_prefix: str, import_job_name: str
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data

    partnership_insurance_payer_mappings: List[PartnershipInsurancePayer] = []

    # Fetching all partnerships and then filtering in loop to save individual query N times in the loop
    all_partnerships: QuerySet[Partnership] = Partnership.objects.all()

    all_states: QuerySet[State] = State.objects.all()

    for row in batch:
        partner_name: str = str(row.get("partner_name", "")).strip()
        filtered_partnership: Optional[Partnership] = None
        for partnership in all_partnerships:
            if partnership.partner_name == partner_name:
                filtered_partnership = partnership
                break

        if not filtered_partnership:
            logger.error("%s: Partnership not found for Partner name %s SKIPPING", log_prefix, partner_name)
            continue

        # We only need to create insurance mapping for PREFERRED_VENDOR as they are applicable for specific insurances
        if partnership.agreement_type != AgreementTypeConfig.PREFERRED_VENDOR:
            logger.info("%s: Skipping insurance mapping as agreement is not preferred vendor: %s", log_prefix, row)
            continue

        # Fetch insurance payer
        insurance_payer_name: str = str(row.get("insurance_payer", "")).strip()
        if not insurance_payer_name:
            logger.error("%s: Insurance payer is None %s SKIPPING", log_prefix, row)
            continue
        insurance_payer: Optional[InsurancePayer] = InsurancePayer.objects.filter(name=insurance_payer_name).first()
        if not insurance_payer:
            logger.error("%s: Insurance payer not found %s SKIPPING", log_prefix, row)
            continue

        # Fetch the insurance type
        insurance_plan_type: str = str(row.get("insurance_type", "")).strip()
        insurance_plan_type_valid = bool(
            [
                choice
                for choice in PartnershipInsurancePayer.PLAN_TYPE_CHOICES
                if insurance_plan_type.upper() == choice[0]
            ]
        )

        if not insurance_plan_type_valid:
            logger.error("%s: Insurance Plan Type is not valid %s SKIPPING", log_prefix, row)
            continue

        # Fetch the states
        states = None
        states_list: str = str(row.get("states", "")).strip()
        if not states_list:
            logger.error("%s: States is None %s SKIPPING", log_prefix, row)
            continue

        if states_list == "ANY":
            states = all_states
        else:
            # Convert the comma separated string of states_list into a List
            state_abb_list = [state.strip() for state in states_list.split(",")]
            states = State.objects.filter(abbreviation__in=state_abb_list).all()

        # Fetch the group number
        group_number: Optional[str] = (
            str(row.get("group_number", None)).strip() if row.get("group_number", None) else None
        )

        # Create insurance payer - partnership mapping for all states
        for state in states:
            partnership_insurance_payer_mappings.append(
                PartnershipInsurancePayer(
                    partnership=partnership,
                    insurance_payer=insurance_payer,
                    state=state,
                    insurance_plan_type=insurance_plan_type,
                    group_number=group_number,
                    import_job_name=import_job_name,
                )
            )
    _: Iterable[PartnershipInsurancePayer] = bulk_upsert_data(
        objects=partnership_insurance_payer_mappings,
        Model=PartnershipInsurancePayer,
        match_fields=[
            "partnership",
            "insurance_payer",
            "state",
            "insurance_plan_type",
        ],
        update_fields=[
            "partnership",
            "insurance_payer",
            "state",
            "insurance_plan_type",
            "import_job_name",
            "group_number",
        ],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_ffnb_coverage_analysis(
    ffnb_coverage_ids: List[int],
    distance_in_miles: float,
    log_prefix: str,
    included_partnership_ids: List[int],
    excluded_partnerships_ids: List[int],
):
    if not ffnb_coverage_ids or not distance_in_miles:
        return

    ffnb_coverage_objects = FireflyNearbyCoverage.objects.filter(id__in=ffnb_coverage_ids, is_processed=False)

    ffnb_coverage_object: FireflyNearbyCoverage
    for ffnb_coverage_object in ffnb_coverage_objects.iterator():
        if not ffnb_coverage_object.zip_code:
            logger.info(
                "%s: SKIPPING: zipcode not for for the FireflyNearbyCoverage object id : %s",
                log_prefix,
                ffnb_coverage_object.id,
            )
            continue

        # Fetch zip code coordinates for ffnb coverage analysis
        member_zipcode_coordinate = ZipCodeCoordinate.objects.filter(
            zip_code=ffnb_coverage_object.zip_code, latitude__isnull=False, longitude__isnull=False
        ).first()

        if not member_zipcode_coordinate:
            logger.info(
                "%s: SKIPPING: member zipcode is not geocoded for id : %s, zipcode: %s",
                log_prefix,
                ffnb_coverage_object.id,
                ffnb_coverage_object.zip_code,
            )
            continue

        if member_zipcode_coordinate.point:
            member_zipcode_coordinate_point: Point = member_zipcode_coordinate.point
        else:
            member_zipcode_coordinate_point = Point(
                x=float(member_zipcode_coordinate.longitude),
                y=float(member_zipcode_coordinate.latitude),
                srid=4326,
            )

        # Fetch all in_person partners within range and in_home partners within the same zip_code
        curated_providers = (
            CuratedProvider.objects.annotate(
                # Annotate CuratedProvider objects with distance representing their
                # distance from the given longitude/ latitude
                distance=Distance("point", member_zipcode_coordinate_point)
            )
            .filter(
                # Filter out CuratedProvider that are not within a given radius for in_person partner
                # And for in_home partners, just match the zip_code
                Q(
                    Q(distance__lte=DistanceMeasure(mi=float(distance_in_miles)))
                    & Q(provider_type=CuratedProviderType.IN_PERSON)
                )
                | Q(Q(provider_type=CuratedProviderType.IN_HOME) & Q(zip_code=member_zipcode_coordinate.zip_code))
            )
            .filter(
                latitude__isnull=False,
                longitude__isnull=False,
            )
            .filter(
                Q(curatedprovider_partnerships__deleted__isnull=True)
                & Q(
                    Q(curatedprovider_partnerships__partnership__is_active=True)
                    | Q(curatedprovider_partnerships__partnership__id__in=included_partnership_ids)
                )
                & (~Q(curatedprovider_partnerships__partnership__id__in=excluded_partnerships_ids))
            )
            .prefetch_related(
                Prefetch(
                    "curatedprovider_partnerships",
                    queryset=CuratedProviderPartnership.objects.select_related("partnership").distinct(),
                ),
            )
            .distinct()
        )

        # Define the coverage analysis booleans
        has_urgent_care_within_range = False
        has_in_home_services_within_range = False
        has_lab_within_range = False

        # else there are four cases to confirm the coverage analysis
        # 1. has_urgent_care_within_range: It will be only true,
        #   a. provider_type is "in_person"
        #   b. partnership_type is "Urgent/Retail Care"
        #   c. distance is within the input distance (Already filtered as a part of curated_providers query)
        #   d. agreement_type is "direct_contract"
        # 2. has_in_home_services_within_range: It will be only true,
        #   a. provider_type is "in_home"
        #   b. partnership_type is "In Home Services"
        #   c. zip_code is matching (Already filtered as a part of curated_providers query)
        #   d. agreement_type is "direct_contract"
        # 3. has_lab_within_range: It will be only true,
        #   a. partnership_type is "Lab"
        #   b. provider_type is "in_person"
        #   c. distance is within the input distance (Already filtered as a part of curated_providers query)
        # 4. has_ffnb_within_range: It will be only true,
        #   a. If has_urgent_care_within_range OR has_in_home_services_within_range is True
        for curated_provider in curated_providers.iterator():
            # Find the partnership
            curated_provider_partnerships = curated_provider.curatedprovider_partnerships.all()
            partnership: Optional[Partnership] = None
            for curated_provider_partnership in curated_provider_partnerships:
                partnership = curated_provider_partnership.partnership
                break
            if not partnership:
                continue

            if (
                curated_provider.provider_type == CuratedProviderType.IN_PERSON
                and partnership.partnership_type == PartnershipType.URGENT_RETAIL_CARE
                and partnership.agreement_type == AgreementTypeConfig.DIRECT_CONTRACT
                and has_urgent_care_within_range is False
            ):
                has_urgent_care_within_range = True

            if (
                curated_provider.provider_type == CuratedProviderType.IN_HOME
                and partnership.partnership_type == PartnershipType.IN_HOME_SERVICES
                and partnership.agreement_type == AgreementTypeConfig.DIRECT_CONTRACT
                and has_in_home_services_within_range is False
            ):
                has_in_home_services_within_range = True

            if (
                curated_provider.provider_type == CuratedProviderType.IN_PERSON
                and partnership.partnership_type == PartnershipType.LAB
                and has_lab_within_range is False
            ):
                has_lab_within_range = True

        # Update the coverage analysis and mark them as processed
        ffnb_coverage_object.is_processed = True
        ffnb_coverage_object.coverage_range_in_miles = float(distance_in_miles)
        ffnb_coverage_object.has_urgent_care_within_range = has_urgent_care_within_range
        ffnb_coverage_object.has_in_home_services_within_range = has_in_home_services_within_range
        ffnb_coverage_object.has_lab_within_range = has_lab_within_range
        ffnb_coverage_object.has_ffnb_within_range = has_urgent_care_within_range or has_in_home_services_within_range
        ffnb_coverage_object.save(
            update_fields=[
                "is_processed",
                "coverage_range_in_miles",
                "has_urgent_care_within_range",
                "has_in_home_services_within_range",
                "has_lab_within_range",
                "has_ffnb_within_range",
            ]
        )


def create_ffnb_partner_geojson(log_prefix: str):
    logger.info("%s: Starting the async process for uploading geojson", log_prefix)

    # Fetch all the urgent care in_person/in_home/lab partners
    curated_providers_queryset = (
        CuratedProvider.objects.filter(
            zip_code__isnull=False,
            latitude__isnull=False,
            longitude__isnull=False,
            curatedprovider_partnerships__deleted__isnull=True,
            curatedprovider_partnerships__partnership__is_active=True,
        )
        .filter(Q(provider_type=CuratedProviderType.IN_PERSON) | Q(provider_type=CuratedProviderType.IN_HOME))
        .filter(
            Q(
                Q(curatedprovider_partnerships__partnership__partnership_type=PartnershipType.URGENT_RETAIL_CARE)
                & Q(curatedprovider_partnerships__partnership__agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
            )
            | Q(
                Q(curatedprovider_partnerships__partnership__partnership_type=PartnershipType.IN_HOME_SERVICES)
                & Q(curatedprovider_partnerships__partnership__agreement_type=AgreementTypeConfig.DIRECT_CONTRACT)
            )
            | Q(curatedprovider_partnerships__partnership__partnership_type=PartnershipType.LAB)
        )
        .prefetch_related(
            Prefetch(
                "curatedprovider_partnerships",
                queryset=CuratedProviderPartnership.objects.select_related("partnership").distinct(),
            ),
        )
    ).distinct()
    logger.info("%s: Found total %s Partners", log_prefix, curated_providers_queryset.count())

    # Create geojson data
    geojson_data = []
    curated_provider: CuratedProvider
    for curated_provider in curated_providers_queryset.iterator():
        # Find the partnership
        curated_provider_partnerships = curated_provider.curatedprovider_partnerships.all()
        partnership: Optional[Partnership] = None
        for curated_provider_partnership in curated_provider_partnerships:
            partnership = curated_provider_partnership.partnership
            break
        if not partnership:
            continue

        address_components = []
        # Append each component if it's not null or empty
        if curated_provider.address_line_1:
            address_components.append(curated_provider.address_line_1)
        if curated_provider.address_line_2:
            address_components.append(curated_provider.address_line_2)
        if curated_provider.city:
            address_components.append(curated_provider.city)
        if curated_provider.state and curated_provider.state.name:
            address_components.append(curated_provider.state.name)
        if curated_provider.zip_code:
            address_components.append(curated_provider.zip_code)

        # Join the components with a comma and space
        full_address = ", ".join(address_components)
        if curated_provider.longitude and curated_provider.latitude:
            data = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [
                        float(curated_provider.longitude),  # noqa
                        float(curated_provider.latitude),  # noqa
                    ],
                },
                "properties": {
                    "Address": full_address,
                    "PartnerType": partnership.partnership_type,
                    "ZipCode": curated_provider.zip_code,
                },
            }
            geojson_data.append(data)
    feature_collection = {"type": "FeatureCollection", "features": geojson_data}
    return feature_collection


# Upload the geojson data to S3
def upload_geojson_to_s3(json_data, file_path, log_prefix):
    # Step 1: Write JSON data to a file
    json_content = json.dumps(json_data)

    s3_client = boto3.client(
        "s3",
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        aws_session_token=settings.AWS_SESSION_TOKEN,
        region_name=settings.AWS_REGION,
    )

    # Step 3: Upload the file to S3
    try:
        s3_client.put_object(
            Bucket=settings.AWS_S3_BUCKET_NAME, Key=file_path, Body=json_content, ContentType="application/json"
        )
    except Exception:
        logger.exception("%s: Failed to upload geojson to S3", log_prefix)


def process_provider_recommendation_analysis(
    provider_recommendation_analysis_ids: List[int],
    import_job_name: str,
    log_prefix: str,
):
    from firefly.modules.firefly_django.utils import bulk_upsert_data
    from firefly.modules.referral.models import SearchRequestVendor
    from firefly.modules.referral.utils.provider_search_utils import (
        ProviderDetail,
        ProviderSearchResult,
        get_provider_search_results,
    )

    if not provider_recommendation_analysis_ids or not import_job_name:
        return

    provider_recommendation_analysis_queryset = ProviderRecommendationAnalysis.objects.filter(
        id__in=provider_recommendation_analysis_ids, is_processed=False, import_job_name=import_job_name
    )

    provider_recommendation_analysis_objects: List[ProviderRecommendationAnalysis] = []

    provider_recommendation_analysis: ProviderRecommendationAnalysis
    for provider_recommendation_analysis in provider_recommendation_analysis_queryset.iterator():
        if not provider_recommendation_analysis.zip_code or not provider_recommendation_analysis.npi:
            logger.error(
                "%s: SKIPPING: zipcode or NPI not found for the ProviderRecommendationAnalysis object id : %s",
                log_prefix,
                provider_recommendation_analysis.id,
            )
            continue

        # Call the Ribbon API to fetch provider based on the NPI
        try:
            insurance_plan = InsurancePlan.objects.get(name=FIREFLY_PLAN)
            network_ids = fetch_ribbon_insurance_network_ids_for_insurance_plan(insurance_plan)

            if not network_ids:
                logger.error(
                    "%s: SKIPPING: Insurance network not mapped for the benefit plan %s",
                    log_prefix,
                    provider_recommendation_analysis.id,
                )
                continue

            search_results: ProviderSearchResult = get_provider_search_results(
                address="",
                distance=SystemSuggestedProviderConfig.DISTANCE,
                insurance_uids=network_ids,
                # Most of the partnerships falls under CARE_AND_COVERAGE
                member_service_level=MemberServiceLevel.CARE_AND_COVERAGE,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                name=None,
                gender=None,
                npis=[provider_recommendation_analysis.npi],
                page=SystemSuggestedProviderConfig.PAGE,
                page_size=SystemSuggestedProviderConfig.PAGE_SIZE,
                procedure_uids=None,
                speciality_uids=None,
                specialty_group_id=None,
            )
            if search_results["search_results"]:
                result: Optional[ProviderDetail]
                for result in search_results["search_results"]:
                    if result["npi"] == provider_recommendation_analysis.npi:
                        provider_recommendation_analysis.is_processed = True
                        provider_recommendation_analysis.composite_score = result["composite_score"]
                        provider_recommendation_analysis.recommendation_status = result["recommendation_status"]
                        provider_recommendation_analysis.results = search_results["vendor_results"]
                        provider_recommendation_analysis.vendor = SearchRequestVendor.RIBBON
                        provider_recommendation_analysis.in_network = result["in_network"]

                        provider_recommendation_analysis_objects.append(provider_recommendation_analysis)
                        break
            else:
                logger.error(
                    "%s: SKIPPING: Search results not found for the id %s",
                    log_prefix,
                    provider_recommendation_analysis.id,
                )

        except Exception:
            logger.exception(
                "%s: SKIPPING: Something went wrong while fetching Ribbon results for id %s",
                log_prefix,
                provider_recommendation_analysis.id,
            )
            continue

    _: Iterable[ProviderRecommendationAnalysis] = bulk_upsert_data(
        objects=provider_recommendation_analysis_objects,
        Model=ProviderRecommendationAnalysis,
        match_fields=["npi", "import_job_name"],
        update_fields=["is_processed", "composite_score", "recommendation_status", "results", "vendor", "in_network"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )


def process_service_cpt_code_mapping(batch: List[ServiceCPTCodeRow], log_prefix: str, import_job_name: str):
    from firefly.modules.firefly_django.utils import bulk_upsert_data
    from firefly.modules.referral.models import Service, ServiceCPTCode, ServiceType

    service_cpt_code_mappings: List[ServiceCPTCode] = []

    for row in batch:
        sanitized_service: str = str(row.get("sanitized_service", "")).strip()
        code = str(row.get("cpt_code", "")).strip()
        service_category_ordering: int | None = (
            int(row.get("service_category_ordering", "")) if row.get("service_category_ordering", "") else None  # type: ignore
        )
        service_type_name: str = str(row.get("service_category", "")).strip()
        service_name = str(row.get("service", "")).strip()

        if service_category_ordering is None:
            logger.error("%s: Service Category Ordering not present for row %s SKIPPING", log_prefix, row)
            continue

        if not service_type_name:
            logger.error("%s: Service Category not present for row %s SKIPPING", log_prefix, row)
            continue

        if not sanitized_service or not code:
            logger.error("%s: Sanitized service or cpt code not present for row %s SKIPPING", log_prefix, row)
            continue

        # Fetch the service type
        try:
            filtered_service_type = ServiceType.objects.filter(
                description=service_type_name, ordering=service_category_ordering
            ).get()
        except Service.DoesNotExist:
            logger.error("%s: ServiceType %s not found SKIPPING", log_prefix, sanitized_service)
            continue

        # Fetch the Service
        service: Optional[Service] = None
        try:
            service = Service.objects.get(description=service_name, category=filtered_service_type)
        except Service.DoesNotExist:
            logger.error("%s: Service %s not found SKIPPING", log_prefix, sanitized_service)
            continue

        # Fetch the CPT Code
        cpt_code: Optional[CPTCode] = None
        current_year = datetime.datetime.now().year
        current_year_str = str(current_year)
        cpt_code = CPTCode.objects.filter(code=code, version=current_year_str).first()
        if not cpt_code:
            logger.error("%s: CPTCode %s not found SKIPPING", log_prefix, sanitized_service)
            continue

        service_cpt_code_mappings.append(
            ServiceCPTCode(
                cpt_code=cpt_code,
                service=service,
                import_job_name=import_job_name,
            )
        )

    # create mapping based on Service and CPTCode
    bulk_upsert_data(
        objects=service_cpt_code_mappings,
        Model=ServiceCPTCode,
        match_fields=[
            "cpt_code",
            "service",
        ],
        update_fields=["cpt_code", "service", "import_job_name"],
        log_prefix=log_prefix,
        skip_unchanged_objects=True,
    )
