import itertools

from django.contrib import admin

from firefly.core.utils.admin import link_field
from firefly.modules.firefly_django.admin import BaseModelV3AdminMixin, BaseTabularMixin
from firefly.modules.network.models import (
    Availability,
    ContactInformation,
    CuratedProvider,
    CuratedProviderPartnership,
    FireflyNearbyCoverage,
    HealthPlanConfigurationPartnership,
    Partnership,
    PartnershipContract,
    PartnershipInsurancePayer,
    PartnershipRecord,
    PartnershipServicingStateExclusion,
    ProviderRecommendationAnalysis,
    Ranking,
    TaxIdentifier,
)
from firefly.modules.referral.models import (
    ClinicalFocusAreaSpecialtyGroup,
    PartnershipClinicalFocusArea,
    PartnershipService,
    ServiceCPTCode,
    ServiceSpecialtyGroupLocationType,
)


class ContactInformationInline(BaseTabularMixin):
    model = ContactInformation


class AvailabilityInline(BaseTabularMixin):
    model = Availability


class RankingInline(BaseTabularMixin):
    model = Ranking


class CuratedProviderPartnershipInline(BaseTabularMixin):
    model = CuratedProviderPartnership


class PartnershipServiceInline(BaseTabularMixin):
    model = PartnershipService


class PartnershipClinicalFocusAreaInline(BaseTabularMixin):
    model = PartnershipClinicalFocusArea


class HealthPlanConfigurationPartnershipInline(BaseTabularMixin):
    model = HealthPlanConfigurationPartnership


@admin.register(CuratedProvider)
class CuratedProviderAdmin(BaseModelV3AdminMixin):
    search_fields = ["npi", "id", "care_org_name"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "npi",
                "care_org_name",
                "sanitized_care_org_name",
                "address_line_1",
                "address_line_2",
                "city",
                link_field("state"),
                "zip_code",
            ],
        )
    )
    inlines = [
        ContactInformationInline,
        AvailabilityInline,
        RankingInline,
        CuratedProviderPartnershipInline,
    ]


@admin.register(Partnership)
class PartnershipAdmin(BaseModelV3AdminMixin):
    search_fields = ["partner_name"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "partner_name",
                "is_active",
                "agreement_type",
                "is_valid_for_all_zipcodes",
                "partnership_type",
                "description",
            ],
        )
    )

    inlines = [PartnershipServiceInline, PartnershipClinicalFocusAreaInline, HealthPlanConfigurationPartnershipInline]


@admin.register(PartnershipService)
class PartnershipServiceAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("service"),
                link_field("partnership"),
            ],
        )
    )


@admin.register(PartnershipClinicalFocusArea)
class PartnershipClinicalFocusAreaAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("clinical_focus_area"),
                link_field("partnership"),
            ],
        )
    )


@admin.register(CuratedProviderPartnership)
class CuratedProviderPartnershipAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("curated_provider", display_field="care_org_name"),
                link_field("partnership", display_field="partner_name"),
                "partnership_level",
            ],
        )
    )


@admin.register(ContactInformation)
class ContactInformationAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("curated_provider"),
                link_field("specialty_group"),
                "phone",
                "fax",
                "is_verified",
            ],
        )
    )


@admin.register(Availability)
class AvailabilityAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("curated_provider"),
                link_field("specialty_group"),
                "number_of_days_till_next_availability",
                "added_at",
                "does_provider_exist",
            ],
        )
    )


@admin.register(Ranking)
class RankingAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("curated_provider"),
                "ranking_level",
                "reason",
                "rank_change",
            ],
        )
    )


@admin.register(PartnershipRecord)
class PartnershipRecordAdmin(BaseModelV3AdminMixin):
    search_fields = ["npi", "id", "care_org_name", "partner_name", "address_line_1"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "npi",
                "care_org_name",
                "address_line_1",
                "address_line_2",
                "city",
                link_field("state"),
                "zip_code",
                "partner_name",
                "partnership_level",
                "agreement_type",
                "is_processed",
                "is_tagged_as_partner",
                "can_accept_care_members",
                "can_accept_coverage_members",
                "can_accept_care_and_coverage_members",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            [
                "partnership_level",
                "partner_name",
                "import_job_name",
                "is_tagged_as_partner",
                "is_processed",
            ],
        )
    )


@admin.register(ServiceSpecialtyGroupLocationType)
class ServiceSpecialtyGroupLocationTypeAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [link_field("service"), link_field("specialty_group"), link_field("location_type"), "import_job_name"],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            [
                "import_job_name",
            ],
        )
    )


@admin.register(ClinicalFocusAreaSpecialtyGroup)
class ClinicalFocusAreaSpecialtyGroupAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [link_field("clinical_focus_area"), link_field("specialty_group"), "import_job_name"],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            [
                "import_job_name",
            ],
        )
    )


@admin.register(PartnershipInsurancePayer)
class PartnershipInsurancePayerAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("partnership"),
                link_field("insurance_payer"),
                link_field("state"),
                "insurance_plan_type",
                "group_number",
                "import_job_name",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            ["import_job_name", "partnership"],
        )
    )


@admin.register(PartnershipContract)
class PartnershipContractAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [link_field("partnership"), link_field("contract"), "is_primary_subscriber_only", "import_job_name"],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            ["import_job_name", "partnership"],
        )
    )


@admin.register(PartnershipServicingStateExclusion)
class PartnershipServicingStateExclusionAdmin(BaseModelV3AdminMixin):
    search_fields = ["state__abbreviation"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [link_field("partnership"), link_field("state")],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            ["partnership"],
        )
    )


@admin.register(FireflyNearbyCoverage)
class FireflyNearbyCoverageAdmin(BaseModelV3AdminMixin):
    search_fields = ["zip_code"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "import_job_name",
                "target_name",
                "zip_code",
                "member_count",
                "is_processed",
                "coverage_range_in_miles",
                "has_urgent_care_within_range",
                "has_in_home_services_within_range",
                "has_lab_within_range",
                "has_ffnb_within_range",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            ["import_job_name"],
        )
    )


@admin.register(ProviderRecommendationAnalysis)
class ProviderRecommendationAnalysisAdmin(BaseModelV3AdminMixin):
    search_fields = ["zip_code", "npi"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                "target_name",
                "npi",
                "zip_code",
                "is_processed",
                "recommendation_status",
                "in_network",
                "composite_score",
                "import_job_name",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            ["import_job_name"],
        )
    )


@admin.register(HealthPlanConfigurationPartnership)
class HealthPlanConfigurationPartnershipAdmin(BaseModelV3AdminMixin):
    search_fields = ["health_plan_configuration", "partnership"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [
                link_field("health_plan_configuration"),
                link_field("partnership", display_field="partner_name"),
                "point_solution_url",
                "point_solution_type",
            ],
        )
    )


@admin.register(TaxIdentifier)
class TaxIdentifierAdmin(BaseModelV3AdminMixin):
    search_fields = ["id", "tin"]
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            ["tin", "name", "legal_name", "address", "confirmed"],
        )
    )


@admin.register(ServiceCPTCode)
class ServiceCPTCodeAdmin(BaseModelV3AdminMixin):
    list_display = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_display,
            [link_field("service"), link_field("cpt_code"), "import_job_name"],
        )
    )
    list_filter = tuple(
        itertools.chain(
            BaseModelV3AdminMixin.list_filter,
            [
                "import_job_name",
            ],
        )
    )
