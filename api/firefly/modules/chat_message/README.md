# Chat Messages

The Chat Messages module provides real-time messaging capabilities between members and their care teams, enabling secure, HIPAA-compliant communication.

## Overview

Chat messaging is one of the primary communication channels between members and the Firefly care team. The system provides real-time delivery, message persistence, and comprehensive search capabilities.

## Key Models

### ChatMessageV2
Individual message model containing:
- **Content**: Message text and metadata
- **Sender**: User who sent the message (member or provider)
- **Timestamp**: When the message was sent
- **Thread**: Associated conversation thread
- **Message Type**: Text, image, document, or system message

### ChatThread
Conversation thread model representing:
- **Participants**: Member and assigned care team providers
- **Thread ID**: Unique identifier for the conversation
- **Last Activity**: Most recent message timestamp
- **Status**: Active, archived, or closed
- **Metadata**: Additional thread information

## Architecture

The chat system combines Django persistence with PubNub for real-time delivery:
- **Django Backend**: Message storage and retrieval
- **PubNub**: Real-time message broadcasting
- **Elasticsearch**: Message search and indexing
- **WebSocket Connections**: Live message delivery

### Retrieving messages

This is the case where a user loads a Chat Thread, or the messages tab with multiple Chat Thread, and needs to retrieve all the messages in those Threads.

Chat data coming from the back-end may include data about the patient's state so that extra metadata (e.g. the patient's onboarding state) can be displayed in the front-end to help us triage their messages.

### Sending messages, real-time notifications

This is the case where a user has already loaded a Chat Thread and a new real-time message is received.

1. A front-end client sends a message. This might be a patient sending a message from the mobile app, or a Firefly provider sending a message from our internal Lucian front-end
2. The message data is sent to PubNub via PubNub's JS library
3. PubNub transforms the message data. For a message sent by a patient, this includes fetching patient metadata (name, onboarding state, etc.) from the Lucian back-end and appending it to the message data
4. PubNub publishes / broadcasts the transformed message data
5. Clients subscribe to PubNub and receive the transformed message data. This works in 2 different ways:

   a) Front-ends receive the event, and treat the event as a real-time notification. The message data shows up as a new message if a User happens to be viewing the corresponding Chat Thread. In order to power the same UX as the one outlined in the basic [Retrieving messages](###Retrieving-messages) case, this implies that the metadata coming from PubNub needs to match the metadata that we serialize directly from the back-end

   b) The Lucian back-end receives the event, and persists the message data in the database using the ChatMessageV2 model. Persisting the data allows us to later fetch and display the message

For more details, see [this Whimsical diagram](https://whimsical.com/messaging-ux-changes-dra-1092-Ya9jvFYC7RiqTb45RLckuq)

## Search

Having one chat thread for all clinical related, engagement related messages makes it hard to know if something was already talked about with this patient or someone has already chimed in. Message search allows Lucian users to look through all past messages and quickly jump to where the keyword appeared in the thread.

## Message Search

The system provides full-text search capabilities across all message history, allowing care team members to quickly find previous conversations and context.

### Search Implementation
- **Elasticsearch Integration**: Messages indexed for fast search
- **Real-time Indexing**: New messages automatically indexed
- **Periodic Reindexing**: Full reindex every 2 days at 4 AM
- **Search Alias**: Uses `chat_message` alias for consistent access

### Key Files
```
firefly/modules/chat_message/management/commands/reindex_chat_messages.py
firefly/modules/chat_message/search.py
firefly/modules/chat_message/signals.py
firefly/modules/chat_message/tasks.py
```

### Search Features
- **Keyword Search**: Find messages containing specific terms
- **Thread Context**: Jump directly to relevant conversation points
- **Historical Access**: Search across all past conversations
- **Provider Efficiency**: Quickly check if topics were previously discussed

### How to Test in Local:

Set the `ELASTICSEARCH_DEBUG` settings variable to `True`

Next, check that your elasticsearch configs are not commented out in `docker-compose.override.yaml`

```
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:6.8.3
    container_name: elasticsearch
    environment:
      - cluster.name=docker-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - esdata1:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
```

Restart your docker and make sure you see `elasticsearch` coming up

Follow [Elasticsearch docs](https://www.elastic.co/guide/en/elasticsearch/reference/current/indices.html):

```
http://localhost:9200/_cat/indices
```

### How to reindex messages:

- Run django command through lucible (or docker in local)

```
lucible ssh prod -- python manage.py reindex_chat_messages
```

### How to see current state in env other than local:

- Connect to the cluster in an ephemeral container:

```
lucible ssh prod
```

- Make a request to see all indices
- The alias should always be `chat_message`

```
curl -v https://es-prod.i.firefly.health/_alias
```

### How to delete indices:

- You can specify the specific index (also takes a list), or do `chat_message\*` to delete them in bulk

```
curl -X DELETE https://es-prod.i.firefly.health/chat_message\*
```

## Message Filtering

`firefly/modules/chat_message/api - get_threads()` will explain how we filter each message bucket

`firefly/core/user/api/crud - UserListView`
If filtered states is [] show all patients otherwise filter by the `Insurance Member Info - State field`. Note: This field is a Charfield length 2 of the abbreviation.
