import logging
from typing import ClassV<PERSON>, Generic, List, TypeVar

import shortuuid
from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON><PERSON><PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Q
from django.utils import timezone
from imagekit.models import ImageS<PERSON>Field
from imagekit.processors import ResizeToFit, Transpose
from safedelete.config import HARD_DELETE_NOCASCADE

from firefly.modules.firefly_django.models import (
    BaseModelV3,
    BaseModelV3ManyToManyField,
    SafeDeleteManagerWithBulkUpdate,
)
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import is_current_enrollment_status
from firefly.modules.tenants.models import Tenant

T = TypeVar("T", bound="ChatThread")
logger = logging.getLogger(__name__)


class ChatThreadQuerySet(models.QuerySet, Generic[T]):
    def for_onboarding(self):
        return self.filter(patient__onboarding_state__status=OnboardingStatus.SIGNEDUP) or is_current_enrollment_status(
            self.model.patient.person, ProgramCodes.PRIMARY_CARE, PrimaryCareProgramStatus.CHURNED
        )


class ChatThreadManager(SafeDeleteManagerWithBulkUpdate["ChatThread"]):
    def get_queryset(self):
        return ChatThreadQuerySet(self.model, using=self._db)

    def for_onboarding(self):
        return self.get_queryset().for_onboarding()


class MessageTag(BaseModelV3):
    ONBOARDING = "onboarding"
    CLINICAL = "clinical"
    OPERATIONAL = "operational"

    # make tag case-insensitive at the db level
    tag = models.TextField(db_collation="case_insensitive")  # Case-insensitive collation

    class Meta:
        db_table = "message_tag"


def _get_shortuuid():
    return shortuuid.uuid()


class ChatThread(BaseModelV3):
    # Unique ID in shortuuid format. Used as part of the Pubnub channel name.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=31, db_index=True, default=_get_shortuuid)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255)  # noqa: TID251
    description = models.TextField(null=True, blank=True)

    objects: ClassVar[ChatThreadManager] = ChatThreadManager()

    # Used to designate different types of threads, including "default" or "care_team"
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(max_length=31, null=True, blank=True, db_index=True)  # noqa: TID251

    patient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="chat_threads")

    # TODO: make this field mandatory after backfill
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    tenant = models.ForeignKey(Tenant, related_name="chat_threads", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    # Temporary property to maintain backwards compatibility w/ read receipts.
    # Holds the pk of the last read message in thread.
    # When a thread is marked as unread, this will be reset to null.
    # When a thread is marked as read, this will be set to the time of the most recent message
    # in the thread.
    last_read_at = models.DateTimeField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    last_message = models.ForeignKey("ChatMessageV2", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    last_read_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # This flag indicates chat thread is read or unread.
    # If the patient sends a message mark this as True
    # If the clinician sends a message mark this as False
    # If Lucian bot sends a message do nothing
    is_unread = models.BooleanField(blank=True, null=True, default=False)

    class Meta:
        db_table = "chat_thread"
        unique_together = ["uid", "tenant"]

    @property
    def pubnub_channel(self):
        """Get PubNub channel name for thread."""
        return f"thread.{self.uid}"

    def get_last_message(self):
        """
        Query for the last saved chat message on the thread, which may be
        different from last_message if the thread has not yet been updated.
        """
        return (
            ChatMessageV2.objects.filter(thread=self, deleted__isnull=True)
            # Note: This assumes messages are created in the order they are
            # sent.
            # ID is a monotonically increasing sequence and breaks the tie if
            # multiple messages are created at the same time.
            .order_by("-created_at", "-id")
            .first()
        )


class ChatAttachment(BaseModelV3):
    """An attachment (image, PDF) in a chat thread."""

    file = models.FileField(upload_to="chat-attachment/")
    thumbnail = ImageSpecField(
        source="file",
        processors=[Transpose(), ResizeToFit(600, 600)],
        format="JPEG",
        options={"quality": 80},
    )

    class Meta(BaseModelV3.Meta):
        db_table = "chat_attachment"


class ChatMessageV2(SaveHandlersMixin, BaseModelV3):
    """A rich chat message."""

    # Unique ID set by _client_ in UUID4 format upon sending a message.
    #   As such, no default function is set here and the field is required in the API.
    # Pubnub logic will send errors to Bugsnag when messages are sent w/out UID.
    # This UID keys message creates/edits/deletes across Pubnub, frontends, and API.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, db_index=True, unique=True)  # noqa: TID251

    text = models.TextField(blank=True, default="")
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    thread = models.ForeignKey(ChatThread, related_name="messages", on_delete=models.CASCADE)

    sent_at = models.DateTimeField(default=timezone.now, null=True, blank=True, db_index=True)
    edited_at = models.DateTimeField(null=True, blank=True)
    deleted_at = models.DateTimeField(null=True, blank=True)
    push_sent_at = models.DateTimeField(null=True, blank=True)
    patient_read_at = models.DateTimeField(null=True, blank=True, db_index=True)
    patient_reminder_sent_at = models.DateTimeField(null=True, blank=True, db_index=True)
    patient_reminder_24_hrs_sent_at = models.DateTimeField(null=True, blank=True, db_index=True)

    message_tags = BaseModelV3ManyToManyField(
        MessageTag, related_name="chat_messages", blank=True, through="ChatMessageV2MessageTags"
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # Overrides BaseModel created_at to have an index
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    class MessageTypes:
        TEXT = "text"  # A basic text message with optional attachments.
        INTEGRATION = "integration"  # An integration (link to questionnaire, vitals collection, etc...)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(  # noqa: TID251
        max_length=15,
        db_index=True,
        default=MessageTypes.TEXT,
        choices=(
            (MessageTypes.TEXT, MessageTypes.TEXT),
            (MessageTypes.INTEGRATION, MessageTypes.INTEGRATION),
        ),
    )

    # Holds extra metadata for complex message types (integrations, etc...)
    metadata = JSONField(blank=True, default=dict)

    # TODO: Merge with file_attachment field and deprecate
    attachment = models.ForeignKey(
        ChatAttachment,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="message",
    )
    file_attachment = models.ForeignKey(
        "documents.PatientSharedFile",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="message",
    )

    searchable = models.BooleanField(default=True, null=True)

    # Related cases tied to the chat message.
    case_relations = GenericRelation("cases.CaseRelation")

    # Related tasks tied to the chat message. Superseded by case_relations, these exist only to display historical
    # tasks.
    task_relations = GenericRelation("tasks.TaskRelation")

    # ML urgency
    urgency_score = models.FloatField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    urgency_score_version = models.CharField(max_length=30, null=True, blank=True)  # noqa: TID251
    is_urgent_message = models.BooleanField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    urgent_chat_token_matches = ArrayField(null=True, blank=True, size=255, base_field=models.CharField(max_length=255))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    urgent_chat_token_version = models.CharField(max_length=30, null=True, blank=True)  # noqa: TID251

    # ML model outputs
    # {<model_name_version>: <score between 0-1>}
    machine_learning_outputs = models.JSONField(null=True, blank=True)

    @property
    def patient_user_id(self):
        return self.thread.patient_id

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.chat_message.tasks import send_slack_alert_urgency_score
        from firefly.modules.features.constants import Features
        from firefly.modules.features.utils import get_feature_access_for_user_v2
        from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY

        self.recompute_thread_last_message()
        if changed("id") and self.sender.is_patient:
            tenant_feature_access = get_feature_access_for_user_v2(
                self.sender, provider_context=False, patient_user_tenant_key=FIREFLY_TENANT_KEY
            )["tenant"][Features.machine_learning_chat_triage.name]

            if tenant_feature_access:
                # Temporary log to track urgency score
                logger.info(
                    "Chat Urgency Model: ID %s saved with urgency score %s",
                    self.pk,
                    self.urgency_score,
                )
                send_slack_alert_urgency_score.send_with_options(args=(self.pk,))

    def recompute_thread_last_message(self):
        thread = self.thread
        if self.deleted is not None and thread.last_message.id == self.id:
            thread.last_message = thread.get_last_message()
            thread.save()
        elif (not thread.last_message) or (thread.last_message.id < self.id):
            thread.last_message = self
            thread.save()

    class Meta:
        db_table = "chat_message"


class ChatMessageV2MessageTags(BaseModelV3):
    # This is pre-existing behavior; we make this continue hard deleting while we
    # figure out a way to filter out hard deleted rows in a performant manner
    _safedelete_policy = HARD_DELETE_NOCASCADE
    chatmessagev2 = models.ForeignKey(
        ChatMessageV2,
        related_name="chatmessagev2_messagetags",
        on_delete=models.CASCADE,
    )

    messagetag = models.ForeignKey(
        MessageTag,
        related_name="chatmessagev2_messagetags",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "chat_message_message_tags"
        verbose_name_plural: str = "Chat message v2 message tags"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["chatmessagev2", "messagetag"], condition=Q(deleted=None), name="chatmessagev2_messagetag_uniq"
            )
        ]


class ChatThreadMembership(BaseModelV3):
    """Represents a user's membership in a chat thread and also stores read receipts."""

    thread = models.ForeignKey(ChatThread, on_delete=models.CASCADE, related_name="members")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="chat_thread_memberships")

    # Who and when added this user to this thread
    added_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="+",
    )
    added_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "chat_thread_membership"
        constraints = [
            models.UniqueConstraint(fields=["thread", "user"], condition=Q(deleted=None), name="thread_user_uniq")
        ]


class ChatMessageTemplate(BaseModelV3):
    """A template for a chat message."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    uid = models.CharField(max_length=63, db_index=True, unique=True)  # noqa: TID251
    description = models.TextField(blank=True, default="")

    # Jinja2 template for message
    template_text = models.TextField()
