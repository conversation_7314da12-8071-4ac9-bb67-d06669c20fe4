import logging
from typing import List, <PERSON>

from rest_framework import serializers
from rest_framework.relations import PrimaryKey<PERSON>elatedField

from firefly.core.redact_phi.serializers import RedactPHIListSerializer
from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.user.serializers import IDNameUserSerializer
from firefly.modules.cases.serializers import CaseSerializer
from firefly.modules.chat_message.models import (
    Chat<PERSON><PERSON>chment,
    ChatMessageV2,
    ChatMessageV2MessageTags,
    ChatThread,
    ChatThreadMembership,
    MessageTag,
)
from firefly.modules.chat_message.utils import clinician_has_chatted_with_patient, is_urgent_message
from firefly.modules.documents.models import PatientSharedFile
from firefly.modules.documents.serializers import LimitedPatientSharedFileSerializer
from firefly.modules.firefly_django.serializers import BaseSerializerV3
from firefly.modules.programs.utils import insurance_details_for_person
from firefly.modules.statemachines.coverage.serializers import CoverageSerializer
from firefly.modules.tenants.utils import get_tenant_key_from_request

logger = logging.getLogger(__name__)


class ChatThreadMembershipSerializer(BaseSerializerV3):
    thread: "serializers.SlugRelatedField[ChatThread]" = serializers.SlugRelatedField(
        slug_field="uid", queryset=ChatThread.objects
    )

    class Meta(BaseSerializerV3.Meta):
        model = ChatThreadMembership
        fields = BaseSerializerV3.Meta.fields + ["id", "user", "thread", "last_read_at"]


class ChatThreadSerializer(BaseSerializerV3):
    members = ChatThreadMembershipSerializer(many=True, read_only=True)
    clinician_has_chatted_with_patient = serializers.SerializerMethodField()

    def get_clinician_has_chatted_with_patient(self, thread):
        tenant_key = get_tenant_key_from_request(self.context["request"])
        return clinician_has_chatted_with_patient(
            clinician=self.context["request"].user, patient=thread.patient, tenant_key=tenant_key
        )

    class Meta(BaseSerializerV3.Meta):
        model = ChatThread
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + ["clinician_has_chatted_with_patient"]
        fields = BaseSerializerV3.Meta.fields + [
            "uid",
            "name",
            "description",
            "type",
            "patient",
            "members",
            "last_read_at",
            "is_unread",
            "last_read_by",
            "clinician_has_chatted_with_patient",
            "tenant",
        ]


class MessageTagSerializer(BaseSerializerV3):
    class Meta(BaseSerializerV3.Meta):
        model = MessageTag
        fields: List[str] = BaseSerializerV3.Meta.fields + [
            "tag",
        ]


class ChatMessageV2CaseSerializer(CaseSerializer):
    class Meta(CaseSerializer.Meta):
        fields: List[str] = BaseSerializerV3.Meta.fields + [
            "status",
            "status_category",
            "person",
            "category",
            "description",
            "tasks",
            "relations",
        ]


class ChatMessageV2CasesField(serializers.Field):
    def to_representation(self, value):
        related_cases = value.all()
        serializer = ChatMessageV2CaseSerializer([relation.case for relation in related_cases], many=True)
        return serializer.data

    def to_internal_value(self, data):
        return data


class BaseChatMessageV2Serializer(BaseSerializerV3):
    # Allow text to be null (defaults to "") for attachment-only messages.
    text = serializers.CharField(required=False, allow_blank=True, allow_null=True, default="")
    is_urgent = serializers.SerializerMethodField(read_only=True)
    message_tags = MessageTagSerializer(many=True, required=False)
    # TODO: Merge with file_attachment field and deprecate
    attachment: PrimaryKeyRelatedField = serializers.PrimaryKeyRelatedField(
        queryset=ChatAttachment.objects.all(), required=False, allow_null=True
    )

    def get_is_urgent(self, instance) -> bool:
        return is_urgent_message(instance)


class ChatMessageV2MessageTagsSerializer(BaseSerializerV3):
    messagetag = MessageTagSerializer()

    class Meta(BaseSerializerV3.Meta):
        model = ChatMessageV2MessageTags
        fields = BaseSerializerV3.Meta.read_only_fields + ["messagetag"]


class BaseChatMessageV3Serializer(BaseSerializerV3):
    # Allow text to be null (defaults to "") for attachment-only messages.
    text = serializers.CharField(required=False, allow_blank=True, allow_null=True, default="")
    is_urgent = serializers.SerializerMethodField(read_only=True)
    chatmessagev2_messagetags = ChatMessageV2MessageTagsSerializer(many=True, required=False)
    # TODO: Merge with file_attachment field and deprecate
    attachment: PrimaryKeyRelatedField = serializers.PrimaryKeyRelatedField(
        queryset=ChatAttachment.objects.all(), required=False, allow_null=True
    )

    def get_is_urgent(self, instance) -> bool:
        return is_urgent_message(instance)


class ChatMessageV2Serializer(BaseChatMessageV2Serializer):
    thread: "serializers.SlugRelatedField[ChatThread]" = serializers.SlugRelatedField(
        slug_field="uid", queryset=ChatThread.objects
    )

    file_attachment: serializers.PrimaryKeyRelatedField = serializers.PrimaryKeyRelatedField(
        queryset=PatientSharedFile.objects.all(), required=False, allow_null=True
    )

    cases = ChatMessageV2CasesField(source="case_relations", read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = ChatMessageV2
        fields = [
            "id",
            "uid",
            "text",
            "is_urgent",
            "is_urgent_message",
            "sender",
            "thread",
            "sent_at",
            "edited_at",
            "deleted_at",
            "push_sent_at",
            "patient_read_at",
            "message_tags",
            "metadata",
            "attachment",
            "event",
            "file_attachment",
            "cases",
            "urgency_score",
            "urgency_score_version",
            "urgent_chat_token_matches",
            "urgent_chat_token_version",
        ]

    def create(self, validated_data: dict):
        """Create or update message w/ UID if one already exists."""
        # Log incoming message data, without the text for privacy reasons
        log_data = validated_data.copy()
        log_data.pop("text")
        logger.info("ChatMessageV2Serializer.create: %s", log_data)

        validated_data["text"] = validated_data["text"] or ""
        message_tags_data = validated_data.pop("message_tags", None)
        message, _ = ChatMessageV2.objects.update_or_create(uid=validated_data["uid"], defaults=validated_data)
        if message_tags_data is not None and len(message_tags_data) > 0:
            message_tags = []
            for message_tag_data in message_tags_data:
                if "id" in message_tag_data:
                    message_tag = MessageTag.objects.get(
                        id=message_tag_data["id"],
                    )
                else:
                    # Although tag is not guaranteed to be unique at the
                    # database level, let's attempt to avoid creating
                    # unnecessary MessageTag objects.
                    message_tag, _ = MessageTag.objects.get_or_create(
                        tag=message_tag_data["tag"],
                    )

                message_tags.append(message_tag)

            message.message_tags.set(message_tags)
        else:
            if validated_data["thread"].patient_id == validated_data["sender"].id:
                # A patient-sent message with no tags? This is dangerous because it will likely be missed
                # by users performing triage who are working out of either the Operational or Clinical buckets
                # This may be a result of a failure upstream in PubNub functions
                # TODO: automatically choose the operational tag?
                logger.error("Creating ChatMessageV2 with no message tags: %s", log_data)
        return message

    def update(self, instance, validated_data):
        # Handle nested updates of message tags using JSON Merge Patch semantics
        # (full replacement).
        # See https://datatracker.ietf.org/doc/html/rfc7396.
        message_tags_data = validated_data.pop("message_tags", None if self.partial else [])
        instance = super(ChatMessageV2Serializer, self).update(instance, validated_data)

        if message_tags_data is not None:
            message_tags = []
            for message_tag_data in message_tags_data:
                if "id" in message_tag_data:
                    message_tag = MessageTag.objects.get(
                        id=message_tag_data["id"],
                    )
                else:
                    # Although tag is not guaranteed to be unique at the
                    # database level, let's attempt to avoid creating
                    # unnecessary MessageTag objects.
                    message_tag, created = MessageTag.objects.get_or_create(
                        tag=message_tag_data["tag"],
                    )

                message_tags.append(message_tag)

            instance.message_tags.set(message_tags)

        return instance

    def to_representation(self, instance):
        self.fields["file_attachment"] = LimitedPatientSharedFileSerializer(required=False, allow_null=True)
        return super(ChatMessageV2Serializer, self).to_representation(instance)


class ChatMessageV3Serializer(BaseChatMessageV3Serializer):
    thread: "serializers.SlugRelatedField[ChatThread]" = serializers.SlugRelatedField(
        slug_field="uid", queryset=ChatThread.objects
    )

    file_attachment: Union[serializers.PrimaryKeyRelatedField, LimitedPatientSharedFileSerializer] = (
        serializers.PrimaryKeyRelatedField(queryset=PatientSharedFile.objects.all(), required=False, allow_null=True)
    )

    cases = ChatMessageV2CasesField(source="case_relations", read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = ChatMessageV2
        fields = [
            "id",
            "uid",
            "text",
            "is_urgent",
            "is_urgent_message",
            "sender",
            "thread",
            "sent_at",
            "edited_at",
            "deleted_at",
            "push_sent_at",
            "patient_read_at",
            "chatmessagev2_messagetags",
            "metadata",
            "attachment",
            "event",
            "file_attachment",
            "cases",
            "urgency_score",
            "urgency_score_version",
            "urgent_chat_token_matches",
            "urgent_chat_token_version",
        ]

    def create(self, validated_data: dict):
        """Create or update message w/ UID if one already exists."""
        # Log incoming message data, without the text for privacy reasons
        log_data = validated_data.copy()
        log_data.pop("text")
        logger.info("ChatMessageV2Serializer.create: %s", log_data)

        validated_data["text"] = validated_data["text"] or ""
        message_tags_data = validated_data.pop("message_tags", None)
        message, _ = ChatMessageV2.objects.update_or_create(uid=validated_data["uid"], defaults=validated_data)
        if message_tags_data is not None:
            message_tags = []
            for message_tag_data in message_tags_data:
                if "id" in message_tag_data:
                    message_tag = MessageTag.objects.get(
                        id=message_tag_data["id"],
                    )
                else:
                    # Although tag is not guaranteed to be unique at the
                    # database level, let's attempt to avoid creating
                    # unnecessary MessageTag objects.
                    message_tag, _ = MessageTag.objects.get_or_create(
                        tag=message_tag_data["tag"],
                    )

                message_tags.append(message_tag)

            message.message_tags.set(message_tags)
        return message

    def update(self, instance, validated_data):
        # Handle nested updates of message tags using JSON Merge Patch semantics
        # (full replacement).
        # See https://datatracker.ietf.org/doc/html/rfc7396.
        message_tags_data = validated_data.pop("chatmessagev2_messagetags", None if self.partial else [])
        instance = super(ChatMessageV3Serializer, self).update(instance, validated_data)

        if message_tags_data is not None:
            message_tags = []
            for message_tag_data in message_tags_data:
                messagetag = message_tag_data.get("messagetag")
                if messagetag:
                    if "id" in messagetag:
                        message_tag = MessageTag.objects.get(
                            id=messagetag["id"],
                        )
                    else:
                        # Although tag is not guaranteed to be unique at the
                        # database level, let's attempt to avoid creating
                        # unnecessary MessageTag objects.
                        message_tag, created = MessageTag.objects.get_or_create(
                            tag=messagetag["tag"],
                        )
                message_tags.append(message_tag)

            instance.message_tags.set(message_tags)

        return instance

    def to_representation(self, instance):
        self.fields["file_attachment"] = LimitedPatientSharedFileSerializer(required=False, allow_null=True)
        return super(ChatMessageV3Serializer, self).to_representation(instance)


class ChatMessageV3ResponseSerializer(ChatMessageV3Serializer):
    """
    Exists for Schema annotation purposes
    """

    file_attachment: LimitedPatientSharedFileSerializer = LimitedPatientSharedFileSerializer()


class ReadOnlyChatMessageV3Serializer(RedactPHIListSerializer, BaseChatMessageV3Serializer):
    """Similar to ChatMessageV2Serializer, but omits thread.

    Only used in ChatThreadWithHistorySerializer.
    """

    class Meta(RedactPHIListSerializer.Meta, BaseSerializerV3.Meta):
        model = ChatMessageV2
        fields = [
            "id",
            "uid",
            "text",
            "is_urgent",
            "is_urgent_message",
            "sender",
            "sent_at",
            "edited_at",
            "deleted_at",
            "push_sent_at",
            "patient_read_at",
            "chatmessagev2_messagetags",
            "metadata",
            "attachment",
            "event",
        ]
        redact_phi_field_paths = [["text"]]


class LimitedUserSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    first_name = serializers.CharField(source="firstname")
    last_name = serializers.CharField(source="lastname")


class PodFilterOptionSerializer(serializers.Serializer):
    name = serializers.CharField()
    id = serializers.CharField()
    np_provider_ids = serializers.ListField()


class ChatThreadWithHistorySerializerV2(RedactPHIListSerializer, BaseSerializerV3):
    messages = serializers.SerializerMethodField()
    patient = serializers.IntegerField(source="patient_id")
    person_id = serializers.IntegerField(source="patient.person.id")
    patient_meta = IDNameUserSerializer(source="patient")

    onboarding_status = serializers.SerializerMethodField()
    enrolled_in_primary_care_program = serializers.SerializerMethodField()
    enrolled_in_benefit_program = serializers.SerializerMethodField()
    coverage_state = CoverageSerializer(source="patient.person.coverage")
    address_state = serializers.SerializerMethodField()
    request_auth_for_phi = serializers.SerializerMethodField()
    patient_insurance_details = serializers.SerializerMethodField()
    care_team = serializers.SerializerMethodField()

    def get_address_state(self, obj):
        insurance_info = obj.patient.person.insurance_info if hasattr(obj.patient, "person") else None
        if insurance_info is None:
            return None
        return insurance_info.state

    def get_messages(self, thread):
        serializer = ReadOnlyChatMessageV3Serializer(
            thread.last_message,
            read_only=True,
        )
        serializer.set_force_redact_phi(self.get_request_auth_for_phi(thread))
        return [serializer.data]

    # See PaginatedChatThreadsListViewV4
    def get_enrolled_in_primary_care_program(self, obj):
        try:
            return obj.is_enrolled_in_primary_care_program
        except Exception:
            None

    # See PaginatedChatThreadsListViewV4
    def get_enrolled_in_benefit_program(self, obj):
        try:
            return obj.is_enrolled_in_benefit_program
        except Exception:
            None

    def get_onboarding_status(self, obj):
        try:
            return obj.onboarding_status
        except Exception:
            None

    def get_patient_insurance_details(self, obj):
        insurance_info = obj.patient.person.insurance_info if hasattr(obj.patient, "person") else None
        if insurance_info is None:
            return None
        return insurance_details_for_person(insurance_info)

    def get_care_team(self, obj):
        care_team_providers = obj.patient.person.care_team.all()

        ids = []
        np_id = None
        for provider in care_team_providers:
            ids.append(provider.user_id)
            if provider.internal_role and provider.internal_role.role_name == ROLE_VALUES.NP:
                np_id = provider.user_id
        return {"ids": ids, "nurse_practitioner": {"id": np_id}}

    def get_request_auth_for_phi(self, obj):
        return False if not hasattr(obj.patient, "person") else obj.patient.person.request_auth_for_phi

    class Meta(RedactPHIListSerializer.Meta, BaseSerializerV3.Meta):
        model = ChatThread
        fields = [
            "uid",
            "name",
            "description",
            "enrolled_in_primary_care_program",
            "enrolled_in_benefit_program",
            "type",
            "patient",
            "person_id",
            "patient_meta",
            "messages",
            "last_read_at",
            "is_unread",
            "onboarding_status",
            "address_state",
            "coverage_state",
            "request_auth_for_phi",
            "patient_insurance_details",
            "care_team",
            "tenant",
        ]
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + fields
        redact_phi_field_paths = [["patient_meta", "first_name"], ["patient_meta", "last_name"]]
        redact_phi_test_path = ["request_auth_for_phi"]


class ChatAttachmentSerializer(BaseSerializerV3):
    thumbnail = serializers.SerializerMethodField(read_only=True)

    def get_thumbnail(self, attachment):
        return attachment.thumbnail.url

    class Meta(BaseSerializerV3.Meta):
        model = ChatAttachment
        fields: List[str] = BaseSerializerV3.Meta.fields + ["file", "message", "thumbnail"]
        read_only_fields = BaseSerializerV3.Meta.read_only_fields


class ChatThreadsCountsSerializer(serializers.Serializer):
    all = serializers.IntegerField()
    patient_last_sent = serializers.IntegerField()
    unread = serializers.IntegerField()
    onboarding = serializers.IntegerField()
    clinical = serializers.IntegerField()
    operational = serializers.IntegerField()


class ChatUrgencyScoreSerializer(serializers.Serializer):
    urgency_score = serializers.FloatField()

    urgency_score_version = serializers.CharField(required=False, allow_blank=True, allow_null=True, default="")
    urgent_chat_token_matches = serializers.ListField(child=serializers.CharField())
    urgent_chat_token_version = serializers.CharField(required=False, allow_blank=True, allow_null=True, default="")
    is_urgent_message = serializers.BooleanField()
    message_tags = MessageTagSerializer(many=True, required=False)
