from django.db import models
from django.db.models import J<PERSON><PERSON><PERSON>

from firefly.modules.firefly_django.models import BaseModelV3


# Practical application of Singleton design pattern in Django
# https://link.medium.com/Dqt8veH5y4
class SingletonModel(BaseModelV3):
    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        """Direct all saves to pk=1"""
        self.pk = 1
        super(SingletonModel, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """No-op delete"""
        pass

    @classmethod
    def load(cls):
        """Helper method that returns active singleton"""
        obj, created = cls.objects.get_or_create(pk=1)
        return obj


class ServerSetting(SingletonModel):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    onboarding_guide = models.ForeignKey(to="user.User", on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251

    # Keys are keys in OnboardingGuideNotifier.MESSAGE_TYPES
    # Values are Booleans
    onboarding_guide_notifications = JSONField(default=dict)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    app_min_ios_version = models.CharField(  # noqa: TID251
        max_length=15,
        default="1.30",
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    app_min_android_version = models.CharField(  # noqa: TID251
        max_length=15,
        default="1.30",
    )
