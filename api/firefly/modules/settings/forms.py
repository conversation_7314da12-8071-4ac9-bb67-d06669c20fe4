"""Allow selection of a user from a queryset."""

from django import forms

from .models import ServerSetting
from .querysets import ONBOARDING_QUERYSET


class NamedUserChoiceField(forms.ModelChoiceField):
    """Define how to set a user label for a choice field"""

    def label_from_instance(self, obj):
        return f"{obj.firstname} {obj.lastname}"


class ServerSettingForm(forms.ModelForm):
    """Allow setting of onboarding user from django admin"""

    class Meta:
        model = ServerSetting
        fields = (
            "onboarding_guide",
            "onboarding_guide_notifications",
            "app_min_ios_version",
            "app_min_android_version",
        )

    onboarding_guide = NamedUserChoiceField(queryset=ONBOARDING_QUERYSET, required=False)
