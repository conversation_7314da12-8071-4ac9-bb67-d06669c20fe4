from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.modules.referral.factories import ReferralFactory, SteerageFactory
from firefly.modules.referral.models import ReferralPriority


class ReferralTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

    def referral_priority(self):
        # For each of the test cases, try to include something of a lower-priority
        # in the body as well, to show that the highest priority gets caught first.
        urgent_referral = ReferralFactory(body="I am urgently referring this patient to you, this is time sensitive")
        self.assertEqual(urgent_referral.referral_priority, ReferralPriority.URGENT)

        medium_referral = ReferralFactory(body="I am referring this patient for a time sensitive issue")
        self.assertEqual(medium_referral.referral_priority, ReferralPriority.MEDIUM)

        standard_referral = ReferralFactory(body="I am referring this patient to see you")
        self.assertEqual(standard_referral.referral_priority, ReferralPriority.STANDARD)

        unknown_referral = ReferralFactory(body="I am a doctor")
        self.assertEqual(unknown_referral.referral_priority, ReferralPriority.STANDARD)

        blank_body_referral = ReferralFactory(body="")
        self.assertEqual(blank_body_referral.referral_priority, ReferralPriority.STANDARD)

        empty_body_referral = ReferralFactory(body=None)
        self.assertEqual(empty_body_referral.referral_priority, ReferralPriority.STANDARD)

    def test_soonest_available_referral_priority(self, *args):
        medium_referral = ReferralFactory(body="I am referring this patient for a time sensitive issue")

        steerage = SteerageFactory(referral=medium_referral)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["id"], steerage_id)

        # Assert the soonest Available
        self.assertEqual(response.json()["referral"]["referral_priority"], "Soonest Available")

    def test_referral_body_html_entities(self):
        body = """
        Here's a referral body!
        Sometimes there are funky characters in here like &#x27; (apostrophe)
        Example usage: We couldn&#x27;t find someone &amp; something
        Here are some more common HTML entities:
        &lt; - less than
        &gt; - greater than
        &quot; - double quote
        &apos; - single quote
        &amp; - ampersand
        &copy; - copyright symbol
        &reg; - registered trademark symbol
        &trade; - trademark symbol
        """
        referral = ReferralFactory(body=body)
        steerage = SteerageFactory(referral=referral)
        response = self.provider_client.get(
            f"/referral/steerage/{steerage.id}/",
            format="json",
        )

        unescaped_body = """
        Here's a referral body!
        Sometimes there are funky characters in here like ' (apostrophe)
        Example usage: We couldn't find someone & something
        Here are some more common HTML entities:
        < - less than
        > - greater than
        " - double quote
        ' - single quote
        & - ampersand
        © - copyright symbol
        ® - registered trademark symbol
        ™ - trademark symbol
        """
        self.assertEqual(response.json()["referral"]["body"], unescaped_body)
