import hashlib
import json
from datetime import timed<PERSON><PERSON>

from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from faker import Faker

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP
from firefly.core.user.models.models import AssigneeGroup
from firefly.modules.cases.constants import REFERRAL_REQUEST
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import CaseCategory, CaseRelation
from firefly.modules.facts.factories import SpecialtyGroupFactory
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.referral.constants import (
    MemorialHermannConfig,
)
from firefly.modules.referral.factories import (
    ReferralRecipientFactory,
    SteerageProviderFactory,
)
from firefly.modules.referral.models import (
    Referral,
    ReferralInsuranceAuthICDCode,
    ReferralPriority,
    Steerage,
    VendorTransmissionStatusChoices,
)
from firefly.modules.referral.tests.test_steerage_utils import (
    set_up_steerage_affected_case_categories,
)
from firefly.modules.referral.utils.referral_utils import (
    get_mh_referral_row,
    maybe_queue_for_transmission,
)
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.states.models import State
from firefly.modules.work_units.constants import StatusCategory


def set_up_referral_affected_case_categories(testCaseSelf):
    # A super basic, no-frills state machine content diagram
    state_machine_content = {
        "state_with_categories": [
            {"state": {"name": "Done"}, "category": StatusCategory.COMPLETE},
            {"state": {"name": "Draft"}, "category": StatusCategory.NOT_STARTED},
            {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
        ],
        "initial_state": "Draft",
        "transitions": [
            {
                "trigger": "Draft",
                "source": ["Will Not Do"],
                "dest": "Draft",
            },
            {
                "trigger": "Done",
                "source": ["Draft"],
                "dest": "Done",
            },
            {
                "trigger": "Will Not Do",
                "source": ["Draft"],
                "dest": "Will Not Do",
            },
        ],
    }
    state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
        # Both title and content are unique, so ensure title is deterministic
        # based on content to avoid collisions.
        title=hashlib.sha256(json.dumps(state_machine_content).encode("utf-8")).hexdigest(),
        content=state_machine_content,
    )

    # Get or create the referrals group, to which insurance approval cases will be assigned
    referrals_group_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["referrals_group"]
    testCaseSelf.referrals_group_assignee_group, _ = AssigneeGroup.objects.get_or_create(
        name=referrals_group_unique_key, unique_key=referrals_group_unique_key
    )


class TestReferralRelatedCaseGeneration(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()
        set_up_referral_affected_case_categories(cls)


class TestCreateMemorialHermannCsv(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()
        set_up_steerage_affected_case_categories(cls)

    def test_get_mh_referral_row(self):
        care_category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        care_case = CaseFactory(person=self.patient.person, category=care_category)
        steerage = CaseRelation.objects.get(
            case=care_case,
            content_type=ContentType.objects.get_for_model(Steerage),
        ).content_object

        # Create a fake specialty
        specialty_group = SpecialtyGroupFactory.create()
        steerage.specialty_group = specialty_group

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        # Create some fake ICD 10 codes
        faker: Faker = Faker()
        icd10_code_1 = ReferralInsuranceAuthICDCode.objects.create(
            code=faker.name(), description=faker.name(), referral=steerage.referral
        )
        icd10_code_2 = ReferralInsuranceAuthICDCode.objects.create(
            code=faker.name(), description=faker.name(), referral=steerage.referral
        )
        icd10_codes_string = icd10_code_1.code + "," + icd10_code_2.code

        # Create a fake provider
        state_obj = State.objects.get(abbreviation="NJ")
        steerage_provider = SteerageProviderFactory.create(
            steerage=steerage,
            first_name="Mary",
            middle_name="S",
            last_name="Lennox",
            npi="123456789",
            care_organization_name="The Secret Garden",
            address_line_1="123 Main St",
            address_line_2="Apt 2A",
            city="Elizabeth",
            state=state_obj,
            zip_code="07083",
        )
        expected_steerage_provider_string = (
            "Mary S Lennox, 123456789, The Secret Garden, 123 Main St, Apt 2A, Elizabeth"
        )
        steerage.steerage_providers.add(steerage_provider)
        steerage.save()

        # Fetch the generated row
        referral_row = get_mh_referral_row(steerage)

        # So many of the fields we send are pretty straightforward
        # and it would be pretty repetitive to generate the exact same
        # list again in the test suite. Test a few of the pieces.
        self.assertIn(icd10_codes_string, referral_row)
        self.assertIn(expected_steerage_provider_string, referral_row)
        self.assertIn(specialty_group.label, referral_row)
        if steerage.referral.referral_priority in (
            ReferralPriority.URGENT,
            ReferralPriority.MEDIUM,
        ):
            self.assertIn(MemorialHermannConfig.MH_REFERRAL_PRIORITY_URGENT, referral_row)
        else:
            self.assertIn(MemorialHermannConfig.MH_REFERRAL_PRIORITY_ROUTINE, referral_row)

        today = timezone.now().date()
        expected_seen_by = ""
        if steerage.referral.referral_priority == ReferralPriority.URGENT:
            expected_seen_by = (today + timedelta(days=3)).strftime("%Y-%m-%d")

        elif steerage.referral.referral_priority == ReferralPriority.MEDIUM:
            expected_seen_by = (today + timedelta(days=60)).strftime("%Y-%m-%d")

        self.assertEqual(expected_seen_by, referral_row[-2])


class TestMaybeQueueForTransmission(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        mh_recipient = ReferralRecipientFactory(direct_message_to=MemorialHermannConfig.MH_DIRECT_MESSAGE_ADDRESS)
        cls.mh_referral: Referral = mh_recipient.referral

        not_mh_recipient = ReferralRecipientFactory()
        cls.not_mh_referral: Referral = not_mh_recipient.referral

    def test_mh_referral_first_locked(self):
        # Test locking a MH referral that hasn't already been sent.
        maybe_queue_for_transmission(self.mh_referral, True)

        self.mh_referral.refresh_from_db()

        # It should move to PENDING
        self.assertEqual(self.mh_referral.vendor_transmission_status, VendorTransmissionStatusChoices.PENDING)

    def test_mh_referral_later_relocked(self):
        # Test locking a MH referral that was already successfully sent, then presumably unlocked
        # days later, and then re-locked
        self.mh_referral.vendor_transmission_status = VendorTransmissionStatusChoices.SUCCESS
        self.mh_referral.save()

        maybe_queue_for_transmission(self.mh_referral, True)

        self.mh_referral.refresh_from_db()

        # It should remain SUCCESS
        self.assertEqual(self.mh_referral.vendor_transmission_status, VendorTransmissionStatusChoices.SUCCESS)

    def test_mh_referral_unlocked_from_pending(self):
        # Test unlocking a MH referral that was set to PENDING, then presumably quickly unlocked
        # such as to fix an error.
        self.mh_referral.vendor_transmission_status = VendorTransmissionStatusChoices.PENDING
        self.mh_referral.save()

        maybe_queue_for_transmission(self.mh_referral, False)

        self.mh_referral.refresh_from_db()

        # It should move back to blank
        self.assertIsNone(self.mh_referral.vendor_transmission_status)

    def test_mh_referral_unlocked_from_success(self):
        # Test unlocking a MH referral that was already successfully sent, then presumably unlocked
        # days later.
        self.mh_referral.vendor_transmission_status = VendorTransmissionStatusChoices.SUCCESS
        self.mh_referral.save()

        maybe_queue_for_transmission(self.mh_referral, False)

        self.mh_referral.refresh_from_db()

        # It should remain SUCCESS
        self.assertEqual(self.mh_referral.vendor_transmission_status, VendorTransmissionStatusChoices.SUCCESS)

    def test_not_mh_referral_first_locked(self):
        # Test locking a referral NOT going to MH, which otherwise should trigger a status
        # change, but rather leaves it untouched.
        maybe_queue_for_transmission(self.not_mh_referral, True)

        self.not_mh_referral.refresh_from_db()

        # It should remain None
        self.assertIsNone(self.not_mh_referral.vendor_transmission_status)
