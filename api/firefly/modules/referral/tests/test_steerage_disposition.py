from datetime import date, timed<PERSON>ta

from django.contrib.contenttypes.models import ContentType

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.core.user.factories import PersonUserFactory
from firefly.modules.cases.constants import (
    REFERRAL_NOTE_RETRIEVAL_CATEGORY,
    REFERRAL_REQUEST,
    ReferralRequestCaseStatuses,
)
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation
from firefly.modules.facts.factories import SpecialtyGroupFactory
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.referral.constants import (
    DRAFT_ACTION,
    MEMBER_SCHEDULED_ACTION,
    MEMBER_UNRESPONSIVE_ACTION,
    REOPENED_ACTION,
    STEERAGE_NOT_NEEDED_ACTION,
    WILL_NOT_DO_ACTION,
)
from firefly.modules.referral.factories import SteerageProviderFactory
from firefly.modules.referral.models import Steerage, SteerageDisposition
from firefly.modules.referral.tests.test_referral_utils import (
    set_up_referral_affected_case_categories,
)
from firefly.modules.referral.tests.test_steerage_utils import (
    set_up_steerage_affected_case_categories,
)


class SteerageDispositionLocationReferralTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.person = PersonUserFactory.create()
        set_up_steerage_affected_case_categories(cls)

        # Location search category data
        cls.ref_initiation_category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        # create a case
        cls.ref_initiation_category_case = Case.objects.create(
            person=cls.person,
            category=cls.ref_initiation_category,
        )
        # Find steerage - add steerage provider - lock the steerage
        cls.steerage: Steerage = CaseRelation.objects.get(
            case=cls.ref_initiation_category_case,
            content_type=ContentType.objects.get_for_model(Steerage),
        ).content_object
        cls.steerage.description = "Dermatology"
        cls.steerage_provider = SteerageProviderFactory.create()
        cls.steerage.steerage_providers.set([cls.steerage_provider])
        # Create a provider user for locking - we'll use the provider from parent class in tests
        from firefly.core.user.factories import ProviderUserFactory

        provider_user = ProviderUserFactory.create()
        cls.steerage.lock(user=provider_user)
        # Add steerage disposition to steerage
        cls.steerage.disposition = SteerageDisposition.objects.get(
            matches_case_status_name=MEMBER_UNRESPONSIVE_ACTION,
        )
        cls.steerage.save()

    def test_case_transition_when_member_adds_scheduling_date(self):
        # Scenario 1
        #   We add a new scheduling future date for location_search_category_case
        # Expected
        #   case status should stay to draft

        # Assert the case status inital status is draft
        self.assertEqual(self.ref_initiation_category_case.status, ReferralRequestCaseStatuses.NEW)

        # Update the scheduling date for for member
        scheduling_date = date.today() + timedelta(days=5)
        self.steerage.scheduling_date = scheduling_date
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, scheduling_date)

        # assert that the case status is still draft for location search category
        self.ref_initiation_category_case.refresh_from_db()
        self.assertEqual(self.ref_initiation_category_case.status, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)

        # Scenario 2
        #   We update the scheduling from future date to None
        # Expected
        #   case status should stay to draft

        # Update the scheduling date for for member
        self.steerage.scheduling_date = None
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, None)

        # assert that the case status is stll in draft for location search category
        self.ref_initiation_category_case.refresh_from_db()
        self.assertEqual(self.ref_initiation_category_case.status, ReferralRequestCaseStatuses.REOPENED)

        # Scenario 3
        #   We update the scheduling from None to past date
        # Expected
        #   case status should stay to draft

        # Update the scheduling date for for member
        scheduling_date = date.today() - timedelta(days=5)
        self.steerage.scheduling_date = scheduling_date
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, scheduling_date)

        # assert that the case status is still in draft for location search category
        self.ref_initiation_category_case.refresh_from_db()
        self.assertEqual(self.ref_initiation_category_case.status, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)


class SteerageDispositionReferralRequestTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.person = PersonUserFactory.create()
        add_person_to_program(cls.person, ProgramCodes.PRIMARY_CARE)
        set_up_steerage_affected_case_categories(cls)

        # create steerage - add steerage provider - lock the steerage
        cls.case = CaseFactory.create(category=cls.referral_initiation_category, description="Test", person=cls.person)
        cls.steerage = CaseRelation.objects.get(
            case=cls.case, content_type=ContentType.objects.get_for_model(Steerage)
        ).content_object
        cls.steerage.description = "Dermatology"
        cls.steerage_provider = SteerageProviderFactory.create()
        cls.steerage.steerage_providers.set([cls.steerage_provider])
        # Create a provider user for locking
        from firefly.core.user.factories import ProviderUserFactory

        provider_user = ProviderUserFactory.create()
        cls.steerage.lock(user=provider_user)
        cls.steerage.save()

    def test_case_transition_for_referral_scheduling(self):
        # Scenario 1
        #   We add a new scheduling future date for referral_scheduling_case
        # Expected
        #   Should move the case status to MEMBER_SCHEDULED_ACTION

        # Assert the case status inital status is DRAFT
        self.assertEqual(self.case.status, ReferralRequestCaseStatuses.NEW)

        # Update the scheduling date for for member
        scheduling_date = date.today() + timedelta(days=5)
        self.steerage.scheduling_date = scheduling_date
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, scheduling_date)

        # assert the case status
        self.case.refresh_from_db()
        self.assertEqual(self.case.status, MEMBER_SCHEDULED_ACTION)

        # assert steerage disposition
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.disposition.matches_case_status_name, MEMBER_SCHEDULED_ACTION)

        # Scenario 2
        #   We update the scheduling from future date to None
        # Expected
        #   Should move the case status to REOPENED_ACTION

        # Update the scheduling date for for member
        self.steerage.scheduling_date = None
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, None)

        # assert case status
        self.case.refresh_from_db()
        self.assertEqual(self.case.status, REOPENED_ACTION)

        # assert steerage disposition is None
        self.steerage.refresh_from_db()
        self.assertIsNone(self.steerage.disposition)

        # Scenario 3
        #   We update the scheduling from None to past date
        # Expected
        #   Should move the case status to MEMBER_SCHEDULED_ACTION

        # Update the scheduling date for for member
        scheduling_date = date.today() - timedelta(days=5)
        self.steerage.scheduling_date = scheduling_date
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, scheduling_date)

        # assert case status
        self.case.refresh_from_db()
        self.assertEqual(self.case.status, MEMBER_SCHEDULED_ACTION)

        # assert steerage disposition
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.disposition.matches_case_status_name, MEMBER_SCHEDULED_ACTION)

    def test_case_transition_on_adding_scheduling_date_without_locking_steerage(self):
        # Scenario 1
        #   Add a future scheduling date on a unlocked steerage
        # Expected
        #   Should not update case status to member scheduled

        # Assert the case status inital status is DRAFT
        self.assertEqual(self.case.status, ReferralRequestCaseStatuses.NEW)

        # unlock the steerage
        self.steerage.unlock()
        self.steerage.save()

        # Update the scheduling on behalf of member
        scheduling_date = date.today() + timedelta(days=5)
        self.steerage.scheduling_date = scheduling_date
        self.steerage.save()
        self.steerage.refresh_from_db()
        self.assertEqual(self.steerage.scheduling_date, scheduling_date)

        self.case.refresh_from_db()
        self.assertEqual(self.case.status, ReferralRequestCaseStatuses.NEW)
        self.assertEqual(self.steerage.disposition.matches_case_status_name, MEMBER_SCHEDULED_ACTION)


class SteerageDispositionReopenCloseExtraCasesTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        cls.person = PersonUserFactory.create()
        add_person_to_program(cls.person, ProgramCodes.PRIMARY_CARE)
        set_up_steerage_affected_case_categories(cls)
        set_up_referral_affected_case_categories(cls)

        care_category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        cls.care_case = CaseFactory.create(person=cls.person, category=care_category)
        cls.steerage: Steerage = CaseRelation.objects.get(
            case=cls.care_case,
            content_type=ContentType.objects.get_for_model(Steerage),
        ).content_object

        # Create a referral directly instead of using API
        from firefly.modules.referral.factories import ReferralFactory

        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()

        # Create referral directly
        referral = ReferralFactory.create(body=body, is_active=True)
        cls.steerage.referral = referral
        cls.steerage.description = description
        cls.steerage.specialty_group = specialty_group
        cls.steerage.priority = "URGENT"  # Set priority on steerage, not referral
        cls.steerage.save()
        cls.steerage.refresh_from_db()

        cls.steerage.description = "Dermatology"

        cls.steerage_provider = SteerageProviderFactory.create()
        cls.steerage.steerage_providers.set([cls.steerage_provider])
        # Adding a scheduling date will create a note retrieval case
        cls.steerage.scheduling_date = date.today() - timedelta(days=5)
        # Locking will create a Scheduling Follow Up Case
        # Create a patient user for locking
        from firefly.core.user.factories import PatientUserFactory

        patient_user = PatientUserFactory.create()
        cls.steerage.lock(user=patient_user)
        cls.steerage.save()

        cls.steerage.refresh_from_db()

        # Assert all the cases exist
        assert Case.objects.count() == 2
        note_retrieval_category = CaseCategory.objects.get(unique_key=REFERRAL_NOTE_RETRIEVAL_CATEGORY)
        cls.note_retrieval_case = Case.objects.get(category=note_retrieval_category)

        # Assert the initial status
        assert cls.note_retrieval_case.status == DRAFT_ACTION

    def test_close_extra_cases(self):
        # scenario
        #   Create a steerage with Note retrieval and insurance Approval in Draft,
        #   with close_extra_cases is marked true for STEERAGE_NOT_NEEDED steerage disposition
        # Result
        #   Updating the case status will transisiton steerage disposition to STEERAGE_NOT_NEEDED
        #   and as a side effect, it will mark the Note retrieval and Insurance Approval Cases
        #   as "Will Not Do"

        # Mark True for only close extra cases for the steerage disposition
        # so that there will be no conflict b/w closing and reopening the cases
        disposition: SteerageDisposition = SteerageDisposition.objects.get(
            matches_case_status_name=STEERAGE_NOT_NEEDED_ACTION,
        )
        disposition.reopen_extra_cases = False
        disposition.save()
        # Assert that close_extra_cases is true
        self.assertEqual(disposition.close_extra_cases, True)

        # Move the referral case up case into this disposition
        self.care_case.action = STEERAGE_NOT_NEEDED_ACTION
        self.care_case.save()

        # Refresh
        self.note_retrieval_case.refresh_from_db()
        self.steerage.refresh_from_db()

        # Assert that the two cases were closed when the disposition changed
        # to the one above
        self.assertEqual(self.steerage.disposition.matches_case_status_name, STEERAGE_NOT_NEEDED_ACTION)
        self.assertEqual(self.note_retrieval_case.status, WILL_NOT_DO_ACTION)
