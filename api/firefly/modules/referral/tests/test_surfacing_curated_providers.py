from typing import List, Optional

from faker import Faker

from firefly.core.services.ribbon.types import <PERSON><PERSON>ocation, RibbonProvider
from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.network.factories import (
    ContactInformationFactory,
    CuratedProviderFactory,
    PartnershipFactory,
    RibbonAddressFactory,
    RibbonLocationFactory,
    RibbonProviderFactory,
)
from firefly.modules.network.models import (
    CuratedProvider,
    CuratedProviderCareOrgNameChoice,
    CuratedProviderPartnership,
    Partnership,
    PartnershipTypeConfig,
)
from firefly.modules.referral.constants import MemberServiceLevel
from firefly.modules.referral.utils.provider_search_utils import (
    AddressDetail,
    get_curated_facilities_list,
    get_curated_providers_list,
    update_facility_contact_information,
    update_facility_partnership,
    update_provider_partnership,
)


class SurfacingCuratedProvidersAndPartnershipTestCases(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.faker: Faker = Faker()
        # Create Five type of partner combination for provider search

        # 1. NPI, Care org name and Address present
        self.npi_care_org_name_address_provider: CuratedProvider = CuratedProviderFactory.create(
            care_org_name="Dr Guy Facial Plastic Surgery",
        )
        self.npi_care_org_name_address_partnership: Partnership = PartnershipFactory.create(
            partner_name="CVS MinuteClinic"
        )
        self.npi_care_org_name_address_curated_partnership: CuratedProviderPartnership = (
            CuratedProviderPartnership.objects.create(
                curated_provider=self.npi_care_org_name_address_provider,
                partnership=self.npi_care_org_name_address_partnership,
            )
        )

        # 2. NPI, Care org name present but address not present
        self.npi_care_org_name_provider: CuratedProvider = CuratedProviderFactory.create(
            address_line_1=None, address_line_2=None, city=None, state=None, zip_code=None
        )
        self.npi_care_org_name_partnership: Partnership = PartnershipFactory.create(partner_name="Memorial Hermann")
        self.npi_care_org_name_curated_partnership: CuratedProviderPartnership = (
            CuratedProviderPartnership.objects.create(
                curated_provider=self.npi_care_org_name_provider,
                partnership=self.npi_care_org_name_partnership,
            )
        )
        # 3. NPI, address present but Care org name not present
        self.npi_address_provider: CuratedProvider = CuratedProviderFactory.create(
            care_org_name=None,
        )
        self.npi_address_partnership: Partnership = PartnershipFactory.create(partner_name="Advanced Gynecology")
        self.npi_address_curated_partnership: CuratedProviderPartnership = CuratedProviderPartnership.objects.create(
            curated_provider=self.npi_address_provider,
            partnership=self.npi_address_partnership,
        )
        # 4. NPI not present but address, Care org name present
        self.care_org_name_address_provider: CuratedProvider = CuratedProviderFactory.create(
            care_org_name="Cancer Center at Harrington",
            npi=None,
        )
        self.care_org_name_address_partnership: Partnership = PartnershipFactory.create(partner_name="BIDMC Cardiology")
        self.care_org_name_address_curated_parnership: CuratedProviderPartnership = (
            CuratedProviderPartnership.objects.create(
                curated_provider=self.care_org_name_address_provider,
                partnership=self.care_org_name_address_partnership,
            )
        )
        # 5. NPI and address not present but Care org name present
        self.care_org_name_provider: CuratedProvider = CuratedProviderFactory.create(
            npi=None,
            address_line_1=None,
            address_line_2=None,
            city=None,
            state=None,
            zip_code=None,
        )
        self.care_org_name_partnership: Partnership = PartnershipFactory.create(partner_name="Acadian Health")
        self.care_org_name_curated_partnership: CuratedProviderPartnership = CuratedProviderPartnership.objects.create(
            curated_provider=self.care_org_name_provider,
            partnership=self.care_org_name_partnership,
        )

        # Create a facility with care org name and zipcode for location search
        self.facility_provider: CuratedProvider = CuratedProviderFactory.create(
            npi=None,
            care_org_name="Pediatric Clinic of La Porte",
        )
        self.facility_provider_partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            partner_name="Ash Wellness",
        )
        self.facility_provider_curated_partnership: CuratedProviderPartnership = (
            CuratedProviderPartnership.objects.create(
                curated_provider=self.facility_provider,
                partnership=self.facility_provider_partnership,
            )
        )
        self.contact_info = ContactInformationFactory.create(
            curated_provider=self.facility_provider,
            is_verified=True,
            specialty_group=None,
        )

    def assert_curated_provider(
        self,
        curated_provider: CuratedProvider,
        expected_curated_provider: CuratedProvider,
        partnership: Optional[Partnership],
        expected_partnership: Optional[Partnership],
    ):
        if curated_provider.npi:
            self.assertEqual(str(curated_provider.npi), expected_curated_provider.npi)

        self.assertEqual(curated_provider.care_org_name, expected_curated_provider.care_org_name)
        self.assertEqual(curated_provider.address_line_1, expected_curated_provider.address_line_1)
        self.assertEqual(curated_provider.address_line_2, expected_curated_provider.address_line_2)
        self.assertEqual(curated_provider.city, expected_curated_provider.city)
        self.assertEqual(curated_provider.state, expected_curated_provider.state)
        self.assertEqual(curated_provider.zip_code, expected_curated_provider.zip_code)

        if partnership and expected_partnership:
            self.assertEqual(partnership.agreement_type, expected_partnership.agreement_type)
            self.assertEqual(
                str(partnership.can_accept_care_members), str(expected_partnership.can_accept_care_members)
            )
            self.assertEqual(
                str(partnership.can_accept_coverage_members), str(expected_partnership.can_accept_coverage_members)
            )
            self.assertEqual(
                str(partnership.can_accept_care_and_coverage_members),
                str(expected_partnership.can_accept_care_and_coverage_members),
            )

    def test_get_curated_provider_list_for_valid_npi_address_and_care_org_name(self, *args):
        # Scenario 0
        #   Create a provider with npi, care_org_name and address matching the partner
        #   and partnership is at CARE for member and curated provider
        # Result
        #   get_curated_providers_list should return the partner
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Update partnership to care_only
        self.npi_care_org_name_address_partnership.can_accept_care_members = True
        self.npi_care_org_name_address_partnership.can_accept_coverage_members = False
        self.npi_care_org_name_address_partnership.can_accept_care_and_coverage_members = False
        self.npi_care_org_name_address_partnership.save()

        # update the above provider details with partner npi detail
        provider_detail["npi"] = self.npi_care_org_name_address_provider.npi
        provider_detail["locations"][0]["name"] = self.npi_care_org_name_address_provider.care_org_name
        provider_detail["locations"][0]["address_details"] = AddressDetail(
            address_line_1=self.npi_care_org_name_address_provider.address_line_1,
            address_line_2=self.npi_care_org_name_address_provider.address_line_2,
            city=self.npi_care_org_name_address_provider.city,
            state=self.npi_care_org_name_address_provider.state.abbreviation,
            zip=self.npi_care_org_name_address_provider.zip_code,
            street=self.npi_care_org_name_address_provider.address_line_1,
        )
        # no match should be returned when the partnership level is not set
        with self.assertNumQueries(5):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.CARE
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 1)
        # Assert the curated providers
        self.assert_curated_provider(
            curated_provider=self.npi_care_org_name_address_provider,
            expected_curated_provider=curated_providers.first(),
            partnership=self.npi_care_org_name_address_partnership,
            expected_partnership=curated_providers.first().curatedprovider_partnerships.first().partnership,
        )
        # Match should be returned when partnership level is set
        self.npi_care_org_name_address_curated_partnership.partnership_level = PartnershipTypeConfig.PROVIDER_CARE_ORG
        self.npi_care_org_name_address_curated_partnership.save()

        # Create multiple partnerships
        another_provider_for_same_partnership: RibbonProvider = RibbonProviderFactory.create()
        another_provider_for_same_partnership["npi"] = self.npi_care_org_name_address_provider.npi
        another_provider_for_same_partnership["locations"][0]["name"] = (
            self.npi_care_org_name_address_provider.care_org_name
        )
        another_provider_for_same_partnership["locations"][0]["address_details"] = AddressDetail(
            address_line_1=self.npi_care_org_name_address_provider.address_line_1,
            address_line_2=self.npi_care_org_name_address_provider.address_line_2,
            city=self.npi_care_org_name_address_provider.city,
            state=self.npi_care_org_name_address_provider.state.abbreviation,
            zip=self.npi_care_org_name_address_provider.zip_code,
            street=self.npi_care_org_name_address_provider.address_line_1,
        )
        another_npi_care_org_name_address_provider: CuratedProvider = CuratedProviderFactory.create()
        another_npi_care_org_name_address_partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=False,
            can_accept_care_and_coverage_members=False,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=another_npi_care_org_name_address_provider,
            partnership=another_npi_care_org_name_address_partnership,
            partnership_level=PartnershipTypeConfig.PROVIDER_CARE_ORG,
        )
        another_provider_for_diff_partnership: RibbonProvider = RibbonProviderFactory.create()
        # Assert that network partner agreement is not set
        self.assertIsNone(another_provider_for_diff_partnership["locations"][0]["network_partner_agreement_type"])
        # update the above provider details with partner npi detail
        another_provider_for_diff_partnership["npi"] = another_npi_care_org_name_address_provider.npi
        another_provider_for_diff_partnership["locations"][0]["name"] = (
            another_npi_care_org_name_address_provider.care_org_name
        )
        another_provider_for_diff_partnership["locations"][0]["address_details"] = AddressDetail(
            address_line_1=another_npi_care_org_name_address_provider.address_line_1,
            address_line_2=another_npi_care_org_name_address_provider.address_line_2,
            city=another_npi_care_org_name_address_provider.city,
            state=another_npi_care_org_name_address_provider.state.abbreviation,
            zip=another_npi_care_org_name_address_provider.zip_code,
            street=another_npi_care_org_name_address_provider.address_line_1,
        )

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail, another_provider_for_diff_partnership, another_provider_for_same_partnership],
            MemberServiceLevel.CARE,
        )

        # Update the partnership in the provider search details
        update_provider_partnership(
            curated_providers=curated_providers,
            provider_search_results=[
                provider_detail,
                another_provider_for_diff_partnership,
                another_provider_for_same_partnership,
            ],
        )
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.npi_care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(
            another_provider_for_diff_partnership["locations"][0]["network_partner_agreement_type"],
            another_npi_care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(
            another_provider_for_same_partnership["locations"][0]["network_partner_agreement_type"],
            self.npi_care_org_name_address_partnership.agreement_type,
        )
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 2)

        # Scenario 1
        #   Use same provider with npi, care_org_name (but with differences in casing)
        #   and address matching with the partner
        # Result
        #   get_curated_providers_list should return the partner
        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None
        provider_detail["locations"][0]["name"] = "DR Guy Facial Plastic Surgery"

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail],
            MemberServiceLevel.CARE,
        )

        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.npi_care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(curated_providers.count(), 1)
        self.assert_curated_provider(
            curated_provider=self.npi_care_org_name_address_provider,
            expected_curated_provider=curated_providers.first(),
            partnership=self.npi_care_org_name_address_partnership,
            expected_partnership=curated_providers.first().curatedprovider_partnerships.first().partnership
            if curated_providers.first().curatedprovider_partnerships.first()
            else None,
        )

        # Scenario 2
        #   Use same provider with npi, care_org_name (but with differences in special chars)
        #   and address matching with the partner
        # Result
        #   get_curated_providers_list should return the partner
        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None
        provider_detail["locations"][0]["name"] = "Dr. Guy, Facial Plastic Surgery"
        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail],
            MemberServiceLevel.CARE,
        )

        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.npi_care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(curated_providers.count(), 1)
        self.assert_curated_provider(
            curated_provider=self.npi_care_org_name_address_provider,
            expected_curated_provider=curated_providers.first(),
            partnership=self.npi_care_org_name_address_partnership,
            expected_partnership=curated_providers.first().curatedprovider_partnerships.first().partnership
            if curated_providers.first().curatedprovider_partnerships.first()
            else None,
        )

        # Scenario 3
        #   Use same provider with npi, care_org_name and address matching with the partner
        #   and curated provider partnership is only at CARE but the member service level is at COVERAGE
        # Result
        #   The provider result should not have any partnership

        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail], MemberServiceLevel.COVERAGE
        )

        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertEqual(provider_detail["locations"][0]["network_partner_agreement_type"], None)

        # Scenario 4
        #   Use a provider where npi, care_org_name and address matching with the partner
        #   and curated provider partnership is CARE but the member service level is at COVERAGE
        # Result
        #   get_curated_providers_list should not return any partners
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertEqual(provider_detail["locations"][0]["network_partner_agreement_type"], None)

        # 1 queries should be executed (As there are no provider match)
        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )
            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertEqual(provider_detail["locations"][0]["network_partner_agreement_type"], None)
        self.assertEqual(curated_providers.count(), 0)

        # Scenario 5
        #   Use a provider where npi, care_org_name and address not matching with the partner
        # Result
        #   get_curated_providers_list should not return any partners
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertEqual(provider_detail["locations"][0]["network_partner_agreement_type"], None)
        provider_detail["locations"][0]["address_details"]["zip"] = self.faker.postcode()

        # 1 queries should be executed (As there are no provider match)
        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.CARE
            )
            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertEqual(provider_detail["locations"][0]["network_partner_agreement_type"], None)
        self.assertEqual(curated_providers.count(), 0)

    def test_get_curated_provider_list_for_valid_npi_address_but_blank_care_org_name(self, *args):
        # Scenario 0
        #   Create a partner with valid npi and address but blank care_org
        #   and partnership is at coverage for member and curated provider
        # Result
        #   unsupported match - no partnership info is shown
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])
        # Assert that the care_org name is blank
        self.assertEqual(self.npi_address_provider.care_org_name, CuratedProviderCareOrgNameChoice.UNKNOWN)

        # Update partnership to care_only
        self.npi_address_partnership.can_accept_care_members = False
        self.npi_address_partnership.can_accept_coverage_members = True
        self.npi_address_partnership.can_accept_care_and_coverage_members = False
        self.npi_address_partnership.save()
        self.npi_address_curated_partnership.partnership_level = PartnershipTypeConfig.PROVIDER_CARE_ORG
        self.npi_address_curated_partnership.save()

        # update the above provider details with partner npi detail
        provider_detail["npi"] = self.npi_address_provider.npi
        provider_detail["locations"][0]["name"] = None
        provider_detail["locations"][0]["address_details"] = AddressDetail(
            address_line_1=self.npi_address_provider.address_line_1,
            address_line_2=self.npi_address_provider.address_line_2,
            city=self.npi_address_provider.city,
            state=self.npi_address_provider.state.abbreviation,
            zip=self.npi_address_provider.zip_code,
            street=self.npi_address_provider.address_line_1,
        )
        all_membership_levels = [
            MemberServiceLevel.COVERAGE,
            MemberServiceLevel.CARE,
            MemberServiceLevel.CARE_AND_COVERAGE,
        ]
        for membership_level in all_membership_levels:
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail],
                membership_level,
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertEqual(
                provider_detail["locations"][0]["network_partner_agreement_type"],
                self.npi_address_partnership.agreement_type,
            )
            # Asserting curated provider count outside "with" clause so that this query won't get counted
            self.assertEqual(curated_providers.count(), 1)
            # Assert the curated providers
            self.assert_curated_provider(
                curated_provider=self.npi_address_provider,
                expected_curated_provider=curated_providers.first(),
                partnership=self.npi_address_partnership,
                expected_partnership=curated_providers.first().curatedprovider_partnerships.first().partnership
                if curated_providers.first().curatedprovider_partnerships.first()
                else None,
            )

        # Scenario 2
        #   Use a provider where npi and address matches with the partner
        #   but provider have care_org present and partner have care org as blank
        # Result
        #   The provider result should not have any partnership
        provider_detail["locations"][0]["name"] = self.faker.name()
        provider_detail["locations"][0]["network_partner_agreement_type"] = None
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail],
            membership_level,
        )

        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

    def test_get_curated_provider_list_for_valid_npi_care_org_name_but_blank_address(self, *args):
        # Scenario 0
        #   Create a partner with valid npi and care_org but blank address similar to partner data
        # Result
        #   unsupported match - no partnership info is shown
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Update partnership to care_only
        self.npi_care_org_name_partnership.can_accept_care_members = False
        self.npi_care_org_name_partnership.can_accept_coverage_members = True
        self.npi_care_org_name_partnership.can_accept_care_and_coverage_members = False
        self.npi_care_org_name_partnership.save()

        # update the above provider details with partner npi detail
        provider_detail["npi"] = self.npi_care_org_name_provider.npi
        provider_detail["locations"][0]["name"] = self.npi_care_org_name_provider.care_org_name
        provider_detail["locations"][0]["address_details"] = None

        with self.assertNumQueries(0):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Scenario 1
        #   Use a provider with same npi and address as the partner but diferent care_org_name
        # Result
        #   get_curated_providers_list should return the partner
        provider_detail["locations"][0]["name"] = self.faker.name()
        provider_detail["locations"][0]["network_partner_agreement_type"] = None
        with self.assertNumQueries(0):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Scenario 2
        #   Use same provider with same npi and care_org as the partner
        #   but provider have address but curated provider doesn't
        # Result
        #   should return the partner as partnership doesn't require address match
        provider_detail["locations"][0]["name"] = self.npi_care_org_name_provider.care_org_name
        provider_detail["locations"][0]["address_details"] = RibbonAddressFactory.create()
        # 1. For fetching the curated_providers
        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

    def test_get_curated_provider_list_for_valid_address_care_org_name_but_blank_npi(self, *args):
        # Scenario 0
        #   Create a partner with valid address and care_org but blank npi with same data as partner
        # Result
        #   unsupported match - no partnership info is shown until partnership level is set to care org
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Update partnership to care_only
        self.care_org_name_address_partnership.can_accept_care_members = False
        self.care_org_name_address_partnership.can_accept_coverage_members = True
        self.care_org_name_address_partnership.can_accept_care_and_coverage_members = False
        self.care_org_name_address_partnership.save()

        provider_detail["npi"] = self.faker.pystr()[:10]
        provider_detail["locations"][0]["name"] = self.care_org_name_address_provider.care_org_name
        provider_detail["locations"][0]["address_details"] = AddressDetail(
            address_line_1=self.care_org_name_address_provider.address_line_1,
            address_line_2=self.care_org_name_address_provider.address_line_2,
            city=self.care_org_name_address_provider.city,
            state=self.care_org_name_address_provider.state.abbreviation,
            zip=self.care_org_name_address_provider.zip_code,
            street=self.care_org_name_address_provider.address_line_1,
        )

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail], MemberServiceLevel.COVERAGE
        )
        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 1)
        self.assertEqual(curated_providers.first(), self.care_org_name_address_provider)

        self.care_org_name_address_curated_parnership.partnership_level = PartnershipTypeConfig.CARE_ORG
        self.care_org_name_address_curated_parnership.save()
        another_provider_for_same_partnership: RibbonProvider = RibbonProviderFactory.create()
        another_provider_for_same_partnership["npi"] = self.care_org_name_address_provider.npi
        another_provider_for_same_partnership["locations"][0]["name"] = (
            self.care_org_name_address_provider.care_org_name
        )
        another_provider_for_same_partnership["locations"][0]["address_details"] = AddressDetail(
            address_line_1=self.care_org_name_address_provider.address_line_1,
            address_line_2=self.care_org_name_address_provider.address_line_2,
            city=self.care_org_name_address_provider.city,
            state=self.care_org_name_address_provider.state.abbreviation,
            zip=self.care_org_name_address_provider.zip_code,
            street=self.care_org_name_address_provider.address_line_1,
        )
        another_care_org_name_provider: CuratedProvider = CuratedProviderFactory.create(
            npi="",
        )
        another_care_org_partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=False,
        )
        CuratedProviderPartnership.objects.create(
            curated_provider=another_care_org_name_provider,
            partnership=another_care_org_partnership,
            partnership_level=PartnershipTypeConfig.CARE_ORG,
        )

        another_provider_for_diff_partnership: RibbonProvider = RibbonProviderFactory.create()
        another_provider_for_diff_partnership["npi"] = None
        another_provider_for_diff_partnership["locations"][0]["name"] = another_care_org_name_provider.care_org_name
        another_provider_for_diff_partnership["locations"][0]["address_details"] = AddressDetail(
            address_line_1=another_care_org_name_provider.address_line_1,
            address_line_2=another_care_org_name_provider.address_line_2,
            city=another_care_org_name_provider.city,
            state=another_care_org_name_provider.state.abbreviation,
            zip=another_care_org_name_provider.zip_code,
            street=another_care_org_name_provider.address_line_1,
        )
        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail, another_provider_for_same_partnership, another_provider_for_diff_partnership],
            MemberServiceLevel.COVERAGE,
        )
        # Update the partnership in the provider search details
        update_provider_partnership(
            curated_providers=curated_providers,
            provider_search_results=[
                provider_detail,
                another_provider_for_same_partnership,
                another_provider_for_diff_partnership,
            ],
        )
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(
            another_provider_for_same_partnership["locations"][0]["network_partner_agreement_type"],
            self.care_org_name_address_partnership.agreement_type,
        )
        self.assertEqual(
            another_provider_for_diff_partnership["locations"][0]["network_partner_agreement_type"],
            another_care_org_partnership.agreement_type,
        )
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 2)

        provider_detail["locations"][0]["network_partner_agreement_type"] = None
        # Scenario 1
        #   Provider and partner have same care_org name but with differences in casing
        #   and partner npi is blank
        # Result
        #   get_curated_providers_list should return the partner
        provider_detail["locations"][0]["name"] = "Cancer Center At Harrington"
        provider_detail["locations"][0]["address_details"] = RibbonAddressFactory.create()
        provider_detail["locations"][0]["address_details"]["zip"] = self.care_org_name_address_provider.zip_code
        # verify that blank vs None npi has no impact
        self.care_org_name_address_provider.npi = ""
        self.care_org_name_address_provider.save()

        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail], MemberServiceLevel.COVERAGE
        )
        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.care_org_name_address_partnership.agreement_type,
        )
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 1)
        self.assertEqual(curated_providers.first(), self.care_org_name_address_provider)

        # Scenario 2
        #   Provider and partner have same care_org name but different addresses but same zipcode
        #   and partner npi is blank
        # Result
        #   get_curated_providers_list should return any partner
        provider_detail["locations"][0]["address_details"] = RibbonAddressFactory.create()
        provider_detail["locations"][0]["address_details"]["zip"] = self.care_org_name_address_provider.zip_code

        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None

        # call get_curated_providers_list
        curated_providers: List[CuratedProvider] = get_curated_providers_list(
            [provider_detail], MemberServiceLevel.COVERAGE
        )
        # Update the partnership in the provider search details
        update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        # Assert that agreement_type is updated
        self.assertEqual(
            provider_detail["locations"][0]["network_partner_agreement_type"],
            self.care_org_name_address_partnership.agreement_type,
        )
        # Asserting curated provider count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_providers.count(), 1)
        self.assertEqual(curated_providers.first(), self.care_org_name_address_provider)

        # Scenario 3
        #   Provider and partner have same care_org name and addresses but different zipcode
        #   and partner npi is blank
        # Result
        #   get_curated_providers_list should return any partner
        provider_detail["locations"][0]["address_details"]["zip"] = self.faker.postcode()

        # Reset the partner agreement type
        provider_detail["locations"][0]["network_partner_agreement_type"] = None

        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )
            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
        self.assertEqual(curated_providers.count(), 0)

    def test_get_curated_provider_list_for_valid_care_org_name_but_blank_npi_and_address(self, *args):
        # Scenario 0
        #   Create provider with different npi and address but same care_org as partner
        # Result
        #   unsupported match - no partnership info is shown
        provider_detail: RibbonProvider = RibbonProviderFactory.create()

        # Assert that network partner agreement is not set
        self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Update partnership to care_only
        self.care_org_name_partnership.can_accept_care_members = False
        self.care_org_name_partnership.can_accept_coverage_members = True
        self.care_org_name_partnership.can_accept_care_and_coverage_members = False
        self.care_org_name_partnership.save()

        provider_detail["npi"] = self.faker.pystr()[:10]
        provider_detail["locations"][0]["name"] = self.care_org_name_provider.care_org_name
        provider_detail["locations"][0]["address_details"] = RibbonAddressFactory.create()

        # 1. For fetching the curated_providers
        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )
            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            # Assert that agreement_type is updated
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

        # Scenario 1
        #   Create provider with different npi and address and different care_org
        # Result
        #   get_curated_providers_list should not return any partner
        # change care_org name
        provider_detail["locations"][0]["name"] = self.faker.name()
        with self.assertNumQueries(1):
            # call get_curated_providers_list
            curated_providers: List[CuratedProvider] = get_curated_providers_list(
                [provider_detail], MemberServiceLevel.COVERAGE
            )

            # Update the partnership in the provider search details
            update_provider_partnership(curated_providers=curated_providers, provider_search_results=[provider_detail])
            self.assertIsNone(provider_detail["locations"][0]["network_partner_agreement_type"])

    def test_get_curated_facility_list_for_valid_care_org_name_and_zip(self, *args):
        # Scenario 0
        #   Create curated facility with care_org_name and zip_code
        # Result
        #   get_curated_facilities_list should return the facility
        facility_detail: RibbonLocation = RibbonLocationFactory.create()

        # update the above facility_details with the same care org name and address (so they match)
        facility_detail["name"] = self.facility_provider.care_org_name
        facility_detail["address_details"] = AddressDetail(
            address_line_1=self.facility_provider.address_line_1,
            address_line_2=self.facility_provider.address_line_2,
            city=self.facility_provider.city,
            state=self.facility_provider.state.abbreviation,
            zip=self.facility_provider.zip_code,
            street=self.facility_provider.address_line_1,
        )

        # No partnership should be tagged with incorrect levels
        for partnership_level in [None, PartnershipTypeConfig.PROVIDER_CARE_ORG]:
            self.facility_provider_curated_partnership.partnership_level = partnership_level
            self.facility_provider_curated_partnership.save()
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail],
                MemberServiceLevel.CARE,
            )
            update_facility_partnership(
                curated_facilities=curated_facilities,
                facility_search_results=[facility_detail],
            )
            update_facility_contact_information(
                curated_facilities=curated_facilities,
                facility_search_results=[facility_detail],
            )
            self.assertIsNone(facility_detail["network_partner_agreement_type"])
            # Asserting curated facility count outside "with" clause so that this query won't get counted
            self.assertEqual(curated_facilities.count(), 1)
            # Assert the curated facilities
            self.assert_curated_provider(
                curated_provider=self.facility_provider,
                expected_curated_provider=curated_facilities.first(),
                partnership=None,
                expected_partnership=None,
            )
        self.facility_provider_curated_partnership.partnership_level = PartnershipTypeConfig.CARE_ORG
        self.facility_provider_curated_partnership.save()
        # call get_curated_facilities_list
        curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
            [facility_detail],
            MemberServiceLevel.CARE,
        )
        update_facility_partnership(
            curated_facilities=curated_facilities,
            facility_search_results=[facility_detail],
        )
        self.assertEqual(
            facility_detail["network_partner_agreement_type"],
            self.facility_provider_partnership.agreement_type,
        )
        # Asserting curated facility count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_facilities.count(), 1)
        # Assert the curated facilities
        self.assert_curated_provider(
            curated_provider=self.facility_provider,
            expected_curated_provider=curated_facilities.first(),
            partnership=self.facility_provider_partnership,
            expected_partnership=curated_facilities.first().curatedprovider_partnerships.first().partnership,
        )

        # Scenario 2
        #   Facility and partner have same care_org name but with differences in casing
        # Result
        #   get_curated_facilities_list should return the partner
        facility_detail["name"] = "Pediatric Clinic of LA Porte"
        # call get_curated_facilities_list
        curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
            [facility_detail],
            MemberServiceLevel.CARE,
        )
        update_facility_contact_information(
            curated_facilities=curated_facilities, facility_search_results=[facility_detail]
        )
        self.assertEqual(curated_facilities.count(), 1)
        # Assert the curated facilities
        self.assert_curated_provider(
            curated_provider=self.facility_provider,
            expected_curated_provider=curated_facilities.first(),
            partnership=None,
            expected_partnership=None,
        )

        # Scenario 3
        #   Facility and partner have same care_org name but with differences in casing
        # Result
        #   get_curated_facilities_list should return the partner
        facility_detail["name"] = "Pediatric, Clinic of L.A. Porte"
        # call get_curated_facilities_list
        curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
            [facility_detail],
            MemberServiceLevel.CARE,
        )
        update_facility_contact_information(
            curated_facilities=curated_facilities, facility_search_results=[facility_detail]
        )
        self.assertEqual(curated_facilities.count(), 1)
        # Assert the curated facilities
        self.assert_curated_provider(
            curated_provider=self.facility_provider,
            expected_curated_provider=curated_facilities.first(),
            partnership=self.facility_provider_partnership,
            expected_partnership=curated_facilities.first().curatedprovider_partnerships.first().partnership,
        )

        # Scenario 4
        #   Update curated facility with npi
        # Result
        #   get_curated_facilities_list should not return the facility
        self.facility_provider.npi = self.faker.pystr()[:10]
        self.facility_provider.save()

        with self.assertNumQueries(1):
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail],
                MemberServiceLevel.CARE,
            )
            update_facility_contact_information(
                curated_facilities=curated_facilities, facility_search_results=[facility_detail]
            )
        self.assertEqual(curated_facilities.count(), 0)

        # verify that multiple facilities get tagged as partners
        self.facility_provider.npi = ""  # test for None and blank NPI
        self.facility_provider.save()
        self.facility_provider_curated_partnership.partnership_level = PartnershipTypeConfig.CARE_ORG
        self.facility_provider_curated_partnership.save()
        another_facility_at_same_partnership: RibbonLocation = RibbonLocationFactory.create()
        another_facility_at_same_partnership["name"] = self.facility_provider.care_org_name
        another_facility_at_same_partnership["address_details"] = AddressDetail(
            address_line_1=self.facility_provider.address_line_1,
            address_line_2=self.facility_provider.address_line_2,
            city=self.facility_provider.city,
            state=self.facility_provider.state.abbreviation,
            zip=self.facility_provider.zip_code,
            street=self.facility_provider.address_line_1,
        )
        another_facility_provider: CuratedProvider = CuratedProviderFactory.create(
            npi=None,
        )
        another_facility_provider_partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
        )
        self.facility_provider_curated_partnership: CuratedProviderPartnership = (
            CuratedProviderPartnership.objects.create(
                curated_provider=another_facility_provider,
                partnership=another_facility_provider_partnership,
                partnership_level=PartnershipTypeConfig.CARE_ORG,
            )
        )
        another_facility_at_diff_partnership: RibbonLocation = RibbonLocationFactory.create()
        another_facility_at_diff_partnership["name"] = another_facility_provider.care_org_name
        another_facility_at_diff_partnership["address_details"] = AddressDetail(
            address_line_1=another_facility_provider.address_line_1,
            address_line_2=another_facility_provider.address_line_2,
            city=another_facility_provider.city,
            state=another_facility_provider.state.abbreviation,
            zip=another_facility_provider.zip_code,
            street=another_facility_provider.address_line_1,
        )
        with self.assertNumQueries(7):
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail, another_facility_at_same_partnership, another_facility_at_diff_partnership],
                MemberServiceLevel.CARE,
            )
            update_facility_partnership(
                curated_facilities=curated_facilities,
                facility_search_results=[
                    facility_detail,
                    another_facility_at_same_partnership,
                    another_facility_at_diff_partnership,
                ],
            )
        self.assertEqual(
            facility_detail["network_partner_agreement_type"],
            self.facility_provider_partnership.agreement_type,
        )
        self.assertEqual(
            another_facility_at_same_partnership["network_partner_agreement_type"],
            self.facility_provider_partnership.agreement_type,
        )
        self.assertEqual(
            another_facility_at_diff_partnership["network_partner_agreement_type"],
            another_facility_provider_partnership.agreement_type,
        )
        # Asserting curated facility count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_facilities.count(), 2)
        # Assert the curated facilities
        self.assert_curated_provider(
            curated_provider=self.facility_provider,
            # ordering is by descending order of id; expected provider was created first
            expected_curated_provider=curated_facilities.last(),
            partnership=self.facility_provider_partnership,
            expected_partnership=curated_facilities.last().curatedprovider_partnerships.first().partnership,
        )

        # Verify that null care org names are not supported
        # Since without the care org name - the partnership would not
        # have any matching criteria (except the zipcode)
        facility_detail["name"] = None
        self.facility_provider.care_org_name = None
        self.facility_provider.save()
        with self.assertNumQueries(0):
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail],
                MemberServiceLevel.CARE,
            )
            update_facility_contact_information(
                curated_facilities=curated_facilities, facility_search_results=[facility_detail]
            )
        self.assertIsNone(curated_facilities)

    def test_get_curated_facility_list_for_invalid_care_org_name(self, *args):
        # Scenario 0
        #   Create curated provider with different care_org_name and same zip_code
        # Result
        #   get_curated_facilities_list should not return the facility
        self.facility_provider_curated_partnership.partnership_level = PartnershipTypeConfig.CARE_ORG
        self.facility_provider_curated_partnership.save()
        facility_detail: RibbonLocation = RibbonLocationFactory.create()

        # Update the above facility_details with the same address (so they match)
        # Leave care org name different
        facility_detail["address_details"] = AddressDetail(
            address_line_1=self.facility_provider.address_line_1,
            address_line_2=self.facility_provider.address_line_2,
            city=self.facility_provider.city,
            state=self.facility_provider.state.abbreviation,
            zip=self.facility_provider.zip_code,
            street=self.facility_provider.address_line_1,
        )

        # 1 queries should be executed
        # 1. For fetching the curated_providers
        with self.assertNumQueries(1):
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail],
                MemberServiceLevel.CARE,
            )
            update_facility_contact_information(
                curated_facilities=curated_facilities, facility_search_results=[facility_detail]
            )

        # Asserting curated facility count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_facilities.count(), 0)

    def test_get_curated_facility_list_for_invalid_zipcode(self, *args):
        # Scenario 0
        #   Create curated provider with same care_org_name and different zip_code
        # Result
        #   get_curated_facilities_list should not return the facility
        self.facility_provider_curated_partnership.partnership_level = PartnershipTypeConfig.CARE_ORG
        self.facility_provider_curated_partnership.save()
        facility_detail: RibbonLocation = RibbonLocationFactory.create()

        # Update the above facility_details with the same care org name
        # Leave zip code different
        facility_detail["name"] = self.facility_provider.care_org_name

        # 1. For fetching the curated_providers
        with self.assertNumQueries(1):
            # call get_curated_facilities_list
            curated_facilities: List[CuratedProvider] = get_curated_facilities_list(
                [facility_detail],
                MemberServiceLevel.CARE,
            )
            update_facility_contact_information(
                curated_facilities=curated_facilities, facility_search_results=[facility_detail]
            )

        # Asserting curated facility count outside "with" clause so that this query won't get counted
        self.assertEqual(curated_facilities.count(), 0)
