from datetime import timed<PERSON><PERSON>

from django.core import management
from django.utils import timezone
from freezegun import freeze_time

from firefly.core.tests.test_case import FireflyTestCase
from firefly.modules.cases.constants import REFERRAL_NOTE_RETRIEVAL_CATEGORY
from firefly.modules.cases.factories import CaseCategoryFactory
from firefly.modules.facts.factories import SpecialtyGroupFactory
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.network.factories import PartnershipFactory
from firefly.modules.network.models import Partnership
from firefly.modules.referral.factories import SteerageFactory, SteerageProviderFactory
from firefly.modules.referral.models import SteerageProviderStatuses
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.models import StatusCategory


class TestAssignFeedbackForm(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.today_string = "2000-01-02"
        self.basic_state_machine_content = {
            "state_with_categories": [
                {
                    "state": {
                        "name": "Done",
                    },
                    "category": StatusCategory.COMPLETE,
                },
                {"state": {"name": "Draft"}, "category": StatusCategory.NOT_STARTED},
                {"state": {"name": "Will Not Do"}, "category": StatusCategory.DEFERRED},
            ],
            "initial_state": "Draft",
            "transitions": [
                {
                    "trigger": "Done",
                    "source": "*",
                    "dest": "Done",
                },
                {
                    "trigger": "Will Not Do",
                    "source": "*",
                    "dest": "Will Not Do",
                },
                {
                    "trigger": "deferred",
                    "system_action": "system_deferred",
                    "source": "*",
                    "dest": "Will Not Do",
                },
                {
                    "trigger": "Draft",
                    "source": "*",
                    "dest": "Draft",
                },
            ],
        }
        self.basic_state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
            content=self.basic_state_machine_content,
        )
        CaseCategoryFactory.create(
            unique_key=REFERRAL_NOTE_RETRIEVAL_CATEGORY,
            title="Note Retrieval",
            state_machine_definition=self.basic_state_machine_definition,
        )
        self.specialty = SpecialtyGroupFactory()
        self.feedback_form, _ = Form.objects.get_or_create(uid=FormUID.NAVIGATION_SPECIALIST_FEEDBACK_FORM)
        self.partner_with_feedback: Partnership = PartnershipFactory(requires_member_feedback=True)
        self.partner_no_feedback: Partnership = PartnershipFactory(requires_member_feedback=False)

    def _create_steerage_in_window(self, scheduling_date=None):
        """Creates a steerage that is in the feedback window"""
        if scheduling_date is None:
            scheduling_date = timezone.now().date() - timedelta(days=1)

        steerage = SteerageFactory(
            person=self.patient.person,
            specialty_group=self.specialty,
            scheduling_date=scheduling_date,
        )
        provider = SteerageProviderFactory(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            first_name="TEST",
            last_name="TEST",
            partnership=self.partner_with_feedback,
        )
        provider.member_selected_at = timezone.now()
        provider.save()
        steerage.lock(user=self.provider)
        steerage.save()
        return steerage

    def _create_steerage_outside_window(self, days_before=10):
        """Creates a steerage that is outside the feedback window"""
        return self._create_steerage_in_window(scheduling_date=timezone.now().date() - timedelta(days=days_before))

    def _create_steerage_with_future_date(self, days_ahead=1):
        """Creates a steerage with a future scheduling date"""
        return self._create_steerage_in_window(scheduling_date=timezone.now().date() + timedelta(days=days_ahead))

    def _create_steerage_with_existing_form(self):
        """Creates a steerage that already has a feedback form"""
        steerage = self._create_steerage_in_window()
        form_submission = FormSubmission.objects.create(
            user=steerage.person.user,
            form=self.feedback_form,
            completed_at=None,
        )
        steerage.feedback_form = form_submission
        steerage.save()
        return steerage, form_submission

    def _create_steerage_with_multiple_providers(self, member_selected=True):
        """Creates a steerage with multiple providers"""
        steerage = self._create_steerage_in_window()
        provider1 = SteerageProviderFactory(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            first_name=None,
            last_name=None,
            partnership=self.partner_with_feedback,
        )
        provider2 = SteerageProviderFactory(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            partnership=self.partner_with_feedback,
        )
        if member_selected:
            provider1.member_selected_at = timezone.now()
            provider1.save()
            provider2.member_selected_at = None
            provider2.save()
        else:
            # Set member_selected_at=None for all providers on this steerage
            for p in steerage.steerage_providers.all():
                p.member_selected_at = None
                p.save()
        return steerage

    def _create_steerage_with_no_feedback_partner(self):
        """Creates a steerage with a partner that doesn't require feedback"""
        steerage = SteerageFactory(
            person=self.patient.person,
            specialty_group=self.specialty,
            scheduling_date=timezone.now().date() - timedelta(days=1),
        )
        SteerageProviderFactory(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            first_name="TEST",
            last_name="TEST",
            partnership=self.partner_no_feedback,
        )
        steerage.lock(user=self.provider)
        steerage.save()
        return steerage

    def test_admin_command(self):
        with freeze_time(self.today_string):
            # Case 1: steerage in window with partner
            steerage1 = self._create_steerage_in_window()

            # Case 2: steerage more than 7 days old - no form
            steerage2 = self._create_steerage_outside_window()

            # Case 3: steerage later than today - no form
            steerage3 = self._create_steerage_with_future_date()

            # Case 4: steerage in window but already has form - no new form
            steerage4, form_submission = self._create_steerage_with_existing_form()

            # Case 5: steerage is not scheduled
            steerage5 = SteerageFactory(
                person=self.patient.person,
                scheduling_date=None,
            )
            SteerageProviderFactory(
                steerage=steerage5,
                status=SteerageProviderStatuses.ACCEPTED,
                first_name="TEST",
                last_name="TEST",
                partnership=self.partner_with_feedback,
            )
            steerage5.lock(user=self.provider)
            steerage5.save()

            # Case 6: member has multiple providers, two steerages, two forms assigned to member
            steerage6 = self._create_steerage_with_multiple_providers()

            # Case 7: member has multiple providers, but partner not selected
            steerage7 = self._create_steerage_with_multiple_providers(member_selected=False)

            # Case 8: member selected partner, but partner does not require form
            steerage8 = self._create_steerage_with_no_feedback_partner()

            management.call_command(
                "assign_feedback_form",
                user=self.provider,
                dry_run_off=True,
            )

            # Refresh all steerages from database
            for steerage in [steerage1, steerage2, steerage3, steerage4, steerage5, steerage6, steerage7, steerage8]:
                steerage.refresh_from_db()

            # Assertions
            self.assertIsNotNone(steerage1.feedback_form)
            self.assertIsNotNone(steerage6.feedback_form)
            self.assertEqual(steerage4.feedback_form, form_submission)

            self.assertIsNone(steerage2.feedback_form)
            self.assertIsNone(steerage3.feedback_form)
            self.assertIsNone(steerage5.feedback_form)
            self.assertIsNone(steerage7.feedback_form)
            self.assertIsNone(steerage8.feedback_form)

            # Verify form data
            self.assertEqual(
                steerage1.feedback_form.data,
                {"providerName": self.partner_with_feedback.partner_name},
            )
            self.assertEqual(
                steerage6.feedback_form.data,
                {"providerName": self.partner_with_feedback.partner_name},
            )

    def test_dry_run(self):
        with freeze_time(self.today_string):
            # Create all test cases
            steerage1 = self._create_steerage_in_window()
            steerage2 = self._create_steerage_outside_window()
            steerage3 = self._create_steerage_with_future_date()
            steerage4, form_submission = self._create_steerage_with_existing_form()
            steerage5 = SteerageFactory(
                person=self.patient.person,
                scheduling_date=None,
            )
            SteerageProviderFactory(
                steerage=steerage5,
                status=SteerageProviderStatuses.ACCEPTED,
                first_name="TEST",
                last_name="TEST",
                partnership=self.partner_with_feedback,
            )
            steerage5.lock(user=self.provider)
            steerage5.save()
            steerage6 = self._create_steerage_with_multiple_providers()
            steerage7 = self._create_steerage_with_multiple_providers(member_selected=False)
            steerage8 = self._create_steerage_with_no_feedback_partner()

            management.call_command(
                "assign_feedback_form",
                user=self.provider,
                dry_run_off=False,
            )

            # Refresh all steerages from database
            for steerage in [steerage1, steerage2, steerage3, steerage4, steerage5, steerage6, steerage7, steerage8]:
                steerage.refresh_from_db()

            # Assertions
            self.assertEqual(steerage4.feedback_form, form_submission)
            self.assertIsNone(steerage1.feedback_form)
            self.assertIsNone(steerage2.feedback_form)
            self.assertIsNone(steerage3.feedback_form)
            self.assertIsNone(steerage5.feedback_form)
            self.assertIsNone(steerage6.feedback_form)
            self.assertIsNone(steerage7.feedback_form)
            self.assertIsNone(steerage8.feedback_form)
