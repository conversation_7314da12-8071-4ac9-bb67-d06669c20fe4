from copy import deepcopy
from datetime import datetime
from random import randint
from typing import ClassVar, Optional
from unittest import mock
from unittest.mock import <PERSON><PERSON><PERSON>, Mock

from django.contrib.contenttypes.models import ContentType
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.geos import Point
from django.db.models.query import QuerySet
from dramatiq.rate_limits.backends import StubBackend
from faker import Faker

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.feature.testutils import override_switch
from firefly.core.services.ribbon.types import (
    RibbonLocation,
    RibbonLocationResponse,
    RibbonProvider,
    RibbonProviderResponse,
)
from firefly.core.services.talon.types import TalonProcedurePricesResponse
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import get_mock_redis, reset_context_to_luci_user
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.models.models import Person
from firefly.modules.code_systems.factories import CPTCodeFactory
from firefly.modules.facts.factories import (
    ClinicalFocusAreaFactory,
    LocationTypeFactory,
    SpecialtyFactory,
    SpecialtyGroupFactory,
)
from firefly.modules.facts.models import (
    ClinicalFocusArea,
    LocationType,
    ServiceClinicalFocusAreas,
    Specialty,
    SpecialtyGroup,
)
from firefly.modules.insurance.constants import ContractAttributionType, ContractPMPMType
from firefly.modules.insurance.factories import (
    ContractFactory,
    InsurancePayerFactory,
    InsurancePlanFactory,
    NetworkFactory,
)
from firefly.modules.insurance.models import InsuranceMemberInfo, InsurancePayer, InsurancePlan, Network
from firefly.modules.network.constants import CuratedProviderRankingConfig
from firefly.modules.network.factories import (
    CuratedProviderFactory,
    CuratedProviderPartnershipFactory,
    PartnershipFactory,
    RibbonLocationFactory,
    RibbonProviderFactory,
)
from firefly.modules.network.models import (
    AgreementTypeConfig,
    CuratedProvider,
    CuratedProviderRankingLevelConfig,
    CuratedProviderRankingReasonConfig,
    CuratedProviderType,
    Partnership,
    PartnershipContract,
    PartnershipInsurancePayer,
    PartnershipServicingStateExclusion,
    PartnershipType,
    Ranking,
)
from firefly.modules.network.utils.utils import clear_cache_data
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, remove_person_from_program
from firefly.modules.referral.constants import (
    WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2,
    WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER,
    WAFFLE_SWITCH_STORE_TINS,
    AnnotatedMatchedSanitizedDescriptionCountCuratedProvider,
    LocationTypes,
    MemberServiceLevel,
    SystemSuggestedProviderConfig,
)
from firefly.modules.referral.factories import (
    PartnershipClinicalFocusAreaFactory,
    PartnershipServiceFactory,
    ReferralFactory,
    SearchAvailabilityFactory,
    ServiceFactory,
    SteerageFactory,
    TalonProcedurePricesFactory,
    WaiverFactory,
)
from firefly.modules.referral.models import (
    LocationTypePartnershipType,
    PartnershipClinicalFocusArea,
    PartnershipService,
    RecommendationStatus,
    RecommendationStatusChoices,
    Referral,
    SearchRequest,
    SearchRequestVendor,
    Service,
    ServiceCPTCode,
    Steerage,
    SteerageClinicalFocusArea,
    SteerageProvider,
    SteerageProviderDataSourceConfig,
    SteerageProviderStatuses,
    SteerageRequestTypeConfig,
    SteerageService,
    SteerageTypeOfVisit,
)
from firefly.modules.referral.tasks import add_system_suggested_providers_async
from firefly.modules.referral.utils.provider_search_utils import (
    ProcessedRibbonSearchResult,
    ProviderSearchResult,
    get_clinical_focus_area_uids_for_steerage,
    process_ribbon_results,
)
from firefly.modules.referral.utils.referral_utils import filter_recommended_providers_from_search_results
from firefly.modules.referral.utils.suggested_provider_utils import (
    convert_partners_into_provider_search_result,
    get_suggested_in_person_partners,
    get_suggested_partners,
    get_suggested_remote_partners,
    sort_suggested_partners,
)
from firefly.modules.states.models import State


@mock.patch(
    "firefly.modules.network.utils.utils.get_redis_cache",
)
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SystemSuggestedRibbonProvidersTestCase(FireflyTestCase):
    # Add type annotations for class attributes
    ribbon_response: ClassVar[RibbonProviderResponse]
    person: ClassVar[Person]
    specialty_group: ClassVar[SpecialtyGroup]
    specialty: ClassVar[Specialty]
    mock_redis_method: ClassVar[MagicMock]
    payer: ClassVar[InsurancePayer]
    plan: ClassVar[InsurancePlan]
    network: ClassVar[Network]
    network_alias: ClassVar[AliasMapping]
    zipcode: ClassVar[str]
    street_address: ClassVar[str]
    street_address_2: ClassVar[str]
    city: ClassVar[str]
    address: ClassVar[str]

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        # Setup insurance details for person
        clear_cache_data()
        mock_redis_obj = get_mock_redis()
        # binding a side_effect of a MagicMock instance with redis methods
        mock_redis_method = MagicMock()
        mock_redis_method.get = Mock(side_effect=mock_redis_obj.get)
        mock_redis_method.set = Mock(side_effect=mock_redis_obj.set)
        mock_redis_method.flushall = Mock(side_effect=mock_redis_obj.flushall)
        cls.mock_redis_method = mock_redis_method
        cls.mock_redis_method.flushall()

        # Intentionally mock this as BCBS Blue based on real ribbon data.
        # This allows us to sanity check ribbon and our mocks against the real thing
        # by turning off the test flag temporarily
        cls.payer = InsurancePayerFactory.create(name="BCBS MSA", payer_codes=["64222"])
        cls.plan = InsurancePlanFactory.create(name="0750 - HMO BLUE NEW ENGLAND DEDUCTIBLE", insurance_payer=cls.payer)
        cls.network = NetworkFactory.create(name="Blue Cross Blue Shield of Massachusetts - Blue New England - HMO")
        cls.plan.networks.add(cls.network)
        cls.plan.save()

        cls.network_alias = AliasMapping.set_mapping_by_object(
            obj=cls.network,
            alias_name=AliasName.RIBBON,
            alias_id="09acf90b-503d-4d78-ade2-da3b5dab12a2",
        )

        cls.person = PersonUserFactory.create()
        cls.person.insurance_info.insurance_payer = cls.payer
        cls.person.insurance_info.plan_description = cls.plan.name
        cls.person.insurance_info.insurance_plan = cls.plan
        fake = Faker()
        cls.zipcode = "%05d" % randint(1, 99999)
        cls.street_address = fake.street_address()
        cls.street_address_2 = fake.street_address()
        cls.city = fake.city()
        cls.address = (
            f"{cls.street_address} {cls.street_address_2} {cls.city} {cls.person.insurance_info.state} {cls.zipcode} "
        )
        cls.person.insurance_info.street_address = cls.street_address
        cls.person.insurance_info.street_address_2 = cls.street_address_2
        cls.person.insurance_info.city = cls.city
        cls.person.insurance_info.zipcode = cls.zipcode
        cls.person.insurance_info.save()

        # Create dermatology specialty
        cls.specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        cls.specialty: Specialty = SpecialtyFactory.create(specialty_groups=(cls.specialty_group,), label="Dermatology")
        cls.specialty.save()
        AliasMapping.set_mapping_by_object(
            obj=cls.specialty,
            alias_name=AliasName.RIBBON,
            alias_id="dermatology_specialty_uid",
        )

        # Create three suggested providers and one not suggested provider as a mock response to ribbon call
        providers = []
        for i in range(3):
            recommended_provider_location: RibbonLocation = RibbonLocationFactory.create(confidence=3, distance=12.34)
            recommended_provider: RibbonProvider = RibbonProviderFactory.create(
                locations=[recommended_provider_location],
                recommendation_status=RecommendationStatus.RECOMMENDED,
                ratings_count=50,
                ratings_avg=10,
            )
            recommended_provider["npi"] = recommended_provider["npi"][:10]
            recommended_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
            recommended_provider["performance"]["aggregate"]["cost"]["ribbon_cost_score"] = 1
            recommended_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
            providers.append(recommended_provider)

        # Add a provider that has a location without a practice name -> this should NOT be added to the steerage
        recommended_provider_location_without_name: RibbonLocation = RibbonLocationFactory.create(
            confidence=3, distance=12.34, name=None
        )
        recommended_provider_without_name: RibbonProvider = RibbonProviderFactory.create(
            locations=[recommended_provider_location_without_name],
            recommendation_status=RecommendationStatus.RECOMMENDED,
            ratings_count=50,
            ratings_avg=10,
        )
        recommended_provider_without_name["npi"] = recommended_provider_without_name["npi"][:10]
        recommended_provider_without_name["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        recommended_provider_without_name["performance"]["aggregate"]["cost"]["ribbon_cost_score"] = 1
        recommended_provider_without_name["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        providers.append(recommended_provider_without_name)

        # But let's create a recommended provider with two locations but one does not have a name, the one location
        # should be added to the steerage
        recommended_provider_location: RibbonLocation = RibbonLocationFactory.create(confidence=3, distance=12.34)
        recommended_provider_location_without_name: RibbonLocation = RibbonLocationFactory.create(
            confidence=3, distance=12.34, name=None
        )
        recommended_provider: RibbonProvider = RibbonProviderFactory.create(
            locations=[recommended_provider_location, recommended_provider_location_without_name],
            recommendation_status=RecommendationStatus.RECOMMENDED,
            ratings_count=50,
            ratings_avg=10,
        )
        recommended_provider["npi"] = recommended_provider["npi"][:10]
        recommended_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 1
        recommended_provider["performance"]["aggregate"]["cost"]["ribbon_cost_score"] = 1
        recommended_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 5
        providers.append(recommended_provider)

        not_recommended_provider_location: RibbonLocation = RibbonLocationFactory.create()
        not_recommended_provider: RibbonProvider = RibbonProviderFactory.create(
            locations=[not_recommended_provider_location],
            recommendation_status=RecommendationStatus.NOT_RECOMMENDED,
            ratings_count=50,
            ratings_avg=3,
        )
        not_recommended_provider["performance"]["aggregate"]["cost"]["efficiency_index"] = 10
        not_recommended_provider["performance"]["aggregate"]["cost"]["ribbon_cost_score"] = 10
        not_recommended_provider["performance"]["aggregate"]["quality"]["outcomes_index"] = 1
        not_recommended_provider["npi"] = not_recommended_provider["npi"][:10]
        providers.append(not_recommended_provider)

        # Mock the ribbon response with the above providers
        cls.ribbon_response: RibbonProviderResponse = {
            "parameters": {"total_count": 6, "sort_by": "distance", "page": 1, "page_size": 4},
            "data": deepcopy(providers),
        }

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_async_called(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

    @override_switch(WAFFLE_SWITCH_STORE_TINS, active=True)
    @mock.patch("firefly.modules.network.tasks.update_tin_details_for_tax_identifier")
    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers(
        self,
        add_system_suggested_async_mock,
        update_tin_details_for_tax_identifier_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        steerage_provider: SteerageProvider
        for steerage_provider in SteerageProvider.objects.all():
            self.assertEqual(steerage_provider.status, SteerageProviderStatuses.UNDER_REVIEW)
            self.assertEqual(steerage_provider.steerage, steerage)
            self.assertEqual(steerage_provider.recommendation_status, RecommendationStatusChoices.RECOMMENDED)
            self.assertEqual(steerage_provider.is_system_suggested, True)
            self.assertIsNotNone(steerage_provider.search_request)
            self.assertEqual(steerage_provider.composite_score, 5.0)
            self.assertEqual(steerage_provider.cost_score, 5.0)
            self.assertEqual(steerage_provider.quality_score, 5.0)
            self.assertEqual(steerage_provider.number_of_rating, 50)
            self.assertEqual(steerage_provider.average_rating, 5.0)
            self.assertEqual(steerage_provider.recommendation_reason, ["Great cost", "quality", "ratings"])
            self.assertEqual(steerage_provider.languages, ["English", "Spanish", "Korean"])
            self.assertEqual(steerage_provider.location_confidence_score, 3)
            self.assertEqual(steerage_provider.distance, 12.34)
            # Since there are 2 TINs associated with the location, we should get 2 TaxIdentifer objects associated
            # to the SteerageProvider (one is a dupe so we shouldn't have 3)
            tax_identifiers = steerage_provider.tax_identifiers
            self.assertEqual(tax_identifiers.count(), 2)
            for tax_identifier in tax_identifiers.iterator():
                self.assertIsNotNone(tax_identifier.tin)

        # 3 steerage providers added with 2 tins but we have 2 duplicates:
        # First steerage provider added has 2 unique ones
        # Second and third steerage providers added have 1 unique one each
        self.assertEqual(update_tin_details_for_tax_identifier_mock.send.call_count, 4)
        update_tin_details_for_tax_identifier_mock.reset_mock()

        # Confirm a search request was created and that is_system_suggestion is True
        search_request = SearchRequest.objects.filter(steerage=steerage)
        self.assertIsNotNone(search_request.first())
        self.assertTrue(search_request.first().is_automated_suggestion)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_coverage(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_request_type(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # CASE 1 - Specific request

        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.specialty_group = self.specialty_group
        steerage.request_type = SteerageRequestTypeConfig.SPECIFIC_REQUEST
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should not add steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        # CASE 2 - Broad request

        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 3)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.specialty_group = self.specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 6)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_person_whoes_address_not_aded(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # Update address zip code to None
        self.person.insurance_info.zipcode = None
        self.person.insurance_info.save()
        self.person.insurance_info.refresh_from_db()
        self.assertEqual(self.person.insurance_info.zipcode, None)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.specialty_group = self.specialty_group
        steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should not generate exception
        try:
            add_system_suggested_providers_async(steerage.id)
        except Exception:
            self.fail("Unexpected exception raised")

        self.assertEqual(SteerageProvider.objects.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_person_whoes_plan_not_mapped(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # Update plan and payer to None
        self.person.insurance_info.insurance_payer = None
        self.person.insurance_info.insurance_plan = None
        self.person.insurance_info.save()
        self.person.insurance_info.refresh_from_db()
        self.assertEqual(self.person.insurance_info.insurance_payer, None)
        self.assertEqual(self.person.insurance_info.insurance_plan, None)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.specialty_group = self.specialty_group
        steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should not generate exception
        try:
            add_system_suggested_providers_async(steerage.id)
        except Exception:
            self.fail("Unexpected exception raised")

        self.assertEqual(SteerageProvider.objects.count(), 0)

    def test_filter_system_suggested_providers_from_ribbon(self, _ribbon_call, _mutex_mock, _get_redis_cache_mock):
        processed_results: ProcessedRibbonSearchResult = process_ribbon_results(
            vendor_results=self.ribbon_response,
            insurance_uids=[],
            member_service_level=MemberServiceLevel.CARE,
        )
        provider_search_results: ProviderSearchResult = {
            "vendor_http_status_code": 200,
            "vendor_results": self.ribbon_response,
            "records_available": 0,
            "search_results": deepcopy(processed_results["search_results"]),
            "payload": "",
        }

        recommended_results = filter_recommended_providers_from_search_results(provider_search_results)
        self.assertEquals(len(recommended_results), 4)

        for result in recommended_results:
            self.assertIsNotNone(result["locations"])
            self.assertNotEqual(len(result["locations"]), 0)
            self.assertEqual(result["recommendation_status"], RecommendationStatus.RECOMMENDED)
            for location in result["locations"]:
                self.assertIsNotNone(location["name"])
                self.assertNotEqual(location["name"], "")

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_when_last_mile_as_false(
        self,
        _add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Case: The system suggested partner is present but the referral is not firefly nearby
        #       This should fallback to ribbon recommended provider search

        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        person: Person = self.person
        person.insurance_info.latitude = 34.0522
        person.insurance_info.longitude = -118.2437
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
            latitude=40.7128,
            longitude=-74.0060,
            point=Point(
                x=float(-74.0060),
                y=float(40.7128),
                srid=4326,
            ),
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=False, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Calling the add_system_suggested_providers_async should add three Ribbon steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(
            SteerageProvider.objects.filter(data_source=SteerageProviderDataSourceConfig.RIBBON).count(), 3
        )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_cached_alias_id_from_specialty")
    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    @override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, False)
    def test_add_system_suggested_providers_based_on_clinical_focus_area(
        self,
        add_system_suggested_async_mock,
        mock_get_cached_alias,  # Add this parameter
        ribbon_call: MagicMock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        """
        Test that when a steerage has services with clinical focus areas, search uses them instead of specialty uids.
        When the clinical focus area search doesn't return enough providers, fallback search should use specialty uids.
        """
        # Configure the mock to return the specialty's Ribbon alias ID
        mock_get_cached_alias.side_effect = lambda specialty: AliasMapping.get_alias_id_for_object(
            obj=specialty, alias_name=AliasName.RIBBON
        )
        payload = {
            "page": 1,
            "address": "02472",
            "sort_by": "distance",
            "distance": 10,
            "page_size": 100,
            "specialty_ids": "8257ce19-3245-47b0-bdd5-eb07ca777513",
            "location_insurance_ids": "95520e0d-a6f8-401a-bfcd-9c0c85802cfe",
            "min_location_confidence": 3,
            "location_within_distance": True,
        }

        # Mock the search response using existing ribbon_response
        ribbon_response = deepcopy(self.ribbon_response)
        ribbon_response["data"] = []
        ribbon_call.return_value = (ribbon_response, 200), payload

        # Create a steerage with specialty group and services with clinical focus areas
        service_1 = ServiceFactory()
        service_2 = ServiceFactory()
        clinical_focus_area_1 = ClinicalFocusAreaFactory()
        clinical_focus_area_2 = ClinicalFocusAreaFactory()
        # This clinical focus area is deprecated, so it should not be used in the search
        clinical_focus_area_3 = ClinicalFocusAreaFactory(is_deprecated=True)
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_1,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_1",
        )
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_2,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_2",
        )
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_3,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_3",
        )
        ServiceClinicalFocusAreas.objects.create(service=service_1, clinical_focus_area=clinical_focus_area_1)
        ServiceClinicalFocusAreas.objects.create(service=service_2, clinical_focus_area=clinical_focus_area_2)
        ServiceClinicalFocusAreas.objects.create(service=service_2, clinical_focus_area=clinical_focus_area_3)
        steerage = SteerageFactory(
            person=self.person,
            type_of_visit=SteerageTypeOfVisit.IN_PERSON,
            request_type=SteerageRequestTypeConfig.SPECIFIC_REQUEST,
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        # Add specialty group to trigger the async task
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Verify the steerage has two services
        self.assertEqual(len(steerage.services.all()), 2)
        # Verify the steerage should have two clinical focus area uids to use for search
        clinical_focus_area_uids = get_clinical_focus_area_uids_for_steerage(steerage)
        self.assertEqual(len(clinical_focus_area_uids), 2)

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(steerage.id)

        # Verify the search was called twice
        self.assertEqual(ribbon_call.call_count, 2)

        # Verify the search was called with clinical focus area uids for the first search
        ribbon_initial_call = ribbon_call.mock_calls[0]
        ribbon_fallback_call = ribbon_call.mock_calls[1]

        # Assert clinical focus areas were used and specialty uids were empty in initial search
        initial_clinical_focus_area_uid_kwarg: list[str] = []
        initial_clinical_focus_area_uid_kwarg = ribbon_initial_call.kwargs.get(
            "clinical_focus_area_uids", initial_clinical_focus_area_uid_kwarg
        )
        initial_speciality_uid_kwarg: list[str] = []
        initial_speciality_uid_kwarg = ribbon_initial_call.kwargs.get("speciality_uids", initial_speciality_uid_kwarg)
        self.assertIn("clinical_focus_area_1", initial_clinical_focus_area_uid_kwarg)
        self.assertIn("clinical_focus_area_2", initial_clinical_focus_area_uid_kwarg)
        self.assertEqual(clinical_focus_area_uids, initial_clinical_focus_area_uid_kwarg)
        self.assertIn("speciality_uids", ribbon_initial_call.kwargs)
        self.assertEqual(len(initial_speciality_uid_kwarg), 0)

        # Assert clinical focus areas were not used in fallback search
        fallback_clinical_focus_area_uid_kwarg: list[str] = []
        fallback_clinical_focus_area_uid_kwarg = ribbon_fallback_call.kwargs.get(
            "clinical_focus_area_uids", fallback_clinical_focus_area_uid_kwarg
        )
        fallback_speciality_uid_kwarg: list[str] = []
        fallback_speciality_uid_kwarg = ribbon_fallback_call.kwargs.get(
            "speciality_uids", fallback_speciality_uid_kwarg
        )
        self.assertNotIn("clinical_focus_area_1", fallback_clinical_focus_area_uid_kwarg)
        self.assertNotIn("clinical_focus_area_2", fallback_clinical_focus_area_uid_kwarg)
        self.assertEqual([], fallback_clinical_focus_area_uid_kwarg)
        self.assertIn("speciality_uids", ribbon_fallback_call.kwargs)
        self.assertEqual(len(fallback_speciality_uid_kwarg), 1)

        # There should be two SearchRequest objects at this point:
        # - First for the initial search using ClinicalFocusAreas
        # - Second for the fallback search
        search_requests = SearchRequest.objects.filter(steerage=steerage)
        specialty_uid = AliasMapping.get_alias_id_for_object(obj=self.specialty, alias_name=AliasName.RIBBON)
        self.assertEqual(search_requests.count(), 2)
        self.assertEqual(search_requests[0].clinical_focus_area_uids, clinical_focus_area_uids)
        self.assertEqual(search_requests[0].vendor, SearchRequestVendor.RIBBON)
        self.assertEqual(search_requests[0].specialty_uids, [])
        self.assertIsNotNone(search_requests[0].payload)
        self.assertEqual(search_requests[0].payload, payload)
        self.assertEqual(search_requests[1].clinical_focus_area_uids, [])
        self.assertEqual(search_requests[1].specialty_uids, [specialty_uid])
        self.assertEqual(search_requests[1].vendor, SearchRequestVendor.RIBBON)

        # No providers should be added to the steerage with the overridden search results
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # If we restore the results, the providers should be added to the steerage
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        add_system_suggested_providers_async(steerage.id)

        # Verify the providers were added to the steerage
        self.assertEqual(SteerageProvider.objects.count(), 3)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_cached_alias_id_from_specialty")
    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_based_on_clinical_focus_area_v2(
        self,
        add_system_suggested_async_mock,
        mock_get_cached_alias,  # Add this parameter
        ribbon_call: MagicMock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        """
        Test that when a steerage has services with clinical focus areas, search uses them instead of specialty uids.
        When the clinical focus area search doesn't return enough providers, fallback search should use specialty uids.
        """
        # Configure the mock to return the specialty's Ribbon alias ID
        mock_get_cached_alias.side_effect = lambda specialty: AliasMapping.get_alias_id_for_object(
            obj=specialty, alias_name=AliasName.RIBBON
        )
        payload = {
            "page": 1,
            "address": "02472",
            "sort_by": "distance",
            "distance": 10,
            "page_size": 100,
            "specialty_ids": "8257ce19-3245-47b0-bdd5-eb07ca777513",
            "location_insurance_ids": "95520e0d-a6f8-401a-bfcd-9c0c85802cfe",
            "min_location_confidence": 3,
            "location_within_distance": True,
        }

        # Mock the search response using existing ribbon_response
        ribbon_response = deepcopy(self.ribbon_response)
        ribbon_response["data"] = []
        ribbon_call.return_value = (ribbon_response, 200), payload

        # Create a steerage with specialty group and services with clinical focus areas
        service_1 = ServiceFactory()
        clinical_focus_area_1 = ClinicalFocusAreaFactory()
        clinical_focus_area_2 = ClinicalFocusAreaFactory()
        # This clinical focus area is deprecated, so it should not be used in the search
        clinical_focus_area_3 = ClinicalFocusAreaFactory(is_deprecated=True)
        # Let's only set an alias mapping for two of the clinical focus areas since we will have
        # clinical focus areas without alias mappings to Ribbon
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_1,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_1",
        )
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_3,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_3",
        )
        steerage = SteerageFactory(
            person=self.person,
            type_of_visit=SteerageTypeOfVisit.IN_PERSON,
            request_type=SteerageRequestTypeConfig.SPECIFIC_REQUEST,
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=clinical_focus_area_1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=clinical_focus_area_2)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=clinical_focus_area_3)
        # Add specialty group to trigger the async task
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Verify the steerage has 1 service and 3 clinical focus areas
        self.assertEqual(steerage.services.count(), 1)
        self.assertEqual(steerage.clinical_focus_areas.count(), 3)
        # Verify the steerage should have one clinical focus area uids to use for search
        # minus deprecated cfa and cfa without alias mapping to ribbon
        clinical_focus_area_uids = get_clinical_focus_area_uids_for_steerage(steerage)
        self.assertEqual(len(clinical_focus_area_uids), 1)

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(steerage.id)

        # Verify the search was called twice
        self.assertEqual(ribbon_call.call_count, 2)

        # Verify the search was called with clinical focus area uids for the first search
        ribbon_initial_call = ribbon_call.mock_calls[0]
        ribbon_fallback_call = ribbon_call.mock_calls[1]

        # Assert clinical focus areas were used and specialty uids were empty in initial search
        initial_clinical_focus_area_uid_kwarg: list[str] = []
        initial_clinical_focus_area_uid_kwarg = ribbon_initial_call.kwargs.get(
            "clinical_focus_area_uids", initial_clinical_focus_area_uid_kwarg
        )
        initial_speciality_uid_kwarg: list[str] = []
        initial_speciality_uid_kwarg = ribbon_initial_call.kwargs.get("speciality_uids", initial_speciality_uid_kwarg)
        self.assertIn("clinical_focus_area_1", initial_clinical_focus_area_uid_kwarg)
        self.assertEqual(clinical_focus_area_uids, initial_clinical_focus_area_uid_kwarg)
        self.assertIn("speciality_uids", ribbon_initial_call.kwargs)
        self.assertEqual(len(initial_speciality_uid_kwarg), 0)

        # Assert clinical focus areas were not used in fallback search
        fallback_clinical_focus_area_uid_kwarg: list[str] = []
        fallback_clinical_focus_area_uid_kwarg = ribbon_fallback_call.kwargs.get(
            "clinical_focus_area_uids", fallback_clinical_focus_area_uid_kwarg
        )
        fallback_speciality_uid_kwarg: list[str] = []
        fallback_speciality_uid_kwarg = ribbon_fallback_call.kwargs.get(
            "speciality_uids", fallback_speciality_uid_kwarg
        )
        self.assertNotIn("clinical_focus_area_1", fallback_clinical_focus_area_uid_kwarg)
        self.assertNotIn("clinical_focus_area_2", fallback_clinical_focus_area_uid_kwarg)
        self.assertEqual([], fallback_clinical_focus_area_uid_kwarg)
        self.assertIn("speciality_uids", ribbon_fallback_call.kwargs)
        self.assertEqual(len(fallback_speciality_uid_kwarg), 1)

        # There should be two SearchRequest objects at this point:
        # - First for the initial search using ClinicalFocusAreas
        # - Second for the fallback search
        search_requests = SearchRequest.objects.filter(steerage=steerage)
        specialty_uid = AliasMapping.get_alias_id_for_object(obj=self.specialty, alias_name=AliasName.RIBBON)
        self.assertEqual(search_requests.count(), 2)
        self.assertEqual(search_requests[0].clinical_focus_area_uids, clinical_focus_area_uids)
        self.assertEqual(search_requests[0].vendor, SearchRequestVendor.RIBBON)
        self.assertEqual(search_requests[0].specialty_uids, [])
        self.assertIsNotNone(search_requests[0].payload)
        self.assertEqual(search_requests[0].payload, payload)
        self.assertEqual(search_requests[1].clinical_focus_area_uids, [])
        self.assertEqual(search_requests[1].specialty_uids, [specialty_uid])
        self.assertEqual(search_requests[1].vendor, SearchRequestVendor.RIBBON)

        # No providers should be added to the steerage with the overridden search results
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # If we restore the results, the providers should be added to the steerage
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        add_system_suggested_providers_async(steerage.id)

        # Verify the providers were added to the steerage
        self.assertEqual(SteerageProvider.objects.count(), 3)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_disable_clinical_focus_area_search_when_other_service_present(
        self,
        add_system_suggested_async_mock,
        ribbon_call: MagicMock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        """
        Test that when a steerage has a service labeled "Other", the clinical focus area search is disabled.
        """
        # TODO: We can remove this test after fulling migrating to Clinical Focus Areas V2 since Other will no longer
        # be a service that we maintain anymore. And if it is, it should be independent of clinical focus areas.

        # Mock the search response using existing ribbon_response
        ribbon_response = deepcopy(self.ribbon_response)
        ribbon_response["data"] = []
        ribbon_call.return_value = (ribbon_response, 200), ""

        # Create a steerage with specialty group and services with clinical focus areas
        service_1 = ServiceFactory()
        service_2 = ServiceFactory()
        service_3 = ServiceFactory(description="Other")
        clinical_focus_area_1 = ClinicalFocusAreaFactory()
        clinical_focus_area_2 = ClinicalFocusAreaFactory()
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_1,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_1",
        )
        AliasMapping.set_mapping_by_object(
            obj=clinical_focus_area_2,
            alias_name=AliasName.RIBBON,
            alias_id="clinical_focus_area_2",
        )
        steerage = SteerageFactory(
            person=self.person,
            type_of_visit=SteerageTypeOfVisit.IN_PERSON,
            request_type=SteerageRequestTypeConfig.SPECIFIC_REQUEST,
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        SteerageService.objects.create(steerage=steerage, service=service_3)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=clinical_focus_area_1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=clinical_focus_area_2)
        # Add specialty group to trigger the async task
        steerage.specialty_group = self.specialty_group
        steerage.save()

        # Verify the steerage has three services
        self.assertEqual(len(steerage.services.all()), 3)
        # Verify the steerage should have two clinical focus area uids to use for search
        clinical_focus_area_uids = get_clinical_focus_area_uids_for_steerage(steerage)
        self.assertEqual(len(clinical_focus_area_uids), 2)

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(steerage.id)

        # Verify the search was called without any clinical focus area uids
        ribbon_initial_call = ribbon_call.mock_calls[0]
        initial_clinical_focus_area_uid_kwarg: list[str] | None = None
        initial_clinical_focus_area_uid_kwarg = ribbon_initial_call.kwargs.get("clinical_focus_area_uids", None)
        self.assertEqual(initial_clinical_focus_area_uid_kwarg, [])


@mock.patch(
    "firefly.modules.network.utils.utils.get_redis_cache",
)
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch("firefly.modules.referral.utils.provider_search_utils.get_facilities")
@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SystemSuggestedRibbonFacilitiesTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # Setup insurance details for person
        clear_cache_data()
        mock_redis_obj = get_mock_redis()
        # binding a side_effect of a MagicMock instance with redis methods
        mock_redis_method = MagicMock()
        mock_redis_method.get = Mock(side_effect=mock_redis_obj.get)
        mock_redis_method.set = Mock(side_effect=mock_redis_obj.set)
        mock_redis_method.flushall = Mock(side_effect=mock_redis_obj.flushall)
        self.mock_redis_method = mock_redis_method
        self.mock_redis_method.flushall()

        # Intentionally mock this as BCBS Blue based on real ribbon data.
        # This allows us to sanity check ribbon and our mocks against the real thing
        # by turning off the test flag temporarily
        self.payer = InsurancePayerFactory.create(name="BCBS MSA", payer_codes=["64222"])
        self.plan = InsurancePlanFactory.create(
            name="0750 - HMO BLUE NEW ENGLAND DEDUCTIBLE", insurance_payer=self.payer
        )
        self.network = NetworkFactory.create(name="Blue Cross Blue Shield of Massachusetts - Blue New England - HMO")
        self.plan.networks.add(self.network)
        self.plan.save()

        self.network_alias = AliasMapping.set_mapping_by_object(
            obj=self.network,
            alias_name=AliasName.RIBBON,
            alias_id="09acf90b-503d-4d78-ade2-da3b5dab12a2",
        )

        self.person = PersonUserFactory.create()
        self.person.insurance_info.insurance_payer = self.payer
        self.person.insurance_info.plan_description = self.plan.name
        self.person.insurance_info.insurance_plan = self.plan
        fake = Faker()
        self.zipcode = "%05d" % randint(1, 99999)
        self.street_address = fake.street_address()
        self.street_address_2 = fake.street_address()
        self.city = fake.city()
        self.address = (
            f"{self.street_address} "
            f"{self.street_address_2} "
            f"{self.city} "
            f"{self.person.insurance_info.state} "
            f"{self.zipcode} "
        )
        self.person.insurance_info.street_address = self.street_address
        self.person.insurance_info.street_address_2 = self.street_address_2
        self.person.insurance_info.city = self.city
        self.person.insurance_info.zipcode = self.zipcode
        self.person.insurance_info.save()
        self.person.insurance_info.refresh_from_db()

        # Create Imagine Center Location Type
        self.location_type: LocationType = LocationTypeFactory.create(label="Imaging Center")

        # Create three suggested providers and one not suggested provider as a mock response to ribbon call
        facilities = []
        for i in range(3):
            recommended_provider_location: RibbonLocation = RibbonLocationFactory.create(confidence=3, distance=12.34)
            facilities.append(recommended_provider_location)

            curated_provider = CuratedProvider.objects.create(
                npi=None,
                care_org_name=recommended_provider_location["name"],
                zip_code=recommended_provider_location["address_details"]["zip"],
                address_line_1=None,
                address_line_2=None,
                city=None,
                state=None,
            )

            Ranking.objects.get_or_create(
                curated_provider=curated_provider,
                reason=CuratedProviderRankingReasonConfig.LOW_COST,
                ranking_level=CuratedProviderRankingLevelConfig.CARE_ORG,
                defaults={
                    "rank_change": CuratedProviderRankingConfig.CURATED_PROVIDER_UPRANKING_RANK_CHANGE,
                },
            )

        # Mock the ribbon response with the above providers
        self.ribbon_response: RibbonLocationResponse = {
            "parameters": {"total_count": 6, "sort_by": "distance", "page": 1, "page_size": 4},
            "data": deepcopy(facilities),
        }

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        for steerage_provider in SteerageProvider.objects.all():
            self.assertEqual(steerage_provider.status, SteerageProviderStatuses.UNDER_REVIEW)
            self.assertEqual(steerage_provider.steerage, steerage)
            self.assertEqual(steerage_provider.recommendation_status, RecommendationStatusChoices.RECOMMENDED)
            self.assertEqual(steerage_provider.is_system_suggested, True)
            self.assertIsNotNone(steerage_provider.search_request)
            self.assertEqual(steerage_provider.recommendation_reason, [CuratedProviderRankingReasonConfig.LOW_COST])
            self.assertEqual(steerage_provider.location_confidence_score, 3)
            self.assertEqual(steerage_provider.distance, 12.34)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_coverage(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        waiver = WaiverFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, waiver=waiver)
        steerage.location_type = self.location_type
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_not_recommended_facilities(
        self,
        add_system_suggested_async_mock,
        ribbon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mark all the existing facilities as unknown by deleting there ranking
        Ranking.objects.all().delete()

        # Mock the ribbon response data
        ribbon_call.return_value = ((deepcopy(self.ribbon_response), 200), "")
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented and it is for dermatology referral
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should not add steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 0)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SystemSuggestedInPersonPartnersTestCase(FireflyTestCase):
    logger_prefix = "SystemSuggestedProvidersTestCase"

    def test_get_suggested_partner(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with services and call the get_suggested_partner
        # Result
        #   It should return all the matching curated partners

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON)
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)

    def test_get_suggested_partner_with_clinical_focus_areas(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with one service and focus areas
        #   and call the get_suggested_partner
        # Result
        #   It should return all the matching curated partners

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        cfa1: ClinicalFocusArea = ClinicalFocusAreaFactory()
        cfa2: ClinicalFocusArea = ClinicalFocusAreaFactory()

        # Created a steerage
        steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON)
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=cfa1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=cfa2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipClinicalFocusAreaFactory(partnership=partnership, clinical_focus_area=cfa1)
            PartnershipClinicalFocusAreaFactory(partnership=partnership, clinical_focus_area=cfa2)

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)

    def test_get_suggested_partner_for_any_visit_type(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with services and call the get_suggested_partner
        # Result
        #   It should return all the matching in-person curated partners

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.ANY)
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)

    def test_get_suggested_partner_with_partial_service_match(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with services and call the get_suggested_partner
        # Result
        #   It should not return curated partners which have partial service match

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory(description="A")
        service_2: Service = ServiceFactory(description="B")

        # Created a steerage
        steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON)
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)
            PartnershipServiceFactory(partnership=partnership, service=ServiceFactory(description="C"))
            PartnershipServiceFactory(partnership=partnership, service=ServiceFactory(description="D"))

        # Create suggested partners with partial service match
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=ServiceFactory(description="E"))
            PartnershipServiceFactory(partnership=partnership, service=ServiceFactory(description="F"))

        # Create suggested partners with no service match
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=ServiceFactory(description="G"))

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)
        result_curated_provider_ids = []
        for result_curated_provider in result_curated_providers:
            result_curated_provider_ids.append(result_curated_provider.id)
        self.assertEqual(curated_provider_ids, result_curated_provider_ids)

    def test_get_suggested_partner_with_partial_clinical_focus_area_match(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with clinical focus areas and call the get_suggested_partner
        # Result
        #   It should not return curated partners which have partial service match

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        cfa1: ClinicalFocusArea = ClinicalFocusAreaFactory(label="A")
        cfa2: ClinicalFocusArea = ClinicalFocusAreaFactory(label="B")

        # Created a steerage
        steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=cfa1)
        SteerageClinicalFocusArea.objects.create(steerage=steerage, clinical_focus_area=cfa2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipClinicalFocusAreaFactory(partnership=partnership, clinical_focus_area=cfa1)
            PartnershipClinicalFocusAreaFactory(partnership=partnership, clinical_focus_area=cfa2)
            PartnershipClinicalFocusAreaFactory(
                partnership=partnership, clinical_focus_area=ClinicalFocusAreaFactory(label="C")
            )
            PartnershipClinicalFocusAreaFactory(
                partnership=partnership, clinical_focus_area=ClinicalFocusAreaFactory(label="D")
            )

        # Create suggested partners with partial service match
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipClinicalFocusAreaFactory(partnership=partnership, clinical_focus_area=cfa1)
            PartnershipClinicalFocusAreaFactory(
                partnership=partnership, clinical_focus_area=ClinicalFocusAreaFactory(label="E")
            )
            PartnershipClinicalFocusAreaFactory(
                partnership=partnership, clinical_focus_area=ClinicalFocusAreaFactory(label="F")
            )

        # Create suggested partners with no service match
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipClinicalFocusAreaFactory(
                partnership=partnership, clinical_focus_area=ClinicalFocusAreaFactory(label="G")
            )

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)
        result_curated_provider_ids = []
        for result_curated_provider in result_curated_providers:
            result_curated_provider_ids.append(result_curated_provider.id)
        self.assertEqual(curated_provider_ids, result_curated_provider_ids)

    def test_get_suggested_partner_with_different_visit_type(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with services but different visit type
        # Result
        #   It should only return the curated partners which have in_person provider type

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match and same visit type as in person
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Create suggested partners with different visit type as the member
        for i in range(0, 3):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_HOME,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(curated_providers)
        self.assertEqual(curated_providers.count(), 2)

    def test_get_suggested_partner_for_non_firefly_nearby_referral(self):
        # Scenario
        #   Create a steerage where is last mile is FALSE but have related mappings
        # Result
        #   It should return partners

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=False, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 3):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 3)

    def test_get_suggested_partner_without_services_or_clinical_focus_areas(self):
        # Scenario
        #   Create a steerage for firefly nearby referral without services and clinical focus areas
        # Result
        #   It should not return the curated partners

        person: Person = self.patient.person
        person.insurance_info.latitude = 34.0522
        person.insurance_info.longitude = -118.2437
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )

        # Create suggested partners with same zip_code as the member and in_person provider type
        for i in range(0, 3):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON, zip_code=person.insurance_info.zipcode
            )
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                partner_name=str(i),
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNone(curated_providers)

    def test_get_suggested_partner_for_client_point_solution(self):
        # Add contract to member
        person: Person = self.patient.person
        person.employer = None
        person.save(update_fields=["employer"])
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        person.eligible()
        insurance_member_info: InsuranceMemberInfo = person.insurance_info
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        insurance_member_info.insurance_plan.firefly_accepted = True
        insurance_member_info.insurance_plan.save()
        pcp_selection_contract = ContractFactory(
            config={
                "payer_group": "Anthem/BCBS",
                "payer_id": insurance_member_info.insurance_payer.id,
                "plan_description_specific": True,
                "accepts_out_of_state_payers": True,
                "attribution_type": ContractAttributionType.PCP_SELECTION,
                "insurance_state": "MA",
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
                "is_attribution_tracked_externally": True,
            },
            contracted_entity_content_type=ContentType.objects.get_for_model(InsurancePayer),
            contracted_entity=insurance_member_info.insurance_payer,
        )
        insurance_member_info.is_firefly_pcp = True
        insurance_member_info.firefly_pcp_at = datetime.now()
        insurance_member_info.save()

        person.attribution.contract = pcp_selection_contract
        person.attribution.save()

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create 1 in-person client point solutions

        self.curated_provider_client = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.IN_PERSON,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.CLIENT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_client, partnership=self.partnership)
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Create mapping b/w partnership and contract
        self.partnership_contract: PartnershipContract = PartnershipContract.objects.create(
            partnership=self.partnership,
            contract=pcp_selection_contract,
            is_primary_subscriber_only=False,
        )

        # Create 2nd in-home client provider
        self.curated_provider_client = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.IN_HOME,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.CLIENT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_client, partnership=self.partnership)
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Create mapping b/w partnership and contract
        self.partnership_contract: PartnershipContract = PartnershipContract.objects.create(
            partnership=self.partnership,
            contract=pcp_selection_contract,
            is_primary_subscriber_only=False,
        )

        # Fetch the partners
        curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert that only in-person partner is returned
        self.assertIsNotNone(curated_providers)
        self.assertEqual(curated_providers.count(), 1)

        # CASE 2 - If the contract is changed then the provider shouldn't return
        person.attribution.contract = None
        person.attribution.save()
        # Fetch the partners
        curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        self.assertEqual(curated_providers.count(), 0)

    def test_get_suggested_partner_for_in_person_preferred_vendor(self):
        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        state_ma, _ = State.objects.get_or_create(abbreviation="MA")
        person.insurance_info.state = state_ma.abbreviation
        person.insurance_info.plan_type = InsuranceMemberInfo.PLAN_TYPE_HMO
        person.insurance_info.group_number = "FF100"
        person.insurance_info.save()

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 2):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.PREFERRED_VENDOR,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

            self.partnership_insurance_payer: PartnershipInsurancePayer = PartnershipInsurancePayer.objects.create(
                partnership=partnership,
                insurance_payer=person.insurance_info.insurance_payer,
                insurance_plan_type=person.insurance_info.plan_type.upper(),
                state=state_ma,
                group_number="FF100",
            )

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertIsNotNone(result_curated_providers)
        self.assertEqual(result_curated_providers.count(), 2)

        # CASE 2 - IF insurance is removed then it should not match any providers
        PartnershipInsurancePayer.objects.all().delete()
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertEqual(result_curated_providers.count(), 0)

    def test_get_suggested_in_person_partner_limit(self):
        # Scenario
        #   Create a steerage for firefly nearby referral with services and call the get_suggested_partner
        # Result
        #   It should return only limited providers

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.8584
        person.insurance_info.longitude = -73.9293
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1: Service = ServiceFactory()
        service_2: Service = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=False, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)

        # Create suggested partners with all service match
        curated_provider_ids = []
        for i in range(0, 5):
            curated_provider = CuratedProviderFactory(
                provider_type=CuratedProviderType.IN_PERSON,
                zip_code=person.insurance_info.zipcode,
                latitude=40.8602,
                longitude=-73.9356,
                point=Point(
                    x=float(-73.9356),
                    y=float(40.8602),
                    srid=4326,
                ),
            )
            curated_provider_ids.append(curated_provider.id)
            partnership: Partnership = PartnershipFactory(
                can_accept_care_members=True,
                can_accept_coverage_members=True,
                can_accept_care_and_coverage_members=True,
                agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            )
            CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
            PartnershipServiceFactory(partnership=partnership, service=service_1)
            PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Fetch the partners
        result_curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=steerage, logger_prefix=self.logger_prefix)
        )
        converted_suggested_providers = convert_partners_into_provider_search_result(
            result_curated_providers, steerage.person, ""
        )
        sorted_provider_details = sort_suggested_partners(converted_suggested_providers)
        in_person_suggested_providers = sorted_provider_details[
            : SystemSuggestedProviderConfig.SYSTEM_SUGGESTED_PROVIDER_LIMIT
        ]

        # Assert
        self.assertIsNotNone(in_person_suggested_providers)
        self.assertEqual(
            len(in_person_suggested_providers), SystemSuggestedProviderConfig.SYSTEM_SUGGESTED_PROVIDER_LIMIT
        )

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_partner(self, add_system_suggested_async_mock):
        # Scenario
        #   Create a partner matching the steerage services and steerage details
        # Result
        #   Partner should be added as suggested provider on steerage

        # Example latitude and longitude for testing (New York)
        person: Person = self.patient.person
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 1)

        steerage_provider = SteerageProvider.objects.first()
        self.assertEqual(steerage_provider.status, SteerageProviderStatuses.UNDER_REVIEW)
        self.assertEqual(steerage_provider.steerage, steerage)
        self.assertEqual(steerage_provider.recommendation_status, RecommendationStatusChoices.RECOMMENDED)
        self.assertEqual(steerage_provider.is_system_suggested, True)
        self.assertIsNone(steerage_provider.search_request)
        self.assertEqual(steerage_provider.composite_score, None)
        self.assertEqual(steerage_provider.cost_score, None)
        self.assertEqual(steerage_provider.quality_score, None)
        self.assertEqual(steerage_provider.number_of_rating, None)
        self.assertEqual(steerage_provider.average_rating, None)
        self.assertEqual(steerage_provider.recommendation_reason, ["Partner"])
        self.assertEqual(steerage_provider.languages, [])
        self.assertEqual(steerage_provider.data_source, SteerageProviderDataSourceConfig.FIREFLY)

        self.assertEqual(steerage_provider.partner_name, partnership.partner_name)
        self.assertEqual(steerage_provider.network_partner_agreement_type, partnership.agreement_type)
        self.assertIsNotNone(steerage_provider.distance)
        self.assertIsNotNone(steerage_provider.source_identifier)
        self.assertEqual(steerage_provider.provider_type, curated_provider.provider_type)
        self.assertEqual(steerage_provider.address_line_1, curated_provider.address_line_1)
        self.assertEqual(steerage_provider.address_line_2, curated_provider.address_line_2)
        self.assertEqual(steerage_provider.city, curated_provider.city)
        self.assertEqual(steerage_provider.zip_code, curated_provider.zip_code)
        self.assertEqual(steerage_provider.state, curated_provider.state)
        self.assertEqual(steerage_provider.care_organization_name, curated_provider.care_org_name)
        self.assertEqual(steerage_provider.npi, str(curated_provider.npi))
        self.assertEqual(steerage_provider.first_name, curated_provider.first_name)
        self.assertEqual(steerage_provider.last_name, curated_provider.last_name)
        self.assertEqual(steerage_provider.partnership, partnership)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_partner_without_member_lat_long(self, add_system_suggested_async_mock):
        # Scenario
        #   Create a curated provider without lat/long matching the steerage services and steerage details
        # Result
        #   Partner should not be added as suggested provider on steerage

        person: Person = self.patient.person
        person.insurance_info.latitude = None
        person.insurance_info.longitude = None
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_partner_without_curated_provider_lat_long(self, add_system_suggested_async_mock):
        # Scenario
        #   Create a curated provider without lat/long matching the steerage services and steerage details
        # Result
        #   Partner should not be added as suggested provider on steerage

        person: Person = self.patient.person
        person.insurance_info.latitude = 34.0522
        person.insurance_info.longitude = -118.2437
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
            latitude=None,
            longitude=None,
            point=None,
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_partner_with_distance_none(self, add_system_suggested_async_mock):
        # Scenario
        #   Create a curated provider with lat/long matching the steerage services and steerage details
        #   but geopy not able to calculate the distance
        # Result
        #   Partner should not be added as suggested provider on steerage

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
            latitude=None,
            longitude=None,
            point=None,
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        add_system_suggested_async_mock.send.assert_called_once()

        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_partner_sorting(self, _add_system_suggested_async_mock):
        # Case - Sort Partner with 5 miles, 7 mile and 9 miles

        person: Person = self.patient.person
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Create suggested partner, partnership and service mappings
        curated_provider: CuratedProvider = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            zip_code=person.insurance_info.zipcode,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Created a firefly nearby steerage with same service
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        steerage.specialty_group = specialty_group
        steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        steerage.save()
        address_point: Point = Point(
            x=float(steerage.person.insurance_info.longitude),
            y=float(steerage.person.insurance_info.latitude),
            srid=4326,
        )
        curated_provider_querySet = CuratedProvider.objects.annotate(distance=Distance("point", address_point)).filter(
            id=curated_provider.pk
        )

        provider_details = convert_partners_into_provider_search_result(
            curated_provider_querySet, self.patient.person, ""
        )

        provider_details_5 = None
        provider_details_7 = None
        provider_details_9 = None

        for provider_detail in provider_details:
            provider_detail["distance"] = 5
            provider_details_5 = provider_detail
            provider_detail["distance"] = 7
            provider_details_7 = provider_detail
            provider_detail["distance"] = 9
            provider_details_9 = provider_detail

        # Call the sorting
        partners = sort_suggested_partners([provider_details_7, provider_details_9, provider_details_5])

        self.assertEqual([provider_details_5, provider_details_7, provider_details_9], partners)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class CuratedProviderProximityFilterTestCase(FireflyTestCase):
    logger_prefix = "CuratedProviderProximityFilterTestCase"

    def setUp(self):
        super().setUp()
        person: Person = self.patient.person
        # Example latitude and longitude for testing (New York)
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.save(update_fields=["latitude", "longitude"])

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        self.steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.IN_PERSON, referral=referral
        )
        SteerageService.objects.create(steerage=self.steerage, service=service_1)
        SteerageService.objects.create(steerage=self.steerage, service=service_2)

        # Create some test CuratedProvider instances with known latitude and longitude
        self.curated_provider_within_10 = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_within_10, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

        # Create some test CuratedProvider instances with known latitude and longitude
        curated_provider_outside_10 = CuratedProviderFactory(
            provider_type=CuratedProviderType.IN_PERSON,
            latitude=40.8584,
            longitude=-73.9293,
            point=Point(
                x=float(-73.9293),
                y=float(40.8584),
                srid=4326,
            ),
        )
        partnership: Partnership = PartnershipFactory(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider_outside_10, partnership=partnership)
        PartnershipServiceFactory(partnership=partnership, service=service_1)
        PartnershipServiceFactory(partnership=partnership, service=service_2)

    def test_filter_by_distance(self):
        # Fetch the partners
        curated_providers: Optional[QuerySet[AnnotatedMatchedSanitizedDescriptionCountCuratedProvider]] = (
            get_suggested_in_person_partners(steerage=self.steerage, logger_prefix=self.logger_prefix)
        )

        # Assert
        self.assertEqual(len(curated_providers), 1)
        self.assertEqual(curated_providers.first(), self.curated_provider_within_10)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SystemSuggestedPartnersForInHomeTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        person: Person = self.patient.person
        # Example latitude and longitude for testing (New York)
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.zipcode = 10007
        person.insurance_info.save(update_fields=["latitude", "longitude", "zipcode"])

        cfa1 = ClinicalFocusAreaFactory()
        cfa2 = ClinicalFocusAreaFactory()

        # Created a steerage
        referral: Referral = ReferralFactory(is_last_mile=True, person=person)
        self.steerage: Steerage = SteerageFactory(
            person=person, type_of_visit=SteerageTypeOfVisit.ANY, referral=referral
        )
        SteerageClinicalFocusArea.objects.create(steerage=self.steerage, clinical_focus_area=cfa1)
        SteerageClinicalFocusArea.objects.create(steerage=self.steerage, clinical_focus_area=cfa2)

        # Create some test CuratedProvider instances with known latitude and longitude
        self.curated_provider_within_10 = CuratedProviderFactory(
            npi=None,
            provider_type=CuratedProviderType.AT_HOME_TEST_KIT,
            address_line_1=None,
            address_line_2=None,
            state=None,
            city=None,
            first_name=None,
            last_name=None,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
            zip_code=person.insurance_info.zipcode,
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(
            curated_provider=self.curated_provider_within_10, partnership=self.partnership
        )
        PartnershipClinicalFocusAreaFactory(partnership=self.partnership, clinical_focus_area=cfa1)
        PartnershipClinicalFocusAreaFactory(partnership=self.partnership, clinical_focus_area=cfa2)

    def test_get_in_home_for_all_zipcodes(self):
        # Case 0 - Create one partnership valid for all zipcodes and call get_in_home_for_all_zipcodes

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_in_home_for_all_zipcodes_when_the_steerage_is_in_person(self):
        # Case 0 - Create one partnership valid for all zipcodes when referral is in-person
        #  and call get_in_home_for_all_zipcodes
        self.steerage.type_of_visit = SteerageTypeOfVisit.IN_PERSON
        self.steerage.save()

        provider_detail = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["provider_type"], CuratedProviderType.AT_HOME_TEST_KIT)

    def test_get_in_home_for_all_zipcodes_when_service_not_match_with_referral(self):
        # Case 0 - Create one partnership valid for all zipcodes when referral is in-person
        #  and call get_in_home_for_all_zipcodes

        # Delete all the existing clinical focus areas on the partnership
        PartnershipClinicalFocusArea.objects.all().delete()
        self.assertEqual(PartnershipClinicalFocusArea.objects.count(), 0)
        # Create a new service mapping which is not added on steerage
        PartnershipServiceFactory(partnership=self.partnership, service=ServiceFactory(description="test"))
        self.assertEqual(PartnershipService.objects.count(), 1)

        self.steerage.type_of_visit = SteerageTypeOfVisit.IN_PERSON
        self.steerage.save()

        provider_detail = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(provider_detail), 0)

    def test_get_in_home_for_all_zipcodes_when_partnership_service_level_is_different(self):
        # Case 0 - Update partnership for coverage only and member is care only
        self.partnership.can_accept_care_and_coverage_members = False
        self.partnership.can_accept_care_members = False
        self.partnership.can_accept_coverage_members = True
        self.partnership.save()

        provider_detail = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(provider_detail), 1)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_get_in_home_partners(self, add_system_suggested_async_mock):
        # Case 0 - Crate in_home partner
        self.partnership.is_valid_for_all_zipcodes = False
        self.partnership.save()
        self.curated_provider_within_10.zip_code = self.steerage.person.insurance_info.zipcode
        self.curated_provider_within_10.provider_type = CuratedProviderType.IN_HOME
        self.curated_provider_within_10.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # Assert
        self.assertEqual(SteerageProvider.objects.count(), 1)
        steerage_provider = SteerageProvider.objects.first()
        self.assertEqual(steerage_provider.status, SteerageProviderStatuses.UNDER_REVIEW)
        self.assertEqual(steerage_provider.steerage, self.steerage)
        self.assertEqual(steerage_provider.recommendation_status, RecommendationStatusChoices.RECOMMENDED)
        self.assertEqual(steerage_provider.is_system_suggested, True)
        self.assertIsNone(steerage_provider.search_request)
        self.assertEqual(steerage_provider.composite_score, None)
        self.assertEqual(steerage_provider.cost_score, None)
        self.assertEqual(steerage_provider.quality_score, None)
        self.assertEqual(steerage_provider.number_of_rating, None)
        self.assertEqual(steerage_provider.average_rating, None)
        self.assertEqual(steerage_provider.recommendation_reason, ["Partner"])
        self.assertEqual(steerage_provider.languages, [])
        self.assertEqual(steerage_provider.data_source, SteerageProviderDataSourceConfig.FIREFLY)

        self.assertEqual(steerage_provider.partner_name, self.partnership.partner_name)
        self.assertEqual(steerage_provider.network_partner_agreement_type, self.partnership.agreement_type)
        self.assertIsNone(steerage_provider.distance)
        self.assertIsNotNone(steerage_provider.source_identifier)
        self.assertEqual(steerage_provider.provider_type, CuratedProviderType.IN_HOME)
        self.assertEqual(steerage_provider.address_line_1, None)
        self.assertEqual(steerage_provider.address_line_2, None)
        self.assertEqual(steerage_provider.city, None)
        self.assertIsNone(steerage_provider.zip_code)
        self.assertEqual(steerage_provider.state, None)
        self.assertEqual(steerage_provider.care_organization_name, self.partnership.partner_name)
        self.assertEqual(steerage_provider.npi, None)
        self.assertEqual(steerage_provider.first_name, self.partnership.partner_name)
        self.assertEqual(steerage_provider.last_name, None)
        self.assertEqual(steerage_provider.partnership, self.partnership)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class AllSystemSuggestedPartnersTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # This setup is for testing entire system suggested partners flow
        person: Person = self.patient.person
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.zipcode = 10007
        self.state_ma, _ = State.objects.get_or_create(abbreviation="MA")
        self.state_ca, _ = State.objects.get_or_create(abbreviation="CA")
        self.state_ny, _ = State.objects.get_or_create(abbreviation="NY")
        person.insurance_info.state = self.state_ma.abbreviation
        person.insurance_info.plan_type = InsuranceMemberInfo.PLAN_TYPE_HMO
        person.insurance_info.save(update_fields=["latitude", "longitude", "zipcode", "state", "plan_type"])

        # Add contract to member
        person.employer = None
        person.save(update_fields=["employer"])
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        person.eligible()
        insurance_member_info: InsuranceMemberInfo = person.insurance_info
        insurance_member_info.insurance_plan.firefly_accepted = True
        insurance_member_info.insurance_plan.save()
        pcp_selection_contract = ContractFactory(
            config={
                "payer_group": "Anthem/BCBS",
                "payer_id": insurance_member_info.insurance_payer.id,
                "plan_description_specific": True,
                "accepts_out_of_state_payers": True,
                "attribution_type": ContractAttributionType.PCP_SELECTION,
                "insurance_state": "MA",
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
                "is_attribution_tracked_externally": True,
            },
            contracted_entity_content_type=ContentType.objects.get_for_model(InsurancePayer),
            contracted_entity=insurance_member_info.insurance_payer,
        )
        insurance_member_info.is_firefly_pcp = True
        insurance_member_info.firefly_pcp_at = datetime.now()
        insurance_member_info.save()

        person.attribution.contract = pcp_selection_contract
        person.attribution.save()

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage with visit type as ANY
        self.steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.ANY)
        SteerageService.objects.create(steerage=self.steerage, service=service_1)
        SteerageService.objects.create(steerage=self.steerage, service=service_2)

        # 1. Create first CuratedProvider with in_person partner
        self.curated_provider_in_person = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.IN_PERSON,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            partner_name="CVS",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            partnership_type=PartnershipType.URGENT_RETAIL_CARE,
        )
        CuratedProviderPartnershipFactory(
            curated_provider=self.curated_provider_in_person, partnership=self.partnership
        )
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # 2. Create second CuratedProvider with in_home partner with different partnership
        self.curated_provider_in_home = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.IN_HOME,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
            zip_code=person.insurance_info.zipcode,
        )  # Midtown Manhattan
        self.in_home_partnership: Partnership = PartnershipFactory.create(
            partner_name="Headway",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            partnership_type=PartnershipType.IN_HOME_SERVICES,
        )
        CuratedProviderPartnershipFactory(
            curated_provider=self.curated_provider_in_home, partnership=self.in_home_partnership
        )
        PartnershipServiceFactory(partnership=self.in_home_partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.in_home_partnership, service=service_2)

        # 3. Create third Partnership, which is valid for all zipcodes
        self.curated_provider_in_home_all_zip = CuratedProviderFactory.create(
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
            zip_code=person.insurance_info.zipcode,
            provider_type=CuratedProviderType.AT_HOME_TEST_KIT,
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            partner_name="QT Medical",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            partnership_type=PartnershipType.TEST_KIT,
        )
        CuratedProviderPartnershipFactory(
            curated_provider=self.curated_provider_in_home_all_zip, partnership=self.partnership
        )
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # 4. Virtual Partner
        self.curated_provider_virtual = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.VIRTUAL,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            partner_name="Spring Health",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.PREFERRED_VENDOR,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_virtual, partnership=self.partnership)
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Create mapping b/w partnership and insurance
        self.partnership_insurance_payer: PartnershipInsurancePayer = PartnershipInsurancePayer.objects.create(
            partnership=self.partnership,
            insurance_payer=person.insurance_info.insurance_payer,
            insurance_plan_type=person.insurance_info.plan_type.upper(),
            state=self.state_ma,
        )

        # 5. Client Point Solutions
        self.curated_provider_client = CuratedProviderFactory.create(
            provider_type=CuratedProviderType.VIRTUAL,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            partner_name="Luna PT",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.CLIENT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_client, partnership=self.partnership)
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Create mapping b/w partnership and contract
        self.partnership_contract: PartnershipContract = PartnershipContract.objects.create(
            partnership=self.partnership,
            contract=pcp_selection_contract,
            is_primary_subscriber_only=False,
        )

        # Create a location type that only supports Test Kits
        self.remote_testing_location_type = LocationTypeFactory.create(label=LocationTypes.REMOTE_TESTING)
        LocationTypePartnershipType.objects.create(
            location_type=self.remote_testing_location_type, partnership_type=PartnershipType.TEST_KIT
        )

        # Create a location type that supports Urgent/Retail Care as well as In Home Services
        self.urgent_care_location_type = LocationTypeFactory.create(label="Urgent Care")
        LocationTypePartnershipType.objects.create(
            location_type=self.urgent_care_location_type, partnership_type=PartnershipType.URGENT_RETAIL_CARE
        )
        LocationTypePartnershipType.objects.create(
            location_type=self.urgent_care_location_type, partnership_type=PartnershipType.IN_HOME_SERVICES
        )

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_any_visit_type(self, add_system_suggested_async_mock):
        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # The result should contain all type of suggested partner
        self.assertEqual(SteerageProvider.objects.count(), 5)

    def test_suggested_partners_sorting(self):
        partners = get_suggested_partners(self.steerage, "")

        # Sorting should be in the below order
        # 1. Client Contract
        # 2. Virtual
        # 3. At Home Test Kit
        # 4. In Home
        # 5. In Person
        self.assertEqual(partners[0]["network_partner_agreement_type"], AgreementTypeConfig.CLIENT_CONTRACT)
        self.assertEqual(partners[1]["provider_type"], CuratedProviderType.VIRTUAL)
        self.assertEqual(partners[2]["provider_type"], CuratedProviderType.AT_HOME_TEST_KIT)
        self.assertEqual(partners[3]["provider_type"], CuratedProviderType.IN_HOME)
        self.assertEqual(partners[4]["provider_type"], CuratedProviderType.IN_PERSON)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_specific_request(self, add_system_suggested_async_mock):
        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.SPECIFIC_REQUEST
        self.steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # The result should contain all three type of suggested partner
        # that is "in_person", "in_home", "in_home" valid for all zip codes
        self.assertEqual(SteerageProvider.objects.count(), 5)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_in_person_visit_type(self, add_system_suggested_async_mock):
        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.SPECIFIC_REQUEST
        self.steerage.type_of_visit = SteerageTypeOfVisit.IN_PERSON
        self.steerage.save()
        self.steerage.refresh_from_db()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # This should contain "in_person", "in_home", "at_home_test_kit"
        self.assertEqual(SteerageProvider.objects.count(), 3)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_excluded_states(self, add_system_suggested_async_mock):
        # Case 1: Add excluded states for all partnership - should not return all type of suggested providers

        for partnership in Partnership.objects.all():
            # Create multiple states for the partnerships
            PartnershipServicingStateExclusion.objects.create(partnership=partnership, state=self.state_ma)
            PartnershipServicingStateExclusion.objects.create(partnership=partnership, state=self.state_ca)
            PartnershipServicingStateExclusion.objects.create(partnership=partnership, state=self.state_ny)

        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.save()
        add_system_suggested_async_mock.send.assert_called_once()
        partners = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(partners), 0)

        # Case 2: Delete the excluded states for MA partnerships - should return all type of suggested providers
        PartnershipServicingStateExclusion.objects.filter(state=self.state_ma).all().delete()
        partners = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(partners), 4)

        # Case 3: One partner is excluded in MA and others not
        state_exclusion = PartnershipServicingStateExclusion.objects.create(
            partnership=self.in_home_partnership, state=self.state_ma
        )
        partners = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(partners), 3)

        # Deleting the excluded state mapping should return the deleted partners
        state_exclusion.delete()
        partners = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(partners), 4)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_in_active_partnership(self, add_system_suggested_async_mock):
        # Case 1: Mark all partnerships as in_active
        for partnership in Partnership.objects.all():
            partnership.is_active = False
            partnership.save()

        # Adding specialty group to the steerage should trigger addition of suggested providers
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.save()
        add_system_suggested_async_mock.send.assert_called_once()
        partners = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(partners), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_based_on_partnership_type(self, add_system_suggested_async_mock):
        # Adding location type to the steerage should trigger addition of suggested providers
        # Let't use the location type that has a partnership type mapping so we only retrieve partners of the same
        # partnership type.
        self.steerage.location_type = self.remote_testing_location_type
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.type_of_visit = SteerageTypeOfVisit.ANY
        self.steerage.save()
        self.steerage.refresh_from_db()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # This should contain test kit ONLY
        partnership_types = list(
            self.steerage.location_type.partnership_types.values_list("partnership_type", flat=True)
        )
        self.assertEqual(self.steerage.steerage_providers.count(), 1)
        for provider in self.steerage.steerage_providers.iterator():
            self.assertTrue(provider.partnership.partnership_type in partnership_types)

        # Reset steerage providers
        self.steerage.steerage_providers.clear()
        self.assertEqual(self.steerage.steerage_providers.count(), 0)

        # Let's do it again, but with the urgent care location type that supports urgent retail care
        # and in home services
        self.steerage.location_type = self.urgent_care_location_type
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.type_of_visit = SteerageTypeOfVisit.ANY
        self.steerage.save()
        self.steerage.refresh_from_db()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # This should contain urgent retail care AND in home services
        partnership_types = list(
            self.steerage.location_type.partnership_types.values_list("partnership_type", flat=True)
        )
        self.assertEqual(self.steerage.steerage_providers.count(), 2)
        for provider in self.steerage.steerage_providers.iterator():
            self.assertTrue(provider.partnership.partnership_type in partnership_types)

        # Reset steerage providers
        self.steerage.steerage_providers.clear()
        self.assertEqual(self.steerage.steerage_providers.count(), 0)

        # Let's do it again, but with no partnership types
        new_location_type = LocationTypeFactory.create()
        self.assertEqual(new_location_type.partnership_types.count(), 0)
        self.steerage.location_type = new_location_type
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.type_of_visit = SteerageTypeOfVisit.VIRTUAL
        self.steerage.save()
        self.steerage.refresh_from_db()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # There should be no providers since there are no partnership types
        self.assertEqual(self.steerage.steerage_providers.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_suggested_partners_for_coverage_only(self, add_system_suggested_async_mock):
        # Since the member is coverage only, we should not get ANY direct contract partnerships. In this case,
        # we should ONLY get the virtual preferred vendor and one client contract
        # Adding specialty group to the steerage should trigger addition of suggested providers
        remove_person_from_program(self.steerage.person, program_uid=ProgramCodes.PRIMARY_CARE)

        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label="Dermatology")
        self.steerage.specialty_group = specialty_group
        self.steerage.request_type = SteerageRequestTypeConfig.BROAD_REQUEST
        self.steerage.save()

        add_system_suggested_async_mock.send.assert_called_once()
        add_system_suggested_providers_async(self.steerage.id)

        # The result should contain only the client contract and preferred vendor agreement types
        self.assertEqual(SteerageProvider.objects.count(), 2)
        providers = SteerageProvider.objects.all()
        for provider in providers.iterator():
            self.assertNotEqual(provider.partnership.agreement_type, AgreementTypeConfig.DIRECT_CONTRACT)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SuggestedPartnersForVirtualTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # This setup is for testing virtual partners

        person: Person = self.patient.person
        # Example latitude and longitude for testing (New York)
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.zipcode = 10007
        state_ma, _ = State.objects.get_or_create(abbreviation="MA")
        person.insurance_info.state = state_ma.abbreviation
        person.insurance_info.plan_type = InsuranceMemberInfo.PLAN_TYPE_HMO
        person.insurance_info.save(update_fields=["latitude", "longitude", "zipcode", "state", "plan_type"])
        self.insurance_info = person.insurance_info

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage with visit type as ANY
        self.steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.ANY)
        SteerageService.objects.create(steerage=self.steerage, service=service_1)
        SteerageService.objects.create(steerage=self.steerage, service=service_2)

        self.curated_provider_virtual = CuratedProviderFactory(
            provider_type=CuratedProviderType.VIRTUAL,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.PREFERRED_VENDOR,
        )
        CuratedProviderPartnershipFactory(curated_provider=self.curated_provider_virtual, partnership=self.partnership)
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Create mapping b/w partnership and insurance
        self.partnership_insurance_payer: PartnershipInsurancePayer = PartnershipInsurancePayer.objects.create(
            partnership=self.partnership,
            insurance_payer=person.insurance_info.insurance_payer,
            insurance_plan_type=person.insurance_info.plan_type.upper(),
            state=state_ma,
        )

    def test_get_virtual_suggested_partner_with_services(self):
        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_virtual_suggested_partner_with_clinical_focus_areas(self):
        cfa1 = ClinicalFocusAreaFactory()
        cfa2 = ClinicalFocusAreaFactory()
        SteerageClinicalFocusArea.objects.create(steerage=self.steerage, clinical_focus_area=cfa1)
        SteerageClinicalFocusArea.objects.create(steerage=self.steerage, clinical_focus_area=cfa2)
        PartnershipClinicalFocusAreaFactory(partnership=self.partnership, clinical_focus_area=cfa1)
        PartnershipClinicalFocusAreaFactory(partnership=self.partnership, clinical_focus_area=cfa2)

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_virtual_suggested_partner_for_any_plan_type(self):
        self.partnership_insurance_payer.insurance_plan_type = PartnershipInsurancePayer.PLAN_TYPE_ANY
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_virtual_suggested_partner_for_any_plan(self):
        self.partnership_insurance_payer.insurance_plan_type = PartnershipInsurancePayer.PLAN_TYPE_ANY
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_virtual_suggested_partner_for_different_state(self):
        state_ny, _ = State.objects.get_or_create(abbreviation="NY")

        self.partnership_insurance_payer.state = state_ny
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 0)

    def test_get_virtual_suggested_partner_for_different_payer(self):
        self.partnership_insurance_payer.insurance_payer = InsurancePayerFactory()
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 0)

    def test_preferred_vendor_partner_with_group_id(self):
        # Case 1 match the group id
        self.assertEqual(self.partnership.agreement_type, AgreementTypeConfig.PREFERRED_VENDOR)
        # Add group id for member
        self.insurance_info.group_number = "FF100"
        self.insurance_info.save()

        # Add group id mapping for the insurance
        self.partnership_insurance_payer.group_number = "FF100"
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)

    def test_preferred_vendor_partner_without_group_id(self):
        # Case 1 match the group id
        self.assertEqual(self.partnership.agreement_type, AgreementTypeConfig.PREFERRED_VENDOR)
        # Add group id for member
        self.insurance_info.group_number = "FF100"
        self.insurance_info.save()

        # Add group id mapping for the insurance
        self.partnership_insurance_payer.group_number = None
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)

    def test_preferred_vendor_partner_with_different_group_id(self):
        # Case 1 match the group id
        self.assertEqual(self.partnership.agreement_type, AgreementTypeConfig.PREFERRED_VENDOR)
        # Add group id for member
        self.insurance_info.group_number = "FF100"
        self.insurance_info.save()

        # Add group id mapping for the insurance
        self.partnership_insurance_payer.group_number = "FF101"
        self.partnership_insurance_payer.save()

        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 0)


@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SuggestedPartnersForClientPointSolutionTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # This setup is for testing virtual partners

        person: Person = self.patient.person
        # Example latitude and longitude for testing (New York)
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.zipcode = 10007
        state_ma, _ = State.objects.get_or_create(abbreviation="MA")
        person.insurance_info.state = state_ma.abbreviation
        person.insurance_info.plan_type = InsuranceMemberInfo.PLAN_TYPE_HMO
        person.insurance_info.is_primary_subscriber = False
        person.insurance_info.save(
            update_fields=["latitude", "longitude", "zipcode", "state", "plan_type", "is_primary_subscriber"]
        )

        service_1 = ServiceFactory()
        service_2 = ServiceFactory()

        # Created a steerage with visit type as ANY
        self.steerage: Steerage = SteerageFactory(person=person, type_of_visit=SteerageTypeOfVisit.ANY)
        SteerageService.objects.create(steerage=self.steerage, service=service_1)
        SteerageService.objects.create(steerage=self.steerage, service=service_2)

        self.curated_provider_in_person = CuratedProviderFactory(
            provider_type=CuratedProviderType.VIRTUAL,
            latitude=40.7401,
            longitude=-73.9903,
            point=Point(
                x=float(-73.9903),
                y=float(40.7401),
                srid=4326,
            ),
        )  # Midtown Manhattan
        self.partnership: Partnership = PartnershipFactory.create(
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=False,
            agreement_type=AgreementTypeConfig.CLIENT_CONTRACT,
        )
        CuratedProviderPartnershipFactory(
            curated_provider=self.curated_provider_in_person, partnership=self.partnership
        )
        PartnershipServiceFactory(partnership=self.partnership, service=service_1)
        PartnershipServiceFactory(partnership=self.partnership, service=service_2)

        # Add contract to member
        person.employer = None
        person.save(update_fields=["employer"])
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        person.eligible()
        insurance_member_info: InsuranceMemberInfo = person.insurance_info
        insurance_member_info.insurance_plan.firefly_accepted = True
        insurance_member_info.insurance_plan.save()
        pcp_selection_contract = ContractFactory(
            config={
                "payer_group": "Anthem/BCBS",
                "payer_id": insurance_member_info.insurance_payer.id,
                "plan_description_specific": True,
                "accepts_out_of_state_payers": True,
                "attribution_type": ContractAttributionType.PCP_SELECTION,
                "insurance_state": "MA",
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
                "is_attribution_tracked_externally": True,
            },
            contracted_entity_content_type=ContentType.objects.get_for_model(InsurancePayer),
            contracted_entity=insurance_member_info.insurance_payer,
        )
        insurance_member_info.is_firefly_pcp = True
        insurance_member_info.firefly_pcp_at = datetime.now()
        insurance_member_info.save()

        person.attribution.contract = pcp_selection_contract
        person.attribution.save()

        # Create mapping b/w partnership and contract
        self.partnership_contract: PartnershipContract = PartnershipContract.objects.create(
            partnership=self.partnership,
            contract=pcp_selection_contract,
            is_primary_subscriber_only=False,
        )

    def test_get_client_suggested_partner(self):
        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

    def test_get_remote_suggested_partner_for_dependent(self):
        # case 1 - When Partnership contract is applicable for dependant
        self.patient.person.insurance_info.is_primary_subscriber = False
        self.patient.person.insurance_info.save(update_fields=["is_primary_subscriber"])
        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 1)
        self.assertEqual(provider_detail[0]["name"], self.partnership.partner_name)

        # case 2 - When Partnership is not applicable for dependant
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(self.patient.person.insurance_info.is_primary_subscriber, False)

        self.partnership_contract.is_primary_subscriber_only = True
        self.partnership_contract.save()

        provider_detail = get_suggested_remote_partners(self.steerage)
        self.assertEqual(len(provider_detail), 0)

    def test_get_client_suggested_partner_different_contract(self):
        self.partnership_contract.contract = ContractFactory()
        self.partnership_contract.save()
        provider_detail = get_suggested_remote_partners(self.steerage)

        self.assertEqual(len(provider_detail), 0)

    def test_get_in_person_suggested_partner_for_dependent(self):
        # case 1 - When Partnership contract is applicable for dependant
        self.patient.person.insurance_info.is_primary_subscriber = False
        self.patient.person.insurance_info.save()

        self.curated_provider_in_person.provider_type = CuratedProviderType.IN_PERSON
        self.curated_provider_in_person.save()

        curated_provider = get_suggested_in_person_partners(self.steerage, "")

        self.assertEqual(len(curated_provider), 1)


@mock.patch(
    "firefly.modules.network.utils.utils.get_redis_cache",
)
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch("firefly.modules.referral.utils.talon_search_utils.get_facilities_based_on_procedure")
@override_switch(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2, True)
@override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
class SystemSuggestedTalonProcedureTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # Setup insurance details for person
        clear_cache_data()
        mock_redis_obj = get_mock_redis()
        # binding a side_effect of a MagicMock instance with redis methods
        mock_redis_method = MagicMock()
        mock_redis_method.get = Mock(side_effect=mock_redis_obj.get)
        mock_redis_method.set = Mock(side_effect=mock_redis_obj.set)
        mock_redis_method.flushall = Mock(side_effect=mock_redis_obj.flushall)
        self.mock_redis_method = mock_redis_method
        self.mock_redis_method.flushall()

        # Intentionally mock this as BCBS Blue based on real talon data.
        # This allows us to sanity check talon and our mocks against the real thing
        # by turning off the test flag temporarily
        self.payer = InsurancePayerFactory.create(name="BCBS MSA", payer_codes=["64222"])
        self.plan = InsurancePlanFactory.create(
            name="0750 - HMO BLUE NEW ENGLAND DEDUCTIBLE", insurance_payer=self.payer
        )
        self.network = NetworkFactory.create(name="Blue Cross Blue Shield of Massachusetts - Blue New England - HMO")
        self.plan.networks.add(self.network)
        self.plan.save()

        # Create network and there alias mappings
        self.network_alias = AliasMapping.set_mapping_by_object(
            obj=self.network, alias_id="123456789", alias_name=AliasName.TALON
        )

        # Create some procedure codes and there alias mappings
        self.code_1 = CPTCodeFactory()
        self.cpt_alias = AliasMapping.set_mapping_by_object(obj=self.code_1, alias_id="mri", alias_name=AliasName.TALON)
        self.code_2 = CPTCodeFactory()
        self.cpt_alias_2 = AliasMapping.set_mapping_by_object(
            obj=self.code_2, alias_id="abc", alias_name=AliasName.TALON
        )

        # UC and PT hardcoded cpt codes
        self.code_urgent_care = CPTCodeFactory(code="UC")
        AliasMapping.set_mapping_by_object(obj=self.code_urgent_care, alias_id="UC", alias_name=AliasName.TALON)
        self.code_pt = CPTCodeFactory(code="PT")
        AliasMapping.set_mapping_by_object(obj=self.code_pt, alias_id="PT", alias_name=AliasName.TALON)

        # Create a service name same as procedure name
        self.service: Service = ServiceFactory.create(description=self.code_1.display)
        self.service_2: Service = ServiceFactory.create(description=self.code_2.display)

        # Create Service to CPTCode mapping
        ServiceCPTCode.objects.create(service=self.service, cpt_code=self.code_1)
        ServiceCPTCode.objects.create(service=self.service_2, cpt_code=self.code_2)

        self.person = PersonUserFactory.create()
        self.person.insurance_info.insurance_payer = self.payer
        self.person.insurance_info.plan_description = self.plan.name
        self.person.insurance_info.insurance_plan = self.plan
        fake = Faker()
        self.zipcode = "%05d" % randint(1, 99999)
        self.street_address = fake.street_address()
        self.street_address_2 = fake.street_address()
        self.city = fake.city()
        self.address = (
            f"{self.street_address} "
            f"{self.street_address_2} "
            f"{self.city} "
            f"{self.person.insurance_info.state} "
            f"{self.zipcode} "
        )
        self.person.insurance_info.street_address = self.street_address
        self.person.insurance_info.street_address_2 = self.street_address_2
        self.person.insurance_info.city = self.city
        self.person.insurance_info.zipcode = self.zipcode
        self.person.insurance_info.save()
        self.person.insurance_info.refresh_from_db()

        # Create Imagine Center Location Type
        self.location_type: LocationType = LocationTypeFactory.create(label="Imaging Center")
        self.location_type_urgent_care: LocationType = LocationTypeFactory.create(label="Urgent Care")
        self.location_type_pt: LocationType = LocationTypeFactory.create(label="Physical Therapy")

        # Create three suggested providers and one not suggested provider as a mock response to talon call
        facilities = []
        for i in range(3):
            talon_prices_response = TalonProcedurePricesFactory.create(bundleMedianPrice=10.0, totalQualityScore=0.9)
            facilities.append(talon_prices_response)

        # Mock the ribbon response with the above providers
        self.talon_response: TalonProcedurePricesResponse = {"procedurePrices": facilities}
        self.search_availability = SearchAvailabilityFactory.create()

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers(
        self,
        add_system_suggested_async_mock,
        talon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        talon_call.return_value = (deepcopy(self.talon_response), 200)
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type
        steerage.save()
        SteerageService.objects.create(steerage=steerage, service=self.service)

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        for steerage_provider in SteerageProvider.objects.all():
            self.assertEqual(steerage_provider.status, SteerageProviderStatuses.UNDER_REVIEW)
            self.assertEqual(steerage_provider.steerage, steerage)
            self.assertEqual(steerage_provider.recommendation_status, RecommendationStatusChoices.RECOMMENDED)
            self.assertEqual(steerage_provider.is_system_suggested, True)
            self.assertIsNotNone(steerage_provider.search_request)
            self.assertIsNotNone(steerage_provider.distance)
            self.assertEqual(steerage_provider.recommendation_reason, ["Great cost", "expertise"])
            self.assertEqual(steerage_provider.composite_score, 5.0)
            self.assertEqual(steerage_provider.estimated_cost, 10.0)
            self.assertEqual(steerage_provider.data_source, SteerageProviderDataSourceConfig.TALON)
            self.assertEqual(steerage_provider.expertise_score, 5)
            self.assertEqual(steerage_provider.cost_score, 5)
            self.assertEqual(steerage_provider.cpt_code, self.code_1.code)
            self.assertIsNotNone(steerage_provider.cost_breakdown)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_when_some_are_recommended(
        self,
        add_system_suggested_async_mock,
        talon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data with 1 recommended and 1 not recommended
        talon_prices_recommended = TalonProcedurePricesFactory.create(bundleMedianPrice=10.0, totalQualityScore=0.9)
        talon_prices_not_recommended = TalonProcedurePricesFactory.create(
            bundleMedianPrice=500.0, totalQualityScore=0.1
        )
        facilities = [talon_prices_recommended, talon_prices_not_recommended]

        # Mock the ribbon response with the above providers
        talon_response: TalonProcedurePricesResponse = {"procedurePrices": facilities}
        talon_call.return_value = (deepcopy(talon_response), 200)
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type
        steerage.save()
        SteerageService.objects.create(steerage=steerage, service=self.service)

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add one steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 1)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_when_steerage_have_more_then_one_service(
        self,
        add_system_suggested_async_mock,
        talon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        talon_call.return_value = (deepcopy(self.talon_response), 200)
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type
        steerage.save()
        SteerageService.objects.create(steerage=steerage, service=self.service)
        SteerageService.objects.create(steerage=steerage, service=self.service_2)

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should not add steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 0)

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_urgent_care(
        self,
        add_system_suggested_async_mock,
        talon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        talon_call.return_value = (deepcopy(self.talon_response), 200)
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type_urgent_care
        steerage.save()

        # Add some other service on the steerage
        service_1 = ServiceFactory.create(description="Eye Exam")
        service_2 = ServiceFactory.create(description="Ear Exam")
        SteerageService.objects.create(steerage=steerage, service=service_1)
        SteerageService.objects.create(steerage=steerage, service=service_2)
        ServiceCPTCode.objects.create(service=service_1, cpt_code=self.code_urgent_care)
        ServiceCPTCode.objects.create(service=service_2, cpt_code=self.code_urgent_care)

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        # Validate the CPT CODE used is the one which we hardcoded
        steerage_provider: SteerageProvider = SteerageProvider.objects.first()
        self.assertEqual(
            steerage_provider.search_request.cpt_code.code,
            self.code_urgent_care.code,
        )

    @mock.patch("firefly.modules.referral.tasks.add_system_suggested_providers_async")
    def test_add_system_suggested_providers_for_physical_therapy(
        self,
        add_system_suggested_async_mock,
        talon_call,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        # Mock the ribbon response data
        talon_call.return_value = (deepcopy(self.talon_response), 200)
        self.assertEqual(SteerageProvider.objects.count(), 0)

        # create a steerage which is segmented
        referral = ReferralFactory.create(person=self.person)
        steerage = SteerageFactory.create(person=self.person, referral=referral)
        steerage.location_type = self.location_type_pt
        steerage.save()

        # Add some other service on the steerage
        service_1 = ServiceFactory.create(description="Physical therapy")
        SteerageService.objects.create(steerage=steerage, service=service_1)
        ServiceCPTCode.objects.create(service=service_1, cpt_code=self.code_pt)

        add_system_suggested_async_mock.send.assert_called_once()

        # Calling the add_system_suggested_providers_async should add three steerage provider to steerage
        add_system_suggested_providers_async(steerage.id)
        self.assertEqual(SteerageProvider.objects.count(), 3)

        # Validate the CPT CODE used is the one which we hardcoded
        steerage_provider: SteerageProvider = SteerageProvider.objects.first()
        self.assertEqual(
            steerage_provider.search_request.cpt_code.code,
            self.code_pt.code,
        )
