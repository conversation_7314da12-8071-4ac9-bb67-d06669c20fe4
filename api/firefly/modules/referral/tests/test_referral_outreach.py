from datetime import date, <PERSON><PERSON><PERSON>
from unittest import mock

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.management import call_command

from firefly.core.alias.models import <PERSON>asMapping, AliasName
from firefly.core.feature.testutils import override_switch
from firefly.core.services.braze.tasks import reminders_sync
from firefly.core.services.braze.utils import mock_braze_markdown_renderer
from firefly.core.tests.test_case import FireflyTestCase, MockResponse
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.core.user.models.models import Person, User
from firefly.modules.eligibility.utils import get_member_key_for_person, get_persons_from_member_key
from firefly.modules.events.models import EventLog
from firefly.modules.facts.factories import (
    LocationTypeFactory,
    ServiceCategoryFactory,
    SpecialtyGroupFactory,
)
from firefly.modules.facts.models import SpecialtyGroup
from firefly.modules.insurance.models import Employer
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.referral.api import get_person_and_dependents
from firefly.modules.referral.campaigns.steerage_choose_provider import (
    SteerageChooseProviderCampaign,
    SteerageSelfServiceCampaign,
)
from firefly.modules.referral.constants import (
    ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE,
    WAFFLE_SWITCH_SELF_SERVICE_NAV,
)
from firefly.modules.referral.factories import (
    SteerageFactory,
    SteerageProviderFactory,
    WaiverFactory,
)
from firefly.modules.referral.models import (
    Steerage,
    SteerageProviderStatuses,
    SteerageProviderWaiverLevel,
)
from firefly.modules.referral.tasks import get_person_for_outreach, send_referral_outreach_sync
from firefly.modules.referral.tests.test_steerage_utils import (
    set_up_steerage_affected_case_categories,
)


class ReferralOutreachTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        set_up_steerage_affected_case_categories(cls)
        cls.specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create()
        cls.location_type = LocationTypeFactory.create()
        cls.today = date.today()

        # Create required objects that are normally cached properties
        from firefly.modules.insurance.models import InsurancePayer
        from firefly.modules.programs.benefit.constants import FIREFLY_PAYER
        from firefly.modules.tenants.models import ConsentForm

        cls.firefly_payer, _ = InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        cls.consent_form, _ = ConsentForm.objects.get_or_create(
            form_name="Privacy Policy",
            html_content="<h1>Test Form</h1>",
            version="v1",
            key="consentKey",
        )

        # Create person and dependents first
        cls.firefly_employer, _ = Employer.objects.get_or_create(name="Firefly")
        AliasMapping.set_mapping_by_object(cls.firefly_employer, AliasName.FLUME, "FF100")

        # Create a provider user for the command since cls.provider is not available in setUpTestData
        from firefly.core.user.factories import ProviderUserFactory

        provider_user = ProviderUserFactory.create()

        call_command(
            "parse_fake_flume_data_from_data_pipeline",
            dry_run_off=True,
            user=provider_user,
            dependents=True,
            email="<EMAIL>",
            employer_id=cls.firefly_employer.id,
            first_name="Zaphod",
            last_name="Beeblebrox",
            phone="**********",
        )
        person: Person = Person.objects.get(email="<EMAIL>")
        patient_create_payload = {
            "created_from": "web",
            "dob": person.dob,
            "sex": person.sex,
            "gender": person.gender,
            "pronouns": person.pronouns,
            "first_name": person.first_name,
            "last_name": person.last_name,
            "phone_number": person.phone_number,
            "insurance_member_info": {
                "state": person.insurance_info.state,
                "source_type": "employer",
                "insurance_payer_id": cls.firefly_payer.pk,
                "member_id": person.insurance_info.member_id,
            },
            "address": {"zip": "02139"},
            "patient_referral_program": "",
            "consent_forms": [cls.consent_form.id],
            "employee_identifier": person.employee_identifier,
            "programs": [ProgramCodes.PRIMARY_CARE, ProgramCodes.BENEFIT],
        }
        user: User = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        response = client.post("/bff/app/signup/member-id-confirmation/", patient_create_payload, format="json")
        assert response.status_code == 200
        user.refresh_from_db()
        person = user.person
        cls.steerage_person = person
        # Get dependent and ensure they are over 18
        cls.steerage_dependent_person: Person = (
            get_persons_from_member_key(get_member_key_for_person(person)).exclude(pk=person.pk).first()
        )
        cls.steerage_dependent_person.dob = date.today() - timedelta(days=365 * 26)
        cls.steerage_dependent_person.save()

    @mock.patch("firefly.modules.referral.tasks.send_referral_outreach_async.send")
    def test_steerage_send_outreach(self, send_referral_outreach_async):
        steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
        )
        steerage_provider = SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
        )

        # Assert there's no recommendation_sent_at to start
        self.assertIsNone(steerage.recommendation_sent_at)
        self.assertEqual(steerage_provider.steerage, steerage)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={True}", format="json"
        )

        # Assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        self.assertEqual(locked_steerage.json()["locked_by"], self.provider.id)
        self.assertNotEqual(locked_steerage.json()["locked_at"], None)

        # Since this is the first lock, assert "recommendation_sent_at" is set
        steerage.refresh_from_db()
        initial_locked_at = steerage.recommendation_sent_at
        self.assertIsNotNone(steerage.recommendation_sent_at)
        self.assertEqual(steerage.recommendation_sent_at, steerage.locked_at)

        # Assert send_referral_outreach_async is being called
        send_referral_outreach_async.assert_called_once_with(steerage.id)

        # Unlock the steerage
        unlocked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={False}", format="json"
        )
        self.assertEqual(unlocked_steerage.json()["is_locked"], False)
        self.assertEqual(unlocked_steerage.json()["locked_at"], None)
        self.assertEqual(unlocked_steerage.json()["locked_by"], None)

        # Assert the recommendation_sent_at column is unaffected
        steerage.refresh_from_db()
        self.assertEqual(steerage.recommendation_sent_at, initial_locked_at)

        # Re-lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={True}", format="json"
        )
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        self.assertEqual(locked_steerage.json()["locked_by"], self.provider.id)
        self.assertNotEqual(locked_steerage.json()["locked_at"], None)

        # Assert the recommendation_sent_at column is unaffected
        steerage.refresh_from_db()
        self.assertEqual(steerage.recommendation_sent_at, initial_locked_at)

        # Assert send_referral_outreach_async hasn't been called again
        send_referral_outreach_async.assert_called_once()

    @mock.patch(
        "firefly.core.services.braze.templates.reminders.send",
        new=reminders_sync,
    )
    @mock.patch("requests.post", return_value=MockResponse({}, 201))
    def test_prevent_duplicate_outreach(self, mock_post):
        steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            is_locked=True,
            locked_by=self.provider,
            locked_at=self.today,
        )
        steerage_provider = SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
        )

        event_logs = EventLog.objects.filter(
            target_object_id=steerage.id,
            target_content_type=ContentType.objects.get_for_model(Steerage),
        )

        # Ensure there is only one event log (indicating a successful Braze campaign send)
        self.assertEqual(event_logs.count(), 0)
        self.assertEqual(steerage_provider.steerage, steerage)
        send_referral_outreach_sync(steerage.id)
        self.assertEqual(event_logs.count(), 1)
        send_referral_outreach_sync(steerage.id)
        self.assertEqual(event_logs.count(), 1)

        # Ensure the event log content type is correct
        for event in event_logs.iterator():
            self.assertEqual(event.target_content_type, ContentType.objects.get_for_model(Steerage))

    @mock.patch(
        "firefly.modules.referral.tasks.send_referral_outreach_async.send",
        new=send_referral_outreach_sync,
    )
    @mock.patch("firefly.core.services.braze.templates.reminders.send")
    def test_send_outreach_with_primary_subscriber_and_dependents(self, reminders):
        # Case 1: Primary subscriber has a referral. Outreach should send to primary subscriber only
        steerage_one: Steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            is_locked=True,
            locked_by=self.provider,
            locked_at=self.today,
        )
        steerage_provider_one = SteerageProviderFactory.create(
            steerage=steerage_one,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
        )
        send_referral_outreach_sync(steerage_one.id)

        # Ensure the outreach recipient is included in the list of persons that are
        # expected to have access to the steerage
        persons_with_steerage_access = get_person_and_dependents(steerage_one.person.id)
        self.assertIn(steerage_one.person.id, persons_with_steerage_access)
        self.assertEqual(steerage_provider_one.steerage, steerage_one)

        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SCHEDULING"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_person.id))

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage_one.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SCHEDULING"]},
            },
        )

        # Reset mock for next test case
        reminders.reset_mock()

        # Case 2: Dependent over 18 has a referral. Outreach should send to dependent only
        self.assertTrue(self.steerage_dependent_person.dob < date.today() - timedelta(days=365 * 18))
        steerage_two = SteerageFactory(
            person=self.steerage_dependent_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            is_locked=True,
            locked_by=self.provider,
            locked_at=self.today,
        )
        steerage_provider_two = SteerageProviderFactory.create(
            steerage=steerage_two,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
        )
        send_referral_outreach_sync(steerage_two.id)

        # Ensure the outreach recipient is included in the list of persons that are
        # expected to have access to the steerage
        persons_with_steerage_access = get_person_and_dependents(steerage_two.person.id)
        self.assertIn(steerage_two.person.id, persons_with_steerage_access)
        self.assertEqual(steerage_provider_two.steerage, steerage_two)

        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SCHEDULING"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_dependent_person.id))

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage_two.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SCHEDULING"]},
            },
        )

        # Reset mock for next test case
        reminders.reset_mock()

        # Case 3: Dependent under 18 has a referral. Outreach should send to primary subscriber only
        # First, update the dependent DOB to be under 18
        self.steerage_dependent_person.dob = date.today() - timedelta(days=365 * 15)
        self.steerage_dependent_person.save()
        self.assertTrue(self.steerage_dependent_person.dob > date.today() - timedelta(days=365 * 18))
        steerage_three = SteerageFactory(
            person=self.steerage_dependent_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            is_locked=True,
            locked_by=self.provider,
            locked_at=self.today,
        )
        steerage_provider_three = SteerageProviderFactory.create(
            steerage=steerage_three,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
        )
        send_referral_outreach_sync(steerage_three.id)

        # Ensure the outreach recipient is included in the list of persons that are
        # expected to have access to the steerage. In the case of a dependent under 18,
        # the primary subscriber should be included in the list of persons with access
        persons_with_steerage_access = get_person_and_dependents(self.steerage_person.id)
        self.assertIn(steerage_three.person.id, persons_with_steerage_access)
        self.assertIn(self.steerage_person.id, persons_with_steerage_access)
        self.assertEqual(steerage_provider_three.steerage, steerage_three)

        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SCHEDULING"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_person.id))

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage_three.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SCHEDULING"]},
            },
        )

    @mock.patch(
        "firefly.modules.referral.tasks.send_referral_outreach_async.send",
        new=send_referral_outreach_sync,
    )
    @mock.patch("firefly.core.services.braze.templates.reminders.send")
    def test_steerage_outreach_campaign_scheduling(self, reminders):
        # Case 1: Steerage with referral
        steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
        )
        SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="Foo",
            last_name="Bar",
        )

        # Lock the steerage
        self.provider_client.patch(f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={True}", format="json")

        call_args = reminders.call_args[0]
        recipients = call_args[1]
        trigger_props = recipients[0]["trigger_properties"]

        # Verify static content
        self.assertEqual(
            trigger_props["push_message"],
            "Book a visit with the provider today. See scheduling instructions in the app.",
        )
        self.assertEqual(trigger_props["email_preheader"], "Next up: Book the visit")
        self.assertTrue(f"referral-detail?id={steerage.id}" in trigger_props["push_deeplink"])

        # Verify dynamic content
        self.assertEqual(trigger_props["email_subject"], "Your referral is ready")
        self.assertEqual(trigger_props["push_title"], "Your referral is ready")

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SCHEDULING"]},
            },
        )

        # Reset mock for next test case
        reminders.reset_mock()

        # Case 2: Steerage without referral
        steerage_no_referral = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            referral=None,
        )

        # Create this variable but use it in assertions to avoid linter warnings
        SteerageProviderFactory.create(
            steerage=steerage_no_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="John",
            last_name="Doe",
        )

        # Lock the steerage
        self.provider_client.patch(
            f"/referral/steerage/{steerage_no_referral.id}/toggle_lock/?is_locked={True}", format="json"
        )

        # Check that reminders.send was called with the right campaign ID
        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]
        recipients = call_args[1]
        trigger_props = recipients[0]["trigger_properties"]

        # Email and push notification properties for case without referral
        self.assertEqual(trigger_props["email_subject"], "Your provider request is ready")
        self.assertEqual(trigger_props["push_title"], "Your provider request is ready")

    @mock_braze_markdown_renderer
    def test_steerage_outreach_campaign_scheduling_markdown(self):
        """Test that the SteerageReadyToScheduleCampaign produces the expected markdown content
        for different combinations of referral and waiver."""
        # Test Case 1: Steerage with referral
        specialty_group = SpecialtyGroupFactory.create(label="Dermatology")

        steerage_with_referral = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
        )

        SteerageProviderFactory.create(
            steerage=steerage_with_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="Foo",
            last_name="Bar",
        )

        person_for_outreach = get_person_for_outreach(steerage_with_referral.person.id)

        # Create the campaign and set the context
        from firefly.modules.referral.campaigns.steerage_ready_to_schedule import (
            SteerageReadyToScheduleCampaign,
        )

        campaign = SteerageReadyToScheduleCampaign()
        campaign.create_context(steerage=steerage_with_referral, person=person_for_outreach)

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with referral
        expected_content = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit.

Here's what to do next.

1. **Book your visit with Foo Bar.**
Get their info in the app.

2. **Add the visit date in the Firefly app.**
This helps us follow up on care.

3. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content)

        # Test Case 2: Steerage without referral (provider request)
        steerage_no_referral = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            referral=None,
        )

        SteerageProviderFactory.create(
            steerage=steerage_no_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="John",
            last_name="Doe",
        )

        person_for_outreach = get_person_for_outreach(steerage_no_referral.person.id)

        campaign = SteerageReadyToScheduleCampaign()
        campaign.create_context(steerage=steerage_no_referral, person=person_for_outreach)

        # Get the email content for case without referral
        email_content = campaign.get_email_content()

        # Expected content should be the same except for the title differences
        expected_content_no_referral = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit.

Here's what to do next.

1. **Book your visit with John Doe.**
Get their info in the app.

2. **Add the visit date in the Firefly app.**
This helps us avoid billing issues.

3. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_no_referral)

        # Test Case 3: Provider with facility name
        steerage_facility = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
        )

        SteerageProviderFactory.create(
            steerage=steerage_facility,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.FACILITY,
            care_organization_name="FooMed Bar Center",
        )

        person_for_outreach = get_person_for_outreach(steerage_facility.person.id)

        campaign = SteerageReadyToScheduleCampaign()
        campaign.create_context(steerage=steerage_facility, person=person_for_outreach)

        # Get the email content for case with facility name
        email_content = campaign.get_email_content()

        # Verify the facility name is used instead of provider name
        expected_content_with_facility_name = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit.

Here's what to do next.

1. **Book your visit with FooMed Bar Center.**
Get their info in the app.

2. **Add the visit date in the Firefly app.**
This helps us follow up on care.

3. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""
        self.assertEqual(email_content, expected_content_with_facility_name)

    @mock_braze_markdown_renderer
    def test_choose_provider_campaign_markdown(self):
        """Test that the SteerageChooseProviderCampaign produces the expected markdown content
        for different combinations of referral and waiver."""
        # Test Case 1: Steerage with both referral and waiver
        specialty_group = SpecialtyGroupFactory.create(label="Dermatology")
        service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])
        waiver = WaiverFactory()
        waiver.service_categories.add(service_category)
        waiver.save()

        steerage_with_both = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            waiver=waiver,
        )
        person_for_outreach = get_person_for_outreach(steerage_with_both.person.id)

        # Create two providers
        for _ in range(2):
            SteerageProviderFactory.create(
                steerage=steerage_with_both,
                status=SteerageProviderStatuses.ACCEPTED,
                waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            )

        # Create the campaign and set the context
        campaign = SteerageChooseProviderCampaign()
        campaign.create_context(steerage=steerage_with_both, person=person_for_outreach)

        # Test email subject with referral
        self.assertEqual(campaign.get_email_subject(), "Your referral is ready")

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with both referral and waiver
        expected_content_with_both = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit. This service qualifies for a Care Key.

Here's what to do next.

1. **Choose a provider.**
Check out the options in the app. Care will be free or lower cost when you pick a provider who's Care Key eligible.

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
This helps us follow up on care and avoid billing issues.

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_with_both)

        # Test Case 2: Steerage with referral but no waiver
        steerage_with_referral_only = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            waiver=None,
        )

        for _ in range(2):
            SteerageProviderFactory.create(
                steerage=steerage_with_referral_only,
                status=SteerageProviderStatuses.ACCEPTED,
                waiver_level=SteerageProviderWaiverLevel.NOT_APPLICABLE,
            )

        campaign = SteerageChooseProviderCampaign()
        campaign.create_context(steerage=steerage_with_referral_only, person=person_for_outreach)

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with referral only
        expected_content_with_referral_only = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit.

Here's what to do next.

1. **Choose a provider.**
Check out the options in the app.

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
This helps us follow up on care.

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_with_referral_only)

        # Test Case 3: Steerage with waiver and no referral
        steerage_with_waiver_only = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            referral=None,
        )

        for _ in range(2):
            SteerageProviderFactory.create(
                steerage=steerage_with_waiver_only,
                status=SteerageProviderStatuses.ACCEPTED,
                waiver_level=SteerageProviderWaiverLevel.NOT_APPLICABLE,
            )

        campaign = SteerageChooseProviderCampaign()
        campaign.create_context(steerage=steerage_with_waiver_only, person=person_for_outreach)

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with referral only
        expected_content_with_referral_only = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit.

Here's what to do next.

1. **Choose a provider.**
Check out the options in the app.

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
This helps us follow up on care.

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

    @mock.patch(
        "firefly.modules.referral.tasks.send_referral_outreach_async.send",
        new=send_referral_outreach_sync,
    )
    @mock.patch("firefly.core.services.braze.templates.reminders.send")
    def test_steerage_outreach_campaign_scheduled(self, reminders):
        # Case 1: Steerage with a referral
        steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            scheduling_date=self.today + timedelta(days=1),
        )
        SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
        )

        # Lock the steerage
        self.provider_client.patch(f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={True}", format="json")

        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SCHEDULED"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_person.id))

        # Verify expected trigger properties exist
        trigger_props = recipients[0]["trigger_properties"]

        # Verify content for case with a referral
        self.assertEqual(trigger_props["email_subject"], "Your referral is ready")
        self.assertEqual(trigger_props["push_title"], "Your referral is ready")

        # Verify static content
        self.assertEqual(trigger_props["email_preheader"], "And the visit is already booked")
        self.assertEqual(
            trigger_props["push_message"],
            "And the visit is already booked. See visit details in the app."
            + " (Need to reschedule? Reach out to the provider directly.)",
        )
        self.assertTrue(f"referral-detail?id={steerage.id}" in trigger_props["push_deeplink"])

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SCHEDULED"]},
            },
        )

        # Reset mock for next test case
        reminders.reset_mock()

        # Case 2: Steerage without a referral
        steerage_no_referral = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            scheduling_date=self.today + timedelta(days=1),
            referral=None,
        )
        SteerageProviderFactory.create(
            steerage=steerage_no_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=self.today,
        )

        # Lock the steerage
        self.provider_client.patch(
            f"/referral/steerage/{steerage_no_referral.id}/toggle_lock/?is_locked={True}", format="json"
        )

        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SCHEDULED"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_person.id))

        # Verify expected trigger properties exist
        trigger_props = recipients[0]["trigger_properties"]

        # Verify content for case without a referral
        self.assertEqual(trigger_props["email_subject"], "Your provider request is ready")
        self.assertEqual(trigger_props["push_title"], "Your provider request is ready")

    @mock_braze_markdown_renderer
    def test_steerage_outreach_campaign_scheduled_markdown(self):
        """Test that the SteerageScheduledCampaign produces the expected markdown content
        for different combinations of referral and care provider."""
        # Test Case 1: Steerage with referral and provider name
        specialty_group = SpecialtyGroupFactory.create(label="Dermatology")

        steerage_with_referral = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            scheduling_date=date.today() + timedelta(days=3),
        )

        SteerageProviderFactory.create(
            steerage=steerage_with_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="Foo",
            last_name="Bar",
        )

        person_for_outreach = get_person_for_outreach(steerage_with_referral.person.id)

        # Create the campaign and set the context
        from firefly.modules.referral.campaigns.steerage_scheduled import SteerageScheduledCampaign

        campaign = SteerageScheduledCampaign()
        campaign.create_context(steerage=steerage_with_referral, person=person_for_outreach)

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with referral and provider name
        expected_content = f"""
Hi {self.patient.person.first_name},

**You've already got a visit booked with Foo Bar.** See the visit date and provider details in the app.

There's nothing else you need to do right now. But keep in mind: **If you need to change your visit date, reschedule with the provider directly.** Then update the visit date in the app.

That's it. If you have questions, send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""  # noqa: E501

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content)

        # Test Case 2: Steerage without referral
        steerage_no_referral = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            referral=None,
            scheduling_date=date.today() + timedelta(days=3),
        )

        SteerageProviderFactory.create(
            steerage=steerage_no_referral,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.PROVIDER,
            first_name="John",
            last_name="Doe",
        )

        person_for_outreach = get_person_for_outreach(steerage_no_referral.person.id)

        campaign = SteerageScheduledCampaign()
        campaign.create_context(steerage=steerage_no_referral, person=person_for_outreach)

        # Get the email content for case without referral
        email_content = campaign.get_email_content()

        # Expected content for case without referral (should be same except for title differences)
        expected_content_no_referral = f"""
Hi {self.patient.person.first_name},

**You've already got a visit booked with John Doe.** See the visit date and provider details in the app.

There's nothing else you need to do right now. But keep in mind: **If you need to change your visit date, reschedule with the provider directly.** Then update the visit date in the app.

That's it. If you have questions, send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""  # noqa: E501

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_no_referral)

        # Test Case 3: Provider with facility name
        steerage_facility = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            scheduling_date=date.today() + timedelta(days=3),
        )

        SteerageProviderFactory.create(
            steerage=steerage_facility,
            status=SteerageProviderStatuses.ACCEPTED,
            member_selected_at=date.today(),
            waiver_level=SteerageProviderWaiverLevel.FACILITY,
            care_organization_name="FooMed Bar Center",
        )

        person_for_outreach = get_person_for_outreach(steerage_facility.person.id)

        campaign = SteerageScheduledCampaign()
        campaign.create_context(steerage=steerage_facility, person=person_for_outreach)

        # Get the email content for case with facility name
        email_content = campaign.get_email_content()

        # Verify the facility name is used instead of provider name
        expected_content_with_facility_name = f"""
Hi {self.patient.person.first_name},

**You've already got a visit booked with FooMed Bar Center.** See the visit date and provider details in the app.

There's nothing else you need to do right now. But keep in mind: **If you need to change your visit date, reschedule with the provider directly.** Then update the visit date in the app.

That's it. If you have questions, send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""  # noqa: E501
        self.assertEqual(email_content, expected_content_with_facility_name)

    @override_switch(WAFFLE_SWITCH_SELF_SERVICE_NAV, active=True)
    @mock.patch(
        "firefly.modules.referral.tasks.send_referral_outreach_async.send",
        new=send_referral_outreach_sync,
    )
    @mock.patch("firefly.core.services.braze.templates.reminders.send")
    def test_steerage_outreach_campaign_self_service(self, reminders):
        #  Create a eligible service category for Self Serve
        service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])
        # Create waiver from this service
        waiver = WaiverFactory()
        waiver.service_categories.add(service_category)
        waiver.save()

        # Case 1: Has both referral and waiver
        steerage = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            waiver=waiver,
        )

        # Create two providers, the minimum threshold for self-service
        for _ in range(2):
            SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.UNDER_REVIEW)

        # Lock the steerage
        self.provider_client.patch(f"/referral/steerage/{steerage.id}/toggle_lock/?is_locked={True}", format="json")

        # Check that reminders.send was called once with the right campaign ID
        reminders.assert_called_once()

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]

        # Verify the campaign ID
        self.assertEqual(call_args[0], settings.BRAZE["REFERRAL_READY_SELF_SERVICE"])

        # Verify the recipient external_user_id
        recipients = call_args[1]
        self.assertEqual(recipients[0]["external_user_id"], str(self.steerage_person.id))

        # Verify expected trigger properties exist
        trigger_props = recipients[0]["trigger_properties"]

        # Test the email subject - should be "Your referral is ready"
        # when steerage has referral (default in the test)
        self.assertEqual(trigger_props["email_subject"], "Your referral is ready")

        # Verify push notification content for case with both referral and waiver
        self.assertEqual(trigger_props["push_title"], "Your referral is ready")
        self.assertEqual(
            trigger_props["push_message"],
            "Care will be free or lower cost when you pick a Care Key eligible provider."
            + " See options in the app, then book a visit.",
        )

        # Verify other basic expected content
        self.assertEqual(trigger_props["email_preheader"], "Next up: Pick a provider")
        self.assertTrue(f"referral-search?id={steerage.id}" in trigger_props["push_deeplink"])

        # Verify event metadata
        self.assertEqual(
            call_args[2],
            {
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": settings.BRAZE["REFERRAL_READY_SELF_SERVICE"]},
            },
        )

        # Reset mock to test the case without a referral but with waiver
        reminders.reset_mock()

        # Case 2: Has waiver but no referral
        steerage_no_referral = SteerageFactory(
            person=self.steerage_person,
            effective_from=self.today,
            effective_through=self.today + timedelta(days=2),
            specialty_group=self.specialty_group,
            waiver=waiver,
            referral=None,
        )

        # Create two providers for self-service
        for _ in range(2):
            SteerageProviderFactory.create(steerage=steerage_no_referral, status=SteerageProviderStatuses.UNDER_REVIEW)

        # Lock the steerage
        self.provider_client.patch(
            f"/referral/steerage/{steerage_no_referral.id}/toggle_lock/?is_locked={True}", format="json"
        )

        # Extract the call arguments for validation
        call_args = reminders.call_args[0]
        recipients = call_args[1]
        trigger_props = recipients[0]["trigger_properties"]

        # Test the email subject - should be "Your provider request is ready" when no referral
        self.assertEqual(trigger_props["email_subject"], "Your provider request is ready")

        # Test push notification content for case with waiver but no referral
        self.assertEqual(trigger_props["push_title"], "Your provider request is ready")
        self.assertEqual(
            trigger_props["push_message"],
            "Care will be free or lower cost when you pick a Care Key eligible provider."
            + " See options in the app, then book a visit.",
        )

    @mock_braze_markdown_renderer
    def test_self_service_campaign_markdown(self):
        """Test that the SteerageSelfServiceCampaign produces the expected markdown content with and without referral"""
        # Test Case 1: Steerage with both referral and waiver
        specialty_group = SpecialtyGroupFactory.create(label="Dermatology")
        service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])
        waiver = WaiverFactory()
        waiver.service_categories.add(service_category)
        waiver.save()

        steerage_with_referral = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            waiver=waiver,
            is_self_service_enabled=True,
        )
        person_for_outreach = get_person_for_outreach(steerage_with_referral.person.id)

        # Create the campaign and set the context
        campaign = SteerageSelfServiceCampaign()
        campaign.create_context(steerage=steerage_with_referral, person=person_for_outreach)

        # Test email subject with referral
        self.assertEqual(campaign.get_email_subject(), "Your referral is ready")

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with both referral and waiver
        expected_content_with_both = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit. This service qualifies for a Care Key.

Here's what to do next.

1. **Choose a provider.**
Check out the options in the app. Care will be free or lower cost when you pick a provider who's Care Key eligible.

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
This helps us follow up on care and avoid billing issues.

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_with_both)

        # Test Case 2: Steerage with waiver but no referral
        steerage_with_waiver_only = SteerageFactory(
            person=self.patient.person,
            effective_from=date.today(),
            effective_through=date.today() + timedelta(days=2),
            specialty_group=specialty_group,
            waiver=waiver,
            referral=None,
            is_self_service_enabled=True,
        )
        person_for_outreach = get_person_for_outreach(steerage_with_waiver_only.person.id)
        campaign = SteerageSelfServiceCampaign()
        campaign.create_context(steerage=steerage_with_waiver_only, person=person_for_outreach)

        # Test email subject without referral
        self.assertEqual(campaign.get_email_subject(), "Your provider request is ready")

        # Get the email content
        email_content = campaign.get_email_content()

        # Expected content with waiver only
        expected_content_with_waiver_only = f"""
Hi {self.patient.person.first_name},

You're all set to schedule your dermatology visit. This service qualifies for a Care Key.

Here's what to do next.

1. **Choose a provider.**
Check out the options in the app. Care will be free or lower cost when you pick a provider who's Care Key eligible.

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
This helps us avoid billing issues.

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""

        # Compare the actual with expected content
        self.assertEqual(email_content, expected_content_with_waiver_only)
