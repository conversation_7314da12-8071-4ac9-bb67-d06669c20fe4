import unittest
from datetime import date, datetime, timedelta

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.core.user.factories import PersonUserFactory
from firefly.modules.programs.constants import BenefitProgramStatus, PrimaryCareProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.referral.constants import MemberServiceLevel
from firefly.modules.referral.utils.provider_search_utils import get_member_service_level
from firefly.modules.referral.utils.waiver_utils import is_in_the_past


class TestMemberServiceLevel(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

    def test_get_member_service_level(self):
        person = PersonUserFactory()
        self.assertEqual(get_member_service_level(person), MemberServiceLevel.CARE)
        start_date = datetime.now()
        benefit_enrollment = ProgramEnrollment.objects.create(
            person=person,
            program_id=ProgramCodes.BENEFIT,
            period=(start_date, None),
            status=BenefitProgramStatus.ENROLLED,
        )
        self.assertEqual(get_member_service_level(person), MemberServiceLevel.COVERAGE)
        primary_care_enrollment = ProgramEnrollment.objects.create(
            person=person,
            program_id=ProgramCodes.PRIMARY_CARE,
            period=(start_date, None),
            status=PrimaryCareProgramStatus.ESTABLISHED,
        )
        self.assertEqual(get_member_service_level(person), MemberServiceLevel.CARE_AND_COVERAGE)
        # Being churned from Primary Care should drop the member out of care + coverage
        primary_care_enrollment.status = PrimaryCareProgramStatus.CHURNED
        primary_care_enrollment.save()
        self.assertEqual(get_member_service_level(person), MemberServiceLevel.COVERAGE)
        # Being terminated from the benefit should drop the member out of coverage
        benefit_enrollment.status = BenefitProgramStatus.TERMINATED
        benefit_enrollment.save()
        # Care is the default
        self.assertEqual(get_member_service_level(person), MemberServiceLevel.CARE)


class TestIsInThePast(unittest.TestCase):
    def test_is_null_when_input_is_none(self):
        self.assertEqual(is_in_the_past(None), None)

    def test_is_false_when_input_is_now_as_datetime(self):
        value = datetime.now()
        self.assertEqual(is_in_the_past(value), False)

    def test_is_false_when_input_is_today_as_date(self):
        value = date.today()
        self.assertEqual(is_in_the_past(value), False)

    def test_is_false_when_input_is_today_as_string(self):
        value = date.today().strftime("%Y-%m-%d")
        self.assertEqual(is_in_the_past(value), False)

    def test_is_true_when_input_is_24_hours_ago_as_datetime(self):
        value = datetime.now() - timedelta(days=1)
        self.assertEqual(is_in_the_past(value), True)

    def test_is_true_when_input_is_yesterday_as_date(self):
        value = date.today() - timedelta(days=1)
        self.assertEqual(is_in_the_past(value), True)

    def test_is_true_when_input_is_yesterday_as_string(self):
        value = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        self.assertEqual(is_in_the_past(value), True)
