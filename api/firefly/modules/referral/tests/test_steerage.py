import datetime
import uuid
from datetime import date, timedelta

from django.contrib.contenttypes.models import ContentType
from faker import Faker

from firefly.core.alias.models import <PERSON><PERSON><PERSON>apping, AliasName, get_content_type
from firefly.core.feature.testutils import override_switch
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import reset_context_to_luci_user
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.models.models import AssigneeGroup, Person
from firefly.modules.cases.constants import (
    REFERRAL_NOTE_RETRIEVAL_CATEGORY,
    REFERRAL_REQUEST,
    ReferralRequestCaseStatuses,
)
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation, Tag
from firefly.modules.facts.factories import (
    ClinicalFocusAreaFactory,
    LocationTypeFactory,
    ServiceCategoryFactory,
    SpecialtyGroupFactory,
)
from firefly.modules.facts.models import ClinicalFocusArea, LocationType, SpecialtyGroup
from firefly.modules.insurance.constants import ContractAttributionType, ContractPMPMType
from firefly.modules.insurance.factories import ContractFactory, InsuranceMemberInfoFactory
from firefly.modules.insurance.models import InsuranceMemberInfo, InsurancePayer
from firefly.modules.network.factories import (
    CuratedProviderFactory,
    CuratedProviderPartnershipFactory,
    PartnershipFactory,
)
from firefly.modules.network.models import (
    AgreementTypeConfig,
    ContactInformation,
    CuratedProvider,
    CuratedProviderCareOrgNameChoice,
    CuratedProviderType,
    Partnership,
    PartnershipType,
)
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.referral.constants import (
    CARE_NOT_NEEDED_ACTION,
    COMMODITY_SEGMENT_LOCATION_TYPES,
    COMMODITY_SEGMENT_SPECIALTY,
    ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE,
    HIGH_TCOC_SEGMENT_SPECIALTY,
    MEMBER_DISCHARGED_ACTION,
    RESTEERED_ACTION,
    STEERAGE_NOT_NEEDED_ACTION,
    WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER,
    WAFFLE_SWITCH_SELF_SERVICE_NAV,
    LocationTypes,
    MemorialHermannConfig,
)
from firefly.modules.referral.factories import (
    ClinicalFocusAreaSpecialtyFactory,
    ElationReferralOrderFactory,
    PartnershipServiceFactory,
    ReferralFactory,
    ReferralRecipientFactory,
    ServiceFactory,
    ServiceSpecialtyGroupLocationTypeFactory,
    SteerageFactory,
    SteerageProviderFactory,
    SteerageServiceQuantityFactory,
    WaiverFactory,
)
from firefly.modules.referral.models import (
    ElationReferralLetter,
    FaxStatus,
    LocationTypePartnershipType,
    ReasonForExceptionConfig,
    ReferralInsuranceAuthICDCode,
    ReferralPriority,
    Service,
    Steerage,
    SteerageDisposition,
    SteerageProviderDataSourceConfig,
    SteerageProviderStatuses,
    SteerageRequestTypeConfig,
    SteerageSegment,
    SteerageStatus,
    SteerageSuccessConfig,
    SteerageTypeOfVisit,
    VendorTransmissionStatusChoices,
    Waiver,
    WaiverTransmissionStatus,
)
from firefly.modules.referral.tests.test_referral_letter_elation_sync import create_mock_elation_referral_letter
from firefly.modules.referral.tests.test_steerage_utils import set_up_steerage_affected_case_categories
from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case
from firefly.modules.referral.utils.waiver_utils import _add_to_benefit
from firefly.modules.states.models import State


class SteerageTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        # Get or create the referrals group, to which insurance approval cases will be assigned
        referrals_group_unique_key = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["referrals_group"]
        cls.referrals_group_assignee_group, _ = AssigneeGroup.objects.get_or_create(
            name=referrals_group_unique_key, unique_key=referrals_group_unique_key
        )

        # Create commonly used objects once
        cls.state_ma, _ = State.objects.get_or_create(
            abbreviation="MA", defaults={"name": "Massachusetts", "can_service": True}
        )

        # Create common specialty groups and location types
        cls.specialty_group = SpecialtyGroupFactory.create()
        cls.location_type = LocationTypeFactory.create()

        # Create common service categories
        cls.service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])

        # Create common clinical focus area and service
        cls.banana_service = ServiceFactory.create(description="banana")
        cls.banana_clinical_focus_area = ClinicalFocusAreaFactory.create(label="banana_focus_area")

        # Create common case categories with proper state machine
        set_up_steerage_affected_case_categories(cls)

        # Create common users that are used across multiple tests
        cls.common_provider_user = PersonUserFactory.create()
        cls.common_member_user = PersonUserFactory.create()

        # Create common steerage objects for tests that don't need specific configurations
        cls.common_steerage = SteerageFactory.create()
        cls.common_steerage_with_specialty = SteerageFactory.create(specialty_group=cls.specialty_group)

    def test_default_effective_from(self, *args):
        steerage = SteerageFactory()
        # it should create a waiver with effective from as todays date
        self.assertEqual(steerage.effective_from.strftime("%Y-%m-%d"), date.today().strftime("%Y-%m-%d"))

        # now update it to tomorrow and it should change to tomorrow
        payload = {"effective_from": date.today() + timedelta(days=1)}
        result = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        self.assertEqual(result.json()["effective_from"], payload["effective_from"].strftime("%Y-%m-%d"))

    def test_steerage_validation(self, *args):
        # test the validations for the effective dates
        referral_person = self.patient.person
        today = date.today()
        yesterday = today - timedelta(days=1)
        tomorrow = date.today() + timedelta(days=1)

        steerage = SteerageFactory(
            person=referral_person,
            effective_from=today,
            effective_through=tomorrow,
        )
        steerage_id = steerage.id

        # steerage should not be updated if the effective through is before tomorrow
        # and it is unlocked
        payload = {
            "person": referral_person.pk,
            "effective_from": today,
            "effective_through": today,
            "description": "Lorem ipsum",
        }
        updated_steerage_1 = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(updated_steerage_1.status_code, 400)

        # update waiver if all data are valid
        payload = {
            "person": referral_person.pk,
            "effective_from": yesterday,
            "effective_through": tomorrow,
            "description": "Lorem ipsum",
        }
        updated_steerage_1 = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )

        self.assertEqual(updated_steerage_1.status_code, 200)
        self.assertEqual(updated_steerage_1.json()["description"], payload["description"])
        self.assertEqual(
            updated_steerage_1.json()["effective_from"],
            payload["effective_from"].strftime("%Y-%m-%d"),
        )
        self.assertEqual(
            updated_steerage_1.json()["effective_through"],
            payload["effective_through"].strftime("%Y-%m-%d"),
        )

    def test_list_steerage_by_person(self, *args):
        # test create and get api for waiver
        referral_person = self.patient.person
        SteerageFactory(person=referral_person)

        # Test GET as provider
        get_response = self.provider_client.get(f"/referral/steerage/person/{referral_person.pk}/", format="json")
        self.assertEqual(get_response.status_code, 200)
        self.assertEqual(len(get_response.json()), 1)

        # add two more steerage to same person and assert
        SteerageFactory(person=referral_person)
        SteerageFactory(person=referral_person)
        # Test GET as provider
        get_response = self.provider_client.get(f"/referral/steerage/person/{referral_person.pk}/", format="json")
        self.assertEqual(get_response.status_code, 200)
        self.assertEqual(len(get_response.json()), 3)

        # Add one more steerage without waiver and the count of steerage should be 4
        SteerageFactory(person=referral_person, waiver=None)
        get_response = self.provider_client.get(f"/referral/steerage/person/{referral_person.pk}/", format="json")
        self.assertEqual(get_response.status_code, 200)
        self.assertEqual(len(get_response.json()), 4)

    def test_retrieving_steerage(self, *args):
        steerage = SteerageFactory(is_initiated_from_lucian=True)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        # TODO: assert more fields when we add to steerage
        self.assertEqual(response.json()["id"], steerage_id)
        self.assertEqual(response.json()["person"], steerage.person.id)
        self.assertEqual(
            response.json()["member_requested_data"]["specialty"], steerage.member_requested_data.specialty
        )
        self.assertEqual(response.json()["is_initiated_from_lucian"], True)

    def test_update_steerage_new_member_data(self, *args):
        steerage = SteerageFactory()
        steerage_id = steerage.id
        steerage.member_requested_data = None
        steerage.save()
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # Add member requested fields and confirm it got created
        provider_response = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/", {"member_requested_data": {"specialty": "some value"}}, format="json"
        )
        self.assertEqual(provider_response.status_code, 200)
        steerage.refresh_from_db()
        self.assertEqual(steerage.member_requested_data.specialty, "some value")

    def test_update_steerage_update_member_data(self, *args):
        steerage = SteerageFactory()
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # Add member requested fields and confirm it got created
        provider_response = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/", {"member_requested_data": {"specialty": "some value"}}, format="json"
        )
        self.assertEqual(provider_response.status_code, 200)
        steerage.refresh_from_db()
        self.assertEqual(steerage.member_requested_data.specialty, "some value")
        # other fields should not be updated
        self.assertEqual(
            steerage.member_requested_data.description, provider_response.json()["member_requested_data"]["description"]
        )

    def test_has_waiver_and_has_referral(self, *args):
        # first create steerage factory without waiver and steerage
        steerage = SteerageFactory(waiver=None, referral=None)
        # now get the same steerage
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_waiver"], False)
        self.assertEqual(response.json()["has_referral"], False)

        # create a steerage with both waiver and referral
        steerage = SteerageFactory()
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        # has_waiver and has_referral should not be none
        self.assertEqual(response.json()["has_waiver"], True)
        self.assertEqual(response.json()["has_referral"], True)

    def test_has_active_waiver(self, *args):
        # Scenario 1
        #   Create steerage without waiver without referral
        # Expected behavior
        #   has_active_waiver is false
        steerage = SteerageFactory(waiver=None, referral=None)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], False)

        # Scenario 2
        #   Create steerage without waiver with referral
        # Expected behavior
        #   has_active_waiver is false
        steerage = SteerageFactory(waiver=None)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], False)

        # Scenario 3
        #   Create steerage with waiver and with referral
        # Expected behavior
        #   has_active_waiver is True
        steerage = SteerageFactory()
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], True)

        # Scenario 4
        #   Create steerage with waiver and without referral
        # Expected behavior
        #   has_active_waiver is True
        steerage = SteerageFactory(referral=None)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], True)

        # Scenario 5
        #   Create steerage with waiver and with referral and waiver.is_active is false
        # Expected behavior
        #   has_active_waiver is False
        steerage = SteerageFactory()
        steerage.waiver.is_active = False
        steerage.waiver.save()

        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], False)

        # Scenario 6
        #   Create steerage with waiver and with referral and waiver.is_active is True
        # Expected behavior
        #   has_active_waiver is True
        steerage = SteerageFactory()
        steerage.waiver.is_active = True
        steerage.waiver.save()

        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_active_waiver"], True)

    def test_referral_icd_10_codes_in_steerage(self):
        # first create steerage factory without waiver and steerage
        referral = ReferralFactory()

        # create auth icd 10 codes for
        faker: Faker = Faker()
        icd10_code_1 = ReferralInsuranceAuthICDCode.objects.create(
            code=faker.name(), description=faker.name(), referral=referral
        )
        icd10_code_2 = ReferralInsuranceAuthICDCode.objects.create(
            code=faker.name(), description=faker.name(), referral=referral
        )

        steerage = SteerageFactory(waiver=None, referral=referral)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        response_body = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_body["has_waiver"], False)
        self.assertEqual(response_body["has_referral"], True)

        icd10_codes_received = response_body["referral"]["referral_insurance_auth_icd_codes"]
        self.assertEqual(len(icd10_codes_received), 2)

        for icd10_code in icd10_codes_received:
            if icd10_code["code"] == icd10_code_1.code:
                self.assertEqual(icd10_code["description"], icd10_code_1.description)
            else:
                self.assertEqual(icd10_code["description"], icd10_code_2.description)

    def test_is_effectively_properties(self, *args):
        # first create steerage factory without waiver and steerage
        steerage = SteerageFactory(waiver=None, referral=None)
        # now get the same steerage
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["is_effectively_waiver_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_and_waiver"], False)

        # create a steerage with both waiver and referral
        steerage = SteerageFactory()
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["is_effectively_waiver_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_and_waiver"], True)

        # create a steerage with just a waiver
        steerage = SteerageFactory(referral=None)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["is_effectively_waiver_only"], True)
        self.assertEqual(response.json()["is_effectively_referral_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_and_waiver"], False)

        # create a steerage with just a referral
        steerage = SteerageFactory(waiver=None)
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["is_effectively_waiver_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_only"], True)
        self.assertEqual(response.json()["is_effectively_referral_and_waiver"], False)

        # create a steerage with both waiver and referral, and set waiver.is_active to False
        steerage = SteerageFactory()
        steerage.waiver.is_active = False
        steerage.waiver.save()
        steerage_id = steerage.id
        response = self.provider_client.get(
            f"/referral/steerage/{steerage_id}/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["has_waiver"], True)
        self.assertEqual(response.json()["has_referral"], True)
        self.assertEqual(response.json()["is_effectively_waiver_only"], False)
        self.assertEqual(response.json()["is_effectively_referral_only"], True)
        self.assertEqual(response.json()["is_effectively_referral_and_waiver"], False)

    def test_coverage_only_priority(self, *args):
        # create a steerage with both waiver and referral
        steerage = SteerageFactory(priority=None)
        self.assertIsNone(steerage.priority)

        # create a steerage with just a waiver
        steerage = SteerageFactory(priority=None, referral=None)
        self.assertEquals(steerage.priority, ReferralPriority.STANDARD)

        # Try patching the steerage, check priority doesn't change
        payload = {
            "notify_clinician_yn": False,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        steerage.refresh_from_db()
        self.assertEquals(steerage.priority, ReferralPriority.STANDARD)

    def test_waiver_is_active_priority(self, *args):
        # create a steerage with both waiver and referral
        steerage = SteerageFactory(priority=None)
        steerage.waiver.is_active = False
        steerage.waiver.save()
        steerage.waiver.refresh_from_db()
        self.assertEqual(steerage.waiver.is_active, False)

        # Try patching the steerage with is_active as True
        payload = {
            "waiver": {
                "service_category_ids": [],
                "is_active": True,
            }
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        steerage.refresh_from_db()
        steerage.waiver.refresh_from_db()
        self.assertEquals(steerage.waiver.is_active, True)

        # Change it to False and patch it again
        payload = {
            "waiver": {
                "service_category_ids": [],
                "is_active": False,
            }
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        steerage.refresh_from_db()
        steerage.waiver.refresh_from_db()
        self.assertEquals(steerage.waiver.is_active, False)

    def test_note_retrieval_creation_side_effect(self, *args):
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group
        two_days_ago = date.today() - timedelta(days=2)

        steerage = SteerageFactory()

        # Ensure that no Cases were created with the scheduling date unset.
        self.assertEqual(Case.objects.count(), 0)

        # attempt to update the steerage
        payload = {
            "scheduling_date": two_days_ago,
            "request_type": "broad",
        }
        steerage_id = steerage.id
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # get steerage and validate
        steerage = Steerage.objects.get(id=steerage_id)
        self.assertEqual(steerage.scheduling_date.strftime("%Y-%m-%d"), two_days_ago.strftime("%Y-%m-%d"))
        self.assertEqual(steerage.request_type, SteerageRequestTypeConfig.BROAD_REQUEST)

        # A Note Retreival case should have been created
        cases = Case.objects.all()
        self.assertEqual(len(cases), 1)
        self.assertEqual(cases[0].category.unique_key, REFERRAL_NOTE_RETRIEVAL_CATEGORY)
        # The case status should be "Draft" based on the initial state in our state machine
        self.assertEqual(cases[0].status, "Draft")

        # set scheduling date to blank
        payload = {"scheduling_date": None}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        # get steerage and validate
        steerage = Steerage.objects.get(id=steerage_id)
        self.assertEqual(steerage.scheduling_date, None)

        # Next, lock the steerage
        payload = {"is_locked": True}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(response.status_code, 200)

    def test_steerage_v2(self, *args):
        referral_person = self.patient.person
        steerage: Steerage = SteerageFactory(person=referral_person, effective_through=date.today())
        # Case category already exists from class-level setup
        response = self.client.get(
            f"/referral/steerage/person/{referral_person.id}/viewable/v2",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])
        steerage.is_locked = True
        provider = SteerageProviderFactory.create()
        steerage.steerage_providers.set([provider])
        steerage.save()
        response = self.client.get(
            f"/referral/steerage/person/{referral_person.id}/viewable/v2",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        result = response.json()[0]
        self.assertEqual(result["archive_at"], (date.today() + timedelta(days=1)).strftime("%Y-%m-%d"))
        self.assertEqual(result["has_waiver"], True)
        self.assertEqual(result["has_active_waiver"], True)
        self.assertEqual(result["has_referral"], True)
        self.assertIsNotNone(result["effective_through"])
        self.assertEqual(result["is_locked"], True)
        self.assertIsNotNone(result["description"])
        self.assertIsNotNone(result["steerage_providers"])

        provider_1 = SteerageProviderFactory.create()
        steerage.steerage_providers.set([provider, provider_1])
        steerage.save()
        specialty_group = SpecialtyGroupFactory.create()
        steerage.specialty_group = specialty_group
        steerage.save()

        response = self.client.get(
            f"/referral/steerage/person/{referral_person.id}/viewable/v2",
            format="json",
        )
        result = response.json()[0]
        self.assertEqual(result["specialty_group"], specialty_group.id)
        self.assertEqual(result["specialty_group_details"]["label"], specialty_group.label)

        # add dependent to referral person to chcek the dependent flow.
        member_id = "FF00001"
        date_18_years_ago = datetime.datetime.now() - timedelta(days=365 * 18)
        _add_to_benefit(referral_person, member_id, "00")
        under_18_dependent = PersonUserFactory(dob=date_18_years_ago + timedelta(days=30))
        _add_to_benefit(under_18_dependent, member_id, "01")
        over_18_dependent = PersonUserFactory(dob=date_18_years_ago - timedelta(days=30))
        _add_to_benefit(over_18_dependent, member_id, "02")
        steerage_referral_dependent_1 = SteerageFactory(person=under_18_dependent)
        steerage_referral_dependent_1.is_locked = True
        provider = SteerageProviderFactory.create()
        steerage_referral_dependent_1.steerage_providers.set([provider])
        steerage_referral_dependent_1.save()
        steerage_referral_dependent_2 = SteerageFactory(person=over_18_dependent)
        steerage_referral_dependent_2.is_locked = True
        provider = SteerageProviderFactory.create()
        steerage_referral_dependent_2.steerage_providers.set([provider])
        steerage_referral_dependent_2.save()
        response = self.client.get(
            f"/referral/steerage/person/{referral_person.id}/viewable/v2",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 2)

    def test_steerage_lock_unlock_with_waiver(self, *args):
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # first create a steerage
        steerage_person = PersonUserFactory.create()
        tomorrow = date.today() + timedelta(days=1)
        steerage = SteerageFactory(
            person=steerage_person,
            effective_from=tomorrow,
            effective_through=tomorrow + timedelta(days=1),
        )
        _ = ReferralRecipientFactory(
            direct_message_to=MemorialHermannConfig.MH_DIRECT_MESSAGE_ADDRESS,
            referral=steerage.referral,
        )

        steerage_pk = steerage.id
        waiver_pk = steerage.waiver.id

        # Assert there's no transmission status to start
        self.assertIsNone(steerage.referral.vendor_transmission_status)

        # Assert there's no recommendation_sent_at to start
        self.assertIsNone(steerage.recommendation_sent_at)

        # Verify that a transmission status exists
        waiver: Waiver = Waiver.objects.get(id=waiver_pk)
        waiver_transmission_status: WaiverTransmissionStatus = waiver.transmission_status
        self.assertIsNotNone(waiver_transmission_status)
        self.assertEqual(waiver_transmission_status.status, VendorTransmissionStatusChoices.PENDING)
        self.assertIsNone(waiver_transmission_status.sent_at)
        self.assertIsNone(waiver_transmission_status.response)
        self.assertIsNone(waiver_transmission_status.payload)

        # Now add a provider to the steerage for testing the retain of member selected provider
        # Use class-level state
        state_ma = self.__class__.state_ma
        faker: Faker = Faker()
        payload = {
            "first_name": faker.name(),
            "last_name": faker.name(),
            "npi": uuid.uuid1().hex[:10],
            "care_organization_name": faker.name(),
            "street_address": faker.name(),
            "address_line_1": "",
            "address_line_2": "",
            "city": faker.name(),
            "zip_code": "12345",
            "state_id": state_ma.pk,
            "specialty_list": [faker.name()],
            "reason_for_exception": ReasonForExceptionConfig.SOURCED_FROM_GARNER,
            "data_source": SteerageProviderDataSourceConfig.RIBBON,
        }
        steerage_provider = self.provider_client.post(
            f"/referral/steerage/{steerage_pk}/providers/",
            payload,
            format="json",
        )
        # select this provider for the steerage
        steerage_provider_id = steerage_provider.json()["id"]
        member_selected = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/providers/{steerage_provider_id}/select/",
            format="json",
        )
        self.assertEqual(member_selected.status_code, 200)
        self.assertEqual(member_selected.json()["member_selected_at"], date.today().strftime("%Y-%m-%d"))

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked={True}", format="json"
        )

        # Assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        self.assertEqual(locked_steerage.json()["locked_by"], self.provider.id)
        self.assertNotEqual(locked_steerage.json()["locked_at"], None)

        # Since this was mocked to be a MH Referral, test the Vendor Transmission Status
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.vendor_transmission_status, VendorTransmissionStatusChoices.PENDING)

        # Update the transmission status to mock that the waiver was sent out
        waiver_transmission_status.status = VendorTransmissionStatusChoices.SUCCESS
        waiver_transmission_status.sent_at = datetime.datetime.now()
        waiver_transmission_status.response = {}
        waiver_transmission_status.payload = {}
        waiver_transmission_status.save()

        # Unlock the steerage
        unlocked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked={False}", format="json"
        )
        self.assertEqual(unlocked_steerage.json()["is_locked"], False)
        self.assertEqual(unlocked_steerage.json()["locked_at"], None)
        self.assertEqual(unlocked_steerage.json()["locked_by"], None)

        # Assert the waiver moved back to pending
        waiver_transmission_status.refresh_from_db()
        self.assertEqual(waiver_transmission_status.status, VendorTransmissionStatusChoices.PENDING)
        self.assertIsNone(waiver_transmission_status.sent_at)
        self.assertIsNone(waiver_transmission_status.response)
        self.assertIsNone(waiver_transmission_status.payload)

        # assert if the provider still have the member selected date
        steerage_provider = self.provider_client.get(
            f"/referral/steerage/{steerage_pk}/provider/{steerage_provider_id}/",
            format="json",
        )
        self.assertEqual(
            steerage_provider.json()["member_selected_at"],
            date.today().strftime("%Y-%m-%d"),
        )

        # Assert an invalid request fails
        response_for_invalid_request = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked=RandomValue", format="json"
        )
        self.assertEqual(response_for_invalid_request.status_code, 400)

    def test_steerage_lock_unlock_without_waiver(self, *args):
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # first create a steerage
        steerage_person = PersonUserFactory.create()
        steerage: Steerage = SteerageFactory(person=steerage_person, effective_from=None, effective_through=None)
        steerage_pk = steerage.id

        # Now add a provider to the steerage for testing the retain of member selected provider
        # Use class-level state
        state_ma = self.__class__.state_ma
        faker: Faker = Faker()
        payload = {
            "first_name": faker.name(),
            "last_name": faker.name(),
            "npi": uuid.uuid1().hex[:10],
            "care_organization_name": faker.name(),
            "street_address": faker.name(),
            "address_line_1": "",
            "address_line_2": "",
            "city": faker.name(),
            "zip_code": "12345",
            "state_id": state_ma.pk,
            "specialty_list": [faker.name()],
            "reason_for_exception": ReasonForExceptionConfig.SOURCED_FROM_GARNER,
            "data_source": SteerageProviderDataSourceConfig.RIBBON,
            "status": SteerageProviderStatuses.ACCEPTED,
        }
        steerage_provider = self.provider_client.post(
            f"/referral/steerage/{steerage_pk}/providers/",
            payload,
            format="json",
        )

        # select this provider for the steerage
        steerage_provider_id = steerage_provider.json()["id"]
        member_selected = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/providers/{steerage_provider_id}/select/",
            format="json",
        )
        self.assertEqual(member_selected.status_code, 200)
        self.assertEqual(member_selected.json()["member_selected_at"], date.today().strftime("%Y-%m-%d"))

        # Assert does not have an effective through date
        self.assertIsNone(steerage.effective_through)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked={True}", format="json"
        )

        # Assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        self.assertEqual(locked_steerage.json()["locked_by"], self.provider.id)
        self.assertNotEqual(locked_steerage.json()["locked_at"], None)

        # Since this is the first lock, assert "recommendation_sent_at"
        steerage.refresh_from_db()
        initial_locked_at = steerage.recommendation_sent_at
        self.assertIsNotNone(steerage.recommendation_sent_at)
        self.assertEqual(steerage.recommendation_sent_at, steerage.locked_at)

        # Set the effective through date
        initial_effective_through = steerage.effective_through
        self.assertIsNotNone(steerage.effective_through)
        self.assertEqual(steerage.effective_through, steerage.recommendation_sent_at.date() + timedelta(days=365))

        # Since we have an accepted provider, self service should be disabled
        self.assertFalse(steerage.is_self_service_enabled)

        # Unlock the steerage
        unlocked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked={False}", format="json"
        )
        self.assertEqual(unlocked_steerage.json()["is_locked"], False)
        self.assertEqual(unlocked_steerage.json()["locked_at"], None)
        self.assertEqual(unlocked_steerage.json()["locked_by"], None)

        # Assert the recommendation_sent_at and effective_through column are unaffected
        steerage.refresh_from_db()
        self.assertEqual(steerage.recommendation_sent_at, initial_locked_at)
        self.assertEqual(steerage.effective_through, initial_effective_through)

        # assert if the provider still have the member selected date
        steerage_provider = self.provider_client.get(
            f"/referral/steerage/{steerage_pk}/provider/{steerage_provider_id}/",
            format="json",
        )
        self.assertEqual(
            steerage_provider.json()["member_selected_at"],
            date.today().strftime("%Y-%m-%d"),
        )

        # Assert an invalid request fails
        response_for_invalid_request = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked=RandomValue", format="json"
        )
        self.assertEqual(response_for_invalid_request.status_code, 400)

    def test_steerage_lock_without_providers_added(self, *args):
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # first create a steerage
        steerage_person = PersonUserFactory.create()
        #  Create a eligible service category for Self Serve
        service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])
        # Create waiver from this service
        waiver = WaiverFactory()
        waiver.service_categories.add(service_category)
        waiver.save()
        steerage: Steerage = SteerageFactory(
            person=steerage_person, effective_from=None, effective_through=None, waiver=waiver
        )
        steerage_pk = steerage.id

        # Now add a provider under review to the steerage for testing the retain of member selected provider
        state_ma, _ = State.objects.get_or_create(
            abbreviation="MA", defaults={"name": "Massachusetts", "can_service": True}
        )
        faker: Faker = Faker()
        payload = {
            "first_name": faker.name(),
            "last_name": faker.name(),
            "npi": uuid.uuid1().hex[:10],
            "care_organization_name": faker.name(),
            "street_address": faker.name(),
            "address_line_1": "",
            "address_line_2": "",
            "city": faker.name(),
            "zip_code": "12345",
            "state_id": state_ma.pk,
            "specialty_list": [faker.name()],
            "reason_for_exception": ReasonForExceptionConfig.SOURCED_FROM_GARNER,
            "data_source": SteerageProviderDataSourceConfig.RIBBON,
        }

        # Create two providers
        self.provider_client.post(
            f"/referral/steerage/{steerage_pk}/providers/",
            payload,
            format="json",
        )
        self.provider_client.post(
            f"/referral/steerage/{steerage_pk}/providers/",
            payload,
            format="json",
        )

        # Since the provider is being added from the API, we override the status to Accepted, so let's just manually
        # set the status here
        for provider in steerage.steerage_providers.all():
            provider.status = SteerageProviderStatuses.UNDER_REVIEW
            provider.save()

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_pk}/toggle_lock/?is_locked={True}", format="json"
        )

        # Assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        self.assertEqual(locked_steerage.json()["locked_by"], self.provider.id)
        self.assertNotEqual(locked_steerage.json()["locked_at"], None)

        # Since this is the first lock, assert "recommendation_sent_at"
        steerage.refresh_from_db()
        self.assertIsNotNone(steerage.recommendation_sent_at)
        self.assertEqual(steerage.recommendation_sent_at, steerage.locked_at)

        # Confirm that self service is enabled since we have no accepted providers
        self.assertTrue(steerage.is_self_service_enabled)

    def test_specialty_or_location_save(self, *args):
        steerage = SteerageFactory()
        # Use class-level specialty group and location type
        specialty_group = self.__class__.specialty_group
        location_type = self.__class__.location_type

        payload = {"specialty_group": specialty_group.id}
        result = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        self.assertEqual(result.json()["specialty_group"], payload["specialty_group"])

        # Note that in the UI, we blank out the Specialty Group when we save a Location Type,
        # and vice versa. However, we're not explicitly making this one or the other in the backend.
        payload = {"location_type": location_type.id}
        result = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )
        self.assertEqual(result.json()["location_type"], payload["location_type"])

    def test_steerage_services_and_clinical_focus_areas_update(self, *args):
        # Case 1 - The specialty group is mapped to Clinical Focus Area added
        # on the steerage - it should save the focus area
        # Use class-level objects
        specialty_group = self.__class__.specialty_group
        banana_service = self.__class__.banana_service
        banana_clinical_focus_area = self.__class__.banana_clinical_focus_area

        # Create the mappings for this test
        ServiceSpecialtyGroupLocationTypeFactory.create(specialty_group=specialty_group, service=banana_service)
        ClinicalFocusAreaSpecialtyFactory.create(
            specialty_group=specialty_group, clinical_focus_area=banana_clinical_focus_area
        )
        steerage: Steerage = SteerageFactory.create(specialty_group=specialty_group)

        services = steerage.services.all()
        self.assertEqual(services.count(), 0)

        clinical_focus_areas = steerage.clinical_focus_areas.all()
        self.assertEqual(clinical_focus_areas.count(), 0)

        # Save a Service with the services mapped to the steerage specialty_group
        payload = {"service_ids": [banana_service.id]}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()

        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        # Assert that the service have "category" and "description"
        for service in services_saved:
            self.assertEqual(service["description"], banana_service.description)
            self.assertEqual(service["category"]["description"], banana_service.category.description)
        self.assertEqual(len(services_saved), 1)
        self.assertEqual(services_saved[0].get("id"), banana_service.id)

        # Save a Service and Clinical Focus Area with the services mapped to the steerage specialty_group
        payload = {"service_ids": [banana_service.id], "clinical_focus_area_ids": [banana_clinical_focus_area.id]}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()

        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        # Assert that the service have "category" and "description"
        for service in services_saved:
            self.assertEqual(service["description"], banana_service.description)
            self.assertEqual(service["category"]["description"], banana_service.category.description)
        self.assertEqual(len(services_saved), 1)
        self.assertEqual(services_saved[0].get("id"), banana_service.id)

        clinical_focus_areas_saved = response_body["clinical_focus_areas"]
        # Assert that the clinical focus area is present
        self.assertEqual(clinical_focus_areas_saved[0].get("label"), banana_clinical_focus_area.label)
        self.assertEqual(len(clinical_focus_areas_saved), 1)
        self.assertEqual(clinical_focus_areas_saved[0].get("id"), banana_clinical_focus_area.id)

        # Case 2 - Updating the service to remove banana and add in cat with valid specialty mapping, similarly
        # remove banana focus area and add in cat focus area
        cat_service: Service = ServiceFactory.create(description="cat")
        cat_clinical_focus_area: ClinicalFocusArea = ClinicalFocusAreaFactory.create(label="cat")
        ServiceSpecialtyGroupLocationTypeFactory.create(specialty_group=specialty_group, service=cat_service)
        ClinicalFocusAreaSpecialtyFactory.create(
            specialty_group=specialty_group, clinical_focus_area=cat_clinical_focus_area
        )
        payload = {
            "service_ids": [cat_service.id],
            "clinical_focus_area_ids": [cat_clinical_focus_area.id],
        }

        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()

        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        self.assertEqual(len(services_saved), 1)
        self.assertEqual(services_saved[0].get("id"), cat_service.id)

        clinical_focus_areas_saved = response_body["clinical_focus_areas"]
        self.assertEqual(len(clinical_focus_areas_saved), 1)
        self.assertEqual(clinical_focus_areas_saved[0].get("id"), cat_clinical_focus_area.id)

        # Case 3 - Saving Service without a specialty_group mapping - should not save specialty
        dog_service: Service = ServiceFactory.create(description="dog")
        payload = {
            "service_ids": [dog_service.id],
        }
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()
        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        self.assertEqual(len(services_saved), 0)

        # Case 4 - Saving Clinical Focus Area without a specialty_group mapping - should not save specialty
        dog_clinical_focus_area: ClinicalFocusArea = ClinicalFocusAreaFactory.create(label="dog")
        payload = {
            "clinical_focus_area_ids": [dog_clinical_focus_area.id],
        }
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()
        self.assertEqual(response.status_code, 200)
        dog_clinical_focus_areas_saved = response_body["clinical_focus_areas"]
        self.assertEqual(len(dog_clinical_focus_areas_saved), 0)

        # Case 5 - Updating the service should not depend on is_last_mile
        cat_service: Service = ServiceFactory.create(description="cat")
        ServiceSpecialtyGroupLocationTypeFactory.create(specialty_group=specialty_group, service=cat_service)
        payload = {
            "service_ids": [cat_service.id],
            "referral": {"is_last_mile": False},
        }

        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()

        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        self.assertEqual(len(services_saved), 1)
        self.assertEqual(services_saved[0].get("id"), cat_service.id)

    @override_switch(WAFFLE_SWITCH_PARTNERSHIP_TYPE_FILTER, True)
    def test_steerage_remote_test_kit_partnership(self, *args):
        # Setup the person
        # This setup is for testing entire system suggested partners flow
        person: Person = self.patient.person
        person.insurance_info.latitude = 40.7128
        person.insurance_info.longitude = -74.0060
        person.insurance_info.zipcode = 10007
        self.state_ma, _ = State.objects.get_or_create(abbreviation="MA")
        self.state_ca, _ = State.objects.get_or_create(abbreviation="CA")
        self.state_ny, _ = State.objects.get_or_create(abbreviation="NY")
        person.insurance_info.state = self.state_ma.abbreviation
        person.insurance_info.plan_type = InsuranceMemberInfo.PLAN_TYPE_HMO
        person.insurance_info.save(update_fields=["latitude", "longitude", "zipcode", "state", "plan_type"])
        # Add contract to member
        person.employer = None
        person.save(update_fields=["employer"])
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        person.eligible()
        insurance_member_info: InsuranceMemberInfo = person.insurance_info
        insurance_member_info.insurance_plan.firefly_accepted = True
        insurance_member_info.insurance_plan.save()
        pcp_selection_contract = ContractFactory(
            config={
                "payer_group": "Anthem/BCBS",
                "payer_id": insurance_member_info.insurance_payer.id,
                "plan_description_specific": True,
                "accepts_out_of_state_payers": True,
                "attribution_type": ContractAttributionType.PCP_SELECTION,
                "insurance_state": "MA",
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
                "is_attribution_tracked_externally": True,
            },
            contracted_entity_content_type=ContentType.objects.get_for_model(InsurancePayer),
            contracted_entity=insurance_member_info.insurance_payer,
        )
        insurance_member_info.is_firefly_pcp = True
        insurance_member_info.firefly_pcp_at = datetime.datetime.now()
        insurance_member_info.save()
        person.attribution.contract = pcp_selection_contract
        person.attribution.save()

        # Setup a partner
        # 3. Create third Partnership, which is valid for all zipcodes
        curated_provider = CuratedProviderFactory.create(
            latitude=40.7401,
            longitude=-73.9903,
            zip_code=person.insurance_info.zipcode,
            provider_type=CuratedProviderType.AT_HOME_TEST_KIT,
        )  # Midtown Manhattan
        partnership: Partnership = PartnershipFactory.create(
            partner_name="QT Medical",
            can_accept_care_members=True,
            can_accept_coverage_members=True,
            can_accept_care_and_coverage_members=True,
            is_valid_for_all_zipcodes=True,
            agreement_type=AgreementTypeConfig.DIRECT_CONTRACT,
            partnership_type=PartnershipType.TEST_KIT,
        )
        CuratedProviderPartnershipFactory(curated_provider=curated_provider, partnership=partnership)

        # Case 1 - The location type is mapped to services and test kit partnership type and there is a partnership
        # available with the selected service
        location_type = LocationTypeFactory.create(label=LocationTypes.REMOTE_TESTING)
        banana_service: Service = ServiceFactory.create(description="banana")
        ServiceSpecialtyGroupLocationTypeFactory.create(location_type=location_type, service=banana_service)
        LocationTypePartnershipType.objects.create(
            location_type=location_type, partnership_type=PartnershipType.TEST_KIT
        )
        PartnershipServiceFactory(partnership=partnership, service=banana_service)

        steerage: Steerage = SteerageFactory.create(person=person, type_of_visit=SteerageTypeOfVisit.ANY)

        services = steerage.services.all()
        self.assertEqual(services.count(), 0)

        # Save a Service with the services mapped to the steerage specialty_group. Since there is a test kit partnership
        # for the selected service, we're good and the steerage should save with no errors.
        payload = {"location_type": location_type.id, "service_ids": [banana_service.id]}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()

        self.assertEqual(response.status_code, 200)
        services_saved = response_body["services"]
        # Assert that the service have "category" and "description"
        for service in services_saved:
            self.assertEqual(service["description"], banana_service.description)
            self.assertEqual(service["category"]["description"], banana_service.category.description)
        self.assertEqual(len(services_saved), 1)
        self.assertEqual(services_saved[0].get("id"), banana_service.id)

        # Case 2 - There is no partnership available for the selected service
        banana_bad_service: Service = ServiceFactory.create(description="banana_bad")
        ServiceSpecialtyGroupLocationTypeFactory.create(location_type=location_type, service=banana_bad_service)

        steerage: Steerage = SteerageFactory.create(person=person, type_of_visit=SteerageTypeOfVisit.ANY)

        services = steerage.services.all()
        self.assertEqual(services.count(), 0)

        # Save a Service with the services mapped to the steerage specialty_group. Since there is a test kit partnership
        # for the selected service, we're good and the steerage should save with no errors.
        payload = {"location_type": location_type.id, "service_ids": [banana_bad_service.id]}
        response = self.provider_client.patch(
            f"/referral/steerage/{steerage.id}/",
            payload,
            format="json",
        )

        response_body = response.json()
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_body[0], "Try another specialty/service type, no partners found.")

        # Confirm nothing on the steerage was saved
        steerage.refresh_from_db()
        self.assertIsNone(steerage.location_type)

    def test_steerage_archive_at(self, *args):
        # Case 1
        #   Create a new steerage without effective_through and scheduling date
        # Result
        #   archive_at should be none
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group
        referral_person = self.patient.person
        steerage = SteerageFactory(person=referral_person, effective_through=None, scheduling_date=None, waiver=None)
        self.assertIsNone(steerage.archive_at)
        self.assertIsNone(steerage.scheduling_date)
        self.assertIsNone(steerage.effective_through)

        # Case 2
        #   Update steerage with effective_through date and scheduling date as None
        # Result
        #   archive_at should be Tomorrow date
        steerage.effective_through = date.today()
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=1))
        self.assertEqual(steerage.effective_through, date.today())
        self.assertIsNone(steerage.scheduling_date)

        # Case 3
        #   Update steerage with effective_through date as None and scheduling date as today's date
        # Result
        #   archive_at should be Tomorrow date
        steerage.scheduling_date = date.today()
        steerage.effective_through = None
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=1))
        self.assertEqual(steerage.effective_through, None)
        self.assertEqual(steerage.scheduling_date, date.today())

        # Case 4
        #   Update steerage with effective_through date as (today+10days) and scheduling date as (today+15days)
        # Result
        #   archive_at should be scheduling date +1 day i.e 16 days from now
        steerage.effective_through = date.today() + timedelta(days=10)
        steerage.scheduling_date = date.today() + timedelta(days=15)
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=16))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=10))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # Case 5
        #   Update steerage with effective_through date as (today+25days) and scheduling date as (today+15days)
        # Result
        #   archive_at should be scheduling date +1 day i.e 16 days from now
        steerage.effective_through = date.today() + timedelta(days=25)
        steerage.scheduling_date = date.today() + timedelta(days=15)
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=16))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # Case 6
        #   Add a service quantity with value >1 to the steerage, also check if waiver
        # Result
        #   archive_at should be effective date +1 day i.e 26 days from now

        # Without a waiver, even if there is a service quantity, we will set it to the scheduling date
        steerage.effective_through = date.today() + timedelta(days=25)
        steerage.scheduling_date = date.today() + timedelta(days=15)
        SteerageServiceQuantityFactory(steerage=steerage, value=2)
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=16))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # Let's add a waiver and check again
        waiver = WaiverFactory(is_active=True)
        steerage.waiver = waiver
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=26))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # Case 7
        #   Add a service quantity with value <=1 to the steerage (waiver present)
        # Result
        #   archive_at should be scheduling date +1 day i.e 16 days from now
        steerage.effective_through = date.today() + timedelta(days=25)
        steerage.scheduling_date = date.today() + timedelta(days=15)
        steerage.service_quantity.value = 1
        steerage.service_quantity.save()
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=16))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # Case 8
        #   Null types for steerage quantity with waiver
        # Result
        #   archive_at should be effective date +1 day i.e 26 days from now
        steerage.effective_through = date.today() + timedelta(days=25)
        steerage.scheduling_date = date.today() + timedelta(days=15)
        steerage.service_quantity.value = None
        steerage.service_quantity.save()
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=26))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        steerage.service_quantity.delete()
        steerage.save()
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=26))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=15))

        # For the remainder of the cases lets remove the waiver
        waiver.is_active = False
        waiver.save()

        # Case 9
        #  Clear scheduling date as a member
        # Result
        #   archive_at should be effective date +1 day i.e 25+1=26 days from now
        steerage_id = steerage.id
        scheduling_date = self.client.patch(
            f"/referral/steerage/{steerage_id}/schedule/",
            {"scheduling_date": None},
            format="json",
        )
        self.assertEqual(scheduling_date.status_code, 200)
        # get steerage and validate
        steerage = Steerage.objects.get(id=steerage_id)
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=26))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, None)

        # Case 10
        #  update scheduling date to 10 days from now as a member
        # Result
        #   archive_at should be scheduling date +1 day i.e 10+1=11 days from now
        steerage_id = steerage.id
        scheduling_date = self.client.patch(
            f"/referral/steerage/{steerage_id}/schedule/",
            {"scheduling_date": date.today() + timedelta(days=10)},
            format="json",
        )
        self.assertEqual(scheduling_date.status_code, 200)
        # get steerage and validate
        steerage = Steerage.objects.get(id=steerage_id)
        self.assertEqual(steerage.archive_at, date.today() + timedelta(days=11))
        self.assertEqual(steerage.effective_through, date.today() + timedelta(days=25))
        self.assertEqual(steerage.scheduling_date, date.today() + timedelta(days=10))

        # Case 11
        #  Changing the steerage disposition to steerage not needed
        # Result
        #   archive_at should be set to yesterday

        # Set archive_at as null
        steerage.effective_through = None
        steerage.scheduling_date = None
        steerage.save()

        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

        # create steerage dispositions
        SteerageDisposition.objects.get_or_create(
            matches_case_status_name=STEERAGE_NOT_NEEDED_ACTION, follow_through_success=SteerageSuccessConfig.EXCLUDED
        )
        SteerageDisposition.objects.get_or_create(
            matches_case_status_name=CARE_NOT_NEEDED_ACTION, follow_through_success=SteerageSuccessConfig.EXCLUDED
        )
        SteerageDisposition.objects.get_or_create(
            matches_case_status_name=RESTEERED_ACTION, follow_through_success=SteerageSuccessConfig.EXCLUDED
        )
        SteerageDisposition.objects.get_or_create(
            matches_case_status_name=MEMBER_DISCHARGED_ACTION, follow_through_success=SteerageSuccessConfig.EXCLUDED
        )

        # Update disposition on steerage
        steerage_not_needed_disposition: SteerageDisposition = SteerageDisposition.objects.get(
            matches_case_status_name=STEERAGE_NOT_NEEDED_ACTION,
        )
        steerage.disposition = steerage_not_needed_disposition
        steerage.save()

        # Saving disposition should update the archive date to past date
        steerage.refresh_from_db()
        self.assertEqual(steerage.archive_at, date.today() - timedelta(days=1))

        # Case 12
        #  Changing the steerage disposition to care not needed
        # Result
        #   archive_at should be set to yesterday

        # Set archive_at as null
        steerage.effective_through = None
        steerage.scheduling_date = None
        steerage.disposition = None
        steerage.save()

        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

        # Update disposition on steerage
        care_not_needed_disposition: SteerageDisposition = SteerageDisposition.objects.get(
            matches_case_status_name=CARE_NOT_NEEDED_ACTION,
        )
        steerage.disposition = care_not_needed_disposition
        steerage.save()

        # Saving disposition should update the archive date to past date
        steerage.refresh_from_db()
        self.assertEqual(steerage.archive_at, date.today() - timedelta(days=1))

        # Case 13
        #  Changing the steerage disposition to resteered
        # Result
        #   archive_at should be set to yesterday

        # Set archive_at as null
        steerage.effective_through = None
        steerage.scheduling_date = None
        steerage.disposition = None
        steerage.save()

        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

        # Update disposition on steerage
        resteered_disposition: SteerageDisposition = SteerageDisposition.objects.get(
            matches_case_status_name=RESTEERED_ACTION,
        )
        steerage.disposition = resteered_disposition
        steerage.save()

        # Saving disposition should update the archive date to past date
        steerage.refresh_from_db()
        self.assertEqual(steerage.archive_at, date.today() - timedelta(days=1))

        # Case 14
        #  Changing the steerage disposition to Member Discharged
        # Result
        #   archive_at should be set to yesterday

        # Set archive_at as null
        steerage.effective_through = None
        steerage.scheduling_date = None
        steerage.disposition = None
        steerage.save()

        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

        # Update disposition on steerage
        member_discharge_disposition: SteerageDisposition = SteerageDisposition.objects.get(
            matches_case_status_name=MEMBER_DISCHARGED_ACTION,
        )
        steerage.disposition = member_discharge_disposition
        steerage.save()

        # Saving disposition should update the archive date to past date
        steerage.refresh_from_db()
        self.assertEqual(steerage.archive_at, date.today() - timedelta(days=1))

        # Case 15
        #  Removing disposition
        # Result
        #   archive_at should be null

        # Set archive_at as null
        steerage.effective_through = None
        steerage.scheduling_date = None
        steerage.disposition = None
        steerage.save()

        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

        # Update disposition on steerage
        steerage.disposition = None
        steerage.save()

        # Saving disposition should remove archive date
        steerage.refresh_from_db()
        self.assertIsNone(steerage.archive_at)

    def test_store_curated_provider_on_steerage_lock(self):
        # Scenario 1
        #   Create a steerage, add a steerage provider without specialty group and lock it
        # Result
        #   Should lock the steerage and create curated provider and contact information
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Start with creating a steerage
        steerage_person = PersonUserFactory.create()
        tomorrow = date.today() + timedelta(days=1)
        steerage = SteerageFactory(
            person=steerage_person,
            effective_from=tomorrow,
            effective_through=tomorrow + timedelta(days=1),
        )

        steerage_id = steerage.id

        # Now add a provider to the steerage
        steerage_provider = SteerageProviderFactory.create(steerage=steerage)
        self.assertEqual(CuratedProvider.objects.count(), 0)
        self.assertEqual(ContactInformation.objects.count(), 0)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/toggle_lock/?is_locked={True}", format="json"
        )

        # assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        curate_provider: CuratedProvider = CuratedProvider.objects.first()
        contact_information: ContactInformation = ContactInformation.objects.first()

        # assert the curated provider
        self.assertEqual(steerage_provider.npi, curate_provider.npi)
        self.assertEqual(steerage_provider.care_organization_name, curate_provider.care_org_name)
        self.assertEqual(steerage_provider.address_line_1, curate_provider.address_line_1)
        self.assertEqual(steerage_provider.address_line_2, curate_provider.address_line_2)
        self.assertEqual(steerage_provider.city, curate_provider.city)
        self.assertEqual(steerage_provider.state, curate_provider.state)
        self.assertEqual(steerage_provider.zip_code, curate_provider.zip_code)
        self.assertEqual(steerage_provider.phone, contact_information.phone)
        self.assertEqual(steerage_provider.fax, contact_information.fax)
        self.assertEqual(steerage_provider.specialty_group, None)
        self.assertEqual(contact_information.is_verified, True)

        # Scenario 2
        #   Add one more steerage provider and lock it
        # Result
        #   Should lock the steerage, curated provider and contact information

        # Add another provider to the steerage with specialty group
        steerage_provider_2 = SteerageProviderFactory.create(
            steerage=steerage,
        )

        self.assertEqual(CuratedProvider.objects.count(), 1)
        self.assertEqual(ContactInformation.objects.count(), 1)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/toggle_lock/?is_locked={True}", format="json"
        )

        # assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)
        curate_provider: CuratedProvider = CuratedProvider.objects.last()
        contact_information: ContactInformation = ContactInformation.objects.last()

        # assert the curated provider
        self.assertEqual(steerage_provider_2.npi, curate_provider.npi)
        self.assertEqual(steerage_provider_2.care_organization_name, curate_provider.care_org_name)
        self.assertEqual(steerage_provider_2.address_line_1, curate_provider.address_line_1)
        self.assertEqual(steerage_provider_2.address_line_2, curate_provider.address_line_2)
        self.assertEqual(steerage_provider_2.city, curate_provider.city)
        self.assertEqual(steerage_provider_2.state, curate_provider.state)
        self.assertEqual(steerage_provider_2.zip_code, curate_provider.zip_code)
        self.assertEqual(steerage_provider_2.phone, contact_information.phone)
        self.assertEqual(steerage_provider_2.fax, contact_information.fax)
        self.assertEqual(steerage_provider_2.specialty_group, None)
        self.assertEqual(contact_information.is_verified, True)

    def test_store_curated_provider_on_steerage_lock_without_zipcode(self):
        # Scenario 1
        #   Create a steerage, add a steerage provider without zipcode and lock it
        # Result
        #   Should lock the steerage but should not create curated provider and contact information
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Start with creating a steerage
        steerage_person = PersonUserFactory.create()
        tomorrow = date.today() + timedelta(days=1)
        steerage = SteerageFactory(
            person=steerage_person,
            effective_from=tomorrow,
            effective_through=tomorrow + timedelta(days=1),
        )

        steerage_id = steerage.id

        # Now add a provider to the steerage
        SteerageProviderFactory.create(steerage=steerage, zip_code=None)
        self.assertEqual(CuratedProvider.objects.count(), 0)
        self.assertEqual(ContactInformation.objects.count(), 0)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/toggle_lock/?is_locked={True}", format="json"
        )

        # assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)

        # assert the curated provider
        self.assertEqual(CuratedProvider.objects.count(), 0)
        self.assertEqual(ContactInformation.objects.count(), 0)

    def test_store_curated_provider_on_steerage_lock_without_care_org(self):
        # Scenario 1
        #   Create a steerage, add a steerage provider without care_org_name and lock it
        # Result
        #   Should lock the steerage but should not create curated provider and contact information
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Start with creating a steerage
        steerage_person = PersonUserFactory.create()
        tomorrow = date.today() + timedelta(days=1)
        steerage = SteerageFactory(
            person=steerage_person,
            effective_from=tomorrow,
            effective_through=tomorrow + timedelta(days=1),
        )

        steerage_id = steerage.id

        # Now add a provider to the steerage
        steerage_provider = SteerageProviderFactory.create(steerage=steerage, care_organization_name=None)
        self.assertEqual(CuratedProvider.objects.count(), 0)
        self.assertEqual(ContactInformation.objects.count(), 0)

        # Lock the steerage
        locked_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/toggle_lock/?is_locked={True}", format="json"
        )

        # assert it with locked as true and not null locked_by and locked_at
        self.assertEqual(locked_steerage.json()["is_locked"], True)

        # assert the curated provider
        self.assertEqual(CuratedProvider.objects.count(), 1)
        self.assertEqual(ContactInformation.objects.count(), 1)

        curate_provider: CuratedProvider = CuratedProvider.objects.first()
        self.assertEqual(steerage_provider.npi, curate_provider.npi)
        self.assertEqual(steerage_provider.zip_code, curate_provider.zip_code)
        self.assertEqual(CuratedProviderCareOrgNameChoice.UNKNOWN, curate_provider.care_org_name)
        self.assertEqual(CuratedProviderCareOrgNameChoice.UNKNOWN.lower(), curate_provider.sanitized_care_org_name)

    def test_steerage_segment(self, *args):
        # Scenario 0
        #   Create a steerage with priority as Urgent and specialty group as HIGH_TCOC_SEGMENT_SPECIALTY
        # Result
        #   The steerage should be segmented as High TCoc
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.URGENT)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.HIGH_TCOC)

        # Scenario 1
        #   Create a steerage with priority as Urgent and specialty group as LOW TCoC
        # Result
        #   The steerage should be segmented as High TCoc
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create()
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.URGENT)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.HIGH_TCOC)

        # Scenario 2
        #   Create a steerage with priority as Medium and specialty group as HIGH_TCOC_SEGMENT_SPECIALTY
        # Result
        #   The steerage should be segmented as High TCoc
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[1])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.HIGH_TCOC)

        # Scenario 3
        #   Create a steerage with priority as Medium and specialty group as Radiology
        # Result
        #   The steerage should be segmented as commodity
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=COMMODITY_SEGMENT_SPECIALTY[0])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.COMMODITY)

        # Scenario 4
        #   Create a steerage with priority as Urgent and specialty group as Radiology
        # Result
        #   The steerage should be segmented as commodity
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=COMMODITY_SEGMENT_SPECIALTY[0])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.URGENT)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.COMMODITY)

        # Scenario 5
        #   Create a steerage with priority as Urgent and location type as Imaging Center
        # Result
        #   The steerage should be segmented as commodity
        location_type: LocationType = LocationTypeFactory.create(label=COMMODITY_SEGMENT_LOCATION_TYPES[3])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.URGENT)
        steerage.location_type = location_type
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.COMMODITY)

        # Scenario 6
        #   Create a steerage with priority as medium and location type as a Lab
        # Result
        #   The steerage should be segmented as commodity
        location_type: LocationType = LocationTypeFactory.create(label=COMMODITY_SEGMENT_LOCATION_TYPES[0])
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.MEDIUM)
        steerage.location_type = location_type
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.COMMODITY)

        # Scenario 7
        #   Create a steerage with priority as standard with Low TCoC specialty group
        # Result
        #   The steerage should be segmented as Low TCoC
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create()
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.STANDARD)
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.LOW_TCOC)

        # Scenario 8
        #   Create a steerage with priority as urgent with a location type
        # Result
        #   The steerage should be segmented as High TCoC
        location_type: LocationType = LocationTypeFactory.create()
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.URGENT)
        steerage.location_type = location_type
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.HIGH_TCOC)

        # Scenario 9
        #   Create a steerage with priority as standard with a location type
        # Result
        #   The steerage should be segmented as Low TCoC
        location_type: LocationType = LocationTypeFactory.create()
        steerage: Steerage = SteerageFactory(priority=ReferralPriority.STANDARD)
        steerage.location_type = location_type
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.LOW_TCOC)

        # Scenario 10
        #  Updating the specialty/location_type for same steerage
        # Result
        #   Should not set the segment to None
        steerage.location_type = None
        steerage.specialty_group = None
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.segment, SteerageSegment.LOW_TCOC)

    def test_steerage_save(self):
        steerage = SteerageFactory()
        steerage_id = steerage.id
        faker: Faker = Faker()

        # Update the steerage with notify_clinician_yn as false
        payload = {
            "notify_clinician_yn": False,
            "priority": ReferralPriority.MEDIUM,
            "requested_by_member": True,
            "preferred_provider_or_facility": faker.text(),
        }
        updated_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(updated_steerage.status_code, 200)
        self.assertEqual(updated_steerage.json()["notify_clinician_yn"], payload["notify_clinician_yn"])
        self.assertEqual(updated_steerage.json()["priority"], ReferralPriority.MEDIUM)
        self.assertEqual(updated_steerage.json()["requested_by_member"], payload["requested_by_member"])
        self.assertEqual(
            updated_steerage.json()["preferred_provider_or_facility"], payload["preferred_provider_or_facility"]
        )

        # Update the steerage with notify_clinician_yn as True
        payload = {
            "notify_clinician_yn": True,
            "priority": ReferralPriority.URGENT,
            "requested_by_member": False,
            "preferred_provider_or_facility": faker.text(),
        }
        updated_steerage = self.provider_client.patch(
            f"/referral/steerage/{steerage_id}/",
            payload,
            format="json",
        )
        self.assertEqual(updated_steerage.json()["notify_clinician_yn"], payload["notify_clinician_yn"])
        self.assertEqual(updated_steerage.json()["priority"], ReferralPriority.URGENT)
        self.assertEqual(updated_steerage.json()["requested_by_member"], payload["requested_by_member"])
        self.assertEqual(
            updated_steerage.json()["preferred_provider_or_facility"], payload["preferred_provider_or_facility"]
        )

    def test_steerage_has_elation_referral_letter_or_order(self, *args):
        # Scenario 1
        #   Create a steerage which have a referral and referral order object
        # Result
        #   The has_elation_referral_letter_or_order should be True
        referral = ReferralFactory.create()
        ElationReferralOrderFactory.create(referral=referral)
        steerage: Steerage = SteerageFactory(referral=referral)
        steerage.refresh_from_db()
        self.assertEqual(steerage.was_elation_letter_created_from_lucian, False)
        self.assertEqual(steerage.has_elation_referral_letter_or_order, True)

        # Scenario 2
        #   Create a steerage which have a alias mapping b/w referral and ElationReferralLetter
        #   but doesn't have Elation referral order
        # Result
        #   The has_elation_referral_letter_or_order should be True

        # Create a referral elation letter and referral for the steerage
        referral = ReferralFactory.create()
        steerage: Steerage = SteerageFactory(referral=referral)
        elation_referral_letter = create_mock_elation_referral_letter()
        response = self.provider_client.post(
            "/referral/letter/subscription/callback/",
            elation_referral_letter,
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        elation_referral_letter = ElationReferralLetter.objects.first()

        # create alias mapping b/w Elation referral letter and referral
        AliasMapping.objects.create(
            content_type=get_content_type(Steerage),
            alias_name=AliasName.ELATION_REFERRAL_LETTER,
            alias_id=elation_referral_letter,
            object_id=steerage.id,
        )
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.elation_referral_orders.count(), 0)
        self.assertEqual(steerage.was_elation_letter_created_from_lucian, True)
        self.assertEqual(steerage.has_elation_referral_letter_or_order, True)

        # Scenario 3
        #   Create a steerage without a elation referral order and referral letter object
        # Result
        #   The has_elation_referral_letter_or_order should be False
        referral = ReferralFactory.create()
        steerage: Steerage = SteerageFactory(referral=referral)
        steerage.refresh_from_db()
        self.assertEqual(steerage.was_elation_letter_created_from_lucian, False)
        self.assertEqual(steerage.has_elation_referral_letter_or_order, False)

    def test_set_insurance_auth_toggle_on_segmentation(self):
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Case 1
        # Test situations that should NOT produce a insurnace case.
        # A Referral with Steerage but plan type as PPO
        steerage_person: Person = PersonUserFactory.create()
        steerage_person.insurance_info = InsuranceMemberInfoFactory.create(
            plan_type=InsuranceMemberInfo.PLAN_TYPE_PPO.lower()
        )
        steerage_person.save()
        steerage: Steerage = SteerageFactory(person=steerage_person, scheduling_date=None)
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.insurance_authorization_required, None)

        # Updating the segment will not create a case
        steerage.segment = SteerageSegment.COMMODITY
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.insurance_authorization_required, None)

        # Case 2
        # Test situations that will produce a insurnace case.
        # A Referral with Steerage and plan type as HMO
        steerage_person: Person = PersonUserFactory.create()
        steerage_person.insurance_info = InsuranceMemberInfoFactory.create(
            plan_type=InsuranceMemberInfo.PLAN_TYPE_HMO.lower()
        )
        steerage_person.save()
        steerage: Steerage = SteerageFactory(person=steerage_person, scheduling_date=None)
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.insurance_authorization_required, None)

        # Updating the segment will create a case
        steerage.segment = SteerageSegment.COMMODITY
        steerage.save()
        steerage.refresh_from_db()
        self.assertEqual(steerage.referral.insurance_authorization_required, True)

        # If we manually set the insurance_authorization_required to False manually, it should remain False
        steerage.referral.insurance_authorization_required = False
        steerage.referral.save()
        self.assertEqual(steerage.referral.insurance_authorization_required, False)

    def test_steerage_status(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group
        # Create tags for the statuses
        for status in SteerageStatus.values:
            sanitized_status = status.lower().replace(" ", "_")
            Tag.objects.get_or_create(unique_key=sanitized_status, display_name=status)

        # Create a Steerage
        category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        case = CaseFactory.create(person=self.patient.person, category=category)
        steerage = (
            CaseRelation.objects.filter(
                case=case,
                content_type=ContentType.objects.get_for_model(Steerage),
            )
            .first()
            .content_object
        )

        self.assertEqual(steerage.status, SteerageStatus.REQUEST_RECEIVED)

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        assert_case_tags_match_status(self, steerage)
        # Segment the Steerage
        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage.priority = ReferralPriority.URGENT
        steerage.specialty_group = specialty_group
        steerage.save()
        steerage.refresh_from_db()

        self.assertEqual(steerage.status, SteerageStatus.IN_PROGRESS)
        assert_case_tags_match_status(self, steerage)

        # Lock the Steerage
        steerage.is_locked = True
        steerage.save()
        steerage.refresh_from_db()

        # The status should not change because there are no providers and no scheduling date added
        self.assertEqual(steerage.status, SteerageStatus.IN_PROGRESS)
        assert_case_tags_match_status(self, steerage)

        # Unlock the Steerage
        steerage.is_locked = False
        steerage.save()
        steerage.refresh_from_db()

        self.assertEqual(steerage.status, SteerageStatus.IN_PROGRESS)
        assert_case_tags_match_status(self, steerage)

        # Add a single provider to the Steerage
        # The context manager here isn't *technically* necessary because the save on the Steerage
        # below will cause it to recalculate the status. But it's here for clarity.
        with self.captureOnCommitCallbacks(execute=True):
            provider_1 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)

        # Lock the Steerage
        steerage.is_locked = True
        steerage.save()
        steerage.refresh_from_db()

        # There's a single provider, the Steerage is segmented, and there's no scheduling date,
        # so the status should be "Pending Scheduling"
        self.assertEqual(steerage.status, SteerageStatus.PENDING_SCHEDULING)
        assert_case_tags_match_status(self, steerage)

        with self.captureOnCommitCallbacks(execute=True):
            provider_2 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)

        steerage.refresh_from_db()

        # There are multiple providers but none have been selected, so the status should be
        # "Pending Provider Selection"
        self.assertEqual(steerage.status, SteerageStatus.PENDING_PROVIDER_SELECTION)
        assert_case_tags_match_status(self, steerage)

        # There are multiple providers and one has been selected, so the status should be
        # "Pending Scheduling"
        with self.captureOnCommitCallbacks(execute=True):
            provider_2.member_selected_at = date.today()
            provider_2.save()

        self.assertEqual(steerage.status, SteerageStatus.PENDING_SCHEDULING)
        assert_case_tags_match_status(self, steerage)

        # Delete the providers *without unlocking the Steerage*
        # This isn't a legitimate workflow, but ensures the status is recalculated correctly
        # with self.captureOnCommitCallbacks(execute=True):
        for provider in [provider_1, provider_2]:
            self.provider_client.delete(
                f"/referral/steerage/{steerage.id}/provider/{provider.id}/",
                format="json",
            )
        steerage.refresh_from_db()
        self.assertEqual(steerage.status, SteerageStatus.IN_PROGRESS)
        assert_case_tags_match_status(self, steerage)

        # Add a single provider back to the Steerage
        with self.captureOnCommitCallbacks(execute=True):
            SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)

        steerage.refresh_from_db()

        # It's a single provider, the Steerage is segmented, and there's no scheduling date,
        # so the status should be "Pending Scheduling" again
        self.assertEqual(steerage.status, SteerageStatus.PENDING_SCHEDULING)
        assert_case_tags_match_status(self, steerage)

        # Add a scheduling date to the Steerage
        steerage.scheduling_date = date.today() + timedelta(days=1)
        steerage.save()

        # The status should now be "Scheduled"
        self.assertEqual(steerage.status, SteerageStatus.APPOINTMENT_SCHEDULED)
        assert_case_tags_match_status(self, steerage)

        # Set the scheduling date to None
        steerage.scheduling_date = None
        steerage.save()

        # The status should now be "Pending Scheduling"
        self.assertEqual(steerage.status, SteerageStatus.PENDING_SCHEDULING)
        assert_case_tags_match_status(self, steerage)

        # Unlock and remove a provider
        steerage.is_locked = False
        steerage.save()
        self.provider_client.delete(
            f"/referral/steerage/{steerage.id}/provider/{provider_2.id}/",
            format="json",
        )
        steerage.is_locked = True
        steerage.save()

        # There's now one provider, status should be "Pending Scheduling"
        self.assertEqual(steerage.status, SteerageStatus.PENDING_SCHEDULING)
        assert_case_tags_match_status(self, steerage)

        # Cancel a referral by transitionng a case in the Steerage to "Steerage Not Needed"
        # Transition the case status to "Steerage Not Needed"
        case.action = STEERAGE_NOT_NEEDED_ACTION
        case.save()

        steerage.refresh_from_db()

        self.assertEqual(steerage.status, SteerageStatus.REQUEST_CANCELED)
        assert_case_tags_match_status(self, steerage)

        # Create a brand new steerage that is locked with two recommended providers and no accepted providers,
        # this should update the status to PENDING_PROVIDER_SELECTION since this will be a self service
        # referral
        #  Create a eligible service category for Self Serve
        service_category = ServiceCategoryFactory.create(label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0])
        # Create waiver from this service
        waiver = WaiverFactory()
        waiver.service_categories.add(service_category)
        waiver.save()
        steerage: Steerage = SteerageFactory.create(
            person=self.patient.person,
            priority=ReferralPriority.MEDIUM,
            specialty_group=specialty_group,
            waiver=waiver,
        )
        SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.UNDER_REVIEW,
        )
        SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.UNDER_REVIEW,
        )
        steerage.lock(self.provider)
        steerage.save()
        steerage.refresh_from_db()

        self.assertEqual(steerage.status, SteerageStatus.PENDING_PROVIDER_SELECTION)

    def test_select_steerage_provider_one_provider(self):
        # When only one provider is added to a steerage, when adding a scheduling date, select that provider
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Create a Steerage
        category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        case = CaseFactory.create(person=self.patient.person, category=category)
        steerage = (
            CaseRelation.objects.filter(
                case=case,
                content_type=ContentType.objects.get_for_model(Steerage),
            )
            .first()
            .content_object
        )

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage.priority = ReferralPriority.URGENT
        steerage.specialty_group = specialty_group
        steerage.save()

        with self.captureOnCommitCallbacks(execute=True):
            provider1 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)
            provider2 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.UNDER_REVIEW)
        steerage.scheduling_date = date.today() + timedelta(days=1)
        steerage.save()

        provider1.refresh_from_db()
        provider2.refresh_from_db()
        self.assertIsNotNone(provider1.member_selected_at)
        self.assertIsNone(provider2.member_selected_at)

        # Updating the scheduling date should not change the provider selected at date
        old_selected_at = provider1.member_selected_at
        steerage.scheduling_date = date.today() + timedelta(days=2)
        steerage.save()

        provider1.refresh_from_db()
        self.assertEqual(provider1.member_selected_at, old_selected_at)
        self.assertIsNone(provider2.member_selected_at)

        # Removing the scheduling date should clear the selected at
        steerage.scheduling_date = None
        steerage.save()

        provider1.refresh_from_db()
        self.assertIsNone(provider1.member_selected_at)
        self.assertIsNone(provider2.member_selected_at)

    def test_do_not_select_steerage_provider_multiple_providers(self):
        # When more than one provider is added to a steerage, when adding a scheduling date, DO NOT select that provider
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Create a Steerage
        category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        case = CaseFactory.create(person=self.patient.person, category=category)
        steerage = (
            CaseRelation.objects.filter(
                case=case,
                content_type=ContentType.objects.get_for_model(Steerage),
            )
            .first()
            .content_object
        )

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage.priority = ReferralPriority.URGENT
        steerage.specialty_group = specialty_group
        steerage.save()

        with self.captureOnCommitCallbacks(execute=True):
            provider1 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)
            provider2 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)
        steerage.scheduling_date = date.today() + timedelta(days=1)
        steerage.save()

        provider1.refresh_from_db()
        provider2.refresh_from_db()
        self.assertIsNone(provider1.member_selected_at)
        self.assertIsNone(provider2.member_selected_at)

    @override_switch(WAFFLE_SWITCH_SELF_SERVICE_NAV, True)
    def test_set_case_to_pending_fax_on_steerage_provider_selection(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # Use class-level setup instead of per-test setup
        self.referrals_group_assignee_group = self.__class__.referrals_group_assignee_group

        # Create a Steerage
        category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        case = CaseFactory.create(person=self.patient.person, category=category)
        steerage = (
            CaseRelation.objects.filter(
                case=case,
                content_type=ContentType.objects.get_for_model(Steerage),
            )
            .first()
            .content_object
        )

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage.priority = ReferralPriority.URGENT
        steerage.specialty_group = specialty_group

        referral = ReferralFactory.create(is_active=True)
        steerage.referral = referral
        steerage.save()

        referral_order = ElationReferralOrderFactory.create(referral=referral)
        elation_letter = ElationReferralLetter.objects.create(elation_id=123123123)
        referral_order.elation_referral_letter = elation_letter
        referral_order.save()
        elation_letter.fax_status = None
        elation_letter.save()

        apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.LOCATION_SEARCH)

        with self.captureOnCommitCallbacks(execute=True):
            provider1 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)
            provider2 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)

        steerage.lock(self.provider)

        # If this is not a self service referral, case status should not change
        steerage.is_self_service_enabled = False
        steerage.save()

        with self.captureOnCommitCallbacks(execute=True):
            provider2.member_selected_at = date.today()
            provider2.save()

        case.refresh_from_db()
        self.assertNotEqual(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        # Selecting a provider should update the case status to Pending Fax if self service
        steerage.is_self_service_enabled = True
        steerage.save()
        with self.captureOnCommitCallbacks(execute=True):
            provider2.member_selected_at = None
            provider2.save()
            provider1.member_selected_at = date.today()
            provider1.save()

        case.refresh_from_db()
        self.assertEquals(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        # Let's say we're in another status such as Outreach 1, member selects another provider, we should change again
        with self.captureOnCommitCallbacks(execute=True):
            # Clearing out the selection should move it back to Outreach 1
            provider1.member_selected_at = None
            provider1.save()

            case.refresh_from_db()
            self.assertEquals(case.status, ReferralRequestCaseStatuses.OUTREACH_1)

            # Adding the new selection should pull it to pending fax
            provider2.member_selected_at = date.today()
            provider2.save()

        case.refresh_from_db()
        self.assertEquals(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        # Let's say another provider is added, we should remove member selected at. Let's add a scheduling date,
        # when we clear member selected at, it should remove the scheduling date too
        apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.OUTREACH_1)
        steerage.scheduling_date = date.today()
        steerage.save()
        with self.captureOnCommitCallbacks(execute=True):
            provider3 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)

        # Check all steerage providers to ensure they are not selected
        steerage.refresh_from_db()
        self.assertEquals(steerage.steerage_providers.filter(member_selected_at__isnull=False).count(), 0)
        self.assertEquals(steerage.status, SteerageStatus.PENDING_PROVIDER_SELECTION)
        self.assertIsNone(steerage.scheduling_date)

        # Let's select one
        elation_letter.fax_status = FaxStatus.PENDING
        elation_letter.save()
        with self.captureOnCommitCallbacks(execute=True):
            provider3.member_selected_at = date.today()
            provider3.save()

        case.refresh_from_db()
        self.assertEquals(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        steerage.refresh_from_db()
        self.assertEquals(steerage.status, SteerageStatus.PENDING_SCHEDULING)

        # Let's say we delete the provider, we should remove the member selected at, and pull it to outreach 1
        with self.captureOnCommitCallbacks(execute=True):
            provider3.delete()
            provider2.delete()
        case.refresh_from_db()
        steerage.refresh_from_db()
        self.assertEquals(case.status, ReferralRequestCaseStatuses.OUTREACH_1)
        self.assertIsNone(steerage.scheduling_date)

        # Adding a scheduling date now should select provider 1 since its the only provider,
        # and this should not move the case to Member Scheduled since fax has not been sent yet. So status
        # should be Pending Fax
        steerage.scheduling_date = date.today()
        steerage.save()
        provider1.refresh_from_db()
        case.refresh_from_db()
        self.assertIsNotNone(provider1.member_selected_at)
        self.assertEquals(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        # Let's say fax is now sent. And scheduling date trigger gets called.
        elation_letter.fax_status = FaxStatus.SUCCESS
        elation_letter.save()
        case.refresh_from_db()
        self.assertEquals(case.status, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)

        # If for some reason the status of the fax has changed, and member adds a new
        # scheduling date, we should pull this back
        elation_letter.fax_status = FaxStatus.FAILED
        elation_letter.save()
        steerage.scheduling_date = date.today() + timedelta(days=2)
        steerage.save()
        provider1.refresh_from_db()
        case.refresh_from_db()
        self.assertIsNotNone(provider1.member_selected_at)
        self.assertEquals(case.status, ReferralRequestCaseStatuses.PENDING_FAX)

        # Finally let's create a brand new steerage but add a provider with
        # the member selected at value already, for this one we SHOULD not
        # clear out the member selected at for the new provider but it should
        # for any old ones
        category = CaseCategory.objects.get(unique_key=REFERRAL_REQUEST)
        case = CaseFactory.create(person=self.patient.person, category=category)
        steerage = (
            CaseRelation.objects.filter(
                case=case,
                content_type=ContentType.objects.get_for_model(Steerage),
            )
            .first()
            .content_object
        )

        # Create a referral
        description = "some test description"
        body = "test referral body"
        specialty_group = SpecialtyGroupFactory.create()
        payload = {
            "description": description,
            "referral": {"body": body, "is_active": True},
            "priority": "URGENT",
            "specialty_group": specialty_group.id,
        }
        self.provider_client.patch(
            f"/referral/steerage/{steerage.pk}/?send_to_elation={True}",
            payload,
            format="json",
        )
        steerage.refresh_from_db()

        specialty_group: SpecialtyGroup = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        steerage.priority = ReferralPriority.URGENT
        steerage.specialty_group = specialty_group
        steerage.save()

        apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.LOCATION_SEARCH)

        with self.captureOnCommitCallbacks(execute=True):
            provider1 = SteerageProviderFactory.create(steerage=steerage, status=SteerageProviderStatuses.ACCEPTED)
            provider1.member_selected_at = date.today()
            provider1.save()
            provider2 = SteerageProviderFactory.create(
                steerage=steerage, status=SteerageProviderStatuses.ACCEPTED, member_selected_at=date.today()
            )
        provider1.refresh_from_db()
        provider2.refresh_from_db()

        self.assertIsNone(provider1.member_selected_at)
        self.assertIsNotNone(provider2.member_selected_at)

    def test_steerage_member_service_level(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # test create and get api for waiver
        referral_person = self.patient.person
        steerage: Steerage = SteerageFactory(person=referral_person)

        result = self.provider_client.get(
            f"/referral/steerage/{steerage.id}/",
            format="json",
        )
        self.assertEqual(result.json()["member_service_level"], "Care")

        add_person_to_program(self.patient.person, ProgramCodes.BENEFIT)
        result = self.provider_client.get(
            f"/referral/steerage/{steerage.id}/",
            format="json",
        )
        self.assertEqual(result.json()["member_service_level"], "Care and Coverage")


# Without concerning ourselves with specifics around case creation, ensure that
# cases are tagged with the appropriate status when the status of the Steerage changes.
def assert_case_tags_match_status(test: FireflyTestCase, steerage: Steerage):
    has_cases = steerage.case_relations.count() > 0
    if not has_cases:
        return

    has_matching_tag = False
    for case_relation in steerage.case_relations.all().iterator():
        # Ensure that the case has a tag that matches the steerage status, *and* that
        # no tags exist that match other possible statuses.
        matching_tag_count = 0
        for tag in case_relation.case.tags.all().iterator():
            if tag.display_name in SteerageStatus.values:
                matching_tag_count += 1
            if tag.display_name == steerage.status:
                has_matching_tag = True
        test.assertTrue(matching_tag_count, 1)

    test.assertTrue(has_matching_tag)
