import logging
import uuid
from random import randint
from typing import List, Optional, Union
from unittest import mock
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>

from dramatiq.rate_limits.backends import StubBackend
from faker import Faker

from firefly.core.alias.models import <PERSON>asMapping, AliasName
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.tests.utils import get_mock_redis
from firefly.core.user.factories import PersonUserFactory
from firefly.modules.code_systems.factories import CPTCodeFactory
from firefly.modules.code_systems.models import CPTCode
from firefly.modules.facts.factories import (
    LocationTypeFactory,
    SpecialtyFactory,
    SpecialtyGroupFactory,
)
from firefly.modules.facts.models import LocationType
from firefly.modules.insurance.factories import (
    InsurancePayerFactory,
    InsurancePlanFactory,
    NetworkFactory,
)
from firefly.modules.network.utils.utils import clear_cache_data
from firefly.modules.referral.constants import (
    RIBBON_SEARCH_EXCLUDE_FIELDS,
    LocationSearchConfig,
    ProviderSearchConfig,
)
from firefly.modules.referral.factories import SearchAvailabilityFactory, SteerageFactory
from firefly.modules.referral.models import (
    SearchRequest,
    SearchRequestVendor,
    SearchStatus,
    Steerage,
)
from firefly.modules.referral.serializers import fetch_search_status_choice
from firefly.modules.referral.tasks import store_search_request_for_provider_search_async
from firefly.modules.referral.utils.provider_search_utils import (
    AddressDetail,
    ProviderDetail,
    get_unique_key_from_location,
)

logger = logging.getLogger(__name__)


# function to generate number in increasing order


ribbon_specialty_search_results = {
    "parameters": {"total_count": 2},
    "data": [
        {
            "npi": "**********",
            "first_name": "Firsty",
            "middle_name": "Middy",
            "last_name": "Lasty",
            "age": 33,
            "gender": "F",
            "panel_demographics": {
                "ages": [
                    "Pediatric (0-12)",
                    "Adolescent (13-21)",
                    "Adult (22-44)",
                    "Adult (45-64)",
                    "Senior (65 and over)",
                ],
            },
            "locations": [
                {
                    "name": None,
                    "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                    "address": "5 E 98th St FL 2, New York, NY 10029, US",
                    "latitude": 40.789032,
                    "longitude": -73.9538422,
                    "confidence": 2.0,
                    "phone_numbers": [
                        {"phone": "**********", "details": "primary"},
                        {"phone": "**********", "details": "secondary"},
                    ],
                    "faxes": [{"fax": "**********", "details": "primary"}],
                    "address_details": {
                        "zip": "10029",
                        "city": "New York",
                        "state": "NY",
                        "street": "5 E 98th St FL 2",
                        "address_line_1": "5 E 98th St",
                        "address_line_2": "FL 2",
                    },
                    "google_maps_link": "https://www.google.com/maps/@40.7890329,"
                    "-73.95384589999999?q=5%20E%2098th%20St%20FL%202%2C%20New%20"
                    "York%2C%20NY%2010029%2C%20US",
                    "insurances": [
                        {
                            "uuid": "d8addf29-1054-4ccb-b179-dda65f7fefdd",
                            "carrier_association": "Aetna",
                            "carrier_brand": "Aetna",
                            "carrier_name": "Aetna",
                            "state": "",
                            "plan_name": "Aetna HealthFund Open Choice",
                            "plan_type": "PPO",
                            "metal_level": "",
                            "display_name": "Aetna - HealthFund Open Choice - PPO",
                            "network": "",
                            "confidence": 4,
                        },
                    ],
                    "distance": 4.19,
                },
                {
                    "name": None,
                    "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                    "address": "1 Gustave L. Levy Pl, New York, NY 10029, US",
                    "latitude": 40.7899895,
                    "longitude": -73.9536896,
                    "confidence": 2.0,
                    "phone_numbers": [
                        {"phone": "**********", "details": "primary"},
                        {"phone": "**********", "details": "secondary"},
                        {"phone": "**********", "details": "secondary"},
                    ],
                    "faxes": [{"fax": "**********", "details": "primary"}],
                    "address_details": {
                        "zip": "10029",
                        "city": "New York",
                        "state": "NY",
                        "street": "1 Gustave L. Levy Pl",
                        "address_line_1": "1 Gustave L. Levy Pl",
                        "address_line_2": None,
                    },
                    "google_maps_link": "https://www.google.com/maps/@40.78998989999999,-73.953689?"
                    "q=1%20Gustave%20L.%20Levy%20Pl%2C%20New%20York%2C%20NY%2010029%2C%20US",
                    "insurances": [],
                    "distance": 4.25,
                },
            ],
            "specialties": [
                {
                    "uuid": "79cd6503-436d-4dfc-88cd-0067c43164f6",
                    "taxonomy_code": "207QS0010X",
                    "board_specialty": "Family Medicine",
                    "board_sub_specialty": "Sports Medicine",
                    "non_md_specialty": None,
                    "non_md_sub_specialty": None,
                    "provider_name": "Sports Medicine Doctor",
                    "colloquial": None,
                    "taxonomy_1": "Allopathic & Osteopathic Physicians",
                    "taxonomy_2": "Family Medicine",
                    "taxonomy_3": "Sports Medicine",
                    "display": "Family Medicine - Sports Medicine",
                    "provider_type": "Doctor",
                },
                {
                    "uuid": "b942991d-777e-4d1c-9e43-12ea6cff0a51",
                    "taxonomy_code": "207X00000X",
                    "board_specialty": "Orthopaedic Surgery",
                    "board_sub_specialty": None,
                    "non_md_specialty": None,
                    "non_md_sub_specialty": None,
                    "provider_name": "Orthopedic Surgeon",
                    "colloquial": "Bone & Skeletal Surgeon",
                    "taxonomy_1": "Allopathic & Osteopathic Physicians",
                    "taxonomy_2": "Orthopaedic Surgery",
                    "taxonomy_3": None,
                    "display": "Orthopaedic Surgery",
                    "provider_type": "Doctor",
                },
            ],
            "performance": {
                "aggregate": {"cost": {"efficiency_index": 2, "ribbon_cost_score": 1}, "quality": {"outcomes_index": 5}}
            },
            "ratings_count": 0,
            "ratings_avg": None,
            "languages": ["english"],
            "insurances": [
                {
                    "uuid": "d8addf29-1054-4ccb-b179-dda65f7fefdd",
                    "carrier_association": "Aetna",
                    "carrier_brand": "Aetna",
                    "carrier_name": "Aetna",
                    "state": "",
                    "plan_name": "Aetna HealthFund Open Choice",
                    "plan_type": "PPO",
                    "metal_level": "",
                    "display_name": "Aetna - HealthFund Open Choice - PPO",
                    "network": "",
                    "confidence": 4,
                },
                {
                    "uuid": "d4357c57-76d9-4fda-94b6-16ca8869cd45",
                    "carrier_association": "UnitedHealthcare",
                    "carrier_brand": "UnitedHealthcare",
                    "carrier_name": "UnitedHealthcare",
                    "state": "",
                    "plan_name": "Passport Connect Choice Plus",
                    "plan_type": "",
                    "metal_level": "",
                    "display_name": "Unitedhealthcare - Passport Connect Choice Plus",
                    "network": "",
                    "confidence": 4,
                },
            ],
            "clinical_areas": None,
            "educations": None,
            "degrees": None,
        },
        {
            "npi": "**********",
            "first_name": "Firsty2",
            "middle_name": "Middy2",
            "last_name": "Lasty2",
            "age": 99,
            "gender": "M",
            "locations": [
                {
                    "name": None,
                    "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                    "address": "5 E 98th St FL 2, New York, NY 10029, US",
                    "latitude": 40.789032,
                    "longitude": -73.9538422,
                    "confidence": 2.0,
                    "phone_numbers": [
                        {"phone": "**********", "details": "primary"},
                        {"phone": "**********", "details": "secondary"},
                    ],
                    "faxes": [{"fax": "**********", "details": "primary"}],
                    "address_details": {
                        "zip": "10029",
                        "city": "New York",
                        "state": "NY",
                        "street": "5 E 98th St FL 2",
                        "address_line_1": "5 E 98th St",
                        "address_line_2": "FL 2",
                    },
                    "google_maps_link": "https://www.google.com/maps/@40.7890329,"
                    "-73.95384589999999?q=5%20E%2098th%20St%20FL%202%2C%20New%20"
                    "York%2C%20NY%2010029%2C%20US",
                    "insurances": [],
                    "distance": 4.19,
                },
                {
                    "name": None,
                    "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                    "address": "1 Gustave L. Levy Pl, New York, NY 10029, US",
                    "latitude": 40.7899895,
                    "longitude": -73.9536896,
                    "confidence": 2.0,
                    "phone_numbers": [
                        {"phone": "**********", "details": "primary"},
                        {"phone": "**********", "details": "secondary"},
                        {"phone": "**********", "details": "secondary"},
                    ],
                    "faxes": [{"fax": "**********", "details": "primary"}],
                    "address_details": {
                        "zip": "10029",
                        "city": "New York",
                        "state": "NY",
                        "street": "1 Gustave L. Levy Pl",
                        "address_line_1": "1 Gustave L. Levy Pl",
                        "address_line_2": None,
                    },
                    "google_maps_link": "https://www.google.com/maps/@40.78998989999999,-73.953689?"
                    "q=1%20Gustave%20L.%20Levy%20Pl%2C%20New%20York%2C%20NY%2010029%2C%20US",
                    "insurances": [],
                    "distance": 4.25,
                },
            ],
            "specialties": [
                {
                    "uuid": "ffbc61e5-92dd-405f-8c38-c26b001eac38",
                    "taxonomy_code": "207XX0005X",
                    "board_specialty": "Orthopaedic Surgery",
                    "board_sub_specialty": "Orthopaedic Sports Medicine",
                    "non_md_specialty": None,
                    "non_md_sub_specialty": None,
                    "provider_name": "Orthopedic Sports Surgeon",
                    "colloquial": "Sports Bone & Skeletal Surgeon",
                    "taxonomy_1": "Allopathic & Osteopathic Physicians",
                    "taxonomy_2": "Orthopaedic Surgery",
                    "taxonomy_3": "Sports Medicine",
                    "display": "Orthopaedic Surgery - Sports Medicine",
                    "provider_type": "Doctor",
                },
                {
                    "uuid": "18d8ad26-7e5f-44ac-9afa-966efb375344",
                    "taxonomy_code": "207Q00000X",
                    "board_specialty": "Family Medicine",
                    "board_sub_specialty": None,
                    "non_md_specialty": None,
                    "non_md_sub_specialty": None,
                    "provider_name": "Family Medicine Doctor",
                    "colloquial": None,
                    "taxonomy_1": "Allopathic & Osteopathic Physicians",
                    "taxonomy_2": "Family Medicine",
                    "taxonomy_3": None,
                    "display": "Family Medicine",
                    "provider_type": "Doctor",
                },
            ],
            "performance": {
                "aggregate": {"cost": {"efficiency_index": 3, "ribbon_cost_score": 1}, "quality": {"outcomes_index": 5}}
            },
            "ratings_count": 4,
            "ratings_avg": 10.0,
            "languages": ["english", "spanish", "korean"],
            "insurances": [
                {
                    "uuid": "d8addf29-1054-4ccb-b179-dda65f7fefdd",
                    "carrier_association": "Aetna",
                    "carrier_brand": "Aetna",
                    "carrier_name": "Aetna",
                    "state": "",
                    "plan_name": "Aetna HealthFund Open Choice",
                    "plan_type": "PPO",
                    "metal_level": "",
                    "display_name": "Aetna - HealthFund Open Choice - PPO",
                    "network": "",
                    "confidence": 4,
                },
                {
                    "uuid": "d4357c57-76d9-4fda-94b6-16ca8869cd45",
                    "carrier_association": "UnitedHealthcare",
                    "carrier_brand": "UnitedHealthcare",
                    "carrier_name": "UnitedHealthcare",
                    "state": "",
                    "plan_name": "Passport Connect Choice Plus",
                    "plan_type": "",
                    "metal_level": "",
                    "display_name": "Unitedhealthcare - Passport Connect Choice Plus",
                    "network": "",
                    "confidence": 4,
                },
            ],
            "clinical_areas": None,
            "educations": None,
            "degrees": None,
        },
    ],
}

ribbon_location_search_results = {
    "parameters": {"total_count": 2},
    "data": [
        {
            "uuid": "96dee11a-5d28-4736-8e08-8fb233aaf249",
            "name": "Location 1",
            "address": "5 E 98th St FL 2, New York, NY 10029, US",
            "address_details": {
                "street": "5 E 98th St FL 2",
                "address_line_1": "736 Cambridge St",
                "address_line_2": None,
                "city": "New York",
                "state": "NY",
                "zip": "10029",
            },
            "latitude": 11.101,
            "longitude": -11.101,
            "google_maps_link": "https://www.google.com/maps/",
            "location_types": ["Hospital"],
            "insurances": [],
            "specialties": [],
            "distance": 1.66,
            "clinical_areas": None,
            "educations": None,
            "degrees": None,
        },
        {
            "uuid": "49836f94-affe-4591-839c-0142eaa642eb",
            "name": "Location 2",
            "address": "5 E 2 98th St FL 2, New York, NY 10029, US",
            "address_details": {
                "street": "5 E 2  98th St FL 2",
                "address_line_1": "736 Cambridge St",
                "address_line_2": None,
                "city": "New York",
                "state": "NY",
                "zip": "10029",
            },
            "latitude": 11.101,
            "longitude": -11.101,
            "google_maps_link": "https://www.google.com/maps/",
            "phone_numbers": [
                {"phone": "1234", "detail": "primary"},
                {"phone": "5678", "detail": "secondary"},
                {"phone": "1233", "detail": "secondary"},
            ],
            "location_types": ["Hospital"],
            "insurances": [],
            "specialties": [],
            "distance": 1.66,
            "clinical_areas": None,
            "educations": None,
            "degrees": None,
        },
    ],
}


@mock.patch(
    "firefly.modules.network.utils.utils.get_redis_cache",
)
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch("firefly.core.user.utils.Geocoder.geocode_address", return_value=(40.69334, 73.98898))
@mock.patch("firefly.modules.referral.ribbon_search_serializers.store_search_request_for_provider_search_async")
class ReferralSearchTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()

        clear_cache_data()
        mock_redis_obj = get_mock_redis()
        # binding a side_effect of a MagicMock instance with redis methods
        mock_redis_method = MagicMock()
        mock_redis_method.get = Mock(side_effect=mock_redis_obj.get)
        mock_redis_method.set = Mock(side_effect=mock_redis_obj.set)
        mock_redis_method.flushall = Mock(side_effect=mock_redis_obj.flushall)
        self.mock_redis_method = mock_redis_method
        self.mock_redis_method.flushall()
        # Intentionally mock this as BCBS Blue based on real ribbon data.
        # This allows us to sanity check ribbon and our mocks against the real thing
        # by turning off the test flag temporarily
        self.payer = InsurancePayerFactory.create(name="BCBS MSA", payer_codes=["64222"])
        self.plan = InsurancePlanFactory.create(
            name="0750 - HMO BLUE NEW ENGLAND DEDUCTIBLE", insurance_payer=self.payer
        )
        self.network = NetworkFactory.create(name="Blue Cross Blue Shield of Massachusetts - Blue New England - HMO")
        self.plan.networks.add(self.network)
        self.plan.save()

        self.network_alias = AliasMapping.set_mapping_by_object(
            obj=self.network,
            alias_name=AliasName.RIBBON,
            alias_id="09acf90b-503d-4d78-ade2-da3b5dab12a2",
        )

        self.referral_patient = PersonUserFactory.create()
        self.referral_patient.insurance_info.insurance_payer = self.payer
        self.referral_patient.insurance_info.plan_description = self.plan.name
        self.referral_patient.insurance_info.insurance_plan = self.plan
        fake = Faker()
        self.zipcode = "%05d" % randint(1, 99999)
        self.street_address = fake.street_address()
        self.street_address_2 = fake.street_address()
        self.city = fake.city()
        self.address = (
            f"{self.street_address} "
            f"{self.street_address_2} "
            f"{self.city} "
            f"{self.referral_patient.insurance_info.state} "
            f"{self.zipcode} "
        )
        self.referral_patient.insurance_info.street_address = self.street_address
        self.referral_patient.insurance_info.street_address_2 = self.street_address_2
        self.referral_patient.insurance_info.city = self.city
        self.referral_patient.insurance_info.zipcode = self.zipcode
        self.referral_patient.insurance_info.save()
        self.referral_patient.insurance_info.refresh_from_db()

        self.page = 1
        self.page_size = 25
        self.distance = 10

        self.specialty_group = SpecialtyGroupFactory.create()
        self.specialty = SpecialtyFactory.create(specialty_groups=(self.specialty_group,))
        self.specialty.save()
        self.location_type = LocationTypeFactory.create()
        self.specialty_ribbon_alias = "1de33770-eb1c-47fa-ab3e-f9a4ab924d9d"

        self.specialty_alias = AliasMapping.set_mapping_by_object(
            obj=self.specialty,
            alias_name=AliasName.RIBBON,
            alias_id=self.specialty_ribbon_alias,
        )

        self.gender = "F"

    def get_search_attrs(self):
        return {
            "person": self.referral_patient.user.person.id,
            "distance": self.distance,
            "page": self.page,
            "page_size": self.page_size,
        }

    def get_specialty_group_search_attrs(self):
        search_attrs = self.get_search_attrs()
        search_attrs["specialty_group"] = self.specialty_group.id
        return search_attrs

    def get_location_search_attrs(self):
        location_type_id: Union[int, None] = None
        if self.location_type is not None:
            location_type_id = self.location_type.id
        search_attrs = self.get_search_attrs()
        search_attrs["location_type"] = location_type_id
        return search_attrs

    def run_available_case(self, available: bool):
        response = self.provider_client.post(
            f"/referral/search/person/{self.referral_patient.user.person.id}/availability/",
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["person"], self.referral_patient.user.person.id)
        self.assertEqual(response.json()["is_available"], available)
        if available:
            self.assertEqual(response.json()["error"], fetch_search_status_choice(SearchStatus.SUCCESS))
        else:
            self.assertIsNotNone(response.json()["error"])

    def run_specialty_search_case(
        self,
        expected_status,
        store_search_request_for_provider_search_async_mock,
        is_address_provided: bool = True,
        npi_override: Optional[List[Optional[str]]] = None,
        is_npi_expected_to_be_null: Optional[bool] = False,
        address_override: Optional[str] = None,
        cpt_code_override: Optional[CPTCode] = None,
        name_override: Optional[str] = None,
        steerage_override: Optional[Steerage] = None,
        gender_override: Optional[str] = None,
        panel_age: Optional[str] = None,
    ):
        request_params = self.get_specialty_group_search_attrs()
        search_availability = SearchAvailabilityFactory.create()
        request_params["search_availability_id"] = search_availability.id
        if not is_address_provided:
            request_params["address"] = None
        elif address_override:
            request_params["address"] = address_override
        if npi_override:
            request_params["npis"] = npi_override
        if name_override:
            request_params["name"] = name_override
        if cpt_code_override:
            request_params["cpt_code"] = cpt_code_override.pk
        if steerage_override:
            request_params["steerage"] = steerage_override.pk
        if gender_override:
            request_params["gender"] = gender_override
        if panel_age:
            request_params["panel_ages"] = [panel_age]
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            request_params,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        search_request_id = resp.get("id")
        self.assertIsNotNone(search_request_id)
        store_search_request_for_provider_search_async_mock.send.assert_called_once()
        # mimic the call synchronously
        for call in store_search_request_for_provider_search_async_mock.send.call_args_list:
            _args, kwargs = call
            if kwargs["specialty_group_id"]:
                kwargs["specialty_uids"] = [self.specialty_ribbon_alias]
            store_search_request_for_provider_search_async(**kwargs)
        self.assertEqual(resp["person"], self.referral_patient.user.person.id)
        self.assertEqual(resp["search_status"], expected_status)
        self.assertEqual(resp["search_availability_id"], search_availability.id)
        search_request: SearchRequest = SearchRequest.objects.get(id=search_request_id)
        self.assertIsNotNone(search_request.person)
        error_statuses = [
            SearchStatus.MISSING_INSURANCE_PLAN,
            SearchStatus.INSURANCE_UNMAPPED,
            SearchStatus.NETWORK_UNMAPPED,
        ]
        if expected_status not in error_statuses:
            self.assertIsNotNone(search_request.insurance_payer)
            self.assertIsNotNone(search_request.insurance_plan)
        if search_request.person:
            self.assertEqual(search_request.person.pk, resp["person"])
            self.assertEqual(search_request.person.id, self.referral_patient.user.person.id)
        if search_request.insurance_payer:
            self.assertEqual(search_request.insurance_payer.pk, resp["insurance_payer"])
        if search_request.insurance_plan:
            self.assertEqual(search_request.insurance_plan.pk, resp["insurance_plan"])
        if expected_status not in [SearchStatus.NETWORK_UNMAPPED, SearchStatus.MISSING_INSURANCE_PLAN]:
            self.assertEqual(
                search_request.networks.all().count(),
                self.referral_patient.insurance_info.insurance_plan.networks.all().count(),
            )
        self.assertEqual(search_request.distance, self.distance)
        self.assertEqual(search_request.min_location_confidence, LocationSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE)
        self.assertEqual(search_request.page_size, self.page_size)
        self.assertEqual(search_request.page, self.page)
        self.assertEqual(search_request.search_availability, search_availability)
        if expected_status == SearchStatus.SUCCESS:
            self.assertIsNotNone(search_request.records_available)
            self.assertIsNotNone(search_request.vendor_http_status_code)
            self.assertIsNotNone(search_request.vendor_results)
            self.assertEqual(search_request.specialty_uids, [self.specialty_ribbon_alias])
            self.assertEqual(search_request.vendor, SearchRequestVendor.RIBBON)
        self.assertEqual(search_request.search_status, expected_status)
        if is_npi_expected_to_be_null:
            self.assertIsNone(search_request.npis)
        elif npi_override:
            self.assertEqual(search_request.npis, npi_override)
        else:
            self.assertIsNone(search_request.npis)
        if cpt_code_override:
            cpt_aliases = AliasMapping.get_all_alias_ids_for_object(
                obj=cpt_code_override,
                alias_name=AliasName.RIBBON_PROCEDURE,
            )
            self.assertEqual(search_request.cpt_code, cpt_code_override)
            self.assertEqual(search_request.procedure_uids, cpt_aliases)
        else:
            self.assertIsNone(search_request.cpt_code)
        if name_override:
            self.assertEqual(search_request.name, name_override)
        else:
            self.assertIsNone(search_request.name)
        if gender_override:
            self.assertEqual(search_request.gender, gender_override)
        if not is_address_provided:
            self.assertEqual(search_request.address, self.address)
        elif address_override and address_override != self.zipcode:
            self.assertEqual(search_request.address, address_override)
        elif address_override and address_override == self.zipcode:
            self.assertEqual(search_request.address, self.address)
        if steerage_override:
            self.assertEqual(search_request.steerage, steerage_override)
        else:
            self.assertIsNone(search_request.steerage)
        return resp

    def run_location_search_case(
        self,
        expected_status=None,
        store_search_request_for_provider_search_async_mock=None,
        facility_name_override: Optional[str] = None,
        is_location_type_search: Optional[bool] = True,
    ):
        request_params = self.get_location_search_attrs()
        search_availability = SearchAvailabilityFactory.create()
        request_params["search_availability_id"] = search_availability.id
        if facility_name_override:
            request_params["facility_name"] = facility_name_override
        if not is_location_type_search:
            request_params.pop("location_type", None)
        response = self.provider_client.post("/referral/search/ribbon/facilities/", request_params, format="json")
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        search_request_id = resp.get("id")
        self.assertIsNotNone(search_request_id)
        self.assertEqual(resp["person"], self.referral_patient.user.person.id)
        self.assertEqual(resp["search_status"], expected_status)
        self.assertEqual(resp["search_availability_id"], search_availability.id)
        store_search_request_for_provider_search_async_mock.send.assert_called_once()
        # mimic the call synchronously
        for call in store_search_request_for_provider_search_async_mock.send.call_args_list:
            _args, kwargs = call
            store_search_request_for_provider_search_async(**kwargs)
        # Ensure person is set.
        search_request: SearchRequest = SearchRequest.objects.get(id=search_request_id)
        self.assertEqual(search_request.person, self.referral_patient.user.person)
        self.assertEqual(search_request.results, resp.get("results"))
        self.assertIsNotNone(search_request.person)
        self.assertIsNotNone(search_request.insurance_payer)
        self.assertIsNotNone(search_request.insurance_plan)
        if search_request.person:
            self.assertEqual(search_request.person.pk, resp["person"])
        if search_request.insurance_payer:
            self.assertEqual(search_request.insurance_payer.pk, resp["insurance_payer"])
        if search_request.insurance_plan:
            self.assertEqual(search_request.insurance_plan.pk, resp["insurance_plan"])
        self.assertEqual(
            search_request.networks.all().count(),
            self.referral_patient.insurance_info.insurance_plan.networks.all().count(),
        )
        self.assertIsNone(search_request.specialty)
        self.assertIsNone(search_request.specialty_group)
        self.assertIsNone(search_request.cpt_code)
        self.assertIsNone(search_request.ribbon_specialty_uid)
        self.assertIsNone(search_request.npis)
        self.assertIsNone(search_request.name)
        self.assertEqual(search_request.distance, self.distance)
        self.assertEqual(search_request.min_location_confidence, LocationSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE)
        self.assertEqual(search_request.page_size, self.page_size)
        self.assertEqual(search_request.page, self.page)
        self.assertEqual(search_request.facility_name, request_params.get("facility_name"))
        self.assertEqual(search_request.search_availability, search_availability)
        if expected_status == SearchStatus.SUCCESS:
            self.assertIsNotNone(search_request.records_available)
            self.assertIsNotNone(search_request.vendor_http_status_code)
            self.assertIsNotNone(search_request.vendor_results)
        self.assertEqual(search_request.search_status, expected_status)
        if is_location_type_search:
            self.assertEqual(search_request.location_type, self.location_type)
        else:
            self.assertIsNone(search_request.location_type)
        return resp

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_specialty_group_search_case_with_overridden_zip(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""

        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            address_override="0000",
        )
        # The call to Ribbon should include the overwritten zipcode
        for call in ribbon_call_mock.call_args_list:
            _, kwargs = call
            self.assertEqual(kwargs["address"], "0000")

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_specialty_group_search_case_with_overridden_zip_mathing_patient_address_zip(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""

        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            address_override=self.zipcode,
        )
        # The call to Ribbon should send only the zipcode, not the full address
        for call in ribbon_call_mock.call_args_list:
            _, kwargs = call
            self.assertEqual(kwargs["address"], self.zipcode)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_specialty_group_search_case_with_gender(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            gender_override=self.gender,
        )

        # The call to Ribbon should include gender
        for call in ribbon_call_mock.call_args_list:
            _, kwargs = call
            self.assertEqual(kwargs["gender"], self.gender)

    # get_providers gets pulled into the firefly.modules.referral.ribbon_search_serializers
    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_specialty_search_success(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        _get_redis_cache_mock,
    ):
        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_specialty_search_results, "200"), ""
        specialty_search_results = self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        ribbon_call.assert_called_once()

        results = ({"search_type": "specialty_group", "results": specialty_search_results},)
        for search_result_data in results:
            search_result = search_result_data["results"]
            # Ensure Firefly values are stored
            self.assertIsNotNone(search_result.get("insurance_payer"))
            self.assertIsNotNone(search_result.get("insurance_plan"))
            self.assertIsNotNone(search_result.get("address"))
            if search_result_data["search_type"] == "specialty_group":
                self.assertIsNotNone(search_result.get("specialty_group"))
            self.assertIsNotNone(search_result.get("networks"))
            self.assertIsNotNone(search_result.get("results"))
            self.assertIsNotNone(search_result.get("records_available"))
            # Ribbon values are computed, saved, but not revealed via API.
            self.assertIsNone(search_result.get("ribbon_specialty_uid"))
            self.assertIsNone(search_result.get("vendor_results"))
            self.assertIsNone(search_result.get("vendor_network_uids"))

            search_request: Optional[SearchRequest] = SearchRequest.objects.last()
            self.assertIsNotNone(search_request)
            if search_request is not None:
                self.assertIsNotNone(search_request.vendor_results)
                self.assertIsNotNone(search_request.vendor_network_uids)
                self.assertIsNone(search_request.npis)
                self.assertIsNone(search_request.name)
                self.assertIsNone(search_request.facility_name)
                self.assertIsNone(search_request.cpt_code)
                self.assertEqual(search_request.procedure_uids, [])

            # Validate synthesized results
            for result in search_result["results"]:
                self.assertIsNotNone(result.get("name"))
                self.assertIsNotNone(result.get("npi"))
                self.assertIsNotNone(result.get("locations"))
                self.assertIsNotNone(result.get("in_network"))
            is_provider_in_network: bool = False

            expected_results: List[ProviderDetail] = [
                {
                    "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                    "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                    "npi": "**********",
                    "name": "Firsty Middy Lasty",
                    "first_name": "Firsty",
                    "middle_name": "Middy",
                    "last_name": "Lasty",
                    "locations": [
                        {
                            "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                            "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                            "name": None,
                            "address_details": {
                                "address_line_1": "5 E 98th St",
                                "address_line_2": "FL 2",
                                "city": "New York",
                                "state": "NY",
                                "zip": "10029",
                                "street": "5 E 98th St FL 2",
                            },
                            "phone_numbers": [
                                {"details": "primary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                            ],
                            "distance": 4.19,
                            "fax_numbers": [{"details": "primary", "fax": "**********"}],
                            "location_in_network": False,
                            "availability": None,
                            "confidence": 2.0,
                            "network_partner_agreement_type": None,
                            "is_verified": None,
                            "partner_name": None,
                            "curated_provider_ranking": None,
                            "curated_provider_ranking_reason": None,
                            "tins": None,
                            "geo_code": None,
                            "talon_associated_procedure_prices": [],
                        }
                    ],
                    "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                    "languages": ["English"],
                    "gender": "Female",
                    "recommendation_status": "RECOMMENDED",
                    "recommendation_reason_labels": "Great cost, quality",
                    "in_network": is_provider_in_network,
                    "specialty_groups": None,
                    "location_types": None,
                    "distance": 4.19,
                    "network_partner_agreement_type": None,
                    "composite_score": 4.5,
                    "firefly_recommendation_factors": {
                        "cost_score_out_of_five": 5.0,
                        "quality_score_out_of_five": 5,
                        "average_rating_out_of_five": None,
                        "number_of_ratings": 0,
                        "expertise_score_out_of_five": None,
                    },
                    "ribbon_recommendation_factors": {
                        "cost_score": 1,
                        "quality_score": 5,
                        "average_rating": None,
                        "number_of_ratings": 0,
                    },
                    "talon_recommendation_factors": None,
                    "provider_type": None,
                    "partnership": None,
                    "clinical_focus_areas": None,
                    "educations": None,
                    "degrees": None,
                },
                {
                    "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                    "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                    "npi": "**********",
                    "name": "Firsty2 Middy2 Lasty2",
                    "first_name": "Firsty2",
                    "middle_name": "Middy2",
                    "last_name": "Lasty2",
                    "locations": [
                        {
                            "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                            "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                            "name": None,
                            "address_details": {
                                "address_line_1": "5 E 98th St",
                                "address_line_2": "FL 2",
                                "city": "New York",
                                "state": "NY",
                                "zip": "10029",
                                "street": "5 E 98th St FL 2",
                            },
                            "phone_numbers": [
                                {"details": "primary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                            ],
                            "distance": 4.19,
                            "fax_numbers": [{"details": "primary", "fax": "**********"}],
                            "location_in_network": None,
                            "availability": None,
                            "confidence": 2.0,
                            "network_partner_agreement_type": None,
                            "is_verified": None,
                            "partner_name": None,
                            "curated_provider_ranking": None,
                            "curated_provider_ranking_reason": None,
                            "tins": None,
                            "geo_code": None,
                            "talon_associated_procedure_prices": [],
                        }
                    ],
                    "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                    "languages": ["English", "Spanish", "Korean"],
                    "gender": "Male",
                    "recommendation_status": "RECOMMENDED",
                    "recommendation_reason_labels": "Great cost, quality",
                    "in_network": is_provider_in_network,
                    "specialty_groups": None,
                    "location_types": None,
                    "distance": 4.19,
                    "network_partner_agreement_type": None,
                    "composite_score": 4.5,
                    "firefly_recommendation_factors": {
                        "cost_score_out_of_five": 5.0,
                        "quality_score_out_of_five": 5,
                        "average_rating_out_of_five": 5.0,
                        "number_of_ratings": 4,
                        "expertise_score_out_of_five": None,
                    },
                    "ribbon_recommendation_factors": {
                        "cost_score": 1,
                        "quality_score": 5,
                        "average_rating": 10.0,
                        "number_of_ratings": 4,
                    },
                    "talon_recommendation_factors": None,
                    "provider_type": None,
                    "partnership": None,
                    "clinical_focus_areas": None,
                    "educations": None,
                    "degrees": None,
                },
                {
                    "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                    "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                    "npi": "**********",
                    "name": "Firsty Middy Lasty",
                    "first_name": "Firsty",
                    "middle_name": "Middy",
                    "last_name": "Lasty",
                    "locations": [
                        {
                            "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                            "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                            "name": None,
                            "address_details": {
                                "address_line_1": "1 Gustave L. Levy Pl",
                                "address_line_2": None,
                                "city": "New York",
                                "state": "NY",
                                "zip": "10029",
                                "street": "1 Gustave L. Levy Pl",
                            },
                            "phone_numbers": [
                                {"details": "primary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                            ],
                            "distance": 4.25,
                            "fax_numbers": [{"details": "primary", "fax": "**********"}],
                            "location_in_network": None,
                            "availability": None,
                            "confidence": 2.0,
                            "network_partner_agreement_type": None,
                            "is_verified": None,
                            "partner_name": None,
                            "curated_provider_ranking": None,
                            "curated_provider_ranking_reason": None,
                            "tins": None,
                            "geo_code": None,
                            "talon_associated_procedure_prices": [],
                        }
                    ],
                    "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                    "languages": ["English"],
                    "gender": "Female",
                    "recommendation_status": "RECOMMENDED",
                    "recommendation_reason_labels": "Great cost, quality",
                    "in_network": is_provider_in_network,
                    "specialty_groups": None,
                    "location_types": None,
                    "distance": 4.25,
                    "network_partner_agreement_type": None,
                    "composite_score": 4.5,
                    "firefly_recommendation_factors": {
                        "cost_score_out_of_five": 5.0,
                        "quality_score_out_of_five": 5,
                        "average_rating_out_of_five": None,
                        "number_of_ratings": 0,
                        "expertise_score_out_of_five": None,
                    },
                    "ribbon_recommendation_factors": {
                        "cost_score": 1,
                        "quality_score": 5,
                        "average_rating": None,
                        "number_of_ratings": 0,
                    },
                    "talon_recommendation_factors": None,
                    "provider_type": None,
                    "partnership": None,
                    "clinical_focus_areas": None,
                    "educations": None,
                    "degrees": None,
                },
                {
                    "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                    "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                    "npi": "**********",
                    "name": "Firsty2 Middy2 Lasty2",
                    "first_name": "Firsty2",
                    "middle_name": "Middy2",
                    "last_name": "Lasty2",
                    "locations": [
                        {
                            "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                            "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                            "name": None,
                            "address_details": {
                                "address_line_1": "1 Gustave L. Levy Pl",
                                "address_line_2": None,
                                "city": "New York",
                                "state": "NY",
                                "zip": "10029",
                                "street": "1 Gustave L. Levy Pl",
                            },
                            "phone_numbers": [
                                {"details": "primary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                                {"details": "secondary", "phone": "**********"},
                            ],
                            "distance": 4.25,
                            "fax_numbers": [{"details": "primary", "fax": "**********"}],
                            "location_in_network": None,
                            "availability": None,
                            "confidence": 2.0,
                            "network_partner_agreement_type": None,
                            "is_verified": None,
                            "partner_name": None,
                            "curated_provider_ranking": None,
                            "curated_provider_ranking_reason": None,
                            "tins": None,
                            "geo_code": None,
                            "talon_associated_procedure_prices": [],
                        }
                    ],
                    "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                    "languages": ["English", "Spanish", "Korean"],
                    "gender": "Male",
                    "recommendation_status": "RECOMMENDED",
                    "recommendation_reason_labels": "Great cost, quality",
                    "in_network": is_provider_in_network,
                    "specialty_groups": None,
                    "location_types": None,
                    "distance": 4.25,
                    "network_partner_agreement_type": None,
                    "composite_score": 4.5,
                    "firefly_recommendation_factors": {
                        "cost_score_out_of_five": 5.0,
                        "quality_score_out_of_five": 5,
                        "average_rating_out_of_five": 5.0,
                        "number_of_ratings": 4,
                        "expertise_score_out_of_five": None,
                    },
                    "ribbon_recommendation_factors": {
                        "cost_score": 1,
                        "quality_score": 5,
                        "average_rating": 10.0,
                        "number_of_ratings": 4,
                    },
                    "talon_recommendation_factors": None,
                    "provider_type": None,
                    "partnership": None,
                    "clinical_focus_areas": None,
                    "educations": None,
                    "degrees": None,
                },
            ]

            self.assertEqual(
                search_result["results"],
                expected_results,
            )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_npi_search_case(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        npi = uuid.uuid1().hex[:10]
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            npi_override=[npi],
        )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_steerage_is_storing_in_search_request(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # create a steerage
        steerage = SteerageFactory()

        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        npi = uuid.uuid1().hex[:10]
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            npi_override=[npi],
            steerage_override=steerage,
        )

    @mock.patch("firefly.core.services.ribbon.client.get_ribbon_providers")
    def test_provider_name_search_case(
        self,
        ribbon_provider_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Scenario:
        #   When we do ribbon name search, the insurance filters should not be sent
        # Expected:
        #   The payload doesn't contain the insurance
        ribbon_provider_call_mock.return_value = (ribbon_specialty_search_results, "200")
        faker: Faker = Faker()
        name = faker.name()
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            name_override=name,
        )
        expected_payload = {
            "sort_by": "distance",
            "distance": self.distance,
            "page_size": self.page_size,
            "page": self.page,
            "location_within_distance": True,
            "name": name,
            # We also obscure the full address from Ribbon
            "address": self.zipcode,
            "specialty_ids": self.specialty_ribbon_alias,
            "_excl_fields": RIBBON_SEARCH_EXCLUDE_FIELDS,
            "min_location_confidence": ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
        }
        # assert if expected payload doesn't have insurance and min_location_confidence filters
        ribbon_provider_call_mock.assert_called_once_with(expected_payload, True)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_procedure_search_case(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        cpt_code: CPTCode = CPTCodeFactory.create()

        cpt_alias: str = str(uuid.uuid1())
        AliasMapping.set_mapping_by_object(
            obj=cpt_code,
            alias_name=AliasName.RIBBON_PROCEDURE,
            alias_id=cpt_alias,
        )
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            cpt_code_override=cpt_code,
        )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_facilities")
    def test_location_search_success(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_location_search_results, "200"), ""
        post = self.run_location_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        ribbon_call.assert_called_once()

        # Ensure Firefly values are stored
        self.assertIsNotNone(post.get("insurance_payer"))
        self.assertIsNotNone(post.get("insurance_plan"))
        self.assertIsNotNone(post.get("address"))
        self.assertIsNone(post.get("specialty"))
        self.assertIsNotNone(post.get("location_type"))
        self.assertIsNotNone(post.get("networks"))
        self.assertIsNotNone(post.get("results"))
        self.assertIsNotNone(post.get("records_available"))

        # Ribbon values are computed, saved, but not revealed via API.
        search = SearchRequest.objects.last()
        self.assertIsNone(post.get("vendor_results"))
        self.assertIsNotNone(search.vendor_results)

        # Validate synthesized results
        for result in post["results"]:
            self.assertIsNotNone(result.get("name"))
            self.assertIsNone(result.get("npi"))
            self.assertIsNotNone(result.get("locations"))
        is_provider_in_network: bool = False
        is_location_in_network: Optional[bool] = None
        expected_results: List[ProviderDetail] = [
            {
                "id": "96dee11a-5d28-4736-8e08-8fb233aaf249",
                "npi": None,
                "name": "Location 1",
                "unique_identifier": "Location 1_736 Cambridge St_None_New York_NY_10029",
                "first_name": None,
                "middle_name": None,
                "last_name": None,
                "locations": [
                    {
                        "unique_identifier": "Location 1_736 Cambridge St_None_New York_NY_10029",
                        "uuid": "96dee11a-5d28-4736-8e08-8fb233aaf249",
                        "name": "Location 1",
                        "address_details": {
                            "street": "5 E 98th St FL 2",
                            "address_line_1": "736 Cambridge St",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                        },
                        "phone_numbers": [],
                        "fax_numbers": [],
                        "distance": 1.66,
                        "location_in_network": is_location_in_network,
                        "availability": None,
                        "confidence": None,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    },
                ],
                "specialties": [],
                "languages": [],
                "gender": None,
                "recommendation_status": "NEUTRAL",
                "recommendation_reason_labels": "Unknown Cost/Quality",
                "in_network": is_provider_in_network,
                "specialty_groups": None,
                "location_types": ["Hospital"],
                "distance": 1.66,
                "network_partner_agreement_type": None,
                "composite_score": None,
                "firefly_recommendation_factors": None,
                "ribbon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
            {
                "id": "49836f94-affe-4591-839c-0142eaa642eb",
                "npi": None,
                "unique_identifier": "Location 2_736 Cambridge St_None_New York_NY_10029",
                "name": "Location 2",
                "first_name": None,
                "middle_name": None,
                "last_name": None,
                "locations": [
                    {
                        "uuid": "49836f94-affe-4591-839c-0142eaa642eb",
                        "unique_identifier": "Location 2_736 Cambridge St_None_New York_NY_10029",
                        "name": "Location 2",
                        "address_details": {
                            "street": "5 E 2  98th St FL 2",
                            "address_line_1": "736 Cambridge St",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                        },
                        "phone_numbers": [
                            {"phone": "1234", "details": "primary"},
                            {"phone": "5678", "details": "secondary"},
                            {"phone": "1233", "details": "secondary"},
                        ],
                        "fax_numbers": [],
                        "distance": 1.66,
                        "location_in_network": is_location_in_network,
                        "availability": None,
                        "confidence": None,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    },
                ],
                "specialties": [],
                "languages": [],
                "gender": None,
                "recommendation_status": "NEUTRAL",
                "recommendation_reason_labels": "Unknown Cost/Quality",
                "in_network": is_provider_in_network,
                "specialty_groups": None,
                "location_types": ["Hospital"],
                "distance": 1.66,
                "network_partner_agreement_type": None,
                "composite_score": None,
                "firefly_recommendation_factors": None,
                "ribbon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
        ]

        self.assertEqual(
            post["results"],
            expected_results,
        )

    @mock.patch("firefly.core.services.ribbon.client.get_ribbon_facilities")
    def test_location_search_with_facility_name(
        self,
        ribbon_facility_call_mock,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        faker: Faker = Faker()
        facility_name = faker.name()
        expected_payload = {
            "sort_by": "distance",
            "distance": self.distance,
            "page_size": self.page_size,
            "page": self.page,
            "location_within_distance": True,
            "name": facility_name,
            # We also obscure the full address from Ribbon
            "address": self.zipcode,
            "_excl_fields": RIBBON_SEARCH_EXCLUDE_FIELDS,
            "min_confidence": LocationSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
        }

        # Scenario:
        #   When we do ribbon name search, the insurance filters should not be sent
        # Expected:
        #   The payload doesn't contain the insurance
        ribbon_facility_call_mock.return_value = (ribbon_location_search_results), 200

        self.run_location_search_case(
            is_location_type_search=False,
            facility_name_override=facility_name,
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        # assert if expected payload doesn't have insurance filters
        ribbon_facility_call_mock.assert_called_once_with(expected_payload, True)
        search_request: Optional[SearchRequest] = SearchRequest.objects.last()
        self.assertIsNotNone(search_request)
        if search_request is not None:
            self.assertEqual(search_request.facility_name, facility_name)

    def test_no_address(
        self,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.referral_patient.insurance_info.street_address = None
        self.referral_patient.insurance_info.save()
        self.run_available_case(False)
        self.run_specialty_search_case(
            expected_status=SearchStatus.ADDRESS_MISSING,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

    def test_no_plan(
        self,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.referral_patient.insurance_info.insurance_plan = None
        self.referral_patient.insurance_info.save()
        self.run_available_case(False)
        self.run_specialty_search_case(
            expected_status=SearchStatus.MISSING_INSURANCE_PLAN,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

    def test_no_network(
        self,
        store_search_request_for_provider_search_async_mock,
        _geocode_zipcode_mock,
        _mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.referral_patient.insurance_info.insurance_plan.networks.clear()
        self.referral_patient.insurance_info.insurance_plan.save()
        self.run_available_case(False)
        self.run_specialty_search_case(
            expected_status=SearchStatus.INSURANCE_UNMAPPED,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

    def test_no_network_alias(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        for network in self.referral_patient.insurance_info.insurance_plan.networks.all():
            AliasMapping.delete_mapping_for_object(obj=network, alias_name=AliasName.RIBBON)
        self.run_available_case(False)
        self.run_specialty_search_case(
            expected_status=SearchStatus.NETWORK_UNMAPPED,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

    def test_no_specialty_alias(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        for specialty in self.specialty_group.specialties.all():
            AliasMapping.delete_mapping_for_object(obj=specialty, alias_name=AliasName.RIBBON)
        self.run_specialty_search_case(
            expected_status=SearchStatus.SPECIALTY_UNMAPPED,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )
        self.run_available_case(True)

    def test_bad_distance(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.distance = -10
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

        self.distance = None
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

    def test_bad_page(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.page = -10
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

        self.page = None
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

    def test_bad_page_size(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        self.page_size = -10
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

        self.page_size = None
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            self.get_specialty_group_search_attrs(),
            format="json",
        )
        self.assertEqual(response.status_code, 400)

    def test_no_location(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        location_type: Union[LocationType, None] = self.location_type
        self.location_type = None
        self.run_location_search_case(
            expected_status=SearchStatus.LOCATION_TYPE_UNMAPPED,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )
        self.run_available_case(True)
        self.location_type = location_type

    def test_missing_search_terms(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)
        self.run_available_case(True)

    def test_invalid_search_request(
        self,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Combination of specialty and location type
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "specialty": self.specialty.id,
                "location_type": self.location_type.id,
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.INVALID_SEARCH_REQUEST)
        # Combination of specialty group and location type
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "specialty_group": self.specialty_group.id,
                "location_type": self.location_type.id,
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.INVALID_SEARCH_REQUEST)
        # Combination of npi and location type
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "npis": [uuid.uuid1().hex[:10]],
                "location_type": self.location_type.id,
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.INVALID_SEARCH_REQUEST)
        # NPI as an empty string
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "npis": [""],
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)
        # Multiple NPI as empty string
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "npis": ["", ""],
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)
        # name as an empty string
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "name": "",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)
        # facility_name as an empty string
        response = self.provider_client.post(
            "/referral/search/ribbon/facilities/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "facility_name": "",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)
        # cpt_code as an empty string
        response = self.provider_client.post(
            "/referral/search/ribbon/providers/",
            {
                "person": self.referral_patient.user.person.id,
                "distance": self.distance,
                "page": self.page,
                "page_size": self.page_size,
                "cpt_code": "",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.MISSING_SEARCH_TERMS)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_provider_not_in_network(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Scenario:
        #   member insurance not covered by provider
        # Expected Result:
        #   should return false for in_network

        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_specialty_search_results, "200"), ""
        specialty_search_results = self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        for search_result_data in specialty_search_results["results"]:
            self.assertFalse(search_result_data.get("in_network"))

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_provider_in_network(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Scenario:
        #   member insurance should be covered by provider
        # Expected Result:
        #   should return true for in_network

        # Map the member's network to have the uuid of an insurance accepted
        # by the provider
        AliasMapping.set_mapping_by_object(
            obj=self.network,
            alias_name=AliasName.RIBBON,
            alias_id="d8addf29-1054-4ccb-b179-dda65f7fefdd",
        )

        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_specialty_search_results, "200"), ""
        specialty_search_results = self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        for search_result_data in specialty_search_results["results"]:
            self.assertTrue(search_result_data.get("in_network"))

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_location_not_in_network(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Scenario:
        #   member insurance not covered by any location of the provider
        # Expected Result:
        #   should return false for location_in_network
        #   should return false for provider_in_network

        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_specialty_search_results, "200"), ""
        specialty_search_results = self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )
        expected_results: List[ProviderDetail] = [
            {
                "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty Middy Lasty",
                "first_name": "Firsty",
                "middle_name": "Middy",
                "last_name": "Lasty",
                "locations": [
                    {
                        "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                        "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "5 E 98th St",
                            "address_line_2": "FL 2",
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "5 E 98th St FL 2",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.19,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": False,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                "languages": ["English"],
                "gender": "Female",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": False,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.19,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": None,
                    "number_of_ratings": 0,
                },
                "talon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
            },
            {
                "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty2 Middy2 Lasty2",
                "first_name": "Firsty2",
                "middle_name": "Middy2",
                "last_name": "Lasty2",
                "locations": [
                    {
                        "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                        "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "5 E 98th St",
                            "address_line_2": "FL 2",
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "5 E 98th St FL 2",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.19,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                "languages": ["English", "Spanish", "Korean"],
                "gender": "Male",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": False,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.19,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": 5.0,
                    "number_of_ratings": 4,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": 10.0,
                    "number_of_ratings": 4,
                },
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
            {
                "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty Middy Lasty",
                "first_name": "Firsty",
                "middle_name": "Middy",
                "last_name": "Lasty",
                "locations": [
                    {
                        "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                        "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "1 Gustave L. Levy Pl",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "1 Gustave L. Levy Pl",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.25,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                "languages": ["English"],
                "gender": "Female",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": False,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.25,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": None,
                    "number_of_ratings": 0,
                },
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
            {
                "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty2 Middy2 Lasty2",
                "first_name": "Firsty2",
                "middle_name": "Middy2",
                "last_name": "Lasty2",
                "locations": [
                    {
                        "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                        "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "1 Gustave L. Levy Pl",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "1 Gustave L. Levy Pl",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.25,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                "languages": ["English", "Spanish", "Korean"],
                "gender": "Male",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": False,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.25,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": 5.0,
                    "number_of_ratings": 4,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": 10.0,
                    "number_of_ratings": 4,
                },
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
        ]
        self.assertEqual(
            specialty_search_results["results"],
            expected_results,
        )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_location_in_network(
        self,
        ribbon_call,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        # Scenario:
        #   member insurance should be covered by one location of provider
        # Expected Result:
        #   should return true for location_in_network for the location that accepts insurance
        #   should return true for provider_in_network

        # Map the member's network to have the uuid of an insurance accepted
        # by the provider
        AliasMapping.set_mapping_by_object(
            obj=self.network,
            alias_name=AliasName.RIBBON,
            alias_id="d8addf29-1054-4ccb-b179-dda65f7fefdd",
        )

        self.run_available_case(True)
        ribbon_call.return_value = (ribbon_specialty_search_results, "200"), ""
        specialty_search_results = self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
        )

        expected_results: List[ProviderDetail] = [
            {
                "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty Middy Lasty",
                "first_name": "Firsty",
                "middle_name": "Middy",
                "last_name": "Lasty",
                "locations": [
                    {
                        "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                        "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "5 E 98th St",
                            "address_line_2": "FL 2",
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "5 E 98th St FL 2",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.19,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": True,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                "languages": ["English"],
                "gender": "Female",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": True,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.19,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": None,
                    "number_of_ratings": 0,
                },
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
                "talon_recommendation_factors": None,
            },
            {
                "id": "**********_bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                "unique_identifier": "**********_None_5 E 98th St_FL 2_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty2 Middy2 Lasty2",
                "first_name": "Firsty2",
                "middle_name": "Middy2",
                "last_name": "Lasty2",
                "locations": [
                    {
                        "uuid": "bb9c3dbf-c982-4ca8-92a2-2f2a04a7c6d6",
                        "unique_identifier": "None_5 E 98th St_FL 2_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "5 E 98th St",
                            "address_line_2": "FL 2",
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "5 E 98th St FL 2",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.19,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                "languages": ["English", "Spanish", "Korean"],
                "gender": "Male",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": True,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.19,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": 5.0,
                    "number_of_ratings": 4,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": 10.0,
                    "number_of_ratings": 4,
                },
                "talon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
            },
            {
                "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty Middy Lasty",
                "first_name": "Firsty",
                "middle_name": "Middy",
                "last_name": "Lasty",
                "locations": [
                    {
                        "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                        "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "1 Gustave L. Levy Pl",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "1 Gustave L. Levy Pl",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.25,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Family Medicine - Sports Medicine", "Orthopaedic Surgery"],
                "languages": ["English"],
                "gender": "Female",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": True,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.25,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": None,
                    "number_of_ratings": 0,
                },
                "talon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
            },
            {
                "id": "**********_162f1c7b-c6fc-48bf-99cf-3862629e4491",
                "unique_identifier": "**********_None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                "npi": "**********",
                "name": "Firsty2 Middy2 Lasty2",
                "first_name": "Firsty2",
                "middle_name": "Middy2",
                "last_name": "Lasty2",
                "locations": [
                    {
                        "uuid": "162f1c7b-c6fc-48bf-99cf-3862629e4491",
                        "unique_identifier": "None_1 Gustave L. Levy Pl_None_New York_NY_10029",
                        "name": None,
                        "address_details": {
                            "address_line_1": "1 Gustave L. Levy Pl",
                            "address_line_2": None,
                            "city": "New York",
                            "state": "NY",
                            "zip": "10029",
                            "street": "1 Gustave L. Levy Pl",
                        },
                        "phone_numbers": [
                            {"details": "primary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                            {"details": "secondary", "phone": "**********"},
                        ],
                        "distance": 4.25,
                        "fax_numbers": [{"details": "primary", "fax": "**********"}],
                        "location_in_network": None,
                        "availability": None,
                        "confidence": 2.0,
                        "network_partner_agreement_type": None,
                        "is_verified": None,
                        "partner_name": None,
                        "curated_provider_ranking": None,
                        "curated_provider_ranking_reason": None,
                        "tins": None,
                        "geo_code": None,
                        "talon_associated_procedure_prices": [],
                    }
                ],
                "specialties": ["Orthopaedic Surgery - Sports Medicine", "Family Medicine"],
                "languages": ["English", "Spanish", "Korean"],
                "gender": "Male",
                "recommendation_status": "RECOMMENDED",
                "recommendation_reason_labels": "Great cost, quality",
                "in_network": True,
                "specialty_groups": None,
                "location_types": None,
                "distance": 4.25,
                "network_partner_agreement_type": None,
                "composite_score": 4.5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": 5.0,
                    "number_of_ratings": 4,
                    "expertise_score_out_of_five": None,
                },
                "ribbon_recommendation_factors": {
                    "cost_score": 1,
                    "quality_score": 5,
                    "average_rating": 10.0,
                    "number_of_ratings": 4,
                },
                "talon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": None,
                "educations": None,
                "degrees": None,
            },
        ]
        self.assertEqual(
            specialty_search_results["results"],
            expected_results,
        )

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_address_obscured_for_ribbon(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        self.run_specialty_search_case(
            expected_status=SearchStatus.SUCCESS,
            store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
            is_address_provided=False,
        )

        # But the call to Ribbon should replace the address with the Zip
        for call in ribbon_call_mock.call_args_list:
            _, kwargs = call
            self.assertEqual(kwargs["address"], self.zipcode)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_ribbon_search_9_digit_person_zipcode(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""
        payload = self.get_specialty_group_search_attrs()
        # Typically, with a null address, we would replace it with the
        # memer's own address.
        payload["address"] = None

        self.referral_patient.insurance_info.zipcode = f"{self.zipcode}0210"
        self.referral_patient.insurance_info.save()

        response = self.provider_client.post("/referral/search/ribbon/providers/", payload, format="json")
        self.assertEqual(response.status_code, 201)
        resp = response.json()
        self.assertEqual(resp["search_status"], SearchStatus.SUCCESS)

        # But the call to Ribbon should replace the address with the Zip
        for call in ribbon_call_mock.call_args_list:
            _, kwargs = call
            self.assertEqual(kwargs["address"], self.zipcode)

    @mock.patch("firefly.modules.referral.utils.provider_search_utils.get_providers")
    def test_specialty_group_search_case_with_overridden_zip_falsy_npi(
        self,
        ribbon_call_mock,
        store_search_request_for_provider_search_async_mock,
        geocode_zipcode_mock,
        mutex_mock,
        get_redis_cache_mock,
    ):
        # This test is a copy of the specialty group search with overriden zip
        # test. I use it as a test for handling of falsy NPI values in search
        # requests.
        #
        # Writing the test this way is a compromise between writing the
        # minimum amount of tests for the code change and changing every test
        # to see if the change has affected payloads.

        get_redis_cache_mock.return_value = self.mock_redis_method
        ribbon_call_mock.return_value = (ribbon_specialty_search_results, "200"), ""

        # This sets up tests for the falsy cases we are interested in
        falsy_cases = [
            {"msg": None, "value": None},
            {"msg": "Empty list", "value": []},
            {"msg": "List with None", "value": [None]},
            {"msg": "List with empty string", "value": [""]},
        ]

        for falsy_case in falsy_cases:
            with self.subTest(falsy_case["msg"]):
                store_search_request_for_provider_search_async_mock.reset_mock()
                self.run_specialty_search_case(
                    expected_status=SearchStatus.SUCCESS,
                    store_search_request_for_provider_search_async_mock=store_search_request_for_provider_search_async_mock,
                    npi_override=falsy_case["value"],
                    is_npi_expected_to_be_null=True,
                )


class TestUniqueKeyGeneration(FireflyTestCase):
    def test_when_all_parts_of_key_are_defined(self):
        faker: Faker = Faker()
        address_line_1: str = faker.name()
        address_line_2: str = faker.name()
        city: str = faker.name()
        state: str = faker.name()
        zip: str = faker.name()
        street: str = faker.name()
        care_org_name: str = faker.name()
        address: AddressDetail = {
            "address_line_1": address_line_1,
            "address_line_2": address_line_2,
            "city": city,
            "state": state,
            "zip": zip,
            "street": street,
        }
        expected_key: str = f"{care_org_name}_{address_line_1}_{address_line_2}_{city}_{state}_{zip}"
        self.assertEqual(
            get_unique_key_from_location(
                name=care_org_name,
                address=address,
            ),
            expected_key,
        )

    def test_when_some_parts_of_key_are_defined(self):
        faker: Faker = Faker()
        care_org_name: Optional[str] = None
        address_line_1: str = faker.name()
        address_line_2: str = faker.name()
        city: Optional[str] = None
        state: str = faker.name()
        zip: str = faker.name()
        street: str = faker.name()
        address: AddressDetail = {
            "address_line_1": address_line_1,
            "address_line_2": address_line_2,
            "city": city,
            "state": state,
            "zip": zip,
            "street": street,
        }
        expected_key: str = f"None_{address_line_1}_{address_line_2}_None_{state}_{zip}"
        self.assertEqual(
            get_unique_key_from_location(
                name=care_org_name,
                address=address,
            ),
            expected_key,
        )

    def test_when_some_parts_of_key_are_missing(self):
        faker: Faker = Faker()
        care_org_name: Optional[str] = None
        address_line_1: str = faker.name()
        city: Optional[str] = None
        state: str = faker.name()
        zip: str = faker.name()
        street: str = faker.name()

        expected_key: str = f"None_{address_line_1}_None_None_{state}_{zip}"
        self.assertEqual(
            get_unique_key_from_location(
                name=care_org_name,
                address={
                    "address_line_1": address_line_1,
                    "city": city,
                    "state": state,
                    "zip": zip,
                    "street": street,
                },
            ),
            expected_key,
        )
