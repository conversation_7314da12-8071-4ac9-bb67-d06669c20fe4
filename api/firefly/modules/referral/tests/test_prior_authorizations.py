import logging
import uuid
from copy import deepcopy
from datetime import date, datetime, timed<PERSON>ta
from random import randint
from typing import Iterable, List, Optional
from unittest import mock

from django.contrib.contenttypes.models import ContentType
from faker import Faker
from faker.generator import random

from firefly.core.alias.models import <PERSON>as<PERSON>apping, AliasName, get_content_type
from firefly.core.services.slack.constants import SlackChannel
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonFactory, PersonUserFactory
from firefly.core.user.models import Person
from firefly.modules.cases.constants import (
    PRIOR_AUTH_MISSING_DETAILS,
    REFERRAL_REQUEST,
    ReferralRequestCaseStatuses,
)
from firefly.modules.cases.factories import CaseFactory
from firefly.modules.cases.models import Case, CaseCategory, CaseRelationTemplate
from firefly.modules.code_systems.models import CPTCode
from firefly.modules.facts.factories import ServiceCategoryFactory
from firefly.modules.facts.models import ServiceCategory
from firefly.modules.insurance.factories import InsuranceMemberInfoFactory
from firefly.modules.programs.constants import BenefitProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.referral.constants import MEMBER_SCHEDULED_ACTION
from firefly.modules.referral.factories import PriorAuthorizationFactory
from firefly.modules.referral.models import (
    PriorAuthDiagnosisCode,
    PriorAuthorization,
    PriorAuthorizationProvider,
    PriorAuthorizationWebhook,
    PriorAuthProcedureCode,
    ServiceQuantityUnitConfig,
    Steerage,
    SteerageServiceQuantity,
    Waiver,
)
from firefly.modules.referral.tasks import consume_prior_authorization
from firefly.modules.referral.types import FlumeCode, FlumePriorAuthorization, FlumeProvider, FlumeServiceQuantity
from firefly.modules.statemachines.contants import DEFAULT_V2_STATE_MACHINE_CONTENT
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.states.models import State
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)


def sample(*items):
    index = randint(0, len(items) - 1)
    return items[index]


class TestPriorAuthorizationWebhook(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)

        state_machine, _ = StateMachineDefinition.objects.get_or_create(
            content=DEFAULT_V2_STATE_MACHINE_CONTENT, title="Default v2"
        )
        CaseCategory.objects.get_or_create(
            unique_key=PRIOR_AUTH_MISSING_DETAILS, state_machine_definition=state_machine
        )
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(self.patient.person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=self.patient.person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )

        # Setup referral initation case with the state machine
        referral_initiation_state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": "*", "trigger": "auto_close", "system_action": "system_close"},
                {"dest": "Done", "source": [], "trigger": "Done"},
                {"dest": "Steerage Not Needed", "source": "*", "trigger": "Steerage Not Needed"},
                {
                    "dest": "Steerage Not Needed",
                    "source": "*",
                    "trigger": "deferred",
                    "system_action": "system_deferred",
                },
                {"dest": "Complete in Elation", "source": ["New"], "trigger": "Complete in Elation"},
                {"dest": "Member Scheduled", "source": "*", "trigger": "Member Scheduled"},
                {"dest": "Location Search", "source": ["New", "Complete in Elation"], "trigger": "Location Search"},
                {
                    "dest": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                    "source": "*",
                    "trigger": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                },
            ],
            "initial_state": "New",
            "state_with_categories": [
                {
                    "state": {"name": "New", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 2, "use_business_days": True}],
                },
                {"state": {"name": "Complete in Elation", "ignore_invalid_triggers": None}, "category": "in_progress"},
                {
                    "state": {
                        "name": "Steerage Not Needed",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "deferred",
                },
                {
                    "state": {
                        "name": "Location Search",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": "Done",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
                {
                    "state": {
                        "name": "Member Scheduled",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
            ],
        }
        referral_initiation_state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
            title="referral_request_statemachine",
            content=referral_initiation_state_machine_content,
        )
        self.referral_initiation_category = CaseCategory.objects.create(
            unique_key=REFERRAL_REQUEST,
            state_machine_definition=referral_initiation_state_machine_definition,
            title="Referral Initiation",
        )
        CaseRelationTemplate.objects.create(
            content_type=ContentType.objects.get_for_model(Steerage),
            case_category=self.referral_initiation_category,
            on_after_creation="associate_referral_and_waiver_if_coverage_member",
        )

        # Create a random PriorAuthorization using the factory, so that we can get our
        # side effect case categories up front.
        PriorAuthorizationFactory.create()

    @staticmethod
    def as_date(date_string):
        return datetime.strptime(date_string, "%Y-%m-%d").date()

    @staticmethod
    def generate_codes(**options) -> List[FlumeCode]:
        fake = Faker()
        codes: List[FlumeCode] = []

        # Pulling this generation out of the loop to avoid test flake
        first_cpt = randint(10000, 99999)
        for iterator in range(0, randint(3, 5)):
            new_cpt = first_cpt + iterator
            codes.append(
                {
                    "code": str(new_cpt),
                    "system": options.get("system", fake.text()),
                    # One day, if we load more dynamic, yearly-versioned CPT codes
                    # we can update this to be the current year.
                    "version": "2022",
                }
            )
        return codes

    @staticmethod
    def generate_service_category_ids(use_labels_instead_of_alias: bool = False) -> List[str]:
        service_category_ids: List[str] = []
        for _ in range(0, randint(3, 5)):
            service_category: ServiceCategory = ServiceCategoryFactory.create()
            if use_labels_instead_of_alias:
                service_category_ids.append(service_category.label)
            else:
                service_category_flume_id = str(randint(10000, 99999))
                AliasMapping.set_mapping_by_object(
                    obj=service_category,
                    alias_name=AliasName.FLUME,
                    alias_id=service_category_flume_id,
                )
                service_category_ids.append(service_category_flume_id)
        return service_category_ids

    @staticmethod
    def generate_provider_list() -> List[FlumeProvider]:
        fake = Faker()
        providers: List[FlumeProvider] = []
        for _ in range(0, randint(1, 5)):
            state_abbreviation = fake.text(max_nb_chars=5)[:2]
            State.objects.get_or_create(
                abbreviation=state_abbreviation,
                defaults={
                    "can_service": True,
                    "name": fake.text(max_nb_chars=30),
                },
            )
            providers.append(
                {
                    "facility": {
                        "address": fake.street_address(),
                        "city": fake.city(),
                        "facilityName": fake.text(),
                        "npi2": uuid.uuid1().hex[:10],
                        "state": state_abbreviation,
                        "tin": fake.text(),
                        "zip": fake.text(),
                    },
                    "practitioner": {
                        "firstName": fake.first_name(),
                        "lastName": fake.last_name(),
                        "middleName": fake.last_name(),
                        "nameSuffix": fake.suffix(),
                        "networkStatus": fake.city(),
                        "npi1": uuid.uuid1().hex[:10],
                    },
                }
            )
        return providers

    @classmethod
    def generate_service_quantity(cls, unit: str) -> FlumeServiceQuantity:
        if unit == ServiceQuantityUnitConfig.DAYS:
            return {
                "days": randint(1, 10),
                "units": None,
                "visits": None,
            }
        elif unit == ServiceQuantityUnitConfig.UNITS:
            return {
                "days": None,
                "units": randint(1, 10),
                "visits": None,
            }
        else:
            return {
                "days": None,
                "units": None,
                "visits": randint(1, 10),
            }

    @classmethod
    def generate_data(cls, memberKey: Optional[str] = None, id: Optional[str] = None) -> FlumePriorAuthorization:
        fake = Faker()

        return {
            "comment": fake.text(),
            "createDate": fake.date(),
            "determination": "Approved",
            "effectiveFrom": fake.date(),
            "effectiveThrough": str(date.today() + timedelta(days=1)),
            "id": id if id else str(randint(1, 1000)),
            "memberKey": memberKey if memberKey else fake.text(),
            "networkID": fake.text(),
            "originatorSystemID": fake.text(),
            "serviceCategoryIDs": cls.generate_service_category_ids(),
            "diagnosisCodes": cls.generate_codes(),
            "procedureCodes": cls.generate_codes(system="CPT"),
            "providers": cls.generate_provider_list(),
            "serviceQuantity": cls.generate_service_quantity(unit=ServiceQuantityUnitConfig.VISITS),
            "status": random.choice([value for _, value in PriorAuthorization.STATUS_CHOICES]),
            "type": random.choice([value for _, value in PriorAuthorization.TYPE_CHOICES]),
            "updateDate": fake.date(),
        }

    @classmethod
    def generate_payload(cls, id: Optional[str] = None, memberKey: Optional[str] = None):
        fake = Faker()
        payload = {
            "id": fake.uuid4(),
            "eventType": fake.word(),
            "timestamp": fake.date_time().isoformat(),
            "data": cls.generate_data(memberKey=memberKey, id=id),
        }

        return payload

    @staticmethod
    def save_cpt_codes_and_return_descriptions(cpt_codes: Iterable[dict]) -> dict:
        fake = Faker()
        descriptions = {}
        for cpt_code in cpt_codes:
            description = fake.sentence()
            CPTCode.objects.get_or_create(
                code=cpt_code["code"],
                version=cpt_code["version"],
                defaults={"description": description},
            )
            key = (cpt_code["code"], cpt_code["system"])
            descriptions[key] = description

        return descriptions

    def test_when_conversion_throws_an_error_due_to_malformed_payload(self):
        payload = self.generate_payload()

        # Remove a required field  Python will raise a KeyError when trying to unpack this data
        del payload["data"]["id"]

        person: Person = PersonFactory()
        person.insurance_info = InsuranceMemberInfoFactory(
            member_id=payload["data"]["memberKey"],
        )
        person.save()

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=payload["data"],
        )

        try:
            consume_prior_authorization(prior_authorization_webhook.id)
            raise AssertionError("code failed to raise KeyError as expected")
        except KeyError:
            pass

        prior_authorization_webhook.refresh_from_db()

        self.assertEqual(prior_authorization_webhook.conversion_status, "failed")

        self.assertIsNone(
            prior_authorization_webhook.prior_authorization,
            "was not supposed to save prior_authorization model if the conversion failed",
        )

    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.is_person_in_firefly")
    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.send_slack_message")
    def test_slack_alerts_to_firefly(self, mock_send_slack_message, mock_is_person_in_firefly):
        fake = Faker()
        person: Person = PersonUserFactory()
        person: Person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )
        mock_is_person_in_firefly.return_value = True
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()
        mock_send_slack_message.send.assert_called_once()
        mock_send_slack_message.reset_mock()

        mock_is_person_in_firefly.return_value = False
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()
        mock_send_slack_message.send.assert_not_called()
        mock_send_slack_message.reset_mock()

    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.send_slack_message")
    def test_conversion_for_approved_prior_authorizations(self, mock_send_slack_message):
        fake = Faker()
        person: Person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]
        cpt_descriptions = self.save_cpt_codes_and_return_descriptions(data["procedureCodes"])
        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )

        # Let's test the happy path
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        self.assertTrue(
            AliasMapping.objects.filter(
                object_id=prior_authorization_webhook.prior_authorization.id,
                content_type__model="priorauthorization",
                alias_name=AliasName.FLUME,
                alias_id=payload["data"]["id"],
            ).exists(),
            "failed to create alias mapping for flume webhook",
        )

        prior_auth: PriorAuthorization = prior_authorization_webhook.prior_authorization
        waiver: Waiver = Waiver.objects.get(prior_authorization=prior_auth)
        self.assertIsNotNone(waiver)

        self.assertEqual(prior_authorization_webhook.conversion_status, "succeeded")

        # Assert that on this first run, and before we test updating scenarios, only ONE message
        # was sent to the slack channel (that it was created, not updated)
        MESSAGE = (
            "A new Approved Prior Authorization has been processed (OriginatorSystemID "
            f"{prior_auth.originator_system_id}) for user {prior_auth.person.user.id}"
        )
        mock_send_slack_message.send.assert_called_once_with(
            markdown_text=MESSAGE,
            slack_channel=SlackChannel.WAIVER_ALERTS,
        )

        # Reset the slack chat mock
        mock_send_slack_message.reset_mock()

        # Test that the fields are updated
        prior_auth.refresh_from_db()
        self.assertEqual(prior_auth.comment, data["comment"])
        self.assertEqual(prior_auth.determination, data["determination"])
        self.assertEqual(prior_auth.effective_from, self.as_date(data["effectiveFrom"]))
        self.assertEqual(prior_auth.effective_through, self.as_date(data["effectiveThrough"]))
        self.assertEqual(prior_auth.person, person)
        self.assertEqual(prior_auth.status, data["status"])
        self.assertEqual(prior_auth.type, data["type"])
        self.assertEqual(prior_auth.vendor_create_date, self.as_date(data["createDate"]))
        self.assertEqual(prior_auth.vendor_update_date, self.as_date(data["updateDate"]))
        self.assertIsNotNone(data["diagnosisCodes"])
        if data["diagnosisCodes"] is not None:
            self.assertGreater(len(data["diagnosisCodes"]), 0)
            expected_diagnosis_codes: List[FlumeCode] = []
            for diagnosis_code in data["diagnosisCodes"]:
                expected_diagnosis_codes.append(diagnosis_code)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                expected_diagnosis_codes,
                PriorAuthDiagnosisCode.objects.filter(prior_authorization=prior_auth).values(
                    "code", "system", "version"
                ),
            )
            # Scenario
            #   A diagnosis is removed when we receive the next webhook event
            # Expected behavior
            #   Irrelevant diagnoses are dropped from the PriorAuthDiagnosisCode table
            last_diagnosis = data["diagnosisCodes"].pop()
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            expected_diagnosis_codes: List[FlumeCode] = []
            for diagnosis_code in data["diagnosisCodes"]:
                expected_diagnosis_codes.append(diagnosis_code)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                expected_diagnosis_codes,
                PriorAuthDiagnosisCode.objects.filter(prior_authorization=prior_auth).values(
                    "code", "system", "version"
                ),
            )
            deleted_diagnosis_code = PriorAuthDiagnosisCode.deleted_objects.first()
            self.assertIsNotNone(deleted_diagnosis_code)
            if deleted_diagnosis_code is not None:
                self.assertEqual(deleted_diagnosis_code.code, last_diagnosis["code"])
                self.assertEqual(deleted_diagnosis_code.system, last_diagnosis["system"])
                self.assertEqual(deleted_diagnosis_code.version, last_diagnosis["version"])

            # Test that an update triggers another slack message
            MESSAGE = (
                "An update for an Approved Prior Authorization has been processed (OriginatorSystemID "
                f"{prior_auth.originator_system_id}) for user {prior_auth.person.user.id}"
            )
            mock_send_slack_message.send.assert_called_once_with(
                markdown_text=MESSAGE,
                slack_channel=SlackChannel.WAIVER_ALERTS,
            )
            # Reset the slack chat mock
            mock_send_slack_message.reset_mock()
        self.assertIsNotNone(data["procedureCodes"])
        if data["procedureCodes"] is not None:
            self.assertGreater(len(data["procedureCodes"]), 0)
            expected_procedure_codes: List[FlumeCode] = []
            for procedure_code in data["procedureCodes"]:
                expected_procedure_codes.append(procedure_code)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                expected_procedure_codes,
                PriorAuthProcedureCode.objects.filter(prior_authorization=prior_auth).values(
                    "code", "system", "version"
                ),
            )

            # Check that the CPT code descriptions are mapped correctly
            expected_cpt_code_descriptions = {}
            procedure_code: PriorAuthProcedureCode
            for procedure_code in prior_auth.procedure_codes.all():
                key = (procedure_code.code, procedure_code.system)
                expected_cpt_code_descriptions[key] = (
                    procedure_code.content_object and procedure_code.content_object.description
                )

            self.assertSetEqual(set(expected_cpt_code_descriptions.keys()), set(cpt_descriptions.keys()))
            for key, actual_description in cpt_descriptions.items():
                self.assertEqual(
                    actual_description,
                    expected_cpt_code_descriptions[key],
                    "descriptions are not equal for code=%r, system=%r" % key,
                )

            # Scenario
            #   A procedure code is removed when we receive the next webhook event
            # Expected behavior
            #   Irrelevant procedure codes are dropped from the PriorAuthProcedureCode table
            procedure_to_delete = data["procedureCodes"].pop()
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            expected_procedure_codes: List[FlumeCode] = []
            for procedure_code in data["procedureCodes"]:
                expected_procedure_codes.append(procedure_code)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                expected_procedure_codes,
                PriorAuthProcedureCode.objects.filter(prior_authorization=prior_auth).values(
                    "code", "system", "version"
                ),
            )
            deleted_procedure_code = PriorAuthProcedureCode.deleted_objects.first()
            self.assertIsNotNone(deleted_procedure_code)
            if deleted_procedure_code is not None:
                self.assertEqual(deleted_procedure_code.code, procedure_to_delete["code"])
                self.assertEqual(deleted_procedure_code.system, procedure_to_delete["system"])
                self.assertEqual(deleted_procedure_code.version, procedure_to_delete["version"])

            # Test that an update triggers another slack message
            MESSAGE = (
                "An update for an Approved Prior Authorization has been processed (OriginatorSystemID "
                f"{prior_auth.originator_system_id}) for user {prior_auth.person.user.id}"
            )
            mock_send_slack_message.send.assert_called_once_with(
                markdown_text=MESSAGE,
                slack_channel=SlackChannel.WAIVER_ALERTS,
            )
            # Reset the slack chat mock
            mock_send_slack_message.reset_mock()
        self.assertIsNotNone(data["serviceCategoryIDs"])
        if data["serviceCategoryIDs"] is not None:
            self.assertGreater(len(data["serviceCategoryIDs"]), 0)
            actual_service_category_alias_ids: List[str] = []
            for service_category in prior_auth.service_categories.all():
                alias_id = AliasMapping.get_alias_id_for_object(
                    obj=service_category,
                    alias_name=AliasName.FLUME,
                )
                if alias_id is not None:
                    actual_service_category_alias_ids.append(alias_id)
            # for service_category in prior_auth.
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                data["serviceCategoryIDs"],
                actual_service_category_alias_ids,
            )
            self.assertCountEqual(
                prior_auth.service_categories.all(),
                waiver.service_categories.all(),
            )

            # Scenario
            #   A service category is removed when we receive the next webhook event
            # Expected behavior
            #   Irrelevant service categories are dropped
            data["serviceCategoryIDs"].pop()
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            actual_service_category_alias_ids: List[str] = []
            for service_category in prior_auth.service_categories.all():
                alias_id = AliasMapping.get_alias_id_for_object(
                    obj=service_category,
                    alias_name=AliasName.FLUME,
                )
                if alias_id is not None:
                    actual_service_category_alias_ids.append(alias_id)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                data["serviceCategoryIDs"],
                actual_service_category_alias_ids,
            )
            # The waiver should get the updates as well
            self.assertCountEqual(
                prior_auth.service_categories.all(),
                waiver.service_categories.all(),
            )
            # Test that an update triggers another slack message
            MESSAGE = (
                "An update for an Approved Prior Authorization has been processed (OriginatorSystemID "
                f"{prior_auth.originator_system_id}) for user {prior_auth.person.user.id}"
            )
            mock_send_slack_message.send.assert_called_once_with(
                markdown_text=MESSAGE,
                slack_channel=SlackChannel.WAIVER_ALERTS,
            )
            # Reset the slack chat mock
            mock_send_slack_message.reset_mock()
            # Scenario
            #   A "bad" service category is received in the webhook that cannot be mapped
            # Expected behavior
            #   Conversion still passes with a logger message
            data["serviceCategoryIDs"].append("00000000000000")
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            prior_authorization_webhook.refresh_from_db()
            self.assertEqual(
                prior_authorization_webhook.conversion_status,
                PriorAuthorizationWebhook.ConversionStatus.SUCCEEDED,
            )
            # Scenario
            #   Service categories are received as labels instead of aliases
            #   when we receive the next webhook event
            # Expected behavior
            #   Service categories based on labels are added
            data["serviceCategoryIDs"] = self.generate_service_category_ids(use_labels_instead_of_alias=True)
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            actual_service_category_labels: List[str] = []
            for service_category in prior_auth.service_categories.all():
                actual_service_category_labels.append(service_category.label)
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                data["serviceCategoryIDs"],
                actual_service_category_labels,
            )

        self.assertIsNotNone(data["providers"])
        if data["providers"] is not None:
            self.assertGreater(len(data["providers"]), 0)
            expected_providers: List[dict] = []
            for provider in data["providers"]:
                facility = provider.get("facility", {})
                practitioner = provider.get("practitioner", {})
                expected_providers.append(
                    {
                        "street_address": facility.get("address"),
                        "address_line_1": None,
                        "address_line_2": None,
                        "city": facility.get("city"),
                        "care_organization_name": facility.get("facilityName"),
                        "first_name": practitioner.get("firstName"),
                        "last_name": practitioner.get("lastName"),
                        "middle_name": practitioner.get("middleName"),
                        "npi": practitioner.get("npi1"),
                        "state__abbreviation": facility.get("state"),
                        "zip_code": facility.get("zip"),
                        "phone": None,
                        "fax": None,
                    }
                )

            actual_providers = [
                dict(item)
                for item in PriorAuthorizationProvider.objects.filter(prior_authorization=prior_auth).values(
                    "street_address",
                    "address_line_1",
                    "address_line_2",
                    "city",
                    "care_organization_name",
                    "first_name",
                    "last_name",
                    "middle_name",
                    "npi",
                    "state__abbreviation",
                    "zip_code",
                    "phone",
                    "fax",
                )
            ]

            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(expected_providers, actual_providers)

            # Scenario
            #   A provider is removed when we receive the next webhook event
            # Expected behavior
            #   Irrelevant provider are dropped
            provider_to_delete = data["providers"].pop()
            prior_authorization_webhook.data = data
            prior_authorization_webhook.save()
            consume_prior_authorization(prior_authorization_webhook.id)
            expected_providers: List[dict] = []
            for provider in data["providers"]:
                facility = provider.get("facility", {})
                practitioner = provider.get("practitioner", {})
                expected_providers.append(
                    {
                        "street_address": facility.get("address"),
                        "address_line_1": None,
                        "address_line_2": None,
                        "city": facility.get("city"),
                        "care_organization_name": facility.get("facilityName"),
                        "first_name": practitioner.get("firstName"),
                        "last_name": practitioner.get("lastName"),
                        "middle_name": practitioner.get("middleName"),
                        "npi": practitioner.get("npi1"),
                        "state__abbreviation": facility.get("state"),
                        "zip_code": facility.get("zip"),
                        "phone": None,
                        "fax": None,
                    }
                )
            # assertCountEqual checks for item equality irrespective of order
            self.assertCountEqual(
                expected_providers,
                PriorAuthorizationProvider.objects.filter(prior_authorization=prior_auth).values(
                    "street_address",
                    "address_line_1",
                    "address_line_2",
                    "city",
                    "care_organization_name",
                    "first_name",
                    "last_name",
                    "middle_name",
                    "npi",
                    "state__abbreviation",
                    "zip_code",
                    "phone",
                    "fax",
                ),
            )
            deleted_provider = PriorAuthorizationProvider.deleted_objects.first()
            self.assertIsNotNone(deleted_provider)
            if deleted_provider is not None:
                self.assertEqual(deleted_provider.first_name, provider_to_delete["practitioner"]["firstName"])
        self.assertIsNotNone(data["serviceQuantity"])
        if data["serviceQuantity"] is not None:
            self.assertIsNotNone(prior_auth.service_quantity)
            self.assertEqual(prior_auth.service_quantity.value, data["serviceQuantity"]["visits"])
            self.assertEqual(prior_auth.service_quantity.unit, ServiceQuantityUnitConfig.VISITS)
        data["serviceQuantity"] = self.generate_service_quantity(unit=ServiceQuantityUnitConfig.DAYS)
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_auth.refresh_from_db()
        self.assertIsNotNone(data["serviceQuantity"])
        if data["serviceQuantity"] is not None:
            self.assertIsNotNone(prior_auth.service_quantity)
            self.assertEqual(prior_auth.service_quantity.unit, ServiceQuantityUnitConfig.DAYS)
            self.assertEqual(prior_auth.service_quantity.value, data["serviceQuantity"]["days"])
        # Nuke service quantity from flume
        data["serviceQuantity"] = None
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_auth.refresh_from_db()
        self.assertIsNotNone(prior_auth.service_quantity.deleted)

        # Let's test a case where we have an approved PA but not effective through date, this should throw an error
        bad_data = deepcopy(data)
        bad_data["effectiveThrough"] = ""
        bad_prior_authorization_webhook: PriorAuthorizationWebhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=bad_data,
        )
        consume_prior_authorization(bad_prior_authorization_webhook.id)
        bad_prior_authorization_webhook.refresh_from_db()
        self.assertIsNotNone(bad_prior_authorization_webhook.prior_authorization)
        self.assertEqual(bad_prior_authorization_webhook.conversion_status, "succeeded")

    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.send_slack_message")
    def test_missing_prior_auth_case(self, mock_send_slack_message):
        fake = Faker()
        person: Person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]
        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )
        # Let's test the happy path
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()
        self.assertTrue(
            AliasMapping.objects.filter(
                object_id=prior_authorization_webhook.prior_authorization.id,
                content_type__model="priorauthorization",
                alias_name=AliasName.FLUME,
                alias_id=payload["data"]["id"],
            ).exists(),
            "failed to create alias mapping for flume webhook",
        )
        prior_auth: PriorAuthorization = prior_authorization_webhook.prior_authorization
        waiver: Waiver = Waiver.objects.get(prior_authorization=prior_auth)
        self.assertIsNotNone(waiver)
        self.assertEqual(prior_authorization_webhook.conversion_status, "succeeded")

        # Scenario 1
        #   Service Category list is empty
        # Expected behavior
        #   Prior Auth missing details Case is created
        data["serviceCategoryIDs"] = []
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)

        # The waiver should get the updates as well
        self.assertCountEqual(
            prior_auth.service_categories.all(),
            waiver.service_categories.all(),
        )

        # A prior auth missing details case should be created
        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
            status_category=StatusCategory.NOT_STARTED,
        ).first()
        self.assertIsNotNone(missing_details_case)

        # Scenario 2
        # Case above was marked done, but Service Category list is empty
        # Expected behavior
        #   Prior Auth missing details Case is reopened
        missing_details_case.status_category = StatusCategory.COMPLETE
        missing_details_case.save()
        consume_prior_authorization(prior_authorization_webhook.id)

        # A prior auth missing details case should be reopened
        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
            status_category=StatusCategory.NOT_STARTED,
        ).first()
        self.assertIsNotNone(missing_details_case)

        # Scenario 3
        # Case is open, Service Category list is not empty
        # Expected behavior
        #   Prior Auth missing details Case is auto closed
        data["serviceCategoryIDs"] = self.generate_service_category_ids()
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)

        # A prior auth missing details case should be closed
        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
            status_category=StatusCategory.COMPLETE,
        ).first()
        self.assertIsNotNone(missing_details_case)

        # Scenario 4
        # Case above was marked done, but effective through is empty
        # Expected behavior
        #   Prior Auth Missing Details Case is reopened
        missing_details_case.status_category = StatusCategory.COMPLETE
        missing_details_case.save()

        data["effectiveThrough"] = ""
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)

        # A prior auth missing details case should be closed
        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
            status_category=StatusCategory.NOT_STARTED,
        ).first()
        self.assertIsNotNone(missing_details_case)
        self.assertEqual(missing_details_case.notes, "Member Key: 00001 \n Missing Service categories \n ")

        # Scenario 5
        # Case above was marked done, but effective through, service category is empty
        # Expected behavior
        #   Prior Auth Missing Details Case is reopened
        missing_details_case.status_category = StatusCategory.COMPLETE
        missing_details_case.save()

        data["effectiveThrough"] = ""
        data["serviceCategoryIDs"] = ""
        prior_authorization_webhook.data = data
        prior_authorization_webhook.save()
        consume_prior_authorization(prior_authorization_webhook.id)

        # A prior auth missing details case should be closed
        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
            status_category=StatusCategory.NOT_STARTED,
        ).first()
        self.assertIsNotNone(missing_details_case)
        self.assertEqual(missing_details_case.notes, "Member Key: 00001 \n Missing Service categories \n ")

    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.send_slack_message")
    def test_conversion_for_denied_prior_authorizations(self, mock_send_slack_message):
        fake = Faker()

        person: Person = PersonFactory()
        member_id = fake.uuid4()
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        prior_auth: PriorAuthorization = PriorAuthorizationFactory(person=person)

        alias_id = fake.uuid4()

        AliasMapping.objects.create(
            object_id=prior_auth.id,
            content_type=get_content_type(prior_auth),
            alias_name=AliasName.FLUME,
            alias_id=alias_id,
        )

        payload = self.generate_payload(
            memberKey=member_id,
            id=alias_id,
        )

        data = payload["data"]
        data["determination"] = "Denied"

        # Let's say service categories are empty, we should still create the Nav Request case
        # as expected but would not create the missing service categories case
        data["serviceCategoryIDs"] = []

        # We likely will not have an effective through date
        # with denials (effective through dates are tested in the approval scenario anyway)
        data["effectiveThrough"] = ""

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )

        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        self.assertEqual(prior_authorization_webhook.prior_authorization, prior_auth)
        self.assertEqual(prior_authorization_webhook.conversion_status, "succeeded")

        missing_details_case = Case.objects.filter(
            person=prior_auth.person,
            category__unique_key=PRIOR_AUTH_MISSING_DETAILS,
        )
        self.assertFalse(missing_details_case.exists())

        self.assertTrue(
            AliasMapping.objects.filter(
                object_id=prior_auth.id,
                content_type__model="priorauthorization",
                alias_name=AliasName.FLUME,
                alias_id=payload["data"]["id"],
            ).exists(),
            "accidentally removed mapping for flume webhook",
        )

        # Test that the fields are updated
        prior_auth.refresh_from_db()
        self.assertEqual(prior_auth.comment, data["comment"])
        self.assertEqual(prior_auth.determination, data["determination"])
        self.assertEqual(prior_auth.effective_from, self.as_date(data["effectiveFrom"]))
        self.assertEqual(prior_auth.effective_through, None)
        self.assertEqual(prior_auth.person, person)
        self.assertEqual(prior_auth.status, data["status"])
        self.assertEqual(prior_auth.type, data["type"])
        self.assertEqual(prior_auth.vendor_create_date, self.as_date(data["createDate"]))
        self.assertEqual(prior_auth.vendor_update_date, self.as_date(data["updateDate"]))

        # Assert that a message was sent to the slack channel even for a Denial
        MESSAGE = (
            "An update for a Denied Prior Authorization has been processed (OriginatorSystemID "
            f"{prior_auth.originator_system_id}) for person {prior_auth.person.id} (no user id)"
        )
        mock_send_slack_message.send.assert_called_once_with(
            markdown_text=MESSAGE,
            slack_channel=SlackChannel.WAIVER_ALERTS,
        )

        # Reset the slack chat mock
        mock_send_slack_message.reset_mock()

        mock_send_slack_message.send.assert_not_called()

    def test_when_cpt_code_version_does_not_exist_in_database(self):
        fake = Faker()
        person: Person = PersonFactory()
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        # Generate the payload with CPT code version == "2022"
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]
        descriptions = self.save_cpt_codes_and_return_descriptions(data["procedureCodes"])

        # Now go through the procedureCodes and give them a different version, for example "4"
        # This corresponds to the version Flume sends us in its procedure codes
        # We store our CPT codes with versions names such as "2021" and "2022"
        for procedure_code in data["procedureCodes"]:
            procedure_code["version"] = "4"

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )

        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Check that the CPT code versions are all equal to the overridden version
        # Also Double-check that the other values are also properly mapped:
        for procedure_code in prior_authorization_webhook.prior_authorization.procedure_codes.all():
            self.assertIsNotNone(procedure_code.content_object)
            self.assertEqual(procedure_code.content_object.version, "2022")

            self.assertEqual(procedure_code.code, procedure_code.content_object.code)
            expected_description = descriptions[(procedure_code.code, procedure_code.system)]
            self.assertEqual(procedure_code.content_object.description, expected_description)

    def test_when_cpt_code_version_already_exists_in_database(self):
        fake = Faker()
        person: Person = PersonFactory()
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        # Generate the payload with CPT code version == "2022"
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]
        procedure_code: PriorAuthProcedureCode

        OVERRIDDEN_CPT_CODE = "4"
        for procedure_code in data["procedureCodes"]:
            procedure_code["version"] = OVERRIDDEN_CPT_CODE

        descriptions = self.save_cpt_codes_and_return_descriptions(data["procedureCodes"])

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )

        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Check that the CPT code versions are all equal to the overridden version
        # Also Double-check that the other values are also properly mapped:
        for procedure_code in prior_authorization_webhook.prior_authorization.procedure_codes.all():
            self.assertIsNotNone(procedure_code.content_object)
            self.assertEqual(procedure_code.content_object.version, OVERRIDDEN_CPT_CODE)

            self.assertEqual(procedure_code.code, procedure_code.content_object.code)
            expected_description = descriptions[(procedure_code.code, procedure_code.system)]
            self.assertEqual(procedure_code.content_object.description, expected_description)

    @mock.patch("firefly.modules.referral.utils.prior_auth_utils.send_slack_message")
    def test_steerage_update(self, mock_send_slack_message):
        # CASE - 1
        # Create a webhook payload and consume webhook
        # Consuming webhook should update steerage details and

        fake = Faker()
        person: Person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )
        member_id = fake.uuid4()
        # create alias mapping between flume member key and person
        AliasMapping.objects.create(
            content_object=person,
            alias_name=AliasName.FLUME,
            alias_id=member_id,
            object_id=person.id,
        )
        payload = self.generate_payload(memberKey=member_id)
        data: FlumePriorAuthorization = payload["data"]
        data["serviceQuantity"] = self.generate_service_quantity(unit=ServiceQuantityUnitConfig.DAYS)

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=data,
        )

        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        prior_auth: PriorAuthorization = prior_authorization_webhook.prior_authorization
        waiver: Waiver = Waiver.objects.get(prior_authorization=prior_auth)
        self.assertIsNotNone(waiver)
        self.assertEqual(prior_authorization_webhook.conversion_status, "succeeded")

        # Test that the fields are updated
        prior_auth.refresh_from_db()
        self.assertEqual(prior_auth.comment, data["comment"])
        self.assertEqual(prior_auth.determination, data["determination"])
        self.assertEqual(prior_auth.effective_from, self.as_date(data["effectiveFrom"]))
        self.assertEqual(prior_auth.effective_through, self.as_date(data["effectiveThrough"]))
        self.assertEqual(prior_auth.person, person)
        self.assertEqual(prior_auth.status, data["status"])
        self.assertEqual(prior_auth.type, data["type"])
        self.assertEqual(prior_auth.vendor_create_date, self.as_date(data["createDate"]))
        self.assertEqual(prior_auth.vendor_update_date, self.as_date(data["updateDate"]))

        # Test data on the Steerage
        steerage: Steerage = Steerage.objects.filter(waiver=waiver).first()
        self.assertIsNotNone(steerage)
        self.assertEqual(steerage.effective_from, self.as_date(data["effectiveFrom"]))
        self.assertEqual(steerage.effective_through, self.as_date(data["effectiveThrough"]))
        steerage_service_quantity: SteerageServiceQuantity = SteerageServiceQuantity.objects.filter(
            steerage=steerage
        ).first()
        self.assertEqual(steerage_service_quantity.unit, "days")
        self.assertEqual(steerage_service_quantity.value, data["serviceQuantity"]["days"])
        waiver_categories = [category.label for category in waiver.service_categories.all()]
        prior_auth_categories = [category.label for category in prior_auth.service_categories.all()]
        self.assertEqual(waiver_categories, prior_auth_categories)

        # CASE - 2
        # Lock the steerage and update all the webhook details
        # Consuming webhook should update steerage details and
        # unlock the steerage with case status as "Prior auth received"

        existing_service_labels = ", ".join(prior_auth.service_categories.values_list("label", flat=True))
        steerage.scheduling_date = datetime.today().date()
        steerage.is_locked = True
        steerage.save()

        cases = Case.objects.all()
        self.assertEqual(1, len(cases))
        care_pass_from_pa_case: Case = cases[0]
        care_pass_from_pa_case.action = ReferralRequestCaseStatuses.MEMBER_SCHEDULED
        care_pass_from_pa_case.save()
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, MEMBER_SCHEDULED_ACTION)

        # Update the payload
        updated_data: FlumePriorAuthorization = data
        updated_data["serviceQuantity"] = self.generate_service_quantity(unit=ServiceQuantityUnitConfig.VISITS)
        updated_data["effectiveFrom"] = str(fake.date())
        updated_data["effectiveThrough"] = str(date.today() + timedelta(days=1))
        updated_data["serviceCategoryIDs"] = self.generate_service_category_ids()
        updated_data["providers"] = self.generate_provider_list()

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=updated_data,
        )
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Test updated_data on the Steerage
        waiver.refresh_from_db()
        steerage: Steerage = Steerage.objects.filter(waiver=waiver).first()
        self.assertIsNotNone(steerage)
        self.assertEqual(steerage.effective_from, self.as_date(updated_data["effectiveFrom"]))
        self.assertEqual(steerage.effective_through, self.as_date(updated_data["effectiveThrough"]))
        steerage_service_quantity: SteerageServiceQuantity = SteerageServiceQuantity.objects.filter(
            steerage=steerage
        ).first()
        self.assertEqual(steerage_service_quantity.unit, "visits")
        self.assertEqual(steerage_service_quantity.value, updated_data["serviceQuantity"]["visits"])
        waiver_categories = [category.label for category in waiver.service_categories.all()]
        prior_auth.refresh_from_db()
        prior_auth_categories = [category.label for category in prior_auth.service_categories.all()]
        self.assertEqual(waiver_categories, prior_auth_categories)

        # Check steerage is unlocked and status changed to prior auth received
        self.assertEqual(steerage.is_locked, False)
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED)
        new_service_labels = ", ".join(prior_auth.service_categories.values_list("label", flat=True))
        expected_notes = (
            " Prior Auth updated "
            + (prior_auth.updated_at.strftime("%d/%m/%Y") if prior_auth.updated_at else "")
            + ", service categories moving from '"
            + existing_service_labels
            + "' to '"
            + new_service_labels
            + "', and updated provider details."
        )
        self.assertEqual(care_pass_from_pa_case.notes, expected_notes)

        # CASE - 3
        # Lock the steerage and update provider details
        # Consuming webhook should update provider on prior auth and
        # unlock the steerage with case status as "Prior auth received"
        steerage.scheduling_date = datetime.today().date()
        steerage.is_locked = True
        steerage.save()

        cases = Case.objects.all()
        self.assertEqual(1, len(cases))
        care_pass_from_pa_case = cases[0]
        care_pass_from_pa_case.action = ReferralRequestCaseStatuses.MEMBER_SCHEDULED
        care_pass_from_pa_case.notes = "TEST"
        care_pass_from_pa_case.save()
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, MEMBER_SCHEDULED_ACTION)

        # Update the payload
        updated_data: FlumePriorAuthorization = data
        updated_data["providers"] = self.generate_provider_list()

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=updated_data,
        )
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Test updated_data on the Steerage
        waiver.refresh_from_db()
        steerage: Steerage = Steerage.objects.filter(waiver=waiver).first()
        self.assertIsNotNone(steerage)
        self.assertEqual(steerage.effective_from, self.as_date(updated_data["effectiveFrom"]))
        self.assertEqual(steerage.effective_through, self.as_date(updated_data["effectiveThrough"]))
        steerage_service_quantity: SteerageServiceQuantity = SteerageServiceQuantity.objects.filter(
            steerage=steerage
        ).first()
        self.assertEqual(steerage_service_quantity.unit, "visits")
        self.assertEqual(steerage_service_quantity.value, updated_data["serviceQuantity"]["visits"])
        waiver_categories = [category.label for category in waiver.service_categories.all()]
        prior_auth_categories = [category.label for category in prior_auth.service_categories.all()]
        self.assertEqual(waiver_categories, prior_auth_categories)

        # Check steerage is unlocked and status changed to prior auth received
        self.assertEqual(steerage.is_locked, False)
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED)
        # Notes should append to existing notes text
        new_service_labels = ", ".join(prior_auth.service_categories.values_list("label", flat=True))
        expected_notes = (
            "TEST - Prior Auth updated "
            + (prior_auth.updated_at.strftime("%d/%m/%Y") if prior_auth.updated_at else "")
            + ", and updated provider details."
        )
        self.assertEqual(care_pass_from_pa_case.notes, expected_notes)

        # CASE - 4
        # Lock the steerage and update Service Category
        # Consuming webhook should update provider on prior auth and
        # unlock the steerage with case status as "Prior auth received"
        steerage.scheduling_date = datetime.today().date()
        steerage.is_locked = True
        steerage.save()

        cases = Case.objects.all()
        self.assertEqual(1, len(cases))
        care_pass_from_pa_case = cases[0]
        care_pass_from_pa_case.action = ReferralRequestCaseStatuses.MEMBER_SCHEDULED
        care_pass_from_pa_case.notes = "TEST"
        care_pass_from_pa_case.save()
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, MEMBER_SCHEDULED_ACTION)

        # Update the payload
        existing_service_labels = ", ".join(prior_auth.service_categories.values_list("label", flat=True))
        updated_data: FlumePriorAuthorization = data
        updated_data["serviceCategoryIDs"] = self.generate_service_category_ids()

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=updated_data,
        )
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Test updated_data on the Steerage
        waiver.refresh_from_db()
        steerage: Steerage = Steerage.objects.filter(waiver=waiver).first()
        self.assertIsNotNone(steerage)
        self.assertEqual(steerage.effective_from, self.as_date(updated_data["effectiveFrom"]))
        self.assertEqual(steerage.effective_through, self.as_date(updated_data["effectiveThrough"]))
        steerage_service_quantity: SteerageServiceQuantity = SteerageServiceQuantity.objects.filter(
            steerage=steerage
        ).first()
        self.assertEqual(steerage_service_quantity.unit, "visits")
        self.assertEqual(steerage_service_quantity.value, updated_data["serviceQuantity"]["visits"])
        waiver_categories = [category.label for category in waiver.service_categories.all()]
        prior_auth_categories = [category.label for category in prior_auth.service_categories.all()]
        self.assertEqual(waiver_categories, prior_auth_categories)

        # Check steerage is unlocked and status changed to prior auth received
        self.assertEqual(steerage.is_locked, False)
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED)
        # Notes should append to existing notes text
        new_service_labels = ", ".join(prior_auth.service_categories.values_list("label", flat=True))
        expected_notes = (
            "TEST - Prior Auth updated "
            + (prior_auth.updated_at.strftime("%d/%m/%Y") if prior_auth.updated_at else "")
            + ", service categories moving from '"
            + existing_service_labels
            + "' to '"
            + new_service_labels
            + "'"
        )
        self.assertEqual(care_pass_from_pa_case.notes, expected_notes)

        # CASE - 5
        # Lock the steerage and Do not update anything
        # Consuming webhook should not unlock the steerage with case status as "Prior auth received"
        steerage.scheduling_date = datetime.today().date()
        steerage.is_locked = True
        steerage.save()

        cases = Case.objects.all()
        self.assertEqual(1, len(cases))
        care_pass_from_pa_case = cases[0]
        care_pass_from_pa_case.action = ReferralRequestCaseStatuses.MEMBER_SCHEDULED
        care_pass_from_pa_case.save()
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, MEMBER_SCHEDULED_ACTION)

        prior_authorization_webhook = PriorAuthorizationWebhook.objects.create(
            event_id=payload.get("id"),
            event_type=payload.get("eventType"),
            event_timestamp=payload.get("timestamp"),
            data=updated_data,
        )
        consume_prior_authorization(prior_authorization_webhook.id)
        prior_authorization_webhook.refresh_from_db()

        # Test updated_data on the Steerage
        waiver.refresh_from_db()
        steerage: Steerage = Steerage.objects.filter(waiver=waiver).first()
        self.assertIsNotNone(steerage)
        self.assertEqual(steerage.effective_from, self.as_date(updated_data["effectiveFrom"]))
        self.assertEqual(steerage.effective_through, self.as_date(updated_data["effectiveThrough"]))
        steerage_service_quantity: SteerageServiceQuantity = SteerageServiceQuantity.objects.filter(
            steerage=steerage
        ).first()
        self.assertEqual(steerage_service_quantity.unit, "visits")
        self.assertEqual(steerage_service_quantity.value, updated_data["serviceQuantity"]["visits"])
        waiver_categories = [category.label for category in waiver.service_categories.all()]
        prior_auth_categories = [category.label for category in prior_auth.service_categories.all()]
        self.assertEqual(waiver_categories, prior_auth_categories)

        # Check steerage is not unlocked and status not changed
        self.assertEqual(steerage.is_locked, True)
        care_pass_from_pa_case.refresh_from_db()
        self.assertEqual(care_pass_from_pa_case.status, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)
        self.assertEqual(care_pass_from_pa_case.notes, expected_notes)

    @mock.patch("firefly.core.services.auth0.permissions.IsValidatedFlumeClientCredentialsRequest.has_permission")
    @mock.patch("firefly.modules.referral.tasks.consume_prior_authorization.send_with_options")
    def test_webhook_endpoint_when_toggle_is_active(
        self, mock_consume_prior_auth_send_with_options, mock_has_permission
    ):
        mock_has_permission.return_value = True
        # Mock IsValidatedFlumeClientCredentialsRequest to return True
        payload = self.generate_payload()
        response = self.client.post("/referral/prior_authorization/", payload, format="json")

        self.assertEqual(response.status_code, 201)

        # Test that metadata has been saved
        try:
            prior_authorization_webhook = PriorAuthorizationWebhook.objects.get(event_id=payload["id"])
        except PriorAuthorizationWebhook.DoesNotExist:
            raise AssertionError("failed to save webhook to the database")

        self.assertEqual(prior_authorization_webhook.event_id, payload["id"])
        self.assertEqual(prior_authorization_webhook.event_type, payload["eventType"])
        self.assertEqual(
            prior_authorization_webhook.event_timestamp.replace(tzinfo=None),
            datetime.fromisoformat(payload["timestamp"]),
        )
        self.assertEqual(prior_authorization_webhook.conversion_status, "pending")

        # Test that the rabbit-mq task has been called
        mock_consume_prior_auth_send_with_options.assert_called_once_with(
            args=(prior_authorization_webhook.id,), delay=0
        )


class TestPriorAuthorization(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(self.patient.person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=self.patient.person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )

        # Setup referral initation case with the state machine
        referral_initiation_state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": "*", "trigger": "auto_close", "system_action": "system_close"},
                {"dest": "Done", "source": [], "trigger": "Done"},
                {"dest": "Steerage Not Needed", "source": "*", "trigger": "Steerage Not Needed"},
                {
                    "dest": "Steerage Not Needed",
                    "source": "*",
                    "trigger": "deferred",
                    "system_action": "system_deferred",
                },
                {"dest": "Complete in Elation", "source": ["New"], "trigger": "Complete in Elation"},
                {"dest": "Member Scheduled", "source": "*", "trigger": "Member Scheduled"},
                {"dest": "Location Search", "source": ["New", "Complete in Elation"], "trigger": "Location Search"},
                {
                    "dest": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                    "source": "*",
                    "trigger": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                },
            ],
            "initial_state": "New",
            "state_with_categories": [
                {
                    "state": {"name": "New", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 2, "use_business_days": True}],
                },
                {"state": {"name": "Complete in Elation", "ignore_invalid_triggers": None}, "category": "in_progress"},
                {
                    "state": {
                        "name": "Steerage Not Needed",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "deferred",
                },
                {
                    "state": {
                        "name": "Location Search",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": "Done",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
                {
                    "state": {
                        "name": "Member Scheduled",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
            ],
        }
        referral_initiation_state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
            title="referral_request_statemachine",
            content=referral_initiation_state_machine_content,
        )
        self.referral_initiation_category = CaseCategory.objects.create(
            unique_key=REFERRAL_REQUEST,
            state_machine_definition=referral_initiation_state_machine_definition,
            title="Referral Initiation",
        )
        CaseRelationTemplate.objects.create(
            content_type=ContentType.objects.get_for_model(Steerage),
            case_category=self.referral_initiation_category,
            on_after_creation="associate_referral_and_waiver_if_coverage_member",
        )

    def test_only_valid_determinations(self):
        person: Person = PersonFactory.create()
        # Test an exception is logged if we have an unexpected determination
        with self.assertLogs() as logs:
            bad_determination_prior_auth = PriorAuthorizationFactory.create(
                person=person,
                determination="invalid_determination",
                effective_through=datetime.strftime(date.today(), "%Y-%m-%d"),
            )

        # One for the wrong determination
        self.assertEqual(len(logs.records), 1)
        determination: str = bad_determination_prior_auth.determination
        expected_message: str = (
            f"Unexpected determination for Prior Authorization {bad_determination_prior_auth.pk}: {determination}"
        )
        self.assertEqual(
            logs.records[-1].getMessage(),
            expected_message,
        )

    def test_effective_through_date_saving(self):
        my_person: Person = PersonUserFactory.create()
        add_person_to_program(my_person, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(my_person, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=my_person,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )
        prior_auth = PriorAuthorizationFactory.create(person=my_person, determination="")
        today = date.today()

        # the date can be saved as a formatted string
        prior_auth.effective_through = datetime.strftime(today, "%Y-%m-%d")
        try:
            prior_auth.save()
        except Exception as err:
            raise AssertionError(
                "unable to save effective_through date as string, raised a %s" % (type(err).__name__,)
            ) from None
        else:
            prior_auth.refresh_from_db()
            self.assertEqual(prior_auth.effective_through, today)

        # now test that it can be saved as a straight python date object
        prior_auth.effective_through = today
        try:
            prior_auth.save()
        except Exception as err:
            raise AssertionError(
                "unable to save effective_through date as a date, raised a %s" % (type(err).__name__,)
            ) from None
        else:
            prior_auth.refresh_from_db()
            self.assertEqual(prior_auth.effective_through, today)


class TestCreateOrUpdatePriorAuthCase(FireflyTestCase):
    """Linking PA based on originator ID"""

    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)

        self.person_with_user: Person = PersonUserFactory.create()
        add_person_to_program(self.person_with_user, ProgramCodes.PRIMARY_CARE)
        add_person_to_program(self.person_with_user, ProgramCodes.BENEFIT)
        start_date = datetime.now()
        ProgramEnrollment.objects.update_or_create(
            person=self.person_with_user,
            program_id=ProgramCodes.BENEFIT,
            defaults={
                "period": (start_date, None),
                "status": BenefitProgramStatus.ELECTED,
            },
        )

        # Setup referral initation case with the state machine
        referral_initiation_state_machine_content = {
            "transitions": [
                {"dest": "Done", "source": "*", "trigger": "auto_close", "system_action": "system_close"},
                {"dest": "Done", "source": [], "trigger": "Done"},
                {"dest": "Steerage Not Needed", "source": "*", "trigger": "Steerage Not Needed"},
                {
                    "dest": "Steerage Not Needed",
                    "source": "*",
                    "trigger": "deferred",
                    "system_action": "system_deferred",
                },
                {"dest": "Complete in Elation", "source": ["New"], "trigger": "Complete in Elation"},
                {"dest": "Member Scheduled", "source": "*", "trigger": "Member Scheduled"},
                {"dest": "Location Search", "source": ["New", "Complete in Elation"], "trigger": "Location Search"},
                {
                    "dest": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                    "source": "*",
                    "trigger": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                },
            ],
            "initial_state": "New",
            "state_with_categories": [
                {
                    "state": {"name": "New", "ignore_invalid_triggers": None},
                    "category": "pending",
                    "due_date": [{"days": 2, "use_business_days": True}],
                },
                {"state": {"name": "Complete in Elation", "ignore_invalid_triggers": None}, "category": "in_progress"},
                {
                    "state": {
                        "name": "Steerage Not Needed",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "deferred",
                },
                {
                    "state": {
                        "name": "Location Search",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED,
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "in_progress",
                },
                {
                    "state": {
                        "name": "Done",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
                {
                    "state": {
                        "name": "Member Scheduled",
                        "on_enter": ["match_case_status_to_steerage_disposition"],
                        "ignore_invalid_triggers": None,
                    },
                    "category": "complete",
                },
            ],
        }
        referral_initiation_state_machine_definition, _ = StateMachineDefinition.objects.get_or_create(
            title="referral_request_statemachine",
            content=referral_initiation_state_machine_content,
        )
        self.referral_initiation_category = CaseCategory.objects.create(
            unique_key=REFERRAL_REQUEST,
            state_machine_definition=referral_initiation_state_machine_definition,
            title="Referral Initiation",
        )
        CaseRelationTemplate.objects.create(
            content_type=ContentType.objects.get_for_model(Steerage),
            case_category=self.referral_initiation_category,
            on_after_creation="associate_referral_and_waiver_if_coverage_member",
        )

        # This sets up the REFERRAL_REQUEST case
        PriorAuthorizationFactory.create()
        self.approval_category = CaseCategory.objects.get(
            unique_key=REFERRAL_REQUEST,
        )

    def test_create_new_case(self):
        # Create a Prior Auth but no existing case found -> this should create a brand new case
        today = date.today()
        service_category_1 = ServiceCategoryFactory.create()
        service_category_2 = ServiceCategoryFactory.create()
        cases = Case.objects.all()
        self.assertEqual(0, len(cases))

        pa: PriorAuthorization = PriorAuthorizationFactory.create(
            person=self.person_with_user,
            determination="Denied",
            effective_through=today,
            service_categories=(service_category_1, service_category_2),
        )

        # Create service quantity for the prior auth
        pa.effective_through = today
        pa.save()
        pa.refresh_from_db()

        cases = Case.objects.all()
        self.assertEqual(1, len(cases))

        referral_initiation_case = cases[0]
        self.assertEqual(referral_initiation_case.category, self.approval_category)

        # There should be one case relation for steerage
        case_relations = referral_initiation_case.relations.all()
        self.assertEqual(1, len(case_relations))
        self.assertEqual(case_relations[0].content_type.model, "steerage")

        steerage = case_relations[0].content_object
        self.assertIsNotNone(steerage.waiver)
        self.assertEqual(steerage.waiver.prior_authorization.id, pa.id)
        self.assertTrue(steerage.prior_authorization_required)
        self.assertEqual(steerage.prior_authorization_system_id, pa.originator_system_id)
        self.assertEqual(steerage.is_initiated_from_lucian, True)

        # Next, let's test adding another Denied Prior Auth into the mix, so there are
        # two for the same patient. We want to make sure a new case is created
        # for this new prior auth, not that the old one is found/reused.
        new_pa = PriorAuthorizationFactory.create(
            person=self.person_with_user,
            determination="Denied",
            effective_through=today,
        )
        cases = Case.objects.order_by("id").all()
        self.assertEqual(2, len(cases))

        new_case = cases[1]
        self.assertEqual(new_case.category, self.approval_category)

        new_relations = new_case.relations.all()
        has_steerage = False
        steerage = None
        self.assertEqual(1, len(new_relations))
        for relation in new_relations:
            if relation.content_type.model == "steerage":
                has_steerage = True
                steerage = relation.content_object

        self.assertTrue(has_steerage)
        self.assertIsNotNone(steerage.waiver)
        self.assertEqual(steerage.waiver.prior_authorization.id, new_pa.id)
        self.assertEqual(steerage.prior_authorization_system_id, new_pa.originator_system_id)

    def test_link_to_existing_case(self):
        today = date.today()
        yesterday = today - timedelta(days=1)

        # Create a prior auth that is Approved but for a Past Date
        service_category_1 = ServiceCategoryFactory.create()
        service_category_2 = ServiceCategoryFactory.create()
        prior_auth: PriorAuthorization = PriorAuthorizationFactory.create(
            person=self.person_with_user,
            determination="Approved",
            effective_through=yesterday,
            service_categories=(service_category_1, service_category_2),
            originator_system_id="1-123123.1-b",
        )

        # Ensure an approval case was not created for this past date
        cases = Case.objects.all()
        self.assertEqual(0, len(cases))

        # Let's create a referral initiation case that has the same originator ID
        ref_initiation_case = CaseFactory.create(
            person=self.person_with_user, category=self.referral_initiation_category
        )

        # Find the steerage
        steerage: Steerage | None = None
        for relation in ref_initiation_case.relations.iterator():
            if relation.content_type == ContentType.objects.get_for_model(Steerage):
                steerage = relation.content_object
                break

        self.assertIsNotNone(steerage)
        self.assertIsNone(steerage.waiver)

        steerage.prior_authorization_required = True
        steerage.prior_authorization_system_id = prior_auth.originator_system_id
        steerage.save()

        # Now set the date for tomorrow, and check that ...
        prior_auth.effective_through = today
        prior_auth.save()
        prior_auth.refresh_from_db()
        steerage.refresh_from_db()
        prior_auth.refresh_from_db()
        ref_initiation_case.refresh_from_db()

        # - the case is related to a steerage, a waiver
        case_relations = ref_initiation_case.relations.order_by("id").all()
        self.assertEqual(1, len(case_relations))
        steerage_relation = case_relations[0]
        self.assertEqual(steerage_relation.content_type.model, "steerage")

        # - the waiver points to the prior auth, has all of its service
        # categories, and effective from and through dates
        waiver = steerage.waiver
        self.assertIsNotNone(waiver)
        self.assertEqual(waiver.prior_authorization, prior_auth)
        self.assertEqual(len(prior_auth.service_categories.all()), 2)
        self.assertCountEqual(waiver.service_categories.all(), prior_auth.service_categories.all())
        # - And it's correctly related to the Steerage, with the correct effective from and through
        self.assertEqual(steerage.waiver_id, waiver.id)
        self.assertEqual(steerage.is_initiated_from_lucian, True)
        # - and no extraneous Steerages were created
        self.assertEqual(1, Steerage.objects.count())
        # Case status should be Prior Auth Received
        self.assertEqual(ref_initiation_case.status, ReferralRequestCaseStatuses.PRIOR_AUTH_RECEIVED)

        # Do an additional save, and ensure that no new case was created, since
        # a case of this category type already exists.
        prior_auth.save()
        case_count = Case.objects.count()
        self.assertEqual(1, case_count)
        # Lock the Steerage and move the Case to Done, as if Ops did so.
        steerage.lock(user=None)
        steerage.save()
        ref_initiation_case.action = ReferralRequestCaseStatuses.MEMBER_SCHEDULED
        ref_initiation_case.save()
        prior_auth.save()
        prior_auth.refresh_from_db()
        # And assert that ...
        # - no new case was created
        case_count = Case.objects.count()
        self.assertEqual(1, case_count)
