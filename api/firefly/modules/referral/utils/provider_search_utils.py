import logging
import operator
import re
import string
from functools import reduce
from typing import Any, Iterable, List, Optional, Set, Union, cast

import waffle
from django.db.models import (  # Used for type casting, not schema definition.
    CharField,  # noqa: TID251
    F,
    Func,
    Prefetch,
    Q,
    QuerySet,
)
from django.db.models.expressions import Value
from typing_extensions import TypedDict

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.services.ribbon.client import get_facilities, get_providers
from firefly.core.services.ribbon.types import (
    LocationAvailability,
    RibbonAddress,
    RibbonFacilityParams,
    RibbonFaxNumber,
    RibbonLocation,
    RibbonLocationResponse,
    RibbonNetwork,
    RibbonPerformanceData,
    RibbonPhoneNumber,
    RibbonProvider,
    RibbonProviderParams,
    RibbonProviderResponse,
    RibbonResponse,
)
from firefly.modules.facts.models import ClinicalFocusArea, SpecialtyGroup
from firefly.modules.network.models import (
    AgreementTypeConfig,
    Availability,
    ContactInformation,
    CuratedProvider,
    CuratedProviderCareOrgNameChoice,
    CuratedProviderPartnership,
    CuratedProviderRankingLevelConfig,
    Partnership,
    PartnershipTypeConfig,
    Ranking,
    TaxIdentifier,
)
from firefly.modules.network.types import FireflySpecialtyGroup
from firefly.modules.network.utils.utils import (
    get_cached_alias_id_from_specialty,
    get_partner_service_level_agreement_filter,
)
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import get_person_program_enrollments
from firefly.modules.referral.constants import (
    WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2,
    AnnotatedSanitizedCareOrgNameCuratedProvider,
    MemberServiceLevel,
    RecommendationReasonLabel,
)
from firefly.modules.referral.models import RecommendationStatus, Steerage, SteerageProvider

logger = logging.getLogger(__name__)


class ContactDetail(TypedDict):
    phone: str
    details: str


class FaxDetail(TypedDict):
    fax: str
    details: str


class AddressDetail(TypedDict):
    address_line_1: Optional[str]
    address_line_2: Optional[str]
    city: Optional[str]
    state: Optional[str]
    zip: Optional[str]
    street: Optional[str]


class PricePercentiles(TypedDict):
    p_25: float
    p_50: float
    p_75: float
    p_0: float


class TalonAssociatedProcedures(TypedDict):
    price: float
    procedureCode: str
    procedureName: str
    referralPercentage: float
    weightedPrice: float


class LocationGeoCode(TypedDict):
    latitude: float
    longitude: float


class LocationDetail(TypedDict):
    uuid: str
    unique_identifier: str
    name: Optional[str]
    address_details: AddressDetail
    phone_numbers: Optional[List[ContactDetail]]
    fax_numbers: Optional[List[FaxDetail]]
    distance: Optional[float]
    location_in_network: Optional[bool]
    availability: Optional[LocationAvailability]
    confidence: Optional[float]
    network_partner_agreement_type: Optional[str]
    is_verified: Optional[bool]
    partner_name: Optional[str]
    curated_provider_ranking: Optional[int]
    curated_provider_ranking_reason: Optional[str]
    tins: Optional[List[str]]
    talon_associated_procedure_prices: Optional[List[TalonAssociatedProcedures]]
    geo_code: Optional[LocationGeoCode]


# These are the raw recommendation score received from Ribbon
class RibbonRecommendationFactors(TypedDict):
    cost_score: Optional[int]
    quality_score: Optional[float]
    average_rating: Optional[float]
    number_of_ratings: Optional[float]


# These are the firefly recommendation factors scaled from 0 to 5
class FireflyRecommendationFactors(TypedDict):
    cost_score_out_of_five: Optional[float]
    quality_score_out_of_five: Optional[float]
    average_rating_out_of_five: Optional[float]
    number_of_ratings: Optional[float]
    expertise_score_out_of_five: Optional[float]


class TalonRecommendationFactors(TypedDict):
    encounter_price: Optional[float]
    expertise_score: Optional[float]
    price_percentiles: Optional[PricePercentiles]


class ProviderDetail(TypedDict):
    id: str
    npi: Optional[str]
    unique_identifier: str
    name: Optional[str]
    first_name: Optional[str]
    middle_name: Optional[str]
    last_name: Optional[str]
    locations: List[LocationDetail]
    specialties: List[str]
    languages: List[str]
    gender: Optional[str]
    # TODO: NET-1074 Fix typing after python upgrade
    # to list allowed values
    recommendation_status: Optional[str]
    # list of reason labels based on recommendations
    recommendation_reason_labels: Optional[str]
    # boolean to check whether the provider is in-network with First Health
    in_network: bool
    specialty_groups: Optional[List[FireflySpecialtyGroup]]
    location_types: Optional[List[str]]
    # distance and network_partner_agreement_type have been pulled out from
    # the location
    distance: Optional[float]
    network_partner_agreement_type: Optional[str]
    # Is used to calculate the recommendation status for a provider
    # In the future it might be used for ranking
    # The composite score of a provider will be in a range from 0 to 5
    composite_score: Optional[float]
    # Stores the firefly scaled score of recommendation factors out of 5
    firefly_recommendation_factors: Optional[FireflyRecommendationFactors]
    # Stores the ribbon recommendation factor score
    ribbon_recommendation_factors: Optional[RibbonRecommendationFactors]
    # Stores the type of provider like in-person, in-home, virtual
    provider_type: Optional[str]
    # Stores the partnership for which this partner belongs
    partnership: Optional[Partnership]
    # Stores the clinical focus areas
    clinical_focus_areas: Optional[List[str]]
    # Stores the educations
    educations: Optional[List[str]]
    # Stores the degrees, when applicable
    degrees: Optional[List[str]]
    # Stores the talon recommendation factor score
    talon_recommendation_factors: Optional[TalonRecommendationFactors]


class ProviderSearchResult(TypedDict):
    vendor_http_status_code: int
    vendor_results: Union[RibbonProviderResponse, RibbonLocationResponse]
    records_available: int
    search_results: List[ProviderDetail]
    payload: Union[RibbonProviderParams, RibbonFacilityParams]


class ProcessedRibbonSearchResult(TypedDict):
    records_available: int
    search_results: List[ProviderDetail]


def get_sorted_results(providers: List[ProviderDetail]) -> List[ProviderDetail]:
    recommended_status_ranking_order = {
        RecommendationStatus.RECOMMENDED: 1,
        RecommendationStatus.NEUTRAL: 2,
        RecommendationStatus.NOT_RECOMMENDED: 3,
    }
    return sorted(
        providers,
        key=lambda provider: (
            recommended_status_ranking_order.get(provider["recommendation_status"] or "", float("inf")),
            provider["distance"],
        ),
    )


def is_in_network(
    member_insurance_network_uids: List[str],
    insurances: Optional[List[RibbonNetwork]],
) -> Optional[bool]:
    is_in_network: Optional[bool] = None
    # Get the list of supported insurance uuids
    if insurances is not None and len(insurances) > 0:
        insurance_uids: List[str] = [insurance["uuid"] for insurance in insurances]

        # check whether any member network id is covered by provider
        insurance_in_provider_network: Set[str] = set(member_insurance_network_uids).intersection(set(insurance_uids))
        is_in_network = any(insurance_in_provider_network)
    return is_in_network


def is_zip_code_matching(curated_provider: CuratedProvider, address_details: Optional[AddressDetail]) -> bool:
    if not address_details or not address_details.get("zip") or curated_provider.zip_code is None:
        return False
    return curated_provider.zip_code == address_details.get("zip")


def get_curated_providers_list(
    provider_search_results: List[RibbonProvider],
    member_service_level: MemberServiceLevel,
    specialty_group_id: Optional[int] = None,
) -> Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]]:
    # We have five conditions for selecting curated providers
    # 1. if their NPI, care_org_name and zipcode is a perfect match
    # 2. special case: when care org is None: if their NPI, zipcode is a perfect match
    # 3. if their care_org_name and zipcode matches and the npi is null
    filter_clauses = []
    for provider in provider_search_results:
        # Extract NPI, Address and Care_org information
        npi: Optional[str] = provider.get("npi")
        address_details: Optional[AddressDetail] = None
        care_org_name: Optional[str] = None
        locations: Optional[List[RibbonLocation]] = provider.get("locations", None)
        if locations is not None:
            for location in locations:
                care_org_name = location.get("name")
                sanitised_care_org_name = None
                if care_org_name is not None:
                    # remove all special characters from the input string
                    sanitised_care_org_name = get_sanitized_name(care_org_name)
                address_details = location.get("address_details")
                # Case 1 - NPI, care org and zip are an exact match
                if npi and address_details and sanitised_care_org_name and address_details.get("zip"):
                    filter_clauses.append(
                        (
                            Q(npi=str(npi))
                            & Q(sanitised_care_org_name__iexact=sanitised_care_org_name)
                            & Q(zip_code=address_details.get("zip"))
                        ),
                    )
                # To be deprecated after care org is backfilled with unknown
                # Case 2 - NPI and zip are an exact match; care org is null
                if npi and address_details and address_details.get("zip"):
                    filter_clauses.append(
                        (
                            Q(npi=str(npi))
                            & (Q(sanitised_care_org_name__iexact=CuratedProviderCareOrgNameChoice.UNKNOWN))
                            & Q(zip_code=address_details.get("zip"))
                        ),
                    )
                # Case 3 - Care org and zip are an exact match
                if address_details and care_org_name and address_details.get("zip"):
                    filter_clauses.append(
                        (
                            (Q(npi__exact="") | Q(npi__isnull=True))
                            & Q(sanitised_care_org_name__iexact=sanitised_care_org_name)
                            & Q(zip_code=address_details.get("zip"))
                        ),
                    )
    # If there are no filters to apply it means there are no partners
    if not filter_clauses:
        return None

    # Add contact information filter clauses
    contact_information_filter_clauses = []
    contact_information_filter_clauses.append((Q(is_verified=True)))
    # There are two cases for contact information
    # 1. If the search is specialty search - then fetch the contact information at provider and same department level
    # 2. If the search is other then specialty search - then only fetch contact info at provider level
    if specialty_group_id:
        contact_information_filter_clauses.append(
            (Q(specialty_group_id=specialty_group_id) | Q(specialty_group_id__isnull=True))
        )
    else:
        contact_information_filter_clauses.append((Q(specialty_group_id__isnull=True)))

    pattern = Value(r"[^\w]+")  # the regex
    replacement = Value("")  # replacement string
    flags = Value("g")  # regex flags
    curated_providers_queryset: QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider] = (
        CuratedProvider.objects.annotate(
            sanitised_care_org_name=Func(
                F("care_org_name"),
                pattern,
                replacement,
                flags,
                function="REGEXP_REPLACE",
                output_field=CharField(),
            ),
        )
        .filter(reduce(operator.or_, filter_clauses))
        .order_by(
            "-id",
        )
        .prefetch_related(
            Prefetch(
                "contact_informations",
                queryset=ContactInformation.objects.filter(
                    reduce(operator.and_, contact_information_filter_clauses)
                ).distinct(),
            ),
            Prefetch(
                "availabilities",
                queryset=Availability.objects.filter(
                    Q(number_of_days_till_next_availability__isnull=False) | Q(does_provider_exist__isnull=False)
                ).distinct(),
            ),
        )
        .prefetch_related(
            Prefetch(
                "rankings",
            )
        )
    )

    curated_providers_queryset = curated_providers_queryset.prefetch_related(
        Prefetch(
            "curatedprovider_partnerships",
            queryset=CuratedProviderPartnership.objects.filter(
                get_partner_service_level_agreement_filter(member_service_level)
            ).distinct(),
        )
    )

    return curated_providers_queryset


def is_provider_matching_curated_provider(
    curated_provider: AnnotatedSanitizedCareOrgNameCuratedProvider,
    npi,
    address_details,
    sanitised_care_org_name,
):
    if (
        str(curated_provider.npi) == str(npi)
        and is_zip_code_matching(curated_provider, address_details)
        and (
            curated_provider.sanitised_care_org_name == CuratedProviderCareOrgNameChoice.UNKNOWN
            or (
                sanitised_care_org_name
                and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
            )
        )
    ):
        return True
    return False


def is_facility_matching_curated_provider(
    curated_facility: AnnotatedSanitizedCareOrgNameCuratedProvider,
    address_details,
    sanitised_care_org_name,
):
    if (
        (curated_facility.npi is None or curated_facility.npi == "")
        and is_zip_code_matching(curated_facility, address_details)
        and sanitised_care_org_name
        and curated_facility.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
    ):
        return True
    return False


def update_availability(
    curated_providers: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    provider_search_results: List[RibbonProvider],
    specialty_group_id: Optional[int] = None,
) -> int:
    records_removed: int = 0
    if not curated_providers:
        return records_removed
    # If the curated availability present then update it.
    for curated_provider in curated_providers:
        availabilities = curated_provider.availabilities.all()
        if not availabilities:
            continue

        # map provider with the availabilities
        for provider in provider_search_results:
            # Extract NPI, Address and Care_org information
            npi: Optional[str] = provider.get("npi")
            address_details: Optional[AddressDetail] = None
            locations: Optional[List[RibbonLocation]] = provider.get("locations", None)
            if not locations:
                continue
            for location in locations:
                sanitised_care_org_name = get_sanitized_name(location.get("name"))
                address_details = location.get("address_details")
                # If the Provider level availability is present then show that else show department level
                provider_level_availability = None
                department_level_availabilty = None
                for availability in availabilities:
                    if availability.specialty_group is None:
                        provider_level_availability = availability
                    if specialty_group_id == availability.specialty_group_id:
                        department_level_availabilty = availability

                # There are two cases for doctor search
                #  1. Override if the npi, zipcode and care_org matches
                #  2. Override if the npi, zipcode matches and Care_org is None
                # If there are provider level or department level availabilities, add the locations
                if is_provider_matching_curated_provider(
                    curated_provider, npi, address_details, sanitised_care_org_name
                ):
                    location_availability: Optional[LocationAvailability] = None
                    if not provider_level_availability and not department_level_availabilty:
                        continue
                    if provider_level_availability and (
                        provider_level_availability.number_of_days_till_next_availability
                        and provider_level_availability.does_provider_exist is not False
                    ):
                        next_availability = provider_level_availability.number_of_days_till_next_availability
                        location_availability = {
                            "days_till_next_availability": next_availability,
                            "added_on": provider_level_availability.added_at.strftime("%Y-%m-%d")
                            if provider_level_availability.added_at
                            else None,
                            "specialty_group": None,
                            "does_provider_exist": True,
                        }
                    # Only replace the department level availabilities
                    # 1. If the provider level availabilites doesn't exist
                    # 2. And provider search if for same specailty group
                    elif (
                        department_level_availabilty
                        and department_level_availabilty.number_of_days_till_next_availability
                        and department_level_availabilty.does_provider_exist is not False
                        and specialty_group_id
                        and specialty_group_id == department_level_availabilty.specialty_group_id
                    ):
                        next_availability = department_level_availabilty.number_of_days_till_next_availability
                        location_availability = {
                            "days_till_next_availability": next_availability,
                            "added_on": department_level_availabilty.added_at.strftime("%Y-%m-%d")
                            if department_level_availabilty.added_at
                            else None,
                            "specialty_group": department_level_availabilty.specialty_group_id,
                            "does_provider_exist": True,
                        }
                    if location_availability:
                        location["availability"] = [location_availability]
                # If provider/department/care org does not exist, remove the locations
                if is_facility_matching_curated_provider(
                    curated_provider, address_details, sanitised_care_org_name
                ) or is_provider_matching_curated_provider(
                    curated_provider, npi, address_details, sanitised_care_org_name
                ):
                    # If provider doesn't exist then remove this location and increase the records removed count
                    if (provider_level_availability and provider_level_availability.does_provider_exist is False) or (
                        department_level_availabilty
                        and department_level_availabilty.does_provider_exist is False
                        and specialty_group_id
                        and specialty_group_id == department_level_availabilty.specialty_group_id
                    ):
                        locations.remove(location)
                        records_removed = records_removed + 1

            # If there are no locations in the provider then remove the provider
            if len(locations) == 0:
                provider_search_results.remove(provider)
    return records_removed


def update_contact_information(
    curated_providers: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    provider_search_results: List[RibbonProvider],
):
    if not curated_providers:
        return
    # If the curated contact information present then override it over Ribbon contact information.
    for curated_provider in curated_providers:
        contact_informations: Iterable[ContactInformation] = curated_provider.contact_informations.all()

        # If contact information present at provider and department level then prefer the latest contact information.
        latest_contact_information = max(
            contact_informations, key=lambda contact_info: contact_info.updated_at, default=None
        )
        # If there are no latest_contact_information then skip
        if (
            not latest_contact_information
            or not curated_provider
            or (latest_contact_information.phone is None and latest_contact_information.fax is None)
            or latest_contact_information.is_verified is False
        ):
            continue

        # map provider with the contact information
        for provider in provider_search_results:
            # Extract NPI, Address and Care_org information
            npi: Optional[str] = provider.get("npi")
            address_details: Optional[AddressDetail] = None
            locations: Optional[List[RibbonLocation]] = provider.get("locations", None)
            if locations is not None:
                for location in locations:
                    sanitised_care_org_name = get_sanitized_name(location.get("name"))
                    address_details = location.get("address_details")

                    # There are two cases for doctor search
                    #  1. Override if the npi, zipcode and care_org matches
                    #  2. Override if the npi, zipcode matches and Care_org is None
                    if is_provider_matching_curated_provider(
                        curated_provider, npi, address_details, sanitised_care_org_name
                    ):
                        # replace the Ribbon contact info with curated contact info
                        location["phone_numbers"] = [
                            {
                                "phone": latest_contact_information.phone,
                                "details": "primary",
                                "detail": "primary",
                                "last_processed_on": None,
                            }
                        ]
                        location["faxes"] = [
                            {
                                "fax": latest_contact_information.fax,
                                "details": "primary",
                                "detail": "primary",
                                "last_processed_on": None,
                            }
                        ]
                        location["is_verified"] = True


def get_curated_facilities_list(
    facility_search_results: List[RibbonLocation],
    member_service_level: MemberServiceLevel,
) -> Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]]:
    # We have one condition for filtering partnership
    # Mark a facility as partner if their care_org_name and zipcode is a perfect match
    filter_clauses = []
    for facility in facility_search_results:
        # Extract Zipcode and Care_org information
        care_org_name = facility.get("name")
        sanitised_care_org_name = None
        if care_org_name is not None:
            # remove all special characters from the input string
            sanitised_care_org_name = get_sanitized_name(care_org_name)
        address_details = facility.get("address_details")
        zip_code = None

        if isinstance(address_details, dict):
            zip_code = address_details.get("zip")

        if zip_code and sanitised_care_org_name:
            filter_clauses.append(
                (
                    (Q(npi__exact="") | Q(npi__isnull=True))
                    & Q(sanitised_care_org_name__iexact=sanitised_care_org_name)
                    & Q(zip_code=zip_code)
                ),
            )
    # If there are no filters to apply it means there are no partners
    if not filter_clauses:
        return None

    pattern = Value(r"[^\w]+")  # the regex
    replacement = Value("")  # replacement string
    flags = Value("g")  # regex flags

    curated_facilities_queryset: QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider] = (
        CuratedProvider.objects.annotate(
            sanitised_care_org_name=Func(
                F("care_org_name"),
                pattern,
                replacement,
                flags,
                function="REGEXP_REPLACE",
                output_field=CharField(),
            ),
        )
        .filter(reduce(operator.or_, filter_clauses))
        .order_by(
            "-id",
        )
        .prefetch_related(
            Prefetch(
                "contact_informations",
                queryset=ContactInformation.objects.filter(
                    is_verified=True,
                    specialty_group__isnull=True,
                ).distinct(),
            ),
        )
        .prefetch_related(
            Prefetch(
                "availabilities",
                queryset=Availability.objects.filter(
                    (Q(number_of_days_till_next_availability__isnull=False) | Q(does_provider_exist__isnull=False))
                    & Q(specialty_group__isnull=True)
                ).distinct(),
            ),
        )
        .prefetch_related(
            Prefetch(
                "rankings",
                queryset=Ranking.objects.filter(ranking_level=CuratedProviderRankingLevelConfig.CARE_ORG).distinct(),
            )
        )
    )

    curated_facilities_queryset = curated_facilities_queryset.prefetch_related(
        Prefetch(
            "curatedprovider_partnerships",
            queryset=CuratedProviderPartnership.objects.filter(
                get_partner_service_level_agreement_filter(member_service_level)
            ).distinct(),
        )
    )

    return curated_facilities_queryset


def update_facility_contact_information(
    curated_facilities: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    facility_search_results: List[RibbonLocation],
):
    if not curated_facilities:
        return
    # If the curated contact information present then override it over Ribbon contact information.
    for curated_facility in curated_facilities:
        contact_information = None
        contact_informations = curated_facility.contact_informations.all()

        # Saves a query by running in a loop rather than using .first()
        for contact in contact_informations:
            if contact_information is None:
                contact_information = contact
                break

        if contact_information:
            # map facilities with the contact information
            for facility in facility_search_results:
                # Extract Zipcode and Care_org information
                sanitised_care_org_name = get_sanitized_name(facility.get("name"))
                address_details = facility.get("address_details")
                # There is one case for facility search
                #  1. Override if the zipcode and care_org matches
                if is_facility_matching_curated_provider(curated_facility, address_details, sanitised_care_org_name):
                    # replace the Ribbon contact info with curated contact info
                    facility["phone_numbers"] = [
                        {
                            "phone": contact_information.phone,
                            "details": "primary",
                            "detail": "primary",
                            "last_processed_on": None,
                        }
                    ]
                    facility["faxes"] = [
                        {
                            "fax": contact_information.fax,
                            "details": "primary",
                            "detail": "primary",
                            "last_processed_on": None,
                        }
                    ]
                    facility["is_verified"] = True


def update_facility_curated_provider_ranking(
    curated_facilities: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    facility_search_results: List[RibbonLocation],
):
    if not curated_facilities:
        return
    # If the curated ranking information present then override it with our ranking.
    for curated_facility in curated_facilities:
        rankings = curated_facility.rankings.all()
        if not rankings:
            logger.info("No curated provider ranking for Curated Provider %s", curated_facility.id)
        rank_change = None
        ranking_reason = None
        for ranking in rankings:
            if rank_change is None and ranking_reason is None:
                rank_change = ranking.rank_change
                ranking_reason = ranking.reason
                break

        # map facilities with the ranking
        facility: RibbonLocation
        for facility in facility_search_results:
            sanitised_care_org_name = get_sanitized_name(facility.get("name"))
            address_details = facility.get("address_details")
            if is_facility_matching_curated_provider(curated_facility, address_details, sanitised_care_org_name):
                facility["curated_provider_ranking"] = rank_change
                facility["curated_provider_ranking_reason"] = ranking_reason


def update_facility_availability(
    curated_facilities: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    facility_search_results: List[RibbonLocation],
) -> int:
    records_removed: int = 0
    if not curated_facilities:
        return records_removed
    # If the curated contact information present then override it over Ribbon contact information.
    for curated_facility in curated_facilities:
        availabilities = None
        availabilities = curated_facility.availabilities.all()
        if not availabilities:
            continue

        availability: Optional[Availability] = None
        # Saves a query by running in a loop rather than using .first()
        for curated_availability in availabilities:
            if availability is None:
                availability = curated_availability
                break

        if availability:
            # map facilities with the contact information
            for facility in facility_search_results:
                # Extract Zipcode and Care_org information
                sanitised_care_org_name = get_sanitized_name(facility.get("name"))
                address_details = facility.get("address_details")

                # There is one case for facility search
                #  1. Override if the zipcode and care_org matches
                location_availability: Optional[LocationAvailability] = None
                if is_facility_matching_curated_provider(curated_facility, address_details, sanitised_care_org_name):
                    # replace the Ribbon contact info with curated contact info
                    location_availability = {
                        "days_till_next_availability": availability.number_of_days_till_next_availability,
                        "added_on": availability.added_at.strftime("%Y-%m-%d") if availability.added_at else None,
                        "specialty_group": availability.specialty_group.id
                        if availability.specialty_group is not None
                        else None,
                        "does_provider_exist": availability.does_provider_exist,
                    }
                # If facility doesn't exist then remove from search results
                if location_availability and location_availability["does_provider_exist"] is False:
                    facility_search_results.remove(facility)
                    records_removed = records_removed + 1
                elif location_availability:
                    facility["availability"] = [location_availability]
    return records_removed


def update_provider_partnership(
    curated_providers: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    provider_search_results: List[RibbonProvider],
):
    if not curated_providers:
        return
    for curated_provider in curated_providers:
        partnership = None
        # prefetch the partnership
        for partnership in curated_provider.curatedprovider_partnerships.all():
            if partnership:
                partnership = partnership
                break
        # If there are no partnerships then skip
        if not partnership or not curated_provider:
            continue

        # map provider with the agreement details
        for provider in provider_search_results:
            # Extract NPI, Address and Care_org information
            npi: Optional[str] = provider.get("npi")
            address_details: Optional[AddressDetail] = None
            care_org_name: Optional[str] = None
            locations: Optional[List[RibbonLocation]] = provider.get("locations", None)
            if locations is not None:
                for location in locations:
                    care_org_name = location.get("name")
                    sanitised_care_org_name = None
                    if care_org_name is not None:
                        # remove all special characters from the input string
                        sanitised_care_org_name = get_sanitized_name(care_org_name)
                    else:
                        sanitised_care_org_name = CuratedProviderCareOrgNameChoice.UNKNOWN.lower()
                    address_details = location.get("address_details")

                    # For a provider @ care org level partnership
                    # the npi, care org and zip code should match
                    if (
                        npi is not None
                        and str(curated_provider.npi) == str(npi)
                        and is_zip_code_matching(curated_provider, address_details)
                        and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
                        and partnership.partnership_level == PartnershipTypeConfig.PROVIDER_CARE_ORG
                    ):
                        location["network_partner_agreement_type"] = partnership.partnership.agreement_type
                        location["partner_name"] = partnership.partnership.partner_name
                    # For a care org level partnership
                    # the care org and zip code should match
                    elif (
                        (curated_provider.npi is None or curated_provider.npi == "")
                        and is_zip_code_matching(curated_provider, address_details)
                        and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
                        and partnership.partnership_level == PartnershipTypeConfig.CARE_ORG
                    ):
                        location["partner_name"] = partnership.partnership.partner_name
                        location["network_partner_agreement_type"] = partnership.partnership.agreement_type


def update_curated_provider_ranking(
    curated_providers: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    provider_search_results: List[RibbonProvider],
):
    if not curated_providers:
        return
    # If the curated ranking information present then override it with our ranking.
    for curated_provider in curated_providers:
        rankings = curated_provider.rankings.all()
        if not rankings:
            logger.info("No curated provider ranking for Curated Provider %s", curated_provider.id)

        curated_provider_ranking: Optional[Ranking] = None
        for ranking in rankings:
            if ranking:
                curated_provider_ranking = ranking
                break

        # If there are no rankings then skip
        if not curated_provider_ranking or not curated_provider:
            continue

        # map facilities with the ranking
        provider: RibbonProvider
        for provider in provider_search_results:
            # Extract NPI, Address and Care_org information
            npi: Optional[str] = provider.get("npi")
            address_details: Optional[AddressDetail] = None
            care_org_name: Optional[str] = None
            locations: Optional[List[RibbonLocation]] = provider.get("locations", None)
            if locations is not None:
                for location in locations:
                    care_org_name = location.get("name")
                    sanitised_care_org_name = None
                    if care_org_name is not None:
                        # remove all special characters from the input string
                        sanitised_care_org_name = get_sanitized_name(care_org_name)
                    else:
                        sanitised_care_org_name = CuratedProviderCareOrgNameChoice.UNKNOWN.lower()
                    address_details = location.get("address_details")

                    # For a provider @ care org level ranking
                    # the npi, care org and zip code should match
                    if (
                        npi is not None
                        and str(curated_provider.npi) == str(npi)
                        and is_zip_code_matching(curated_provider, address_details)
                        and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
                        and curated_provider_ranking.ranking_level
                        == CuratedProviderRankingLevelConfig.PROVIDER_CARE_ORG
                    ):
                        location["curated_provider_ranking"] = curated_provider_ranking.rank_change
                        location["curated_provider_ranking_reason"] = curated_provider_ranking.reason
                    # For a care org level ranking
                    # the care org and zip code should match
                    elif (
                        (curated_provider.npi is None or curated_provider.npi == "")
                        and is_zip_code_matching(curated_provider, address_details)
                        and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
                        and curated_provider_ranking.ranking_level == CuratedProviderRankingLevelConfig.CARE_ORG
                    ):
                        location["curated_provider_ranking"] = curated_provider_ranking.rank_change
                        location["curated_provider_ranking_reason"] = curated_provider_ranking.reason


def update_facility_partnership(
    curated_facilities: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]],
    facility_search_results: List[RibbonLocation],
):
    if not curated_facilities:
        return
    for curated_provider in curated_facilities:
        partnership = None
        # prefetch the partnership
        for partnership in curated_provider.curatedprovider_partnerships.all():
            if partnership:
                partnership = partnership
                break
        # If there are no partnerships then skip
        if not partnership or not curated_provider:
            continue

        # map provider with the agreement details
        for location in facility_search_results:
            # Address and Care_org information
            address_details: Optional[AddressDetail] = None
            care_org_name: Optional[str] = location.get("name")
            sanitised_care_org_name = None
            if care_org_name is not None:
                # remove all special characters from the input string
                sanitised_care_org_name = get_sanitized_name(care_org_name)
            address_details = location.get("address_details")
            if (
                (curated_provider.npi is None or curated_provider.npi == "")
                and is_zip_code_matching(curated_provider, address_details)
                and curated_provider.sanitised_care_org_name != CuratedProviderCareOrgNameChoice.UNKNOWN
                and sanitised_care_org_name is not None
                and curated_provider.sanitised_care_org_name.lower() == sanitised_care_org_name.lower()
                and partnership.partnership_level == PartnershipTypeConfig.CARE_ORG
            ):
                location["network_partner_agreement_type"] = partnership.partnership.agreement_type
                location["partner_name"] = partnership.partnership.partner_name


def get_converted_provider_search_results(
    provider_search_result: RibbonProvider,
    member_insurance_network_uids: List[str],
    skip_in_network_check: bool = False,
) -> List[ProviderDetail]:
    name_parts: List[str] = []
    if provider_search_result.get("first_name", None) is not None:
        name_parts.append(str(provider_search_result.get("first_name", None)))
    if provider_search_result.get("middle_name", None) is not None:
        name_parts.append(str(provider_search_result.get("middle_name", None)))
    if provider_search_result.get("last_name", None) is not None:
        name_parts.append(str(provider_search_result.get("last_name", None)))
    name: str = " ".join(name_parts)
    locations: Optional[List[RibbonLocation]] = provider_search_result.get("locations", None)
    converted_location_details: List[LocationDetail] = []
    network_partner_agreement_type: Optional[str] = None
    if locations is not None:
        for location in locations:
            phone_numbers: List[ContactDetail] = get_contact_details_from_ribbon_data(
                ribbon_phone_numbers=location.get("phone_numbers", None)
            )
            fax_numbers: List[FaxDetail] = get_fax_details_from_ribbon_data(
                ribbon_fax_numbers=location.get("faxes", None)
            )
            address: AddressDetail = get_address_details_from_ribbon_data(
                ribbon_address=location.get("address_details", None)
            )
            availability: Optional[List[LocationAvailability]] = location.get("availability", None)
            unique_identifier: str = get_unique_key_from_location(
                name=location.get("name", None),
                address=address,
            )
            network_partner_agreement_type = location.get("network_partner_agreement_type", None)
            converted_location_details.append(
                {
                    "uuid": location["uuid"],
                    "unique_identifier": unique_identifier,
                    "name": location.get("name", None),
                    "address_details": address,
                    "phone_numbers": phone_numbers,
                    "distance": location.get("distance", None),
                    "fax_numbers": fax_numbers,
                    "location_in_network": is_in_network(
                        insurances=location.get("insurances"),
                        member_insurance_network_uids=member_insurance_network_uids,
                    ),
                    "availability": availability[0] if availability else None,
                    "confidence": location.get("confidence", None),
                    "network_partner_agreement_type": network_partner_agreement_type,
                    "is_verified": location.get("is_verified", None),
                    "partner_name": location.get("partner_name", None),
                    "curated_provider_ranking": location.get("curated_provider_ranking", None),
                    "curated_provider_ranking_reason": (location.get("curated_provider_ranking_reason", None)),
                    "tins": location.get("tins", None),
                    "geo_code": None,
                    "talon_associated_procedure_prices": [],
                }
            )
    specialties = provider_search_result.get("specialties", None)
    specialty_display_list: List[str] = []
    if specialties is not None:
        for specialty in specialties:
            specialty_from_result: Optional[str] = specialty.get("display", None)
            if specialty_from_result is not None:
                specialty_display_list.append(specialty_from_result)
    provider_languages_from_ribbon: Optional[List[str]] = provider_search_result.get("languages", None)
    languages: List[str] = []
    if provider_languages_from_ribbon is not None:
        for provider_language_from_ribbon in provider_languages_from_ribbon:
            languages.append(string.capwords(provider_language_from_ribbon))

    gender_from_ribbon: Optional[str] = provider_search_result.get("gender", None)
    gender: Optional[str] = None
    if gender_from_ribbon is not None:
        if gender_from_ribbon.lower() == "m":
            gender = "Male"
        if gender_from_ribbon.lower() == "f":
            gender = "Female"

    clinical_areas_from_ribbon = provider_search_result.get("clinical_areas", None)
    clinical_areas: List[str] = []

    if clinical_areas_from_ribbon is not None:
        for clinical_area in clinical_areas_from_ribbon:
            display = clinical_area.get("display", None)
            if display:
                clinical_areas.append(display)

    educations_from_ribbon = provider_search_result.get("educations", None)
    educations: List[str] = []
    if educations_from_ribbon:
        for education in educations_from_ribbon:
            education_inner = education.get("education", None)
            if education_inner and education_inner.get("name", None):
                educations.append(education_inner["name"])

    # Store the degrees from the ribbon data. This is already a list of strings.
    degrees_from_ribbon = provider_search_result.get("degrees", [])

    # Network check is skipped for self-service requests.
    # TODO: In the future, we should only do this network check for NPI search.
    in_network: bool | None
    if skip_in_network_check:
        in_network = True
    else:
        in_network = is_in_network(
            insurances=provider_search_result.get("insurances"),
            member_insurance_network_uids=member_insurance_network_uids,
        )

    performance_data: Optional[RibbonPerformanceData] = provider_search_result.get("performance", None)

    ribbon_recommendation_factors = get_ribbon_recommendation_factors_for_provider(
        performance_data, provider_search_result.get("ratings_count"), provider_search_result.get("ratings_avg")
    )

    provider_details: List[ProviderDetail] = []
    for converted_location_detail in converted_location_details:
        npi: str = provider_search_result["npi"]
        location_id: str = converted_location_detail["uuid"]
        id: str = npi + "_" + location_id
        unique_identifier_parts: List[str] = [
            npi,
            converted_location_detail["unique_identifier"],
        ]
        network_partner_agreement_type = converted_location_detail["network_partner_agreement_type"]
        distance: Optional[float] = converted_location_detail.get("distance")

        from firefly.modules.network.utils.composite_score_utils import (
            get_firefly_recommendation_factors,
            get_provider_composite_score,
            get_provider_recommendation_status,
            get_recommendation_reason_labels,
        )

        # get firefly recommendation factors from ribbon data
        firefly_recommendation_factors = get_firefly_recommendation_factors(
            ribbon_recommendation_factors,
            converted_location_detail.get("curated_provider_ranking"),
            converted_location_detail.get("curated_provider_ranking_reason"),
            network_partner_agreement_type,
            None,
        )

        # calculate the composite score for the provider recommendation details
        composite_score = get_provider_composite_score(firefly_recommendation_factors)

        # get recommendation status
        recommendation_status: str = get_provider_recommendation_status(composite_score, firefly_recommendation_factors)

        # get recommendation reason label
        recommendation_reason_labels = get_recommendation_reason_labels(
            firefly_recommendation_factors, recommendation_status, composite_score
        )

        provider_details.append(
            {
                "id": id,
                "unique_identifier": "_".join(unique_identifier_parts),
                "npi": npi,
                "name": name,
                "first_name": provider_search_result.get("first_name"),
                "middle_name": provider_search_result.get("middle_name"),
                "last_name": provider_search_result.get("last_name"),
                "locations": [converted_location_detail],
                "specialties": specialty_display_list,
                "languages": languages,
                "gender": gender,
                "recommendation_status": recommendation_status,
                "recommendation_reason_labels": recommendation_reason_labels,
                "in_network": in_network if in_network is not None else False,
                "specialty_groups": provider_search_result.get("specialty_groups", None),
                "location_types": None,
                "distance": distance,
                "network_partner_agreement_type": network_partner_agreement_type,
                "composite_score": composite_score,
                "firefly_recommendation_factors": firefly_recommendation_factors,
                "ribbon_recommendation_factors": ribbon_recommendation_factors,
                "talon_recommendation_factors": None,
                "provider_type": None,
                "partnership": None,
                "clinical_focus_areas": clinical_areas or None,
                "educations": educations or None,
                "degrees": degrees_from_ribbon or None,
            }
        )
    return provider_details


def get_result_recommendation_status_for_facilities(
    network_partner_agreement_type: Optional[str] = None,
) -> str:
    recommendation_status: str = RecommendationStatus.NEUTRAL
    if network_partner_agreement_type is not None and (
        network_partner_agreement_type == AgreementTypeConfig.DIRECT_CONTRACT
    ):
        recommendation_status = RecommendationStatus.RECOMMENDED
    return recommendation_status


def get_result_recommendation_reason_labels_for_facilities(
    network_partner_agreement_type: Optional[str],
) -> str:
    # Facilities are recommended only if we have a direct partnership with them
    recommendation_reason_labels: List[str] = []
    if (
        network_partner_agreement_type is not None
        and network_partner_agreement_type == AgreementTypeConfig.DIRECT_CONTRACT
    ):
        recommendation_reason_labels.append(RecommendationReasonLabel.PARTNER)
    else:
        recommendation_reason_labels.append(RecommendationReasonLabel.UNKNOWN_COST_QUALITY)
    return ", ".join(recommendation_reason_labels)


def get_converted_location_search_results(
    location_search_result: RibbonLocation,
    member_insurance_network_uids: List[str],
) -> ProviderDetail:
    phone_numbers: List[ContactDetail] = get_contact_details_from_ribbon_data(
        ribbon_phone_numbers=location_search_result.get("phone_numbers", None)
    )
    fax_numbers: List[FaxDetail] = get_fax_details_from_ribbon_data(
        ribbon_fax_numbers=location_search_result.get("faxes", None)
    )
    address: AddressDetail = get_address_details_from_ribbon_data(
        ribbon_address=location_search_result.get("address_details", None)
    )
    unique_identifier: str = get_unique_key_from_location(
        name=location_search_result.get("name", None),
        address=address,
    )
    availability: Optional[List[LocationAvailability]] = location_search_result.get("availability", None)
    network_partner_agreement_type: Optional[str] = location_search_result.get("network_partner_agreement_type", None)
    location_type_labels: Optional[List[str]] = location_search_result.get("location_types", None)
    location: LocationDetail = {
        "uuid": location_search_result["uuid"],
        "unique_identifier": unique_identifier,
        "name": location_search_result.get("name"),
        "address_details": address,
        "phone_numbers": phone_numbers,
        "distance": location_search_result.get("distance", None),
        "fax_numbers": fax_numbers,
        "location_in_network": is_in_network(
            insurances=location_search_result.get("insurances"),
            member_insurance_network_uids=member_insurance_network_uids,
        ),
        "availability": availability[0] if availability else None,
        "confidence": location_search_result.get("confidence", None),
        "network_partner_agreement_type": network_partner_agreement_type,
        "is_verified": location_search_result.get("is_verified", None),
        "partner_name": location_search_result.get("partner_name", None),
        "curated_provider_ranking": location_search_result.get("curated_provider_ranking", None),
        "curated_provider_ranking_reason": (location_search_result.get("curated_provider_ranking_reason", None)),
        "tins": location_search_result.get("tins", None),
        "geo_code": None,
        "talon_associated_procedure_prices": [],
    }
    recommendation_status: str = get_result_recommendation_status_for_facilities(
        network_partner_agreement_type=network_partner_agreement_type,
    )
    distance: Optional[float] = location.get("distance")
    curated_provider_ranking = location.get("curated_provider_ranking")
    ranking_reason = location.get("curated_provider_ranking_reason", "")
    if not curated_provider_ranking:
        ranking_reason = get_result_recommendation_reason_labels_for_facilities(
            network_partner_agreement_type=network_partner_agreement_type,
        )
        ranking_recommendation = recommendation_status
    elif curated_provider_ranking < 0:
        ranking_recommendation = RecommendationStatus.NOT_RECOMMENDED
    elif curated_provider_ranking >= 0:
        ranking_recommendation = RecommendationStatus.RECOMMENDED

    return {
        "id": location["uuid"],
        "npi": None,
        "name": location_search_result.get("name", None),
        "unique_identifier": unique_identifier,
        "first_name": None,
        "middle_name": None,
        "last_name": None,
        "locations": [location],
        "specialties": [],
        "languages": [],
        "gender": None,
        "recommendation_status": ranking_recommendation,
        "recommendation_reason_labels": ranking_reason or "",
        "in_network": False,
        "specialty_groups": location_search_result.get("specialty_groups", None),
        "location_types": location_type_labels,
        "distance": distance,
        "network_partner_agreement_type": network_partner_agreement_type,
        "composite_score": None,
        "firefly_recommendation_factors": None,
        "ribbon_recommendation_factors": None,
        "talon_recommendation_factors": None,
        "provider_type": None,
        "partnership": None,
        "clinical_focus_areas": None,
        "educations": None,
        "degrees": None,
    }


def get_contact_details_from_ribbon_data(
    ribbon_phone_numbers: Optional[List[RibbonPhoneNumber]],
) -> List[ContactDetail]:
    phone_numbers: List[ContactDetail] = []
    if ribbon_phone_numbers is not None:
        for ribbon_phone_number in ribbon_phone_numbers:
            contact_number: Optional[str] = ribbon_phone_number.get("phone", "")
            contact_details: Optional[Any] = ribbon_phone_number.get("details", "")
            if not contact_details:
                contact_details = ribbon_phone_number.get("detail", "")
            if contact_number is not None and contact_details is not None:
                phone_numbers.append(
                    {
                        "details": str(contact_details),
                        "phone": contact_number,
                    }
                )
    return phone_numbers


def get_fax_details_from_ribbon_data(
    ribbon_fax_numbers: Optional[List[RibbonFaxNumber]],
) -> List[FaxDetail]:
    fax_numbers: List[FaxDetail] = []
    if ribbon_fax_numbers is not None:
        for ribbon_fax_number in ribbon_fax_numbers:
            fax_number = ribbon_fax_number.get("fax", "")
            contact_details = ribbon_fax_number.get("details", "")
            if not contact_details:
                contact_details = ribbon_fax_number.get("detail", "")
            if fax_number and contact_details:
                fax_numbers.append(
                    {
                        "details": str(contact_details),
                        "fax": fax_number,
                    }
                )
    return fax_numbers


def get_address_details_from_ribbon_data(ribbon_address: Optional[RibbonAddress]) -> AddressDetail:
    return {
        "address_line_1": ribbon_address.get("address_line_1") if ribbon_address is not None else None,
        "address_line_2": ribbon_address.get("address_line_2") if ribbon_address is not None else None,
        "city": ribbon_address.get("city") if ribbon_address is not None else None,
        "state": ribbon_address.get("state") if ribbon_address is not None else None,
        "zip": ribbon_address.get("zip") if ribbon_address is not None else None,
        "street": ribbon_address.get("street") if ribbon_address is not None else None,
    }


def get_unique_key_from_location(
    name: Optional[str],
    address: AddressDetail,
):
    # Any `None` values are being intentionally being cast into strings
    # so that they maintain their relative positions in the unique key
    address_parts: List[str] = [str(name)]
    unique_attributes: List[str] = [
        "address_line_1",
        "address_line_2",
        "city",
        "state",
        "zip",
    ]
    address_parts.extend([str(address.get(unique_attribute)) for unique_attribute in unique_attributes])
    return "_".join(address_parts)


def get_member_service_level(person, include_benefit_terminated=False) -> MemberServiceLevel:
    """
    Care, Coverage, or Care + Coverage?
    We use Care as the default when there are no enrollments
    """
    enrollments = list(
        get_person_program_enrollments(
            person,
            include_benefit_terminated=include_benefit_terminated,
        ).filter(program__uid__in=[ProgramCodes.PRIMARY_CARE, ProgramCodes.BENEFIT])
    )
    if len(enrollments) == 2:
        return MemberServiceLevel.CARE_AND_COVERAGE
    if len(enrollments) == 1:
        enrollment = enrollments[0]
        if enrollment.program.uid == ProgramCodes.BENEFIT:
            return MemberServiceLevel.COVERAGE
        elif enrollment.program.uid == ProgramCodes.PRIMARY_CARE:
            return MemberServiceLevel.CARE
    return MemberServiceLevel.CARE


def get_provider_search_results(
    distance: int,
    address: str,
    min_location_confidence: int,
    member_service_level: MemberServiceLevel,
    page_size: int,
    page: int,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    npis: Optional[List[str]] = None,
    speciality_uids: Optional[List[str]] = None,
    insurance_uids: Optional[List[str]] = None,
    procedure_uids: Optional[List[str]] = None,
    specialty_group_id: Optional[int] = None,
    exclude_fields_override: Optional[str] = None,
    max_locations: Optional[int] = None,
    language: Optional[str] = None,
    min_rating: Optional[int] = None,
    max_ribbon_cost_score: Optional[int] = None,
    min_outcomes_index: Optional[int] = None,
    min_efficiency_index: Optional[int] = None,
    max_cost_index: Optional[int] = None,  # Should only be used with procedure_uids
    clinical_focus_area_uids: Optional[List[str]] = None,
    panel_age: Optional[str] = None,
) -> ProviderSearchResult:
    npis = npis or []
    speciality_uids = speciality_uids or []
    insurance_uids = insurance_uids or []
    procedure_uids = procedure_uids or []
    result, payload = get_providers(
        npis=npis,
        page=page,
        page_size=page_size,
        sort_by="distance",  # not yet selectable via API
        distance=distance,
        speciality_uids=speciality_uids,
        insurance_uids=insurance_uids,
        address=address,  # Do not send PII to Ribbon
        min_location_confidence=min_location_confidence,
        raise_for_status=True,
        name=name,
        gender=gender,
        procedure_uids=procedure_uids,
        exclude_fields_override=exclude_fields_override,
        max_locations=max_locations,
        language=language,
        min_rating=min_rating,
        max_ribbon_cost_score=max_ribbon_cost_score,
        min_outcomes_index=min_outcomes_index,
        min_efficiency_index=min_efficiency_index,
        max_cost_index=max_cost_index,  # Should only be used with procedure_uids
        clinical_focus_area_uids=clinical_focus_area_uids,
        panel_age=panel_age,
    )
    results_json: RibbonResponse
    status: int
    if result is not None:
        results_json, status = result

        # Post process ribbon response to format for UI
        results_json = cast(RibbonProviderResponse, results_json)
        results: ProcessedRibbonSearchResult = process_ribbon_results(
            vendor_results=results_json,
            insurance_uids=insurance_uids,
            member_service_level=member_service_level,
            specialty_group_id=specialty_group_id,
        )
    return {
        "vendor_http_status_code": status,
        "vendor_results": results_json,
        "records_available": results["records_available"],
        "search_results": results["search_results"],
        "payload": payload,
    }


def process_ribbon_results(
    vendor_results: RibbonProviderResponse,
    member_service_level: MemberServiceLevel,
    insurance_uids: List[str],
    specialty_group_id: Optional[int] = None,
    skip_in_network_check: bool = False,
) -> ProcessedRibbonSearchResult:
    records_available: int = vendor_results["parameters"]["total_count"]
    curated_providers: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]] = get_curated_providers_list(
        vendor_results["data"], member_service_level, specialty_group_id=specialty_group_id
    )
    update_provider_partnership(curated_providers=curated_providers, provider_search_results=vendor_results["data"])
    update_contact_information(curated_providers=curated_providers, provider_search_results=vendor_results["data"])
    records_removed = update_availability(
        curated_providers=curated_providers,
        provider_search_results=vendor_results["data"],
        specialty_group_id=specialty_group_id,
    )
    update_curated_provider_ranking(
        curated_providers=curated_providers,
        provider_search_results=vendor_results["data"],
    )
    records_available = records_available - records_removed

    results: List[ProviderDetail] = []
    for provider in vendor_results["data"]:
        results.extend(
            get_converted_provider_search_results(
                provider_search_result=provider,
                member_insurance_network_uids=insurance_uids,
                skip_in_network_check=skip_in_network_check,
            )
        )
    results = get_sorted_results(providers=results)
    return {"search_results": results, "records_available": records_available}


def process_ribbon_facility_results(
    vendor_results: RibbonLocationResponse,
    member_service_level: MemberServiceLevel,
    insurance_uids: List[str],
) -> ProcessedRibbonSearchResult:
    records_available: int = vendor_results["parameters"]["total_count"]

    curated_facilities: Optional[QuerySet[AnnotatedSanitizedCareOrgNameCuratedProvider]] = get_curated_facilities_list(
        facility_search_results=vendor_results["data"],
        member_service_level=member_service_level,
    )
    update_facility_partnership(
        curated_facilities=curated_facilities,
        facility_search_results=vendor_results["data"],
    )
    update_facility_contact_information(
        curated_facilities=curated_facilities, facility_search_results=vendor_results["data"]
    )
    update_facility_curated_provider_ranking(
        curated_facilities=curated_facilities, facility_search_results=vendor_results["data"]
    )

    records_removed = update_facility_availability(
        curated_facilities=curated_facilities,
        facility_search_results=vendor_results["data"],
    )

    records_available = records_available - records_removed

    results: List[ProviderDetail] = []
    for location in vendor_results["data"]:
        results.append(
            get_converted_location_search_results(
                location_search_result=location,
                member_insurance_network_uids=insurance_uids,
            )
        )
    results = get_sorted_results(providers=results)

    return {
        "search_results": results,
        "records_available": records_available,
    }


def get_facility_search_results(
    page_size: int,
    page: int,
    distance: int,
    address: str,
    min_location_confidence: int,
    member_service_level: MemberServiceLevel,
    insurance_uids: Optional[List[str]] = None,
    facility_name: Optional[str] = None,
    location_types: Optional[List[str]] = None,
    exclude_fields_override: Optional[str] = None,
) -> ProviderSearchResult:
    insurance_uids = insurance_uids or []
    location_types = location_types or []
    result, payload = get_facilities(
        page=page,
        page_size=page_size,
        sort_by="distance",  # not yet selectable via API
        distance=distance,
        location_types=location_types,
        insurance_uids=insurance_uids,
        address=address,
        min_location_confidence=min_location_confidence,
        raise_for_status=True,
        facility_name=facility_name,
        exclude_fields_override=exclude_fields_override,
    )
    results_json: RibbonResponse
    status: int
    results: ProcessedRibbonSearchResult

    if result is not None:
        results_json, status = result
        # Post process ribbon response to format for UI
        results_json = cast(RibbonLocationResponse, results_json)
        results = process_ribbon_facility_results(
            vendor_results=results_json,
            insurance_uids=insurance_uids,
            member_service_level=member_service_level,
        )

    return {
        "vendor_http_status_code": status,
        "vendor_results": results_json,
        "records_available": results["records_available"],
        "search_results": results["search_results"],
        "payload": payload,
    }


def get_sanitized_name(name: Optional[str]):
    if name:
        # remove all special characters from the input string and convert to lower case
        return re.sub("[^\\w]+", "", name).lower()
    return None


def get_ribbon_recommendation_factors_for_provider(performance_data, rating_count, ratings_avg):
    ribbon_recommendation_factors: RibbonRecommendationFactors = {
        "cost_score": None,
        "quality_score": None,
        "average_rating": None,
        "number_of_ratings": rating_count,
    }
    ribbon_cost_score: Optional[float] = None
    quality_index: Optional[float] = None
    if performance_data is not None:
        cost_quality_aggregate = performance_data.get("aggregate")
        if cost_quality_aggregate is not None:
            cost_data = cost_quality_aggregate.get("cost", None)
            if cost_data is not None:
                ribbon_cost_score = cost_data.get("ribbon_cost_score", None)
                ribbon_recommendation_factors["cost_score"] = (
                    int(ribbon_cost_score) if ribbon_cost_score is not None else None
                )

            quality_data = cost_quality_aggregate.get("quality", None)
            if quality_data is not None:
                quality_index = quality_data.get("outcomes_index", None)
                ribbon_recommendation_factors["quality_score"] = (
                    int(quality_index) if quality_index is not None else None
                )
    average_rating_from_ribbon = ratings_avg  # Normalize scores
    # Ribbon stores the average rating score between 0-10
    if average_rating_from_ribbon is not None:
        ribbon_recommendation_factors["average_rating"] = average_rating_from_ribbon

    return ribbon_recommendation_factors


def get_specialty_uids_for_specialty_group(specialty_group: SpecialtyGroup) -> List[str]:
    specialty_uids: List[str] = []
    if specialty_group:
        for specialty in specialty_group.specialties.all():
            specialty_uid = get_cached_alias_id_from_specialty(
                specialty=specialty,
            )
            if specialty_uid is not None:
                specialty_uids.append(specialty_uid)
    return specialty_uids


def get_clinical_focus_area_uids_for_steerage(steerage: Steerage):
    clinical_focus_area_uids: List[str] = []
    if not waffle.switch_is_active(WAFFLE_SWITCH_CLINICAL_FOCUS_AREAS_V2):
        clinical_focus_areas = ClinicalFocusArea.objects.filter(
            services__in=steerage.services.all(), is_deprecated=False
        )
    else:
        clinical_focus_areas = steerage.clinical_focus_areas.filter(is_deprecated=False)
    for clinical_focus_area in clinical_focus_areas.iterator():
        uid = AliasMapping.get_alias_id_for_object(obj=clinical_focus_area, alias_name=AliasName.RIBBON)
        if uid:
            clinical_focus_area_uids.append(uid)
    return clinical_focus_area_uids


def get_primary_phone_number_from_ribbon_data(
    ribbon_phone_numbers: Optional[List[ContactDetail]],
) -> Optional[str]:
    if ribbon_phone_numbers is not None:
        for ribbon_phone_number in ribbon_phone_numbers:
            contact_number: Optional[str] = ribbon_phone_number.get("phone", "")
            contact_details: Optional[Any] = ribbon_phone_number.get("details", "")
            if not contact_details:
                contact_details = ribbon_phone_number.get("detail", "")

            if contact_details == "primary":
                return contact_number
    return None


def get_primary_fax_from_ribbon_data(
    ribbon_fax_numbers: Optional[List[FaxDetail]],
) -> Optional[str]:
    if ribbon_fax_numbers is not None:
        for ribbon_fax_number in ribbon_fax_numbers:
            fax_number: Optional[str] = ribbon_fax_number.get("fax", "")
            contact_details: Optional[Any] = ribbon_fax_number.get("details", "")
            if not contact_details:
                contact_details = ribbon_fax_number.get("detail", "")
            if contact_details == "primary":
                return fax_number
    return None


def create_tax_identifiers_for_steerage_provider(
    steerage_provider: SteerageProvider,
    tins: List[str] | None,
):
    if tins:
        tax_identifiers = []
        for tin in tins:
            tax_identifier, _ = TaxIdentifier.objects.get_or_create(tin=tin)
            tax_identifiers.append(tax_identifier)
        steerage_provider.tax_identifiers.set(tax_identifiers)
