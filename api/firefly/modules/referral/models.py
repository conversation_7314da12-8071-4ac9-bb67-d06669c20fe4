import copy
import logging
from datetime import date, datetime, timedelta
from typing import List, Optional

import waffle
from dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON>y<PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django.db.models import J<PERSON><PERSON>ield, Q
from django.db.models.query import QuerySet
from django.utils import timezone
from django_deprecate_fields import deprecate_field

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.services.ribbon.types import LocationAvailability
from firefly.core.user.models.models import Person
from firefly.modules.cases.constants import REFERRAL_REQUEST, ReferralRequestCaseStatuses
from firefly.modules.code_systems.models import CPTCode
from firefly.modules.facts.models import (
    ClinicalFocusA<PERSON>,
    LocationType,
    ServiceCategory,
    Specialty,
    SpecialtyGroup,
)
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.forms.mixin import JobStatus, PollableJobMixin
from firefly.modules.forms.models import FormSubmission
from firefly.modules.insurance.models import (
    InsuranceMemberInfo,
    InsurancePayer,
    InsurancePlan,
    Network,
)
from firefly.modules.network.models import (
    AGREEMENT_TYPE_CHOICES,
    CuratedProviderType,
    Partnership,
    TaxIdentifier,
)
from firefly.modules.network.utils.utils import fetch_curated_providers_for_steerage_provider
from firefly.modules.physician.models import Physician
from firefly.modules.practice.models import Practice
from firefly.modules.programs.models import Program
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import person_is_enrolled_in_program
from firefly.modules.referral.constants import (
    CARE_NOT_NEEDED_ACTION,
    COMMODITY_SEGMENT_LOCATION_TYPES,
    COMMODITY_SEGMENT_SPECIALTY,
    HIGH_TCOC_SEGMENT_SPECIALTY,
    MEMBER_SCHEDULED_ACTION,
    RESTEERED_ACTION,
    STEERAGE_NOT_NEEDED_ACTION,
    WAFFLE_SWITCH_REFERRAL_CARE_KEY_ELIGIBLE,
    WAFFLE_SWITCH_SELF_SERVICE_NAV,
    ReferralPriorityMatchingString,
    ReferralSent,
)
from firefly.modules.states.models import State
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.modules.tenants.models import Tenant
from firefly.modules.worklists.constants import ReferralLeakageReasonEnum

logger = logging.getLogger(__name__)


class VendorTransmissionStatusChoices(models.TextChoices):
    SUCCESS = "success", "success"
    FAILURE = "failure", "failure"
    PENDING = "pending", "pending"
    IN_TRANSMISSION = "in_transmission", "in_transmission"
    NOT_NEEDED = "not_needed", "not_needed"


class ReasonForExceptionConfig:
    AVAILABILITY = "availability"
    DIRECT_PARTNER = "direct_partner"
    INSURANCE = "insurance"
    INSURANCE_APPROVAL = "insurance_approval"
    INCORRECT_INFORMATION = "incorrect_information"
    LANGUAGE = "language"
    NO_RECOMMENDED_OPTIONS_AVAILABLE = "no_recommended_options_available"
    OTHER = "other"
    PATIENT_PREFERENCE = "patient_preference"
    PRIOR_RELATIONSHIP = "prior_relationship"
    SERVICE_NEED = "service_need"
    SOURCED_FROM_GARNER = "sourced_from_garner"
    SOURCED_FROM_TALON = "sourced_from_talon"
    UNABLE_TO_USE_TOOL = "unable_to_use_tool"
    MEMBER_CHANGED_PROVIDER = "member_changed_provider"


REASON_FOR_EXCEPTION_CHOICES_COMMON = [
    (ReasonForExceptionConfig.DIRECT_PARTNER, "Firefly partner"),
    (ReasonForExceptionConfig.OTHER, "Other"),
]

REASON_FOR_EXCEPTION_CHOICES_PROVIDER_SEARCH_BASE = [
    (ReasonForExceptionConfig.AVAILABILITY, "Availability"),
    (ReasonForExceptionConfig.INSURANCE, "Insurance approval"),
    (ReasonForExceptionConfig.LANGUAGE, "Language"),
    (ReasonForExceptionConfig.NO_RECOMMENDED_OPTIONS_AVAILABLE, "No recommended options available"),
    (ReasonForExceptionConfig.PATIENT_PREFERENCE, "Patient location preference"),
    (ReasonForExceptionConfig.PRIOR_RELATIONSHIP, "Prior relationship"),
    (ReasonForExceptionConfig.SERVICE_NEED, "Service need"),
    (ReasonForExceptionConfig.SOURCED_FROM_GARNER, "Sourced from Garner"),
]
REASON_FOR_EXCEPTION_CHOICES_PROVIDER_SEARCH = sorted(
    REASON_FOR_EXCEPTION_CHOICES_COMMON + REASON_FOR_EXCEPTION_CHOICES_PROVIDER_SEARCH_BASE,
    key=lambda x: x[1],
)

REASON_FOR_EXCEPTION_CHOICES_MANUAL_BASE = [
    (ReasonForExceptionConfig.SOURCED_FROM_GARNER, "Sourced from Garner"),
    (ReasonForExceptionConfig.SOURCED_FROM_TALON, "Sourced from TALON"),
    (ReasonForExceptionConfig.UNABLE_TO_USE_TOOL, "Unable to use Provider Search tool"),
]
REASON_FOR_EXCEPTION_CHOICES_MANUAL = sorted(
    REASON_FOR_EXCEPTION_CHOICES_COMMON + REASON_FOR_EXCEPTION_CHOICES_MANUAL_BASE,
    key=lambda x: x[1],
)

REASON_FOR_EXCEPTION_CHOICES = sorted(
    REASON_FOR_EXCEPTION_CHOICES_COMMON
    + REASON_FOR_EXCEPTION_CHOICES_PROVIDER_SEARCH_BASE
    + REASON_FOR_EXCEPTION_CHOICES_MANUAL_BASE,
    key=lambda x: x[1],
)

REASON_FOR_EXCEPTION_CHOICES_FACILITY_BASE = [
    (ReasonForExceptionConfig.PATIENT_PREFERENCE, "Patient location preference"),
    (ReasonForExceptionConfig.SERVICE_NEED, "Service need"),
    (ReasonForExceptionConfig.SOURCED_FROM_TALON, "Sourced from TALON"),
]

REASON_FOR_EXCEPTION_CHOICES_FACILITY = sorted(
    REASON_FOR_EXCEPTION_CHOICES_COMMON + REASON_FOR_EXCEPTION_CHOICES_FACILITY_BASE,
    key=lambda x: x[1],
)

REASON_FOR_REJECTION_CHOICES = [
    (ReasonForExceptionConfig.AVAILABILITY, "Availability"),
    (ReasonForExceptionConfig.INCORRECT_INFORMATION, "Incorrect information"),
    (ReasonForExceptionConfig.INSURANCE_APPROVAL, "Insurance approval"),
    (ReasonForExceptionConfig.LANGUAGE, "Language"),
    (ReasonForExceptionConfig.OTHER, "Other"),
    (ReasonForExceptionConfig.PATIENT_PREFERENCE, "Patient location preference"),
    (ReasonForExceptionConfig.PRIOR_RELATIONSHIP, "Prior relationship"),
    (ReasonForExceptionConfig.SERVICE_NEED, "Service need"),
    (ReasonForExceptionConfig.MEMBER_CHANGED_PROVIDER, "Member changed provider"),
]


class SearchStatus:
    SUCCESS = "success"
    INSURANCE_UNMAPPED = "insurance_unmapped"
    MISSING_INSURANCE_PLAN = "missing_insurance_plan"
    NETWORK_UNMAPPED = "network_unmapped"
    ADDRESS_MISSING = "address_missing"
    COORDINATE_MISSING = "coordinate_missing"
    SPECIALTY_UNMAPPED = "specialty_unmapped"
    LOCATION_TYPE_UNMAPPED = "location_type_unmapped"
    MISSING_SEARCH_TERMS = "missing_search_terms"
    REQUEST_TIMEOUT = "request_time"
    HTTP_ERROR = "http_error"
    INVALID_SEARCH_REQUEST = "invalid_search_request"
    ZIP_CODE_NOT_BACKFILLED = "zip_code_not_backfilled"
    INSURANCE_NETWORK_NOT_IN_DIRECTORY = "insurance_network_not_in_directory"


SEARCH_STATUS_CHOICES = [
    (SearchStatus.SUCCESS, "Search successful!"),
    (SearchStatus.INSURANCE_UNMAPPED, "Insurance plan is not mapped to a network"),
    (SearchStatus.MISSING_INSURANCE_PLAN, "Member does not have a Insurance Plan"),
    (SearchStatus.NETWORK_UNMAPPED, "Network not linked to vendor alias mapping"),
    (SearchStatus.ADDRESS_MISSING, "Patient missing an address"),
    (SearchStatus.COORDINATE_MISSING, "Address missing geo-coded values"),
    (SearchStatus.SPECIALTY_UNMAPPED, "Specialty is not mapped"),
    (SearchStatus.LOCATION_TYPE_UNMAPPED, "Location Type is not mapped"),
    (
        SearchStatus.MISSING_SEARCH_TERMS,
        "One of Specialty, Specialty Group or Location Type is required",
    ),
    (SearchStatus.REQUEST_TIMEOUT, "Request Timeout"),
    (SearchStatus.HTTP_ERROR, "Http request errored"),
    (
        SearchStatus.INVALID_SEARCH_REQUEST,
        "Search Requests can either be a facility search or a provider search",
    ),
    (
        SearchStatus.INSURANCE_NETWORK_NOT_IN_DIRECTORY,
        "Insurance Network not yet in Internal Directory",
    ),
]

DIRECTORY_SEARCH_STATUS_CHOICES = copy.deepcopy(SEARCH_STATUS_CHOICES)
DIRECTORY_SEARCH_STATUS_CHOICES.extend(
    [
        (SearchStatus.ZIP_CODE_NOT_BACKFILLED, "Zip code not backfilled in directory"),
    ]
)


class SteerageProviderStatuses:
    UNDER_REVIEW = "under_review"
    ACCEPTED = "accepted"
    REJECTED = "rejected"


STEERAGE_PROVIDER_STATUS_CHOICES = [
    (SteerageProviderStatuses.UNDER_REVIEW, SteerageProviderStatuses.UNDER_REVIEW),
    (SteerageProviderStatuses.ACCEPTED, SteerageProviderStatuses.ACCEPTED),
    (SteerageProviderStatuses.REJECTED, SteerageProviderStatuses.REJECTED),
]


class SearchAvailability(BaseModelV3):
    person = models.ForeignKey(
        Person,
        null=True,
        blank=True,
        related_name="search_availability",
        on_delete=models.CASCADE,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    error = models.CharField(  # noqa: TID251
        max_length=255,
        choices=SEARCH_STATUS_CHOICES,
        null=True,
        blank=True,
    )
    is_available = models.BooleanField(default=True, null=False, blank=False)
    is_provider_search = models.BooleanField(null=True, blank=True)
    is_facility_search = models.BooleanField(null=True, blank=True)
    is_procedure_search = models.BooleanField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251


# BaseProviderMixin models an abstract structure to store demographic data
# for a practitioner or a care-organization that provides care.
class BaseProviderMixin(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    first_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    middle_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npi = models.CharField(max_length=11, null=True, blank=True)  # noqa: TID251
    # practice name for provider
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    care_organization_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # Address
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    street_address = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_1 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address_line_2 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    city = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip_code = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    state = models.ForeignKey(State, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    fax = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        abstract = True


class RecommendationStatus:
    NO_RATING = "NO_RATING"
    RECOMMENDED = "RECOMMENDED"
    NOT_RECOMMENDED = "NOT_RECOMMENDED"
    NEUTRAL = "NEUTRAL"


class RecommendationStatusChoices(models.TextChoices):
    NO_RATING = RecommendationStatus.NO_RATING, RecommendationStatus.NO_RATING
    RECOMMENDED = RecommendationStatus.RECOMMENDED, RecommendationStatus.RECOMMENDED
    NOT_RECOMMENDED = RecommendationStatus.NOT_RECOMMENDED, RecommendationStatus.NOT_RECOMMENDED
    NEUTRAL = RecommendationStatus.NEUTRAL, RecommendationStatus.NEUTRAL


class ServiceQuantityUnitConfig:
    DAYS = "days"
    UNITS = "units"
    VISITS = "visits"


SERVICE_QUANTITY_UNIT_CHOICES = [
    (ServiceQuantityUnitConfig.DAYS, "days"),
    (ServiceQuantityUnitConfig.UNITS, "units"),
    (ServiceQuantityUnitConfig.VISITS, "visits"),
]


# Stores the number of "services" requested/allowed
# in a prior-auth or a waiver.
# The number of "services" can indicate allowed number of
# visits / days of validity / ...
# This is indicated by the unit attribute and the value attribute
# stores the actual numerical value
class ServiceQuantityMixin(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unit: models.CharField = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=SERVICE_QUANTITY_UNIT_CHOICES,
    )
    value: models.PositiveIntegerField = models.PositiveIntegerField(blank=True, null=True)

    class Meta(BaseModelV3.Meta):
        abstract = True
        db_table: Optional[str] = None


class PriorAuthorization(BaseModelV3):
    APPROVED = "Approved"
    DENIED = "Denied"

    ACTIVE = "Active"
    INACTIVE = "Inactive"
    PENDING = "Pending"

    PRE_AUTHORIZATION = "Pre-Authorization"
    PRE_CERTIFICATION = "Pre-Certification"
    REFERRAL = "Referral"

    DETERMINATION_CHOICES = (
        (APPROVED, "Approved"),
        (DENIED, "Denied"),
    )

    STATUS_CHOICES = (
        (ACTIVE, "Active"),
        (INACTIVE, "Inactive"),
        (PENDING, "Pending"),
    )

    TYPE_CHOICES = (
        (PRE_AUTHORIZATION, "Pre-Authorization"),
        (PRE_CERTIFICATION, "Pre-Certification"),
        (REFERRAL, "Referral"),
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    comment = models.CharField(max_length=255, null=True)  # noqa: TID251
    vendor_create_date = models.DateField(null=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    determination = models.CharField(  # noqa: TID251
        max_length=255,
        choices=DETERMINATION_CHOICES,
        null=True,
    )

    effective_from = models.DateField(null=True)
    effective_through = models.DateField(null=True)
    person = models.ForeignKey(Person, on_delete=models.CASCADE)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=255,
        choices=STATUS_CHOICES,
        null=True,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type = models.CharField(  # noqa: TID251
        max_length=255,
        choices=TYPE_CHOICES,
        null=True,
    )
    vendor_update_date = models.DateField(null=True)
    service_categories = BaseModelV3ManyToManyField(
        ServiceCategory,
        related_name="prior_authorizations",
        blank=True,
        through="PriorAuthorizationServiceCategories",
    )

    originator_system_id = models.TextField(null=True, blank=True)

    def delete(self, *args, **kwargs):
        AliasMapping.delete_mapping_for_object(obj=self, alias_name=AliasName.FLUME)
        BaseModelV3.delete(self, *args, **kwargs)
        return super().delete(*args, **kwargs)

    def save(self, *args, **kwargs):
        super(PriorAuthorization, self).save(*args, **kwargs)

        from firefly.modules.referral.utils.prior_auth_utils import create_or_update_prior_auth_case

        create_or_update_prior_auth_case(self)


class PriorAuthorizationServiceCategories(BaseModelV3):
    priorauthorization = models.ForeignKey(
        PriorAuthorization,
        related_name="priorauthorization_servicecategories",
        on_delete=models.CASCADE,
    )

    servicecategory = models.ForeignKey(
        ServiceCategory,
        related_name="priorauthorization_servicecategories",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "referral_priorauthorization_service_categories"
        verbose_name_plural: str = "Prior authorization service categories"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["priorauthorization", "servicecategory"],
                condition=Q(deleted=None),
                name="referral_priorauthorization_servicecategories_uniq",
            )
        ]


class PriorAuthorizationServiceQuantity(ServiceQuantityMixin):
    prior_authorization = models.OneToOneField(
        PriorAuthorization,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name="service_quantity",
    )

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "Prior authorization service quantities"


# PriorAuthorizationProvider stores the the providers associated
# with an incoming prior auth.
class PriorAuthorizationProvider(BaseProviderMixin):
    prior_authorization = models.ForeignKey(
        PriorAuthorization,
        on_delete=models.CASCADE,
        null=True,
        related_name="providers",
    )


class Waiver(BaseModelV3, DirtyFieldsMixin):
    person = models.ForeignKey(Person, on_delete=models.CASCADE, null=False)
    prior_authorization = models.ForeignKey(
        PriorAuthorization,
        related_name="waivers",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    service_categories = BaseModelV3ManyToManyField(
        ServiceCategory,
        related_name="waivers",
        blank=True,
        through="WaiverServiceCategories",
    )

    # set by the Ops team, indicates that the Referral requires Prior Authorization for coverage member
    prior_auth_required = deprecate_field(models.BooleanField(null=True, blank=True))

    prior_auth_originator_system_id = deprecate_field(models.TextField(null=True, blank=True))

    # This is needed in care+coverage case when the steerage is not eligible for Care Key. This allows navigators
    # to toggle off the care key from steerage.
    is_active = models.BooleanField(null=True, blank=True, default=True)

    def maybe_create_transmission_status(self):
        waiver_transmission_status: Optional[WaiverTransmissionStatus] = None
        try:
            waiver_transmission_status = self.transmission_status
        except AttributeError:
            pass
        if waiver_transmission_status is None:
            waiver_transmission_status = WaiverTransmissionStatus()
            waiver_transmission_status.waiver = self
            waiver_transmission_status.status = VendorTransmissionStatusChoices.PENDING
            waiver_transmission_status.sent_at = None
            waiver_transmission_status.save()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.maybe_create_transmission_status()


class WaiverServiceCategories(BaseModelV3):
    waiver = models.ForeignKey(
        Waiver,
        related_name="waiver_servicecategories",
        on_delete=models.CASCADE,
    )

    servicecategory = models.ForeignKey(
        ServiceCategory,
        related_name="waiver_servicecategories",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "referral_waiver_service_categories"
        verbose_name_plural: str = "Waiver service categories"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["waiver", "servicecategory"],
                condition=Q(deleted=None),
                name="referral_waiver_servicecategories_uniq",
            )
        ]


class ServiceType(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    description = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    ordering = models.IntegerField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "Service types"

    def __str__(self):
        return self.description


class Service(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    description = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    sanitized_description = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    category = models.ForeignKey(ServiceType, null=True, blank=True, on_delete=models.CASCADE)

    def __str__(self):
        return self.description

    def save(self, *args, **kwargs):
        from firefly.modules.referral.utils.provider_search_utils import get_sanitized_name

        # Update sanitized_description on save
        if self.description:
            self.sanitized_description = get_sanitized_name(self.description).strip()

        super(Service, self).save(*args, **kwargs)


class PartnershipService(BaseModelV3):
    """
    Table to assist in the ManyToMany join between Partnership and Services.
    """

    partnership = models.ForeignKey(
        Partnership,
        related_name="partnership_services",
        null=False,
        on_delete=models.CASCADE,
    )

    service = models.ForeignKey(
        Service,
        related_name="partnership_services",
        null=False,
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["partnership", "service"],
                condition=Q(deleted=None),
                name="referral_partnership_service_uniq",
            )
        ]


class PartnershipClinicalFocusArea(BaseModelV3):
    """
    Table to assist in the ManyToMany join between Partnership and Clinical Focus Areas.
    """

    partnership = models.ForeignKey(
        Partnership,
        related_name="partnership_clinical_focus_areas",
        null=False,
        on_delete=models.CASCADE,
    )

    clinical_focus_area = models.ForeignKey(
        ClinicalFocusArea,
        related_name="partnership_clinical_focus_areas",
        null=False,
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["partnership", "clinical_focus_area"],
                condition=Q(deleted=None),
                name="referral_partnership_clinical_focus_area_uniq",
            )
        ]


class ServiceSpecialtyGroupLocationType(BaseModelV3):
    from firefly.modules.referral.models import Service

    service = models.ForeignKey(
        Service,
        related_name="services",
        null=False,
        on_delete=models.CASCADE,
    )
    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        related_name="service_specialtygroups",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    location_type = models.ForeignKey(
        LocationType,
        related_name="service_locationtypes",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    # Ties back to the import job that ingested this mapping
    # Useful for deleting the older mappings
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    import_job_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=False,
        null=False,
    )


class ServiceCPTCode(BaseModelV3):
    from firefly.modules.referral.models import Service

    service = models.ForeignKey(
        Service,
        related_name="service_cptcode",
        null=False,
        on_delete=models.CASCADE,
    )
    cpt_code = models.ForeignKey(
        CPTCode,
        related_name="service_cptcode",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    # Ties back to the import job that ingested this mapping
    # Useful for deleting the older mappings
    import_job_name = models.TextField(
        max_length=255,
        blank=False,
        null=False,
    )


class LocationTypePartnershipType(BaseModelV3):
    from firefly.modules.network.models import PartnershipType

    location_type = models.ForeignKey(
        LocationType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="partnership_types",
    )

    partnership_type = models.TextField(
        blank=True,
        null=True,
        choices=PartnershipType.choices,
    )


class ClinicalFocusAreaSpecialtyGroup(BaseModelV3):
    from firefly.modules.referral.models import Service

    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        related_name="clinical_focus_area_specialty_group",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    clinical_focus_area = models.ForeignKey(
        ClinicalFocusArea, related_name="clinical_focus_area_specialty_group", on_delete=models.CASCADE
    )
    # Ties back to the import job that ingested this mapping
    # Useful for deleting the older mappings
    import_job_name = models.TextField(blank=False, null=False)


class ReferralStatus(models.TextChoices):
    SUCCEEDED = "Fulfilled", "Fulfilled"
    PENDING = "Outstanding", "Outstanding"
    FAILED = "Cancelled", "Cancelled"


class ReferralVisitNotePriority(models.TextChoices):
    HIGH = "High", "High"
    MEDIUM = "Medium", "Medium"
    LOW = "Low", "Low"


class ReferralPriority(models.TextChoices):
    URGENT = "URGENT", "Urgent"
    MEDIUM = "MEDIUM", "Soonest Available"
    STANDARD = "STANDARD", "Standard"


class SteerageSegment(models.TextChoices):
    HIGH_TCOC = "High TCoC", "High TCoC"
    LOW_TCOC = "Low TCoC", "Low TCoC"
    COMMODITY = "Commodity", "Commodity"


class SearchRequestVendor(models.TextChoices):
    RIBBON = "Ribbon", "Ribbon"
    TALON = "TALON", "TALON"


class Referral(BaseModelV3, DirtyFieldsMixin):
    """
    Referral model which is in sync with ElationReferral.
    """

    # refers to authorization_for elation referral field
    description = models.TextField(null=True, blank=True)

    referred_to = models.TextField(null=True, blank=True)

    # refers to resolution elation referral field
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=ReferralStatus.choices,
    )

    person = models.ForeignKey(
        Person,
        null=True,
        blank=True,
        related_name="referral",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    subject = models.TextField(null=True, blank=True)

    body = models.TextField(null=True, blank=True)

    # refers to sign_date of elation referral letter
    sign_date = models.DateTimeField(null=True, blank=True)

    # refers to document_date of elation referral letter
    vendor_create_date = models.DateTimeField(null=True, blank=True)

    # set by the Ops team, indicates that the Referral requires Insurance Auth. Approval
    insurance_authorization_required = models.BooleanField(null=True, blank=True)

    # physician who has signed the referral
    sign_off_user = models.ForeignKey(
        Physician,
        blank=True,
        null=True,
        related_name="referrals",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    leakage_reason = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralLeakageReasonEnum.choices(),
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    visit_note_priority = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralVisitNotePriority.choices,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    referral_priority = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralPriority.choices,
    )

    # Tracks whether this was a Last Mile referral (for services a virtual PCP cannot provide)
    is_last_mile = models.BooleanField(null=True, blank=True)

    # Tracks whether we have sent this Referral on to an external vendor
    # (in particular, being implemented for Memorial Hermann)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor_transmission_status = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=VendorTransmissionStatusChoices.choices,
    )

    # This is used by navigators to turn off the referral, when a referral is not needed in case of external providers
    is_active = models.BooleanField(null=True, blank=True, default=True)

    def calculate_referral_priority(self) -> str:
        """
        When a clinician creates a referral in Elation, they utilize a template with certain
        pre-determined text that should imply the referral priority. (This is a work-around to
        not having a structured dropdown for priority.) This function parses the text of the
        referral to assign a priority.
        """
        if self.body is not None:
            body = self.body.lower()

            for matching_string in ReferralPriorityMatchingString:
                if matching_string.value in body:
                    return matching_string.name

            return ReferralPriority.STANDARD.name
        else:
            return ReferralPriority.STANDARD.name

    # Limits the fields analyzed by DirtyFieldsMixin, to improve performance
    FIELDS_TO_CHECK = ["insurance_authorization_required"]

    def save(self, *args, **kwargs):
        from firefly.modules.referral.utils.steerage_utils import (
            update_referral_request_case_status_for_insurance_auth,
        )

        # We want to evaluate the dirty fields before saving, so they are indeed
        # dirty, but we need the object saved so that the called function can
        # have an ID in the database. Thus, message pass with some booleans.
        is_dirty = self.is_dirty()
        dirty_fields = self.get_dirty_fields(check_relationship=True)

        self.referral_priority = self.calculate_referral_priority()

        super(Referral, self).save(*args, **kwargs)

        # Now, execute follow-up functions
        if is_dirty:
            if "insurance_authorization_required" in dirty_fields.keys():
                steerage = self.steerages.first()
                if steerage:
                    update_referral_request_case_status_for_insurance_auth(steerage)


class ReferralService(BaseModelV3):
    """
    Table to assist in the ManyToMany join between Referral and Service.
    """

    referral = deprecate_field(
        models.ForeignKey(
            Referral,
            related_name="referral_services",
            on_delete=models.CASCADE,
        )
    )

    last_mile_service = deprecate_field(
        models.ForeignKey(
            Service,
            related_name="referral_services",
            on_delete=models.CASCADE,
        )
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["referral", "last_mile_service"],
                condition=Q(deleted=None),
                name="referral_referral_service_uniq",
            )
        ]


class SteerageSuccessConfig:
    """
    Naming this Config generically so that it might be used for all Steerage Success metrics.
    """

    SUCCESS = "success"
    FAILURE = "failure"
    EXCLUDED = "excluded"


class SteerageDisposition(BaseModelV3):
    """
    Represents the possible final outcomes of a Steerage. Each has associated analytics
    implications, in addition to a variety of attributes which determine how the related Steerage
    and Cases move and flow in response to the Steerage having this Disposition.
    """

    # Dispositions are sometimes selected based on a Case moving into a particular Status
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    matches_case_status_name = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # Indicates how a Steerage with this Disposition should be counted in terms of
    # Follow Through Success.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    follow_through_success = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
        choices=[
            (SteerageSuccessConfig.SUCCESS, "Success"),
            (SteerageSuccessConfig.FAILURE, "Failure"),
            (SteerageSuccessConfig.EXCLUDED, "Excluded"),
        ],
    )

    # When true - skip updating the case status to member scheduled.
    # This is useful particularly for Insurance Approval cases, where we have a
    # scheduling date available but do not want to take credit for success in
    # scheduling the member.
    ignore_member_scheduled = models.BooleanField(
        blank=True,
        null=True,
    )

    # When true - skip updating a steerage disposition away from "Member Selected".
    retain_member_selected = deprecate_field(
        models.BooleanField(
            blank=True,
            null=True,
        )
    )

    # When true - Move the Note Retrieval and Insurance Approval cases to "will not do"
    close_extra_cases = models.BooleanField(
        blank=True,
        null=True,
    )

    # When true - If Note Retrieval and Insurance Approval cases exists with status "will not do"
    # then move them to draft status.
    reopen_extra_cases = models.BooleanField(
        blank=True,
        null=True,
    )

    # Operations thinks of these as named by this field
    def __str__(self):
        return self.matches_case_status_name


class SteerageMemberRequestedData(BaseModelV3):
    # Store the specialty and other attributed selected by user
    # while filling in referral request form
    specialty = models.TextField(blank=True, null=True)

    description = models.TextField(blank=True, null=True)

    going_on_for = models.TextField(blank=True, null=True)

    provider_name = models.TextField(blank=True, null=True)

    provider_address = models.TextField(blank=True, null=True)

    practice_name = models.TextField(blank=True, null=True)

    class Meta(BaseModelV3.Meta):
        abstract = False


class SteerageRequestTypeConfig:
    BROAD_REQUEST = "broad"
    SPECIFIC_REQUEST = "specific"


STEERAGE_REQUEST_TYPE_CHOICES = [
    (SteerageRequestTypeConfig.BROAD_REQUEST, "Broad Request"),
    (SteerageRequestTypeConfig.SPECIFIC_REQUEST, "Specific Request"),
]


class SteerageTypeOfVisit(models.TextChoices):
    IN_PERSON = "In Person", "In Person"
    VIRTUAL = "Virtual", "Virtual"
    IN_PERSON_OR_VIRTUAL = "In Person Or Virtual", "In Person Or Virtual"
    HOME = "Home", "Home"
    ANY = "Any", "Any"


class SteerageStatus(models.TextChoices):
    REQUEST_RECEIVED = "Request Received", "Request Received"
    IN_PROGRESS = "In Progress", "In Progress"
    PENDING_PROVIDER_SELECTION = "Pending Provider Selection", "Pending Provider Selection"
    PENDING_SCHEDULING = "Pending Scheduling", "Pending Scheduling"
    APPOINTMENT_SCHEDULED = "Appointment Scheduled", "Appointment Scheduled"
    REQUEST_CANCELED = "Request Canceled", "Request Canceled"
    REQUEST_ARCHIVED = "Request Archived", "Request Archived"


class SteerageClinicalRisk(models.TextChoices):
    HIGH = "High", "High"
    LOW = "Low", "Low"


class Steerage(BaseModelV3, DirtyFieldsMixin):
    # The person being Steered/Referred/Waivered
    person = models.ForeignKey(
        Person,
        null=True,
        blank=True,
        related_name="steerages",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # For Care Members, this is a link to the object containing relevant details about the
    # Referral this Steerage applies to.
    referral = models.ForeignKey(
        Referral,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="steerages",
    )

    # For Coverage Members, this is a link to the object containing relevant details about
    # the Waiver (care key) this Steerage applies to.
    waiver = models.ForeignKey(
        Waiver,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="steerages",
    )

    # Also called "Scheduled Date", this is the date the member plans to schedule their
    # encounter with the provider we're recommending. May be set by the Member in the App, or
    # from within Lucian.
    scheduling_date = models.DateField(
        blank=True,
        null=True,
    )

    # Sometimes the navigation request is for a specialty group, and other times the request is
    # for a particular location type. The following two fields capture these possibilities, and
    # then these are merged into a single dropdown on the front end.
    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="steerages",
    )
    location_type = models.ForeignKey(
        LocationType,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        null=True,
        blank=True,
        related_name="steerages",
    )

    effective_from = models.DateField(
        blank=True,
        null=True,
    )
    effective_through = models.DateField(
        blank=True,
        null=True,
    )

    # If this is a Care-and-Coverage Steerage, then it is possible for a Waiver to be POSSIBLE, but
    # not ACTIVE. This could happen if a Referral is made for a service that is not Waiverable.
    is_waiver_included = deprecate_field(models.BooleanField(blank=True, null=True, default=True))

    is_locked = models.BooleanField(
        blank=True,
        null=True,
    )
    locked_at = models.DateTimeField(
        blank=True,
        null=True,
    )
    locked_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="steerages",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # The date that the first automated outreach is sent to the member
    recommendation_sent_at = models.DateTimeField(
        blank=True,
        null=True,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    description = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # For use by the NetOps team to capture the nature of the steerage request.
    # - Broad: "I want to see a dermatologist"
    # - Specific: "I want to see Dr. Smith"
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    request_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=STEERAGE_REQUEST_TYPE_CHOICES,
    )

    # All CaseRelations with a Steerage as the content_object
    case_relations = GenericRelation("cases.CaseRelation")

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    type_of_visit = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=SteerageTypeOfVisit.choices,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=SteerageStatus.choices,
        default=SteerageStatus.REQUEST_RECEIVED,
    )

    # Store the values filled in by the user in referral request form
    member_requested_data = models.ForeignKey(
        SteerageMemberRequestedData,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="steerages",
    )

    # The Disposition represents the final outcome of this steerage and
    # helps inform success metrics.
    disposition = models.ForeignKey(
        SteerageDisposition,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="steerages",
    )

    # Does this Steerage meet Follow-Through Success? Assume not unless explicitly True
    in_follow_through_success_numerator = models.BooleanField(default=False, null=True, blank=True)

    # Does this Steerage count for Follow-Through Success? Assume yes unless explicitly False
    in_follow_through_success_denominator = models.BooleanField(default=True, null=True, blank=True)

    # This denotes when steerage will archive based on scheduling_date and effective through date.
    # If archived, then the steerage will show in the app archived section.
    archive_at = models.DateField(
        blank=True,
        null=True,
    )

    # Priority of a steerage - This will allow navigators to decide how soon referral needs to be send out and
    # how soon a provider needs to be available for the appointment
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    priority = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralPriority.choices,
    )

    # This stores segmentation of the steerage based on steerage specialty and priority.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    segment = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=SteerageSegment.choices,
    )

    # This is toggleable by an admin to show that a referral was started by a member
    requested_by_member = models.BooleanField(blank=True, null=True)

    # This is to notify clinican for the referral
    notify_clinician_yn = models.BooleanField(blank=True, null=True)

    # Stores the information if clinician had any preferred provider or facility
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    preferred_provider_or_facility = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    # Stores the information if the steerage cases were initiated from Lucian/Elation/PriorAuth
    # For True - Initiated from Lucian
    # For False - Initiated from Elation/PriorAuth
    is_initiated_from_lucian = models.BooleanField(blank=True, null=True, default=True)

    # It stores the services for which navigation is needed
    services = BaseModelV3ManyToManyField(Service, related_name="steerages", through="SteerageService")

    # It stores the clinical focus areas for which navigation is needed
    clinical_focus_areas = BaseModelV3ManyToManyField(
        ClinicalFocusArea, related_name="steerages", through="SteerageClinicalFocusArea"
    )

    # set by the Ops team, indicates that the Referral requires Prior Authorization for coverage member
    prior_authorization_required = models.BooleanField(null=True, blank=True)
    prior_authorization_system_id = models.TextField(null=True, blank=True)

    # Feedback form that gets automatically assigned to the member for any partner referrals
    feedback_form = models.OneToOneField(
        FormSubmission,
        related_name="steerage",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # This field is updated when a steerage is first locked, if there are no accepted providers, that means
    # that this steerage is eligible for self service, if providers have been accepted when sending to the
    # member, then a navigator steered the member
    is_self_service_enabled = models.BooleanField(blank=True, null=True)

    # Used to check whether an Elation letter was created from Lucian
    @property
    def was_elation_letter_created_from_lucian(self) -> bool:
        existing_alias_mapping = AliasMapping.get_alias_id_for_object(
            obj=self,
            alias_name=AliasName.ELATION_REFERRAL_LETTER,
        )
        if existing_alias_mapping:
            return True
        return False

    # Used to check whether a steerage has a elation referral letter attached
    @property
    def has_elation_referral_letter_or_order(self) -> bool:
        if self.was_elation_letter_created_from_lucian:
            return True
        if self.referral and self.referral.elation_referral_orders.count() > 0:
            return True
        return False

    @property
    def has_waiver(self) -> bool:
        return True if self.waiver and self.waiver is not None else False

    @property
    def has_referral(self) -> bool:
        return True if self.referral and self.referral is not None else False

    @property
    def is_effectively_referral_only(self) -> bool:
        return True if self.has_referral and (not self.waiver or not self.waiver.is_active) else False

    @property
    def is_effectively_waiver_only(self) -> bool:
        return True if self.has_waiver and not self.has_referral else False

    @property
    def is_effectively_referral_and_waiver(self) -> bool:
        return True if self.waiver and self.waiver.is_active and self.has_referral else False

    @property
    def has_active_waiver(self) -> bool:
        return True if self.waiver and self.waiver.is_active else False

    def maybe_add_effective_dates(self):
        # if effective from is not set then add today as the effective from
        if self.effective_from is None and self.id is None:
            self.effective_from = date.today()

        # if we have locked for the first time and there is no effective_through date,
        # set it to 1 year from today when the fax expires
        if self.effective_through is None and self.recommendation_sent_at is not None:
            self.effective_through = self.recommendation_sent_at.date() + timedelta(days=365)

    def update_archive_at(self):
        # There are four cases
        # 1. If the steerage disposition is an excluded disposition
        #       then set the archive date as a yesterday date
        # 2. If scheduling date and effective_through is not present then set the archive_at as None
        # 3. If scheduling date is present and the number of units is less than 1
        #       then expiry date will be scheduling date + 1 day
        # 4. If scheduling date is not present or service units is greater than 1 and effective_through is present
        #       then expiry date will be effective_through date + 1 day

        if self.disposition and self.disposition.follow_through_success == SteerageSuccessConfig.EXCLUDED:
            # If the achieve date is today then it will still show the steerage in the active section
            # Hence making it yesterday to move the not needed steerage to achieve section
            self.archive_at = date.today() - timedelta(days=1)

        elif not self.scheduling_date and not self.effective_through:
            self.archive_at = None

        elif self.scheduling_date and (
            not self.waiver  # No waiver present, we dont need to check for quantity
            or (self.waiver and not self.waiver.is_active)  # Waiver is inactive, we don't check quantity
            # Waiver is active, check quantity. If a quantity is <=1 then set archive date to scheduling date + day
            # else fallback to effective through date
            or (
                self.waiver
                and self.waiver.is_active
                and hasattr(self, "service_quantity")
                and self.service_quantity
                and self.service_quantity.value is not None
                and self.service_quantity.value == 1
            )
        ):
            scheduling_date = self.scheduling_date
            if isinstance(scheduling_date, str):
                scheduling_date = datetime.strptime(scheduling_date, "%Y-%m-%d").date()
            self.archive_at = scheduling_date + timedelta(days=1)

        elif self.effective_through:
            effective_through = self.effective_through
            if isinstance(effective_through, str):
                effective_through = datetime.strptime(effective_through, "%Y-%m-%d").date()
            self.archive_at = effective_through + timedelta(days=1)

        return

    # Reset the transmission status so that these can be picked
    # up the next time we are pushing data to external vendors
    # Does nothing if the fields are already set to appropriate values
    def maybe_reset_transmission_status(self):
        waiver_transmission_status: WaiverTransmissionStatus = self.waiver.transmission_status
        if (
            waiver_transmission_status.status != VendorTransmissionStatusChoices.PENDING
            or waiver_transmission_status.sent_at is not None
            or waiver_transmission_status.response is not None
            or waiver_transmission_status.payload is not None
        ):
            waiver_transmission_status.status = VendorTransmissionStatusChoices.PENDING
            waiver_transmission_status.sent_at = None
            waiver_transmission_status.response = None
            waiver_transmission_status.payload = None
            waiver_transmission_status.save()

    def remove_selected_provider_by_member(self):
        steerage_provider_list = SteerageProvider.objects.filter(steerage=self.id)
        for provider in steerage_provider_list:
            # there will be atmost one steerage provider as selected
            if provider.member_selected_at is not None:
                provider.member_selected_at = None
                provider.save()
                return

    def mark_provider_as_selected_if_only_one(self):
        """
        If there is only one steerage provider when adding a scheduling date, mark that one as selected
        """
        providers: QuerySet[SteerageProvider] = SteerageProvider.objects.filter(
            steerage=self, status=SteerageProviderStatuses.ACCEPTED
        )
        if providers.count() == 1:
            provider: SteerageProvider = providers.first()
            # If a scheduling date is present and we haven't previously selected the provider to begin with,
            # set the selected date
            if provider.member_selected_at is None and self.scheduling_date is not None:
                logger.info(
                    "[SteerageProvider] Selecting steerage provider %s for steerage %s while adding a scheduling \
                    date since only one provider added",
                    provider.id,
                    self.id,
                )
                provider.select_steerage_provider()
                provider.save()
            # If the scheduling date is cleared, reset the selected at, this is to allow the case if a navigator
            # wants to add additional providers to the steerage
            if self.scheduling_date is None:
                logger.info(
                    "[SteerageProvider] Unselecting steerage provider %s for steerage %s while removing a scheduling \
                    date",
                    provider.id,
                    self.id,
                )
                provider.member_selected_at = None
                provider.save()

    def lock(self, user):
        from firefly.modules.referral.utils.steerage_utils import is_steerage_self_serviceable

        lock_date = datetime.now()
        self.is_locked = True
        self.locked_at = lock_date
        self.locked_by = user

        # If this is the first time locking the Steerage, also update the recommendation_sent_at field
        if self.recommendation_sent_at is None:
            self.recommendation_sent_at = lock_date

        # If this is the first time locking the Steerage, confirm if this is a self service steerage
        # Self service steerages are locked with NO accepted providers selected
        if self.is_self_service_enabled is None:
            self.is_self_service_enabled = is_steerage_self_serviceable(self)

    def unlock(self):
        self.is_locked = False
        self.locked_at = None
        self.locked_by = None
        # When unlocked: reset the transmission status
        # so that we can pick it up the next time we are
        # transmitting data out
        if self.has_waiver:
            self.maybe_reset_transmission_status()

    def is_postable(self) -> bool:
        return (
            self.is_locked is not None
            and self.is_locked is True
            and self.waiver is not None
            and self.waiver.transmission_status.status != VendorTransmissionStatusChoices.SUCCESS
            and self.has_active_waiver is True
        )

    # Limits the fields analyzed by DirtyFieldsMixin, to improve performance
    FIELDS_TO_CHECK = ["is_locked", "scheduling_date", "description", "disposition", "prior_authorization_system_id"]

    def update_segmentation(self):
        old_segment = self.segment
        segment_is_dirty = False
        if self.location_type or self.specialty_group:
            # Update segment based on the specialty and priority of the steerage
            # If the specialty is in COMMODITY_SEGMENT_SPECIALTY then segmented as COMMODITY
            # else if the steerage priority is urgent or specialty is in HIGH_TCOC_SEGMENT_SPECIALTY
            #   then segmented as HIGH_TCOC
            # else segmented as LOW_TCOC
            if (self.specialty_group and self.specialty_group.label in COMMODITY_SEGMENT_SPECIALTY) or (
                self.location_type and self.location_type.label in COMMODITY_SEGMENT_LOCATION_TYPES
            ):
                self.segment = SteerageSegment.COMMODITY
            elif (self.priority and self.priority == ReferralPriority.URGENT) or (
                self.specialty_group and self.specialty_group.label in HIGH_TCOC_SEGMENT_SPECIALTY
            ):
                self.segment = SteerageSegment.HIGH_TCOC
            else:
                self.segment = SteerageSegment.LOW_TCOC

            if old_segment is None and self.segment is not None:
                segment_is_dirty = True
                # Update the Referral Initiation case status on first segmentation
                self.update_steerage_case_on_segmentation()

        return segment_is_dirty

    def update_steerage_case_on_segmentation(self):
        # Once the case has been segmented, let's update the underlying Referral Initiation case status to
        # Location Search if its an Elation induced referral or referral is toggled off by clinician
        if (
            not self.is_initiated_from_lucian
            or not self.referral
            or (self.referral and self.referral.is_active is False)
        ):
            from firefly.modules.cases.models import Case
            from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case

            cases = Case.objects.filter(
                person_id=self.person_id,
                category__unique_key=REFERRAL_REQUEST,
                relations__content_type=ContentType.objects.get_for_model(Steerage),
                relations__object_id=self.id,
            )
            for case in cases:
                if case and case.status in [
                    ReferralRequestCaseStatuses.NEW,
                    ReferralRequestCaseStatuses.COMPLETE_IN_ELATION,
                ]:
                    apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.LOCATION_SEARCH)

    def update_referral_insurance_authorization_required(self, new_scheduling_date=None, segment=None):
        # If member has insurance plan type as HMO and scheduling date is added or if the
        # Insurance Approval case has been merged, if the segment is added, then enable the
        # insurance authorization toggle which will create the insurance approval case as a side effect.
        if (
            segment
            and self.referral
            and self.person
            and self.referral.insurance_authorization_required is None
            and self.person.insurance_info
            and self.person.insurance_info.plan_type
            and self.person.insurance_info.plan_type.upper() == InsuranceMemberInfo.PLAN_TYPE_HMO.upper()
        ):
            referral: Referral = self.referral
            referral.insurance_authorization_required = True
            referral.save(update_fields=["insurance_authorization_required"])

    # Calculate status based on fields on the Steerage
    # SteerageProviders have a foreign key to Steerage, so we can't use this save
    # method to update the status when they are saved. To compensate, the SteerageProvider
    # model has a `post_save_side_effect` method that calls this method on the Steerage
    # when a SteerageProvider is saved.
    def update_status(self):
        from firefly.modules.referral.utils.steerage_utils import is_steerage_self_serviceable

        self.status = SteerageStatus.REQUEST_RECEIVED

        if self.segment is not None:
            self.status = SteerageStatus.IN_PROGRESS

        # update_status getting called before the save() method is invoked, so checking if object exist
        # before any further operation
        if self.is_locked and self.pk:
            providers = self.steerage_providers.filter(status=SteerageProviderStatuses.ACCEPTED)
            providers_count = providers.count()
            has_selected = providers.filter(member_selected_at__isnull=False).exists()
            has_multiple_providers = providers_count > 1
            has_single_provider = providers_count == 1
            has_providers = (has_multiple_providers and has_selected) or has_single_provider

            is_pending_provider_selection = not has_selected and (
                has_multiple_providers or is_steerage_self_serviceable(self)
            )

            if has_providers and self.scheduling_date is not None:
                self.status = SteerageStatus.APPOINTMENT_SCHEDULED

            if has_providers and self.scheduling_date is None:
                self.status = SteerageStatus.PENDING_SCHEDULING

            if is_pending_provider_selection and self.scheduling_date is None:
                self.status = SteerageStatus.PENDING_PROVIDER_SELECTION
        if self.disposition and self.disposition.matches_case_status_name in [
            STEERAGE_NOT_NEEDED_ACTION,
            CARE_NOT_NEEDED_ACTION,
            RESTEERED_ACTION,
        ]:
            self.status = SteerageStatus.REQUEST_CANCELED
        # Should not overwrite request canceled status
        elif (
            self.status != SteerageStatus.REQUEST_ARCHIVED
            and self.scheduling_date is None
            and self.archive_at
            and self.archive_at.strftime("%Y-%m-%d") < date.today().strftime("%Y-%m-%d")
        ):
            self.status = SteerageStatus.REQUEST_ARCHIVED

        # Keep all the referral cases in sync with the steerage status
        from firefly.modules.referral.utils.referral_tags_utils import (
            link_tags_to_all_referral_cases,
        )

        # update_status getting called before the save() method is invoked, so checking if object exist
        # before link_tags_to_all_referral_cases call
        if self.pk:
            link_tags_to_all_referral_cases(self)

        return self.status

    def update_scheduling_date_if_excluded_disposition(self):
        """Remove the scheduling date if in an excluded disposition"""
        if self.disposition is not None and self.disposition.follow_through_success == SteerageSuccessConfig.EXCLUDED:
            self.scheduling_date = None

    def update_scheduling_date_disposition(self):
        """
        Set the dispostion to Member Scheduled on the steerage if there is a scheduling date.
        If the scheduling date is cleared, reset the disposition to None
        """
        if self.scheduling_date is not None:
            disposition: Optional[SteerageDisposition] = SteerageDisposition.objects.filter(
                matches_case_status_name=MEMBER_SCHEDULED_ACTION
            ).first()
            self.disposition = disposition
        elif (
            self.scheduling_date is None
            and self.disposition
            and self.disposition.matches_case_status_name == MEMBER_SCHEDULED_ACTION
        ):
            self.disposition = None

        logger.info(
            "[SteerageDisposition] Setting steerage ID %s disposition %s from update_scheduling_date_disposition",
            self.id,
            self.disposition.matches_case_status_name if self.disposition else None,
        )

    def sync_zus_scheduling_date(self):
        """
        Sync the scheduling date to ZUS
        """
        from firefly.core.services.zus.tasks import async_sync_specialty_appointment

        firefly_tenant, _ = Tenant.objects.get_or_create(key=FIREFLY_TENANT_KEY)
        existing_alias_mapping = AliasMapping.get_alias_id_for_object(self, AliasName.ZUS, tenant_obj=firefly_tenant)

        pcp_program = Program.objects.get(uid=ProgramCodes.PRIMARY_CARE)
        is_person_enrolled_in_care = person_is_enrolled_in_program(self.person.id, pcp_program)

        # If there is no scheduling date and no alias mapping or not in care, skip the sync
        if (existing_alias_mapping or self.scheduling_date) and is_person_enrolled_in_care:
            async_sync_specialty_appointment.send(self.id)

    def set_default_priority_for_coverage_only(self):
        """If a coverage only referral, since we do not require a priority,
        set it to Standard if one is not provided"""

        if self.is_effectively_waiver_only and self.priority is None:
            self.priority = ReferralPriority.STANDARD

    def update_steerage_analytics_success(self):
        """
        Set the steerage analytics booleans when the scheduling date is changed or an Excluded disposition is added.
        """

        self.in_follow_through_success_denominator = True

        if self.disposition is not None and self.disposition.follow_through_success == SteerageSuccessConfig.EXCLUDED:
            self.in_follow_through_success_numerator = False
            self.in_follow_through_success_denominator = False

        if self.scheduling_date is None:
            self.in_follow_through_success_numerator = False
        else:
            self.in_follow_through_success_numerator = True

    def handle_when_prior_auth_system_id_is_dirty(self):
        case_to_update = None
        for cr in self.case_relations.iterator():
            case = cr.case
            if case.category.unique_key == REFERRAL_REQUEST:
                case_to_update = case
                break
        if self.scheduling_date and self.prior_authorization_system_id and case_to_update and self.is_locked:
            from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case

            # Move to member scheduled only in case when either referral is not present
            # or insurance auth is marked as False or insurance details are added
            if (
                self.is_locked is True
                and self.scheduling_date
                and case_to_update
                and (
                    not self.referral
                    or not self.referral.insurance_authorization_required
                    or (
                        self.referral.insurance_authorization_required is True
                        and self.referral.referral_insurance_authorization.authorization_number
                    )
                )
            ):
                if waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV) and self.is_self_service_enabled:
                    # Check if the fax has been sent. If not, let's update the status to Pending Fax since
                    # we do not want to close the case until the fax has been sent and scheduling date
                    # has been added.
                    if (
                        self.referral
                        and self.referral.is_active
                        and self.referral.elation_referral_orders.first()
                        and self.referral.elation_referral_orders.first().elation_referral_letter
                        and self.referral.elation_referral_orders.first().elation_referral_letter.fax_status
                        != FaxStatus.SUCCESS
                    ):
                        apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.PENDING_FAX)
                        return

                # Move to member scheduled only in case when either referral is not present
                # or insurance auth is marked as False or insurance details are added
                apply_action_to_steerage_case(case_to_update, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)

    def handle_steerage_case_transition_when_is_locked_is_dirty(self):
        from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case
        from firefly.modules.work_units.constants import StatusCategory

        # If the steerage is locked and have member scheduling date then update the status to member scheduled
        # If the steerage is locked and doesn't have member scheduling date then do nothing
        case_to_update = None
        for cr in self.case_relations.iterator():
            case = cr.case
            if case.category.unique_key == REFERRAL_REQUEST:
                case_to_update = case
                break

        if (
            self.is_locked is True
            and self.scheduling_date
            and case_to_update
            and (
                not self.referral
                or not self.referral.insurance_authorization_required
                or (
                    self.referral.insurance_authorization_required is True
                    and self.referral.referral_insurance_authorization.authorization_number
                )
            )
        ):
            if waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV) and self.is_self_service_enabled:
                # Check if the fax has been sent. If not, let's update the status to Pending Fax since
                # we do not want to close the case until the fax has been sent and scheduling date
                # has been added.
                if (
                    self.referral
                    and self.referral.is_active
                    and self.referral.elation_referral_orders.first()
                    and self.referral.elation_referral_orders.first().elation_referral_letter
                    and self.referral.elation_referral_orders.first().elation_referral_letter.fax_status
                    != FaxStatus.SUCCESS
                ):
                    apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.PENDING_FAX)
                    return

            # Move to member scheduled only in case when either referral is not present
            # or insurance auth is marked as False or insurance details are added
            apply_action_to_steerage_case(case_to_update, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)

        # If the steerage is unlocked and the current status is member scheduled then move the status to reopened
        if (
            self.is_locked is False
            and self.recommendation_sent_at
            and case_to_update
            and case.status
            and case.status_category in [StatusCategory.COMPLETE, StatusCategory.DEFERRED]
        ):
            apply_action_to_steerage_case(case_to_update, ReferralRequestCaseStatuses.REOPENED)

    def save(self, *args, **kwargs):
        # We want to evaluate the dirty fields before saving, so they are indeed
        # dirty, but we need the object saved so that the called function can
        # have an ID in the database. Thus, message pass with some booleans.
        description_is_dirty = False
        is_locked_is_dirty = False
        scheduling_date_is_dirty = False
        prior_authorization_system_id_is_dirty = False
        existing_scheduling_date = None
        new_scheduling_date = None

        # Fun fact: you need "check_relationship" to look at ForeignKey attributes
        if self.is_dirty(check_relationship=True):
            dirty_fields = self.get_dirty_fields(check_relationship=True)
            if "prior_authorization_system_id" in dirty_fields.keys():
                prior_authorization_system_id_is_dirty = True
            if "description" in dirty_fields.keys():
                description_is_dirty = True
            if "is_locked" in dirty_fields.keys():
                is_locked_is_dirty = True
            if "scheduling_date" in dirty_fields.keys():
                scheduling_date_is_dirty = True
                existing_scheduling_date = dirty_fields["scheduling_date"]
                new_scheduling_date = self.scheduling_date

        # As we are dual writing to the steerage fields,
        # so whenever a steerage is created add today as a default date for effective_from
        self.maybe_add_effective_dates()

        # If coverage only member, set the priority to Standard by default
        self.set_default_priority_for_coverage_only()

        # If the disposition is excluded, update the scheduling date to None
        self.update_scheduling_date_if_excluded_disposition()

        # If there is a scheduling date, the disposition should be updated to Member Scheduled, if not a scheduling date
        # and disposition was previously Member Scheduled, then remove the disposition
        self.update_scheduling_date_disposition()

        # Set the follow through numerator and denominator once the scheduling date has been updated
        self.update_steerage_analytics_success()

        # Update archive_at
        self.update_archive_at()

        # Updates the segment
        segment_is_dirty = self.update_segmentation()

        # Update the status of the steerage
        self.update_status()

        super(Steerage, self).save(*args, **kwargs)

        # Add system suggested providers to steerage once we segment steerage
        if self.segment:
            # Only add system suggested providers if the segment is dirty
            if segment_is_dirty:
                from firefly.modules.referral.tasks import add_system_suggested_providers_async

                add_system_suggested_providers_async.send(self.id)

            # This is to set the HMO toggle on segmentation for when the Insurance Approval case is merged into
            # Referral Initiation
            self.update_referral_insurance_authorization_required(segment=self.segment)

        # When the steerage is locked - save all the steerage provider as a curated contact information
        if self.is_locked:
            from firefly.modules.referral.utils.steerage_utils import store_curated_provider

            try:
                store_curated_provider(self)
            except Exception:
                logger.exception("Failed to store Curated Provider for steerage_id:%d ", self.id)

        # Now, execute follow-up functions
        if description_is_dirty:
            from firefly.modules.referral.utils.steerage_utils import sync_steerage_case_description

            sync_steerage_case_description(self)

        if prior_authorization_system_id_is_dirty:
            self.handle_when_prior_auth_system_id_is_dirty()

        if is_locked_is_dirty:
            if self.has_referral and self.referral is not None:
                from firefly.modules.referral.utils.referral_utils import (
                    maybe_queue_for_transmission,
                )

                maybe_queue_for_transmission(self.referral, self.is_locked)

            self.handle_steerage_case_transition_when_is_locked_is_dirty()

            logger.info(
                "[SteerageDisposition] Steerage ID %s locked with disposition %s from save",
                self.id,
                self.disposition.matches_case_status_name if self.disposition else None,
            )

        if scheduling_date_is_dirty:
            from firefly.modules.referral.utils.steerage_utils import (
                get_or_create_note_retrieval_case,
                handle_scheduling_date_related_case_transitions,
            )
            from firefly.modules.referral.zus.utils import (
                WAFFLE_SWITCH_SYNC_ZUS_SPECIALTY_APPOINTMENTS,
            )

            get_or_create_note_retrieval_case(self)
            handle_scheduling_date_related_case_transitions(self, existing_scheduling_date, new_scheduling_date)
            self.update_referral_insurance_authorization_required(new_scheduling_date=new_scheduling_date)
            if waffle.switch_is_active(WAFFLE_SWITCH_SYNC_ZUS_SPECIALTY_APPOINTMENTS):
                self.sync_zus_scheduling_date()
            self.mark_provider_as_selected_if_only_one()


class SteerageService(BaseModelV3):
    """
    Table to assist in the ManyToMany join between Steerage and Service.
    """

    steerage = models.ForeignKey(
        Steerage,
        related_name="steerage_services",
        on_delete=models.CASCADE,
    )

    service = models.ForeignKey(
        Service,
        related_name="steerage_services",
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["steerage", "service"],
                condition=Q(deleted=None),
                name="steerage_service_uniq",
            )
        ]


class SteerageClinicalFocusArea(BaseModelV3):
    """
    Table to assist in the ManyToMany join between Steerage and Clinical Focus Area.
    """

    steerage = models.ForeignKey(
        Steerage,
        related_name="steerage_clinical_focus_areas",
        on_delete=models.CASCADE,
    )

    clinical_focus_area = models.ForeignKey(
        ClinicalFocusArea,
        related_name="steerage_clinical_focus_areas",
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["steerage", "clinical_focus_area"],
                condition=Q(deleted=None),
                name="steerage_clinical_focus_area_uniq",
            )
        ]


class SteerageProviderWaiverLevel(models.TextChoices):
    FACILITY = "Facility", "Facility"
    PROVIDER = "Provider", "Provider"
    NOT_APPLICABLE = "Not Applicable", "Not Applicable"


class SteerageProviderDataSourceConfig:
    RIBBON = "ribbon"
    FIREFLY = "firefly"
    MANUAL = "manual"
    TALON = "talon"


STEERAGE_PROVIDER_DATA_SOURCE_CHOICES = [
    (SteerageProviderDataSourceConfig.TALON, "Talon"),
    (SteerageProviderDataSourceConfig.RIBBON, "Ribbon"),
    (SteerageProviderDataSourceConfig.FIREFLY, "Firefly"),
    (SteerageProviderDataSourceConfig.MANUAL, "Manual"),
]


class SearchRequest(PollableJobMixin, BaseModelV3):
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    person = models.ForeignKey(Person, related_name="+", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    insurance_payer = models.ForeignKey(
        InsurancePayer,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    insurance_plan = models.ForeignKey(
        InsurancePlan,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    networks = BaseModelV3ManyToManyField(Network, related_name="search_requests", through="SearchRequestNetworks")

    vendor_network_uids = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        blank=True,
        null=True,
        default=list,
    )

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    specialty = models.ForeignKey(Specialty, related_name="+", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    specialty_group = models.ForeignKey(
        SpecialtyGroup,
        related_name="search_requests",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    cpt_code = models.ForeignKey(
        CPTCode,
        related_name="search_requests",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    procedure_uids = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        blank=True,
        null=True,
        default=list,
    )

    clinical_focus_area_uids = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        blank=True,
        null=True,
        default=list,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    specialty_uids = ArrayField(models.CharField(max_length=255, blank=True, null=True), blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    location_type = models.ForeignKey(LocationType, related_name="+", null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ribbon_specialty_uid = deprecate_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    npis = ArrayField(models.CharField(max_length=255, blank=True, null=True), blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    address = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    gender = models.CharField(max_length=1, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    distance = models.IntegerField(null=False, blank=False)

    min_location_confidence = models.IntegerField(null=True, blank=True)

    page_size = models.IntegerField(null=False, blank=False)

    page = models.IntegerField(null=False, blank=False)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    facility_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    records_available = models.IntegerField(null=True, blank=True)

    results = JSONField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor_http_status_code = models.CharField(max_length=20, blank=True, null=True, default=None)  # noqa: TID251

    vendor_results = JSONField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    search_status = models.CharField(  # noqa: TID251
        max_length=50,
        blank=True,
        null=True,
        choices=SEARCH_STATUS_CHOICES,
        default=None,
    )
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    steerage = models.ForeignKey(Steerage, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    referral_priority = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralPriority.choices,
    )
    search_availability = models.ForeignKey(
        SearchAvailability,
        null=True,
        blank=True,
        related_name="search_requests",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # Stores the payload for the search request
    payload = JSONField(null=True, blank=True)

    # Stores the name of vendor for which the search request was made
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    vendor = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=SearchRequestVendor.choices,
    )

    is_automated_suggestion = models.BooleanField(null=True, blank=True, default=False)

    class Meta(BaseModelV3.Meta):
        abstract = False
        db_table = "referral_searchrequest"

    def job_handler(self, job_id):
        logger.info(
            "[SearchRequest job_handler]: Starting search request job: %s for the vendor: %s", job_id, self.vendor
        )
        # Handle Ribbon provider search
        if self.vendor == SearchRequestVendor.RIBBON:
            from firefly.modules.referral.utils.self_service_utils import (
                get_self_service_ribbon_results,
            )

            result = get_self_service_ribbon_results(self)
            self.payload = result["payload"]
            self.records_available = result["records_available"]
            self.results = result["search_results"]
            self.search_status = SearchStatus.SUCCESS
            self.vendor_http_status_code = result["vendor_http_status_code"]
            self.vendor_results = result["vendor_results"]
            self.save()

        if self.vendor == SearchRequestVendor.TALON and self.status != JobStatus.COMPLETE:
            from firefly.modules.referral.utils.talon_search_utils import (
                get_procedure_search_results,
            )

            # Handle nullable address
            if not self.address:
                raise ValueError("Search request address is required")
            # Handle nullable distance
            if not self.distance:
                raise ValueError("Search request distance is required")

            # 1. Call the Talon API with the Payload
            # 2. Process the response and convert it into the FireflyFormat
            result = get_procedure_search_results(self.payload)
            self.records_available = result["records_available"]
            self.results = result["search_results"]
            self.search_status = SearchStatus.SUCCESS
            self.vendor_http_status_code = result["vendor_http_status_code"]
            self.vendor_results = result["vendor_results"]
            self.save()


# SteerageProvider stores the results of the providers associated
# with a steerage workflow (where Firefly navigates care for our members).
class SteerageProvider(SaveHandlersMixin, BaseProviderMixin, DirtyFieldsMixin):
    # Specialty of the provider
    specialty_list = ArrayField(
        models.TextField(blank=False, null=False),
        blank=True,
        null=True,
    )

    steerage = models.ForeignKey(
        Steerage,
        related_name="steerage_providers",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Store the source of the provider information (external vendor vs entered
    # by a Firefly member)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    data_source = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=STEERAGE_PROVIDER_DATA_SOURCE_CHOICES,
    )

    # should be a foreign key and should point to the internal provider
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    source_identifier = models.CharField(max_length=255, null=True, blank=True, default=None)  # noqa: TID251

    # Specifies why the Nav Specialist needed to look outside of our primary directory.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason_for_exception = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
        default=None,
        choices=REASON_FOR_EXCEPTION_CHOICES,
    )

    # The date on which the Member made a selection in the App of where they intend to go.
    member_selected_at = models.DateField(
        blank=True,
        null=True,
    )

    # Store whether or not Firefly deemed this provider "Recommended", per Ribbon's cost/quality
    # and ratings metrics, as surfaced in the Provider Search.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    recommendation_status = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
        choices=RecommendationStatusChoices.choices,
    )

    # It store whether a facility is waived or a provider is waived
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    waiver_level = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=SteerageProviderWaiverLevel.choices,
    )

    # Stores whether this provider's contact details were verified
    # in the Directory before adding to the Steerage
    has_verified_contact_details = models.BooleanField(
        null=True,
        blank=True,
    )

    # Limits the fields analyzed by DirtyFieldsMixin, to improve performance
    FIELDS_TO_CHECK = ["member_selected_at"]

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    network_partner_agreement_type = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=AGREEMENT_TYPE_CHOICES,
    )

    # It will be storing the specialty group that was searched in the provider search
    # while adding this steerage provider
    # It will be useful finding department availability for this steerage provider
    specialty_group = models.ForeignKey(SpecialtyGroup, null=True, blank=True, on_delete=models.PROTECT)

    # Latitude and Longitude ##.###### and ###.######
    latitude = models.DecimalField(max_digits=8, decimal_places=6, null=True, blank=True, default=None)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True, default=None)

    # Status of the provider to track if a provider has been approved by a navigator. This will be used for
    # automated referrals when steerage providers are added automatically but need approval from a navigator
    # in order to show to member.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
        choices=STEERAGE_PROVIDER_STATUS_CHOICES,
    )
    reviewed_at = models.DateTimeField(
        null=True,
        blank=True,
    )
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # This will be true only for the steerage providers which were added via system recommendation
    is_system_suggested = models.BooleanField(
        null=True,
        blank=True,
        default=False,
    )

    # If the steerage provider is added via provider search then this will store the search request details
    search_request = models.ForeignKey(
        SearchRequest,
        related_name="search_request",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # Stores the composite score of the provider, which is calculated based on cost, quality and rating
    composite_score = models.FloatField(null=True, blank=True, default=None)

    # Stores the Firefly scaled cost, quality, expertise and ratings provided by Vendors
    cost_score = models.FloatField(null=True, blank=True, default=None)
    quality_score = models.FloatField(null=True, blank=True, default=None)
    average_rating = models.FloatField(null=True, blank=True, default=None)
    number_of_rating = models.IntegerField(null=True, blank=True, default=None)
    expertise_score = models.FloatField(null=True, blank=True, default=None)

    # Stores the reason why this provider is recommended by Firefly
    recommendation_reason = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        blank=True,
        null=True,
        default=list,
    )
    # This true when a provider is manually edited from cases view
    is_manually_edited = models.BooleanField(
        null=True,
        blank=True,
        default=False,
    )

    # Stores the langagues that provider supports
    languages = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        blank=True,
        null=True,
        default=list,
    )

    # Stores the Ribbon confidence score for a provider location
    location_confidence_score = models.IntegerField(null=True, blank=True)

    # Stores the reason for when an automated suggestion is rejected
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    reason_for_rejection = models.CharField(  # noqa: TID251
        max_length=255,
        null=True,
        blank=True,
        default=None,
        choices=REASON_FOR_REJECTION_CHOICES,
    )

    # Stores the distance *at the time of search* between the provider and the member
    distance = models.FloatField(null=True, blank=True, default=None)

    # Stores the type of the Provider - In Person/In Home/Virtual
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_type = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=CuratedProviderType.choices,
    )
    # Stores the partner name for the provider
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    partner_name = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )
    # Links to the partnership
    partnership = models.ForeignKey(
        Partnership,
        related_name="steerage_providers",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    tax_identifiers = BaseModelV3ManyToManyField(
        TaxIdentifier, related_name="steerage_providers", blank=True, through="SteerageProviderIdentifiers"
    )

    # This will be true if the waiver was sent to the TPA
    included_in_waiver_transmission = models.BooleanField(
        null=True,
        blank=True,
        default=False,
    )

    gender = models.TextField(
        blank=True,
        null=True,
    )

    # Stores the clinical focus areas for the provider.
    # Not to be confused with clinical areas at the Steerage level or elsewhere.
    # These are values that come directly from the provider search API so that we can display them in the UI.
    clinical_areas = ArrayField(
        models.TextField(blank=True, null=True),
        blank=True,
        null=True,
    )

    # Stores the degrees for the provider
    degrees = ArrayField(
        models.TextField(blank=True, null=True),
        blank=True,
        null=True,
    )

    # Stores the educations for the provider
    educations = ArrayField(
        models.TextField(blank=True, null=True),
        blank=True,
        null=True,
    )

    # Stores CPTCode for the procedure search
    cpt_code = models.TextField(max_length=5, null=True, blank=True)

    # Stores the estimated cost for the encounter and its breakdown
    estimated_cost = models.FloatField(null=True, blank=True)
    cost_breakdown = models.JSONField(null=True, blank=True)

    @property
    def live_availability(self) -> Optional[LocationAvailability]:
        return fetch_curated_providers_for_steerage_provider(
            npi=self.npi,
            care_org_name=self.care_organization_name,
            zip_code=self.zip_code,
            specialty_group_id=self.specialty_group.id if self.specialty_group else None,
        )

    def save(self, *args, **kwargs):
        from firefly.core.services.zus.tasks import async_sync_service_request
        from firefly.modules.referral.tasks import save_provider_latitude_longitude_externally_async
        from firefly.modules.referral.zus.utils import WAFFLE_SWITCH_SYNC_ZUS_SPECIALTY_APPOINTMENTS

        created = self.pk is None

        # If the Member is selecting a SteerageProvider, we want to update the
        # associated Case to move it into a status that can notify the Navigation
        # Operations team.
        if self.is_dirty():
            from firefly.modules.referral.utils.steerage_utils import handle_provider_selection

            dirty_fields = self.get_dirty_fields()
            member_previously_selected = bool(dirty_fields["member_selected_at"]) and not created
            member_newly_selected = bool(self.member_selected_at)
            handle_provider_selection(self.steerage, member_previously_selected, member_newly_selected)

        if self.latitude is None and self.longitude is None:
            save_provider_latitude_longitude_externally_async.send(self.id)

        # Set the is_system_suggested to false if the the value is not set - it means it was added through CRM
        if self.is_system_suggested is None or self.is_system_suggested == "":
            self.is_system_suggested = False

        self.handle_waiver_level()

        super().save(*args, **kwargs)

        # If we added a new provider/let's deselect any previous provider we may have added
        # and also clear out the scheduling date since the member should select a new provider at this
        # point
        if waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV) and created:
            self.handle_adding_new_steerage_provider()

        # If the steerage provider is created/updated then update the waiver transmission to pending
        if (
            self.steerage.is_locked
            and self.status == SteerageProviderStatuses.ACCEPTED
            and waffle.switch_is_active(WAFFLE_SWITCH_REFERRAL_CARE_KEY_ELIGIBLE)
            and self.steerage.has_waiver
        ):
            self.steerage.maybe_reset_transmission_status()

        # When accepted provider added, sync the steerage to zus, we will not be syncing
        # coverage only navigation requests
        if (
            self.status == SteerageProviderStatuses.ACCEPTED
            and waffle.switch_is_active(WAFFLE_SWITCH_SYNC_ZUS_SPECIALTY_APPOINTMENTS)
            and self.steerage.person
        ):
            if person_is_enrolled_in_program(
                self.steerage.person.id, Program.objects.get(uid=ProgramCodes.PRIMARY_CARE)
            ):
                async_sync_service_request.send(self.steerage.id)

    def handle_waiver_level(self):
        # If the waiver level is not set then add the waiver level:
        # 1. If the recommendation status is Recommended then set the waiver level based
        #    on the npi
        # 2. Else set it as not Applicable
        if not self.waiver_level and self.steerage and self.steerage.waiver and self.steerage.waiver.is_active is True:
            if waffle.switch_is_active(WAFFLE_SWITCH_REFERRAL_CARE_KEY_ELIGIBLE):
                if self.recommendation_status == RecommendationStatusChoices.RECOMMENDED:
                    if self.npi:
                        self.waiver_level = SteerageProviderWaiverLevel.PROVIDER
                    else:
                        self.waiver_level = SteerageProviderWaiverLevel.FACILITY
                else:
                    self.waiver_level = SteerageProviderWaiverLevel.NOT_APPLICABLE
            else:
                if self.npi:
                    self.waiver_level = SteerageProviderWaiverLevel.PROVIDER
                else:
                    self.waiver_level = SteerageProviderWaiverLevel.FACILITY

    def maybe_update_steerage_status(self):
        if self.steerage is not None and self.steerage.is_locked:
            self.steerage.update_status()
            self.steerage.save(update_fields=["status"])

    def delete(self, *args, **kwargs):
        from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case

        if self.member_selected_at:
            self.steerage.scheduling_date = None
            self.steerage.save(update_fields=["scheduling_date", "status", "disposition"])
            for case_relation in self.steerage.case_relations.iterator():
                case = case_relation.case
                if (
                    waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV)
                    and self.steerage.is_self_service_enabled
                    and case.category.unique_key == REFERRAL_REQUEST
                    and case.status == ReferralRequestCaseStatuses.PENDING_FAX
                    and not self.steerage.scheduling_date
                ):
                    apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.OUTREACH_1)
        # If the steerage provider is deleted, update the steerage status
        super().delete(*args, **kwargs)
        self.maybe_update_steerage_status()

        # If the steerage provider is deleted then update the waiver transmission to pending to reflect this change
        if (
            self.steerage.is_locked
            and waffle.switch_is_active(WAFFLE_SWITCH_REFERRAL_CARE_KEY_ELIGIBLE)
            and self.steerage.has_waiver
        ):
            self.steerage.maybe_reset_transmission_status()

    # Only hook into the Steerage status update if the Steerage is locked
    # If it's not locked, the status won't look at the existence of a provider
    # and calculation can be deferred to a potential future locking event
    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        if changed("steerage") or changed("member_selected_at"):
            self.maybe_update_steerage_status()

    def select_steerage_provider(self):
        self.member_selected_at = date.today()

    def handle_adding_new_steerage_provider(self):
        self.steerage.scheduling_date = None
        self.steerage.save(update_fields=["scheduling_date", "status", "disposition"])
        providers = self.steerage.steerage_providers.filter(member_selected_at__isnull=False).exclude(id=self.id)
        for provider in providers:
            provider.member_selected_at = None
            provider.save()

    @property
    def search_result(self):
        """
        This is recreating the structure of the Ribbon search result so that the front end
        can render SteerageProviders in our DB in the same fashion.
        """
        return {
            "firefly_recommendation_factors": {
                "number_of_ratings": self.number_of_rating,
                "cost_score_out_of_five": self.cost_score,
                "quality_score_out_of_five": self.quality_score,
                "average_rating_out_of_five": self.average_rating,
                "expertise_score_out_of_five": self.expertise_score,
            },
            "first_name": self.first_name,
            "gender": None,  # TODO: Add this
            "languages": self.languages,
            "last_name": self.last_name,
            "location_types": None,  # TODO: Add this
            "locations": [
                {
                    "address_details": {
                        "street": " ".join([self.address_line_1, self.address_line_2])
                        if isinstance(self.address_line_1, str) and isinstance(self.address_line_2, str)
                        else self.address_line_1,
                        "address_line_1": self.address_line_1 if self.address_line_1 else "",
                        "address_line_2": self.address_line_2 if self.address_line_2 else "",
                        "city": self.city if self.city else "",
                        "state": self.state.abbreviation if self.state and self.state.abbreviation else "",
                        "zip": self.zip_code if self.zip_code else "",
                    },
                    "availability": self.live_availability,
                    "confidence": self.location_confidence_score,
                    "distance": self.distance,
                    "fax_numbers": [
                        {
                            "fax": self.fax if self.fax else "",
                            "details": "primary",
                        }
                    ],
                    "is_verified": self.has_verified_contact_details,
                    "location_in_network": None,  # TODO: Add this
                    "name": self.care_organization_name if self.care_organization_name else None,
                    "network_partner_agreement_type": self.network_partner_agreement_type,
                    "partner_name": self.partner_name,
                    "phone_numbers": [
                        {
                            "phone": self.phone if self.phone else "",
                            "details": "primary",
                        }
                    ],
                    "unique_identifier": self.source_identifier,
                }
            ],
            "middle_name": self.middle_name,
            "name": (self.first_name + " " + self.last_name)
            if (self.first_name and self.last_name)
            else (self.first_name or self.last_name or self.care_organization_name),
            "npi": self.npi if self.npi else "",
            "recommendation_reason_labels": ", ".join(self.recommendation_reason) if self.recommendation_reason else "",
            "specialties": self.specialty_list,
            "specialty_groups": None,
            "unique_identifier": self.source_identifier,
            "provider_type": self.provider_type,
            "partnership": {
                "id": self.partnership.id if self.partnership else None,
                "partner_name": self.partnership.partner_name if self.partnership else None,
                "agreement_type": self.partnership.agreement_type if self.partnership else None,
                "partnership_type": self.partnership.partnership_type if self.partnership else None,
            },
            "talon_recommendation_factors": {
                "encounter_price": self.estimated_cost,
                "expertise_score": self.expertise_score,
                "price_percentiles": None,
            },
        }


class SearchRequestNetworks(BaseModelV3):
    searchrequest = models.ForeignKey(
        SearchRequest,
        related_name="searchrequest_networks",
        on_delete=models.CASCADE,
    )

    network = models.ForeignKey(
        Network,
        related_name="searchrequest_networks",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "referral_searchrequest_networks"
        verbose_name_plural: str = "Search request networks"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["searchrequest", "network"],
                condition=Q(deleted=None),
                name="referral_searchrequest_networks_uniq",
            )
        ]


# Once waivers are finalized/locked they are sent out to an external
# vendor (currently only Flume).
class WaiverTransmissionStatus(BaseModelV3):
    waiver = models.OneToOneField(
        Waiver,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name="transmission_status",
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=VendorTransmissionStatusChoices.choices,
    )
    sent_at = models.DateTimeField(
        blank=True,
        null=True,
    )
    payload = models.JSONField(null=True, blank=True)
    response = models.JSONField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "Waiver transmission statuses"


class SteerageServiceQuantity(ServiceQuantityMixin):
    steerage = models.OneToOneField(
        Steerage,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name="service_quantity",
    )

    class Meta(BaseModelV3.Meta):
        verbose_name_plural = "Steerage service quantities"


class ConversionStatus(models.TextChoices):
    PENDING = "pending", "pending"
    SUCCEEDED = "succeeded", "succeeded"
    FAILED = "failed", "failed"


class PriorAuthorizationWebhook(BaseModelV3):
    ConversionStatus = ConversionStatus

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event_id = models.CharField(max_length=50)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    event_type = models.CharField(max_length=50)  # noqa: TID251
    event_timestamp = models.DateTimeField(null=True, blank=True)
    data = models.JSONField()

    prior_authorization = models.ForeignKey(PriorAuthorization, on_delete=models.CASCADE, null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    conversion_status = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=ConversionStatus.choices,
    )

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="%(app_label)s_%(class)s_conversion_status_valid",
                check=models.Q(conversion_status__in=ConversionStatus.values),
            )
        ]


# Representation of a defined concept using a symbol from a defined "code system"
# CodeMixin models an abstract class that should be inherited by models that
# store data about codes.
# Currently this is being sub-classed by the PriorAuthDiagnosisCodes and
# PriorAuthProcedureCodes models.
# Ideally these models would optionally link to an actual code (ICD10/ CPT / ...)
# based on the combination of code, system, and version attributes
# For now : we store these `raw` values in the model to enable linking in a
# future iteration
# TODO: Link to a standardized code
class CodeMixin(BaseModelV3):
    # Symbol in syntax defined by the system
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    code: models.CharField = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )
    # Identity of the terminology system
    # TODO: Add a list of valid systems
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    system: models.CharField = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )
    # Version of the system - if relevant
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    version: models.CharField = models.CharField(  # noqa: TID251
        max_length=255,
        blank=True,
        null=True,
    )

    # Links to CPT Code tables
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=models.Q(app_label="code_systems", model="cptcode"),
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")

    class Meta(BaseModelV3.Meta):
        abstract = True
        db_table: Optional[str] = None


class PriorAuthDiagnosisCode(CodeMixin):
    prior_authorization = models.ForeignKey(
        PriorAuthorization,
        on_delete=models.CASCADE,
        null=True,
        related_name="diagnosis_codes",
    )


class PriorAuthProcedureCode(CodeMixin):
    prior_authorization = models.ForeignKey(
        PriorAuthorization,
        on_delete=models.CASCADE,
        null=True,
        related_name="procedure_codes",
    )


class FaxStatus(models.TextChoices):
    PENDING = "pending", "pending"
    SUCCESS = "success", "success"
    FAILED = "failed", "failed"


class ReferralLetterDeliveryMethod(models.TextChoices):
    EMR = "emr", "emr"
    PASSPORT = "passport", "passport"
    FAX = "fax", "fax"
    EMAIL = "email", "email"
    PRINTED = "printed", "printed"
    DIRECT = "direct", "direct"


class ReferralLetterType(models.TextChoices):
    PATIENT_INITIALTED = "patient_initiated", "patient_initiated"
    PATIENT = "patient", "patient"
    REFERRAL = "referral", "referral"
    PROVIDER = "provider", "provider"


class ReferralSendToContact(BaseModelV3):
    """
    Model to store send_to_contact details of Elation Referral Letter
    """

    elation_id = models.BigIntegerField(unique=False)

    first_name = models.TextField(null=True, blank=True)

    last_name = models.TextField(null=True, blank=True)

    org_name = models.TextField(null=True, blank=True)

    # we receive a list of specialties in format [{'id': 1335, 'name': 'Cardiology']. Hence storing it a JSONField
    specialties = JSONField(default=list, null=True, blank=True)

    npi = models.TextField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        unique_together = ("first_name", "last_name", "org_name", "npi")


class ElationReferralLetter(BaseModelV3, DirtyFieldsMixin):
    """
    Model to store Elation referral letter
    """

    elation_id = models.BigIntegerField(unique=True)

    send_to_contact = models.ForeignKey(
        ReferralSendToContact,
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    send_to_name = models.TextField(null=True, blank=True)

    send_to_elation_user = models.BigIntegerField(unique=False, null=True, blank=True)

    fax_to = models.TextField(null=True, blank=True)

    display_to = models.TextField(null=True, blank=True)

    to_number = models.TextField(null=True, blank=True)

    subject = models.TextField(null=True, blank=True)

    body = models.TextField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    fax_status = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=FaxStatus.choices,
    )

    fax_attachments = models.BooleanField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    delivery_method = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=ReferralLetterDeliveryMethod.choices,
    )

    failure_unacknowledged = models.BooleanField(null=True, blank=True)

    direct_message_to = models.TextField(null=True, blank=True)

    email_to = models.TextField(null=True, blank=True)

    viewed_at = models.TextField(null=True, blank=True)

    delivery_date = models.DateTimeField(null=True, blank=True)

    with_archive = models.BooleanField(null=True, blank=True)

    is_processed = models.BooleanField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    letter_type = models.CharField(max_length=50, null=True, blank=True, choices=ReferralLetterType.choices)  # noqa: TID251

    sign_date = models.DateTimeField(null=True, blank=True)

    document_date = models.DateTimeField(null=True, blank=True)

    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="elation_referral_letters",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    signed_by = models.ForeignKey(
        Physician,
        blank=True,
        null=True,
        related_name="elation_referral_letters",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    FIELDS_TO_CHECK = ["fax_status"]

    def save(self, *args, **kwargs):
        created = self.pk is None

        is_fax_status_dirty = False

        if self.is_dirty(check_relationship=True):
            dirty_fields = self.get_dirty_fields(check_relationship=True)
            if "fax_status" in dirty_fields.keys():
                is_fax_status_dirty = True

        super(ElationReferralLetter, self).save(*args, **kwargs)

        if not created:
            from firefly.modules.referral.tasks import update_referral_letter_dependent_models

            update_referral_letter_dependent_models.send_with_options(args=(self.pk,), delay=0)

        # Update the case tags whenever a elation referral letter is created or updated
        from firefly.modules.referral.utils.referral_tags_utils import (
            update_case_tags_link_on_elation_letter_change,
        )

        update_case_tags_link_on_elation_letter_change(self)

        if waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV) and is_fax_status_dirty:
            self.handle_fax_status_update()

    def handle_fax_status_update(self):
        from firefly.modules.referral.utils.steerage_utils import apply_action_to_steerage_case

        elation_referral_order = ElationReferralOrder.objects.filter(elation_referral_letter=self).first()
        if (
            self.fax_status == FaxStatus.SUCCESS
            and elation_referral_order
            and elation_referral_order.referral
            and elation_referral_order.referral.is_active
        ):
            steerage: Optional[Steerage] = Steerage.objects.filter(referral=elation_referral_order.referral).first()
            assert steerage is not None
            case_to_update = None
            for cr in steerage.case_relations.iterator():
                case = cr.case
                if case.category.unique_key == REFERRAL_REQUEST:
                    case_to_update = case
                    break
            if (
                steerage.is_locked is True
                and steerage.scheduling_date
                and case_to_update
                and (
                    not steerage.referral
                    or not steerage.referral.insurance_authorization_required
                    or (
                        steerage.referral.insurance_authorization_required is True
                        and steerage.referral.referral_insurance_authorization.authorization_number
                    )
                )
            ):
                # Move to member scheduled only in case when either referral is not present
                # or insurance auth is marked as False or insurance details are added
                apply_action_to_steerage_case(case_to_update, ReferralRequestCaseStatuses.MEMBER_SCHEDULED)


class ReferralRecipient(BaseModelV3):
    """
    Model containing Referral recipient related fields.
    """

    first_name = models.TextField(null=True, blank=True)

    middle_name = models.TextField(null=True, blank=True)

    last_name = models.TextField(null=True, blank=True)

    npi = models.TextField(null=True, blank=True)

    org_name = models.TextField(null=True, blank=True)

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    fax_status = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=FaxStatus.choices,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    delivery_method = models.CharField(  # noqa: TID251
        max_length=50,
        null=True,
        blank=True,
        choices=ReferralLetterDeliveryMethod.choices,
    )

    direct_message_to = models.TextField(null=True, blank=True)

    email_to = models.TextField(null=True, blank=True)

    delivery_date = models.DateTimeField(null=True, blank=True)

    referral = models.ForeignKey(
        Referral,
        related_name="referral_recipient",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    @property
    def referred_to(self) -> str:
        """
        An inferred field to get the details of the org/clinician to whom the referral
        has been referred_to

        return: string containing org name if present else clinician name
        """
        referred_to_details = ""

        full_name = "%s %s" % (self.first_name, self.last_name)
        has_full_name = full_name and full_name != " "

        if self.org_name:
            referred_to_details += "ORG: "
            if has_full_name:
                referred_to_details += "CLINICAL: "
            referred_to_details += self.org_name
        elif has_full_name:
            referred_to_details += "CLINICAL: "
            referred_to_details += full_name

        return referred_to_details

    @property
    def referral_sent_yn(self) -> str:
        """
        An inferred field to signify whether the referral has been sent or not

        return: `Yes` or `No`
        """
        referred_to = self.referred_to

        signed_date = None
        if self.referral is not None:
            signed_date = self.referral.sign_date

        if (
            referred_to
            and signed_date
            and (
                self.delivery_method
                not in [
                    ReferralLetterDeliveryMethod.DIRECT,
                    ReferralLetterDeliveryMethod.PASSPORT,
                    ReferralLetterDeliveryMethod.FAX,
                ]
                or self.fax_status == FaxStatus.SUCCESS
            )
            and (not self.delivery_date or ((self.delivery_date - timezone.now()).total_seconds() < 0))
        ):
            return ReferralSent.Yes.value
        else:
            return ReferralSent.No.value


class ElationReferralOrder(BaseModelV3):
    """
    Model to store elation referral order.
    """

    elation_id = models.BigIntegerField(unique=True)

    referral = models.ForeignKey(
        Referral,
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="elation_referral_orders",
    )

    # id to map ElationReferralOrder to ElationReferralLetter
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    elation_referral_letter = models.ForeignKey(ElationReferralLetter, blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251

    authorization_for = models.TextField(null=True, blank=True)

    auth_number = models.TextField(null=True, blank=True)

    consultant_name = models.TextField(null=True, blank=True)

    short_consultant_name = models.TextField(null=True, blank=True)

    specialty_name = models.TextField(null=True, blank=True)

    specialty_abbreviation = models.TextField(null=True, blank=True)

    date_for_reEval = models.DateTimeField(null=True, blank=True)

    resolution = models.TextField(null=True, blank=True)

    authorization_for_short = models.TextField(null=True, blank=True)

    practice = models.ForeignKey(
        Practice,
        related_name="elation_referral_orders",
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    patient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="elation_referral_orders",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    icd10_codes = models.JSONField(default=list, null=True, blank=True)

    def save(self, *args, **kwargs):
        """
        Create/Update a referral whenever a ElationReferralOrder is saved.
        """

        created = self.pk is None

        super(ElationReferralOrder, self).save(*args, **kwargs)

        logger.info("Saving elation referral order for elation id %s", self.elation_id)
        if self.patient:
            from firefly.modules.referral.tasks import save_referral

            logger.info(
                "Publishing event to convert referral for elation id %s",
                self.elation_id,
            )
            # Add delay to allow referral letter to arrive as well
            if (
                self.elation_referral_letter is not None
                and self.elation_referral_letter.body
                and len(self.elation_referral_letter.body) > 0
            ):
                save_referral.send_with_options(args=(self.pk, created), delay=0)
        else:
            logger.info(
                "Skip publishing event to convert referral for elation id %s",
                self.elation_id,
            )


class ReferralInsuranceAuthorizationStatus(models.TextChoices):
    ACCEPTED = "Accepted", "Accepted"
    DENIED = "Denied", "Denied"
    PENDING_OON = "Pending OON Approval", "Pending OON Approval"
    PENDING_CLINICAL_INFO = "Pending Clinical Info", "Pending Clinical Info"
    PENDING_APPROVAL = "Pending Approval", "Pending Approval"
    DATE_CHANGE = "Provider/Date Change", "Provider/Date Change"


class ReferralInsuranceAuthorization(BaseProviderMixin, DirtyFieldsMixin):
    referral = models.OneToOneField(
        Referral,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="referral_insurance_authorization",
    )

    authorization_number = models.TextField(null=True, blank=True)

    use_referral_provider = models.BooleanField(
        blank=True,
        null=True,
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status = models.CharField(  # noqa: TID251
        max_length=100,
        null=True,
        blank=True,
        choices=ReferralInsuranceAuthorizationStatus.choices,
    )

    def save(self, *args, **kwargs):
        from firefly.modules.referral.utils.steerage_utils import (
            update_referral_request_case_status_for_insurance_auth,
        )

        if self.is_dirty(check_relationship=True):
            dirty_fields = self.get_dirty_fields(check_relationship=True)
            # Check if auth number is added and a scheduling date exsists, if so update the Referral Request
            # case status
            if "authorization_number" in dirty_fields.keys():
                steerage = self.referral.steerages.first()
                update_referral_request_case_status_for_insurance_auth(steerage)

        super(ReferralInsuranceAuthorization, self).save(*args, **kwargs)


# This model allows for storing structured icd code data
# associated with insurance of a referral. Note that
# these represent the icd codes required for insurance
# approval which might not be the same as the ones
# added by us on the referral itself.
class ReferralInsuranceAuthICDCode(CodeMixin):
    referral_insurance_auth = models.ForeignKey(
        ReferralInsuranceAuthorization,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="referral_insurance_auth_icd_codes",
    )

    referral = models.ForeignKey(
        Referral,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="referral_insurance_auth_icd_codes",
    )

    # This is a temporary field till we link directly into the ICD Code object
    description = models.TextField(null=True, blank=True)

    class Meta(CodeMixin.Meta):
        constraints = [
            models.UniqueConstraint(
                fields=["code", "referral"],
                condition=Q(deleted=None),
                name="authcode_referral_unique",
            )
        ]


# Currently the insurance approval worklist stores unstructured data
# for icd codes. This model mimics the current behavior and allows for
# free text addition of icd code data. In a future PR - we will
# release functionality with the ability to add structured data to this
# field.
class ReferralInsuranceAuthUnstructuredICDCode(BaseModelV3):
    referral = models.OneToOneField(
        Referral,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="referral_insurance_auth_unstructured_icd_code",
    )

    # A free text field for icd code data
    icd_code_data = models.TextField(null=True, blank=True)


# This stores the document tags recieved in the Elation Referral Letter webhook
class ElationReferralLetterDocumentTag(BaseModelV3):
    elation_id = models.BigIntegerField(unique=False, null=True, blank=True)
    elation_referral_letter = models.ForeignKey(
        ElationReferralLetter,
        blank=False,
        null=False,
        related_name="elation_referral_document_tag",
        on_delete=models.CASCADE,
    )
    # Actual Tags will come in the value field from elation
    value = models.TextField(null=True, blank=True)
    # Desciption of the tag
    description = models.TextField(null=True, blank=True)

    # Metadata
    code = models.TextField(null=True, blank=True)
    code_type = models.TextField(null=True, blank=True)


# TIN information for each Steerage Provider
class SteerageProviderIdentifiers(BaseModelV3):
    steerage_provider = models.ForeignKey(
        SteerageProvider,
        related_name="steerage_provider_identifiers",
        on_delete=models.CASCADE,
    )
    tax_identifier = models.ForeignKey(
        TaxIdentifier,
        related_name="steerage_provider_identifiers",
        on_delete=models.CASCADE,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["steerage_provider", "tax_identifier"],
                condition=Q(deleted=None),
                name="steerage_provider_identifiers_uniq",
            )
        ]
