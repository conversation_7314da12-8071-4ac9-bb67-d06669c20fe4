import logging
from typing import TypedDict

from django.conf import settings

from firefly.core.services.braze.templates import BrazeCampaign
from firefly.core.user.models.models import Person
from firefly.modules.referral.models import (
    Steerage,
    SteerageProviderStatuses,
    SteerageProviderWaiverLevel,
)

logger = logging.getLogger(__name__)

"""
This module contains the logic for two separate Braze campaigns:

1. SteerageSelfServiceCampaign - for notifying about a Self Service Steerage that is ready to choose a provider.
2. SteerageMultipleProvidersCampaign - for notifying about a Steerage that has multiple providers to choose from.

The two campaigns currently share content, but may diverge in the future. If that happens, please separate
the content into two different classes across two different files.
"""


class ChooseProviderContext(TypedDict):
    preferred_name: str
    specialty: str
    steerage_id: str
    has_waiver: bool
    has_referral: bool


class SteerageMultipleProviderBaseCampaign(BrazeCampaign[ChooseProviderContext]):
    """
    Base class for notifying about a steerage that is ready to choose a provider.
    Child classes must define the campaign_id.
    """

    # Static content
    email_preheader = "Next up: Pick a provider"

    def create_context(self, **kwargs):
        # Extract required parameters
        steerage: Steerage = kwargs.get("steerage")
        person: Person = kwargs.get("person")
        if not steerage:
            raise ValueError("Steerage is required")
        if not person:
            raise ValueError("Person is required")

        specialty = steerage.specialty_group or steerage.location_type
        assert specialty is not None
        specialty_label = specialty.label.lower()

        # In this context, the waiver is available if this is a self service Steerage
        # OR if at least one accepted provider is present with an applicable waiver_level
        has_waiver: bool = steerage.steerage_providers.filter(
            status=SteerageProviderStatuses.ACCEPTED,
            waiver_level__in=[
                SteerageProviderWaiverLevel.PROVIDER,
                SteerageProviderWaiverLevel.FACILITY,
            ],
        ).exists() or (steerage.is_self_service_enabled and steerage.waiver and steerage.waiver.is_active)

        return self._set_context(
            {
                "preferred_name": person.preferred_name or person.first_name,
                "specialty": specialty_label,
                "steerage_id": str(steerage.id),
                "has_referral": steerage.referral is not None,
                "has_waiver": has_waiver,
            }
        )

    def get_push_title(self):
        if self.context["has_referral"]:
            return "Your referral is ready"
        return "Your provider request is ready"

    def get_push_message(self):
        if self.context["has_waiver"]:
            return (
                "Care will be free or lower cost when you pick a Care Key eligible provider. "
                + "See options in the app, then book a visit."
            )
        return "See options in the app, then book a visit."

    def get_push_deeplink(self):
        return f"fireflyhealth:v1/referral-search?id={self.context['steerage_id']}"

    def get_email_subject(self):
        if self.context["has_referral"]:
            return "Your referral is ready"
        return "Your provider request is ready"

    def get_email_content(self) -> str:
        line_one = f"You're all set to schedule your {self.context['specialty']} visit."
        if self.context["has_waiver"]:
            line_one += " This service qualifies for a Care Key."

        choose_provider = "Check out the options in the app."
        if self.context["has_waiver"]:
            choose_provider += " Care will be free or lower cost when you pick a provider who's Care Key eligible."

        add_visit_date = "This helps us avoid billing issues."
        if self.context["has_referral"] and self.context["has_waiver"]:
            add_visit_date = "This helps us follow up on care and avoid billing issues."
        if self.context["has_referral"] and not self.context["has_waiver"]:
            add_visit_date = "This helps us follow up on care."

        return self.markdown.add_default(
            f"""
Hi {self.context["preferred_name"]},

{line_one}

Here's what to do next.

1. **Choose a provider.**
{choose_provider}

2. **Book your visit with them.**
Get the provider's info from the app.

3. **Add the visit date in the Firefly app.**
{add_visit_date}

4. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""
        ).render()


class SteerageSelfServiceCampaign(SteerageMultipleProviderBaseCampaign):
    """Campaign for notifying about a Self Service Steerage that is ready to choose a provider."""

    campaign_id = settings.BRAZE["REFERRAL_READY_SELF_SERVICE"]


class SteerageChooseProviderCampaign(SteerageMultipleProviderBaseCampaign):
    """Campaign for notifying about a Steerage that has multiple providers to choose from."""

    campaign_id = settings.BRAZE["REFERRAL_READY_CHOOSE_PROVIDER"]
