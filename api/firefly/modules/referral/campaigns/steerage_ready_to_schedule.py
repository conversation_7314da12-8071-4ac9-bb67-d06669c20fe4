import logging
from typing import TypedDict

from django.conf import settings

from firefly.core.services.braze.templates import BrazeCampaign
from firefly.core.user.models.models import Person
from firefly.modules.referral.models import (
    Steerage,
    SteerageProvider,
    SteerageProviderStatuses,
    SteerageProviderWaiverLevel,
)

logger = logging.getLogger(__name__)


class ReadyToScheduleContext(TypedDict):
    care_organization_name: str
    has_referral: bool
    preferred_name: str
    specialty: str
    steerage_id: str


class SteerageReadyToScheduleCampaign(BrazeCampaign[ReadyToScheduleContext]):
    """Campaign for notifying about a Steerage that is ready to schedule."""

    campaign_id = settings.BRAZE["REFERRAL_READY_SCHEDULING"]

    # Static content
    email_preheader = "Next up: Book the visit"
    push_message = "Book a visit with the provider today. See scheduling instructions in the app."

    def create_context(self, **kwargs):
        # Extract required parameters
        steerage: Steerage = kwargs.get("steerage")
        if not steerage:
            raise ValueError("Steerage is required")
        person: Person = kwargs.get("person")
        if not person:
            raise ValueError("Person is required")

        # Get the specialty label
        specialty = steerage.specialty_group or steerage.location_type
        assert specialty is not None
        specialty_label = specialty.label.lower()

        # Get the provider / facility name
        chosen_steerage_provider: SteerageProvider | None = None
        chosen_steerage_provider = SteerageProvider.objects.filter(
            steerage_id=steerage.id, member_selected_at__isnull=False
        ).first()
        # If there is not one selected, assume there's only one provider and use them
        if chosen_steerage_provider is None:
            chosen_steerage_provider = SteerageProvider.objects.filter(
                steerage_id=steerage.id, status=SteerageProviderStatuses.ACCEPTED
            ).first()

        # Default to filler text in case of no provider or None types in provider info
        care_organization_name = "the practice"

        if chosen_steerage_provider is not None:
            # If it's a facility type provider, use the facility name
            if (
                chosen_steerage_provider.waiver_level == SteerageProviderWaiverLevel.FACILITY
                and chosen_steerage_provider.care_organization_name is not None
            ):
                care_organization_name = chosen_steerage_provider.care_organization_name
            # Otherwise, use the provider's name
            else:
                last_name = chosen_steerage_provider.last_name or ""
                if chosen_steerage_provider.first_name is not None:
                    care_organization_name = (chosen_steerage_provider.first_name + " " + last_name).strip()

        return self._set_context(
            {
                "care_organization_name": care_organization_name,
                "has_referral": steerage.referral is not None,
                "preferred_name": person.preferred_name or person.first_name,
                "specialty": specialty_label,
                "steerage_id": str(steerage.id),
            }
        )

    def get_push_title(self):
        if self.context["has_referral"]:
            return "Your referral is ready"
        return "Your provider request is ready"

    def get_push_deeplink(self):
        return f"fireflyhealth:v1/referral-detail?id={self.context['steerage_id']}"

    def get_email_subject(self):
        if self.context["has_referral"]:
            return "Your referral is ready"
        return "Your provider request is ready"

    def get_email_content(self) -> str:
        this_helps_us = "This helps us "
        if self.context["has_referral"]:
            this_helps_us += "follow up on care."
        else:
            this_helps_us += "avoid billing issues."

        return self.markdown.add_default(
            f"""
Hi {self.context["preferred_name"]},

You're all set to schedule your {self.context["specialty"]} visit.

Here's what to do next.

1. **Book your visit with {self.context["care_organization_name"]}.**
Get their info in the app.

2. **Add the visit date in the Firefly app.**
{this_helps_us}

3. **Let us know about any appointment changes.**
Like a new visit date, provider, or location.


Questions? Send us a chat in the app. We're happy to help.

Yours in health,
The Firefly Team
"""
        ).render()
