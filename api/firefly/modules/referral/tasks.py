import logging
from datetime import date, datetime, timedelta
from typing import List, Optional, cast

import dramatiq
import waffle
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from typing_extensions import TypedDict

from firefly.core.alias.models import <PERSON>as<PERSON>apping, AliasName, get_content_type
from firefly.core.services.braze.tasks import reminders
from firefly.core.services.dramatiq.constants import (
    RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS,
    RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
from firefly.core.services.dramatiq.utils import get_rate_limiter, get_window_rate_limiter
from firefly.core.services.flume.client import FlumeAPIClient
from firefly.core.user.models.models import Person
from firefly.modules.cases.constants import REFERRAL_REQUEST, ReferralRequestCaseStatuses
from firefly.modules.cases.models import Case
from firefly.modules.code_systems.models import CPTCode
from firefly.modules.eligibility.utils import get_primary_subscriber
from firefly.modules.events.models import EventLog
from firefly.modules.facts.models import ServiceCategory
from firefly.modules.programs.utils import get_person_program_enrollments
from firefly.modules.referral.campaigns.steerage_choose_provider import (
    SteerageChooseProviderCampaign,
    SteerageSelfServiceCampaign,
)
from firefly.modules.referral.campaigns.steerage_ready_to_schedule import (
    SteerageReadyToScheduleCampaign,
)
from firefly.modules.referral.campaigns.steerage_scheduled import SteerageScheduledCampaign
from firefly.modules.referral.constants import (
    WAFFLE_SWITCH_SELF_SERVICE_NAV,
    WAFFLE_SWITCH_STORE_TINS,
)
from firefly.modules.referral.models import (
    ElationReferralLetter,
    ElationReferralOrder,
    PriorAuthDiagnosisCode,
    PriorAuthorization,
    PriorAuthorizationProvider,
    PriorAuthorizationServiceQuantity,
    PriorAuthorizationWebhook,
    PriorAuthProcedureCode,
    RecommendationStatusChoices,
    Referral,
    ReferralInsuranceAuthICDCode,
    ReferralInsuranceAuthorization,
    ReferralRecipient,
    SearchRequest,
    ServiceQuantityUnitConfig,
    Steerage,
    SteerageProvider,
    SteerageProviderDataSourceConfig,
    SteerageProviderStatuses,
    Waiver,
)
from firefly.modules.referral.serializers import (
    ElationReferralRecipientSerializer,
    ElationReferralSerializer,
)
from firefly.modules.referral.types import (
    FlumeCode,
    FlumePriorAuthorization,
    FlumeServiceQuantity,
    ReferralIcd10Codes,
)
from firefly.modules.referral.utils.prior_auth_utils import (
    handle_missing_prior_auth_details,
    message_waiver_alerts_channel,
    update_prior_auth_details_on_steerage,
)
from firefly.modules.referral.utils.provider_search_utils import (
    AddressDetail,
    ProviderDetail,
    create_tax_identifiers_for_steerage_provider,
    get_primary_fax_from_ribbon_data,
    get_primary_phone_number_from_ribbon_data,
)
from firefly.modules.referral.utils.referral_tags_utils import (
    link_tags_to_all_referral_cases,
    link_tags_to_referral_case,
)
from firefly.modules.referral.utils.steerage_utils import (
    apply_action_to_steerage_case,
    create_appropriate_referral_case,
    create_referral_for_steerage,
    is_steerage_self_serviceable,
)
from firefly.modules.referral.utils.suggested_provider_utils import (
    get_suggested_facilities_from_talon,
    get_suggested_partners,
    get_suggested_provider_from_ribbon,
)
from firefly.modules.referral.utils.waiver_utils import post_waiver
from firefly.modules.states.models import State

logger = logging.getLogger(__name__)


def _get_cpt_code_version(version: Optional[str]) -> Optional[str]:
    """If at least one record includes the version, return that version
    otherwise return the last-loaded version year as a string"""
    if CPTCode.objects.filter(version=version).exists():
        return version
    else:
        # We have currently loaded CPT codes from 2021 and 2022 in our system.
        # There are no plans to update this list yearly at this point in time.
        # For now, hardcode looking at the most recent version (2022), until such
        # point in time as someone decides to update the data.
        return "2022"


@dramatiq.actor(queue_name="prior-authorization", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def consume_prior_authorization(webhook_instance_id: int) -> None:
    webhook_instance = PriorAuthorizationWebhook.objects.get(pk=webhook_instance_id)
    data: FlumePriorAuthorization = webhook_instance.data

    try:
        # DO NOT COPY-PASTE - transaction.atomic considered harmful
        with transaction.atomic():  # noqa: TID251
            alias_mapping: Optional[AliasMapping] = None
            flume_id: Optional[str] = data["id"]
            if flume_id is None:
                raise Exception(
                    "No flume id found for prior auth webhook data %s",
                    PriorAuthorizationWebhook,
                )
            try:
                alias_mapping = AliasMapping.get_alias_mapping_for_alias(
                    alias_name=AliasName.FLUME,
                    alias_id=flume_id,
                    content_type=get_content_type(PriorAuthorization),
                )

                prior_authorization = alias_mapping.content_object or PriorAuthorization()

            except AliasMapping.DoesNotExist:
                prior_authorization = PriorAuthorization()

            prior_authorization = create_prior_authorization(prior_authorization=prior_authorization, data=data)
            prior_authorization.save()

            was_prior_auth_created: bool = False
            if alias_mapping is None:
                alias_mapping = AliasMapping.set_mapping_by_object(
                    obj=prior_authorization,
                    alias_name=AliasName.FLUME,
                    alias_id=flume_id,
                )
                was_prior_auth_created = True

            if alias_mapping is not None and alias_mapping.object_id != prior_authorization.pk:
                alias_mapping.object_id = prior_authorization.pk
                alias_mapping.save()

            webhook_instance.prior_authorization = prior_authorization
            webhook_instance.save()

            # If the prior auth update is received, then need to check if the provider or service categories are changed
            # If changed, then unlock the steerage and move the status to "Prior Auth Received" else do nothing
            update_required = False

            # Format the datetime field
            formatted_updated_date = (
                prior_authorization.updated_at.strftime("%d/%m/%Y") if prior_authorization.updated_at else ""
            )
            # This Note will be added in the case notes section. So that navigators know what changes were received in
            # the prior auth update.
            updated_note: str = " Prior Auth updated " + formatted_updated_date

            # store the diagnose code
            store_diagnosis_codes(prior_authorization=prior_authorization, data=data)

            # Store procedure code data
            store_procedure_codes(prior_authorization=prior_authorization, data=data)

            # Store service categories
            prior_authorization, update_required, updated_note = store_service_categories(
                prior_authorization=prior_authorization,
                data=data,
                update_required=update_required,
                updated_note=updated_note,
            )
            # Store provider data
            update_required, updated_note = store_providers(
                prior_authorization=prior_authorization,
                data=data,
                update_required=update_required,
                updated_note=updated_note,
            )

            # Store service quantity data
            store_service_quantity(prior_authorization=prior_authorization, data=data)

            # Trigger an additional save for service categories to flow over to dependent objects
            # Determination should be the last attribute of a prior auth to be added in
            # since down stream objects get created based on the attribute
            prior_authorization.determination = data.get("determination")
            prior_authorization.save()

            # Create missing prior auth details case if any of the required fields are missing
            handle_missing_prior_auth_details(prior_authorization, data)

            webhook_instance.conversion_status = PriorAuthorizationWebhook.ConversionStatus.SUCCEEDED
            webhook_instance.save()
            logger.info("Ingested prior auth webhook %s", webhook_instance_id)

            # Update steerage details
            update_prior_auth_details_on_steerage(
                prior_authorization, was_prior_auth_created, update_required, updated_note
            )

            # Alert slack channel about the new webhook processed if the determination is set
            if prior_authorization.determination == "Approved" or prior_authorization.determination == "Denied":
                message = ""
                if was_prior_auth_created:
                    message = "A new "
                else:
                    message = "An update for "

                    if prior_authorization.determination == "Approved":
                        message = message + "an "
                    else:
                        message = message + "a "

                message = (
                    message + prior_authorization.determination + " Prior Authorization has been processed "
                    f"(OriginatorSystemID {prior_authorization.originator_system_id})"
                )

                message_waiver_alerts_channel(
                    message,
                    prior_authorization.person,
                )

    except Exception:
        # Make a fresh copy of the webhook instance, wipe out any manipulation from Python code
        webhook_instance = PriorAuthorizationWebhook.objects.get(pk=webhook_instance_id)
        webhook_instance.conversion_status = PriorAuthorizationWebhook.ConversionStatus.FAILED
        webhook_instance.save()
        logger.exception("Failed to convert prior auth webhook data %s", webhook_instance_id)
        raise
    return None


@dramatiq.actor(queue_name="waiver-transmission-queue", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def post_waiver_async(waiver_id: int):
    with get_window_rate_limiter(
        key="waiver_transmission_mutex",
        limit_per_window=settings.WAIVER_TRANSMISSION_RATE_LIMIT_PER_WINDOW,
    ).acquire():
        post_waiver(
            waiver=Waiver.objects.get(id=waiver_id),
            client=FlumeAPIClient(),
        )


def create_prior_authorization(
    prior_authorization: PriorAuthorization, data: FlumePriorAuthorization
) -> PriorAuthorization:
    # Setting determination triggers creation of downstream objects
    # The determination attribute should be set only after all the
    # attributes of a prior auth and its relations have already been
    # created
    effective_through_date = data.get("effectiveThrough") or None
    determination = data.get("determination")
    if not data.get("createDate"):
        logger.info("No create date on Prior Auth ID %s when consuming webhook", prior_authorization.id)
    if not data.get("effectiveFrom"):
        logger.info("No effective from date on Prior Auth ID %s when consuming webhook", prior_authorization.id)
    if not effective_through_date and determination and determination != "Denied":
        logger.info("No effective through date on Prior Auth ID %s when consuming webhook", prior_authorization.id)

    prior_authorization.comment = data.get("comment")
    prior_authorization.vendor_create_date = data.get("createDate")
    prior_authorization.effective_from = data.get("effectiveFrom")
    prior_authorization.effective_through = effective_through_date
    prior_authorization.originator_system_id = data.get("originatorSystemID")
    try:
        person = AliasMapping.get_alias_mapping_for_alias(
            alias_id=str(data.get("memberKey")),
            alias_name=AliasName.FLUME,
            content_type=ContentType.objects.get_for_model(Person),
        )

    except Exception as e:
        logger.exception("Failed to get person from member key")
        raise e
    assert person.content_object is not None
    prior_authorization.person = cast(Person, person.content_object)

    prior_authorization.status = data.get("status")
    prior_authorization.type = data.get("type")
    prior_authorization.vendor_update_date = data.get("updateDate")
    return prior_authorization


def store_diagnosis_codes(prior_authorization: PriorAuthorization, data: FlumePriorAuthorization):
    # Store diagnosis code data
    diagnosis_codes: Optional[List[FlumeCode]] = data.get("diagnosisCodes")
    # Store a list of all diagnosis codes that are already associated
    # with the prior auth so that we can remove any entries that were
    # deleted
    pre_existing_diagnosis_codes = list(
        PriorAuthDiagnosisCode.objects.filter(
            prior_authorization=prior_authorization,
        ).values_list("id", flat=True)
    )
    if diagnosis_codes is not None:
        for diagnosis_code in diagnosis_codes:
            prior_auth_diagnosis_code, _ = PriorAuthDiagnosisCode.objects.get_or_create(
                prior_authorization=prior_authorization,
                code=diagnosis_code["code"],
                system=diagnosis_code["system"],
                version=diagnosis_code["version"],
            )
            if prior_auth_diagnosis_code.id in pre_existing_diagnosis_codes:
                pre_existing_diagnosis_codes.remove(prior_auth_diagnosis_code.id)
    if len(pre_existing_diagnosis_codes) > 0:
        # Delete any diagnosis codes that are no longer relevant
        PriorAuthDiagnosisCode.objects.filter(id__in=pre_existing_diagnosis_codes).delete()


def store_procedure_codes(prior_authorization: PriorAuthorization, data: FlumePriorAuthorization):
    procedure_codes: Optional[List[FlumeCode]] = data.get("procedureCodes")
    # Store a list of all procedure codes that are already associated
    # with the prior auth so that we can remove any entries that were
    # deleted
    pre_existing_procedure_codes = list(
        PriorAuthProcedureCode.objects.filter(
            prior_authorization=prior_authorization,
        ).values_list("id", flat=True)
    )
    if procedure_codes is not None:
        cpt_content_type = ContentType.objects.get_for_model(CPTCode)
        for procedure_code in procedure_codes:
            cpt_code: Optional[CPTCode] = None
            version = _get_cpt_code_version(procedure_code["version"])
            try:
                cpt_code = CPTCode.objects.get(code=procedure_code["code"], version=version)
            except CPTCode.DoesNotExist:
                logger.error(
                    "unable to find CPT code for %s",
                    {"code": procedure_code["code"], "version": version},
                )
                cpt_code = None

            prior_auth_procedure_code, _ = PriorAuthProcedureCode.objects.get_or_create(
                prior_authorization=prior_authorization,
                code=procedure_code["code"],
                system=procedure_code["system"],
                version=version,
            )

            if cpt_code:
                prior_auth_procedure_code.object_id = cpt_code.id
                prior_auth_procedure_code.content_type = cpt_content_type
                prior_auth_procedure_code.save()

            if prior_auth_procedure_code.id in pre_existing_procedure_codes:
                pre_existing_procedure_codes.remove(prior_auth_procedure_code.id)
    if len(pre_existing_procedure_codes) > 0:
        # Delete any procedure codes that are no longer relevant
        PriorAuthProcedureCode.objects.filter(id__in=pre_existing_procedure_codes).delete()


def store_service_categories(
    prior_authorization: PriorAuthorization, data: FlumePriorAuthorization, update_required: bool, updated_note: str
) -> tuple[PriorAuthorization, bool, str]:
    service_category_ids = data.get("serviceCategoryIDs", [])
    determination = data.get("determination", None)

    # Collect existing service category IDs
    existing_service_category_ids = set(prior_authorization.service_categories.values_list("id", flat=True))

    # Also collect labels for existing and new service category labels to show in the notes
    existing_service_category_label_list = prior_authorization.service_categories.values_list("label", flat=True)
    existing_service_category_label = ", ".join(existing_service_category_label_list) or "None"
    new_service_category_label_list = []
    new_service_category_label: str = ""

    # Clear existing service categories
    prior_authorization.service_categories.clear()

    # Initialize a set for new service category IDs
    new_service_category_ids = set()

    # Set update_required to True if there were existing categories and new webhook came without services
    if not service_category_ids and (determination and determination != "Denied"):
        logger.info(
            "[PriorAuthorization] Empty array of service categories received from Prior Auth for id %s",
            prior_authorization.pk,
        )
        update_required = bool(existing_service_category_ids)
    elif service_category_ids:
        for service_category_id in service_category_ids:
            if not service_category_id:
                continue
            service_category = None
            if service_category_id.isnumeric():
                try:
                    alias_mapping = AliasMapping.get_alias_mapping_for_alias(
                        alias_id=service_category_id,
                        alias_name=AliasName.FLUME,
                        content_type=get_content_type(ServiceCategory),
                    )
                    service_category = alias_mapping.content_object
                except AliasMapping.DoesNotExist:
                    pass
            else:
                try:
                    service_category = ServiceCategory.objects.get(label=service_category_id)
                except ServiceCategory.DoesNotExist:
                    pass

            if service_category:
                prior_authorization.service_categories.add(service_category)
                new_service_category_ids.add(service_category.id)
                new_service_category_label_list.append(service_category.label)
            else:
                logger.info(
                    "[PriorAuthorization] Service category could not be mapped for prior auth id %s",
                    prior_authorization.pk,
                )
        new_service_category_label = ", ".join(new_service_category_label_list) or "None"
        # Determine if the sets of existing and new service categories are different
        if new_service_category_ids != existing_service_category_ids:
            update_required = True

            # Update the note with service category details
            updated_note = (
                updated_note
                + ", service categories moving from '"
                + existing_service_category_label
                + "' to '"
                + new_service_category_label
                + "'"
            )

    return prior_authorization, update_required, updated_note


def store_providers(
    prior_authorization: PriorAuthorization, data: FlumePriorAuthorization, update_required: bool, updated_note: str
):
    providers = data.get("providers", [])

    # Collect existing provider IDs
    existing_provider_ids = set(
        PriorAuthorizationProvider.objects.filter(prior_authorization=prior_authorization).values_list("id", flat=True)
    )

    new_provider_ids = set()

    # Process the provided list of providers
    if providers:
        for provider in providers:
            facility = provider.get("facility", {})
            practitioner = provider.get("practitioner", {})

            state_abbr = facility.get("state")
            state = None
            if state_abbr:
                try:
                    state = State.objects.get(abbreviation=state_abbr)
                except State.DoesNotExist:
                    logger.exception("No state found for %s", state_abbr)

            # Create or update the provider entry
            prior_auth_provider, _ = PriorAuthorizationProvider.objects.get_or_create(
                # TODO: Store NPI2, TIN, nameSuffix
                prior_authorization=prior_authorization,
                street_address=facility.get("address"),
                city=facility.get("city"),
                care_organization_name=facility.get("facilityName"),
                first_name=practitioner.get("firstName"),
                last_name=practitioner.get("lastName"),
                middle_name=practitioner.get("middleName"),
                npi=practitioner.get("npi1"),
                state=state,
                zip_code=facility.get("zip"),
            )
            new_provider_ids.add(prior_auth_provider.id)

    # Determine providers to delete
    providers_to_delete = existing_provider_ids - new_provider_ids

    if providers_to_delete:
        PriorAuthorizationProvider.objects.filter(id__in=providers_to_delete).delete()

    # Update `update_required` if the sets of provider IDs do not match
    if new_provider_ids != existing_provider_ids:
        update_required = True
        updated_note = updated_note + ", and updated provider details."

    return update_required, updated_note


def store_service_quantity(prior_authorization: PriorAuthorization, data: FlumePriorAuthorization):
    service_quantity_from_flume: Optional[FlumeServiceQuantity] = data.get("serviceQuantity")
    # Fetch a service quantity if it's already associated with the prior auth object
    service_quantity: Optional[PriorAuthorizationServiceQuantity] = None
    if hasattr(prior_authorization, "service_quantity"):
        service_quantity = prior_authorization.service_quantity
    if service_quantity_from_flume is not None:
        if service_quantity is None:
            service_quantity = PriorAuthorizationServiceQuantity(
                prior_authorization=prior_authorization,
            )
        if service_quantity_from_flume.get("units") is not None:
            service_quantity.unit = ServiceQuantityUnitConfig.UNITS
            service_quantity.value = service_quantity_from_flume.get("units")
        elif service_quantity_from_flume.get("days") is not None:
            service_quantity.unit = ServiceQuantityUnitConfig.DAYS
            service_quantity.value = service_quantity_from_flume.get("days")
        elif service_quantity_from_flume.get("visits") is not None:
            service_quantity.unit = ServiceQuantityUnitConfig.VISITS
            service_quantity.value = service_quantity_from_flume.get("visits")
        service_quantity.save()
    else:
        # If the webhook data does not have a quantity
        # but we have already stored one: delete it
        if service_quantity is not None:
            service_quantity.delete()


@dramatiq.actor(queue_name="referral_order_conversion", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def save_referral(elation_referral_order_id: int, elation_order_created: bool):
    """
    task to create/update referrals.
    """

    # A rate limiter would make sure we update the models one task at a time
    # Without a rate limiter multiple updates can trigger race condition for model updates

    rate_limiter_key = "save_referral_mutex_{}".format(elation_referral_order_id)
    with get_rate_limiter(
        key=rate_limiter_key,
        limit=settings.SAVE_REFERRAL_CONCURRENT_RATE_LIMIT,
    ).acquire():
        try:
            elation_referral_order_instance: Optional[ElationReferralOrder] = None
            try:
                elation_referral_order_instance = ElationReferralOrder.objects.get(id=elation_referral_order_id)
            except ElationReferralOrder.DoesNotExist:
                logger.error(
                    "save_referral: Elation referral order not found for id: :%s",
                    elation_referral_order_id,
                )
                return

            # Get the steerage object with alias mapping to this elation referral order
            # and pass it to below create_referral_object function
            steerage = None
            try:
                assert elation_referral_order_instance.elation_referral_letter is not None
                alias_mapping: AliasMapping = AliasMapping.get_alias_mapping_for_alias(
                    alias_id=str(elation_referral_order_instance.elation_referral_letter.elation_id),
                    alias_name=AliasName.ELATION_REFERRAL_LETTER,
                    content_type=ContentType.objects.get_for_model(Steerage),
                )
                steerage = Steerage.objects.get(id=alias_mapping.object_id)
                logger.info(
                    "save_referral: steerage id %s found for elation referral order id: %s, elation id: %s",
                    alias_mapping.object_id,
                    elation_referral_order_id,
                    elation_referral_order_instance.elation_id,
                )
            except Exception:
                logger.info(
                    "save_referral: steerage not found for elation referral order id: %s, elation id: %s",
                    elation_referral_order_id,
                    elation_referral_order_instance.elation_id,
                )

            # If elation_order_created is true, then we created a brand new object in the
            # system, and we want to create an appropriate Case for Operations to process it.
            if elation_referral_order_instance.referral is None:
                is_initiated_from_lucian = False
                if steerage and steerage.is_initiated_from_lucian is not None:
                    is_initiated_from_lucian = steerage.is_initiated_from_lucian

                referral = create_referral_object(
                    elation_referral_order_instance,
                    is_initiated_from_lucian,
                    steerage,
                )

                assert referral is not None

                elation_referral_order_instance.referral = referral
                elation_referral_order_instance.save(update_fields=["referral"])

            # We are not sure about the order in which elation sends the event of
            # creating referral letter and referral order. This check would enable
            # creation of ReferralRecipient when we have a referral and elation referral
            # letter attached to elation referral order
            if elation_referral_order_instance.elation_referral_letter and elation_referral_order_instance.referral:
                referral_recipient, _ = ReferralRecipient.objects.get_or_create(
                    referral=elation_referral_order_instance.referral
                )
                referral_recipient.referral = elation_referral_order_instance.referral
                referral_recipient.save(update_fields=["referral"])

                # If we have referral request type cases related to the steerage that was created from Lucian,
                # we should move them to done state or Location Search case
                if steerage:
                    assert steerage.person_id is not None
                    cases = Case.objects.filter(
                        person_id=steerage.person_id,
                        category__unique_key=REFERRAL_REQUEST,
                        relations__content_type=ContentType.objects.get_for_model(Steerage),
                        relations__object_id=steerage.id,
                    )
                    for case in cases:
                        if case and case.status == ReferralRequestCaseStatuses.COMPLETE_IN_ELATION:
                            apply_action_to_steerage_case(case, ReferralRequestCaseStatuses.LOCATION_SEARCH)

            update_referral(
                elation_referral_order=elation_referral_order_instance,
                elation_referral_letter=elation_referral_order_instance.elation_referral_letter,
            )

        except Exception:
            created_or_updated = "create" if elation_order_created else "update"
            logger.exception(
                "save_referral: Failed to %s referral for elation referral order %s ",
                created_or_updated,
                elation_referral_order_id,
            )
            raise


class ReferralRecipientData(TypedDict, total=False):
    direct_message_to: Optional[str]
    email_to: Optional[str]
    delivery_date: Optional[datetime]
    first_name: Optional[str]
    last_name: Optional[str]
    org_name: Optional[str]
    npi: Optional[str]
    specialty: List
    delivery_method: Optional[str]
    fax_status: Optional[str]


def update_referral_recipient(referral: Referral, elation_referral_letter: ElationReferralLetter):
    """
    Update a ReferralRecipient model from elation_referral_letter.
    """
    referral_recipient = None
    try:
        referral_recipient = ReferralRecipient.objects.get(referral=referral)
    except ReferralRecipient.DoesNotExist:
        pass

    data: ReferralRecipientData = {}

    if elation_referral_letter is not None and referral_recipient:
        data["direct_message_to"] = elation_referral_letter.direct_message_to
        data["email_to"] = elation_referral_letter.email_to
        data["delivery_date"] = elation_referral_letter.delivery_date
        data["delivery_method"] = elation_referral_letter.delivery_method
        data["fax_status"] = elation_referral_letter.fax_status

        if elation_referral_letter.send_to_contact:
            data["first_name"] = elation_referral_letter.send_to_contact.first_name
            data["last_name"] = elation_referral_letter.send_to_contact.last_name
            data["npi"] = elation_referral_letter.send_to_contact.npi
            data["org_name"] = elation_referral_letter.send_to_contact.org_name

        serializer = ElationReferralRecipientSerializer(instance=referral_recipient, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()


class ReferralData(TypedDict, total=False):
    description: Optional[str]
    referred_to: Optional[str]
    status: Optional[str]
    person: Optional[int]
    subject: Optional[str]
    body: Optional[str]
    sign_date: Optional[datetime]
    vendor_create_date: Optional[datetime]
    sign_off_user: Optional[int]


def update_referral(
    elation_referral_order: ElationReferralOrder,
    elation_referral_letter: Optional[ElationReferralLetter],
):
    """
    Update referral from elation_referral_order and elation_referral_letter
    """

    referral: Optional[Referral] = elation_referral_order.referral
    data: ReferralData = {}

    if referral is not None:
        data["description"] = elation_referral_order.authorization_for
        data["referred_to"] = elation_referral_order.consultant_name

        if elation_referral_order.patient:
            data["person"] = elation_referral_order.patient.person.id

        if elation_referral_letter is not None:
            data["subject"] = elation_referral_letter.subject
            data["body"] = elation_referral_letter.body
            data["sign_date"] = elation_referral_letter.sign_date
            data["vendor_create_date"] = elation_referral_letter.document_date

            if elation_referral_letter.signed_by:
                data["sign_off_user"] = elation_referral_letter.signed_by.id

            update_referral_recipient(referral=referral, elation_referral_letter=elation_referral_letter)

        serializer = ElationReferralSerializer(instance=referral, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        # if order has icd10_codes create/update the mapping of icd10_codes to this referral
        if elation_referral_order.icd10_codes and len(elation_referral_order.icd10_codes) > 0:
            update_referral_authorization_icd_codes(referral, elation_referral_order.icd10_codes)

        # If elation letter has tag's then link them with the referral cases
        steerage: Optional[Steerage] = Steerage.objects.filter(referral=referral).first()
        assert steerage is not None
        link_tags_to_all_referral_cases(steerage)


@dramatiq.actor(queue_name="referral_letter_conversion", **RETRY_DEFAULTS_FOR_NON_RETRIABLE_WORKFLOWS)
def update_referral_letter_dependent_models(elation_referral_letter_id: int):
    rate_limiter_key = "update_referral_letter_dependent_models_{}".format(elation_referral_letter_id)

    with get_rate_limiter(
        key=rate_limiter_key,
        limit=settings.UPDATE_REFERRAL_LETTER_DEPENDENT_MODELS_CONCURRENT_RATE_LIMIT,
    ).acquire():
        try:
            elation_referral_letter_instance: Optional[ElationReferralLetter] = None
            elation_referral_order: Optional[ElationReferralOrder] = None

            try:
                elation_referral_letter_instance = ElationReferralLetter.objects.get(id=elation_referral_letter_id)
            except ElationReferralLetter.DoesNotExist:
                logger.error(
                    "update_referral_letter_dependent_models: Elation referral letter not found for id: :%s",
                    elation_referral_letter_id,
                )
            if elation_referral_letter_instance:
                try:
                    elation_referral_order = ElationReferralOrder.objects.get(
                        elation_referral_letter=elation_referral_letter_instance
                    )
                except ElationReferralOrder.DoesNotExist:
                    logger.error(
                        "update_referral_letter_dependent_models: Elation referral order not found for letter id: :%s",
                        elation_referral_letter_instance,
                    )

                if elation_referral_order:
                    update_referral(
                        elation_referral_order=elation_referral_order,
                        elation_referral_letter=elation_referral_letter_instance,
                    )

        except Exception:
            logger.exception(
                "failed to update referral and referral recipient for elation_referral_letter : %s",
                elation_referral_letter_id,
            )
            raise


def create_referral_object(
    elation_referral_order: ElationReferralOrder,
    is_initiated_from_lucian: bool,
    steerage: Optional[Steerage] = None,
) -> Optional[Referral]:
    if elation_referral_order.patient and elation_referral_order.patient.person:
        referral_case, steerage = create_appropriate_referral_case(
            elation_referral_order.patient.person.id, is_initiated_from_lucian, steerage
        )

        if not steerage:
            raise Exception(f"save_referral: Steerage is missing for the case id: {referral_case.id}")

        referral = steerage.referral
        # For cases initiated from Elation, create a referral object for the steerage as all the Elation initiated
        # steerages will have a referral
        if not referral and is_initiated_from_lucian is False:
            referral = create_referral_for_steerage(steerage, is_initiated_from_lucian)
            logger.info("save_referral: Created referral for the steerage id %s", steerage.id)

        # Link the elation document tags to the referral case
        if referral and referral_case and steerage:
            link_tags_to_referral_case(referral_case, steerage)
        return referral
    else:
        raise Exception(
            f"save_referral: Elation referral order {elation_referral_order.id} missing patient/person details"
        )


def update_referral_authorization_icd_codes(referral: Referral, icd_codes: List[ReferralIcd10Codes]):
    for icd_code in icd_codes:
        # Update if a matching code exists for a referral else
        # create a new row representing icd10_code mapped to a referral
        referral_insurance_auth_icd_code, _ = ReferralInsuranceAuthICDCode.objects.update_or_create(
            code=icd_code["code"],
            referral=referral,
            defaults={
                "description": icd_code["description"],
            },
        )

        # add a foreign key relation to ReferralInsuranceAuthorization if one exists for a referral
        try:
            referral_insurance_authorization = ReferralInsuranceAuthorization.objects.get(referral=referral)
            referral_insurance_auth_icd_code.referral_insurance_auth = referral_insurance_authorization
            referral_insurance_auth_icd_code.save()
        except ReferralInsuranceAuthorization.DoesNotExist:
            pass


@dramatiq.actor(queue_name="fetch_latitude_longitude", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def save_provider_latitude_longitude_externally_async(steerage_provider_id: int):
    from firefly.modules.referral.utils.steerage_utils import (
        fetch_and_store_provider_latitude_longitude,
    )

    with get_window_rate_limiter(
        key="api_rate_limiter_for_get_latitude_longitude",
        limit_per_window=settings.SMARTY_STREETS_API_RATE_LIMIT,
    ).acquire():
        steerage_provider_instance: Optional[SteerageProvider] = SteerageProvider.objects.filter(
            id=steerage_provider_id
        ).first()
        if steerage_provider_instance:
            fetch_and_store_provider_latitude_longitude(steerage_provider=steerage_provider_instance)


@dramatiq.actor(
    queue_name="directory_backfill",
    **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS,
)
def store_search_request_for_provider_search_async(
    search_request_id: int,
    person_id: int,
    insurance_payer_id: int,
    insurance_plan_id: int,
    results: dict,
    vendor_results: dict,
    search_availability_id: int,
    address: str,
    records_available: int,
    vendor_http_status_code: str,
    search_status: str,
    specialty_group_id: Optional[int] = None,
    specialty_uids: Optional[List[str]] = None,
    vendor_network_uids: Optional[List[str]] = None,
    cpt_code_id: Optional[int] = None,
    procedure_uids: Optional[List[str]] = None,
    location_type_id: Optional[int] = None,
    npis: Optional[List[str]] = None,
    name: Optional[str] = None,
    facility_name: Optional[str] = None,
    steerage_id: Optional[int] = None,
    gender: Optional[str] = None,
):
    with get_window_rate_limiter(
        key="store_search_request_for_provider_search_mutex",
        limit_per_window=6,
        window_in_seconds=1,
    ).acquire():
        log_prefix: str = "store_search_request_for_provider_search_async"
        logger.info("%s: Updating search request %d", log_prefix, search_request_id)
        search_request: SearchRequest = SearchRequest.objects.get(pk=search_request_id)
        search_request.insurance_payer_id = insurance_payer_id
        search_request.insurance_plan_id = insurance_plan_id
        search_request.specialty_group_id = specialty_group_id
        search_request.vendor_network_uids = vendor_network_uids
        search_request.cpt_code_id = cpt_code_id
        search_request.procedure_uids = procedure_uids
        search_request.location_type_id = location_type_id
        search_request.npis = npis
        search_request.address = address
        search_request.name = name
        search_request.facility_name = facility_name
        search_request.records_available = records_available
        search_request.vendor_http_status_code = vendor_http_status_code
        search_request.search_status = search_status
        search_request.steerage_id = steerage_id
        search_request.search_availability_id = search_availability_id
        search_request.gender = gender
        search_request.results = results
        search_request.vendor_results = vendor_results
        search_request.specialty_uids = specialty_uids
        search_request.save(
            update_fields=[
                "insurance_payer_id",
                "insurance_plan_id",
                "specialty_group_id",
                "vendor_network_uids",
                "cpt_code_id",
                "procedure_uids",
                "location_type_id",
                "npis",
                "address",
                "name",
                "facility_name",
                "records_available",
                "vendor_http_status_code",
                "search_status",
                "steerage_id",
                "gender",
                "search_availability_id",
                "results",
                "vendor_results",
                "specialty_uids",
            ]
        )
        person: Person = Person.objects.get(id=person_id)
        insurance_info = person.insurance_info
        if insurance_info is not None:
            insurance_plan = insurance_info.insurance_plan
            if insurance_plan is not None:
                search_request.networks.set(list(insurance_plan.networks.all()))


def create_steerage_provider_from_provider_detail(
    provider: ProviderDetail,
    steerage: Steerage,
    search_request: Optional[SearchRequest],
    suggestion_source: str,
    steerage_provider_overrides: dict,
):
    locations = provider["locations"]
    address_details: AddressDetail = locations[0]["address_details"]
    state_id: Optional[int] = None
    if address_details and address_details["state"]:
        state: Optional[State] = State.objects.filter(abbreviation=address_details["state"].upper()).first()
        if state:
            state_id = state.id
    location_confidence_score = locations[0]["confidence"]
    phone_number: Optional[str] = get_primary_phone_number_from_ribbon_data(
        ribbon_phone_numbers=locations[0]["phone_numbers"]
    )
    fax_number: Optional[str] = get_primary_fax_from_ribbon_data(ribbon_fax_numbers=locations[0]["fax_numbers"])

    composite_score: Optional[float] = provider["composite_score"]
    provider_type: Optional[str] = provider["provider_type"]

    recommendation_reason: Optional[str] = provider["recommendation_reason_labels"]
    recommendation_reason_list = []
    if recommendation_reason:
        recommendation_reason_list = [reason.strip() for reason in recommendation_reason.split(",")]

    # Recommendation details
    cost_score = (
        provider["firefly_recommendation_factors"]["cost_score_out_of_five"]
        if provider["firefly_recommendation_factors"]
        else None
    )
    quality_score = (
        provider["firefly_recommendation_factors"]["quality_score_out_of_five"]
        if provider["firefly_recommendation_factors"]
        else None
    )
    number_of_ratings = (
        provider["firefly_recommendation_factors"]["number_of_ratings"]
        if provider["firefly_recommendation_factors"]
        else None
    )
    avg_rating = (
        provider["firefly_recommendation_factors"]["average_rating_out_of_five"]
        if provider["firefly_recommendation_factors"]
        else None
    )
    expertise_score = (
        provider["firefly_recommendation_factors"]["expertise_score_out_of_five"]
        if provider["firefly_recommendation_factors"]
        else None
    )

    # Pricing
    estimated_cost = (
        provider["talon_recommendation_factors"]["encounter_price"]
        if provider["talon_recommendation_factors"]
        else None
    )
    cost_breakdown = provider["locations"][0]["talon_associated_procedure_prices"]
    cpt_code = search_request.cpt_code.code if search_request and search_request.cpt_code else None

    languages = provider.get("languages", None)
    clinical_areas = provider.get("clinical_focus_areas", None)
    educations = provider.get("educations", None)
    degrees = provider.get("degrees", None)
    gender = provider.get("gender", None)

    # Adding  get_or_create to add a safeguard to avoid duplicate addition of steerage providers
    steerage_provider: SteerageProvider
    steerage_provider, _ = SteerageProvider.objects.get_or_create(
        address_line_1=address_details["address_line_1"] if address_details else None,
        address_line_2=address_details["address_line_2"] if address_details else None,
        care_organization_name=provider["locations"][0]["name"],
        city=address_details["city"] if address_details else None,
        is_system_suggested=True,
        npi=provider["npi"],
        source_identifier=provider["unique_identifier"],
        state_id=state_id,
        steerage=steerage,
        status=SteerageProviderStatuses.UNDER_REVIEW,
        zip_code=address_details["zip"] if address_details else None,
        defaults={
            "average_rating": avg_rating,
            "composite_score": composite_score,
            "cost_score": cost_score,
            "data_source": suggestion_source,
            "distance": provider["distance"],
            "fax": fax_number,
            "first_name": provider["first_name"],
            "languages": languages,
            "last_name": provider["last_name"],
            "location_confidence_score": location_confidence_score,
            "middle_name": provider["middle_name"],
            "network_partner_agreement_type": provider["network_partner_agreement_type"],
            "number_of_rating": number_of_ratings,
            "phone": phone_number,
            "quality_score": quality_score,
            "recommendation_reason": recommendation_reason_list,
            "recommendation_status": RecommendationStatusChoices.RECOMMENDED,
            "search_request": search_request,
            "specialty_group": steerage.specialty_group,
            "specialty_list": provider["specialties"]
            if provider["specialties"] is not None and len(provider["specialties"]) != 0
            else None,
            "provider_type": provider_type,
            "partner_name": locations[0]["partner_name"],
            "partnership": provider["partnership"],
            "educations": educations,
            "clinical_areas": clinical_areas,
            "degrees": degrees,
            "gender": gender,
            "expertise_score": expertise_score,
            "cpt_code": cpt_code,
            "cost_breakdown": cost_breakdown,
            "estimated_cost": estimated_cost,
            **steerage_provider_overrides,
        },
    )

    # Once the steerage provider has been created, loop through the TINs and find any
    # existing ones, or create new ones if they don't exist.
    if waffle.switch_is_active(WAFFLE_SWITCH_STORE_TINS):
        create_tax_identifiers_for_steerage_provider(
            steerage_provider, locations[0]["tins"] if "tins" in locations[0] else None
        )


@dramatiq.actor(queue_name="directory_backfill", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def add_system_suggested_providers_async(steerage_id: int):
    rate_limiter_key = f"add_suggested_providers_for_steerage_{steerage_id}"
    with get_rate_limiter(key=rate_limiter_key, limit=1).acquire():
        logger_prefix = "add_system_suggested_providers_async"
        # Get the steerage
        steerage: Steerage | None = Steerage.objects.filter(id=steerage_id).first()

        if not steerage or not steerage.person:
            logger.error("%s: Steerage not found for ID: %s", logger_prefix, steerage_id)
            return

        # Validate if the steerage provider already exist on steerage then do nothing
        does_steerage_provider_exist = SteerageProvider.objects.filter(steerage=steerage).exists()
        if does_steerage_provider_exist:
            return

        # There are two types of suggested provider to add
        # 1. First check if there are any firefly partner then add all of them as a suggested provider
        # 2. If no partners found then initiate a Talon search for specific procedure
        # 3. Else use top providers from Ribbon as suggested providers
        suggested_providers: List[tuple[ProviderDetail, SearchRequest | None]] = []
        suggested_partners = get_suggested_partners(steerage, logger_prefix) or []

        # Convert into a list of tuples for compatibility with Ribbon provider search
        suggested_providers = [(partner, None) for partner in suggested_partners]

        # Initiate the source as Firefly
        suggestion_source = SteerageProviderDataSourceConfig.FIREFLY
        if not suggested_providers:
            # If suggested partner not present then call the ribbon search to get Ribbon recommended providers
            # Cast the return value to match our variable type
            talon_facilities = get_suggested_facilities_from_talon(steerage, logger_prefix)
            suggested_providers = cast(List[tuple[ProviderDetail, SearchRequest | None]], talon_facilities)
            suggestion_source = SteerageProviderDataSourceConfig.TALON

        if not suggested_providers:
            # If suggested partner not present then call the ribbon search to get Ribbon recommended providers
            # Cast the return value to match our variable type
            ribbon_providers = get_suggested_provider_from_ribbon(steerage, logger_prefix)
            suggested_providers = cast(List[tuple[ProviderDetail, SearchRequest | None]], ribbon_providers)
            suggestion_source = SteerageProviderDataSourceConfig.RIBBON

        if not suggested_providers:
            return

        # Validate if the steerage provider already exist on steerage then do nothing
        does_steerage_provider_exist = SteerageProvider.objects.filter(steerage=steerage).exists()
        if does_steerage_provider_exist:
            return

        # Create steerage provider based on the top provider limited results
        for provider, search_request in suggested_providers:
            create_steerage_provider_from_provider_detail(
                provider,
                steerage,
                search_request,
                suggestion_source,
                steerage_provider_overrides={
                    # "recommendation_status": RecommendationStatusChoices.RECOMMENDED,
                },
            )
        return


def get_person_for_outreach(person_id):
    """
    If the outreach is not for the primary subscriber, we need to ensure that the outreach
    is sent to the appropriate person. If the person is under 18, the outreach should go to
    the primary subscriber. Otherwise, the outreach should go to the person.
    """
    try:
        person: Person = Person.objects.get(id=person_id)
        assert isinstance(person, Person)
        if person.insurance_info is not None and person.insurance_info.is_primary_subscriber:
            return person

        date_18_years_ago = date.today() + relativedelta(years=-18)
        if person.dob is not None and person.dob < date_18_years_ago:
            return person
        else:
            person = get_primary_subscriber(person)
            return person
    except (Person.DoesNotExist, AssertionError):
        logger.exception("Person %s not found for referral outreach campaign", person_id)


@dramatiq.actor(queue_name="send_referral_outreach", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def send_referral_outreach_async(steerage_id: int):
    return send_referral_outreach_sync(steerage_id)


def send_referral_outreach_sync(steerage_id: int):
    """Responsible sending automated outreach for a given referral and coordinating
    which campaign is appropriate based on the state of the Steerage."""

    steerage: Steerage = Steerage.objects.get(id=steerage_id)

    if not steerage.person:
        logger.error("Person not found for steerage id %d", steerage_id)
        return

    # Check if the person is the primary subscriber
    person_for_outreach = get_person_for_outreach(steerage.person.pk)
    person_id = person_for_outreach.id

    # Any given outreach for a referral should only be sent once. This should already be covered
    # for the initial outreach based on the "first lock" logic, but we can also determine this
    # based on the existence of an EventLog with this Steerage as the target.
    # TODO: This is the second instance of deduping campaigns based on EventLog. Consider formalizing.
    campaign_has_been_sent = EventLog.objects.filter(
        target_object_id=steerage.id,
        target_content_type=ContentType.objects.get_for_model(Steerage),
    ).exists()
    if campaign_has_been_sent:
        logger.info("Referral outreach has already been sent for steerage id %s. Skipping.", steerage.id)
        return

    # Determine which campaign to send based on the state of the Steerage:
    # - If there are multiple providers and one hasn't been selected, send REFERRAL_READY_CHOOSE_PROVIDER
    # - If a scheduling date has been set, send REFERRAL_READY_SCHEDULED
    # - If a provider has been selected *or* there is only one provider, send REFERRAL_READY_SCHEDULING
    campaign_id: str | None = None

    accepted_providers = SteerageProvider.objects.filter(
        steerage_id=steerage.id, status=SteerageProviderStatuses.ACCEPTED
    )
    has_selected = (
        SteerageProvider.objects.filter(
            steerage_id=steerage.id,
            member_selected_at__isnull=False,
        ).exists()
        or accepted_providers.count() == 1
    )
    has_multiple_providers = accepted_providers.count() > 1
    has_scheduled = steerage.scheduling_date is not None
    is_self_serviceable = is_steerage_self_serviceable(steerage)

    # Check if this is a self-service case - use the new campaign pattern
    if is_self_serviceable and waffle.switch_is_active(WAFFLE_SWITCH_SELF_SERVICE_NAV):
        # Create and send the campaign using the new pattern
        self_service_campaign = SteerageSelfServiceCampaign()
        self_service_campaign.create_context(steerage=steerage, person=person_for_outreach).send(
            person_id=person_id,
            event={
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": self_service_campaign.campaign_id},
            },
        )
        return

    # Check if this is a case with multiple providers where none has been selected
    if not has_selected and has_multiple_providers:
        # Use SteerageChooseProviderCampaign for the choose provider case
        choose_provider_campaign = SteerageChooseProviderCampaign()
        choose_provider_campaign.create_context(steerage=steerage, person=person_for_outreach).send(
            person_id=person_id,
            event={
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": choose_provider_campaign.campaign_id},
            },
        )
        return

    # For all other cases, use the original approach
    if has_selected and not has_scheduled:
        ready_to_schedule_campaign = SteerageReadyToScheduleCampaign()
        ready_to_schedule_campaign.create_context(steerage=steerage, person=person_for_outreach).send(
            person_id=person_id,
            event={
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": ready_to_schedule_campaign.campaign_id},
            },
        )
        return

    if has_scheduled:
        scheduled_campaign = SteerageScheduledCampaign()
        scheduled_campaign.create_context(steerage=steerage, person=person_for_outreach).send(
            person_id=person_id,
            event={
                "target_content_type": "steerage",
                "target_object_id": steerage.id,
                "type": "referral_outreach",
                "metadata": {"campaign_id": scheduled_campaign.campaign_id},
            },
        )
        return

    if campaign_id is None:
        logger.exception("No campaign found for referral outreach for steerage id %s", steerage.id)
        return


@dramatiq.actor(queue_name="send_referral_outreach", **RETRY_DEFAULTS_FOR_RETRIABLE_WORKFLOWS)
def send_referral_appointment_reminder_async(steerage_id: int):
    return send_referral_appointment_reminder_sync(steerage_id)


def send_referral_appointment_reminder_sync(steerage_id: int):
    steerage: Steerage = Steerage.objects.get(id=steerage_id)

    if not steerage.person:
        logger.error("Person not found for steerage id %d", steerage_id)
        return

    person_for_outreach = get_person_for_outreach(steerage.person.id)
    person_id = person_for_outreach.id

    # Check to see if the person is actively enrolled in any programs. Otherwise it's
    # possible that Churned members will receive reminders.
    enrollments = get_person_program_enrollments(Person.objects.get(id=person_id))
    eligible_for_reminder = len(enrollments) > 0
    if not eligible_for_reminder:
        logger.info("Person %d is not eligible for reminder. Exiting.", person_id)
        return

    campaign_id = settings.BRAZE["REFERRAL_APPOINTMENT_REMINDER"]

    # Since messages in the queue can be delayed. We need to ensure that the scheduling
    # date is in the future, and calculate the days offset to send in the campaign.
    try:
        assert isinstance(steerage.scheduling_date, date)
        days_offset = (steerage.scheduling_date - date.today()).days
        if days_offset < 1:
            logger.error("Scheduling date is in the past for steerage id %d. Exiting.", steerage_id)
            return
        date_offset_specifier = f"{days_offset} day"
        if days_offset > 1:
            date_offset_specifier += "s"
    except AssertionError:
        logger.error("Scheduling date not found for steerage id %d. Exiting.", steerage_id)
        return

    # Check if this campaign has been sent for this Steerage before
    campaign_has_been_sent = EventLog.objects.filter(
        target_object_id=steerage.id,
        target_content_type=ContentType.objects.get_for_model(Steerage),
        metadata__campaign_id=campaign_id,
    ).exists()

    # If the campaign has already been sent, don't send it again
    if campaign_has_been_sent:
        logger.info("Skipping appointment reminder for %d because it has already been sent.", steerage_id)
        return

    # All referrals should have either a specialty or a location type in this context.
    # This is because the CRM shows a combination of those two fields and considers a
    # selection mandatory.
    specialty = steerage.specialty_group or steerage.location_type
    assert specialty is not None
    specialty_label = specialty.label.lower()
    assert steerage.person is not None

    # Send the reminder
    reminders.send(
        campaign_id,
        [
            {
                "external_user_id": str(person_id),
                "send_to_existing_only": True,
                "trigger_properties": {
                    "specialty": specialty_label,
                    "steerage_id": str(steerage_id),
                    "date_offset_specifier": date_offset_specifier,
                    "variant": "coverage" if steerage.is_effectively_waiver_only else "care",
                },
            }
        ],
        {
            "target_content_type": "steerage",
            "target_object_id": steerage.id,
            "type": "referral_outreach",
            "metadata": {"campaign_id": campaign_id},
        },
    )


def get_referral_appointment_reminder_default_scheduling_date():
    return date.today() + timedelta(days=2)
