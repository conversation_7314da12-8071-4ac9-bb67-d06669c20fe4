from django.contrib.auth.models import Group
from django.core import management

from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.roles.models import Role
from firefly.core.tests.client import PubNubTestAPIClient
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import HEALTH_GUIDE_ROLE, MD_ROLE, NP_ROLE
from firefly.core.user.factories import PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import Person, ProviderDetailPatientPersons
from firefly.core.user.serializers.serializers import CareTeamTemplateSerializer
from firefly.modules.care_teams.factories import CareTeamTemplateFactory
from firefly.modules.care_teams.models import CareTeamTemplate
from firefly.modules.care_teams.utils import assign_care_team_from_template, set_primary_physician
from firefly.modules.insurance.factories import InsuranceMemberInfoFactory
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.physician.factories import PhysicianFactory
from firefly.modules.programs.primary_care.models import PrimaryCareProgramInfo
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import (
    add_person_to_program,
    program_info_for_person_program,
    remove_person_from_program,
)
from firefly.modules.states.models import State


def get_set_primary_care_program_info(person):
    primary_care_program_info = program_info_for_person_program(person, ProgramCodes.PRIMARY_CARE)
    if not primary_care_program_info:
        primary_care_program_info = PrimaryCareProgramInfo.objects.create(person=person)
    return primary_care_program_info


class CareTeamTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        physician_1 = ProviderDetailFactory.create().physician
        state_object, _ = State.objects.get_or_create(abbreviation="MA")
        physician_1.practicing_states.add(state_object)
        physician_2 = ProviderDetailFactory.create().physician
        physician_2.practicing_states.add(state_object)
        physician_3 = ProviderDetailFactory.create().physician
        physician_3.practicing_states.add(state_object)
        self.care_team_template = CareTeamTemplateFactory.create(
            physicians=(physician_1.pk, physician_2.pk), primary_physician=physician_3
        )

    def set_patients_to_care_team(self, count, care_team_template: CareTeamTemplate):
        for _i in range(0, count):
            person = PersonUserFactory()
            person_1 = Person.objects.get(id=person.id)
            user = person_1.user
            if user is not None and hasattr(user, "onboarding_state") and user.onboarding_state is not None:
                user.onboarding_state.status = OnboardingStatus.SIGNEDUP
                user.onboarding_state.save()
            ProviderDetailPatientPersons.objects.create(
                providerdetail=care_team_template.primary_physician.provider, person=person
            )

    def test_backfill_panel_size(self):
        CareTeamTemplate.objects.all().delete()
        physician_1 = PhysicianFactory()
        physician_2 = PhysicianFactory()
        care_team_template_1 = CareTeamTemplateFactory()
        care_team_template_1.physicians.set([physician_1, physician_2])
        care_team_template_2 = CareTeamTemplateFactory()
        care_team_template_2.physicians.set([physician_2])
        self.set_patients_to_care_team(2, care_team_template_1)
        self.set_patients_to_care_team(4, care_team_template_2)
        management.call_command("backfill_primary_physician_panel_size", dry_run_off=True, user=self.provider)
        care_team_template_1 = CareTeamTemplate.objects.get(id=care_team_template_1.id)
        self.assertEqual(care_team_template_1.primary_physician_panel_size, 2)
        care_team_template_2 = CareTeamTemplate.objects.get(id=care_team_template_2.id)
        self.assertEqual(care_team_template_2.primary_physician_panel_size, 4)
        deleted_provider_patient = ProviderDetailPatientPersons.objects.filter(
            providerdetail_id=care_team_template_2.primary_physician.provider.pk
        ).first()
        deleted_provider_patient.delete()
        management.call_command("backfill_primary_physician_panel_size", dry_run_off=True, user=self.provider)
        care_team_template_1 = CareTeamTemplate.objects.get(id=care_team_template_1.id)
        self.assertEqual(care_team_template_1.primary_physician_panel_size, 2)
        # When physician is removed from patients care team it should be excluded
        care_team_template_2 = CareTeamTemplate.objects.get(id=care_team_template_2.id)
        self.assertEqual(care_team_template_2.primary_physician_panel_size, 3)
        # only people with onboarding status as member or signedup should be retrieved
        patient_1 = ProviderDetailPatientPersons.objects.filter(
            providerdetail_id=care_team_template_1.primary_physician.provider.pk
        ).first()
        person_1 = Person.objects.get(id=patient_1.person.id)
        person_1.user.onboarding_state.status = OnboardingStatus.DEACTIVATED
        person_1.user.onboarding_state.save()
        management.call_command("backfill_primary_physician_panel_size", dry_run_off=True, user=self.provider)
        care_team_template_1 = CareTeamTemplate.objects.get(id=care_team_template_1.id)
        self.assertEqual(care_team_template_1.primary_physician_panel_size, 1)

    def test_serialized_annotation_data(self):
        # Test for serialized data that we expect to come from queryset annotations
        # Expect reasonable defaults when those annotations aren't present
        for serializerCls in [CareTeamTemplateSerializer]:
            serialized_data = serializerCls(self.care_team_template).data
            self.assertFalse(serialized_data["licensed_in_patient_state"])
            self.assertFalse(serialized_data["license_pending_in_patient_state"])
            self.assertEqual(serialized_data["primary_physician_panel_size"], None)

    def _create_care_team(self, care_team_template):
        response = self.client.post(f"/care-teams/patients/me/templates/{care_team_template.id}/")
        self.assertEqual(response.status_code, 201)
        care_team = response.json()
        response = self.client.get(f"/care-teams/patients/me/teams/{care_team['id']}/")
        self.assertEqual(response.status_code, 200)
        care_team = response.json()
        physician_ids = [physician["id"] for physician in care_team["physicians"]]
        self.assertIn(care_team_template.primary_physician.id, physician_ids)
        return care_team

    def _patch_care_team(self, care_team_id, payload):
        response = self.client.patch(f"/care-teams/patients/me/teams/{care_team_id}/", payload)
        self.assertEqual(response.status_code, 200)
        return response

    def _send_message(self, thread, sender_id):
        message_payload = {
            "text": "Test message from provider",
            "thread": thread.uid,
            "sender": sender_id,
            "uid": f"test1.{sender_id}",
        }
        pubnub_client = PubNubTestAPIClient()
        response = pubnub_client.post(
            "/chat/v2/",
            message_payload,
        )
        self.assertEqual(response.status_code, 201)
        return response

    def test_assign_care_team_from_template(self):
        md_provider = ProviderDetailFactory.create()
        np_provider = ProviderDetailFactory.create()
        hg_provider = ProviderDetailFactory.create()
        another_hg_provider = ProviderDetailFactory.create()

        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)

        group_md.user_set.add(md_provider.user)
        group_np.user_set.add(np_provider.user)
        group_hg.user_set.add(hg_provider.user)
        group_hg.user_set.add(another_hg_provider.user)

        template = CareTeamTemplateFactory(
            physicians=(
                md_provider.physician,
                np_provider.physician,
                hg_provider.physician,
                another_hg_provider.physician,
            )
        )
        template.physicians.set(
            [
                md_provider.physician,
                np_provider.physician,
                hg_provider.physician,
            ]
        )
        state = State.objects.filter(abbreviation__iexact=self.patient.person.insurance_info.state).first()
        template.primary_physician.practicing_states.add(state)

        primary_care_info = get_set_primary_care_program_info(self.patient.person)
        assign_care_team_from_template(self.patient, template)
        primary_care_info.refresh_from_db()
        self.assertEqual(primary_care_info.primary_physician, np_provider.physician)
        expected_physician_ids = list(template.physicians.all().values_list("id", flat=True))
        expected_physician_ids = [
            md_provider.physician.pk,
            np_provider.physician.pk,
            hg_provider.physician.pk,
        ]
        expected_physician_ids.append(template.primary_physician.id)
        self.assertEqual(
            sorted(list(self.patient.person.care_team.all().values_list("physician_id", flat=True))),
            sorted(expected_physician_ids),
        )

        # Set NP as the primary physician
        np_provider.physician.practicing_states.add(state)
        template = CareTeamTemplateFactory(
            primary_physician=np_provider.physician,
            physicians=(md_provider.physician, hg_provider.physician),
        )
        assign_care_team_from_template(self.patient, template)
        primary_care_info.refresh_from_db()
        self.assertEqual(primary_care_info.primary_physician, np_provider.physician)
        expected_physician_ids = list(template.physicians.all().values_list("id", flat=True))
        expected_physician_ids.append(template.primary_physician.id)
        self.assertEqual(
            list(sorted(self.patient.person.care_team.all().values_list("physician_id", flat=True))),
            sorted(expected_physician_ids),
        )

    def test_assign_care_team_template_with_licensing(self):
        md_provider = ProviderDetailFactory.create()
        np_provider = ProviderDetailFactory.create()
        hg_provider = ProviderDetailFactory.create()

        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)

        group_md.user_set.add(md_provider.user)
        group_np.user_set.add(np_provider.user)
        group_hg.user_set.add(hg_provider.user)

        state_MA, _ = State.objects.get_or_create(abbreviation="MA")
        state_NH, _ = State.objects.get_or_create(abbreviation="NH")

        md_provider.physician.practicing_states.add(state_MA)
        md_provider.physician.practicing_states.add(state_NH)
        np_provider.physician.practicing_states.add(state_MA)

        self.patient.person.insurance_info = InsuranceMemberInfoFactory()
        self.patient.person.save()
        self.patient.person.insurance_info.state = state_NH.abbreviation
        self.patient.person.insurance_info.save()

        # Let's also test the case an MD is also marked as an NP (this may happen if there is no NP licensed in state
        # when the user selects a care team)
        group_np.user_set.add(md_provider.user)

        template = CareTeamTemplateFactory(
            primary_physician=np_provider.physician, physicians=(md_provider.physician, hg_provider.physician)
        )
        primary_care_info = get_set_primary_care_program_info(self.patient.person)

        # Let's say the NP is not licensed in the state the patient resides in, so let's resort to MD as the primary
        # physician. In addition, the NP should not be in the care team
        assign_care_team_from_template(self.patient, template)
        primary_care_info.refresh_from_db()
        self.assertEqual(primary_care_info.primary_physician, md_provider.physician)
        expected_physician_ids = list(template.physicians.all().values_list("id", flat=True))
        actual_physician_ids = list(self.patient.person.care_team.all().values_list("physician_id", flat=True))
        self.assertCountEqual(actual_physician_ids, expected_physician_ids)
        np_not_in_careteam = np_provider.user.id not in actual_physician_ids
        self.assertTrue(np_not_in_careteam)
        self.assertCountEqual(
            self.patient.person.pods.all().values_list("id", flat=True),
            np_provider.pods.all().values_list("id", flat=True),
        )

        # Let's say the patient is now in a state where both the NP and MD are licensed. Let's ensure the behavior
        # stays the same as before and all members get added to the care team.
        self.patient.person.insurance_info.state = state_MA.abbreviation
        self.patient.person.insurance_info.save()
        assign_care_team_from_template(self.patient, template)
        primary_care_info.refresh_from_db()
        self.assertEqual(primary_care_info.primary_physician, np_provider.physician)
        expected_physician_ids = list(template.physicians.all().values_list("id", flat=True))
        expected_physician_ids.append(template.primary_physician.id)
        actual_physician_ids = list(self.patient.person.care_team.all().values_list("physician_id", flat=True))
        self.assertCountEqual(actual_physician_ids, expected_physician_ids)

    def test_set_primary_physician(self):
        primary_care_program_info = get_set_primary_care_program_info(self.patient.person)
        new_physician = PhysicianFactory()
        set_primary_physician(self.patient.person, new_physician)
        primary_care_program_info.refresh_from_db()
        self.assertEqual(primary_care_program_info.primary_physician, new_physician)
        # Test update to  inactive program info
        remove_person_from_program(self.patient.person, primary_care_program_info.program.uid)
        new_physician = PhysicianFactory()
        set_primary_physician(self.patient.person, new_physician)
        primary_care_program_info.refresh_from_db()
        self.assertEqual(primary_care_program_info.primary_physician, new_physician)


class ReplaceCareTeamMembersTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(*args, **kwargs)
        self.patient.onboarding_state.to_member()
        self.provider_np = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.NP))
        self.provider_md = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.PHYSICIAN))
        self.provider_hg = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.HG))
        self.provider_bh = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.BH))
        self.provider_new_np = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.NP))
        self.provider_new_hg = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.HG))
        self.provider_other_hg = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.HG))
        self.provider_rn = ProviderDetailFactory.create(
            internal_role=Role.objects.get(role_name=ROLE_VALUES.RN),
        )

        self.person_1 = PersonUserFactory()
        add_person_to_program(person=self.person_1, program_uid=ProgramCodes.PRIMARY_CARE)
        self.person_2 = PersonUserFactory()
        add_person_to_program(person=self.person_2, program_uid=ProgramCodes.PRIMARY_CARE)
        self.person_3 = PersonUserFactory()
        add_person_to_program(person=self.person_3, program_uid=ProgramCodes.PRIMARY_CARE)
        self.person_4 = PersonUserFactory()
        add_person_to_program(person=self.person_4, program_uid=ProgramCodes.PRIMARY_CARE)
        self.person_5 = PersonUserFactory()
        add_person_to_program(person=self.person_5, program_uid=ProgramCodes.PRIMARY_CARE)

        self.person_1.care_team.add(self.provider_np.user.id)
        self.person_1.care_team.add(self.provider_md.user.id)
        self.person_1.care_team.add(self.provider_hg.user.id)
        self.person_1.care_team.add(self.provider_bh.user.id)
        set_primary_physician(self.person_1, self.provider_np.physician)

        self.person_2.care_team.add(self.provider_np.user.id)
        self.person_2.care_team.add(self.provider_md.user.id)
        self.person_2.care_team.add(self.provider_other_hg.user.id)
        self.person_2.care_team.add(self.provider_bh.user.id)
        set_primary_physician(self.person_2, self.provider_np.physician)

        # Set up a Person whose care team has been cleared
        # so we can test for the case of a deleted m2m relationship
        self.person_3.care_team.add(self.provider_np.user.id)
        self.person_3.care_team.add(self.provider_md.user.id)
        self.person_3.care_team.add(self.provider_other_hg.user.id)
        self.person_3.care_team.add(self.provider_bh.user.id)
        set_primary_physician(self.person_3, self.provider_np.physician)
        self.person_3.care_team.clear()

        # set up Person for new_care_team role test
        self.person_4.care_team.add(self.provider_np.user.id)
        self.person_4.care_team.add(self.provider_md.user.id)
        self.person_4.care_team.add(self.provider_rn.user.id)
        set_primary_physician(self.person_4, self.provider_np.physician)

        self.person_5.care_team.add(self.provider_np.user.id)
        self.person_5.care_team.add(self.provider_md.user.id)
        self.person_5.care_team.add(self.provider_other_hg.user.id)
        self.person_5.care_team.add(self.provider_bh.user.id)
        set_primary_physician(self.person_5, self.provider_np.physician)

    def test_users_in_command(self):
        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team = self.person_5.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team_ids = f"{self.provider_other_hg.user.id}"
        new_care_team_ids = f"{self.provider_hg.user.id}"
        users = f"{self.person_2.user.id}, {self.person_5.user.id}"

        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=True,
            limit=100,
            users=users,
            user=self.provider,
        )

        care_team = self.person_2.care_team.all()
        self.assertIn(self.provider_hg, care_team)
        self.assertNotIn(self.provider_other_hg, care_team)

        care_team = self.person_5.care_team.all()
        self.assertIn(self.provider_hg, care_team)
        self.assertNotIn(self.provider_other_hg, care_team)

    def test_command(self):
        care_team = self.person_1.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team_ids = f"{self.provider_np.user.id},{self.provider_hg.user.id}"
        new_care_team_ids = f"{self.provider_np.user.id},{self.provider_new_hg.user.id}"

        # Test state filtering
        self.person_1.insurance_info.state = None
        self.person_1.insurance_info.save()
        self.person_2.insurance_info.state = "ME"
        self.person_2.insurance_info.save()

        # (person_1 has no state)
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=True,
            limit=100,
            states_of_residence="Tx,Fl",
            user=self.provider,
        )

        care_team = self.person_1.care_team.all()
        self.assertIn(self.provider_hg, care_team)
        self.assertNotIn(self.provider_new_hg, care_team)

        self.person_1.insurance_info.state = "MA"
        self.person_1.insurance_info.save()

        # (person_1 state does not match)
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=True,
            limit=100,
            states_of_residence="Tx,Fl",
            user=self.provider,
        )

        care_team = self.person_1.care_team.all()
        self.assertIn(self.provider_hg, care_team)
        self.assertNotIn(self.provider_new_hg, care_team)

        # Now state matches
        # Should only update the hg for person_1
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=True,
            limit=100,
            states_of_residence="Ma,Me",
            user=self.provider,
        )

        care_team = self.person_1.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_new_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        # Should change the NP for both the patients
        care_team_ids = str(self.provider_np.user.id)
        new_care_team_ids = str(self.provider_new_np.user.id)
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=True,
            limit=100,
            user=self.provider,
        )

        care_team = self.person_1.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_new_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_new_hg, care_team)
        self.assertIn(self.provider_bh, care_team)
        primary_care_program_info = get_set_primary_care_program_info(self.person_1)
        self.assertEqual(primary_care_program_info.primary_physician, self.provider_new_np.physician)

        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_new_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)
        primary_care_program_info = get_set_primary_care_program_info(self.person_2)
        self.assertEqual(primary_care_program_info.primary_physician, self.provider_new_np.physician)

    def test_dry_run(self):
        care_team = self.person_1.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team_ids = f"{self.provider_np.user.id},{self.provider_hg.user.id}"
        new_care_team_ids = f"{self.provider_np.user.id},{self.provider_new_hg.user.id}"
        # Shouldn't update the care team
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            dry_run_off=False,
            limit=100,
            user=self.provider,
        )

        care_team = self.person_1.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

        care_team = self.person_2.care_team.all()
        self.assertEqual(care_team.count(), 4)
        self.assertIn(self.provider_np, care_team)
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_other_hg, care_team)
        self.assertIn(self.provider_bh, care_team)

    def test_command_with_role(self):
        original_care_team = list(self.person_4.care_team.all())
        self.assertEqual(len(original_care_team), 3)
        self.assertIn(self.provider_np, original_care_team)
        self.assertIn(self.provider_md, original_care_team)
        self.assertIn(self.provider_rn, original_care_team)
        primary_care_program_info = get_set_primary_care_program_info(self.person_4)
        primary_physician = primary_care_program_info.primary_physician
        new_provider_rn = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.RN))
        new_provider_np = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.NP))

        # NP mapped to RN, but filtering for RN role?
        # Expect no replacement
        care_team_ids = f"{self.provider_np.user.id}"
        new_care_team_ids = f"{new_provider_rn.user.id}"

        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            new_care_team_role=ROLE_VALUES.RN,
            dry_run_off=True,
            limit=100,
            user=self.provider,
        )

        care_team = self.person_4.care_team.all()
        self.assertCountEqual(list(care_team.all()), list(original_care_team))
        primary_care_program_info = get_set_primary_care_program_info(self.person_4)
        self.assertEqual(primary_care_program_info.primary_physician, primary_physician)

        # NP mapped to NP, but filtering for RN role?
        # Expect no replacement
        care_team_ids = f"{self.provider_np.user.id}"
        new_care_team_ids = f"{new_provider_np.user.id}"

        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            new_care_team_role=ROLE_VALUES.RN,
            dry_run_off=True,
            limit=100,
            user=self.provider,
        )

        care_team = self.person_4.care_team.all()
        self.assertCountEqual(list(care_team.all()), list(original_care_team))
        primary_care_program_info = get_set_primary_care_program_info(self.person_4)
        self.assertEqual(primary_care_program_info.primary_physician, primary_physician)

        # Finally a combination that makes sense
        # Map old NP to new NP, and choose the NP role
        care_team_ids = f"{self.provider_np.user.id}"
        new_care_team_ids = f"{new_provider_np.user.id}"
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            new_care_team_role=ROLE_VALUES.NP,
            dry_run_off=True,
            limit=100,
            user=self.provider,
        )
        care_team = self.person_4.care_team.all()
        self.assertEqual(care_team.count(), 3)
        # Only NP should have been replaced
        self.assertNotIn(self.provider_np, care_team)
        self.assertIn(new_provider_np, care_team)
        # Other roles are unchanged
        self.assertIn(self.provider_md, care_team)
        self.assertIn(self.provider_rn, care_team)
        primary_care_program_info = get_set_primary_care_program_info(self.person_4)
        # NP was updated so PCP should be updated
        self.assertEqual(primary_care_program_info.primary_physician, new_provider_np.physician)

        # Select a combination of providers, while only switching 1 of them
        # e.g. "For Danielle's (NP) patients who are currently assigned to Sarah (RN)
        # replace Sarah (RN) with Lisandra (RN)"
        # Here we're specifying that we want to change the RN, the 2nd provider in the list
        care_team_ids = f"{self.provider_np.user.id},{self.provider_rn.user.id}"
        new_care_team_ids = f"{self.provider_np.user.id},{new_provider_rn.user.id}"
        # This person has the matching (NP,RN) combo:
        self.person_1.care_team.set(
            [
                self.provider_rn.user.id,
                self.provider_np.user.id,
            ]
        )
        # This person does not (wrong NP):
        wrong_np = ProviderDetailFactory.create(internal_role=Role.objects.get(role_name=ROLE_VALUES.NP))
        self.person_2.care_team.set(
            [
                self.provider_rn.user.id,
                wrong_np.user.id,
            ]
        )
        original_person_2_care_team = list(map(lambda provider: provider.user_id, self.person_2.care_team.all()))
        management.call_command(
            "replace_care_team_members",
            care_team_ids=care_team_ids,
            new_care_team_ids=new_care_team_ids,
            new_care_team_role=ROLE_VALUES.RN,
            dry_run_off=True,
            limit=100,
            user=self.provider,
        )
        person_1_care_team = list(map(lambda provider: provider.user_id, self.person_1.care_team.all()))
        self.assertCountEqual(list(person_1_care_team), [self.provider_np.user.id, new_provider_rn.user.id])
        # Person 2 didn't have the specified NP, so their RN should be unchanged
        person_2_care_team = list(map(lambda provider: provider.user_id, self.person_2.care_team.all()))
        self.assertCountEqual(list(person_2_care_team), list(original_person_2_care_team))
