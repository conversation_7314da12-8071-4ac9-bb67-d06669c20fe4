import sys

from django import forms

from firefly.modules.care_teams.management.commands.replace_care_team_members import HELP_TEXT
from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand


class ReplaceCareTeamMembers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        care_team_ids = forms.CharField(required=True, help_text=HELP_TEXT["care_team_ids"])
        new_care_team_ids = forms.CharField(required=True, help_text=HELP_TEXT["new_care_team_ids"])
        new_care_team_role = forms.Char<PERSON>ield(required=False, help_text=HELP_TEXT["new_care_team_role"])
        limit = forms.IntegerField(
            required=False,
        )
        offset = forms.IntegerField(
            required=False,
        )
        states_of_residence = forms.CharField(required=False, help_text=HELP_TEXT["states_of_residence"])
        users = forms.Char<PERSON><PERSON>(required=False, help_text=HELP_TEXT["users"])
        selective_users = forms.FileField(
            widget=forms.ClearableFileInput,
            required=False,
            label="Selective users care team replace members CSV",
            help_text=HELP_TEXT["selective_users"],
        )

        def clean_selective_users(self):
            data = self.cleaned_data["selective_users"]
            if data:
                sys.stdin = data.file
            return data

    def get_command_arguments(self, data, user):
        args = [
            "-care_team_ids",
            data["care_team_ids"],
            "-new_care_team_ids",
            data["new_care_team_ids"],
        ]
        if data.get("selective_users") is not None:
            args.append("-selective_users")
            args.append("-")
        if data.get("users") is not None:
            args.append("-users")
            args.append(data["users"])
        if data.get("states_of_residence") is not None:
            args.append("-states_of_residence")
            args.append(data["states_of_residence"])
        if data["new_care_team_role"]:
            args += ["-new_care_team_role", data["new_care_team_role"]]
        if data["limit"]:
            args.append("-limit")
            args.append(data["limit"])
        if data["offset"]:
            args.append("-offset")
            args.append(data["offset"])
        return args, {
            "user": user,
        }


class BackfillPrimaryPhysicianPanelSize(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        opts = []
        return opts, {
            "user": user,
        }
