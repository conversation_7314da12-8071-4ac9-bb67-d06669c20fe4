# Worklists

## Motivation

The worklists module provides organized task management and workflow coordination for healthcare providers, enabling efficient prioritization and tracking of clinical and operational work items. It integrates with multiple data sources to create dynamic, filterable work queues that support provider productivity and patient care coordination.

## Business Context

Worklists serve as the primary task organization system for Firefly providers, supporting:
- Clinical workflow management and task prioritization
- Operational work coordination across different departments
- Provider assignment and workload distribution
- Integration with external data sources (Looker, Work Units)
- State machine-driven workflow automation
- Tenant-specific work organization and access control

## Core Concepts

### Worklist Architecture
- **Worklist**: Container for organized work items with filtering and assignment logic
- **Work Units**: Individual work items that can be cases, tasks, or other actionable items
- **Assignment Schemes**: Automated provider assignment based on configurable rules
- **State Machines**: Workflow automation with status transitions and actions
- **Categories**: Hierarchical organization (Clinical, Clinical Ops, Member Ops, Network Ops)

### Data Sources
- **Work Unit API**: Direct integration with internal work units (cases, tasks)
- **Looker Integration**: Legacy integration with Looker looks for comprehensive data access
- **Filter Queries**: Direct database queries for real-time data access

## Technical Implementation

### Core Models

**Worklist**: Central worklist configuration and management
- Category and subcategory organization for work classification
- Assignment scheme configuration for automated provider assignment
- Status value management for workflow states
- State machine integration for automated workflow transitions
- Tenant association for multi-tenant access control

### Data Source Integration

**Work Unit API Integration**: Direct work unit access
- Query configuration for filtering work units
- Support for cases as primary work unit type
- Real-time data access without external dependencies
- Integration with work unit filtering and sorting

**Legacy Looker Integration**: External data source support
- Deprecated Looker look integration for historical compatibility
- Periodic synchronization with external data sources
- Field mapping and data transformation capabilities

### Frontend Integration

**TypeScript Models**: Consistent data structures
- Worklist interface with data source identification
- Work unit integration for item management
- Status category management for UI state handling
- Real-time item count and status tracking

## Business Logic

### Work Unit Worklists

**Direct Work Unit Integration**: Modern worklist implementation
- Configuration through `work_unit_query_config` JSON field
- Query parameter-based filtering for Django ORM operations
- Support for complex filtering without Looker dependency
- Real-time data access with no synchronization delays

**Query Configuration**: Flexible filtering system
```json
{
  "query_params": "category_id=1&owner__id=9"
}
```
- URL query parameter format for Django filter operations
- Support for foreign key relationships and complex queries
- Automatic filtering for incomplete work units only

### Assignment and Workflow Management

**Automated Assignment**: Provider assignment based on configurable schemes
- Default assignee group configuration
- Owner group management for escalation
- Integration with provider availability and licensing
- Workload distribution and capacity management

**State Machine Workflows**: Automated workflow transitions
- JSON-based state machine configuration
- Status transitions with automated actions
- Category-based state organization
- Integration with work unit lifecycle management

### Filtering and Sorting

**Backend Filtering**: Server-side filtering for performance
- JSON-based filter configuration with multiple operators
- Support for user data, item data, and work unit fields
- Link operators for complex filter combinations
- Type-specific filtering for different data sources

**Filter Structure**:
```json
{
  "items": [
    {
      "field": "status",
      "operator": "equals",
      "type": "item",
      "value": "open"
    }
  ],
  "linkOperator": "and"
}
```

**Sorting Configuration**: Flexible sorting options
- Field-based sorting with direction control
- Support for multiple data source types
- Integration with pagination for large datasets

## Configuration

### Creating Work Unit Worklists

**Modern Approach**: Direct work unit integration
1. **Create Worklist**: Define worklist with category and subcategory
2. **Configure Query**: Set `work_unit_query_config` with filter parameters
3. **Set Assignment**: Configure default assignee groups and ownership
4. **State Machine**: Define workflow states and transitions
5. **Tenant Association**: Link to appropriate tenant for access control

**Example Configuration**:
```json
{
  "query_params": "category__title=Clinical&status_category=open"
}
```

### Legacy Looker Configuration

**Deprecated Approach**: For historical compatibility only
- Looker configuration requires test environment limitations
- Primary key field requirements for data consistency
- Periodic synchronization with external dependencies
- Field mapping and data transformation complexity

### Assignment Configuration

**Provider Assignment**: Automated assignment based on rules
- Default assignee group configuration for new work items
- Owner group management for escalation and oversight
- Integration with provider availability and state licensing
- Workload distribution and capacity management

### State Machine Configuration

**Workflow Automation**: JSON-based state machine setup
```json
{
  "state_with_categories": ["open", "in_progress", "complete"],
  "initial_state": "open",
  "transitions": [
    {"from": "open", "to": "in_progress", "action": "start_work"},
    {"from": "in_progress", "to": "complete", "action": "finish_work"}
  ],
  "initial_action": "create_work_item"
}
```

## API Endpoints

- `GET/POST /worklists/` - List and create worklists
- `GET/PUT /worklists/<id>/` - Retrieve and update specific worklist
- `GET /worklists/<worklist_id>/work-units/` - List work units for worklist
- Support for filtering and sorting via query parameters

## Limitations

- Work Unit worklists currently limited to Case work units
- State machine configuration requires JSON schema knowledge
- Filter and sort operations moved to backend for performance, limiting frontend flexibility
