import logging

from rest_framework.exceptions import ValidationError

logger = logging.getLogger(__name__)


def sort_and_intersect(one, two):
    intersect = list(set(one) & set(two))
    intersect.sort()
    return intersect


# Validate that status values are valid and forward compatible
# Errors here should always be hard and generate a 400
def validate_status_config(
    status_values=None,
    complete_status_values=None,
    defer_status_values=None,
    initial_status_value=None,
    reopen_status_value=None,
):
    status_values = status_values or []
    complete_status_values = complete_status_values or []
    defer_status_values = defer_status_values or []
    if sort_and_intersect(status_values, complete_status_values) != sorted(complete_status_values):
        raise ValidationError(
            "All complete_status_values must be in status_values"
            f" - complete_status_values {complete_status_values} - status_values - {status_values}"
        )

    if defer_status_values and sort_and_intersect(status_values, defer_status_values) != sorted(defer_status_values):
        raise ValidationError(
            "All defer_status_values must be in status_values"
            f" - defer_status_values {defer_status_values} - status_values - {status_values}"
        )

    if defer_status_values and sort_and_intersect(defer_status_values, complete_status_values) != []:
        raise ValidationError(
            "No overlap is allowed between complete_status_values and defer_status_values"
            f" - complete_status_values {complete_status_values}"
            f" - defer_status_values - {defer_status_values}"
        )

    if initial_status_value is None:
        raise ValidationError("'initial_status_value' is required")

    if initial_status_value not in status_values:
        raise ValidationError(
            f"'initial_status_value' value of {initial_status_value}"
            f" must be in status_values.  Options are {status_values}"
        )

    if reopen_status_value and reopen_status_value not in status_values:
        raise ValidationError(
            f"'reopen_status_value' value of {reopen_status_value}"
            f" must bein status_values.  Options are {status_values}"
        )

    if defer_status_values and (
        initial_status_value in complete_status_values or initial_status_value in defer_status_values
    ):
        raise ValidationError(
            "'initial_status_value' cannot be present in complete-status-values or defer-status-values"
        )
