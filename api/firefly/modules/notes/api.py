from django.contrib.contenttypes.models import ContentType
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from firefly.core.user.models.models import Person
from firefly.core.user.permissions import Is<PERSON>rovider

from .models import Note, NoteType
from .serializers import NoteSerializer


class NoteViewSet(viewsets.ModelViewSet):
    """
    A simple ViewSet for viewing and editing notes.
    """

    serializer_class = NoteSerializer
    permission_classes = [IsAuthenticated, IsProvider]
    skip_tenant_access_check = True
    filter_backends = (DjangoFilterBackend, filters.OrderingFilter)
    filterset_fields = ("created_by", "patient", "note_type", "object_id", "person")
    ordering = ("-created_at",)
    queryset = Note.objects.all()

    def create(self, request, *args, **kwargs):
        request.data["created_by"] = request.user.pk
        request_data = request.data
        if request_data.get("note_type", None) == NoteType.ONBOARDING:
            request_data["content_type"] = ContentType.objects.get_for_model(Person).pk
        serializer = NoteSerializer(data=request_data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
