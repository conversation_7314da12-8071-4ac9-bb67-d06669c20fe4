from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from firefly.core.user.models.models import Person
from firefly.modules.firefly_django.models import BaseModelV3


class NoteType:
    ONBOARDING = "onboarding"
    WORKLIST = "worklist"


NOTE_TYPE_CHOICES = [
    (NoteType.ONBOARDING, NoteType.ONBOARDING),
    (NoteType.WORKLIST, NoteType.WORKLIST),
]


class Note(BaseModelV3):
    content = models.TextField(default="", null=True, blank=True)
    metadata = JSONField(default=dict, null=True, blank=True)

    # TODO(nikhil): convert to GenericForeignKey for related items (rather than just patient)
    patient = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        related_name="notes",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    person = models.ForeignKey(
        Person,
        related_name="notes",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.DO_NOTHING,  # noqa: TID251
        null=True,
        blank=True,
    )

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey("content_type", "object_id")

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    note_type = models.CharField(max_length=31, blank=True, null=True, choices=NOTE_TYPE_CHOICES, default=None)  # noqa: TID251
