import abc
import logging
from typing import Optional

from django.conf import settings
from django.contrib.contenttypes.fields import GenericF<PERSON>ign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django_deprecate_fields import deprecate_field

from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.work_units.constants import STATUS_CATEGORY_CHOICES

logger = logging.getLogger(__name__)


class WorkUnit(BaseModelV3):
    # Used to store the current status category of the model.
    # If a model needs status, then it should be mapped to a status_category.
    # TODO: (Akshay) Add default and not null constraint after pg13 update
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    status_category = models.CharField(  # noqa: TID251
        max_length=100,
        blank=True,
        null=True,
        choices=STATUS_CATEGORY_CHOICES,
        db_index=True,
    )

    owner = deprecate_field(
        models.ForeignKey(
            settings.AUTH_USER_MODEL,
            null=True,
            blank=True,
            related_name="+",
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
            default=None,
            db_index=True,
        )
    )

    owner_group = models.ForeignKey(
        "user.AssigneeGroup",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="+",
    )

    # The data type is DateTime to return the correct date on different timezones.
    due_date = models.DateTimeField(null=True, blank=True, db_index=True)

    @property
    @abc.abstractmethod
    # -> Optional[Union[Person, User]]
    def subject(self):
        """Returns the entity the workunit is associated with, for example the member the case is associated with"""
        raise NotImplementedError

    class Meta(BaseModelV3.Meta):
        abstract = True
        db_table: Optional[str] = None


# WorkUnitWithContentObject is not a "real" model
# It is an aggregate of several other models used to drive the profile To Dos tab or Worklist UX
class WorkUnitWithContentObject(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()

    # Only WorkUnit fields should be included here.
    due_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        managed = False


# Similarly, WorkUnitWithContentObjectItem is not a "real" model.
# It is an aggregate of models that are children of WorkUnitWithContentObject
# displayed on the To Dos tab or Worklist UX.
class WorkUnitWithContentObjectItem(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()

    class Meta:
        managed = False
