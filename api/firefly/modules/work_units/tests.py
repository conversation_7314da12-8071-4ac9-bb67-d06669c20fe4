import json
import urllib.parse
import uuid
from datetime import date, datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, TypedDict, Union, cast

from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core import management
from django.core.management import call_command
from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.core.assignment.constants import AssignmentScheme
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import NP_ROLE, ONBOARDING_OUTREACH_CASE_CATEGORY_UNIQUE_KEY
from firefly.core.user.factories import PersonFactory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models import User
from firefly.core.user.models.models import Assignee<PERSON><PERSON>, AssigneeGroupUser, Person
from firefly.core.user.utils import create_update_assignee_group_from_user
from firefly.modules.appointment.constants import (
    CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT,
    AppointmentReason,
    AppointmentStatus,
)
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.appointment.models import Appointment
from firefly.modules.care_plan.factories import CarePlanFactory, CarePlanTemplateFactory
from firefly.modules.care_plan.models import CarePlan
from firefly.modules.cases.constants import CaseActions, CaseStatus
from firefly.modules.cases.factories import (
    CaseCategoryFactory,
    CaseFactory,
    CaseRelationFactory,
    CaseSummaryFactory,
    TagFactory,
)
from firefly.modules.cases.models import Case, CaseCategory, CaseRelation, CaseTags, Tag
from firefly.modules.cases.utils import link_to_case_for_person
from firefly.modules.chat_message.models import ChatMessageV2, ChatThread
from firefly.modules.email.factories import EmailAttachmentFactory, EmailFactory
from firefly.modules.email.models import Email, EmailAttachment
from firefly.modules.facts.factories import PreferredLanguageFactory
from firefly.modules.firefly_django.freeze_database_time import freeze_database_time_context
from firefly.modules.forms.factories import FormFactory, FormSubmissionFactory
from firefly.modules.forms.models import FormSubmission
from firefly.modules.insurance.models import InsuranceMemberInfo
from firefly.modules.phone_calls.models import PhoneCall
from firefly.modules.pods.factories import PodFactory
from firefly.modules.schedule.factories import ScheduleIngestionJobFactory
from firefly.modules.schedule.models import ScheduleIngestionJob
from firefly.modules.statemachines.models import StateMachineDefinition
from firefly.modules.tasks.factories import TaskFactory
from firefly.modules.tasks.models import Task, TaskRelation
from firefly.modules.work_units.constants import StatusCategory
from firefly.modules.worklists.factories import WorkUnitWorklistFactory
from firefly.modules.worklists.models import Worklist

from .views import InboxWorkUnitListView, PatientTodoWorkUnitListView, work_units_queryset


class ToDoContentObjectOnlyWithId(TypedDict):
    id: int


class ToDoTaskContentObject(TypedDict):
    id: int
    completed_by: Optional[str]
    completed_on: Optional[str]
    status_category: str
    title: str
    patient: Optional[int]
    person: Optional[int]
    due_date: Optional[str]
    is_complete: bool
    priority: int
    owner_group: Optional[int]
    is_forwarding_message: Optional[bool]


class PersonMetaData(TypedDict):
    care_team: List[Any]
    elation_url: Optional[str]
    first_name: Optional[str]
    id: Optional[int]
    last_name: Optional[str]
    request_auth_for_phi: Optional[bool]
    user_id: Optional[int]


class CaseTag(TypedDict):
    display_name: str
    id: int


class CaseTagData(TypedDict):
    tag: CaseTag
    id: int


class ToDoCaseContentObject(TypedDict):
    id: int
    status: Optional[str]
    action: Optional[str]
    actions: List[Dict[str, str]]
    due_date: Optional[str]
    owner_group: Optional[int]
    relations: List[Any]
    auto_complete: bool
    status_category: Optional[str]
    category: int
    description: Optional[str]
    notes: Optional[str]
    external_description: Optional[str]
    is_proposed: Optional[bool]
    person: Optional[int]
    person_meta: PersonMetaData
    case_tags: List[CaseTagData]


class ToDoCarePlanContentObject(TypedDict):
    id: int
    status: Optional[str]
    action: Optional[str]
    actions: List[Dict[str, str]]
    status_category: Optional[str]
    notes: Optional[str]
    patient: Optional[int]
    assigned_tasks: List[Any]
    type_id: None
    cases: List["ToDo"]
    title: Optional[str]
    tasks: List[ToDoTaskContentObject]


class ToDoItem(TypedDict):
    content_type: str
    object_id: int
    content_object: ToDoTaskContentObject


class ToDo(TypedDict, total=False):
    content_type: str
    object_id: int
    content_object: Union[ToDoContentObjectOnlyWithId, ToDoCaseContentObject, ToDoCarePlanContentObject]
    due_date: Optional[str]
    items: List[ToDoItem]


class ToDoInstances(TypedDict):
    care_plans: dict
    cases: Dict[Case, List[Task]]
    form_submissions: Dict[FormSubmission, List[Task]]
    insurance_tasks: Dict[InsuranceMemberInfo, List[Task]]


class WorkUnitsTestCase(FireflyTestCase):
    def test_workunit_subject(self):
        """Test whether we can access the subject attribute of a WorkUnit subclass"""
        person = PersonUserFactory()
        category = CaseCategoryFactory()
        case = CaseFactory(
            person=person,
            category=category,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        self.assertEqual(case.subject, case.person)

    def test_work_units_queryset(self):
        # Create Cases + Tasks for 2 separate people
        person_1 = PersonUserFactory()
        person_2 = PersonUserFactory()
        task_1 = TaskFactory(patient=person_1.user)
        task_2 = TaskFactory(patient=person_2.user)
        case_1 = TaskRelation.objects.get(task=task_1).content_object
        case_2 = TaskRelation.objects.get(task=task_2).content_object
        CaseRelationFactory(case=case_1)
        CaseRelationFactory(case=case_2)
        case_content_type = ContentType.objects.get_for_model(Case)

        results = work_units_queryset(
            person_id=None,
            is_complete=False,
        )
        # Expect this to find Case objects for each Person
        self.assertEqual(len(results), 2)
        case_results = list(filter(lambda x: x["content_type_id"] == case_content_type.id, results))
        self.assertEqual(len(case_results), 2)
        # Filter for Person person_1
        results = work_units_queryset(
            person_id=person_1.id,
            is_complete=False,
        )
        case_results = list(filter(lambda x: x["content_type_id"] == case_content_type.id, results))
        self.assertEqual(len(case_results), 1)
        # Filter for Person person_1 and the Case model
        results = work_units_queryset(person_id=person_1.id, is_complete=False, content_type_model__in=["case"])
        self.assertEqual(len(results), 1)

    def test_person_todos_list(self):
        patient = self.patient
        person = Person.objects.get(user=patient)

        care_plan_wo_tasks = CarePlan.objects.create(patient=patient)  # No task, to be filtered out.
        care_plan = None
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=3)):
            care_plan = CarePlan.objects.create(patient=patient)
        care_plan_completed_task = Task.objects.create(patient=patient, person=person, is_complete=True)
        TaskRelation.objects.create(task=care_plan_completed_task, content_object=care_plan, is_parent=True)
        # Deleted incomplete task, to be filtered out.
        task = Task.objects.create(patient=patient, person=person)
        task.delete()
        task_relation = TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        task_relation.delete()

        category = CaseCategoryFactory()
        case = CaseFactory(
            person=person,
            category=category,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        # Tasks should appear in the order they were created, not just the order of their relations.
        task_1 = None
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=3)):
            task_1 = Task.objects.create(patient=patient, person=person)
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=2)):
            task_1.save()
        task_2 = Task.objects.create(patient=patient, person=person)
        TaskRelation.objects.create(task=task_2, content_object=case, is_parent=True)
        TaskRelation.objects.create(task=task_1, content_object=case, is_parent=True)

        # Add case to a care plan without any tasks.
        care_plan_case = CaseFactory(
            person=person,
            category=category,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        CaseRelation.objects.create(case=care_plan_case, content_object=care_plan_wo_tasks)

        onboarding_case_category, _ = CaseCategory.objects.get_or_create(
            unique_key=ONBOARDING_OUTREACH_CASE_CATEGORY_UNIQUE_KEY,
        )
        onboarding_case = CaseFactory(
            person=person,
            category=onboarding_case_category,
        )
        CaseRelation.objects.create(case=onboarding_case, content_object=person.user.onboarding_state)
        email_1 = EmailFactory(subject="Case Test")
        CaseRelation.objects.create(case=case, content_object=email_1)
        email_2 = EmailFactory(subject="Case Test")
        CaseRelation.objects.create(case=case, content_object=email_2)
        # Get all incomplete todos for this person.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 4)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 4)
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        self.assertEqual(todo_care_plan["content_object"]["id"], care_plan_wo_tasks.id)
        self.assertEqual(len(todo_care_plan["items"]), 0)

        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(len(todo_cases), 3)

        result = next(todo for todo in todo_cases if todo["object_id"] == case.id)
        self.assertEqual(result["due_date"], "2000-01-01T00:00:00Z")
        self.assertEqual(result["content_object"]["id"], case.id)
        self.assertEqual(result["content_object"]["notes"], case.notes)
        # email case relations
        self.assertEqual(len(result["content_object"]["relations"]), 2)
        email_relations = result["content_object"]["relations"]
        self.assertEqual(email_relations[0]["content_type"], "email")
        self.assertEqual(email_relations[0]["object_id"], email_1.id)
        self.assertEqual(email_relations[0]["content_object"]["id"], email_1.id)
        self.assertEqual(email_relations[1]["content_type"], "email")
        self.assertEqual(email_relations[1]["object_id"], email_2.id)
        self.assertEqual(email_relations[1]["content_object"]["id"], email_2.id)
        # task items
        items = result["items"]
        self.assertEqual(len(items), 2)
        self.assertEqual(items[0]["content_type"], "task")
        self.assertEqual(items[0]["object_id"], task_1.id)
        self.assertEqual(items[0]["content_object"]["id"], task_1.id)
        self.assertEqual(items[1]["content_type"], "task")
        self.assertEqual(items[1]["object_id"], task_2.id)
        self.assertEqual(items[1]["content_object"]["id"], task_2.id)

        todo_care_plan_case = todo_cases[1]
        self.assertEqual(todo_care_plan_case["object_id"], care_plan_case.id)

        # onboarding case
        result = next(todo for todo in todo_cases if todo["object_id"] == onboarding_case.pk)
        self.assertEqual(result["content_object"]["id"], onboarding_case.pk)
        items = result["items"]
        self.assertEqual(len(items), 0)

        # Complete the case that is linked to a care plan
        care_plan_case.action = CaseActions.CLOSE
        care_plan_case.save()
        # Complete the onboarding case
        onboarding_case.action = CaseActions.CLOSE
        onboarding_case.save()

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["object_id"], case.id)
        self.assertEqual(results[0]["due_date"], "2000-01-01T00:00:00Z")
        self.assertEqual(results[0]["content_object"]["id"], case.id)
        self.assertEqual(results[0]["content_object"]["notes"], case.notes)
        items = results[0]["items"]
        self.assertEqual(len(items), 2)
        self.assertEqual(items[0]["content_type"], "task")
        self.assertEqual(items[0]["object_id"], task_1.id)
        self.assertEqual(items[0]["content_object"]["id"], task_1.id)
        self.assertEqual(items[1]["content_type"], "task")
        self.assertEqual(items[1]["object_id"], task_2.id)
        self.assertEqual(items[1]["content_object"]["id"], task_2.id)

        # Add another active case to test sorted order of todo items
        second_case = None
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=5)):
            second_case = Case.objects.create(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
            )
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=1)):
            second_case.save()

        # Get todos sorted by last updated
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?sort_by=updated_at")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 2)
        # second_case updated yesterday and first case updated today
        # sort by updated at should return first case followed by second case
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)
        self.assertEqual(results[1]["content_type"], "case")
        self.assertEqual(results[1]["content_object"]["id"], second_case.id)

        # Get todos sorted by default values
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?sort_by=")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)
        self.assertEqual(results[1]["content_type"], "case")
        self.assertEqual(results[1]["content_object"]["id"], second_case.id)

        # Get only complete todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=True")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 4)
        results = body["results"]
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["content_type"], "careplan")
        self.assertEqual(results[0]["content_object"]["id"], care_plan_wo_tasks.id)
        self.assertEqual(len(results[0]["items"]), 0)
        self.assertEqual(len(results[0]["content_object"]["cases"]), 0)
        self.assertEqual(results[1]["content_type"], "case")
        self.assertEqual(results[1]["content_object"]["id"], care_plan_case.id)
        self.assertEqual(results[2]["content_type"], "case")
        self.assertEqual(results[2]["content_object"]["id"], onboarding_case.id)
        self.assertEqual(results[3]["content_type"], "careplan")
        self.assertEqual(results[3]["content_object"]["id"], care_plan.id)
        items = results[3]["items"]
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]["content_type"], "task")
        self.assertEqual(items[0]["object_id"], care_plan_completed_task.id)

        # Get only incomplete todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=False")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)
        results = body["results"]
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)

        # Get only first page of todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?limit=1&offset=0")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)

        # Get only second page of todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?limit=1&offset=1")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # Only active
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], second_case.id)

        # Index into completed care plan.
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=1&content_type=careplan&object_id={care_plan.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 4)  # Only inactive
        self.assertEqual(body["params"]["is_complete"], True)
        self.assertEqual(body["next"], None)
        self.assertEqual(
            body["previous"],
            f"http://testserver/providers/me/work_units/people/{person.id}/todos/?is_complete=True&limit=1&offset=2",
        )
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "careplan")
        self.assertEqual(results[0]["content_object"]["id"], care_plan.id)

        # Add another active case that should appear as the third item on the list.
        third_case = None
        with freeze_database_time_context(freeze_time=datetime.now(timezone.utc) - timedelta(days=4)):
            third_case = Case.objects.create(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
            )

        # Index into first item on the list, with a limit of 2 per page.
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?limit=2&content_type=case&object_id={case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 3)  # Only active
        self.assertEqual(body["params"]["is_complete"], False)
        self.assertEqual(
            body["next"],
            f"http://testserver/providers/me/work_units/people/{person.id}/todos/?is_complete=False&limit=2&offset=2",
        )
        self.assertEqual(
            body["previous"],
            None,
        )
        results = body["results"]
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)
        self.assertEqual(results[1]["content_type"], "case")
        self.assertEqual(results[1]["content_object"]["id"], third_case.id)

        # Index into second item on the list, with a limit of 2 per page.
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=2&content_type=case&object_id={third_case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 3)  # Only active
        self.assertEqual(body["params"]["is_complete"], False)
        self.assertEqual(
            body["next"],
            f"http://testserver/providers/me/work_units/people/{person.id}/todos/?is_complete=False&limit=2&offset=2",
        )
        self.assertEqual(
            body["previous"],
            None,
        )
        results = body["results"]
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case.id)
        self.assertEqual(results[1]["content_type"], "case")
        self.assertEqual(results[1]["content_object"]["id"], third_case.id)

        # Index into the third item on the list, with a limit of 2 per page.
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=2&content_type=case&object_id={second_case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 3)  # Only active
        self.assertEqual(body["params"]["is_complete"], False)
        self.assertEqual(
            body["next"],
            None,
        )
        self.assertEqual(
            body["previous"],
            f"http://testserver/providers/me/work_units/people/{person.id}/todos/?is_complete=False&limit=2",
        )
        results = body["results"]
        self.assertEqual(len(results), 1, f"Results were: {results}")
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], second_case.id)

        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?due_date__gte=2000-01-01T00:00:00-00:00&due_date__lte=2000-01-01T00:00:00-00:00"
        )
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)

    def test_person_todos_case_summary(self):
        person = self.patient.person
        case = CaseFactory(
            person=person,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )

        def get_item_in_response():
            response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")
            return [item for item in response.json()["results"] if item["content_object"]["id"] == case.id][0]

        self.assertIsNone(get_item_in_response()["content_object"]["current_summary"])
        summary = CaseSummaryFactory(case=case)
        self.assertIsNone(get_item_in_response()["content_object"]["current_summary"])
        case.current_summary = summary
        case.save()
        summary_data = get_item_in_response()["content_object"]["current_summary"]
        self.assertEqual(summary_data["id"], summary.id)
        self.assertEqual(summary_data["content"], summary.content)

    def test_updated_filter_case(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        category = CaseCategory.objects.create(title="Mystery")
        case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )
        case.updated_at = datetime.now()
        case.save()

        updated_at__lte = case.updated_at.isoformat()
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?updated_at__lte={urllib.parse.quote(updated_at__lte)}"
        )
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)

        updated_at__gte = case.updated_at + timedelta(days=1)
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?updated_at__gte={urllib.parse.quote(updated_at__gte.isoformat())}"
        )
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 0)

    def test_person_todos_list_cases(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        category = CaseCategory.objects.create(title="Mystery")
        in_progress_case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )  # No task, incomplete.
        complete_case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.CLOSED,
        )  # No task, complete.

        # Get only complete todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=True")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], complete_case.id)

        # Get only incomplete todos.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=False")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], in_progress_case.id)

        in_progress_case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )  # Another in progress case that should appear after the earlier one.
        in_progress_case.created_at = datetime.now(timezone.utc) - timedelta(days=3)
        in_progress_case.save()

        # Index into case on second page.
        response = self.provider_client.get(
            (
                f"/providers/me/work_units/people/{person.id}/todos/"
                f"?limit=1&content_type=case&object_id={in_progress_case.id}"
            )
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # Only active
        self.assertEqual(body["params"]["is_complete"], False)
        self.assertEqual(body["next"], None)
        self.assertEqual(
            body["previous"],
            f"http://testserver/providers/me/work_units/people/{person.id}/todos/?is_complete=False&limit=1",
        )
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], in_progress_case.id)

    def test_person_todos_list_due_date(self):
        patient = self.patient
        person = Person.objects.get(user=patient)

        care_plan = CarePlan.objects.create(patient=patient)
        # Completed task for provider.
        task = Task.objects.create(
            patient=patient,
            person=person,
            is_complete=True,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        # Task for patient.
        task = Task.objects.create(
            patient=patient,
            person=person,
            is_complete=False,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        # Task for provider.
        task = Task.objects.create(
            patient=patient,
            person=person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        # Task for provider, due later.
        task = Task.objects.create(
            patient=patient,
            person=person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 4, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        # Nested case, due later.
        category = CaseCategoryFactory()
        case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
            due_date=datetime(2000, 1, 5, 0, 0, 0, 0, timezone.utc),
        )
        CaseRelation.objects.create(case=case, content_object=care_plan)

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # one for the care-plan, one for the case
        results = body["results"]
        self.assertEqual(len(results), 2)  # one for the care-plan, one for the case
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_case = list(filter(lambda result: result["content_type"] == "case", body["results"]))[0]
        self.assertEqual(todo_care_plan["content_object"]["id"], care_plan.id)
        # Next action date should be due date of the earliest incomplete
        # provider-assigned task.
        self.assertEqual(todo_care_plan["due_date"], "2000-01-03T00:00:00Z")
        self.assertEqual(todo_case["content_object"]["id"], case.id)

        # Make nested case due earlier; care plan due date should update to
        # reflect this.
        case.due_date = datetime(1999, 1, 1, 0, 0, 0, 0, timezone.utc)
        case.save()

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # one for the care-plan, one for the case
        results = body["results"]
        self.assertEqual(len(results), 2)  # one for the care-plan, one for the case
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_case = list(filter(lambda result: result["content_type"] == "case", body["results"]))[0]
        self.assertEqual(todo_care_plan["content_object"]["id"], care_plan.id)
        self.assertEqual(todo_care_plan["due_date"], "1999-01-01T00:00:00Z")

    def test_case_careplan_linkage(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        category = CaseCategory.objects.create(title="Mystery")

        # Testing whether cases are shown under careplans if they have a careplan and are not standalone

        standalone_case = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )

        case_with_careplan = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )

        completed_case_with_careplan = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.CLOSED,
        )

        care_plan = CarePlan.objects.create(patient=patient)
        care_plan.created_at = datetime.now(timezone.utc) - timedelta(days=3)
        care_plan.save()
        task = Task.objects.create(patient=patient, person=person, is_complete=False)
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        CaseRelation.objects.create(
            object_id=care_plan.id,
            content_type=ContentType.objects.get_for_model(CarePlan),
            case=case_with_careplan,
        )
        CaseRelation.objects.create(
            object_id=care_plan.id,
            content_type=ContentType.objects.get_for_model(CarePlan),
            case=completed_case_with_careplan,
        )

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 3)  # one care plan; two cases
        results = body["results"]
        self.assertEqual(len(results), 3)  # one care plan; two cases
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(todo_care_plan["object_id"], care_plan.id)
        self.assertEqual(len(todo_cases), 2)
        case_ids_from_api = sorted(map(lambda todo_case: todo_case["object_id"], todo_cases))
        self.assertEqual(case_ids_from_api, sorted([standalone_case.id, case_with_careplan.id]))

        # Let's make sure we still get completed cases even if they're linked with a careplan
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=true")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], completed_case_with_careplan.id)

        # Let's make sure when we deeplink to a case that's in a careplan,
        # we actually get the careplan back
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=1&content_type=case&object_id={case_with_careplan.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "careplan")
        self.assertEqual(results[0]["content_object"]["id"], care_plan.id)

        # Let's make sure when we deeplink to a standalone case correctly
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=1&content_type=case&object_id={standalone_case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], standalone_case.id)

        # And just to be sure, let's make sure the careplan is isn't affected.
        # Let's make sure when we deeplink to a standalone case correctly
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=1&content_type=careplan&object_id={care_plan.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "careplan")
        self.assertEqual(results[0]["content_object"]["id"], care_plan.id)

    def test_case_with_appointment_case_relation(self):
        person = PersonUserFactory(
            preferred_language=PreferredLanguageFactory(name="Other"), preferred_language_other="Test Language"
        )

        provider = ProviderDetailFactory()
        physician = provider.physician
        practice = physician.practice

        category = CaseCategoryFactory(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)

        appointment_1 = AppointmentFactory(
            patient_id=person.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=10),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointment_2 = AppointmentFactory(
            patient_id=person.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=15),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        active_case = Case.objects.create(
            person=person, category=category, description=person.preferred_language_other, status=CaseStatus.IN_PROGRESS
        )
        CaseRelation.objects.create(
            case=active_case, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment_1.id
        )

        completed_case = Case.objects.create(
            person=person, category=category, description=person.preferred_language_other, status=CaseStatus.CLOSED
        )

        CaseRelation.objects.create(
            case=completed_case, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment_2.id
        )

        # Test fetching Case with Appointment Case Relation
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        result = next(
            item for item in results if item["content_type"] == "case" and item["object_id"] == active_case.id
        )
        self.assertEqual(result["content_object"]["relations"][0]["content_object"]["id"], appointment_1.id)

        # Test fetching single case by id
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/"
            + f"?limit=50&content_type=case&object_id={active_case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        result = next(
            item for item in results if item["content_type"] == "case" and item["object_id"] == active_case.id
        )
        self.assertEqual(result["content_type"], "case")
        self.assertEqual(result["object_id"], active_case.id)
        self.assertEqual(result["content_object"]["id"], active_case.id)
        self.assertEqual(result["content_object"]["relations"][0]["content_object"]["id"], appointment_1.id)

        # Test completed case
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=true")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        result = next(
            result
            for result in results
            if result["object_id"] == completed_case.id and result["content_type"] == "case"
        )
        self.assertEqual(result["content_object"]["id"], completed_case.id)
        self.assertEqual(result["content_object"]["relations"][0]["content_object"]["id"], appointment_2.id)

    def test_case_with_ingestion_job_relation(self):
        person = PersonUserFactory()
        category = CaseCategoryFactory()
        ingestion_job = ScheduleIngestionJobFactory.create()
        case = Case.objects.create(person=person, category=category, status=CaseStatus.IN_PROGRESS)
        CaseRelation.objects.create(
            case=case, content_type=ContentType.objects.get_for_model(ScheduleIngestionJob), object_id=ingestion_job.id
        )

        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        result = next(item for item in results if item["content_type"] == "case" and item["object_id"] == case.id)
        self.assertEqual(result["content_object"]["relations"][0]["content_object"]["id"], ingestion_job.id)

        # Test fetching single case by id
        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/" + f"?limit=50&content_type=case&object_id={case.id}"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        result = next(item for item in results if item["content_type"] == "case" and item["object_id"] == case.id)
        self.assertEqual(result["content_type"], "case")
        self.assertEqual(result["object_id"], case.id)
        self.assertEqual(result["content_object"]["id"], case.id)
        self.assertEqual(result["content_object"]["relations"][0]["content_object"]["id"], ingestion_job.id)

    def test_careplan_completion(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        category = CaseCategory.objects.create(title="Mystery")

        case_with_careplan = Case.objects.create(
            person=person,
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )

        care_plan = CarePlan.objects.create(patient=patient)
        care_plan.created_at = datetime.now(timezone.utc) - timedelta(days=3)
        care_plan.save()
        task = Task.objects.create(patient=patient, person=person, is_complete=False)
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        CaseRelation.objects.create(
            object_id=care_plan.id,
            content_type=ContentType.objects.get_for_model(CarePlan),
            case=case_with_careplan,
        )

        # Get incomplete careplan
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # one for care plan ; one for case
        results = body["results"]
        self.assertEqual(len(results), 2)  # one for care plan ; one for case
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(todo_care_plan["object_id"], care_plan.id)
        self.assertEqual(len(todo_cases), 1)
        todo_case = todo_cases[0]
        self.assertEqual(todo_case["object_id"], case_with_careplan.id)

        task.is_complete = True
        task.save()

        # Careplan should still be incomplete even after all associated tasks are marked completed since the case is
        # incomplete
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 2)  # one for care plan ; one for case
        results = body["results"]
        self.assertEqual(len(results), 2)  # one for care plan ; one for case
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(todo_care_plan["object_id"], care_plan.id)
        self.assertEqual(len(todo_cases), 1)
        todo_case = todo_cases[0]
        self.assertEqual(todo_case["object_id"], case_with_careplan.id)

        case_with_careplan.status = CaseStatus.CLOSED
        case_with_careplan.save()

        # Let's make sure the careplan properly got completed.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=true")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 2)  # one for care plan ; one for case
        todo_care_plan = list(filter(lambda result: result["content_type"] == "careplan", body["results"]))[0]
        todo_cases = list(filter(lambda result: result["content_type"] == "case", body["results"]))
        self.assertEqual(todo_care_plan["object_id"], care_plan.id)
        self.assertEqual(len(todo_cases), 1)
        todo_case = todo_cases[0]
        self.assertEqual(todo_case["object_id"], case_with_careplan.id)

        task.is_complete = False
        task.save()

        # Let's make sure what happens when we bring back a task. Careplan should now be incomplete.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "careplan")
        self.assertEqual(results[0]["object_id"], care_plan.id)
        self.assertEqual(len(results[0]["content_object"]["cases"]), 0)

        # But the case should still be marked complete.
        response = self.provider_client.get(f"/providers/me/work_units/people/{person.id}/todos/?is_complete=true")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], case_with_careplan.id)

    def run_test_for_todos(
        self,
        patient,
        # While the expected number of queries is calculated
        # having an explicit parameter helps us document
        # the number of queries that are expected to run
        expected_number_of_queries_in_todos_api: int,
        number_of_care_plans: int = 0,
        number_of_tasks_per_care_plan: int = 0,
        number_of_cases_per_care_plan: int = 0,
        number_of_chat_messages_per_care_plan_case: int = 0,
        number_of_phone_calls_per_care_plan_case: int = 0,
        number_of_tasks_per_care_plan_case: int = 0,
        number_of_cases: int = 0,
        number_of_chat_messages_per_case: int = 0,
        number_of_phone_calls_per_case: int = 0,
        number_of_tasks_per_case: int = 0,
        number_of_email_per_case: int = 0,
        number_of_insurance_tasks: int = 0,
    ):
        person = Person.objects.get(user=patient)
        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )
        category = CaseCategory.objects.create(title="Mystery")
        to_do_instances: ToDoInstances = {
            "care_plans": {},
            "cases": {},
            "form_submissions": {},
            "insurance_tasks": {},
        }

        tag_1: Tag = TagFactory.create()
        tag_2: Tag = TagFactory.create()
        tag_3: Tag = TagFactory.create()
        for _ in range(number_of_care_plans):
            care_plan = CarePlan.objects.create(patient=patient)
            to_do_instances["care_plans"][care_plan] = []
            for _ in range(number_of_tasks_per_care_plan):
                task = Task.objects.create(patient=patient, person=person, is_forwarding_message=False)
                to_do_instances["care_plans"][care_plan].append(task)
                TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
            for _ in range(number_of_cases_per_care_plan):
                case = Case.objects.create(
                    person=person,
                    category=category,
                    status=CaseStatus.IN_PROGRESS,
                )
                case.tags.add(tag_1)
                case.tags.add(tag_2)
                case.tags.add(tag_3)
                case.tags.remove(tag_2)
                to_do_instances["cases"][case] = []
                CaseRelation.objects.create(case=case, content_object=care_plan)
                for _ in range(number_of_chat_messages_per_care_plan_case):
                    chat_message = ChatMessageV2.objects.create(
                        thread=thread,
                        sender=self.patient,
                        text="Hi, I would like some help",
                        sent_at=datetime(2020, 6, 2),
                        uid=str(uuid.uuid4()),
                    )
                    CaseRelation.objects.create(case=case, content_object=chat_message)
                for _ in range(number_of_phone_calls_per_care_plan_case):
                    phone_call = PhoneCall.objects.create(
                        user=self.patient, direction="INBOUND", called_at=datetime.now()
                    )
                    CaseRelation.objects.create(case=case, content_object=phone_call)
                for _ in range(number_of_tasks_per_care_plan_case):
                    task = Task.objects.create(patient=patient, person=person)
                    TaskRelation.objects.create(task=task, content_object=case, is_parent=True)
                    to_do_instances["cases"][case].append(task)

        for _ in range(number_of_cases):
            case = Case.objects.create(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
            )
            case.tags.add(tag_1)
            case.tags.add(tag_2)
            case.tags.add(tag_3)
            # remove a tag from the case
            case.tags.remove(tag_2)
            # nuke a tag
            tag_1.delete()
            to_do_instances["cases"][case] = []
            for _ in range(number_of_chat_messages_per_case):
                chat_message = ChatMessageV2.objects.create(
                    thread=thread,
                    sender=self.patient,
                    text="Hi, I would like some help",
                    sent_at=datetime(2020, 6, 2),
                    uid=str(uuid.uuid4()),
                )
                CaseRelation.objects.create(case=case, content_object=chat_message)
            for _ in range(number_of_phone_calls_per_case):
                phone_call = PhoneCall.objects.create(user=self.patient, direction="INBOUND", called_at=datetime.now())
                CaseRelation.objects.create(case=case, content_object=phone_call)
            for _ in range(number_of_tasks_per_case):
                task = Task.objects.create(patient=patient, person=person, is_forwarding_message=False)
                TaskRelation.objects.create(task=task, content_object=case, is_parent=True)
                to_do_instances["cases"][case].append(task)

            for _ in range(number_of_email_per_case):
                email = EmailFactory()
                # each email can have more than 1 attachments
                for _ in range(number_of_email_per_case):
                    EmailAttachment.objects.create(email=email, file_key="")
                CaseRelation.objects.create(case=case, content_object=email)

        assert person.insurance_info is not None
        insurance: InsuranceMemberInfo = person.insurance_info
        to_do_instances["insurance_tasks"][insurance] = []
        for _ in range(number_of_insurance_tasks):
            task = Task.objects.create(
                patient=self.patient,
                title="Insurance task",
                owner_group=self.patient.assignee_group,
            )
            to_do_instances["insurance_tasks"][insurance].append(task)
            TaskRelation.objects.create(task=task, content_object=insurance, is_parent=True)

        # Normally, we'd use self.provider_client.get(...) to test this, going through the client
        # issues some additional queries unrelated to data fetching. To isolate just the queries we
        # care about, we access the view directly.
        view = PatientTodoWorkUnitListView.as_view()
        factory = APIRequestFactory()
        request = factory.get("/providers/me/work_units/people/{person.id}/todos/", format="json")
        force_authenticate(request, user=self.provider)

        # Ensure cache is cleared for test isolation.
        ContentType.objects.clear_cache()

        # Here's how the queries add up for one care plan, one case...:
        #    1:  to warm up the content types cache
        #    2:  to count the total number of todos
        #    3:  to fetch all todos
        #   =====queries for careplans
        #    4:    to prefetch all care plan details
        #    5:    to prefetch task relation and tasks for above care plans
        #    6:    to prefetch cases for the above careplans
        #    7:        to fetch case details for each care plan
        #    8:        to fetch case relations for above cases for each care plan
        #    9:        to fetch care plan details for each care plan
        #   10:    to fetch chat message for cases for all case plans
        #   11:    to fetch phone calls for cases for all case plans
        #   12:        to fetch person details for each care plan
        #   13:        to fetch care team details for each care plan
        #   14:        to fetch task relations for care plans for each care plan
        #   15:        to fetch task relations for cases for each care plan
        #   =====queries for cases
        #   16:     to fetch case datails for all cases
        #   17:     to fetch case relations for above cases
        #   18:     to fetch chat messages for above cases
        #   19:     to fetch phone calls for above cases
        #   20:     to fetch email for above cases
        #   21:     to fetch care plan details
        #   22:     to fetch person details
        #   23:     to fetch care team details
        #   =====queries for forms
        #   24:    to prefetch all form submission todos
        #   25:    to prefetch task relations and tasks for the above form submissions
        #   =====queries for insurance tasks
        #   26:    to prefetch all insurance member info todos
        #   27:    to prefetch task relations and tasks for the above insurance member info
        #   =====queries for worklists
        #   28:    to prefetch all worklist todos
        #   29:    to prefetch all worklist items for the above worklists
        # ----
        does_atleast_one_todo_item_exist: bool = False
        number_of_queries_for_care_plan: int = 0
        if number_of_care_plans > 0 and number_of_tasks_per_care_plan > 0:
            does_atleast_one_todo_item_exist = True
            # one to fetch all care plan details
            # one to fetch all taskrelations, tasks for careplan
            # one to fetch assignee group
            number_of_constant_queries_for_careplans: int = 3
            # to fetch to get task relation and tasks for case workunit of each careplan
            number_of_queries_to_fetch_tasks_for_care_plans: int = number_of_care_plans
            number_of_queries_to_fetch_cases_for_care_plans: int = 1
            number_of_queries_to_fetch_phone_calls_for_care_plans: int = 0
            number_of_queries_to_fetch_chat_messages_for_care_plans: int = 0
            number_of_queries_for_care_plan = (
                number_of_constant_queries_for_careplans
                + number_of_queries_to_fetch_tasks_for_care_plans
                + number_of_queries_to_fetch_cases_for_care_plans
                + number_of_queries_to_fetch_chat_messages_for_care_plans
                + number_of_queries_to_fetch_phone_calls_for_care_plans
            )

        number_of_queries_for_cases: int = 0
        if (number_of_cases > 0) or (number_of_cases_per_care_plan > 0):
            does_atleast_one_todo_item_exist = True
            #   to fetch case details for all cases
            #   to fetch case relations for above cases
            #   to fetch task details
            #   to fetch person details
            #   to fetch care team details
            #   to fetch case tag details
            number_of_queries_for_cases = 6
            if (number_of_chat_messages_per_case > 0) or (number_of_chat_messages_per_care_plan_case > 0):
                #   to fetch chat messages for above cases
                number_of_queries_for_cases = number_of_queries_for_cases + 1
            if (number_of_phone_calls_per_case > 0) or (number_of_phone_calls_per_care_plan_case > 0):
                #   to fetch phone calls for above cases
                number_of_queries_for_cases = number_of_queries_for_cases + 1
            if number_of_email_per_case > 0:
                #   to fetch email calls for above cases
                number_of_queries_for_cases = number_of_queries_for_cases + 1
                #   to fetch email attachment for above cases
                number_of_queries_for_cases = number_of_queries_for_cases + 1

        number_of_queries_for_insurance_tasks: int = 0
        if number_of_insurance_tasks > 0:
            does_atleast_one_todo_item_exist = True
            # to prefetch all insurance member info todos
            # to prefetch task relations and tasks for the above insurance member info
            number_of_queries_for_insurance_tasks = 2

        # 1 to warm up the content types cache
        # 1 to count the total number of todos
        number_of_static_queries_for_api: int = 2
        # 1 for content_type
        if number_of_care_plans > 0 or number_of_cases > 0:
            number_of_static_queries_for_api = number_of_static_queries_for_api + 1
        if does_atleast_one_todo_item_exist is True:
            # to fetch all todos (only run if a todo item exists)
            number_of_static_queries_for_api = number_of_static_queries_for_api + 1

        expected_number_of_queries: int = (
            number_of_static_queries_for_api
            + number_of_queries_for_care_plan
            + number_of_queries_for_cases
            + number_of_queries_for_insurance_tasks
        )
        results: List[Any] = []
        self.assertEqual(
            expected_number_of_queries_in_todos_api,
            expected_number_of_queries,
            (
                f"Expected # of queries in ToDos API {expected_number_of_queries_in_todos_api} !="
                f" expected_number_of_queries {expected_number_of_queries}"
            ),
        )
        with self.assertNumQueries(expected_number_of_queries):
            response = view(request, person_id=person.id)
            response.render()

            self.assertEqual(response.status_code, 200)
            body = response.data
            expected_number_of_results: int = number_of_care_plans + number_of_cases
            if number_of_insurance_tasks > 0:
                # a person can have only one insurance task
                expected_number_of_results = expected_number_of_results + 1
            if number_of_cases_per_care_plan > 0 and number_of_care_plans > 0:
                expected_number_of_results = expected_number_of_results + (
                    number_of_cases_per_care_plan * number_of_care_plans
                )
            self.assertEqual(body["count"], expected_number_of_results)
            results = body["results"]
            self.assertEqual(len(results), expected_number_of_results)
        return {
            "results": results,
            "to_do_instances": to_do_instances,
        }

    def test_person_todos_list_queries(self):
        self.run_test_for_todos(
            patient=self.patient,
            number_of_care_plans=3,
            number_of_tasks_per_care_plan=3,
            number_of_cases_per_care_plan=3,
            number_of_chat_messages_per_care_plan_case=3,
            number_of_phone_calls_per_care_plan_case=3,
            number_of_tasks_per_care_plan_case=3,
            number_of_cases=3,
            number_of_chat_messages_per_case=3,
            number_of_phone_calls_per_case=3,
            number_of_tasks_per_case=3,
            number_of_insurance_tasks=3,
            number_of_email_per_case=10,
            expected_number_of_queries_in_todos_api=23,
        )

    def remove_audit_fields_from_item(self, item):
        audit_fields: List[str] = [
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
        ]
        for audit_field in audit_fields:
            item.pop(audit_field, None)
        return item

    def remove_keys_not_under_test_from_work_unit_response(self, response):
        response["content_object"] = self.remove_audit_fields_from_item(response["content_object"])
        response["content_object"].pop("states_with_categories", None)
        response["content_object"].pop("current_summary", None)
        if response["content_object"].get("cases") is not None:
            for case in response["content_object"].get("cases", []):
                case = self.remove_keys_not_under_test_from_work_unit_response(response=case)
        for case_tag in response["content_object"].get("case_tags", []):
            case_tag = self.remove_audit_fields_from_item(item=case_tag)
            case_tag["tag"] = self.remove_audit_fields_from_item(item=case_tag["tag"])

        if response["content_object"].get("person_meta") is not None:
            response["content_object"]["person_meta"] = self.remove_audit_fields_from_item(
                response["content_object"]["person_meta"]
            )
        if response["content_object"].get("relations") is not None:
            for relation in response["content_object"].get("relations", []):
                relation = self.remove_audit_fields_from_item(relation)
                relation["content_object"] = self.remove_audit_fields_from_item(relation["content_object"])
                relation["content_object"].pop("title", None)
                if relation.get("content_object", {}).get("attachments"):
                    attachments = []
                    for attachment in relation["content_object"]["attachments"]:
                        attachments.append(self.remove_audit_fields_from_item(attachment))
                    relation["content_object"]["attachments"] = attachments
        for item in response.get("items", []):
            item["content_object"] = self.remove_audit_fields_from_item(item["content_object"])
            item["content_object"].pop("source_type", None)
            item["content_object"].pop("collection_id", None)
            item["content_object"].pop("collection_order", None)
            item["content_object"].pop("metadata", None)
            item["content_object"].pop("template", None)
        for task in response["content_object"].get("tasks", []):
            task = self.remove_audit_fields_from_item(task)
            task.pop("source_type", None)
            task.pop("collection_id", None)
            task.pop("collection_order", None)
            task.pop("metadata", None)
            task.pop("template", None)
        return response

    def convert_task_to_workunit_response(self, task: Task) -> ToDoItem:
        patient: Optional[User] = task.patient
        person: Optional[Person] = task.person
        owner_group: Optional[AssigneeGroup] = task.owner_group
        patient_id: Optional[int] = None
        if patient is not None:
            patient_id = patient.pk
        person_id: Optional[int] = None
        if person is not None:
            person_id = person.pk
        owner_group_id: Optional[int] = None
        if owner_group is not None:
            owner_group_id = owner_group.pk
        task_to_do_item: ToDoItem = {
            "content_type": "task",
            "object_id": task.pk,
            "content_object": {
                "id": task.pk,
                "completed_by": None,
                "completed_on": None,
                "status_category": "not_started",
                "title": task.title,
                "patient": patient_id,
                "person": person_id,
                "due_date": None,
                "is_complete": False,
                "priority": 0,
                "owner_group": owner_group_id,
                "is_forwarding_message": False,
            },
        }
        return task_to_do_item

    def convert_case_to_todo_content_object(self, case: Case) -> ToDoCaseContentObject:
        content_object: ToDoCaseContentObject
        person: Optional[Person] = case.person
        owner_group: Optional[AssigneeGroup] = case.owner_group
        patient_id: Optional[int] = None
        person_first_name: Optional[str] = None
        person_last_name: Optional[str] = None
        elation_url: Optional[str] = None
        request_auth_for_phi: Optional[bool] = None
        person_id: Optional[int] = None
        if person is not None:
            person_id = person.pk
            person_first_name = person.first_name
            person_last_name = person.last_name
            request_auth_for_phi = person.request_auth_for_phi
            elation_url = person.elation_url
            patient: Optional[User] = person.user
            if patient is not None:
                patient_id = patient.pk
        owner_group_id: Optional[int] = None
        if owner_group is not None:
            owner_group_id = owner_group.pk
        person_meta_data: PersonMetaData = {
            "care_team": [],
            "elation_url": elation_url,
            "first_name": person_first_name,
            "id": person_id,
            "last_name": person_last_name,
            "request_auth_for_phi": request_auth_for_phi,
            "user_id": patient_id,
        }
        content_object = {
            "id": int(case.pk),
            "status": case.status,
            "actions": [{"trigger": "close_case", "source": "in_progress", "dest": "closed"}],
            "action": None,
            "relations": [],
            "auto_complete": case.auto_complete,
            "status_category": case.status_category,
            "owner_group": owner_group_id,
            "person": person_id,
            "category": case.category.pk,
            "description": case.description,
            "notes": case.notes,
            "external_description": case.external_description,
            "due_date": None,
            "person_meta": person_meta_data,
            "is_proposed": case.is_proposed,
            "case_tags": [],
        }
        case_tag: CaseTags
        for case_tag in case.case_tags.iterator():
            if case_tag.deleted is None and case_tag.tag.deleted is None:
                tag: CaseTag = {
                    "display_name": case_tag.tag.display_name,
                    "id": case_tag.tag.id,
                }
                case_tag_data: CaseTagData = {"tag": tag, "id": case_tag.pk}
                content_object["case_tags"].append(case_tag_data)
        relation: CaseRelation
        for relation in case.relations.iterator():
            content_type_for_relation: Optional[str] = None
            if relation.content_type == ContentType.objects.get_for_model(ChatMessageV2):
                content_type_for_relation = "chatmessagev2"
            elif relation.content_type == ContentType.objects.get_for_model(PhoneCall):
                content_type_for_relation = "phonecall"
            elif relation.content_type == ContentType.objects.get_for_model(CarePlan):
                content_type_for_relation = "careplan"
            elif relation.content_type == ContentType.objects.get_for_model(Email):
                content_type_for_relation = "email"
            if content_type_for_relation == "email":
                email_attachments = []
                attachment: EmailAttachment
                if relation.content_object:
                    email: Email = relation.content_object
                    for attachment in email.attachments.all():
                        if attachment:
                            email_attachments.append(
                                {
                                    "attachment_id": attachment.attachment_id,
                                    "file_name": attachment.file_name,
                                    "id": attachment.id,
                                    "error": attachment.error,
                                    "status": attachment.status,
                                }
                            )
                    content_object["relations"].append(
                        {
                            "content_type": content_type_for_relation,
                            "object_id": relation.object_id,
                            "content_object": {
                                "email_sent_at": email.email_sent_at.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                                if email.email_sent_at
                                else None,
                                "from_address": email.from_address,
                                "id": email.id,
                                "message": email.message,
                                "sender": email.from_address,
                                "snippet": email.snippet,
                                "status": email.status,
                                "subject": email.subject,
                                "to_address": email.to_address,
                                "attachments": email_attachments,
                            },
                            "id": relation.pk,
                        }
                    )
            elif content_type_for_relation is not None:
                content_object["relations"].append(
                    {
                        "content_type": content_type_for_relation,
                        "object_id": relation.object_id,
                        "content_object": {"id": relation.object_id},
                        "id": relation.pk,
                    }
                )
        return content_object

    def convert_data_to_work_unit_response(
        self,
        workunit_instances: Dict[
            Union[
                InsuranceMemberInfo,
                FormSubmission,
                Worklist,
                CarePlan,
                Case,
            ],
            List[Task],
        ],
        content_type: str,
    ) -> List[ToDo]:
        workunits: List[ToDo] = []
        for work_unit, tasks in workunit_instances.items():
            workunit_todo: ToDo = {
                "content_type": content_type,
                "object_id": int(work_unit.pk),
                "content_object": {
                    "id": int(work_unit.pk),
                },
                "due_date": None,
                "items": [],
            }
            task: Task
            for task in tasks:
                if isinstance(task, Task):
                    workunit_todo["items"].append(self.convert_task_to_workunit_response(task))
            if (
                isinstance(work_unit, InsuranceMemberInfo)
                or isinstance(work_unit, FormSubmission)
                or isinstance(work_unit, Worklist)
            ):
                workunit_todo["content_object"] = {
                    "id": int(work_unit.pk),
                }
            elif isinstance(work_unit, Case):
                workunit_todo["content_object"] = self.convert_case_to_todo_content_object(case=work_unit)
            elif isinstance(work_unit, CarePlan):
                patient = work_unit.patient
                patient_id = None
                if patient is not None:
                    patient_id = patient.pk
                to_do_content_object: ToDoCarePlanContentObject = {
                    "id": int(work_unit.pk),
                    "status": work_unit.status,
                    "actions": [{"trigger": "inactive", "source": "active", "dest": "inactive"}],
                    "action": None,
                    "assigned_tasks": [],
                    "type_id": None,
                    "cases": [],
                    "title": work_unit.title,
                    "patient": patient_id,
                    "status_category": work_unit.status_category,
                    "notes": work_unit.notes,
                    "tasks": [],
                }
                for item in workunit_todo["items"]:
                    to_do_content_object["tasks"].append(cast(ToDoTaskContentObject, item["content_object"]))
                workunit_todo["content_object"] = to_do_content_object
            workunits.append(workunit_todo)
        return sorted(workunits, key=lambda work_unit: work_unit["object_id"])

    def test_person_todos_list_queries_insurance_task_response(self):
        test_data = self.run_test_for_todos(
            patient=self.patient,
            number_of_insurance_tasks=2,
            # constant number of queries irrespective of number of tasks
            expected_number_of_queries_in_todos_api=5,
        )
        to_do_instances: ToDoInstances = test_data["to_do_instances"]
        results = test_data["results"]
        self.assertEqual(len(results), 1)
        sanitized_results: List[Any] = []
        for result in results:
            sanitized_results.append(self.remove_keys_not_under_test_from_work_unit_response(result))
        expected_response: ToDo = self.convert_data_to_work_unit_response(
            workunit_instances=to_do_instances["insurance_tasks"],
            content_type="insurancememberinfo",
        )
        self.assertEqual(expected_response, sanitized_results)

    def test_person_todos_list_queries_case_workunits_response(self):
        number_of_cases = 1
        test_data = self.run_test_for_todos(
            patient=self.patient,
            number_of_cases=number_of_cases,
            number_of_tasks_per_case=1,
            number_of_chat_messages_per_case=1,
            number_of_phone_calls_per_case=1,
            number_of_email_per_case=4,
            # constant number of queries irrespective of number of cases
            expected_number_of_queries_in_todos_api=14,
        )
        to_do_instances: ToDoInstances = test_data["to_do_instances"]
        results: List[Any] = test_data["results"]
        self.assertEqual(len(results), number_of_cases)
        sanitized_results: List[Any] = []
        for result in results:
            sanitized_results.append(self.remove_keys_not_under_test_from_work_unit_response(result))
        expected_response: List[ToDo] = self.convert_data_to_work_unit_response(
            workunit_instances=to_do_instances["cases"],
            content_type="case",
        )
        self.assertEqual(expected_response, sanitized_results)

    def test_person_todos_list_queries_care_plan_workunits_response(self):
        number_of_care_plans = 1
        number_of_cases_per_care_plan = 1
        number_of_cases: int = 1
        test_data = self.run_test_for_todos(
            patient=self.patient,
            number_of_care_plans=number_of_care_plans,
            number_of_tasks_per_care_plan=1,
            number_of_cases_per_care_plan=number_of_cases_per_care_plan,
            number_of_chat_messages_per_care_plan_case=1,
            number_of_phone_calls_per_care_plan_case=1,
            number_of_tasks_per_care_plan_case=1,
            number_of_cases=number_of_cases,
            number_of_email_per_case=4,
            # increases with the number of relations of a careplan
            # but at a lesser rate than before
            expected_number_of_queries_in_todos_api=19,
        )
        to_do_instances: ToDoInstances = test_data["to_do_instances"]
        results: List[Any] = test_data["results"]
        self.assertEqual(
            len(results),
            number_of_care_plans + (number_of_cases_per_care_plan * number_of_care_plans) + number_of_cases,
        )
        sanitized_results: List[Any] = []
        for result in results:
            sanitized_results.append(self.remove_keys_not_under_test_from_work_unit_response(result))
        sanitized_care_plan_results = list(
            filter(lambda sanitized_result: sanitized_result.get("content_type") == "careplan", sanitized_results)
        )
        self.assertEqual(len(sanitized_care_plan_results), number_of_care_plans)
        expected_care_plan_response: List[ToDo] = self.convert_data_to_work_unit_response(
            workunit_instances=to_do_instances["care_plans"],
            content_type="careplan",
        )
        sanitized_case_results = list(
            filter(lambda sanitized_result: sanitized_result.get("content_type") == "case", sanitized_results)
        )
        sanitized_case_results = sorted(sanitized_case_results, key=lambda result: result["object_id"])
        expected_cases_response: List[ToDo] = self.convert_data_to_work_unit_response(
            workunit_instances=to_do_instances["cases"],
            content_type="case",
        )
        self.assertEqual(
            len(sanitized_case_results), ((number_of_cases_per_care_plan * number_of_care_plans) + number_of_cases)
        )
        self.assertEqual(expected_care_plan_response, sanitized_care_plan_results)
        self.assertEqual(expected_cases_response, sanitized_case_results)

    def test_person_todos_list_zone_filter(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        clinical_category = CaseCategory.objects.create(title="Clinical Category", zone="Clinical")
        member_category = CaseCategory.objects.create(title="Member Category", zone="Member")
        clinical_case = Case.objects.create(
            person=person,
            category=clinical_category,
            status=CaseStatus.IN_PROGRESS,
        )
        member_case = Case.objects.create(
            person=person,
            category=member_category,
            status=CaseStatus.IN_PROGRESS,
        )

        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?case__category__zone__in=Clinical"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], clinical_case.id)

        response = self.provider_client.get(
            f"/providers/me/work_units/people/{person.id}/todos/?case__category__zone__in=Member,Network"
        )

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["content_object"]["id"], member_case.id)


class WorkUnitAPITestCase(FireflyTestCase):
    def test_api(self):
        state_machine_definition = StateMachineDefinition.objects.create(
            title="Test",
            content={
                "state_with_categories": [
                    {"state": {"name": "in progress"}, "category": StatusCategory.IN_PROGRESS},
                    {"state": {"name": "done"}, "category": StatusCategory.COMPLETE},
                ],
                "initial_state": "in progress",
                "transitions": [{"trigger": "finish", "source": "in progress", "dest": "done"}],
            },
        )
        category = CaseCategoryFactory(state_machine_definition=state_machine_definition)
        case = CaseFactory(person=self.patient.person, category=category)

        response = self.provider_client.get(f"/providers/me/work_units/{case.id}/case/")
        self.assertEqual(response.status_code, 200)
        response_json = response.json()
        self.assertEqual(response_json["content_type"], "case")
        self.assertEqual(response_json["object_id"], case.id)
        self.assertEqual(response_json["content_object"]["category"], category.id)
        self.assertIsNone(response_json["due_date"])
        self.assertIsNone(response_json["owner_group"])
        self.assertEqual(response_json["content_object"]["status"], "in progress")
        self.assertEqual(response_json["content_object"]["status_category"], StatusCategory.IN_PROGRESS)
        create_update_assignee_group_from_user(self.provider)
        response = self.provider_client.patch(
            f"/providers/me/work_units/{case.id}/case/",
            {
                "due_date": "2020-11-11T00:00:00Z",
                "owner_group": self.provider.assignee_group.id,
                "action": "finish",
            },
            format="json",
        )
        self.assertEqual(response.status_code, 200)

        response = self.provider_client.get(f"/providers/me/work_units/{case.id}/case/")
        self.assertEqual(response.status_code, 200)
        response_json = response.json()
        self.assertEqual(response_json["due_date"], "2020-11-11T00:00:00Z")
        self.assertEqual(response_json["owner_group"], self.provider.assignee_group.id)
        self.assertEqual(response_json["content_object"]["status"], "done")
        self.assertEqual(response_json["content_object"]["status_category"], StatusCategory.COMPLETE)

        # Check if the updated due_date is returned in the case end point
        response = self.provider_client.get(f"/providers/me/cases/{case.id}")
        case_from_response = response.json()
        self.assertEqual(case_from_response["due_date"], "2020-11-11T00:00:00Z")
        self.assertEqual(case_from_response["owner_group"], self.provider.assignee_group.id)
        self.assertEqual(case_from_response["status"], "done")
        self.assertEqual(case_from_response["status_category"], StatusCategory.COMPLETE)

        current_case = Case.objects.get(id=case.id)
        current_case.due_date = "2020-11-14T00:00:00Z"
        current_case.save()
        response = self.provider_client.get(f"/providers/me/cases/{case.id}")
        case_from_response = response.json()
        self.assertEqual(case_from_response["due_date"], "2020-11-14T00:00:00Z")
        self.assertEqual(case_from_response["owner_group"], self.provider.assignee_group.id)
        self.assertEqual(case_from_response["status"], "done")
        self.assertEqual(case_from_response["status_category"], StatusCategory.COMPLETE)

        # check if case still has owner group if assignee_group is removed from user
        assignee_group = self.provider.assignee_group
        self.provider.assignee_group = None
        self.provider.save()
        current_case = Case.objects.get(id=case.id)
        current_case.due_date = "2020-11-13T00:00:00Z"
        current_case.save()
        response = self.provider_client.get(f"/providers/me/cases/{case.id}")
        case_from_response = response.json()
        self.assertEqual(case_from_response["due_date"], "2020-11-13T00:00:00Z")
        self.assertEqual(case_from_response["owner_group"], assignee_group.id)
        self.assertEqual(case_from_response["status"], "done")
        self.assertEqual(case_from_response["status_category"], StatusCategory.COMPLETE)

    def test_api_with_appointment_case_relation(self):
        person = PersonUserFactory(
            preferred_language=PreferredLanguageFactory(name="Other"), preferred_language_other="Test Language"
        )

        provider = ProviderDetailFactory()
        physician = provider.physician
        practice = physician.practice

        category = CaseCategoryFactory(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)

        appointment = AppointmentFactory(
            patient_id=person.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=10),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        case = Case.objects.create(
            person=person, category=category, description=person.preferred_language_other, status=CaseStatus.IN_PROGRESS
        )
        CaseRelation.objects.create(
            case=case, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment.id
        )

        response = self.provider_client.get(f"/providers/me/work_units/{case.id}/case/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        response_json = response.json()
        self.assertEqual(response_json["content_type"], "case")
        self.assertEqual(response_json["object_id"], case.id)
        self.assertEqual(response_json["content_object"]["category"], category.id)
        self.assertEqual(response_json["content_object"]["description"], person.preferred_language_other)
        self.assertEqual(response_json["content_object"]["relations"][0]["content_object"]["id"], appointment.id)


class WorkUnitWorklistTestCase(FireflyTestCase):
    def test_work_unit_worklist_view(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case = CaseFactory(category=category)
        # but not find this one
        other_case = CaseFactory()
        self.assertNotEqual(case.category_id, other_case.category_id)

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={case.category.id}"})
        response_json = self.provider_client.get(f"/providers/me/work_units/worklists/{worklist.id}/").json()

        self.assertEqual(response_json["count"], 1)
        item_in_response = next(filter(lambda item: item["object_id"] == case.id, response_json["results"]), None)
        self.assertEqual(item_in_response["owner_group"], case.owner_group_id)
        self.assertEqual(item_in_response["due_date"], case.due_date)
        self.assertEqual(item_in_response["status_category"], case.status_category)
        self.assertEqual(item_in_response["person_user"], case.person.user_id)
        self.assertEqual(item_in_response["content_type"], "case")
        self.assertEqual(item_in_response["content_object"]["id"], case.id)
        self.assertEqual(item_in_response["case_url"], link_to_case_for_person(case.id))

        # create an another case that has care plan as its parent
        case_with_careplan = CaseFactory(
            category=category,
            status=CaseStatus.IN_PROGRESS,
        )

        care_plan = CarePlan.objects.create(patient=self.patient)
        care_plan.created_at = datetime.now(timezone.utc) - timedelta(days=3)
        care_plan.save()
        task = Task.objects.create(patient=self.patient, person=self.patient.person, is_complete=False)
        TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
        CaseRelation.objects.create(
            object_id=care_plan.id,
            content_type=ContentType.objects.get_for_model(CarePlan),
            case=case_with_careplan,
        )

        response_json = self.provider_client.get(f"/providers/me/work_units/worklists/{worklist.id}/").json()

        self.assertEqual(response_json["count"], 2)

    def test_work_unit_query_config_operator(self):
        category_1 = CaseCategoryFactory()
        case_1 = CaseFactory(category=category_1, description="Find me")
        case_2 = CaseFactory(category=category_1)

        category_2 = CaseCategoryFactory()
        case_3 = CaseFactory(category=category_2, description="Find me")

        CaseFactory(category=CaseCategoryFactory())

        # Test or(|) operator
        # This should return 3 cases
        worklist = WorkUnitWorklistFactory(
            work_unit_query_config={"query_params": f"category_id={category_1.id}|category_id={category_2.id}"}
        )
        response_json = self.provider_client.get(f"/providers/me/work_units/worklists/{worklist.id}/").json()

        self.assertEqual(response_json["count"], 3)

        for response_case in response_json["results"]:
            self.assertIn(response_case["object_id"], [case_1.id, case_2.id, case_3.id])
            self.assertIn(response_case["content_object"]["category"], [category_1.id, category_2.id])

        # Test and(&) operator
        # This should return only case_1
        worklist.work_unit_query_config = {
            "query_params": f"category_id={category_1.id}&description={case_1.description}"
        }
        worklist.save()

        response_json = self.provider_client.get(f"/providers/me/work_units/worklists/{worklist.id}/").json()

        self.assertEqual(response_json["count"], 1)

        item_in_response = next(filter(lambda item: item["object_id"] == case_1.id, response_json["results"]), None)
        self.assertEqual(item_in_response["object_id"], case_1.id)
        self.assertEqual(item_in_response["content_object"]["category"], category_1.id)
        self.assertEqual(item_in_response["content_object"]["description"], case_1.description)

    def test_work_unit_worklist_view_with_pagination(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        CaseFactory(category=category)
        CaseFactory(category=category)
        CaseFactory(category=category)
        CaseFactory(category=category)

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=2&offset=0"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 2)

    def test_work_unit_worklist_view_with_status_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.status = "New"
        case_1.save()
        case_2.status = "In Progress"
        case_2.save()
        case_3.status = "New"
        case_3.save()
        case_4.status = "New"
        case_4.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "isAnyOf", "value": ["New", "In Progress"], "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "isAnyOf", "value": ["In Progress"], "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps({"items": [{"field": "status", "operator": "contains", "value": "New", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 4: operator contains
        filters = json.dumps({"items": [{"field": "status", "operator": "contains", "value": "In ", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator equals
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "equals", "value": "In Progress", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 6: operator equals
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "equals", "value": "IN PROGRESS", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 7: operator equals
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "equals", "value": "in progress", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 8: operator equals
        filters = json.dumps({"items": [{"field": "status", "operator": "equals", "value": "In ", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test Case 9: operator startsWith
        filters = json.dumps({"items": [{"field": "status", "operator": "startsWith", "value": "In", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 10: operator startsWith
        filters = json.dumps({"items": [{"field": "status", "operator": "startsWith", "value": "IN", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 11: operator startsWith
        filters = json.dumps({"items": [{"field": "status", "operator": "startsWith", "value": "in", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 12: operator startsWith
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "startsWith", "value": "Progress", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test Case 13: operator endsWith
        filters = json.dumps({"items": [{"field": "status", "operator": "endsWith", "value": "In", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test Case 14: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "endsWith", "value": "Progress", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 15: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "endsWith", "value": "progress", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 16: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "status", "operator": "endsWith", "value": "PROGRESS", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 17: operator isEmpty
        filters = json.dumps({"items": [{"field": "status", "operator": "isEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test Case 18: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "status", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)
        # Test Case 19: link_operators or
        filters = json.dumps(
            {
                "link_operator": "or",
                "items": [
                    {"field": "status", "operator": "contains", "value": "New", "type": "item"},
                    {"field": "status", "operator": "equals", "value": "In Progress", "type": "item"},
                ],
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)
        # Test Case 20: link_operators and
        filters = json.dumps(
            {
                "link_operator": "and",
                "items": [
                    {"field": "status", "operator": "contains", "value": "New", "type": "item"},
                    {"field": "status", "operator": "equals", "value": "In Progress", "type": "item"},
                ],
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)

    def test_work_unit_worklist_view_with_notes_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.notes = "Test Note"
        case_1.save()
        case_2.notes = "Hello"
        case_2.save()
        case_3.notes = None
        case_3.save()
        case_4.notes = "Test Notes 2"
        case_4.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {"items": [{"field": "notes", "operator": "isAnyOf", "value": ["Hello", "Test Note"], "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps({"items": [{"field": "notes", "operator": "isAnyOf", "value": ["Hello"], "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps({"items": [{"field": "notes", "operator": "contains", "value": "Test", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 4: operator equals
        filters = json.dumps({"items": [{"field": "notes", "operator": "equals", "value": "Hello", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator startsWith
        filters = json.dumps({"items": [{"field": "notes", "operator": "startsWith", "value": "Test", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 6: operator endsWith
        filters = json.dumps({"items": [{"field": "notes", "operator": "endsWith", "value": "2", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 7: operator isEmpty
        filters = json.dumps({"items": [{"field": "notes", "operator": "isEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 8: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "notes", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def test_work_unit_worklist_view_with_person_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.person = PersonUserFactory(first_name="Test", last_name="patient")
        case_1.save()
        case_2.person = PersonUserFactory(first_name="Test", last_name="patient2")
        case_2.save()
        case_3.person = PersonUserFactory(first_name="Robert", last_name="")
        case_3.save()
        case_4.person = PersonUserFactory(first_name="James", last_name="Anderson")
        case_4.save()
        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "person",
                        "operator": "isAnyOf",
                        "value": ["Test patient", "Test patient2"],
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps(
            {"items": [{"field": "person", "operator": "isAnyOf", "value": ["James Anderson"], "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps({"items": [{"field": "person", "operator": "contains", "value": "Test", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 4: operator equals
        filters = json.dumps(
            {"items": [{"field": "person", "operator": "equals", "value": "Test Patient", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator startsWith
        filters = json.dumps(
            {"items": [{"field": "person", "operator": "startsWith", "value": "Test", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 6: operator endsWith
        filters = json.dumps({"items": [{"field": "person", "operator": "endsWith", "value": "2", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 8: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "person", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)

    def test_work_unit_next_action_date_filter(self):
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)
        case_5 = CaseFactory(category=category)
        case_6 = CaseFactory(category=category)
        case_7 = CaseFactory(category=category)

        case_1.due_date = "2023-01-01"
        case_1.save()
        case_2.due_date = "2023-03-04"
        case_2.save()
        case_3.due_date = "2022-08-11"
        case_3.save()
        case_4.due_date = "2023-03-30"
        case_4.save()
        today = date.today()
        case_5.due_date = today.strftime("%Y-%m-%d")
        case_5.save()
        delta_day = today + timedelta(days=2)
        case_6.due_date = delta_day.strftime("%Y-%m-%d")
        case_6.save()
        delta_day = today + timedelta(days=9)
        case_7.due_date = delta_day.strftime("%Y-%m-%d")
        case_7.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test case 1: is operator
        filters = json.dumps({"items": [{"field": "dueDate", "operator": "is", "value": "2023-03-04"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)

        # Test case 2: is after operator
        filters = json.dumps({"items": [{"field": "dueDate", "operator": "after", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 5)
        self.assertEqual(len(results), 5)
        # Test case 3: is on or after operator
        filters = json.dumps({"items": [{"field": "dueDate", "operator": "onOrAfter", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 6)
        self.assertEqual(len(results), 6)
        # Test case 4: is before operator
        filters = json.dumps({"items": [{"field": "dueDate", "operator": "before", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test case 5: is on or before operator
        filters = json.dumps({"items": [{"field": "dueDate", "operator": "onOrBefore", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test case 6: is empty
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "dueDate",
                        "operator": "isEmpty",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test case 7: is not empty
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "dueDate",
                        "operator": "isNotEmpty",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 7)
        self.assertEqual(len(results), 7)
        # Test case 8: is today
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "dueDate",
                        "operator": "today",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test case 9: is overdue
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "dueDate",
                        "operator": "overdue",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)
        # Test case 10: is next 7 days
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "dueDate",
                        "operator": "next_7_days",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)

    def test_work_unit_case_created_date_filter(self):
        category = CaseCategoryFactory()
        with freeze_database_time_context(freeze_time=datetime.strptime("2023-01-01", "%Y-%m-%d")):
            CaseFactory(category=category)
        with freeze_database_time_context(freeze_time=datetime.strptime("2023-03-04", "%Y-%m-%d")):
            CaseFactory(category=category)
        with freeze_database_time_context(freeze_time=datetime.strptime("2022-08-11", "%Y-%m-%d")):
            CaseFactory(category=category)
        with freeze_database_time_context(freeze_time=datetime.strptime("2023-03-30", "%Y-%m-%d")):
            CaseFactory(category=category)

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test case 1: is operator
        filters = json.dumps({"items": [{"field": "createdAt", "operator": "is", "value": "2023-03-04"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)

        # Test case 2: is after operator
        filters = json.dumps({"items": [{"field": "createdAt", "operator": "after", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test case 3: is on or after operator
        filters = json.dumps({"items": [{"field": "createdAt", "operator": "onOrAfter", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test case 4: is before operator
        filters = json.dumps({"items": [{"field": "createdAt", "operator": "before", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test case 5: is on or before operator
        filters = json.dumps({"items": [{"field": "createdAt", "operator": "onOrBefore", "value": "2023-01-01"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test case 6: is empty
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "createdAt",
                        "operator": "isEmpty",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test case 7: is not empty
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "createdAt",
                        "operator": "isNotEmpty",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)

    def test_work_unit_worklist_view_with_case_category_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory(title="Care Gap - Cervical Cancer Screening")
        category1 = CaseCategoryFactory(title="Care Gap - Breast Cancer Screening")
        category2 = CaseCategoryFactory()
        CaseFactory(category=category)
        CaseFactory(category=category1)
        CaseFactory(category=category2)
        CaseFactory(category=category)

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(
            work_unit_query_config={"query_params": f"category_id={category.id}|category_id={category1.id}"}
        )
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "category",
                        "operator": "isAnyOf",
                        "value": ["Care Gap - Cervical Cancer Screening", "Care Gap - Breast Cancer Screening"],
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "category",
                        "operator": "isAnyOf",
                        "value": ["Care Gap - Breast Cancer Screening"],
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps(
            {"items": [{"field": "category", "operator": "contains", "value": "care gap", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 4: operator equals
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "category",
                        "operator": "equals",
                        "value": "Care Gap - Breast Cancer Screening",
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator startsWith
        filters = json.dumps(
            {"items": [{"field": "category", "operator": "startsWith", "value": "Care", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 6: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "category", "operator": "endsWith", "value": "Screening", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 7: operator isEmpty
        filters = json.dumps({"items": [{"field": "category", "operator": "isEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 0)
        self.assertEqual(len(results), 0)
        # Test Case 8: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "category", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def test_work_unit_worklist_view_with_description_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.description = "Test description"
        case_1.save()
        case_2.description = "Hello"
        case_2.save()
        case_3.description = None
        case_3.save()
        case_4.description = "Test description 2"
        case_4.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "description",
                        "operator": "isAnyOf",
                        "value": ["Hello", "Test description"],
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps(
            {"items": [{"field": "description", "operator": "isAnyOf", "value": ["Hello"], "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps(
            {"items": [{"field": "description", "operator": "contains", "value": "Test", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 4: operator equals
        filters = json.dumps(
            {"items": [{"field": "description", "operator": "equals", "value": "Hello", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator startsWith
        filters = json.dumps(
            {"items": [{"field": "description", "operator": "startsWith", "value": "Test", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 6: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "description", "operator": "endsWith", "value": "2", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 7: operator isEmpty
        filters = json.dumps({"items": [{"field": "description", "operator": "isEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 8: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "description", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def test_work_unit_worklist_view_with_care_team_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)

        np_provider_on_care_team1 = ProviderDetailFactory.create(first_name="James")
        np_provider_on_care_team2 = ProviderDetailFactory.create(first_name="Bob")
        np_provider_on_care_team3 = ProviderDetailFactory.create(first_name="Ele")

        person1 = PersonUserFactory(first_name="Test", last_name="patient")
        person1.care_team.add(np_provider_on_care_team1)
        person1.care_team.add(np_provider_on_care_team2)
        case_1.person = person1
        case_1.save()
        person1.save()
        person2 = PersonUserFactory(first_name="Test", last_name="patient2")
        case_2.person = person2
        person2.care_team.add(np_provider_on_care_team2)
        case_2.save()
        person2.save()
        person3 = PersonUserFactory(first_name="Test", last_name="patient3")
        case_3.person = person3
        person3.care_team.add(np_provider_on_care_team3)
        case_3.save()
        person3.save()
        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator contains
        filters = json.dumps({"items": [{"field": "careTeam", "operator": "contains", "value": "Bob", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # change the care team of a person; verify that the new results do not contain the old care team
        # remove bob from person1's care team
        person1.care_team.set([np_provider_on_care_team1])
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)

    def test_work_unit_worklist_view_with_owner_filter(self):
        # Use a Case object as an example WorkUnit
        # The Worklist's FilterQuery should find this object
        category = CaseCategoryFactory()
        clinician_group, created = AssigneeGroup.objects.update_or_create(name="Clinician" + "_" + "Group")
        quality_group, created = AssigneeGroup.objects.update_or_create(name="Quality" + "_" + "Group")
        ops_group, created = AssigneeGroup.objects.update_or_create(name="Ops" + "_" + "Group")
        CaseFactory(category=category, owner_group=clinician_group)
        CaseFactory(category=category, owner_group=quality_group)
        CaseFactory(category=category, owner_group=ops_group)

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})
        # Test Case 1: operator isAnyOf
        filters = json.dumps(
            {
                "items": [
                    {
                        "field": "assignedToName",
                        "operator": "isAnyOf",
                        "value": ["Clinician_Group", "Quality_Group"],
                        "type": "item",
                    }
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        # Test Case 2: operator isAnyOf with 1 value
        filters = json.dumps(
            {
                "items": [
                    {"field": "assignedToName", "operator": "isAnyOf", "value": ["Clinician_Group"], "type": "item"}
                ]
            }
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 3: operator contains
        filters = json.dumps(
            {"items": [{"field": "assignedToName", "operator": "contains", "value": "Group", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 4: operator equals
        filters = json.dumps(
            {"items": [{"field": "assignedToName", "operator": "equals", "value": "Clinician_Group", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 5: operator startsWith
        filters = json.dumps(
            {"items": [{"field": "assignedToName", "operator": "startsWith", "value": "Quality", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        # Test Case 6: operator endsWith
        filters = json.dumps(
            {"items": [{"field": "assignedToName", "operator": "endsWith", "value": "Group", "type": "item"}]}
        )
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)
        # Test Case 7: operator isNotEmpty
        filters = json.dumps({"items": [{"field": "assignedToName", "operator": "isNotEmpty", "type": "item"}]})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&filters={filters}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def test_work_unit_worklist_view_with_pods_filter(self):
        category = CaseCategoryFactory()
        east_pod = PodFactory(name="East Pod")
        west_pod = PodFactory(name="West Pod")

        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        person1 = PersonUserFactory(first_name="Test1", last_name="patient")
        # Test for deleted pod relationships with multiple `set()`s
        person1.pods.set([west_pod])
        person1.pods.set([east_pod])
        person1.save()
        case_1.person = person1
        case_1.save()

        person2 = PersonUserFactory(first_name="Test2", last_name="patient2")
        person2.pods.set([west_pod])
        person2.save()
        case_2.person = person2
        case_2.save()

        person3 = PersonUserFactory(first_name="Test3", last_name="patient3")
        person3.pods.set([east_pod, west_pod])
        person3.save()
        case_3.person = person3
        case_3.save()

        person4 = PersonUserFactory(first_name="Test4", last_name="patient4")
        case_4.person = person4
        case_4.save()

        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})

        worklist_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0"

        east_pod_filters = json.dumps(
            {"items": [{"field": "podNames", "operator": "contains", "value": "Eas", "type": "item"}]}
        )

        west_pod_filters = json.dumps(
            {"items": [{"field": "podNames", "operator": "contains", "value": "west", "type": "item"}]}
        )

        is_empty_filters = json.dumps({"items": [{"field": "podNames", "operator": "isEmpty", "type": "item"}]})

        is_not_empty_filters = json.dumps({"items": [{"field": "podNames", "operator": "isNotEmpty", "type": "item"}]})

        east_or_west_filters = json.dumps(
            {
                "link_operator": "or",
                "items": [
                    {"field": "podNames", "operator": "contains", "value": "east", "type": "item"},
                    {"field": "podNames", "operator": "contains", "value": "west", "type": "item"},
                ],
            }
        )

        # Without filters: should return all the results
        with self.assertNumQueries(12):
            response_json = self.provider_client.get(worklist_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)

        # With filters: should return cases with East pod
        with self.assertNumQueries(11):
            response_json = self.provider_client.get(worklist_uri + f"&filters={east_pod_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        case_1_response_data = next(result for result in results if result["object_id"] == case_1.id)
        pod_data = case_1_response_data["content_object"]["person_meta"]["pods"]
        self.assertEqual(len(pod_data), 1)
        self.assertEqual(pod_data[0]["name"], east_pod.name)

        # With filter should return cases with West pod
        response_json = self.provider_client.get(worklist_uri + f"&filters={west_pod_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)

        # With filter should return cases without care pod
        response_json = self.provider_client.get(worklist_uri + f"&filters={is_empty_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)

        # With filter should return cases with any care pod
        response_json = self.provider_client.get(worklist_uri + f"&filters={is_not_empty_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

        # With filter should return cases with east or west care pod
        response_json = self.provider_client.get(worklist_uri + f"&filters={east_or_west_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def verify_case_tag(
        self,
        results,
        expected_cases: List[Case],
        expected_tag: Optional[str] = None,
    ):
        for case in expected_cases:
            case_response_data = next(result for result in results if result["object_id"] == case.id)
            case_tags = case_response_data["content_object"]["case_tags"]
            tag_names = []
            for case_tag in case_tags:
                tag_names.append(case_tag["tag"]["display_name"])
            if expected_tag is not None:
                self.assertTrue(expected_tag in tag_names)
            else:
                self.assertEqual(tag_names, [])

    def test_work_unit_worklist_view_with_tags_filter(self):
        category = CaseCategoryFactory()
        high_risk_tag_display: str = "High Risk"
        care_gap_tag_display: str = "Care Gap"
        high_risk_tag = TagFactory(display_name=high_risk_tag_display)
        care_gap_tag = TagFactory(display_name=care_gap_tag_display)
        random_tag = TagFactory()

        person1 = PersonUserFactory(first_name="Test1", last_name="patient")
        person1.save()
        high_risk_care_gap_case: Case = CaseFactory(category=category, person=person1)
        high_risk_care_gap_case.tags.set([high_risk_tag, care_gap_tag])
        high_risk_case = CaseFactory(category=category, person=person1)
        high_risk_case.tags.set([high_risk_tag, care_gap_tag])
        high_risk_case.tags.set([high_risk_tag])
        care_gap_case = CaseFactory(category=category, person=person1)
        care_gap_case.tags.set([high_risk_tag, care_gap_tag, random_tag])
        care_gap_case.tags.set([care_gap_tag, random_tag])
        case_without_tags = CaseFactory(category=category, person=person1)
        random_tag.delete()

        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})

        worklist_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0"

        high_risk_filters = json.dumps(
            {"items": [{"field": "tags", "operator": "contains", "value": high_risk_tag_display, "type": "item"}]}
        )

        care_gap_filters = json.dumps(
            {"items": [{"field": "tags", "operator": "contains", "value": care_gap_tag_display, "type": "item"}]}
        )

        is_empty_filters = json.dumps({"items": [{"field": "tags", "operator": "isEmpty", "type": "item"}]})

        is_not_empty_filters = json.dumps({"items": [{"field": "tags", "operator": "isNotEmpty", "type": "item"}]})

        high_risk_or_care_gap_filters = json.dumps(
            {
                "link_operator": "or",
                "items": [
                    {"field": "tags", "operator": "contains", "value": high_risk_tag_display, "type": "item"},
                    {"field": "tags", "operator": "contains", "value": care_gap_tag_display, "type": "item"},
                ],
            }
        )

        # Clear cache
        call_command(
            "invalidate_cachalot",
        )
        ContentType.objects.clear_cache()
        # Without filters: should return all the results
        with self.assertNumQueries(13):
            response_json = self.provider_client.get(worklist_uri).json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 4)
        self.assertEqual(len(results), 4)

        # With filters: should return cases with High Risk
        with self.assertNumQueries(11):
            response_json = self.provider_client.get(worklist_uri + f"&filters={high_risk_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        self.verify_case_tag(
            results=results,
            expected_cases=[high_risk_care_gap_case, high_risk_case],
            expected_tag=high_risk_tag_display,
        )

        # With filter should return cases with Care gap
        response_json = self.provider_client.get(worklist_uri + f"&filters={care_gap_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 2)
        self.assertEqual(len(results), 2)
        self.verify_case_tag(
            results=results,
            expected_cases=[high_risk_care_gap_case, care_gap_case],
            expected_tag=care_gap_tag_display,
        )

        # With filter should return cases without tags
        response_json = self.provider_client.get(worklist_uri + f"&filters={is_empty_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 1)
        self.assertEqual(len(results), 1)
        self.verify_case_tag(
            results=results,
            expected_cases=[case_without_tags],
            expected_tag=None,
        )

        # With filter should return cases with any tag
        response_json = self.provider_client.get(worklist_uri + f"&filters={is_not_empty_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

        # With filter should return cases with high risk or care gap tag
        response_json = self.provider_client.get(worklist_uri + f"&filters={high_risk_or_care_gap_filters}").json()
        results = response_json["results"]
        self.assertEqual(response_json["count"], 3)
        self.assertEqual(len(results), 3)

    def test_work_unit_worklist_view_with_sort(self):
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.status = "New"
        case_1.due_date = "2023-12-31"
        case_1.save()
        case_2.status = "In Progress"
        case_2.due_date = "2022-11-30"
        case_2.save()
        case_3.status = "New"
        case_3.due_date = "2023-10-30"
        case_3.save()
        case_4.status = "New"
        case_4.due_date = "2023-12-30"
        case_4.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})

        # Test Case 1: sort asc field: status
        sort = json.dumps({"field": "status", "sort": "asc", "type": "item"})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["content_object"]["status"], "In Progress")

        # Test Case 2: sort desc field: status
        sort = json.dumps({"field": "status", "sort": "desc", "type": "item"})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["content_object"]["status"], "New")

        # Test Case 3: sort asc field: Next Action Date
        sort = json.dumps({"field": "due_date", "sort": "asc", "type": "item"})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["content_object"]["due_date"], "2022-11-30T00:00:00Z")

        # Test Case 4: sort desc field:  Next Action Date
        sort = json.dumps({"field": "due_date", "sort": "desc", "type": "item"})
        complete_uri = f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}"
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 4)
        self.assertEqual(results[0]["content_object"]["due_date"], "2023-12-31T00:00:00Z")

    def test_work_unit_worklist_view_with_sort_and_filter(self):
        category = CaseCategoryFactory()
        case_1 = CaseFactory(category=category)
        case_2 = CaseFactory(category=category)
        case_3 = CaseFactory(category=category)
        case_4 = CaseFactory(category=category)

        case_1.status = "New"
        case_1.due_date = "2023-12-31"
        case_1.save()
        case_2.status = "In Progress"
        case_2.due_date = "2022-11-30"
        case_2.save()
        case_3.status = "New"
        case_3.due_date = "2023-10-30"
        case_3.save()
        case_4.status = "New"
        case_4.due_date = "2023-12-30"
        case_4.save()

        # Use the Worklist's work_unit_config property to find the params
        # we'll use to filter the Work Unit results
        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})

        # Test Case 1: sort asc field: due_date filter: status
        sort = json.dumps({"field": "due_date", "sort": "asc", "type": "item"})
        filters = json.dumps({"items": [{"field": "status", "operator": "contains", "value": "New", "type": "item"}]})
        complete_uri = (
            f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}&filters={filters}"
        )
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0]["content_object"]["due_date"], "2023-10-30T00:00:00Z")
        self.assertEqual(results[0]["content_object"]["status"], "New")

        # Test Case 2: sort desc field: due_date filter: status
        sort = json.dumps({"field": "status", "sort": "desc", "type": "item"})
        complete_uri = (
            f"/providers/me/work_units/worklists/{worklist.id}/?limit=50&offset=0&sort={sort}&filters={filters}"
        )
        response_json = self.provider_client.get(complete_uri).json()
        results = response_json["results"]
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0]["content_object"]["due_date"], "2023-12-31T00:00:00Z")
        self.assertEqual(results[0]["content_object"]["status"], "New")

    def test_work_unit_worklist_view_with_appointment_case_relation(self):
        preferred_language = PreferredLanguageFactory(name="Other")
        person_1 = PersonUserFactory(preferred_language=preferred_language, preferred_language_other="Test Language")

        person_2 = PersonUserFactory(
            preferred_language=preferred_language, preferred_language_other="Another Test Language"
        )

        provider = ProviderDetailFactory()
        physician = provider.physician
        practice = physician.practice

        category = CaseCategoryFactory(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)

        appointment_1 = AppointmentFactory(
            patient_id=person_1.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=10),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointment_2 = AppointmentFactory(
            patient_id=person_2.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=15),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        appointment_3 = AppointmentFactory(
            patient_id=person_1.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=20),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        active_case_1 = Case.objects.create(
            person=person_1,
            category=category,
            description=person_1.preferred_language_other,
            status=CaseStatus.IN_PROGRESS,
        )

        CaseRelation.objects.create(
            case=active_case_1, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment_1.id
        )

        active_case_2 = Case.objects.create(
            person=person_2,
            category=category,
            description=person_2.preferred_language_other,
            status=CaseStatus.IN_PROGRESS,
        )

        CaseRelation.objects.create(
            case=active_case_2, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment_2.id
        )

        completed_case = Case.objects.create(
            person=person_1, category=category, description=person_1.preferred_language_other, status=CaseStatus.CLOSED
        )

        CaseRelation.objects.create(
            case=completed_case, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment_3.id
        )

        worklist = WorkUnitWorklistFactory(work_unit_query_config={"query_params": f"category_id={category.id}"})

        response = self.provider_client.get(f"/providers/me/work_units/worklists/{worklist.id}/")

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        response_json = response.json()

        response_1 = next(filter(lambda item: item["object_id"] == active_case_1.id, response_json["results"]), None)
        self.assertEqual(response_1["owner_group"], active_case_1.owner_group_id)
        self.assertEqual(response_1["due_date"], active_case_1.due_date)
        self.assertEqual(response_1["status_category"], active_case_1.status_category)
        self.assertEqual(response_1["person_user"], active_case_1.person.user_id)
        self.assertEqual(response_1["content_type"], "case")
        self.assertEqual(response_1["content_object"]["id"], active_case_1.id)
        self.assertEqual(response_1["content_object"]["relations"][0]["content_object"]["id"], appointment_1.id)
        self.assertEqual(response_1["case_url"], link_to_case_for_person(active_case_1.id))

        response_2 = next(filter(lambda item: item["object_id"] == active_case_2.id, response_json["results"]), None)
        self.assertEqual(response_2["owner_group"], active_case_2.owner_group_id)
        self.assertEqual(response_2["due_date"], active_case_2.due_date)
        self.assertEqual(response_2["status_category"], active_case_2.status_category)
        self.assertEqual(response_2["person_user"], active_case_2.person.user_id)
        self.assertEqual(response_2["content_type"], "case")
        self.assertEqual(response_2["content_object"]["id"], active_case_2.id)
        self.assertEqual(response_2["content_object"]["relations"][0]["content_object"]["id"], appointment_2.id)
        self.assertEqual(response_2["case_url"], link_to_case_for_person(active_case_2.id))


class PatientTodoWorkUnitListViewPaginationTestCase(FireflyTestCase):
    def assert_response(self, url, count=0, previous=None, next=None, params=None, results=None):
        results = results or []
        response = self.provider_client.get(url)

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], count)
        self.assertEqual(body["previous"], previous)
        self.assertEqual(body["next"], next)
        self.assertEqual(body["params"], params)
        actual_results = body["results"]
        self.assertEqual(len(actual_results), len(results))
        for i, actual_result in enumerate(actual_results):
            self.assertEqual(actual_result["object_id"], results[i].id, f"At index: {i}")

    def assert_pagination(self, person, sort_by="", is_complete=False, results=None):
        """
        Helper for running a suite of tests to exercise pagination behavior for
        the todos endpoint.

        To use, create test fixtures such that exactly three results appear for
        the given person in the given sort order, and provide those to
        `results`. This function will assert that these results are paginated as
        expected.
        """
        results = results or []
        self.assertEqual(len(results), 3, "Please set up exactly three expected results")

        # Fetch the first page of 2 results.
        self.assert_response(
            (f"/providers/me/work_units/people/{person.id}/todos/?limit=2&sort_by={sort_by}&is_complete={is_complete}"),
            count=3,
            next=(
                f"http://testserver/providers/me/work_units/people/{person.id}/todos/?"
                f"is_complete={is_complete}&limit=2&offset=2&sort_by={sort_by}"
            ),
            results=results[0:2],
        )
        # Fetch the second page of 1 result.
        self.assert_response(
            (
                f"/providers/me/work_units/people/{person.id}/todos/"
                f"?limit=2&offset=2&sort_by={sort_by}&is_complete={is_complete}"
            ),
            count=3,
            previous=(
                f"http://testserver/providers/me/work_units/people/{person.id}/todos/?"
                f"is_complete={is_complete}&limit=2&sort_by={sort_by}"
            ),
            results=results[2:],
        )
        # Index into the first result, which should return the first page of two
        # results.
        self.assert_response(
            (
                f"/providers/me/work_units/people/{person.id}/todos/?"
                f"limit=2&content_type=case&object_id={results[0].id}&sort_by={sort_by}"
            ),
            count=3,
            next=(
                f"http://testserver/providers/me/work_units/people/{person.id}/todos/?"
                f"is_complete={is_complete}&limit=2&offset=2&sort_by={sort_by}"
            ),
            params={"is_complete": is_complete},
            results=results[0:2],
        )
        # Index into the second result, which should return the first page of
        # two results.
        self.assert_response(
            (
                f"/providers/me/work_units/people/{person.id}/todos/?"
                f"limit=2&content_type=case&object_id={results[1].id}&sort_by={sort_by}"
            ),
            count=3,
            next=(
                f"http://testserver/providers/me/work_units/people/{person.id}/todos/?"
                f"is_complete={is_complete}&limit=2&offset=2&sort_by={sort_by}"
            ),
            params={"is_complete": is_complete},
            results=results[0:2],
        )
        # Index into the third result, which should return the second page of
        # one result.
        self.assert_response(
            (
                f"/providers/me/work_units/people/{person.id}/todos/?"
                f"limit=2&content_type=case&object_id={results[2].id}&sort_by={sort_by}"
            ),
            count=3,
            previous=(
                f"http://testserver/providers/me/work_units/people/{person.id}/todos/?"
                f"is_complete={is_complete}&limit=2&sort_by={sort_by}"
            ),
            params={"is_complete": is_complete},
            results=results[2:],
        )

    def test_sort_by_created_at(self):
        person = PersonFactory()
        category = CaseCategoryFactory()
        active_cases = []
        inactive_cases = []
        for i in range(3):
            active_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
            )
            active_case.created_at = datetime.now(timezone.utc) - timedelta(days=i + 1)
            active_case.save()
            active_cases.append(active_case)

            inactive_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.CLOSED,
            )
            inactive_case.created_at = datetime.now(timezone.utc) - timedelta(days=i + 1)
            inactive_case.save()
            inactive_cases.append(inactive_case)

        self.assert_pagination(person, is_complete=False, results=active_cases)
        self.assert_pagination(person, sort_by="created_at", is_complete=False, results=active_cases)
        self.assert_pagination(person, is_complete=True, results=inactive_cases)
        self.assert_pagination(person, sort_by="created_at", is_complete=True, results=inactive_cases)

    def test_sort_by_updated_at(self):
        person = PersonFactory()
        category = CaseCategoryFactory()
        active_cases = []
        inactive_cases = []
        for _ in range(3):
            active_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
            )
            active_cases.append(active_case)

            inactive_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.CLOSED,
            )
            inactive_cases.append(inactive_case)

        self.assert_pagination(person, is_complete=False, sort_by="updated_at", results=list(reversed(active_cases)))
        self.assert_pagination(person, is_complete=True, sort_by="updated_at", results=list(reversed(inactive_cases)))

    def test_sort_by_due_date(self):
        person = PersonFactory()
        category = CaseCategoryFactory()
        active_cases = []
        inactive_cases = []
        for i in range(3):
            active_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
                due_date=datetime(2000 + i, 1, 1, 0, 0, 0, 0),
            )
            # Make sure we're actually sorting by due_date and not defaulting
            # back to created_at by reversing the created_at order.
            active_case.created_at = datetime.now(timezone.utc) + timedelta(days=i + 1)
            active_case.save()
            active_cases.append(active_case)

            inactive_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.CLOSED,
                due_date=datetime(2000 + i, 1, 1, 0, 0, 0, 0),
            )
            # Make sure we're actually sorting by due_date and not defaulting
            # back to created_at by reversing the created_at order.
            inactive_case.created_at = datetime.now(timezone.utc) + timedelta(days=i + 1)
            inactive_case.save()
            inactive_cases.append(inactive_case)

        self.assert_pagination(person, is_complete=False, sort_by="due_date", results=active_cases)
        self.assert_pagination(person, is_complete=True, sort_by="due_date", results=inactive_cases)

    def test_sort_by_due_date_null(self):
        person = PersonFactory()
        category = CaseCategoryFactory()
        active_cases = []
        inactive_cases = []
        # Make the due date of the third case null. In general, due date should
        # be required, but there may still be situations where it remains null,
        # and we should handle them gracefully.
        for i in range(3):
            active_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.IN_PROGRESS,
                due_date=datetime(2000 + i, 1, 1, 0, 0, 0, 0) if i < 2 else None,
            )
            # Make sure we're actually sorting by due_date and not defaulting
            # back to created_at by reversing the created_at order.
            active_case.created_at = datetime.now(timezone.utc) + timedelta(days=i + 1)
            active_case.save()
            active_cases.append(active_case)

            inactive_case = CaseFactory(
                person=person,
                category=category,
                status=CaseStatus.CLOSED,
                due_date=datetime(2000 + i, 1, 1, 0, 0, 0, 0) if i < 2 else None,
            )
            # Make sure we're actually sorting by due_date and not defaulting
            # back to created_at by reversing the created_at order.
            inactive_case.created_at = datetime.now(timezone.utc) + timedelta(days=i + 1)
            inactive_case.save()
            inactive_cases.append(inactive_case)

        self.assert_pagination(person, is_complete=False, sort_by="due_date", results=active_cases)
        self.assert_pagination(person, is_complete=True, sort_by="due_date", results=inactive_cases)


class InboxWorkUnitListViewTestCase(FireflyTestCase):
    def test_multiple_filters(self):
        """
        Regression test against pod filters overwriting due date filters
        """
        provider = ProviderDetailFactory()
        create_update_assignee_group_from_user(provider.user)
        assignee_group = provider.user.assignee_group

        past_date_time = datetime(2023, 8, 20, 0, 0, 0, 0, timezone.utc)
        future_date_time = datetime(2023, 9, 1, 0, 0, 0, 0, timezone.utc)

        pod = PodFactory()
        self.patient.person.pods.set([pod])

        # Case due in the future but a task due in the past
        future_case = CaseFactory(
            person=self.patient.person,
            owner_group=assignee_group,
            due_date=future_date_time,
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=future_case.person,
            owner_group=assignee_group,
            due_date=past_date_time,
            is_complete=True,
        )
        TaskRelation.objects.create(
            task=task,
            content_object=future_case,
        )

        # A care plan with a Task due in the future
        future_care_plan = CarePlanFactory(patient=future_case.person.user, due_date=past_date_time)
        # Task due date is what matters vs. CarePlan due date
        task = Task.objects.create(
            person=future_care_plan.patient.person,
            owner_group=future_case.owner_group,
            due_date=future_date_time,
            is_complete=False,
        )
        TaskRelation.objects.create(
            task=task,
            content_object=future_care_plan,
        )

        today_date_str = "2023-08-24T23:59:59-04:00"
        base_request_url = f"/providers/me/work_units/inbox/?limit=10&offset=0&owner_group_id__in={assignee_group.pk}"

        # Filtering only by pod -> includes both work units
        result_json = self.provider_client.get(f"{base_request_url}&pod_id__in={pod.id}").json()
        object_data = map(
            lambda item: {key: item[key] for key in ["object_id", "content_type"]},
            result_json["results"],
        )
        self.assertCountEqual(
            object_data,
            [
                {"content_type": "careplan", "object_id": future_care_plan.id},
                {"content_type": "case", "object_id": future_case.id},
            ],
        )
        # Filtering by date + pod -> excludes work units due in the future
        result_json = self.provider_client.get(
            f"{base_request_url}&pod_id__in={pod.id}&due_date__lte={today_date_str}"
        ).json()
        self.assertEqual(len(result_json["results"]), 0)

    def test_inbox_work_units(self):
        patient = self.patient
        person = Person.objects.get(user=patient)
        provider_case = CaseFactory(
            person=person,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        case_task = Task.objects.create(
            person=person,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            is_complete=False,
        )
        TaskRelation.objects.create(
            task=case_task,
            content_object=provider_case,
        )
        response = self.provider_client.get("/providers/me/work_units/inbox/?sort_by=due_date")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], 1)
        results = body["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["content_type"], "case")
        self.assertEqual(results[0]["object_id"], provider_case.id)
        self.assertEqual(results[0]["content_object"]["person"]["id"], person.id)
        self.assertEqual(results[0]["content_object"]["person"]["user_id"], patient.id)
        items = results[0]["items"]
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]["content_type"], "task")
        self.assertEqual(items[0]["object_id"], case_task.id)
        item = items[0]["content_object"]
        self.assertEqual(len(item["owner_group"]["assignee_group_users"]), 1)
        self.assertEqual(item["owner_group"]["assignee_group_users"][0]["user_id"], self.provider.id)

    def remove_audit_fields_from_item(self, item):
        audit_fields: List[str] = [
            "created_at",
            "created_by",
            "updated_at",
            "updated_by",
        ]
        for audit_field in audit_fields:
            item.pop(audit_field, None)
        return item

    def assert_response(self, url, count=0, previous=None, next=None, aggregates=None, results=None):
        results = results or []
        response = self.provider_client.get(url)

        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        body = response.json()
        self.assertEqual(body["count"], count, f"Given url: {url}")
        self.assertEqual(body["previous"], previous, f"Given url: {url}")
        self.assertEqual(body["next"], next, f"Given url: {url}")
        if aggregates is not None:
            self.assertEqual(body["aggregates"], aggregates, f"Given url: {url}")
        actual_results = body["results"]
        self.assertEqual(len(actual_results), len(results), f"Given url: {url}")
        for i, actual_result in enumerate(actual_results):
            expected_result = results[i]
            self.assertEqual(actual_result["object_id"], expected_result.id, f"Given url: {url}. At index: {i}")
            if isinstance(expected_result, Case):
                expected_case_tag_data: CaseTagData = []
                for case_tag in expected_result.case_tags.iterator():
                    if case_tag.deleted is None and case_tag.tag.deleted is None:
                        tag: CaseTag = {
                            "display_name": case_tag.tag.display_name,
                            "id": case_tag.tag.id,
                        }
                        case_tag_data: CaseTagData = {"tag": tag, "id": case_tag.pk}
                        expected_case_tag_data.append(case_tag_data)
                for case_tag in actual_result["content_object"].get("case_tags", []):
                    case_tag = self.remove_audit_fields_from_item(item=case_tag)
                    case_tag["tag"] = self.remove_audit_fields_from_item(item=case_tag["tag"])
                self.assertEqual(actual_result["content_object"].get("case_tags", []), expected_case_tag_data)

    def test_filter_by_owner_group(self):
        create_update_assignee_group_from_user(self.provider)
        create_update_assignee_group_from_user(self.patient)
        unowned_case = CaseFactory(
            status=CaseStatus.IN_PROGRESS,
        )  # unowned case
        provider_case = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        patient_case = CaseFactory(
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )

        # All the cases, because there was no filter.
        self.assert_response(
            "/providers/me/work_units/inbox/?sort_by=due_date",
            count=3,
            results=[
                provider_case,
                patient_case,
                unowned_case,
            ],
        )

        # Only case with provider as owner
        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}",
            count=1,
            results=[
                provider_case,
            ],
        )

        # Cases with provider or patient as owner
        owner_group_ids = f"{self.provider.assignee_group.id},{self.patient.assignee_group.id}"
        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={owner_group_ids}",
            count=2,
            results=[
                provider_case,
                patient_case,
            ],
        )

    def test_filter_by_owner_id_for_assignee_group(self):
        create_update_assignee_group_from_user(self.provider)
        create_update_assignee_group_from_user(self.patient)

        # Add provider to another assignee group
        assignee_group_1: AssigneeGroup = AssigneeGroup.objects.create(
            name="Some Group",
            is_provider=True,
        )
        AssigneeGroupUser.objects.update_or_create(group=assignee_group_1, user=self.provider)

        # Some other assignee group provider does not belong to
        assignee_group_2: AssigneeGroup = AssigneeGroup.objects.create(
            name="Some Group 2",
            is_provider=True,
        )
        deleted_row, _ = AssigneeGroupUser.objects.update_or_create(group=assignee_group_2, user=self.provider)
        deleted_row.delete()

        unowned_case = CaseFactory(
            status=CaseStatus.IN_PROGRESS,
        )  # unowned case
        provider_case = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        patient_case = CaseFactory(
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        assignee_group_1_case = CaseFactory(
            owner_group=assignee_group_1,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )  # Case belongs to an assignee group the provider is in
        assignee_group_2_case = CaseFactory(
            owner_group=assignee_group_2,
            due_date=datetime(2000, 1, 4, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )  # Case belongs to some random assignee group that is filterable

        # All the cases, because there was no filter.
        self.assert_response(
            "/providers/me/work_units/inbox/?sort_by=due_date",
            count=5,
            results=[
                provider_case,
                patient_case,
                assignee_group_1_case,
                assignee_group_2_case,
                unowned_case,
            ],
        )

        # Only case with provider as owner and provider's assignee group(s)
        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_id__in={self.provider.id}",
            count=2,
            results=[
                provider_case,
                assignee_group_1_case,
            ],
        )

        # Cases with provider, patient, assignee group belonging to provider, or other assignee group as owner
        owner_ids = f"{self.provider.id}"
        owner_group_ids = f"{self.patient.assignee_group.id},{assignee_group_2.id}"
        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_id__in={owner_ids}&owner_group_id__in={owner_group_ids}",
            count=4,
            results=[
                provider_case,
                patient_case,
                assignee_group_1_case,
                assignee_group_2_case,
            ],
        )

    def test_filter_case_by_due_date(self):
        case = CaseFactory(
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        tag_1: Tag = TagFactory.create()
        tag_2: Tag = TagFactory.create()
        tag_3: Tag = TagFactory.create()
        case.tags.add(tag_1)
        case.tags.add(tag_2)
        case.tags.add(tag_3)
        # remove a tag from the case
        case.tags.remove(tag_2)
        # nuke a tag
        tag_1.delete()

        # All of these filters should include the case:
        for filter in [
            "",
            "&due_date__gte=2000-01-01T00:00:00-00:00",
            "&due_date__gte=1999-01-01T23:59:59-00:00",
            "&due_date__lte=2000-01-01T00:00:00-00:00",
            "&due_date__lte=2000-01-01T00:00:01-00:00",
            "&due_date__gte=2000-01-01T00:00:00-00:00&due_date__lte=2000-01-01T00:00:00-00:00",
            "&due_date__gte=2000-01-01T00:00:00-00:00&due_date__lte=2000-01-01T00:00:01-00:00",
            "&due_date__gte=1999-01-01T23:59:59-00:00&due_date__lte=2000-01-01T00:00:00-00:00",
            "&due_date__gte=1999-01-01T23:59:59-00:00&due_date__lte=2000-01-01T00:00:01-00:00",
            "&due_date__lte=2000-01-01T00:00:00-00:00&due_date__gte=2000-01-01T00:00:00-00:00",
            "&due_date__lte=2000-01-01T00:00:00-00:00&due_date__gte=1999-01-01T23:59:59-00:00",
            "&due_date__lte=2000-01-01T00:00:01-00:00&due_date__gte=2000-01-01T00:00:00-00:00",
            "&due_date__lte=2000-01-01T00:00:01-00:00&due_date__gte=1999-01-01T23:59:59-00:00",
        ]:
            self.assert_response(
                f"/providers/me/work_units/inbox/?sort_by=due_date{filter}",
                count=1,
                results=[
                    case,
                ],
            )

        # All of these filters should exclude the case:
        for filter in [
            "&due_date__gte=2000-01-01T00:00:01-00:00",
            "&due_date__lte=1999-01-01T23:59:59-00:00",
            "&due_date__gte=2000-01-01T00:00:01-00:00&due_date__lte=1999-01-01T23:59:59-00:00",
            "&due_date__gte=2000-01-01T00:00:01-00:00&due_date__lte=2000-01-01T00:00:00-00:00",
            "&due_date__lte=1999-01-01T23:59:59-00:00&due_date__gte=2000-01-01T00:00:00-00:00",
        ]:
            self.assert_response(
                f"/providers/me/work_units/inbox/?sort_by=due_date{filter}",
                count=0,
                results=[],
            )

    def test_filter_by_owner_group_including_tasks(self):
        create_update_assignee_group_from_user(self.provider)
        create_update_assignee_group_from_user(self.patient)
        # Standalone case owned by provider.
        # Should appear in provider's inbox.
        provider_case = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        # Standalone case owned by someone else.
        # Should NOT appear in provider's inbox.
        CaseFactory(
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        # Case owned by someone else with task for provider.
        # Should appear in provider's inbox.
        # Even though the case due date is after provider_case.due_date, the
        # task due date is earlier, so this case should be sorted first.
        other_case_with_provider_task = CaseFactory(
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        provider_task = Task.objects.create(
            person=other_case_with_provider_task.person,
            owner_group=self.provider.assignee_group,
            due_date=datetime(1999, 12, 31, 0, 0, 0, 0, timezone.utc),
            is_complete=False,
        )
        TaskRelation.objects.create(
            task=provider_task,
            content_object=other_case_with_provider_task,
        )
        # Case owned by someone else with task for someone else.
        # Should NOT appear in provider's inbox, even though this case also has
        # a completed task owned by the provider.
        other_case_with_other_task = CaseFactory(
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        other_task = Task.objects.create(
            person=other_case_with_other_task.person,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            is_complete=False,
        )
        TaskRelation.objects.create(
            task=other_task,
            content_object=other_case_with_other_task,
        )
        provider_completed_task = Task.objects.create(
            person=other_case_with_other_task.person,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            is_complete=True,
        )
        TaskRelation.objects.create(
            task=provider_completed_task,
            content_object=other_case_with_other_task,
        )

        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}",
            count=2,
            results=[
                other_case_with_provider_task,
                provider_case,
            ],
        )

    def test_filter_by_care_plan_template_owner_group(self):
        create_update_assignee_group_from_user(self.provider)
        care_plan_template_1 = CarePlanTemplateFactory()
        care_plan_1 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_1_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_1_task, content_object=care_plan_1, is_parent=True)
        care_plan_template_2 = CarePlanTemplateFactory()
        care_plan_2 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_2,
        )
        care_plan_2_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_2_task, content_object=care_plan_2, is_parent=True)
        care_plan_3 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=None,
        )
        care_plan_3_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_3_task, content_object=care_plan_3, is_parent=True)

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&content_type__in=careplan&careplan__autocreated_from_id__in={care_plan_template_1.id}"
            ),
            count=1,
            results=[
                care_plan_1,
            ],
        )
        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&content_type__in=careplan"
                f"&careplan__autocreated_from_id__in={care_plan_template_1.id},{care_plan_template_2.id}"
            ),
            count=2,
            results=[
                care_plan_1,
                care_plan_2,
            ],
        )
        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&content_type__in=careplan"
                f"&careplan__autocreated_from_id__in={care_plan_template_1.id},None"
            ),
            count=2,
            results=[
                care_plan_1,
                care_plan_3,
            ],
        )

    def test_aggregates_with_owner_groups(self):
        create_update_assignee_group_from_user(self.provider)
        create_update_assignee_group_from_user(self.patient)
        category_1 = CaseCategoryFactory()
        case_1 = CaseFactory(
            category=category_1,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        category_2 = CaseCategoryFactory()
        case_2 = CaseFactory(
            category=category_2,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        case_3 = CaseFactory(
            category=category_2,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        provider_task_1 = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=provider_task_1, content_object=case_3, is_parent=True)
        provider_task_2 = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 4, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=provider_task_2, content_object=case_3, is_parent=True)
        other_task = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=other_task, content_object=case_3, is_parent=True)
        care_plan_template_1 = CarePlanTemplateFactory()
        care_plan_1 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_1_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 11, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_1_task, content_object=care_plan_1, is_parent=True)
        care_plan_2 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_2_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 12, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_2_task, content_object=care_plan_2, is_parent=True)
        care_plan_3 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=None,
        )
        care_plan_3_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 13, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_3_task, content_object=care_plan_3, is_parent=True)

        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}",
            count=6,
            aggregates={
                "careplan__autocreated_from": [
                    {
                        "autocreated_from_id": care_plan_template_1.id,
                        "autocreated_from__title": care_plan_template_1.title,
                        "count": 2,
                    },
                    {
                        "autocreated_from_id": None,
                        "autocreated_from__title": None,
                        "count": 1,
                    },
                ],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
                case_2,
                case_3,
                care_plan_1,
                care_plan_2,
                care_plan_3,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&content_type__in=case&case__category_id__in={category_1.id}"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [
                    {
                        "autocreated_from_id": care_plan_template_1.id,
                        "autocreated_from__title": care_plan_template_1.title,
                        "count": 2,
                    },
                    {
                        "autocreated_from_id": None,
                        "autocreated_from__title": None,
                        "count": 1,
                    },
                ],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                "&due_date__lte=2000-01-01T00:00:00-00:00"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                "&due_date__lte=2000-01-10T00:00:00-00:00"
            ),
            count=3,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
                case_2,
                case_3,
            ],
        )

    def test_aggregates_with_pods(self):
        create_update_assignee_group_from_user(self.provider)
        create_update_assignee_group_from_user(self.patient)
        pod = PodFactory()
        self.patient.person.pods.set([pod])
        self.provider.providerdetail.pods.set([pod])
        category_1 = CaseCategoryFactory()
        case_1 = CaseFactory(
            person=self.patient.person,
            category=category_1,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        category_2 = CaseCategoryFactory()
        case_2 = CaseFactory(
            person=self.patient.person,
            category=category_2,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        case_3 = CaseFactory(
            person=self.patient.person,
            category=category_2,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        provider_task_1 = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=provider_task_1, content_object=case_3, is_parent=True)
        provider_task_2 = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 4, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=provider_task_2, content_object=case_3, is_parent=True)
        other_task = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.patient.assignee_group,
            due_date=datetime(2000, 1, 3, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=other_task, content_object=case_3, is_parent=True)
        care_plan_template_1 = CarePlanTemplateFactory()
        care_plan_1 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_1_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 11, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_1_task, content_object=care_plan_1, is_parent=True)
        care_plan_2 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=care_plan_template_1,
        )
        care_plan_2_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 12, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_2_task, content_object=care_plan_2, is_parent=True)
        care_plan_3 = CarePlanFactory(
            patient=self.patient,
            autocreated_from=None,
        )
        care_plan_3_task = Task.objects.create(
            person=self.patient.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 13, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=care_plan_3_task, content_object=care_plan_3, is_parent=True)

        self.provider_client.get(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}&pod_id__in={pod.id}"
        )
        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}&pod_id__in={pod.id}",
            count=6,
            aggregates={
                "careplan__autocreated_from": [
                    {
                        "autocreated_from_id": care_plan_template_1.id,
                        "autocreated_from__title": care_plan_template_1.title,
                        "count": 2,
                    },
                    {
                        "autocreated_from_id": None,
                        "autocreated_from__title": None,
                        "count": 1,
                    },
                ],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
                case_2,
                case_3,
                care_plan_1,
                care_plan_2,
                care_plan_3,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&content_type__in=case&case__category_id__in={category_1.id}&pod_id__in={pod.id}"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [
                    {
                        "autocreated_from_id": care_plan_template_1.id,
                        "autocreated_from__title": care_plan_template_1.title,
                        "count": 2,
                    },
                    {
                        "autocreated_from_id": None,
                        "autocreated_from__title": None,
                        "count": 1,
                    },
                ],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&due_date__lte=2000-01-01T00:00:00-00:00&pod_id__in={pod.id}"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
            ],
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&due_date__lte=2000-01-10T00:00:00-00:00&pod_id__in={pod.id}"
            ),
            count=3,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_1,
                case_2,
                case_3,
            ],
        )

        person = PersonUserFactory.create()
        create_update_assignee_group_from_user(self.patient)
        pod2 = PodFactory()
        person.pods.set([pod2])
        self.provider.providerdetail.pods.set([pod2])
        category_4 = CaseCategoryFactory()
        case_4 = CaseFactory(
            person=person,
            category=category_4,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )

        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&pod_id__in={pod2.id}"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_4.id,
                        "category__title": category_4.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_4,
            ],
        )

        person2 = PersonUserFactory.create()
        category_5 = CaseCategoryFactory()
        case_5 = CaseFactory(
            person=person2,
            category=category_5,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        form_4 = FormFactory()
        form_submission_4 = FormSubmissionFactory(
            form=form_4,
        )
        form_submission_4_task = Task.objects.create(
            person=person2,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 8, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=form_submission_4_task, content_object=form_submission_4, is_parent=True)
        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&pod_id__in={pod.id},{pod2.id},{0}"
            ),
            count=8,
            aggregates={
                "careplan__autocreated_from": [
                    {
                        "autocreated_from_id": care_plan_template_1.id,
                        "autocreated_from__title": care_plan_template_1.title,
                        "count": 2,
                    },
                    {
                        "autocreated_from_id": None,
                        "autocreated_from__title": None,
                        "count": 1,
                    },
                ],
                "case__category": [
                    {
                        "category_id": category_2.id,
                        "category__title": category_2.title,
                        "count": 2,
                    },
                    {
                        "category_id": category_1.id,
                        "category__title": category_1.title,
                        "count": 1,
                    },
                    {
                        "category_id": category_4.id,
                        "category__title": category_4.title,
                        "count": 1,
                    },
                    {
                        "category_id": category_5.id,
                        "category__title": category_5.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_5,
                case_4,
                case_1,
                case_2,
                case_3,
                care_plan_1,
                care_plan_2,
                care_plan_3,
            ],
        )
        self.assert_response(
            (
                f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}"
                f"&pod_id__in={0}"
            ),
            count=1,
            aggregates={
                "careplan__autocreated_from": [],
                "case__category": [
                    {
                        "category_id": category_5.id,
                        "category__title": category_5.title,
                        "count": 1,
                    },
                ],
            },
            results=[
                case_5,
            ],
        )

    def test_order_by_due_date_and_priority(self):
        # For cases with the same due date, priority should break the tie.
        case_1 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_1.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            priority=2,
        )
        TaskRelation.objects.create(task=task, content_object=case_1, is_parent=True)
        case_2 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_2.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            priority=1,
        )
        TaskRelation.objects.create(task=task, content_object=case_2, is_parent=True)
        case_3 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=case_3, is_parent=True)

        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=due_date&owner_group_id__in={self.provider.assignee_group.id}",
            count=3,
            results=[
                case_1,
                case_2,
                case_3,
            ],
        )

    def test_order_by_priority(self):
        # For cases with the same priority, due date should break the tie.
        case_1 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_1.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            priority=2,
        )
        TaskRelation.objects.create(task=task, content_object=case_1, is_parent=True)
        case_2 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_2.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            priority=2,
        )
        TaskRelation.objects.create(task=task, content_object=case_2, is_parent=True)
        case_3 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_3.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            priority=1,
        )
        TaskRelation.objects.create(task=task, content_object=case_3, is_parent=True)
        case_4 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_4.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            priority=1,
        )
        TaskRelation.objects.create(task=task, content_object=case_4, is_parent=True)
        case_5 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_5.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 1, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=case_5, is_parent=True)
        case_6 = CaseFactory(
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
            status=CaseStatus.IN_PROGRESS,
        )
        task = Task.objects.create(
            person=case_6.person,
            is_complete=False,
            owner_group=self.provider.assignee_group,
            due_date=datetime(2000, 1, 2, 0, 0, 0, 0, timezone.utc),
        )
        TaskRelation.objects.create(task=task, content_object=case_6, is_parent=True)

        self.assert_response(
            f"/providers/me/work_units/inbox/?sort_by=priority&owner_group_id__in={self.provider.assignee_group.id}",
            count=6,
            results=[
                case_1,
                case_2,
                case_3,
                case_4,
                case_5,
                case_6,
            ],
        )

    def test_num_queries(self):
        patient = self.patient
        person = patient.person
        category = CaseCategory.objects.create(title="Mystery")
        for _ in range(3):
            care_plan = CarePlan.objects.create(patient=patient)
            for _ in range(3):
                task = Task.objects.create(patient=patient, person=person, owner_group=self.provider.assignee_group)
                TaskRelation.objects.create(task=task, content_object=care_plan, is_parent=True)
            for _ in range(3):
                case = Case.objects.create(
                    person=person,
                    category=category,
                    owner_group=self.provider.assignee_group,
                    status=CaseStatus.IN_PROGRESS,
                )
                for _ in range(3):
                    task = Task.objects.create(patient=patient, person=person, owner_group=self.provider.assignee_group)
                    TaskRelation.objects.create(task=task, content_object=case, is_parent=True)
                for _ in range(3):
                    email = EmailFactory()
                    # each email can have more than 1 attachments
                    for _ in range(3):
                        EmailAttachmentFactory(email=email)
                    CaseRelation.objects.create(case=case, content_object=email)
        for _ in range(3):
            case = Case.objects.create(
                person=person,
                category=category,
                owner_group=self.provider.assignee_group,
                status=CaseStatus.IN_PROGRESS,
            )
            for _ in range(3):
                task = Task.objects.create(patient=patient, person=person, owner_group=self.provider.assignee_group)
                TaskRelation.objects.create(task=task, content_object=case, is_parent=True)
        for _ in range(3):
            form = FormFactory()
            form_submission = FormSubmission.objects.create(form=form, user=self.patient)
            for _ in range(3):
                task = Task.objects.create(
                    patient=self.patient,
                    title="Form task",
                    owner_group=self.provider.assignee_group,
                )
                TaskRelation.objects.create(task=task, content_object=form_submission, is_parent=True)
                for _ in range(3):
                    appointment = AppointmentFactory.create()
                    TaskRelation.objects.create(task=task, content_object=appointment, is_parent=False)
        insurance = person.insurance_info
        for _ in range(3):
            task = Task.objects.create(
                patient=self.patient,
                title="Insurance task",
                owner_group=self.provider.assignee_group,
            )
            TaskRelation.objects.create(task=task, content_object=insurance, is_parent=True)

        # Normally, we'd use self.provider_client.get(...) to test this, going through the client
        # issues some additional queries unrelated to data fetching. To isolate just the queries we
        # care about, we access the view directly.
        view = InboxWorkUnitListView.as_view()
        factory = APIRequestFactory()
        request = factory.get(
            f"/providers/me/work_units/inbox/?sort_by=priority&owner_group_id__in={self.provider.assignee_group.id}",
            format="json",
        )
        force_authenticate(request, user=self.provider)

        # Ensure cache is cleared for test isolation.
        ContentType.objects.clear_cache()

        # Here's how the queries add up:
        #    1  to warm up the content types cache
        #    1  to count the total number of inbox work units
        #    1  to fetch the inbox work units
        #    1    to prefetch insurance member info
        #    1      to prefetch persons
        #    1      to prefetch task relations with task, creator, owner
        #    1        to prefetch owner group users
        #    1    to prefetch cases
        #    1      to prefetch categories
        #    1      to prefetch owner_groups
        #    1      to prefetch persons
        #    1      to prefetch tags
        #    1      to prefetch task relations with task, creator, owner
        #    1        to prefetch owner group users
        #    1    to prefetch care plans
        #    1      to prefetch patients with person
        #    1      to prefetch task relations with task, creator, owner
        #    1        to prefetch owner group users
        #    1  to fetch the care plans aggregate
        #    1  to fetch the cases aggregate
        # ----
        #   20
        with self.assertNumQueries(20):
            response = view(request)
            response.render()

            self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")

    def test_care_plan_task_owner(self):
        patient = self.patient
        person = patient.person

        care_plan = CarePlan.objects.create(patient=patient)
        patient_task = Task.objects.create(patient=patient, person=person, owner_group=patient.assignee_group)
        TaskRelation.objects.create(task=patient_task, content_object=care_plan, is_parent=True)
        provider_task = Task.objects.create(patient=patient, person=person, owner_group=self.provider.assignee_group)
        TaskRelation.objects.create(task=provider_task, content_object=care_plan, is_parent=True)

        # Patient task should not show up in the inbox
        response_json = self.provider_client.get(
            f"/providers/me/work_units/inbox/?sort_by=priority&owner_group_id__in={self.provider.assignee_group.id}"
        ).json()

        self.assertEqual(response_json["count"], 1)
        response_care_plan = response_json["results"][0]
        self.assertEqual(response_care_plan["content_type"], "careplan")
        self.assertEqual(response_care_plan["object_id"], care_plan.id)
        items = response_care_plan["items"]
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]["content_type"], "task")
        self.assertEqual(items[0]["object_id"], provider_task.id)

    def test_case_with_appointment_case_relation(self):
        person = PersonUserFactory(
            preferred_language=PreferredLanguageFactory(name="Other"), preferred_language_other="Test Language"
        )

        provider = ProviderDetailFactory()
        physician = provider.physician
        practice = physician.practice

        category = CaseCategoryFactory(unique_key=CASE_CATEGORY_UNIQUE_KEY_FOR_NON_ENGLISH_APPOINTMENT)

        appointment = AppointmentFactory(
            patient_id=person.user.id,
            physician_id=physician.id,
            practice_id=practice.id,
            start=datetime.now(timezone.utc) + timedelta(days=10),
            duration="00:15:00",
            description="Strained back",
            reason=AppointmentReason.VIDEO,
            time_slot_type="appointment",
            status=AppointmentStatus.SCHEDULED.value,
        )

        case = Case.objects.create(
            person=person, category=category, description=person.preferred_language_other, status=CaseStatus.IN_PROGRESS
        )

        CaseRelation.objects.create(
            case=case, content_type=ContentType.objects.get_for_model(Appointment), object_id=appointment.id
        )

        response = self.provider_client.get("/providers/me/work_units/inbox/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        response_json = response.json()
        response_case = next(
            result_json
            for result_json in response_json["results"]
            if (result_json["object_id"] == case.id and result_json["content_type"] == "case")
        )
        self.assertEqual(response_case["content_object"]["description"], person.preferred_language_other)

    def test_case_with_ingestion_job_case_relation(self):
        person = PersonUserFactory()
        category = CaseCategoryFactory()
        ingestion_job = ScheduleIngestionJobFactory.create()
        case = Case.objects.create(person=person, category=category, status=CaseStatus.IN_PROGRESS)
        CaseRelation.objects.create(
            case=case, content_type=ContentType.objects.get_for_model(ScheduleIngestionJob), object_id=ingestion_job.id
        )

        response = self.provider_client.get("/providers/me/work_units/inbox/")
        self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
        response_json = response.json()
        response_case = next(
            result_json
            for result_json in response_json["results"]
            if (result_json["object_id"] == case.id and result_json["content_type"] == "case")
        )
        self.assertIsNotNone(response_case)


class ReassignWorkUnitOwnerUsingAssignmentSchemeTestCase(FireflyTestCase):
    def test_command(self):
        # Test that Work Units assigned to some provider
        # can be reassigned to a member's NP
        np_group, _ = Group.objects.get_or_create(name=NP_ROLE)
        np_provider = ProviderDetailFactory()
        np_group.user_set.add(np_provider.user)
        self.patient.person.care_team.set([np_provider.user.id, ProviderDetailFactory().user.id])
        task = TaskFactory(patient=self.patient, person=self.patient.person)
        assignee_group = task.owner_group
        case = CaseFactory(person=self.patient.person, owner_group=assignee_group)

        other_task = TaskFactory(patient=self.patient, person=self.patient.person)
        other_task_original_assignee_group = other_task.owner_group

        work_units = [task, case]

        management.call_command(
            "reassign_work_unit_owner_using_assignment_scheme",
            current_assignee_group_id=assignee_group.id,
            assignment_scheme=AssignmentScheme.ASSIGN_TO_ROLE_IN_CARE_TEAM,
            group_id=np_group.id,
            states_of_residence=self.patient.person.insurance_info.state,
            limit=100,
            dry_run_off=True,
            user=self.provider,
        )

        for work_unit in work_units:
            work_unit.refresh_from_db()
            self.assertEqual(
                work_unit.owner_group, np_provider.user.assignee_group, f"Unexpected AssigneeGroup for {work_unit}"
            )

        self.assertEqual(other_task.owner_group, other_task_original_assignee_group)
