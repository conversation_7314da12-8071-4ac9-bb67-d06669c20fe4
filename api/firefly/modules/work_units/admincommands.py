from django import forms

from firefly.core.assignment.constants import ASSIGNMENT_SCHEME_CHOICES
from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand


class ReassignWorkUnitOwnerUsingAssignmentScheme(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True

        current_assignee_group_id = forms.IntegerField(
            required=True, help_text="Assignee Group whose tasks should be reassigned"
        )
        assignment_scheme = forms.ChoiceField(
            choices=ASSIGNMENT_SCHEME_CHOICES,
            required=True,
            help_text="Assignment scheme rules to use for reassignment",
        )
        group_id = forms.IntegerField(required=False, help_text="Group for assignment logic")
        default_assignee_group_id = forms.IntegerField(
            required=False, help_text="Default AssigneeGroup for assignment logic"
        )
        states_of_residence = forms.CharField(
            required=False,
            help_text=(
                "Comma-separated, two-letter abbreviations e.g. 'MA'."
                + " Useful for filtering to only a subset of patients, for licensing rules"
            ),
        )
        limit = forms.Integer<PERSON>ield(required=False, help_text="Max number of Tasks to reassign")

    def get_command_arguments(self, data, user):
        opts = [
            "--current_assignee_group_id",
            data["current_assignee_group_id"],
            "--assignment_scheme",
            data["assignment_scheme"],
        ]

        for form_field_name in ["group_id", "default_assignee_group_id", "limit", "states_of_residence"]:
            if data.get(form_field_name):
                opts.append(f"--{form_field_name}")
                opts.append(data[form_field_name])

        return opts, {
            "user": user,
        }
