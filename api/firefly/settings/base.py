"""
Django settings for firefly project.

Generated by 'django-admin startproject' using Django 2.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""

import os
from datetime import time, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Tuple, Union

from corsheaders.defaults import default_headers

from firefly.core.strutils import strtobool
from firefly.core.user.constants import RiskScore
from firefly.modules.appointment.types import AppointmentSlotReleaseConfig

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.0/howto/deployment/checklist/

# SECURITY WARNING: don't run with debug turned on in production!!
DEBUG = False
IS_TESTING = False

ENABLE_ADMIN = strtobool(os.getenv("ENABLE_ADMIN", default="0"))

# Django 3.2 upgrade request definition of the autofield
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# Application definition

INSTALLED_APPS = [
    # Must be before Django modules to ensure we can override default commands
    "firefly.modules.firefly_django",
    "pghistory.admin",  # Must be before django.contrib.admin. See https://django-pghistory.readthedocs.io/en/3.2.0/admin/
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.postgres",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.gis",
    "cachalot",
    "pgtrigger",
    "pghistory",
    "safedelete",
    "django_extensions",
    "django_s3_storage",
    "waffle",
    "rest_framework",
    "rest_framework_elasticsearch",
    "django_filters",
    "imagekit",
    "django_dramatiq",
    "django_migration_linter",
    "firefly.bff.marketing_website",
    "firefly.core.alias",
    "firefly.core.audit",
    "firefly.core.feature",
    "firefly.core.permissions",
    "firefly.core.phi_access_control",
    "firefly.core.redact_phi",
    "firefly.core.roles",
    "firefly.core.services.elation",
    "firefly.core.services.zus",
    "firefly.core.services.talon",
    "firefly.core.services.auto_eligibility_service",
    "firefly.core.services.braze",
    "firefly.core.services.employer_partner_import_service",
    "firefly.core.services.flume",
    "firefly.core.services.jumpcloud",
    "firefly.core.services.pubnub",
    "firefly.core.services.dramatiq",
    "firefly.core.services.twilio",
    "firefly.core.services.zingle",
    "firefly.core.startup",
    "firefly.core.user",
    "firefly.modules.accumulators",
    "firefly.modules.announcements",
    "firefly.modules.appointment",
    "firefly.modules.attribution",
    "firefly.modules.auto_eligibility",
    "firefly.modules.care_plan",
    "firefly.modules.care_teams",
    "firefly.modules.pods",
    "firefly.modules.cases",
    "firefly.modules.change_data_capture",
    "firefly.modules.chat_message",
    "firefly.modules.claims",
    "firefly.modules.client_log",
    "firefly.modules.clinic_hours",
    "firefly.modules.code_systems",
    "firefly.modules.communications",
    "firefly.modules.content",
    "firefly.modules.data_events",
    "firefly.modules.data_feeds",
    "firefly.modules.data_pipelines",
    "firefly.modules.demo",
    "firefly.modules.device",
    "firefly.modules.devops",
    "firefly.modules.documents",
    "firefly.modules.education_chassis",
    "firefly.modules.eligibility",
    "firefly.modules.email",
    "firefly.modules.escalated_message",
    "firefly.modules.events",
    "firefly.modules.experiments",
    "firefly.modules.facts",
    "firefly.modules.fake_data",
    "firefly.modules.features",
    "firefly.modules.file_transfer",
    "firefly.modules.forms",
    "firefly.modules.health_plan",
    "firefly.modules.insurance",
    "firefly.modules.journeys",
    "firefly.modules.macros",
    "firefly.modules.mailers",
    "firefly.modules.network",
    "firefly.modules.notifications",
    "firefly.modules.notes",
    "firefly.modules.onboarding",
    "firefly.modules.patient_referral_programs",
    "firefly.modules.physician",
    "firefly.modules.risk_capture",
    "firefly.modules.signup_reasons",
    "firefly.modules.states",
    "firefly.modules.phone_calls",
    "firefly.modules.programs",
    "firefly.modules.programs.primary_care",
    "firefly.modules.programs.rtw",
    "firefly.modules.practice",
    "firefly.modules.quality",
    "firefly.modules.referral",
    "firefly.modules.settings",
    "firefly.modules.statemachines",
    "firefly.modules.schedule",
    "firefly.modules.statemachines.coverage",
    "firefly.modules.statemachines.insurance_plan_review",
    "firefly.modules.talkdesk",
    "firefly.modules.tasks",
    "firefly.modules.tenants",
    "firefly.modules.visit_ratings",
    "firefly.modules.worklists",
    "firefly.modules.work_units",
    "firefly.modules.firefly_django.fireflyadmincommand",
    "firefly.modules.billing",
    "firefly.modules.bulletin_board",
    "firefly.modules.segmentation",
    "firefly.modules.csp_report",
    "corsheaders",
    "drf_spectacular",
    "generic_relations",
    "mozilla_django_oidc",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    # corsheaders must be above all Response oriented middleware
    "corsheaders.middleware.CorsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.auth.middleware.RemoteUserMiddleware",
    "pghistory.middleware.HistoryMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "waffle.middleware.WaffleMiddleware",
    "firefly.core.audit.middleware.RequestLoggingMiddleware",
    "firefly.core.audit.middleware.PHIAuditLogMiddleware",
    "firefly.core.utils.headers.ContentSecurityPolicyMiddleware",
    "firefly.modules.tenants.middleware.TenancyMiddleware",
    "firefly.modules.tenants.middleware.TenantAccessMiddleware",
    "mozilla_django_oidc.middleware.SessionRefresh",
]

ROOT_URLCONF = "firefly.urls"

SPECTACULAR_SETTINGS = {
    "TITLE": "Firefly API",
    "DESCRIPTION": "Lucian",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "SERVE_PERMISSIONS": ["rest_framework.permissions.IsAuthenticated"],
    "LICENSE": {"name": "Private"},
    "CONTACT": {"email": "<EMAIL>"},
    "SCHEMA_PATH_PREFIX": "/",
    "ENUM_ADD_EXPLICIT_BLANK_NULL_CHOICE": False,
}

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR + "/templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "firefly.core.admin.admin_context",
            ]
        },
    }
]

WSGI_APPLICATION = "firefly.wsgi.application"

# Password validation
# https://docs.djangoproject.com/en/2.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [{"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"}]

# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/
STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = "/api-static/"

AUTH_USER_MODEL = "user.User"

# Custom Authentication Backend
# https://docs.djangoproject.com/en/2.1/topics/auth/customizing/#specifying-authentication-backends
AUTHENTICATION_BACKENDS = [
    "firefly.core.user.auth.auth_backend.OIDCAuthenticationBackend",
    "firefly.core.user.auth.auth_backend.Auth0Backend",
    "firefly.core.user.auth.auth_backend.BiometricLoggingBackend",
    "firefly.core.user.auth.auth_backend.PasswordLoggingBackend",
]
REQUIRE_PATIENT_VERIFICATION = True
ENABLE_PUBNUB_CHANNEL_CREATION: bool = True

# Cache Config
# https://docs.djangoproject.com/en/2.1/topics/cache/#django-s-cache-framework
CACHES: Dict[str, Any] = {
    "default": {"BACKEND": "django.core.cache.backends.locmem.LocMemCache"},
    "redis": {
        "BACKEND": "django_redis.cache.RedisCache",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": "",
        },
    },
}

# Rest Framework
# http://www.django-rest-framework.org/api-guide/permissions/#setting-the-permission-policy
# http://getblimp.github.io/django-rest-framework-jwt/#usage

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "firefly.core.services.auth0.legacy.LegacyJSONWebTokenAuthentication",
        "rest_framework_jwt.authentication.JSONWebTokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "EXCEPTION_HANDLER": "firefly.core.exception_handler.firefly_exception_handler",
    "DEFAULT_SCHEMA_CLASS": "firefly.bff.utils.BffAutoSchema",
    "DEFAULT_THROTTLE_CLASSES": [
        "firefly.modules.firefly_django.throttling.FireflyAnonRateThrottle",
        "firefly.modules.firefly_django.throttling.FireflyUserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "250/minute",
        "user": "350/minute",
        "warning": "100/minute",
        "ip": "30/hour",
        "nat": "50/hour",
    },
}

LEGACY_JWT_AUTH = {
    "JWT_EXPIRATION_DELTA": timedelta(minutes=30),
    "JWT_ALLOW_REFRESH": True,
    "JWT_REFRESH_EXPIRATION_DELTA": timedelta(hours=24),
    "JWT_AUTH_HEADER_PREFIX": "Bearer",
}

JWT_AUTH = {
    "JWT_PAYLOAD_GET_USERNAME_HANDLER": "firefly.core.services.auth0.utils.jwt_get_username_from_payload_handler",  # noqa
    "JWT_DECODE_HANDLER": "firefly.core.services.auth0.utils.jwt_decode_token",
    "JWT_ALGORITHM": "RS256",
    "JWT_EXPIRATION_DELTA": timedelta(minutes=30),
    "JWT_ALLOW_REFRESH": True,
    "JWT_REFRESH_EXPIRATION_DELTA": timedelta(hours=24),
    "JWT_AUTH_HEADER_PREFIX": "Bearer",
}

# This setting should only be setup for demo patients in a demo environment
# as of this writing, STAGING
DEMO_PATIENTS: List[int] = []

# A dictionary containing the settings for all storages to be used with Django
# https://docs.djangoproject.com/en/4.2/ref/settings/#std-setting-STORAGES
# Store files in S3
# https://github.com/etianen/django-s3-storage
STORAGES = {
    "default": {"BACKEND": "django_s3_storage.storage.S3Storage"},
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# Appointment Settings
NEXT_AVAILABLE_SLOT_PERIOD = timedelta(weeks=5)  # Limits range of search
NEXT_AVAILABLE_SLOT_PERIOD_FOR_PLAN_ELECT_PERIOD = timedelta(weeks=8)
# Makes video visits within the BUFFER window unavailable
NEXT_AVAILABLE_VIDEO_VISIT_BUFFER = timedelta(minutes=30)
NEXT_AVAILABLE_VIDEO_VISIT_BUFFER_FOR_FF_STAFF = timedelta(minutes=15)

# This is used to show Video-New appoitments for booking based on the risk score.
# Ex: HIGH - 2 days. It shows appointments that are greater than 2 days(24hrs) from now.
APPOINTMENT_START_TIME_BY_RISK_SCORE = {
    RiskScore.HIGH: timedelta(hours=1),
    RiskScore.INTERMEDIATE: timedelta(hours=1),
    RiskScore.LOW: timedelta(hours=1),
}
# Used for releasing appointment slots in batches.
APPOINTMENT_SLOT_RELEASE_CONFIGS: List[AppointmentSlotReleaseConfig] = [
    {
        "start": timedelta(days=0),
        "end": timedelta(days=7),
        "capacity": 100,
        "sub_range": None,
        "sub_range_increment": None,
    },
    {
        "start": timedelta(days=8),
        "end": timedelta(weeks=13),
        "capacity": 100,
        "sub_range": timedelta(days=7),
        "sub_range_increment": timedelta(days=1),
    },
]

SEND_RESCHEDULE_NOTIFICATION = False

# Chat Message Settings
SEND_CHAT_PUSH = True

# Base URL for iOS Universal Links / Android App Link
# Domain needs to host apple-app-site-association and assetlinks.json
# iOS Doc https://developer.apple.com/library/archive/documentation/General/Conceptual/AppSearch/UniversalLinks.html  # noqa
# Android Doc https://developer.android.com/training/app-links/verify-site-associations
APP_LINK_URL = "https://m.firefly.health"

NUM_CORES = 1

DATADOG_HEADERS = [
    "x-datadog-trace-id",
    "x-datadog-parent-id",
    "x-datadog-origin",
    "x-datadog-sampling-priority",
]
AWS_WAF_CAPTCHA_HEADERS = [
    "x-aws-waf-token",
]
LUCIAN_USER_TENANT_HEADER = "lucian-user-tenant"
# HTTP header names are transformed by Django
# https://docs.djangoproject.com/en/4.0/ref/request-response/#django.http.HttpRequest.META
LUCIAN_USER_TENANT_HEADER_TRANSFORMED = "HTTP_LUCIAN_USER_TENANT"
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_HEADERS = (
    list(default_headers) + ["cache-control", LUCIAN_USER_TENANT_HEADER] + DATADOG_HEADERS + AWS_WAF_CAPTCHA_HEADERS
)

TASK_AUTOMATION: Dict[str, Union[str, int]] = {}

NOTEBOOK_ARGUMENTS = [
    "--ip=0.0.0.0",
    "--port=8889",
    "--no-browser",
    "--allow-root",
    "--NotebookApp.token=''",
]

# The bot's phone number is currently the clinic phone # on the website.
LUCI_BOT_PHONE_NUMBER = "**********"
ONBOARDING_EXPERIMENTS = {"EMMA": LUCI_BOT_PHONE_NUMBER}

# Dramatiq -- Async tasks
DRAMATIQ_BROKER: Dict[str, Any] = {
    "BROKER": "dramatiq.brokers.rabbitmq.RabbitmqBroker",
    "OPTIONS": {},
    "MIDDLEWARE": [
        "dramatiq.middleware.AgeLimit",
        "dramatiq.middleware.TimeLimit",
        "dramatiq.middleware.Callbacks",
        "dramatiq.middleware.Retries",
        "firefly.core.services.dramatiq.middleware.retries.RetriableExceptionRetries",
        "firefly.core.services.dramatiq.middleware.retries.RateLimitExceptionRetries",
        "django_dramatiq.middleware.DbConnectionsMiddleware",
        "firefly.core.services.dramatiq.middleware.pghistory.PGHistory",
    ],
}

RUN_SCHEDULER = bool(strtobool(os.getenv("RUN_SCHEDULER", "0")))

ALERTS_EMAIL_ADDRESS = "Firefly Health <<EMAIL>>"
REPLY_TO_EMAIL_ADDRESS = "<EMAIL>"
ALERTS_BCC_EMAIL_ADDRESSES: List[str] = []
AWS_SES_CONFIGURATION_SET = "SESConfigurationSet"

WAFFLE_FLAG_MODEL = "feature.Flag"
WAFFLE_SWITCH_MODEL = "feature.Switch"
WAFFLE_SAMPLE_MODEL = "feature.Sample"
# Waffle has an option to create a model at the time that it is queried with
# `waffle.{model}_is_active()` if it does not exist. Do not opt in to this
# behavior. Audited models require an authenticated user for any write
# operations, so opting in will break valid unauthenticated queries. Queries for
# missing models will still return the default False value.
WAFFLE_CREATE_MISSING_FLAGS = False
WAFFLE_CREATE_MISSING_SWITCHES = False
WAFFLE_CREATE_MISSING_SAMPLES = False
# Use a centralized cache to keep different instances of the service in sync.
WAFFLE_CACHE_NAME = "redis"

ONBOARDING_GROUPNAME = "onboarding"

WORKLIST_SYNC_ENABLED = False
RUN_WORKLIST_COMMANDS_IN_MIGRATIONS = False
# User id that is used to set the created by/ updated by fields
# during system actions for worklists
# Replace with a valid user id
WORKLIST_SYNC_USER_ID = 0

NEW_APPOINTMENT_BUFFER = timedelta(hours=1)
NEW_APPOINTMENT_CUTOFF_TIME = {
    "AM": time(hour=12),  # 8AM EDT
    "PM": time(hour=19),  # 3PM EDT
}
NEW_APPOINTMENT_RESTART_TIME = time(hour=15)  # 11AM EDT

# Ignore non-unique phone_number field warning on User model
SILENCED_SYSTEM_CHECKS = ["auth.W004"]

AUTO_ELIGIBILITY_CHECK_ON_PLAN_REVIEW_RATE_LIMIT: int = 1
MAILER_STATUS_UPDATE_RATE_LIMIT: int = 5

# True if running as part of mypy type checking, which short-circuits some external dependencies in
# app setup.
TYPE_CHECKING = False

ALLOW_LOGOUT_GET_METHOD = True

NETWORK_DIRECTORY = {
    # Knobs to control specific parts of the backfill process
    # A data refresh request is limited to a zip code and an insurance network combination
    "REQUEST_DATA_REFRESH_RATE_LIMIT": 1,
    # Rate limit outbound ribbon requests within a window
    "DATA_REFRESH_REQUEST_RIBBON_RATE_LIMIT": 10,
    "DATA_REFRESH_REQUEST_RIBBON_RATE_LIMIT_DURING_PEAK_HOURS": 10,
    "RIBBON_REQUEST_PAGE_SIZE": 100,
    # Rate limit data storage requests within a window
    "SOURCE_DATA_STORAGE_RATE_LIMIT": 16,
    "SOURCE_DATA_STORAGE_RATE_LIMIT_DURING_PEAK_HOURS": 10,
    "CONVERSION_RATE_LIMIT": 4,
    "CONVERSION_RATE_LIMIT_DURING_PEAK_HOURS": 2,
    "BACKFILL_RATE_LIMIT": 2,
    "CURATED_PROVIDER_INGEST_BATCH_SIZE": 100,
}

CACHALOT_ENABLED = True
# Use local mem cache to avoid a roundtrip to redis
CACHALOT_CACHE = "default"
# Number of seconds during which the cache should consider data as valid
# See https://django-cachalot.readthedocs.io/en/latest/quickstart.html#cachalot-timeout
CACHALOT_TIMEOUT = 3600

CACHALOT_ONLY_CACHABLE_TABLES = [
    "facts_humanlanguage",
    "facts_specialty_group",
    "facts_specialty_specialty_groups",
    "facts_specialty",
    "facts_location_type",
    "insurance_network",
    "onboarding_churneddischargedreason",
    "state",
    "worklist",
    "worklist_field",
    "tenants_tenant",
]

WAIVER_TRANSMISSION_RATE_LIMIT_PER_WINDOW = 1

SAVE_REFERRAL_CONCURRENT_RATE_LIMIT = 1

ACCOUNT_MATCHING_CONCURRENT_RATE_LIMIT = 1

UPDATE_REFERRAL_LETTER_DEPENDENT_MODELS_CONCURRENT_RATE_LIMIT = 1

INGEST_SCHEDULE_DATA_RATE_LIMIT = 1

RELEASE_UNBOOKED_SLOTS_RATE_LIMIT = 1

PROMOTE_SCHEDULE_DATA_RATE_LIMIT = 1

LUCIAN_SLOT_GENERATOR_RATE_LIMIT = 1

DELETE_LUCIAN_SLOT_RATE_LIMIT = 1

EMAIL_PROCESSING_CONCURRENT_RATE_LIMIT = 1

# Used by SecurityMiddleware to set the Strict-Transport-Security header on all
# HTTPS responses.
# https://docs.djangoproject.com/en/3.2/ref/settings/#std-setting-SECURE_HSTS_SECONDS
SECURE_HSTS_SECONDS = 63_072_000  # 2 years, the minimum required to be on the preload list
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
# AWS load balancer sets X-Forwarded-Proto to match what protocol the client
# used to connect.
# https://docs.aws.amazon.com/elasticloadbalancing/latest/application/x-forwarded-headers.html#x-forwarded-proto
SECURE_PROXY_SSL_HEADER: Optional[Tuple[str, str]] = ("HTTP_X_FORWARDED_PROTO", "https")

CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True

SESSION_COOKIE_SECURE = True

# Allow outbound HTTP requests by default. Override this explicitly in specific
# environments.
OUTBOUND_HTTP_REQUESTS_POLICY = os.getenv("LUCIAN_OUTBOUND_HTTP_REQUESTS_POLICY", default="allow")

PDF_EXPORT_BASE_URL = os.getenv("LUCIAN_PDF_EXPORT_BASE_URL")

# VENDOR INTEGRATIONS

ELASTICSEARCH_USERNAME: Optional[str] = None
ELASTICSEARCH_PASSWORD: Optional[str] = None

ELASTICSEARCH_DEBUG = False
ELASTICSEARCH_TIMEOUT = 120

FLUME = {
    "PATH_TO_KEY": "flume-personal-keys.json",
    "API_BASE": "https://api-sandbox.flumehealth.com/v1/",
    "CLIENT_ID": "997168045740-r07q8saed83mlcfkv0s9g6ngt1jmiuuh.apps.googleusercontent.com",
}
FLUME_TEST = False

LOOKER_DEBUG = False
LOOKER_ROW_LIMIT = 2500
LOOKER_CONFIG = {
    "base_url": "fizzbuzz",
    "client_id": "fizzbuzz",
    "client_secret": "fizzbuzz",  # pragma: allowlist secret
    "verify_ssl": "True",
    "timeout": 180,
}

OPSGENIE_TEST = False
OPSGENIE_TEAM = {"LUCIAN_OPERATIONS": "TEST_TEAM", "LUCIAN_CLINICIANS": "TEST_TEAM"}

RIBBON_BASE_URL = "https://api.ribbonhealth.com/v1/custom/"
RIBBON_TIMEOUT = 20  # The number of seconds to allow for any HTTP request
RIBBON_TEST = False

SMARTY_STREETS_API_RATE_LIMIT = 50
SMARTY_STREETS_AUTH_ID = ""
SMARTY_STREETS_AUTH_TOKEN = ""
SMARTY_STREETS_STREET_ADDRESS_API_URL = "https://us-street.api.smartystreets.com/street-address"
SMARTY_STREETS_STREET_ZIPCODE_API_URL = "https://us-zipcode.api.smartystreets.com/lookup"
SMARTY_STREETS_GEOCODE_ENABLED = False

# When True, will log Twilio client calls to stdout instead of hitting Twilio.
TWILIO_DEBUG = False

ZINGLE_DEBUG = False

# when true, mock response for claims will be returned instead of hitting Flume's claims API.
FLUME_CLAIMS_API_DEBUG = False

# when true, mock response for accumulators will be returned instead of hitting Flume's accumulators API.
FLUME_ACCUMULATORS_API_DEBUG = False

EXPLORE_REPORT_URL = "https://api.talkdeskapp.com/data/reports/explore_calls/jobs"
EXPLORE_CONTACTS_REPORT_URL = "https://api.talkdeskapp.com/data/reports/contacts/jobs"
EXPLORE_RING_ATTEMPTS_REPORT_URL = "https://api.talkdeskapp.com/data/reports/ring_attempts/jobs"

# REDIS Configuration
REDIS_CONFIG = {
    "PORT": 6379,
    "PASSWORD": "",
}

# Contains credentials to connect to opsgenie
OPSGENIE: Dict[str, Any] = {
    "API_BASE": "https://api.opsgenie.com/v2/",
}

SNYK = {
    "ORG_ID": "************************************",
}

OIDC_RP_SIGN_ALGO = "RS256"
OIDC_CREATE_USER = False
USE_LEGACY_ADMIN_LOGIN = False

SLACK_USER_GROUP_IDS = {
    "member-guide": "!subteam^S027G0D3UCA",
}

# HOSTS are restricted by AWS Load Balancer rules
ALLOWED_HOSTS = ["*"]

AWS_REGION = "us-east-1"
AWS_S3_KEY_PREFIX = "api"
# represents the account specific s3 bucket name
# so that we do not need to use an IAM user to
# access a cross account s3 bucket
AWS_ACCOUNT_S3_BUCKET_NAME = ""
AWS_ACCOUNT_APPOINTMENT_RECORDINGS_S3_BUCKET_NAME = ""
AWS_HEALTH_SCRIBE_ROLE = "arn:aws:iam::************:role/HealthScribeRole"

AWS_SESSION_TOKEN = ""

DATA_UPLOAD_MAX_MEMORY_SIZE = ********  # 20MB

# Database
# https://docs.djangoproject.com/en/2.0/ref/settings/#databases
DATABASE_DEFAULT = {
    "NAME": "firefly",
    "PORT": "5432",
    "ENGINE": "django.contrib.gis.db.backends.postgis",
    "OPTIONS": {"sslmode": "require"},
}

ZINGLE = {
    "BASE_URL": "https://api.zingle.me/v1",
    "USERNAME": "<EMAIL>",
}

BRAZE = {
    "INSTANCE": "US-02",
    "SDK_URL": "sdk.iad-02.braze.com",
    "API_URL": "https://rest.iad-02.braze.com",
}

FLUME_SFTP = {
    "HOST": "sftp.flume.health",
    "USERNAME": "firefly",
    "PORT": 22,
}

JIRA = {
    "BASE_URL": "https://fireflyhealth.atlassian.net/rest",
    "USER": "<EMAIL>",
}

ELATION: Dict[str, Any] = {
    "TOKEN_PATH": "/oauth2/token/",
    "USERNAME": "<EMAIL>",
}


AUTH0_DB_CONNECTION = "Django-Database"
AUTH0_LUCIAN_DB_CONNECTION = "Lucian-Database"

TWILIO = {
    "ACCOUNT_SID": "AC4f73d48db050ce72f6a7c65319e472c3",  # pragma: allowlist secret
    # We have created separate campaigns for different purpose.
    "PHONE_NUMBERS": {
        "DEFAULT": "**********",
        # The below phone number will be used to send SMS to user after they create an account on the web portal.
        "DOWNLOAD_APP": "**********",
        # The below phone number will be used to send SMS to provide last minute appointment reminders to
        # provider / care team member guide / health guides.
        "LAST_MIN_APPOINTMENT_REMINDER": "**********",
        # The below phone number will be used to send SMS to provide last minute appointment cancellation notification
        # to provider / care team member guide / health guides.
        "LAST_MIN_APPOINTMENT_CANCELLATION": "**********",
    },
    "PHONE_VERIFICATION": False,
}

# Concurrency rate limit for the functions that create
# onboarding cases.
# We want multiple parallel invocations from different
# worker threads to be possible - but want to limit
# concurrent invocation of these invocations for a single user
# so that we do not attempt to create duplicate cases.
# This is achieved by making the rate limiter key contain
# the user id
# DO NOT CHANGE THIS VALUE
ONBOARDING_CASE_CREATION_RATE_LIMIT_PER_USER: int = 1

# If the user has not crossed the onboarding state: Initialized within this delay,
# from the time of getting into the initialized state,create an onboarding case
# for member guides to follow up with the user
# Set to 60 minutes
ALLOWED_DELAY_FOR_USER_TO_COMPLETE_INITIALIAZED_STATE_IN_MILLISECONDS: int = 60 * 60 * 1000

# If the user has not crossed the onboarding state: Signed Up within this delay,
# from the time of getting into the initialized state,create an onboarding case
# for member guides to follow up with the user
# Set to 90 minutes
ALLOWED_DELAY_FOR_USER_TO_COMPLETE_SIGNED_UP_STATE_IN_MILLISECONDS: int = 90 * 60 * 1000

# If the user has become a member but not booked a visit within this delay,
# from the time of getting into the initialized state,create an onboarding case
# for member guides to follow up with the user
# Set to 120 minutes
ALLOWED_DELAY_FOR_USER_TO_COMPLETE_SCHEDULING_VISIT_IN_MILLISECONDS: int = 120 * 60 * 1000
# Set to 7 days if the user has chosen to book visit later
ALLOWED_DELAY_FOR_USER_TO_COMPLETE_SCHEDULING_VISIT_LATER_IN_MILLISECONDS: int = 7 * 24 * 60 * 60 * 1000

# If the user has become a member but not booked a visit within this delay,
# from the time of getting into the initialized state,create an onboarding appt.
# outreach case
# Set to 25 minutes
ALLOWED_DELAY_FOR_USER_TO_COMPLETE_SCHEDULING_VISIT_AFTER_BECOMING_MEMBER_IN_MILLISECONDS: int = 25 * 60 * 1000

# If the user has marked the update PCP task as complete, we check the eligibility
# of the user after a certain period of time
# Set to 2 days
ALLOWED_DELAY_FOR_CHECKING_USER_PCP_UPDATE_IN_MILLISECONDS: int = 2 * 24 * 60 * 60 * 1000

ZOOM = {
    "API_KEY": "",
    "API_SECRET": "",
    "SDK_KEY": "",
    "SDK_SECRET": "",
    "API_TIMEOUT_IN_SECONDS": 30,
}

WRITE_DATADOG_HOST_METRICS = False

OPEN_AI = {"API_KEY": ""}

# Settings for admin integration with audit objects
PGHISTORY_ADMIN_MODEL = "pghistory.MiddlewareEvents"  # shows audit with context
# Do not attempt to list all audit in the "Events" page
# when no Filters have been selected
# Note that this is separate from the "History" button shown
# on models
PGHISTORY_ADMIN_ALL_EVENTS = False

MUTEX_DEBUG = False

LUCIAN_EMAIL_ADDRESS = ""

GOOGLE_ISSUER = "https://accounts.google.com"

GCP_SERVICE_ACCOUNT = {"LUCIAN_SERVICE_ACCOUNT_CREDENTIALS": ""}

GCP_GOOGLE_ISSUER = "https://accounts.google.com"

GOOGLE_REJECT_EMAIL_SENDER = "<EMAIL>"

GMAIL_ATTACHMENT_BUCKET = ""

GMAIL_QUARANTINE_BUCKET = ""
