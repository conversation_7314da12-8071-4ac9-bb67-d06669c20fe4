import datetime
from datetime import timezone as datetime_timezone
from unittest import mock, skip

from django.conf import settings
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.test import RequestFactory
from django.test.utils import override_settings
from django.utils import timezone
from dramatiq.rate_limits import WindowRateLimiter
from dramatiq.rate_limits.backends import StubBackend
from faker import Faker
from psycopg2.extras import DateRange
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_409_CONFLICT,
)

from firefly.core.alias.models import get_content_type
from firefly.core.roles.constants import ROLE_VALUES
from firefly.core.roles.models import Role
from firefly.core.services.elation.client.client import ElationClient
from firefly.core.services.elation.sync.mixins.object_to_elation import ObjectToElationRecord
from firefly.core.tests.client import FireflyTestAP<PERSON>lient
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PatientUserFactory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models.models import Person
from firefly.modules.appointment.constants import (
    EVENT_LOG_ACTION_PERFORMED,
    OLD_MEMBER_DATE,
    AppointmentReason,
    AppointmentSource,
    AppointmentStatus,
    SlotType,
)
from firefly.modules.appointment.elation import ElationAppointmentSync
from firefly.modules.appointment.factories import (
    AppointmentFactory,
    AppointmentSlotFactory,
    SymptomFactory,
)
from firefly.modules.appointment.models import Appointment, CancellationReason
from firefly.modules.appointment.permissions import CanAccessCustomScheduling
from firefly.modules.appointment.tasks import (
    publish_appointment_to_elation_async,
    update_custom_appointment_eventlog,
)
from firefly.modules.cases.constants import INSURANCE_PLAN_NEEDS_REVIEW
from firefly.modules.cases.models import CaseCategory
from firefly.modules.events.models import EventLog, EventTypeCodes
from firefly.modules.firefly_django.constants import NY_TIMEZONE, UTC_TIMEZONE
from firefly.modules.insurance.constants import ContractAttributionType, ContractPMPMType
from firefly.modules.insurance.models import Contract, InsuranceMemberInfo, InsurancePayer
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.schedule.constants import CUSTOM_APPOINTMENT_ADDITIONAL_REASONS, SLOTS_NOT_AVAILABLE_REASONS
from firefly.modules.schedule.factories import (
    AppointmentSlotsFactory,
    DateTimeTZRange,
    ProviderScheduleFactory,
    ShiftExceptionFactory,
    ShiftFactory,
)
from firefly.modules.schedule.models import (
    AppointmentSlots,
    AppointmentType,
    AppointmentTypeContractMapping,
    Physician,
    PhysicianAppointmentTypeExcludedStates,
    PhysicianAppointmentTypeMapping,
    PhysicianVisitMixRatio,
    ProviderSchedule,
    TimeSlot,
)
from firefly.modules.schedule.tasks import appointment_slot_generation, create_slots_for_provider_shift
from firefly.modules.states.models import State
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.settings.base import LUCIAN_USER_TENANT_HEADER_TRANSFORMED


@mock.patch("firefly.modules.appointment.signals.auto_close_case_for_non_english_speaker")
class AppointmentsTestCase(FireflyTestCase):
    @mock.patch("firefly.modules.change_data_capture.subscribers.elation.subscribers.elation_update_user")
    def test_get(self, *args):
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW)
        # Give the patient permission to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.scheduled_date = datetime.datetime.now() + datetime.timedelta(days=1)

        self.appt_provider = ProviderDetailFactory()
        self.other_appt_provider = ProviderDetailFactory()
        # create provider schedule
        ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.appt_provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )
        ProviderScheduleFactory.create(
            provider=self.other_appt_provider,
            effective_period=DateRange(self.scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.other_appt_provider.physician, appointment_type=self.video_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=self.scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )

        with self.captureOnCommitCallbacks(execute=True):
            earlier_appointment = AppointmentFactory.create(
                physician=self.appt_provider.physician,
                source=AppointmentSource.LUCIAN,
                start=self.scheduled_date,
                patient=None,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
            self.client.patch(
                f"/appointment/slot/v3/{earlier_appointment.id}",
                format="json",
                data={"description": "Testing Event Logs"},
            )
            future_appointment = AppointmentFactory.create(
                source=AppointmentSource.LUCIAN,
                physician=self.appt_provider.physician,
                start=datetime.datetime.now() + datetime.timedelta(days=30),
                patient=None,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
            self.client.patch(
                f"/appointment/slot/v3/{future_appointment.id}",
                format="json",
                data={"description": "Testing Event Logs"},
            )
            cancelled_appointment = AppointmentFactory.create(
                source=AppointmentSource.LUCIAN,
                physician=self.appt_provider.physician,
                start=datetime.datetime.now() + datetime.timedelta(days=2),
                patient=None,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
            self.client.patch(
                f"/appointment/slot/v3/{cancelled_appointment.id}",
                format="json",
                data={"description": "Testing Event Logs"},
            )
        symptom_1 = SymptomFactory()
        symptom_2 = SymptomFactory()
        symptom_3 = SymptomFactory()
        # Set and re-set appointments to confirm we ignore the deleted m2m records
        future_appointment.symptoms.set([symptom_1])
        future_appointment.symptoms.set([symptom_2, symptom_3])
        future_appointment.other_symptoms = "My other symptom"
        symptom_labels = [symptom_2.label, symptom_3.label]
        symptom_labels.sort()
        future_appointment.other_symptoms = "My other symptom"
        future_appointment.save(update_fields=["other_symptoms"])
        # This appointment only has free-text symptoms
        earlier_appointment.other_symptoms = "My other symptoms"
        earlier_appointment.save(update_fields=["other_symptoms"])
        # Start a new capture block here, so that the first block captures scheduling `cancelled_appointment`
        with self.captureOnCommitCallbacks(execute=True):
            other_reason, _ = CancellationReason.objects.get_or_create(uid="test")
            self.client.post(
                f"/appointment/{cancelled_appointment.pk}/cancel/v2/",
                format="json",
                data={
                    "cancellation_reason_ids": [other_reason.pk],
                },
            )
            provider_booked_appointment = AppointmentFactory.create(
                physician=self.appt_provider.physician,
                source=AppointmentSource.LUCIAN,
                start=datetime.datetime.now() + datetime.timedelta(days=2),
                patient=None,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
            self.provider_client.patch(
                f"/appointment/slot/provider/book-appointment/v2/{provider_booked_appointment.id}",
                format="json",
                data={"description": "Testing Event Logs", "patient_id": self.patient.id},
            )
            rescheduled_appointment = AppointmentFactory.create(
                physician=self.other_appt_provider.physician,
                start=datetime.datetime.now() + datetime.timedelta(days=2),
                source=AppointmentSource.LUCIAN,
                patient=None,
                reason=AppointmentReason.VIDEO,
                time_slot_type="appointment_slot",
                status=AppointmentStatus.SCHEDULED.value,
            )
            other_patient = PersonUserFactory()
            other_patient_client = FireflyTestAPIClient()
            other_patient_client.credentials(**{LUCIAN_USER_TENANT_HEADER_TRANSFORMED: FIREFLY_TENANT_KEY})
            other_patient_client.force_authenticate(user=other_patient.user)
            other_patient.user.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
            other_patient.user.onboarding_state.save()
            other_patient_client.patch(
                f"/appointment/slot/v3/{rescheduled_appointment.id}",
                format="json",
                data={"description": "Other patient booking"},
            )
            other_patient_client.post(
                f"/appointment/{rescheduled_appointment.pk}/cancel/v2/",
                format="json",
                data={
                    "cancellation_reason_ids": [other_reason.pk],
                },
            )
            # Un-delete Appointment (schedule management might do this)
            rescheduled_appointment.deleted = None
            rescheduled_appointment.save(update_fields=["deleted"])
            rescheduled_appointment.refresh_from_db()
            self.client.patch(
                f"/appointment/slot/v3/{rescheduled_appointment.id}",
                format="json",
                data={"description": "Taking an available appointment"},
            )

        # 6 APPOINTMENT_SCHEDULED Event Logs:
        # 4 for bookings for self.patient
        # 1 for the un-deleteing the cancelled appointment
        # 1 for the rescheduled appointment originally booked for other_patient
        self.assertEqual(
            EventLog.objects.filter(
                type=EventTypeCodes.APPOINTMENT_SCHEDULED,
                target_object_id__in=[
                    future_appointment.id,
                    provider_booked_appointment.id,
                    earlier_appointment.id,
                    rescheduled_appointment.id,
                ],
            ).count(),
            6,
        )
        # Update the start time after booking, because booking only allows future appointments
        earlier_appointment.start = datetime.datetime.now() - datetime.timedelta(days=1)
        earlier_appointment.save(update_fields=["start"])
        with self.assertNumQueries(4):
            response = self.provider_client.get(f"/bff/crm/patient-appointments/?patient_id={self.patient.id}")
        json = response.json()
        self.assertEqual(
            list(map(lambda el: el["id"], json["appointments"])),
            [
                future_appointment.id,
                rescheduled_appointment.id,
                provider_booked_appointment.id,
                earlier_appointment.id,
            ],
        )
        for el in json["appointments"]:
            if el["id"] == future_appointment.id:
                self.assertEqual(el["cancelable_status"]["can_cancel"], True)
                self.assertEqual(el["description"], future_appointment.description)
                self.assertEqual(el["duration_minutes"], future_appointment.duration.seconds / 60)
                self.assertEqual(
                    el["end"],
                    (future_appointment.start + future_appointment.duration).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                )
                self.assertEqual(el["provider_id"], future_appointment.physician.provider.pk)
                self.assertEqual(
                    el["provider_name"],
                    (" ").join(
                        [
                            future_appointment.physician.provider.first_name,
                            future_appointment.physician.provider.last_name,
                        ]
                    ),
                )
                self.assertEqual(el["reason"], future_appointment.reason)
                self.assertEqual(el["status"], AppointmentStatus.SCHEDULED.value)
                self.assertIsNotNone(el["scheduled_at"])
                self.assertEqual(el["scheduled_by"], "patient")
                self.assertEqual(el["start"], future_appointment.start.strftime("%Y-%m-%dT%H:%M:%S.%fZ"))
                self.assertEqual(
                    el["symptoms_summary"], (", ").join(symptom_labels + [future_appointment.other_symptoms])
                )
            elif el["id"] == provider_booked_appointment.id:
                self.assertEqual(el["scheduled_by"], "provider")
                self.assertEqual(el["scheduled_by_id"], self.provider.id)
                self.assertEqual(
                    el["scheduled_by_provider_name"],
                    (" ").join([self.provider.providerdetail.first_name, self.provider.providerdetail.last_name]),
                )
                self.assertIsNone(el["symptoms_summary"])
            elif el["id"] == earlier_appointment.id:
                self.assertEqual(el["cancelable_status"]["can_cancel"], False)
                self.assertEqual(el["cancelable_status"]["reason"], "Appointment start time has already passed")
                self.assertEqual(el["scheduled_by"], "patient")
                self.assertEqual(el["symptoms_summary"], (", ").join([earlier_appointment.other_symptoms]))
            elif el["id"] == rescheduled_appointment.id:
                self.assertEqual(el["scheduled_by"], "patient")

        # Test Cancelation view
        self.client.post(
            f"/appointment/{provider_booked_appointment.pk}/cancel/v2/",
            format="json",
            data={
                "cancellation_reason_ids": [other_reason.pk],
            },
        )
        with self.assertNumQueries(7):
            response = self.provider_client.get(
                f"/bff/crm/patient-appointment-cancelations/?patient_id={self.patient.id}"
            )
        json = response.json()
        self.assertEqual(
            list(map(lambda el: el["appointment_id"], json["cancelation_events"])),
            [
                provider_booked_appointment.id,
                cancelled_appointment.id,
            ],
        )
        for el in json["cancelation_events"]:
            if el["appointment_id"] == provider_booked_appointment.id:
                self.assertEqual(el["duration_minutes"], provider_booked_appointment.duration.seconds / 60)
                self.assertEqual(el["reason"], provider_booked_appointment.reason)
                self.assertEqual(el["scheduled_by"], "provider")
                self.assertEqual(el["scheduled_by_id"], self.provider.pk)
                self.assertEqual(
                    el["scheduled_by_provider_name"],
                    (" ").join([self.provider.providerdetail.first_name, self.provider.providerdetail.last_name]),
                )
                self.assertEqual(el["cancelation_reason_ids"], [other_reason.pk])
            elif el["appointment_id"] == cancelled_appointment.id:
                self.assertEqual(el["reason"], cancelled_appointment.reason)
                self.assertEqual(el["scheduled_by"], "patient")
                self.assertEqual(el["scheduled_by_id"], self.patient.id)
                self.assertEqual(el["cancelation_reason_ids"], [other_reason.pk])

        EventLog.objects.create(
            target=future_appointment,
            type=EventTypeCodes.APPOINTMENT_CANCELLED,
            user_id=self.patient.id,
            metadata={
                "changed": {"patient": [self.patient.id, None]},
                "current_appointment": {
                    "physician_id": 123,
                    "reason": "test",
                    "description": "test",
                    "patient_id": self.patient.id,
                    "cancelation_reason": None,
                    "cancellation_reasons": None,
                    "canceled_by_system": False,
                    "action_performed": EVENT_LOG_ACTION_PERFORMED.CANCELED,
                    "action_performed_by": "patient",
                },
            },
        )
        response = self.provider_client.get(f"/bff/crm/patient-appointment-cancelations/?patient_id={self.patient.id}")
        json = response.json()
        self.assertEqual(json["cancelation_events"][0]["cancelation_reason_ids"], [])


class AppointmentSlotTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.schedule_start_date = datetime.datetime.strptime("2024-01-01", "%Y-%m-%d")
        insurance_payer, _ = InsurancePayer.objects.get_or_create(name="Test Provider", firefly_accepted=1)
        insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.MA, _ = State.objects.get_or_create(abbreviation="MA")
        self.CA, _ = State.objects.get_or_create(abbreviation="CA")
        self.np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        self.md_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.PHYSICIAN)

        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.patient.person.elation_id = 1234
        self.patient.person.insurance_info = insurance_info_ppo
        self.patient.person.save()
        self.patient.onboarding_state.to_member()
        self.another_patient = PatientUserFactory.create()

        self.appt_provider = ProviderDetailFactory.create()
        self.appt_provider.physician.save()
        self.appt_provider.physician.practicing_states.add(self.MA)
        self.appt_provider.title = "NP"
        self.appt_provider.internal_role = self.np_role
        self.appt_provider.save()

        self.patient.person.care_team.add(self.appt_provider)
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        self.second_provider = ProviderDetailFactory.create()
        self.second_provider.physician.save()
        self.second_provider.physician.practicing_states.add(self.MA)
        self.second_provider.title = "NP"
        self.second_provider.internal_role = self.np_role
        self.second_provider.is_taking_new_patients = False
        self.second_provider.save()

        self.patient.person.care_team.add(self.second_provider)
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.second_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        self.md_appt_provider = ProviderDetailFactory.create()
        self.md_appt_provider.physician.save()
        self.md_appt_provider.physician.practicing_states.add(self.MA)
        self.md_appt_provider.title = "MD"
        self.md_appt_provider.internal_role = self.md_role
        self.md_appt_provider.save()

        self.patient.person.care_team.add(self.md_appt_provider)

        self.other_appt_provider = ProviderDetailFactory.create()
        self.other_appt_provider.physician.save()
        self.other_appt_provider.physician.practicing_states.add(self.MA)
        self.other_appt_provider.title = "NP"
        self.other_appt_provider.internal_role = self.np_role
        self.other_appt_provider.save()

        self.other_not_licenced_appt_provider = ProviderDetailFactory.create()
        self.other_not_licenced_appt_provider.physician.save()
        self.other_not_licenced_appt_provider.title = "NP"
        self.other_not_licenced_appt_provider.internal_role = self.np_role
        self.other_not_licenced_appt_provider.save()

        # Set some mocked appointment timing
        two_days = timezone.now().date() + datetime.timedelta(days=2)
        tzone = timezone.get_default_timezone()
        self.two_days_am_restart = datetime.datetime.combine(
            two_days, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone
        )
        self.day_of_week = self.two_days_am_restart.isoweekday()
        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
        ]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.appt_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )
            PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.other_appt_provider.physician, appointment_type=appointment_type
            )
            PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.second_provider.physician, appointment_type=appointment_type
            )
            PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.md_appt_provider.physician, appointment_type=appointment_type
            )

    @mock.patch("firefly.bff.crm.appointments.get_available_slots")
    @mock.patch("firefly.bff.crm.appointments.is_flag_active_for_user")
    def test_get(self, mock_is_flag_active_for_user, mock_get_available_slots):
        def side_effect_fn(flag_name, user):
            # Check if the provided arguments match the expected ones
            if user == self.other_appt_provider.user:
                return False
            else:
                return True

        now: datetime = timezone.now()
        AppointmentSlotFactory.create(
            start=now + datetime.timedelta(hours=1),
            physician=self.other_appt_provider.physician,
            reason=AppointmentReason.VIDEO,
        )

        mock_get_available_slots.return_value = {
            "provider_slot_map": {
                self.appt_provider.user_id: [now],
                self.second_provider.user_id: [now],
                self.md_appt_provider.user_id: [now],
            },
            "reasons_for_unavailability": [],
        }
        mock_is_flag_active_for_user.side_effect = side_effect_fn
        # Give the patient permission to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}"
        )
        json = response.json()
        appt_provider_name = (
            f"{self.appt_provider.first_name} {self.appt_provider.last_name}, {self.appt_provider.title}"
        )
        second_provider_name = (
            f"{self.second_provider.first_name} {self.second_provider.last_name}, {self.second_provider.title}"
        )
        md_appt_provider_name = (
            f"{self.md_appt_provider.first_name} {self.md_appt_provider.last_name}, {self.md_appt_provider.title}"
        )
        other_appt_provider = f"{self.other_appt_provider.first_name} {self.other_appt_provider.last_name}"
        other_appt_provider_name = f"{other_appt_provider}, {self.other_appt_provider.title}"

        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.other_appt_provider.physician.id,
                        "physician_name": other_appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": False,
                        "slots": [(now + datetime.timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.second_provider.physician.id,
                        "physician_name": second_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.md_appt_provider.physician.id,
                        "physician_name": md_appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                ]
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.md_appt_provider.physician.id,
                        "physician_name": md_appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                ]
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        # with start_time
        self.md_appt_provider.physician.delete()
        self.other_appt_provider.physician.delete()
        mock_get_available_slots.reset_mock()
        start_time = timezone.now()
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    }
                ]
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=(
                timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
                + datetime.timedelta(minutes=15)
                + datetime.timedelta(weeks=self.video_new_appointment_type.booking_window_in_weeks)
            ),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            exclude_dates=[],
            log_prefix="bff/crm/available-appointment-slots",
        )

        # with end time
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        # Total number of queries should be 13:
        #   1. SELECT ... FROM auth_user - 3
        #   4. SELECT ... FROM schedule_appointmenttype - 2
        #   6. SELECT ... FROM user_person
        #   7. SELECT ... FROM insurance_member_info
        #   8. SELECT ... FROM schedule_physicianappointmenttypemapping
        #   9. SELECT ... FROM appointments
        #   10. SELECT ... FROM attribution_attribution
        #   11. SELECT ... FROM auth_user_provider_details - 3
        with self.assertNumQueries(13):
            response = self.provider_client.get(
                f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}"
            )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    }
                ]
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=timezone.make_aware(datetime.datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%SZ")),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            log_prefix="bff/crm/available-appointment-slots",
            exclude_dates=[],
        )
        # when get available slots return 0 rows
        mock_get_available_slots.return_value = {"provider_slot_map": {}, "reasons_for_unavailability": []}
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {"appointments": []},
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=timezone.make_aware(datetime.datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%SZ")),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            log_prefix="bff/crm/available-appointment-slots",
            exclude_dates=[],
        )


class AppointmentSlotV2TestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        # Set some mocked appointment timing
        self.today = datetime.date.today()
        self.now = datetime.datetime.now(tz=UTC_TIMEZONE)
        self.tomorrow = datetime.date.today() + datetime.timedelta(days=1)
        self.start_time = datetime.datetime(
            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 0, 0, 0, tzinfo=NY_TIMEZONE
        )
        self.start_time_str = self.start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        self.day_of_week_tomorrow = self.tomorrow.isoweekday()

        self.two_days_after = datetime.date.today() + datetime.timedelta(days=2)
        end_time = datetime.datetime(
            self.two_days_after.year, self.two_days_after.month, self.two_days_after.day, 23, 59, 59, tzinfo=NY_TIMEZONE
        )
        self.end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        self.day_of_week_two_days_after = self.two_days_after.isoweekday()
        self.video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.schedule_start_date = datetime.datetime.strptime("2024-01-01", "%Y-%m-%d")
        insurance_payer, _ = InsurancePayer.objects.get_or_create(name="Test Provider", firefly_accepted=1)
        insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.MA, _ = State.objects.get_or_create(abbreviation="MA")
        self.CA, _ = State.objects.get_or_create(abbreviation="CA")
        self.np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        self.md_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.PHYSICIAN)

        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.patient.person.elation_id = 1234
        self.patient.person.insurance_info = insurance_info_ppo
        self.patient.person.save()
        self.patient.onboarding_state.to_member()
        self.another_patient = PatientUserFactory.create()

        self.appt_provider = ProviderDetailFactory.create()
        self.appt_provider.physician.save()
        self.appt_provider.physician.practicing_states.add(self.MA)
        self.appt_provider.title = "NP"
        self.appt_provider.internal_role = self.np_role
        self.appt_provider.save()

        self.patient.person.care_team.add(self.appt_provider)
        # create provider schedule
        self.appt_provider_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_tomorrow = ShiftFactory.create(
            day_of_week=self.day_of_week_tomorrow,
            schedule=self.appt_provider_schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("11:30:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("12:30:00", "%H:%M:%S"),
        )
        self.shift_after_2_days = ShiftFactory.create(
            day_of_week=self.day_of_week_two_days_after,
            schedule=self.appt_provider_schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("11:30:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("12:30:00", "%H:%M:%S"),
        )

        self.not_accepting_new_patient_provider = ProviderDetailFactory.create()
        self.not_accepting_new_patient_provider.physician.save()
        self.not_accepting_new_patient_provider.physician.practicing_states.add(self.MA)
        self.not_accepting_new_patient_provider.title = "NP"
        self.not_accepting_new_patient_provider.internal_role = self.np_role
        self.not_accepting_new_patient_provider.is_taking_new_patients = False
        self.not_accepting_new_patient_provider.save()

        self.patient.person.care_team.add(self.not_accepting_new_patient_provider)
        # create provider schedule
        self.not_accepting_new_patient_provider_schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.not_accepting_new_patient_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        self.shift_tomorrow = ShiftFactory.create(
            day_of_week=self.day_of_week_tomorrow,
            schedule=self.not_accepting_new_patient_provider_schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("11:30:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("12:30:00", "%H:%M:%S"),
        )
        self.shift_after_2_days = ShiftFactory.create(
            day_of_week=self.day_of_week_two_days_after,
            schedule=self.not_accepting_new_patient_provider_schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("11:30:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("12:30:00", "%H:%M:%S"),
        )

        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
        ]:
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.appt_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week_tomorrow,
                defaults={"percentage_of_slots": "50"},
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week_two_days_after,
                defaults={"percentage_of_slots": "50"},
            )
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.not_accepting_new_patient_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week_tomorrow,
                defaults={"percentage_of_slots": "50"},
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week_two_days_after,
                defaults={"percentage_of_slots": "50"},
            )

        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.appt_provider,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=7)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            create_slots_for_provider_shift(
                provider_id=self.not_accepting_new_patient_provider,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=7)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=self.appt_provider,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=7)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
            appointment_slot_generation(
                provider_id=self.not_accepting_new_patient_provider,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=7)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )

    @mock.patch("firefly.bff.crm.utils.is_flag_active_for_user")
    @mock.patch("django.utils.timezone.now")
    def test_get_v2(self, mock_timezone, mock_is_flag_active_for_user):
        mock_timezone.return_value = self.now
        mock_is_flag_active_for_user.return_value = True

        # Make the patient eligible to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()

        # SCENARIO: Test availability for VIDEO appointments
        # EXPECTATION: Only slots of providers mapped to Video appointment type should be shown
        provider_without_mapping = ProviderDetailFactory.create()
        provider_without_mapping.physician.save()
        provider_with_diff_mapping = ProviderDetailFactory.create()
        provider_with_diff_mapping.physician.save()
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider_with_diff_mapping.physician, appointment_type=self.video_new_appointment_type
        )
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}"
        )
        json = response.json()
        appt_provider_name = (
            f"{self.appt_provider.first_name} {self.appt_provider.last_name}, {self.appt_provider.title}"
        )
        name = (
            f"{self.not_accepting_new_patient_provider.first_name} {self.not_accepting_new_patient_provider.last_name}"
        )
        not_accepting_new_patient_provider_name = f"{name}, {self.not_accepting_new_patient_provider.title}"

        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                            datetime.datetime(
                                self.two_days_after.year,
                                self.two_days_after.month,
                                self.two_days_after.day,
                                11,
                                30,
                                tzinfo=NY_TIMEZONE,
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                    {
                        "physician_id": self.not_accepting_new_patient_provider.physician.id,
                        "physician_name": not_accepting_new_patient_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                            datetime.datetime(
                                self.two_days_after.year,
                                self.two_days_after.month,
                                self.two_days_after.day,
                                11,
                                30,
                                tzinfo=NY_TIMEZONE,
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                ],
                "dates_with_slot_availability": [
                    self.tomorrow.strftime("%Y-%m-%d"),
                    self.two_days_after.strftime("%Y-%m-%d"),
                ],
                "booking_window_in_weeks": 4,
            },
        )

        # SCENARIO: Test availability for VIDEO-NEW appointments
        # EXPECTATION: Only slots of providers mapped to Video-New appointment type
        # and those with is_taking_new_patients set as true should only be shown
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                            datetime.datetime(
                                self.two_days_after.year,
                                self.two_days_after.month,
                                self.two_days_after.day,
                                11,
                                30,
                                tzinfo=NY_TIMEZONE,
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                ],
                "dates_with_slot_availability": [
                    self.tomorrow.strftime("%Y-%m-%d"),
                    self.two_days_after.strftime("%Y-%m-%d"),
                ],
                "booking_window_in_weeks": 4,
            },
        )

        # SCENARIO: With start_time param
        # EXPECTATION: All slots after start time should be shown
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={self.start_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                            datetime.datetime(
                                self.two_days_after.year,
                                self.two_days_after.month,
                                self.two_days_after.day,
                                11,
                                30,
                                tzinfo=NY_TIMEZONE,
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                ],
                "dates_with_slot_availability": [
                    self.tomorrow.strftime("%Y-%m-%d"),
                    self.two_days_after.strftime("%Y-%m-%d"),
                ],
                "booking_window_in_weeks": 4,
            },
        )

        # SCENARIO: With end time param
        # EXPECTATION: only slots in the given period should be available though there are slots on other days
        # When end time is start of tomorrow, tomorrow's slots shouldn't be shown
        end_time = self.start_time + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        # Total number of queries should be 13:
        #   1. SELECT ... FROM auth_user - 4
        #   5. SELECT ... FROM schedule_appointmenttype - 3
        #   8. SELECT ... FROM user_person - 1
        #   9. SELECT ... FROM insurance_member_info - 1
        #   10. SELECT ... FROM schedule_physicianappointmenttypemapping - 1
        #   11. SELECT ... FROM appointments - 2
        #   13. SELECT ... FROM attribution_attribution - 1
        #   14. SELECT ... FROM auth_user_provider_details - 1
        #   15. SELECT ... FROM schedule_timeslot - 2
        #   17. SELECT ... FROM schedule_appointmentslots - 1
        #   18. SELECT ... FROM auth_user_provider_details - 2
        #   20. SELECT ... FROM physicians - 1
        #   21. SELECT ... FROM audit_phiauditlog - 1
        #   22. SELECT ... FROM schedule_physicianvisitmixratio - 1
        with self.assertNumQueries(22):
            response = self.provider_client.get(
                f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={self.start_time_str}&end_time={end_time_str}"
            )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                ],
                "dates_with_slot_availability": [
                    self.tomorrow.strftime("%Y-%m-%d"),
                    self.two_days_after.strftime("%Y-%m-%d"),
                ],
                "booking_window_in_weeks": 4,
            },
        )

        # SCENARIO: When the state is excluded for physician
        # EXPECTATION: Shouldn't show any slots for that provider
        type_mapping = PhysicianAppointmentTypeMapping.objects.filter(
            physician=self.appt_provider.physician, appointment_type=self.video_new_appointment_type
        ).first()

        exclude_state, _ = PhysicianAppointmentTypeExcludedStates.objects.get_or_create(
            physician_appt_type_mapping=type_mapping, state=self.MA
        )
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={self.start_time_str}&end_time={end_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [],
                "dates_with_slot_availability": [],
                "booking_window_in_weeks": 4,
            },
        )
        # SCENARIO: When the state excluded is deleted for physician
        # EXPECTATION: Should show slots for that provider
        exclude_state.delete()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/v2/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={self.start_time_str}&end_time={end_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "first_available_slot": datetime.datetime(
                            self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                        )
                        .astimezone(datetime_timezone.utc)
                        .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [
                            datetime.datetime(
                                self.tomorrow.year, self.tomorrow.month, self.tomorrow.day, 11, 30, tzinfo=NY_TIMEZONE
                            )
                            .astimezone(datetime_timezone.utc)
                            .strftime("%Y-%m-%dT%H:%M:%SZ"),
                        ],
                    },
                ],
                "dates_with_slot_availability": [
                    self.tomorrow.strftime("%Y-%m-%d"),
                    self.two_days_after.strftime("%Y-%m-%d"),
                ],
                "booking_window_in_weeks": 4,
            },
        )


class AppointmentSlotTestCaseWithoutType(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.schedule_start_date = datetime.datetime.strptime("2024-01-01", "%Y-%m-%d")
        insurance_payer, _ = InsurancePayer.objects.get_or_create(name="Test Provider", firefly_accepted=1)
        insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.MA, _ = State.objects.get_or_create(abbreviation="MA")
        self.CA, _ = State.objects.get_or_create(abbreviation="CA")
        self.np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)
        self.md_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.PHYSICIAN)

        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.patient.person.elation_id = 1234
        self.patient.person.insurance_info = insurance_info_ppo
        self.patient.person.save()
        self.patient.onboarding_state.to_member()
        self.another_patient = PatientUserFactory.create()

        self.appt_provider = ProviderDetailFactory.create()
        self.appt_provider.physician.save()
        self.appt_provider.physician.practicing_states.add(self.MA)
        self.appt_provider.title = "NP"
        self.appt_provider.internal_role = self.np_role
        self.appt_provider.save()

        self.patient.person.care_team.add(self.appt_provider)
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        self.second_provider = ProviderDetailFactory.create()
        self.second_provider.physician.save()
        self.second_provider.physician.practicing_states.add(self.MA)
        self.second_provider.title = "NP"
        self.second_provider.internal_role = self.np_role
        self.second_provider.is_taking_new_patients = False
        self.second_provider.save()

        self.patient.person.care_team.add(self.second_provider)
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.second_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        self.md_appt_provider = ProviderDetailFactory.create()
        self.md_appt_provider.physician.save()
        self.md_appt_provider.physician.practicing_states.add(self.MA)
        self.md_appt_provider.title = "MD"
        self.md_appt_provider.internal_role = self.md_role
        self.md_appt_provider.save()

        self.patient.person.care_team.add(self.md_appt_provider)

        self.other_appt_provider = ProviderDetailFactory.create()
        self.other_appt_provider.physician.save()
        self.other_appt_provider.physician.practicing_states.add(self.MA)
        self.other_appt_provider.title = "NP"
        self.other_appt_provider.internal_role = self.np_role
        self.other_appt_provider.save()

        self.other_not_licenced_appt_provider = ProviderDetailFactory.create()
        self.other_not_licenced_appt_provider.physician.save()
        self.other_not_licenced_appt_provider.title = "NP"
        self.other_not_licenced_appt_provider.internal_role = self.np_role
        self.other_not_licenced_appt_provider.save()

        # Set some mocked appointment timing
        two_days = timezone.now().date() + datetime.timedelta(days=2)
        tzone = timezone.get_default_timezone()
        self.two_days_am_restart = datetime.datetime.combine(
            two_days, settings.NEW_APPOINTMENT_CUTOFF_TIME["AM"], tzinfo=tzone
        )
        self.day_of_week = self.two_days_am_restart.isoweekday()
        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
        ]:
            for provider in [
                self.second_provider,
                self.md_appt_provider,
                self.other_not_licenced_appt_provider,
                self.other_appt_provider,
            ]:
                PhysicianAppointmentTypeMapping.objects.get_or_create(
                    physician=provider.physician, appointment_type=appointment_type
                )
            type_mapping, _created = PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.appt_provider.physician, appointment_type=appointment_type
            )
            PhysicianVisitMixRatio.objects.get_or_create(
                physician_appointment_type=type_mapping,
                day_of_week=self.day_of_week,
                defaults={"percentage_of_slots": "50"},
            )

    @mock.patch("firefly.bff.crm.appointments.get_available_slots")
    @mock.patch("firefly.bff.crm.appointments.is_flag_active_for_user")
    def test_get(self, mock_is_flag_active_for_user, mock_get_available_slots):
        def side_effect_fn(flag_name, user):
            # Check if the provided arguments match the expected ones
            if user == self.other_appt_provider.user:
                return False
            else:
                return True

        now: datetime = timezone.now()
        AppointmentSlotFactory.create(
            start=now + datetime.timedelta(hours=1),
            physician=self.other_appt_provider.physician,
            reason=AppointmentReason.VIDEO,
        )

        mock_get_available_slots.return_value = {
            "provider_slot_map": {
                self.appt_provider.user_id: [now],
                self.second_provider.user_id: [now],
                self.md_appt_provider.user_id: [now],
            },
            "reasons_for_unavailability": [],
        }
        mock_is_flag_active_for_user.side_effect = side_effect_fn
        # Give the patient permission to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}"
        )
        json = response.json()
        appt_provider_name = (
            f"{self.appt_provider.first_name} {self.appt_provider.last_name}, {self.appt_provider.title}"
        )
        second_provider_name = (
            f"{self.second_provider.first_name} {self.second_provider.last_name}, {self.second_provider.title}"
        )
        md_appt_provider_name = (
            f"{self.md_appt_provider.first_name} {self.md_appt_provider.last_name}, {self.md_appt_provider.title}"
        )
        other_appt_provider = f"{self.other_appt_provider.first_name} {self.other_appt_provider.last_name}"
        other_appt_provider_name = f"{other_appt_provider}, {self.other_appt_provider.title}"
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.other_appt_provider.physician.id,
                        "physician_name": other_appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": False,
                        "slots": [(now + datetime.timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.second_provider.physician.id,
                        "physician_name": second_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.md_appt_provider.physician.id,
                        "physician_name": md_appt_provider_name,
                        "reason": AppointmentReason.VIDEO,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                ]
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                    {
                        "physician_id": self.md_appt_provider.physician.id,
                        "physician_name": md_appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    },
                ]
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        # with start_time
        self.md_appt_provider.physician.delete()
        self.other_appt_provider.physician.delete()
        mock_get_available_slots.reset_mock()
        start_time = timezone.now()
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    }
                ]
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=(
                timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
                + datetime.timedelta(minutes=15)
                + settings.NEXT_AVAILABLE_SLOT_PERIOD
            ),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            log_prefix="bff/crm/available-appointment-slots",
            exclude_dates=[],
        )

        # with end time
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "appointments": [
                    {
                        "physician_id": self.appt_provider.physician.id,
                        "physician_name": appt_provider_name,
                        "reason": AppointmentReason.VIDEO_NEW_PATIENT,
                        "in_care_team": True,
                        "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                    }
                ]
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=timezone.make_aware(datetime.datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%SZ")),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            log_prefix="bff/crm/available-appointment-slots",
            exclude_dates=[],
        )
        # when get available slots return 0 rows
        mock_get_available_slots.return_value = {"provider_slot_map": {}, "reasons_for_unavailability": []}
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-appointment-slots/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {"appointments": []},
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=timezone.make_aware(datetime.datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%SZ"))
            + datetime.timedelta(minutes=15),
            end_date_time=timezone.make_aware(datetime.datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%SZ")),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            log_prefix="bff/crm/available-appointment-slots",
            exclude_dates=[],
        )


@override_settings(ELATION=dict(settings.ELATION, SEND_UPDATES=True))
@mock.patch("firefly.modules.appointment.slots.api.submit_event_async")
@mock.patch.object(ObjectToElationRecord, "object_pre_save_callback")
@mock.patch.object(ElationClient, "create_record")
@mock.patch.object(ElationClient, "update_record")
@mock.patch.object(ElationClient, "init_session")
@mock.patch("firefly.modules.appointment.signals.publish_appointment_to_elation_async")
@mock.patch(
    "firefly.core.services.dramatiq.utils.get_backend_for_mutex",
    return_value=StubBackend(),
)
@mock.patch.object(
    WindowRateLimiter,
    "_acquire",
    return_value=True,
)
class BookAppointmentTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider = ProviderDetailFactory.create()
        self.patient = PatientUserFactory.create()
        Person.objects.create(user=self.patient)
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            duration=30,
            buffer_time_in_minutes=15,
        )
        PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )

    def test_book_appointment_with_past_date(
        self,
        acquire_mock,
        backend_mock,
        publish_appointment_to_elation_async_mock,
        init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        ElationAppointmentSync().connect_model_listener()
        slot_past_time = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=datetime.datetime.now() - datetime.timedelta(hours=2),
            source=AppointmentSource.LUCIAN,
            visible=True,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": slot_past_time.start,
            "physician_id": slot_past_time.physician.id,
            "reason": slot_past_time.reason,
        }
        response = self.provider_client.post("/bff/crm/book-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        create_record_mock.assert_not_called()
        mock_submit_event.send.assert_not_called()
        object_pre_save_callback_mock.assert_not_called()

    def test_book_appointment_with_already_booked_slot(
        self,
        acquire_mock,
        backend_mock,
        publish_appointment_to_elation_async_mock,
        init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        ElationAppointmentSync().connect_model_listener()
        booked_appointment = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=datetime.datetime.now() + datetime.timedelta(hours=1),
            source=AppointmentSource.LUCIAN,
            visible=True,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
        )
        person = PersonUserFactory()
        booked_appointment.patient = person.user
        booked_appointment.status = AppointmentStatus.SCHEDULED.value
        with self.captureOnCommitCallbacks(execute=True):
            booked_appointment.save(update_fields=["patient", "status"])
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=booked_appointment.pk,
            log_prefix=f"appointment_scheduled: Appointment: {booked_appointment.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=booked_appointment.pk, log_prefix="")
        create_record_mock.assert_called_once()
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": booked_appointment.start,
            "physician_id": booked_appointment.physician.id,
            "reason": booked_appointment.reason,
        }
        create_record_mock.reset_mock()
        publish_appointment_to_elation_async_mock.send.reset_mock()
        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)
        publish_appointment_to_elation_async_mock.send.assert_not_called()
        create_record_mock.assert_not_called()
        mock_submit_event.send.assert_not_called()
        create_record_mock.assert_not_called()
        object_pre_save_callback_mock.assert_not_called()

    def test_book_appointment_source_lucian(
        self,
        acquire_mock,
        backend_mock,
        publish_appointment_to_elation_async_mock,
        init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        scheduled_date = datetime.datetime.now() + datetime.timedelta(hours=2)
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        ElationAppointmentSync().connect_model_listener()
        slot_future_time = AppointmentSlotFactory(
            elation_id=None,
            physician=self.provider.physician,
            start=scheduled_date,
            source=AppointmentSource.LUCIAN,
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            visible=True,
        )
        create_record_mock.reset_mock()
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": slot_future_time.start,
            "physician_id": slot_future_time.physician.id,
            "reason": slot_future_time.reason,
        }
        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-appointment/", format="json", data=payload)
        slot_future_time.refresh_from_db()
        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(slot_future_time.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(slot_future_time.patient, self.patient)
        self.assertEqual(slot_future_time.description, "test")
        self.patient.person.elation_id = Faker().pyint()
        self.patient.person.save()
        # should not call pre save if source Lucian
        object_pre_save_callback_mock.assert_not_called()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=slot_future_time.pk,
            log_prefix=f"appointment_scheduled: Appointment: {slot_future_time.pk}",
        )
        publish_appointment_to_elation_async(appointment_id=slot_future_time.pk, log_prefix="")
        # should call post save if source Lucian
        create_record_mock.assert_called_once()
        update_record_mock.assert_not_called()
        mock_submit_event.send.assert_called_once()

    def test_book_appointment_with_no_slot_generated_in_backend(
        self,
        acquire_mock,
        backend_mock,
        publish_appointment_to_elation_async_mock,
        init_session_mock,
        update_record_mock,
        create_record_mock,
        object_pre_save_callback_mock,
        mock_submit_event,
    ):
        create_record_mock.return_value = {"id": "1234567"}
        scheduled_date = datetime.datetime.now() + datetime.timedelta(hours=3)
        type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=self.provider.physician, appointment_type=self.video_new_appointment_type
        )
        PhysicianVisitMixRatio.objects.get_or_create(
            physician_appointment_type=type_mapping,
            day_of_week=scheduled_date.isoweekday(),
            defaults={"percentage_of_slots": "50"},
        )
        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.provider,
            effective_period=DateRange(scheduled_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )
        ElationAppointmentSync().connect_model_listener()
        create_record_mock.reset_mock()
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": scheduled_date,
            "physician_id": self.provider.physician.id,
            "reason": AppointmentReason.VIDEO_NEW_PATIENT,
        }
        response = None
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_200_OK)

        # Should create an appointment with patient
        appointment = Appointment.objects.filter(
            start=scheduled_date, physician=self.provider.physician, reason=AppointmentReason.VIDEO_NEW_PATIENT
        )

        self.assertEqual(appointment[0].status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(appointment[0].patient, self.patient)
        self.assertEqual(appointment[0].description, "test")
        self.assertEqual(appointment[0].source, AppointmentSource.LUCIAN)
        self.assertEqual(appointment[0].time_slot_type, SlotType.APPOINTMENT_SLOT)
        self.assertTrue(appointment[0].visible)
        self.patient.person.elation_id = Faker().pyint()
        self.patient.person.save()
        # should not call pre save if source Lucian
        object_pre_save_callback_mock.assert_not_called()
        publish_appointment_to_elation_async_mock.send.assert_called_once_with(
            appointment_id=appointment[0].pk,
            log_prefix=f"appointment_scheduled: Appointment: {appointment[0].pk}",
        )
        publish_appointment_to_elation_async(appointment_id=appointment[0].pk, log_prefix="")
        # should call post save if source Lucian
        create_record_mock.assert_called_once()
        update_record_mock.assert_not_called()
        mock_submit_event.send.assert_called_once()


class AvailableSlotsByProviderViewTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.video_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO,
            unique_key=AppointmentReason.VIDEO,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.awv_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.AWV_NEW,
            unique_key=AppointmentReason.AWV_NEW,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.schedule_start_date = datetime.datetime.strptime("2024-01-01", "%Y-%m-%d")
        insurance_payer, _ = InsurancePayer.objects.get_or_create(name="Test Provider", firefly_accepted=1)
        insurance_info_ppo = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer,
            plan_type="ppo",
            member_id="00011",
            group_number="11234",
            state="MA",
        )
        self.MA, _ = State.objects.get_or_create(abbreviation="MA")
        self.np_role, _ = Role.objects.get_or_create(role_name=ROLE_VALUES.NP)

        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        self.patient.person.elation_id = 1234
        self.patient.person.insurance_info = insurance_info_ppo
        self.patient.person.save()
        self.patient.onboarding_state.to_member()
        self.another_patient = PatientUserFactory.create()

        self.appt_provider = ProviderDetailFactory.create()
        self.appt_provider.physician.save()
        self.appt_provider.physician.practicing_states.add(self.MA)
        self.appt_provider.title = "NP"
        self.appt_provider.internal_role = self.np_role
        self.appt_provider.save()

        # create provider schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.appt_provider,
            effective_period=DateRange(self.schedule_start_date.replace(tzinfo=UTC_TIMEZONE).date(), None),
            timezone=NY_TIMEZONE,
        )

        for appointment_type in [
            self.video_appointment_type,
            self.video_new_appointment_type,
        ]:
            PhysicianAppointmentTypeMapping.objects.get_or_create(
                physician=self.appt_provider.physician, appointment_type=appointment_type
            )

    @skip("flaky")
    @mock.patch("firefly.bff.crm.utils.get_available_slots")
    @mock.patch("firefly.bff.crm.utils.is_flag_active_for_user")
    @mock.patch("django.utils.timezone.now")
    def test_get(self, mock_timezone, mock_is_flag_active_for_user, mock_get_available_slots):
        faker: Faker = Faker()
        dt = faker.past_datetime(tzinfo=UTC_TIMEZONE)
        mock_timezone.return_value = dt
        mock_is_flag_active_for_user.return_value = True

        now: datetime = timezone.now()

        mock_get_available_slots.return_value = {
            "provider_slot_map": {self.appt_provider.user_id: [now]},
            "reasons_for_unavailability": [],
        }
        # Give the patient permission to book
        self.patient.onboarding_state.initialized_at = OLD_MEMBER_DATE - datetime.timedelta(days=1)
        self.patient.onboarding_state.save()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}"
        )
        json = response.json()

        self.assertEqual(
            json,
            {
                "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                "first_available_slot": now.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "dates_with_slot_availability": [now.strftime("%Y-%m-%d")],
                "reasons_for_unavailability": [],
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&physician_id={self.appt_provider.physician.id}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                "first_available_slot": now.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "dates_with_slot_availability": [now.strftime("%Y-%m-%d")],
                "reasons_for_unavailability": [],
            },
        )
        self.assertEqual(mock_get_available_slots.call_count, 1)

        # with start_time
        mock_get_available_slots.reset_mock()
        start_time = now - datetime.timedelta(hours=2)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&physician_id={self.appt_provider.physician.id}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                "first_available_slot": now.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "dates_with_slot_availability": [now.strftime("%Y-%m-%d")],
                "reasons_for_unavailability": [],
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=dt,
            end_date_time=(dt + datetime.timedelta(weeks=self.video_new_appointment_type.booking_window_in_weeks)),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            exclude_dates=[],
            log_prefix="bff/crm/available-slots-by-provider/",
            choosen_date=now.date(),
        )

        # with end time, only slots in the given period should be available though there are slots on other days
        mock_get_available_slots.return_value = {
            "provider_slot_map": {self.appt_provider.user_id: [now, now + datetime.timedelta(days=2)]},
            "reasons_for_unavailability": [],
        }
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        # Total number of queries should be 13:
        #   1. SELECT ... FROM auth_user - 3
        #   4. SELECT ... FROM schedule_appointmenttype - 2
        #   6. SELECT ... FROM user_person
        #   7. SELECT ... FROM insurance_member_info
        #   8. SELECT ... FROM schedule_physicianappointmenttypemapping
        #   9. SELECT ... FROM appointments
        #   10. SELECT ... FROM attribution_attribution
        #   11. SELECT ... FROM auth_user_provider_details - 3
        with self.assertNumQueries(13):
            response = self.provider_client.get(
                f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}&physician_id={self.appt_provider.physician.id}"
            )
        json = response.json()
        self.assertEqual(
            json,
            {
                "slots": [now.strftime("%Y-%m-%dT%H:%M:%S.%fZ")],
                "first_available_slot": now.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "dates_with_slot_availability": [
                    now.strftime("%Y-%m-%d"),
                    (now + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                ],
                "reasons_for_unavailability": [],
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=dt,
            end_date_time=dt + datetime.timedelta(weeks=self.video_appointment_type.booking_window_in_weeks),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            exclude_dates=[],
            log_prefix="bff/crm/available-slots-by-provider/",
            choosen_date=now.date(),
        )
        # show next available slot if it exists outside the date selected
        mock_get_available_slots.reset_mock()
        mock_get_available_slots.return_value = {
            "provider_slot_map": {self.appt_provider.user_id: [now + datetime.timedelta(days=2)]},
            "reasons_for_unavailability": [],
        }
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}&physician_id={self.appt_provider.physician.id}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "slots": [],
                "first_available_slot": (now + datetime.timedelta(days=2)).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "dates_with_slot_availability": [(now + datetime.timedelta(days=2)).strftime("%Y-%m-%d")],
                "reasons_for_unavailability": [],
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=dt,
            end_date_time=dt + datetime.timedelta(weeks=self.video_appointment_type.booking_window_in_weeks),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            exclude_dates=[],
            log_prefix="bff/crm/available-slots-by-provider/",
            choosen_date=now.date(),
        )

        # when get available slots return 0 rows
        mock_get_available_slots.return_value = {"provider_slot_map": {}, "reasons_for_unavailability": []}
        end_time = timezone.now() + datetime.timedelta(days=1)
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        mock_get_available_slots.reset_mock()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO_NEW_PATIENT}&start_time={start_time_str}&end_time={end_time_str}&physician_id={self.appt_provider.physician.id}"
        )
        json = response.json()
        self.assertEqual(
            json,
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [],
            },
        )
        mock_get_available_slots.assert_called_once_with(
            provider_ids=[self.appt_provider.user_id],
            start_date_time=dt,
            end_date_time=dt + datetime.timedelta(weeks=self.video_appointment_type.booking_window_in_weeks),
            reason=AppointmentReason.VIDEO_NEW_PATIENT,
            exclude_dates=[],
            log_prefix="bff/crm/available-slots-by-provider/",
            choosen_date=now.date(),
        )

    def test_ineligible_physician_reasons(self):
        provider = ProviderDetailFactory.create()
        provider.internal_role = self.np_role
        provider.save()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [
                    SLOTS_NOT_AVAILABLE_REASONS["appointment_type_mapping_missing"],
                    SLOTS_NOT_AVAILABLE_REASONS["provider_not_licensed"],
                ],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

        provider.physician.practicing_states.add(self.MA)
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [SLOTS_NOT_AVAILABLE_REASONS["appointment_type_mapping_missing"]],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )
        physician_appt_type_mapping, _ = PhysicianAppointmentTypeMapping.objects.get_or_create(
            physician=provider.physician, appointment_type=self.awv_new_appointment_type
        )
        self.awv_new_appointment_type.requires_patient_booking_within_care_team = True
        self.awv_new_appointment_type.save()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [SLOTS_NOT_AVAILABLE_REASONS["provider_not_in_care_team"]],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )
        self.patient.person.care_team.add(provider)
        exclude_state = PhysicianAppointmentTypeExcludedStates.objects.create(
            physician_appt_type_mapping=physician_appt_type_mapping, state=self.MA
        )
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )
        exclude_state.delete()
        provider.is_taking_new_patients = False
        provider.save()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [SLOTS_NOT_AVAILABLE_REASONS["provider_not_accepting_new_patients"]],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )
        provider.is_taking_new_patients = True
        provider.save()
        appt = AppointmentFactory.create(patient=self.patient, reason=self.awv_new_appointment_type.unique_key)
        appt.physician = provider.physician
        appt.save()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.AWV_NEW}&physician_id={provider.physician.id}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

    @mock.patch("firefly.modules.appointment.slots.api.get_dates_to_be_excluded_based_on_client_config")
    def test_exclude_date_reasons(self, mock_get_dates_to_be_excluded_based_on_client_config):
        mock_get_dates_to_be_excluded_based_on_client_config.return_value = []
        now: datetime = timezone.now()
        start_time = now + datetime.timedelta(days=1)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        appt = AppointmentFactory.create(patient=self.patient)
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [SLOTS_NOT_AVAILABLE_REASONS["existing_appointment_present"]],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

        test_contract, _ = Contract.objects.get_or_create(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": self.employer.id,
                "allowable_group_ids": ["test241241"],
                "is_coverage_program_enrollment_enabled": False,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.POC_FORM,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.employer),
            contracted_entity_object_id=self.employer.id,
        )
        test_contract.name = "Test_employer"
        test_contract.save()
        self.patient.person.insurance_info.group_number = test_contract.config.get("allowable_group_ids")[0]
        self.patient.person.insurance_info.save(update_fields=["group_number"])
        self.patient.person.employer = self.employer
        self.patient.person.save()
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        self.patient.person.attribution.contract = test_contract
        self.patient.person.attribution.save()
        appt.delete()
        AppointmentTypeContractMapping.objects.create(
            contract=test_contract, appointment_type=self.video_appointment_type, percentage_of_allowed_appointments=0
        )
        mock_get_dates_to_be_excluded_based_on_client_config.return_value = [start_time.date()]
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [f"Test_employer {SLOTS_NOT_AVAILABLE_REASONS['visit_limit_reached']}"],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

    @mock.patch("firefly.modules.schedule.utils.slot_handler.apply_rules_for_timeslot")
    @mock.patch("firefly.bff.crm.utils.is_flag_active_for_user")
    def test_slots_not_available_reasons(self, mock_is_flag_active_for_user, mock_apply_rules_for_timeslot):
        mock_is_flag_active_for_user.return_value = True
        mock_apply_rules_for_timeslot.return_value = []
        now: datetime = timezone.now()
        start_time = now + datetime.timedelta(days=1)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

        self.shift = ShiftFactory.create(
            day_of_week=start_time.date().isoweekday(),
            schedule=self.schedule,
            effective_period=DateRange(now.date(), now.date() + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("10:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("10:45:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.appt_provider.user_id,
                log_prefix="",
                start_date=now.strftime("%Y-%m-%d"),
                end_date=(now + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        all_slots = TimeSlot.objects.all().order_by("period")
        appt = AppointmentFactory.create()
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[0])
        AppointmentSlotsFactory(appointment=appt, slot=all_slots[1])
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )
        appt.delete()
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [
                    SLOTS_NOT_AVAILABLE_REASONS["visit_mix_ratio_acheived"] % "established patient visits"
                ],
                "allow_custom_appointment": False,
                "booking_window_in_weeks": 4,
            },
        )

    @mock.patch("firefly.modules.schedule.utils.slot_handler.apply_rules_for_timeslot")
    @mock.patch("firefly.bff.crm.utils.is_flag_active_for_user")
    def test_custom_appointment_access(self, mock_is_flag_active_for_user, mock_apply_rules_for_timeslot):
        mock_is_flag_active_for_user.return_value = True
        mock_apply_rules_for_timeslot.return_value = []
        now: datetime = timezone.now()
        start_time = now + datetime.timedelta(days=1)
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        new_provider = self.create_provider()
        permission_codename = "can_access_custom_appointment"
        content_type = ContentType.objects.get_for_model(Appointment)
        group = Group.objects.create(name="schedulingSuperUsers")
        permission = Permission.objects.create(
            codename=permission_codename,
            name="Can access custom appointment",
            content_type=content_type,
        )
        group.permissions.add(permission)
        new_provider.groups.add(group)
        self.provider_client = FireflyTestCase.get_provider_client(provider=new_provider)
        response = self.provider_client.get(
            f"/bff/crm/available-slots-by-provider/?patient_id={self.patient.id}&reason={AppointmentReason.VIDEO}&physician_id={self.appt_provider.physician.id}&start_time={start_time_str}"
        )
        self.assertEqual(
            response.json(),
            {
                "slots": [],
                "first_available_slot": None,
                "dates_with_slot_availability": [],
                "reasons_for_unavailability": [],
                "allow_custom_appointment": True,
                "booking_window_in_weeks": 4,
            },
        )


class CanAccessCustomSchedulingTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.factory = RequestFactory()
        self.new_provider = self.create_provider()
        self.permission_codename = "can_access_custom_appointment"
        self.content_type = ContentType.objects.get_for_model(Appointment)
        self.group = Group.objects.create(name="schedulingSuperUsers")
        self.permission = Permission.objects.create(
            codename=self.permission_codename,
            name="Can access custom scheduling",
            content_type=self.content_type,
        )
        self.group.permissions.add(self.permission)

    def test_user_with_permission(self):
        self.new_provider.groups.add(self.group)
        request = self.factory.get("/")
        request.user = self.new_provider
        permission_check = CanAccessCustomScheduling()
        self.assertTrue(permission_check.has_permission(request, None))

    def test_user_without_permission(self):
        request = self.factory.get("/")
        request.user = self.new_provider
        permission_check = CanAccessCustomScheduling()
        self.assertFalse(permission_check.has_permission(request, None))


class BookCustomAppointmentViewTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.client_provider = self.create_provider()
        self.provider_client = FireflyTestCase.get_provider_client(provider=self.client_provider)
        # create custom_appointment permission and group
        permission_codename = "can_access_custom_appointment"
        content_type = ContentType.objects.get_for_model(Appointment)
        self.group = Group.objects.create(name="schedulingSuperUsers")
        permission = Permission.objects.create(
            codename=permission_codename,
            name="Can access custom scheduling",
            content_type=content_type,
        )
        self.group.permissions.add(permission)
        # tomorrow 10 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_10am_nyc = tomorrow.replace(hour=10, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        tomorrow_10_05am_nyc = tomorrow.replace(hour=10, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)

        # Convert the New York time to UTC
        tomorrow_10am = timezone.localtime(tomorrow_10am_nyc, timezone=UTC_TIMEZONE)
        self.tomorrow_10_05am = timezone.localtime(tomorrow_10_05am_nyc, timezone=UTC_TIMEZONE)

        self.day_of_week = tomorrow.isoweekday()
        self.appointment_start_time = tomorrow_10am

        self.reason = AppointmentReason.VIDEO_NEW_PATIENT
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.physician = Physician.objects.create(
            first_name="Test",
            last_name="Physician1",
            elation_id=99999991,
            elation_user_id=22222212,
            calendar_url="https://testurl",
        )
        self.physician_provider = ProviderDetailFactory.create(physician=self.physician)

        self.other_custom_appointment_reason = "Slots not available"
        self.today = datetime.date.today()
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.physician_provider,
            effective_period=DateRange(
                self.today - datetime.timedelta(weeks=4),
                self.today + datetime.timedelta(weeks=4),
            ),
        )
        PhysicianAppointmentTypeMapping.objects.create(
            physician=self.physician_provider.physician, appointment_type=self.video_new_appointment_type
        )

    def test_custom_appointment_without_permission(self):
        # custom_appointment_group not added to user
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_403_FORBIDDEN)

    @mock.patch("firefly.modules.appointment.tasks.update_custom_appointment_eventlog")
    def test_custom_appointment_sucess_response(self, mock_update_custom_appointment_eventlog):
        # custom_appointment_group added to user, not testing post_save_effects here
        # should create new appointment and EventLog
        self.client_provider.groups.add(self.group)
        symptom_1 = SymptomFactory()
        symptom_2 = SymptomFactory()
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [
                SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"],
                self.other_custom_appointment_reason,
            ],
            "symptom_ids": [symptom_1.id, symptom_2.id],
            "other_symptoms": "test symptom",
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)

        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        self.assertEqual(appt.source, AppointmentSource.LUCIAN)
        self.assertEqual(appt.time_slot_type, SlotType.APPOINTMENT)
        self.assertEqual(appt.duration, datetime.timedelta(minutes=self.video_new_appointment_type.duration))
        self.assertEqual(appt.reason, self.reason)
        self.assertEqual(appt.status, AppointmentStatus.SCHEDULED.value)
        self.assertEqual(appt.physician, self.physician)
        self.assertEqual(appt.other_symptoms, "test symptom")
        self.assertEqual(appt.symptoms.all().count(), 2)

        mock_update_custom_appointment_eventlog.send.assert_called_once_with(
            appointment_id=appt.pk,
            custom_appointment_reasons=[
                SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"],
                self.other_custom_appointment_reason,
            ],
            log_prefix="bff/crm/book-custom-appointment",
        )
        update_custom_appointment_eventlog(
            appointment_id=appt.pk,
            custom_appointment_reasons=[
                SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"],
                self.other_custom_appointment_reason,
            ],
            log_prefix="bff/crm/book-custom-appointment",
        )
        event_log = EventLog.objects.filter(
            type=EventTypeCodes.APPOINTMENT_SCHEDULED,
            user=self.patient,
            target_object_id=appt.pk,
        )
        self.assertEqual(event_log.count(), 1)
        event_log = event_log[0]
        self.assertEqual(event_log.user, self.patient)
        self.assertEqual(
            event_log.metadata["current_appointment"]["custom_appointment_reasons"],
            [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"], self.other_custom_appointment_reason],
        )

        with self.captureOnCommitCallbacks(execute=True):
            other_reason, _ = CancellationReason.objects.get_or_create(uid="test")
            self.client.post(
                f"/appointment/{appt.pk}/cancel/v2/",
                format="json",
                data={
                    "cancellation_reason_ids": [other_reason.pk],
                },
            )
            self.assertEqual(response.status_code, HTTP_200_OK)
        event_log = EventLog.objects.filter(
            type=EventTypeCodes.APPOINTMENT_CANCELLED,
            target_object_id=appt.pk,
        )
        self.assertEqual(event_log.count(), 1)

    def test_custom_appointment_for_already_existing_appointment(self):
        self.client_provider.groups.add(self.group)
        Appointment.objects.create(
            physician=self.physician,
            reason=self.reason,
            start=self.appointment_start_time,
            source=AppointmentSource.LUCIAN,
            time_slot_type=SlotType.APPOINTMENT,
            visible=True,
            duration=datetime.timedelta(minutes=self.video_appointment_type.duration),
            status=AppointmentStatus.SCHEDULED.value,
            patient=self.patient,
        )
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_409_CONFLICT)

    def test_custom_appointment_with_past_appointment_start_time(self):
        self.client_provider.groups.add(self.group)
        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": (datetime.datetime.now() - datetime.timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%SZ"),
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)

    def test_custom_appointment_outside_shift(self):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 10 AM for tomorrow
        # appointment start time 10 AM, which is outside of shift
        # should not consume timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("10:00:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 16)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
            self.assertEqual(timeslots.count(), 16)
            appt = Appointment.objects.filter(patient=self.patient)
            self.assertEqual(appt.count(), 1)
            appt = appt[0]
            appt_slots = AppointmentSlots.objects.filter(appointment=appt)
            self.assertEqual(appt_slots.count(), 0)

    def test_custom_appointment_inside_shift(self):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 11 AM for tomorrow
        # appointment start time 10 AM, which is inside shift time
        # should consume timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("11:00:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 20)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 20)
        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 2)

    def test_custom_appointment_inside_shift_with_overlap_timeslots(self):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 11 AM for tomorrow
        # appointment start time 10:05 AM, which is inside shift time
        # should consume timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("11:00:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 20)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.tomorrow_10_05am,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 20)
        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 2)

    @mock.patch("firefly.modules.insurance.utils.create_insurance_plan_review_case")
    def test_custom_appointment_inside_shift_with_existing_appointment_overlapping(
        self, mock_create_insurance_plan_review_case
    ):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 10:15 AM for tomorrow
        # existing appointment at 9:45 - 10:15 Am
        # custom_appointment appointment start time 10:00 AM, which is overlapping existing appointment time
        # should consume 10 - 10:15 timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_45am_nyc = tomorrow.replace(hour=9, minute=45, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("10:15:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 17)
        # existing appointment 9:45 - 10:15
        with self.captureOnCommitCallbacks(execute=True):
            appt = Appointment.objects.create(
                physician=self.physician,
                reason=self.reason,
                start=tomorrow_9_45am_nyc,
                source=AppointmentSource.LUCIAN,
                time_slot_type=SlotType.APPOINTMENT,
                visible=True,
                duration=datetime.timedelta(minutes=self.video_appointment_type.duration),
                status=AppointmentStatus.SCHEDULED.value,
                patient=PersonUserFactory().user,
            )
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 2)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 17)
        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 1)

    @mock.patch("firefly.modules.insurance.utils.create_insurance_plan_review_case")
    def test_custom_appointment_inside_shift_with_existing_appointment_same_time(
        self, mock_create_insurance_plan_review_case
    ):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 10:30 AM for tomorrow
        # existing appointment at 10:00 - 10:30 AM
        # custom_appointment start time 10:00 AM, which is same time as existing appointment time
        # should book appointment and consume 10 - 10:15 timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("10:30:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 18)
        # existing appointment 10 - 10:30 AM
        with self.captureOnCommitCallbacks(execute=True):
            appt = Appointment.objects.create(
                physician=self.physician,
                reason=self.reason,
                start=self.appointment_start_time,
                source=AppointmentSource.LUCIAN,
                time_slot_type=SlotType.APPOINTMENT,
                visible=True,
                duration=datetime.timedelta(minutes=self.video_appointment_type.duration),
                status=AppointmentStatus.SCHEDULED.value,
                patient=PersonUserFactory().user,
            )
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 2)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 18)
        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 2)

    def test_custom_appointment_overlapping_shift(self):
        self.client_provider.groups.add(self.group)
        # create shift 6 to 10:15 AM for tomorrow
        # appointment start time 10:00 AM, which is overlapping shift time
        # should consume 10 - 10:15 timeslot and create appointment slots
        # calling with self.captureOnCommitCallbacks to trigger post save side effects
        shift = ShiftFactory.create(
            day_of_week=self.day_of_week,
            schedule=self.schedule,
            effective_period=DateRange(self.today, self.today + datetime.timedelta(days=4)),
            start_time=datetime.datetime.strptime("06:00:00", "%H:%M:%S"),
            stop_time=datetime.datetime.strptime("10:15:00", "%H:%M:%S"),
        )
        with self.captureOnCommitCallbacks(execute=True):
            create_slots_for_provider_shift(
                provider_id=self.physician_provider.user_id,
                log_prefix="",
                start_date=self.today.strftime("%Y-%m-%d"),
                end_date=(self.today + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                dry_run_off=True,
            )
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 17)

        payload = {
            "description": "test",
            "patient_id": self.patient.id,
            "scheduled_date": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
            "custom_appointment_reasons": [SLOTS_NOT_AVAILABLE_REASONS["physician_excluded_state"]],
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post("/bff/crm/book-custom-appointment/", format="json", data=payload)
            self.assertEqual(response.status_code, HTTP_200_OK)
        timeslots = TimeSlot.objects.filter(shift=shift)
        self.assertEqual(timeslots.count(), 17)
        appt = Appointment.objects.filter(patient=self.patient)
        self.assertEqual(appt.count(), 1)
        appt = appt[0]
        appt_slots = AppointmentSlots.objects.filter(appointment=appt)
        self.assertEqual(appt_slots.count(), 1)


class AdditionalReasonsForCustomAppointmentViewTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.client_provider = self.create_provider()
        self.provider_client = FireflyTestCase.get_provider_client(provider=self.client_provider)
        # create custom_appointment permission and group
        permission_codename = "can_access_custom_appointment"
        content_type = ContentType.objects.get_for_model(Appointment)
        self.group = Group.objects.create(name="schedulingSuperUsers")
        permission = Permission.objects.create(
            codename=permission_codename,
            name="Can access custom scheduling",
            content_type=content_type,
        )
        self.group.permissions.add(permission)

        # tomorrow 10 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_10am_nyc = tomorrow.replace(hour=10, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_10am = timezone.localtime(tomorrow_10am_nyc, timezone=UTC_TIMEZONE)
        self.appointment_start_time = tomorrow_10am.strftime("%Y-%m-%dT%H:%M:%SZ")

        self.reason = AppointmentReason.VIDEO_NEW_PATIENT
        self.video_new_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.VIDEO_NEW_PATIENT,
            unique_key=AppointmentReason.VIDEO_NEW_PATIENT,
            buffer_time_in_minutes=15,
            duration=30,
            booking_window_in_weeks=4,
        )
        self.foucused_appointment_type, _ = AppointmentType.objects.get_or_create(
            name=AppointmentReason.FOCUSED_VISIT,
            unique_key=AppointmentReason.FOCUSED_VISIT,
            buffer_time_in_minutes=15,
            duration=15,
            booking_window_in_weeks=4,
        )
        self.foucused_reason = AppointmentReason.FOCUSED_VISIT
        self.physician = Physician.objects.create(
            first_name="Test",
            last_name="Physician1",
            elation_id=99999991,
            elation_user_id=22222212,
            calendar_url="https://testurl",
        )
        self.physician_provider = ProviderDetailFactory.create(physician=self.physician)

        # set timezone America/New_York for schedule
        self.schedule: ProviderSchedule = ProviderScheduleFactory.create(
            provider=self.physician_provider,
            effective_period=DateRange(datetime.datetime.now().date(), None),
            timezone=NY_TIMEZONE,
        )
        # shift 10 - 14  America/New_York time
        self.shift = ShiftFactory.create(
            day_of_week=tomorrow_10am_nyc.isoweekday(),
            schedule=self.schedule,
            effective_period=DateRange(
                datetime.datetime.now().date(), datetime.datetime.now().date() + datetime.timedelta(days=4)
            ),
            start_time=tomorrow_10am_nyc.time(),
            stop_time=(tomorrow_10am_nyc + datetime.timedelta(hours=4)).time(),
        )

        # shift exception 10 - 11  America/New_York time
        self.shift_exception = ShiftExceptionFactory.create(
            schedule=self.schedule,
            reason="OOO",
            period=DateTimeTZRange(
                tomorrow_10am_nyc,
                tomorrow_10am_nyc + datetime.timedelta(hours=1),
            ),
        )

    def test_additional_reason_for_custom_appointment_without_permission(self):
        # custom_appointment_group not added to user
        payload = {
            "appointment_date_time": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_403_FORBIDDEN)

    def test_additional_reason_for_custom_appointment_sucess_response(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)
        payload = {
            "appointment_date_time": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)

    def test_additional_reason_for_custom_appointment_with_shift_exception_exist(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)
        # scenerio 1
        # shift : 10 -14
        # shift exception : 10 - 11
        # appointment time : 10 - 10:30
        payload = {
            "appointment_date_time": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["shift_exception_exist"]]})

        # scenerio 2
        # shift : 10 -14
        # shift exception : 10 - 11
        # appointment time : 9:45 - 10:15

        # tomorrow 9:45 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_45am_nyc = tomorrow.replace(hour=9, minute=45, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_9_45am = timezone.localtime(tomorrow_9_45am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_9_45am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["shift_exception_exist"]]})

        # scenerio 3
        # shift : 10 -14
        # shift exception : 10 - 11
        # appointment time : 10:45 - 11:15

        # tomorrow 10:45 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_10_45am_nyc = tomorrow.replace(hour=10, minute=45, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_10_45am = timezone.localtime(tomorrow_10_45am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_10_45am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["shift_exception_exist"]]})

    def test_additional_reason_for_custom_appointment_with_out_of_shift(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)

        # scenerio 1
        # shift : 10 - 14
        # shift exception : 10 - 11
        # appointment time : 9:30 - 10:00

        # tomorrow 9:30 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_30am_nyc = tomorrow.replace(hour=9, minute=30, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_9_30am = timezone.localtime(tomorrow_9_30am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_9_30am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"]]})

        # scenerio 2
        # shift : 10 - 14
        # shift exception : 10 - 11
        # appointment time : 9:00 - 9:30

        # tomorrow 9:00 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_0am_nyc = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_9_0am = timezone.localtime(tomorrow_9_0am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_9_0am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"]]})

        # scenerio 3
        # shift : 10 - 14
        # shift exception : 10 - 11
        # appointment time : 14:00 - 14:30

        # tomorrow 14:00 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_14_0am_nyc = tomorrow.replace(hour=14, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_14_0am = timezone.localtime(tomorrow_14_0am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_14_0am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"]]})

        # scenerio 4
        # shift : 10 - 14
        # shift exception : 10 - 11
        # appointment time : 15:00 - 15:30

        # tomorrow 15:00 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_15_0am_nyc = tomorrow.replace(hour=15, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_15_0am = timezone.localtime(tomorrow_15_0am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_15_0am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"]]})

    def test_additional_reason_for_custom_appointment_with_existing_appointment(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)

        # scenerio 1
        # shift : 10 - 14
        # shift exception : 10 - 11
        # existing appointment: 12 - 12:30
        # appointment time : 12:00 - 12:30

        # tomorrow 12:00 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_12_0am_nyc = tomorrow.replace(hour=12, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_12_0am = timezone.localtime(tomorrow_12_0am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_12_0am.strftime("%Y-%m-%dT%H:%M:%SZ")
        # book appointment at 12 - 12:30
        appt = AppointmentFactory.create(
            physician=self.physician, start=appointment_start_time, duration=datetime.timedelta(minutes=30)
        )

        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"]]})

        # scenerio 2
        # shift : 10 - 14
        # shift exception : 10 - 11
        # existing appointment: 12 - 12:30
        # appointment time : 12:15 - 12:45

        # tomorrow 12:15 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_12_15am_nyc = tomorrow.replace(hour=12, minute=15, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_12_15am = timezone.localtime(tomorrow_12_15am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_12_15am.strftime("%Y-%m-%dT%H:%M:%SZ")

        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"]]})

        # scenerio 3
        # shift : 10 - 14
        # shift exception : 10 - 11
        # existing appointment: 12 - 12:30
        # appointment time : 11:45 - 12:15

        # tomorrow 11:45 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_11_45am_nyc = tomorrow.replace(hour=11, minute=45, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_11_45am = timezone.localtime(tomorrow_11_45am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_11_45am.strftime("%Y-%m-%dT%H:%M:%SZ")

        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"]]})

        # scenerio 3
        # shift : 10 - 14
        # shift exception : 10 - 11
        # existing appointment: 12 - 12:30
        # appointment time : 12:00 - 12:15

        # tomorrow 12:00 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_12_0am_nyc = tomorrow.replace(hour=12, minute=0, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_12_0am = timezone.localtime(tomorrow_12_0am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_12_0am.strftime("%Y-%m-%dT%H:%M:%SZ")

        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.foucused_reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": [CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"]]})

        # scenerio - 4 with status cancelled
        appt.status = AppointmentStatus.CANCELLED.value
        appt.save()
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": []})

        # scenerio - 5 with patient null
        appt.status = AppointmentStatus.SCHEDULED.value
        appt.patient = None
        appt.save()
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(json, {"additional_reasons": []})

    def test_additional_reason_for_custom_appointment_with_shift_exception_and_existing_appointment(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)
        # scenerio 1
        # shift : 10 -14
        # shift exception : 10 - 11
        # existing appointment: 10 - 10:30
        # appointment time : 10 - 10:30

        # book appointment at 10 - 10:30
        AppointmentFactory.create(
            physician=self.physician, start=self.appointment_start_time, duration=datetime.timedelta(minutes=30)
        )
        payload = {
            "appointment_date_time": self.appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(
            json,
            {
                "additional_reasons": [
                    CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["shift_exception_exist"],
                    CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"],
                ]
            },
        )

    def test_additional_reason_for_custom_appointment_with_out_of_shift_and_existing_appointment(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)

        # scenerio 1
        # shift : 10 - 14
        # shift exception : 10 - 11
        # existing appointment: 9:30 - 10:00
        # appointment time : 9:30 - 10:00

        # tomorrow 9:30 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_30am_nyc = tomorrow.replace(hour=9, minute=30, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_9_30am = timezone.localtime(tomorrow_9_30am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_9_30am.strftime("%Y-%m-%dT%H:%M:%SZ")

        # book appointment at 9:30 - 10:00
        AppointmentFactory.create(
            physician=self.physician, start=appointment_start_time, duration=datetime.timedelta(minutes=30)
        )
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }
        response = self.provider_client.get(
            "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
        )
        self.assertEqual(response.status_code, HTTP_200_OK)
        json = response.json()
        self.assertEqual(
            json,
            {
                "additional_reasons": [
                    CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["appointment_exist"],
                    CUSTOM_APPOINTMENT_ADDITIONAL_REASONS["outside_visit_shift"],
                ]
            },
        )

    def test_query_count(self):
        # custom_appointment_group added to user
        self.client_provider.groups.add(self.group)
        # tomorrow 9:30 AM
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        tomorrow_9_30am_nyc = tomorrow.replace(hour=9, minute=30, second=0, microsecond=0, tzinfo=NY_TIMEZONE)
        # Convert the New York time to UTC
        tomorrow_9_30am = timezone.localtime(tomorrow_9_30am_nyc, timezone=UTC_TIMEZONE)
        appointment_start_time = tomorrow_9_30am.strftime("%Y-%m-%dT%H:%M:%SZ")
        payload = {
            "appointment_date_time": appointment_start_time,
            "physician_id": self.physician.pk,
            "reason": self.reason,
        }

        # 1. SELECT ... FROM "auth_group"
        # 2. SELECT ... FROM "auth_permission"
        # 3. SELECT ... FROM "django_content_type"
        # 4. SELECT ... FROM "schedule_appointmenttype"
        # 5. SELECT ... FROM "schedule_shiftexception"
        # 6. SELECT ... FROM "appointments"
        # 7. SELECT ... FROM "schedule_shift"
        # 8. SELECT ... FROM "audit_phiauditlog"
        # 9. SELECT ... FROM "auth_user"
        with self.assertNumQueries(10):
            response = self.provider_client.get(
                "/bff/crm/custom-appointment-additional-reasons/", format="json", data=payload
            )
            self.assertEqual(response.status_code, 200)
