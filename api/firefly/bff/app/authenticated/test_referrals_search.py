from datetime import date, <PERSON><PERSON><PERSON>
from unittest import mock

from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.bff.app.authenticated.referrals_search import ReferralSearchView
from firefly.core.alias.models import get_content_type
from firefly.core.feature.testutils import override_switch
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonFactory
from firefly.modules.facts.factories import ServiceCategoryFactory, SpecialtyGroupFactory
from firefly.modules.facts.models import SpecialtyGroup
from firefly.modules.forms.mixin import JobStatus
from firefly.modules.health_plan.factories import HealthPlanServiceCategoryBenefitInfoFactory
from firefly.modules.health_plan.models import HealthPlanConfiguration, HealthPlanServiceCategory
from firefly.modules.insurance.constants import (
    ContractAttributionType,
    ContractPMPMType,
    EmployerName,
)
from firefly.modules.insurance.factories import (
    ContractFactory,
    InsuranceMemberInfoFactory,
    InsurancePlanFactory,
    NetworkFactory,
)
from firefly.modules.insurance.models import Employer, InsuranceMemberInfo
from firefly.modules.referral.constants import (
    ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE,
    HIGH_TCOC_SEGMENT_SPECIALTY,
    WAFFLE_SWITCH_DYNAMIC_REFERRAL_COST_VALUES,
    ProviderSearchConfig,
)
from firefly.modules.referral.factories import SteerageFactory, SteerageProviderFactory
from firefly.modules.referral.models import (
    ReasonForExceptionConfig,
    RecommendationStatus,
    ReferralPriority,
    SearchRequest,
    SteerageProvider,
    SteerageProviderStatuses,
    Waiver,
)
from firefly.modules.referral.tests.test_referral_utils import (
    set_up_referral_affected_case_categories,
)
from firefly.modules.referral.tests.test_steerage_utils import (
    set_up_steerage_affected_case_categories,
)
from firefly.modules.states.factories import StateFactory
from firefly.modules.states.models import State


class ReferralsSearchTestCase(FireflyTestCase):
    specialty: SpecialtyGroup
    state: State
    tomorrow = date.today() + timedelta(days=1)

    def load(self, steerage_id):
        url = f"/bff/app/referral-search/{steerage_id}/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        return response.data

    def setUp(self):
        super().setUp()
        self.specialty = SpecialtyGroupFactory.create(label=HIGH_TCOC_SEGMENT_SPECIALTY[0])
        self.state = StateFactory.create()
        set_up_steerage_affected_case_categories(self)
        set_up_referral_affected_case_categories(self)

        # Create a service category for benefit info tests
        self.service_category = ServiceCategoryFactory.create(label="Ref OV and Consultations")

        # Create mock results for search requests
        self.mock_results = [
            {
                "id": "provider1",
                "name": "John Doe",
                "distance": 15,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology", "Internal Medicine"],
                "languages": ["English", "Spanish"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Main Clinic",
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "Suite 100",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
            {
                "id": "provider2",
                "name": "Dr. Jane Smith",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.NEUTRAL,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Female",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Downtown Clinic",
                        "address_details": {
                            "address_line_1": "456 Downtown Ave",
                            "address_line_2": "",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
        ]

    def test_get_unauthorized(self):
        # Create a steerage for a random Person
        steerage = SteerageFactory.create(person=PersonFactory(), priority=ReferralPriority.MEDIUM)

        # Try to access the search results for a steerage that doesn't belong to the default test user
        url = f"/bff/app/referral-search/{steerage.id}/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 401)

    def test_get_search_results(self):
        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a waiver for the steerage
        waiver = Waiver.objects.create(person=self.patient.person, is_active=True)
        steerage.waiver = waiver
        steerage.save()

        # Create a search request with mock results
        mock_results = [
            {
                "id": "provider1",
                "name": "John Doe",
                "distance": 15,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology", "Internal Medicine"],
                "languages": ["English", "Spanish"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Main Clinic",
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "Suite 100",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
            {
                "id": "provider2",
                "name": "Dr. Jane Smith",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Female",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Downtown Clinic",
                        "address_details": {
                            "address_line_1": "456 Downtown Ave",
                            "address_line_2": "",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
            {
                "id": "provider3",
                "name": "Dr. Not Recommended",
                "distance": 10,
                "in_network": False,
                "recommendation_status": RecommendationStatus.NOT_RECOMMENDED,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Far Away Clinic",
                        "address_details": {
                            "address_line_1": "789 Far St",
                            "address_line_2": "",
                            "city": "Far City",
                            "state": "MA",
                            "zip": "54321",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                    }
                ],
            },
            {
                "id": "provider4",
                "name": "Dr. Neutral",
                "distance": 7,
                "in_network": True,
                "recommendation_status": RecommendationStatus.NEUTRAL,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Neutral Clinic",
                        "address_details": {
                            "address_line_1": "123 Neutral St",
                            "address_line_2": "",
                            "city": "Neutral City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
        ]

        with self.captureOnCommitCallbacks(execute=True):
            search_request = SearchRequest.objects.create(
                is_automated_suggestion=True,
                address="123 Test St, Test City, Test State 12345",
                distance=50,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                page_size=100,
                page=1,
                results=mock_results,
                steerage=steerage,
                status=JobStatus.SKIPPED,
            )

        # Get the search results
        data = self.load(steerage.id)

        # Verify the response structure
        self.assertEqual(data["options"]["location"], search_request.address)
        self.assertEqual(data["search_request_id"], search_request.id)
        self.assertEqual(data["care_key_label"], f"{steerage.specialty_group.label} Specialist Visit")

        # Verify that only recommended providers are included
        self.assertEqual(len(data["list"]), 3)
        self.assertIn("provider1", data["list"])
        self.assertIn("provider2", data["list"])
        self.assertIn("provider4", data["list"])
        self.assertNotIn("provider3", data["list"])

        # Verify that the results are sorted by distance
        self.assertEqual(data["list"][0], "provider2")
        self.assertEqual(data["list"][2], "provider1")
        self.assertEqual(data["list"][1], "provider4")

        # Verify the provider details
        provider1 = data["results"]["provider1"]
        self.assertEqual(provider1["name"], "John Doe")
        self.assertEqual(provider1["distance"], 15)
        self.assertEqual(provider1["location_name"], "Main Clinic")
        self.assertEqual(provider1["location_phone"], "(*************")
        self.assertTrue(provider1["is_care_key_eligible"])
        self.assertEqual(provider1["cost_final"], 0)
        self.assertEqual(provider1["cost_original"], 100)
        self.assertTrue(provider1["cost_show_comparison"])

        # Verify the neutral recommendation is not care key eligible
        provider4 = data["results"]["provider4"]
        self.assertFalse(provider4["is_care_key_eligible"])

        # Verify that the original results are included
        self.assertEqual(len(data["results_original"]), 3)
        self.assertIn("provider1", data["results_original"])
        self.assertIn("provider2", data["results_original"])
        self.assertIn("provider4", data["results_original"])

    def test_post_select_provider(self):
        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a search request with mock results
        mock_results = [
            {
                "id": "provider1",
                "first_name": "John",
                "middle_name": "",
                "last_name": "Doe",
                "name": "John Doe",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology", "Internal Medicine"],
                "languages": ["English", "Spanish"],
                "gender": "Male",
                "npi": "**********",
                "composite_score": 5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "provider_type": None,
                "partnership": None,
                "network_partner_agreement_type": None,
                "recommendation_reason_labels": "Literally the best",
                "unique_identifier": "foobar",
                "talon_recommendation_factors": None,
                "locations": [
                    {
                        "name": "Main Clinic",
                        "partner_name": None,
                        "confidence": ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "Suite 100",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"phone": "**********", "type": "office"}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            }
        ]

        with self.captureOnCommitCallbacks(execute=True):
            search_request = SearchRequest.objects.create(
                is_automated_suggestion=True,
                steerage=steerage,
                address="123 Test St, Test City, Test State 12345",
                distance=50,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                page_size=100,
                page=1,
                results=mock_results,
                status=JobStatus.SKIPPED,
            )

        # Create an existing steerage provider that should be rejected
        existing_provider = SteerageProviderFactory.create(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
            state=self.state,
        )

        # Select a provider
        url = f"/bff/app/referral-search/{steerage.id}/"
        data = {
            "action": "select_provider",
            "search_request_id": search_request.id,
            "provider": mock_results[0],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 200)

        # Verify that the existing provider was rejected
        existing_provider.refresh_from_db()
        self.assertEqual(existing_provider.status, SteerageProviderStatuses.REJECTED)
        self.assertEqual(existing_provider.reason_for_rejection, ReasonForExceptionConfig.MEMBER_CHANGED_PROVIDER)
        self.assertIsNone(existing_provider.member_selected_at)

        # Verify that a new provider was created
        new_providers = SteerageProvider.objects.filter(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
        )
        self.assertEqual(new_providers.count(), 1)
        new_provider = new_providers.first()
        self.assertEqual(new_provider.member_selected_at, date.today())
        self.assertEqual(new_provider.recommendation_status, RecommendationStatus.RECOMMENDED)

    def test_select_provider_recommendation_status(self):
        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a search request with mock results - one neutral provider and one with no status
        mock_results = [
            {
                "id": "provider_neutral",
                "first_name": "John",
                "middle_name": "",
                "last_name": "Doe",
                "name": "John Doe",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.NEUTRAL,
                "specialties": ["Cardiology", "Internal Medicine"],
                "languages": ["English", "Spanish"],
                "gender": "Male",
                "npi": "**********",
                "composite_score": 5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "provider_type": None,
                "partnership": None,
                "network_partner_agreement_type": None,
                "recommendation_reason_labels": "Literally the best",
                "unique_identifier": "foobar",
                "talon_recommendation_factors": None,
                "locations": [
                    {
                        "name": "Main Clinic",
                        "partner_name": None,
                        "confidence": ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "Suite 100",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"phone": "**********", "type": "office"}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
            {
                "id": "provider_no_status",
                "first_name": "John",
                "middle_name": "",
                "last_name": "Doe",
                "name": "John Doe",
                "distance": 5,
                "in_network": True,
                # "recommendation_status": RecommendationStatus.RECOMMENDED, // <-- This is missing
                "specialties": ["Cardiology", "Internal Medicine"],
                "languages": ["English", "Spanish"],
                "gender": "Male",
                "npi": "**********",
                "composite_score": 5,
                "firefly_recommendation_factors": {
                    "cost_score_out_of_five": 5.0,
                    "quality_score_out_of_five": 5,
                    "average_rating_out_of_five": None,
                    "number_of_ratings": 0,
                    "expertise_score_out_of_five": None,
                },
                "provider_type": None,
                "partnership": None,
                "network_partner_agreement_type": None,
                "recommendation_reason_labels": "Literally the best",
                "unique_identifier": "foobar",
                "talon_recommendation_factors": None,
                "locations": [
                    {
                        "name": "Main Clinic",
                        "partner_name": None,
                        "confidence": ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "Suite 100",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"phone": "**********", "type": "office"}],
                        "talon_associated_procedure_prices": None,
                    }
                ],
            },
        ]

        with self.captureOnCommitCallbacks(execute=True):
            search_request = SearchRequest.objects.create(
                is_automated_suggestion=True,
                steerage=steerage,
                address="123 Test St, Test City, Test State 12345",
                distance=50,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                page_size=100,
                page=1,
                results=mock_results,
                status=JobStatus.SKIPPED,
            )

        # Select the NEUTRAL provider
        url = f"/bff/app/referral-search/{steerage.id}/"
        data = {
            "action": "select_provider",
            "search_request_id": search_request.id,
            "provider": mock_results[0],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 200)

        # Verify that the provider was created with NEUTRAL status
        neutral_provider = SteerageProvider.objects.get(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
        )
        self.assertEqual(neutral_provider.recommendation_status, RecommendationStatus.NEUTRAL)

        # Now select the provider with no recommendation status
        data = {
            "action": "select_provider",
            "search_request_id": search_request.id,
            "provider": mock_results[1],
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 200)

        # Verify that the first provider was rejected
        neutral_provider.refresh_from_db()
        self.assertEqual(neutral_provider.status, SteerageProviderStatuses.REJECTED)

        # Verify that the new provider was created with NO_RATING status
        no_status_provider = SteerageProvider.objects.get(
            steerage=steerage,
            status=SteerageProviderStatuses.ACCEPTED,
        )
        self.assertEqual(no_status_provider.recommendation_status, RecommendationStatus.NO_RATING)
        # Verify they are different providers
        self.assertNotEqual(neutral_provider.id, no_status_provider.id)

    def test_query_count(self):
        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a waiver for the steerage
        waiver = Waiver.objects.create(person=self.patient.person, is_active=True)
        steerage.waiver = waiver
        steerage.save()

        # Create a search request with mock results
        mock_results = [
            {
                "id": "provider1",
                "name": "John Doe",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Main Clinic",
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"phone": "**********", "type": "office"}],
                    }
                ],
            }
        ]

        # Create a bunch of search requests to ensure that the query count is limited
        with self.captureOnCommitCallbacks(execute=True):
            for _ in range(10):
                SearchRequest.objects.create(
                    is_automated_suggestion=True,
                    steerage=steerage,
                    address="123 Test St, Test City, Test State 12345",
                    distance=50,
                    min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                    page_size=100,
                    page=1,
                    results=mock_results,
                    status=JobStatus.SKIPPED,
                )

        # Check query count for GET request
        view = ReferralSearchView.as_view()
        factory = APIRequestFactory()
        request = factory.get(f"/bff/app/referral-search/{steerage.id}/", format="json")
        setattr(request, "lucian_user_tenant", self.tenant)
        force_authenticate(request, user=self.patient)

        # 1. SELECT "referral_steerage"."id", "referral_steerage"."created_at", "referral_steera...
        # 2. SELECT "user_person"."id", "user_person"."created_at", "user_person"."created_by_id...
        # 3. SELECT "user_person"."id", "user_person"."created_at", "user_person"."created_by_id...
        # 4. SELECT "programs_programinfo"."created_at", "programs_programinfo"."created_by_id",...
        # 5. SELECT "programs_program"."created_at", "programs_program"."created_by_id", "progra...
        # 6. SELECT 1 AS "a" FROM "programs_programenrollment" WHERE ("programs_programenrollmen...
        # 7. SELECT "referral_searchrequest"."id", "referral_searchrequest"."created_at", "refer...
        # 8. SELECT "facts_specialty_group"."created_at", "facts_specialty_group"."created_by_id...
        # 9. SELECT "referral_waiver"."id", "referral_waiver"."created_at", "referral_waiver"."c...

        with self.assertNumQueries(9):
            view(request, id=steerage.id)

    @mock.patch(
        "firefly.modules.referral.serializers.get_cached_alias_id_from_insurance_network",
    )
    def test_search_action(self, mock_alias_id):
        # Setup mapped networks for the patient
        self.patient.person.insurance_info = InsuranceMemberInfoFactory.create(
            plan_type=InsuranceMemberInfo.PLAN_TYPE_PPO.lower()
        )
        self.patient.person.save()
        plan = InsurancePlanFactory.create()
        network = NetworkFactory.create()
        plan.networks.add(network)
        self.patient.person.insurance_info.insurance_plan = plan
        self.patient.person.insurance_info.save()

        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Test initial search request creation
        url = f"/bff/app/referral-search/{steerage.id}/"
        data = {"action": "search", "search_params": {"zipcode": "12345", "page": 1}}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertIn("job_id", response.data)
        self.assertIn("status", response.data)
        self.assertEqual(response.data["status"], JobStatus.PENDING)
        search_request_id = response.data["job_id"]

        # Test polling for PENDING status
        response = self.client.get(f"{url}?job_id={search_request_id}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], JobStatus.PENDING)

        # Update status to IN_PROGRESS and test
        search_request = SearchRequest.objects.get(id=search_request_id)
        search_request.status = JobStatus.IN_PROGRESS
        search_request.save()
        response = self.client.get(f"{url}?job_id={search_request_id}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], JobStatus.IN_PROGRESS)

        # Update status to FAILED and test
        search_request.status = JobStatus.FAILED
        search_request.save()
        response = self.client.get(f"{url}?job_id={search_request_id}")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["error"], "Search request failed")

        # Test with invalid job_id
        response = self.client.get(f"{url}?job_id=99999")
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.data["error"], "Search request with id 99999 not found")

        # Test with job_id belonging to different steerage
        other_steerage = SteerageFactory.create(person=PersonFactory(), priority=ReferralPriority.MEDIUM)
        other_search_request = SearchRequest.objects.create(
            is_automated_suggestion=True,
            steerage=other_steerage,
            address="123 Test St, Test City, Test State 12345",
            distance=50,
            min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
            page_size=100,
            page=1,
            results=[],
        )
        response = self.client.get(f"{url}?job_id={other_search_request.id}")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["error"], "The search request job does not belong to this steerage")

        # Update status to COMPLETE and add results
        mock_results = [
            {
                "id": "provider1",
                "name": "John Doe",
                "distance": 5,
                "in_network": True,
                "recommendation_status": RecommendationStatus.RECOMMENDED,
                "specialties": ["Cardiology"],
                "languages": ["English"],
                "gender": "Male",
                "npi": "**********",
                "locations": [
                    {
                        "name": "Main Clinic",
                        "address_details": {
                            "address_line_1": "123 Main St",
                            "address_line_2": "",
                            "city": "Test City",
                            "state": "MA",
                            "zip": "12345",
                        },
                        "phone_numbers": [{"phone": "**********", "details": "primary"}],
                        "fax_numbers": [{"fax": "**********", "detail": None}],
                    }
                ],
            }
        ]
        search_request.status = JobStatus.COMPLETE
        search_request.results = mock_results
        search_request.save()

        response = self.client.get(f"{url}?job_id={search_request_id}")
        self.assertEqual(response.status_code, 200)

        # Verify the completed search results
        self.assertIn("list", response.data)
        self.assertIn("results", response.data)
        self.assertIn("provider1", response.data["list"])

        # Verify provider details
        provider = response.data["results"]["provider1"]
        self.assertEqual(provider["name"], "John Doe")
        self.assertEqual(provider["distance"], 5)
        self.assertEqual(provider["location_name"], "Main Clinic")
        self.assertEqual(provider["location_phone"], "(*************")

    @override_switch(WAFFLE_SWITCH_DYNAMIC_REFERRAL_COST_VALUES, active=True)
    def test_get_search_results_with_dynamic_cost_values(self):
        """Test that the correct cost values are supplied through the response."""
        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a waiver for the steerage
        waiver = Waiver.objects.create(person=self.patient.person, is_active=True)
        steerage.waiver = waiver
        steerage.save()

        # Set up benefit info for the user
        employer_1, _ = Employer.objects.get_or_create(name=EmployerName.FIREFLY_HEALTH)
        self.patient.person.employer = employer_1
        self.patient.person.save()
        contract_1 = ContractFactory(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": employer_1.id,
                "allowable_group_ids": ["123123"],
                "is_coverage_program_enrollment_enabled": True,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.AUTO_ATTRIBUTED,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.employer),
            contracted_entity=employer_1,
        )

        health_plan_1 = HealthPlanConfiguration.objects.create(
            contract=contract_1,
            pbm_name="The Greatest PBM",
            pbm_url="https://fireflyhealth.com",
            deductible_firefly_inn=0,
            deductible_oon=100,
            deductible_self_inn=50,
        )

        self.patient.person.attribution.contract = contract_1
        self.patient.person.attribution.save()
        self.patient.person.attribution.refresh_from_db()
        contract_service_category = ServiceCategoryFactory.create(
            label=ELIGIBLE_SERVICE_CATEGORY_LIST_FOR_SELF_SERVE[0], is_authorization_specific=False
        )

        health_plan_service_category = HealthPlanServiceCategory.objects.create(
            health_plan=health_plan_1, service_category=contract_service_category
        )

        # Create benefit info with specific copay values we can test
        HealthPlanServiceCategoryBenefitInfoFactory.create(
            health_plan_service_category=health_plan_service_category,
            inn_copay=75,
            waiver_copay=25,
        )
        self.assertEquals(self.patient.person.attribution.contract, contract_1)

        # Create a search request with mock results
        with self.captureOnCommitCallbacks(execute=True):
            SearchRequest.objects.create(
                is_automated_suggestion=True,
                address="123 Test St, Test City, Test State 12345",
                distance=50,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                page_size=100,
                page=1,
                results=self.mock_results,
                steerage=steerage,
                status=JobStatus.COMPLETE,
            )

        # Get the search results from the endpoint
        data = self.load(steerage.id)

        # Verify the cost values for the recommended provider (provider1)
        provider1 = data["results"]["provider1"]
        self.assertEqual(provider1["cost_final"], 25)  # Should be the waiver_copay value
        self.assertEqual(provider1["cost_original"], 75)  # Should be the inn_copay value
        self.assertTrue(provider1["cost_show_comparison"])
        self.assertTrue(provider1["is_care_key_eligible"])

        # Verify the cost values for the non-recommended provider (provider2)
        provider2 = data["results"]["provider2"]
        self.assertEqual(provider2["cost_final"], 75)  # Should be inn_copay (not care key eligible)
        self.assertEqual(provider2["cost_original"], 75)  # Should be inn_copay
        self.assertFalse(provider2["cost_show_comparison"])
        self.assertFalse(provider2["is_care_key_eligible"])

    @override_switch(WAFFLE_SWITCH_DYNAMIC_REFERRAL_COST_VALUES, active=True)
    def test_get_search_results_missing_benefit_info(self):
        """Test that a 400 error is raised if the correct benefit info model is not found."""

        # Create a steerage for the current user
        steerage = SteerageFactory.create(person=self.patient.person, priority=ReferralPriority.MEDIUM)
        steerage.specialty_group = self.specialty
        steerage.save()

        # Create a waiver for the steerage
        waiver = Waiver.objects.create(person=self.patient.person, is_active=True)
        steerage.waiver = waiver
        steerage.save()

        # Create attribution and contract(person=self.patient.person, contract=contract)
        self.patient.person.attribution.contract = None
        self.patient.person.attribution.save()

        # Create a search request
        with self.captureOnCommitCallbacks(execute=True):
            SearchRequest.objects.create(
                is_automated_suggestion=True,
                address="123 Test St, Test City, Test State 12345",
                distance=50,
                min_location_confidence=ProviderSearchConfig.MIN_LOCATION_CONFIDENCE_SCORE,
                page_size=100,
                page=1,
                results=self.mock_results,
                steerage=steerage,
                status=JobStatus.COMPLETE,
            )

        # Make the request to the endpoint - should fail with 400 error because
        # we don't have the required benefit info
        url = f"/bff/app/referral-search/{steerage.id}/"
        response = self.client.get(url)

        # Verify the 400 error was returned
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data["error"], "Failed to retrieve benefit info")
