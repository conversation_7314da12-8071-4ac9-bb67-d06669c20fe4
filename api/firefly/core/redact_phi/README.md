# Redact PHI

This app handles the actual redaction of Protected Health Information (PHI) in API responses, replacing sensitive patient data with placeholder values when unauthorized users access VIP patient information.

## Business Logic

The PHI redaction workflow works as follows:

1. **VIP Detection**: Identifies patients with `Person.request_auth_for_phi = True` (VIPs)
2. **Access Check**: Determines if requesting user has authorized access
3. **Selective Redaction**: Redacts PHI in list views, blocks access in detail views
4. **Response Processing**: Replaces sensitive fields with redacted placeholders

## Overview

This app works in conjunction with the `phi_access_control` app:
- **phi_access_control**: Handles authorization and access logging upstream
- **redact_phi**: Performs actual data redaction in API responses

## VIP Patient Identification

Currently, PHI redaction applies to patients who are also Firefly employees, identified by the `Person.request_auth_for_phi` flag. The business refers to these patients as "VIPs".

## Redaction Strategy

### List Requests
VIP patients' PHI is redacted in list views (e.g., Tasks list, Patient list):
- **Names**: Replaced with "REDACTED"
- **Contact Info**: Phone numbers and emails masked
- **Identifiers**: Patient IDs and other identifying information hidden

### Detail Requests
Patient-specific requests for VIP patients are handled differently:
- **Authorized Access**: Full data returned if user has proper permissions
- **Unauthorized Access**: Request rejected entirely (no redacted data shown)

## Implementation Guide

### Serializer-Based Redaction

Redaction happens during Django REST Framework [Serialization](https://www.django-rest-framework.org/api-guide/serializers/). Configure redaction in the serializer's `Meta` class:

#### Configuration Requirements
1. **VIP Detection Path**: How to identify if a patient is a VIP
2. **Redaction Fields**: Which fields to redact for VIP patients

#### Example Implementation
```python
class MyListSerializer(RedactPHIListSerializer, serializers.ListSerializer):
    class Meta(RedactPHIListSerializer.Meta):
        # Fields to redact if patient is VIP
        redact_phi_field_paths = [
            ["patient_meta", "first_name"],
            ["patient_meta", "last_name"],
            ["patient_meta", "phone_number"],
            ["patient_meta", "email"]
        ]
        # Path to VIP flag
        redact_phi_test_path = ["patient_meta", "request_auth_for_phi"]
```

#### How It Works
1. **Field Path Navigation**: Uses nested field paths to access data in complex objects
2. **VIP Check**: Evaluates `redact_phi_test_path` to determine if redaction needed
3. **Field Replacement**: Replaces specified fields with "REDACTED" placeholder
4. **Selective Processing**: Only processes items where VIP flag is True

#### Implementation Details
- **Looker Integration**: PHI data normally omitted from Looker exports
- **Internal Data**: System pulls PHI from internal sources when needed
- **VIP Override**: For VIP patients, redacts data instead of displaying

### Usage Examples

#### API Response Before Redaction
```json
{
  "results": [
    {
      "id": 123,
      "patient_meta": {
        "first_name": "John",
        "last_name": "Doe",
        "phone_number": "555-1234",
        "request_auth_for_phi": true
      }
    }
  ]
}
```

#### API Response After Redaction
```json
{
  "results": [
    {
      "id": 123,
      "patient_meta": {
        "first_name": "REDACTED",
        "last_name": "REDACTED",
        "phone_number": "REDACTED",
        "request_auth_for_phi": true
      }
    }
  ]
}
```

## Integration Points

### PHI Access Control Integration
- **Upstream Authorization**: `phi_access_control` determines access permissions
- **Downstream Redaction**: `redact_phi` handles actual data masking
- **Workflow**: Authorization check → redaction decision → response formatting