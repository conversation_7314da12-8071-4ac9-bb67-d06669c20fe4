from typing import List

from rest_framework.serializers import BaseSerializer

from .constants import FORCE_REDACT_PHI_KEY, REDACT_STRING, TEST_REDACT_PHI_KEY
from .utils import deep_get_bool, deep_replace_value


class RedactPHIListSerializer(BaseSerializer):
    """
    Allows defining PHI values in the return value which should conditionally be redacted
    """

    class Meta:
        # The condition which tells us whether or not we need to redact PHI
        # This should be the key path to a boolean value
        # e.g ["patient_meta", "redact_phi"] will redact PHI for an object like:
        # {"patient_meta": {"foo": "bar", "redact_phi": True}}
        #
        # If this is not set, the other way to tell a Serializer
        # whether or not <PERSON><PERSON><PERSON> needs to redacted
        # is by calling set_force_redact_phi(), which passes down a flag in the Serializer context
        # This might be helpful when passing the decision to redact PHI down from a parent Serializer
        # to child Serializers
        redact_phi_test_path: List[str] = []
        # The key paths to the string values which should be redacted with the REDACT_STRING
        # e.g [["patient_meta", "foo"], ["patient_meta", "bar"]]
        # would replace {"patient_meta": {"foo": "PHI", "bar": "PHI"}}
        # with {"patient_meta": {"foo": "****", "bar": ****}}
        redact_phi_field_paths: List[List[str]] = []

    def to_representation(self, instance):
        meta_cls = self.Meta
        need_to_redact_is_testable = hasattr(meta_cls, TEST_REDACT_PHI_KEY) or self.force_redact_phi_enabled is not None
        if not need_to_redact_is_testable or not hasattr(meta_cls, "redact_phi_field_paths"):
            raise Exception("RedactPHIListSerializer Meta class requires redact_phi_test_path, redact_phi_field_paths")

        self.meta_cls_redact_phi_test_path = meta_cls.redact_phi_test_path
        self.meta_cls_redact_phi_field_paths = meta_cls.redact_phi_field_paths

        representation = super().to_representation(instance)

        if isinstance(representation, dict):
            # Handle paginated List Views, which instantiate a Serializer
            # once per item in the data's "results" value
            return self.redact_phi_in_item(item=representation)
        elif isinstance(representation, List):
            # Handle standard List Views
            return self.redact_phi_in_list(list_data=representation)

        raise Exception(
            f"RedactPHIListSerializer received an unexpected type from to_representation: {representation.__class__}"
        )

    def redact_phi_in_list(self, list_data: List):
        for item in list_data:
            self.redact_phi_in_item(item)
        return list_data

    def redact_phi_in_item(self, item):
        if self.should_redact_phi_in_item(item) is True:
            for path_arr in self.meta_cls_redact_phi_field_paths:
                deep_replace_value(item, path_arr, REDACT_STRING)
        return item

    def should_redact_phi_in_item(self, item) -> bool:
        if self.force_redact_phi_enabled is not None:
            return self.force_redact_phi_enabled
        return deep_get_bool(item, self.meta_cls_redact_phi_test_path)

    def set_force_redact_phi(self, val: bool):
        self.context[FORCE_REDACT_PHI_KEY] = val

    @property
    def force_redact_phi_enabled(self):
        return self.context.get(FORCE_REDACT_PHI_KEY, None)
