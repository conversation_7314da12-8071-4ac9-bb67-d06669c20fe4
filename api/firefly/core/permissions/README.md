# Permissions

The Permissions module provides a comprehensive role-based access control (RBAC) system for healthcare data protection. It implements fine-grained permissions for PHI (Protected Health Information) and PII (Personally Identifiable Information) access based on user roles and data ownership.

## Business Context

Healthcare applications require strict access controls to comply with HIPAA and other regulations. This module provides:
- Role-based access control for different user types (clinicians, patients, staff)
- PHI/PII access permissions with read/write granularity
- Self-access permissions for patients to view their own data
- Administrative permissions for system management

## Permission Models

### PHI (Protected Health Information)
Defines healthcare-specific data access permissions:

**Permission Types:**
- `read_all_phi` - Read all patient health information (clinicians, admins)
- `read_self_phi` - Read own health information (patients)
- `write_all_phi` - Write all patient health information (clinicians, admins)
- `write_self_phi` - Write own health information (patients)

**Model Configuration:**
```python
class PHI(models.Model):
    class Meta:
        managed = False  # Permission-only model
        default_permissions = ()  # Custom permissions only
        permissions = (
            ("read_all_phi", "Read all PHI"),
            ("read_self_phi", "Read self PHI"),
            ("write_all_phi", "Write all PHI"),
            ("write_self_phi", "Write self PHI"),
        )
```

### PII (Personally Identifiable Information)
Defines general personal data access permissions:

**Permission Types:**
- `read_all_pii` - Read all personal information (staff, admins)
- `read_self_pii` - Read own personal information (all users)
- `write_all_pii` - Write all personal information (staff, admins)
- `write_self_pii` - Write own personal information (all users)

## User Groups and Roles

### Clinical Roles

**Clinician Group:**
- Full PHI and PII access (read/write all)
- Complete patient data access for care delivery
- Administrative capabilities for clinical workflows

**OnboardingGuide Group:**
- Full PHI and PII access for member onboarding
- Patient enrollment and initial care setup
- Care coordination and member support

**PracticeCoordinator Group:**
- Full PHI and PII access for practice management
- Appointment scheduling and care coordination
- Administrative oversight of clinical operations

### Administrative Roles

**SystemAdmin Group:**
- Full system access (read/write all data types)
- System configuration and user management
- Complete administrative privileges

**Engineer Group:**
- PII access for system development and debugging
- No PHI access for privacy protection
- Technical system access without patient data

**Staff Group:**
- Limited PII access for operational support
- No PHI access for privacy protection
- Basic system access for administrative tasks

### Patient Role

**Patient Group:**
- Self-access only (read/write own data)
- Personal health information access
- Limited to own records and care team interactions

## Permission Implementation

### Group Configuration
Groups are configured through JSON fixtures and migrations:

```json
{
  "Clinician": [
    "read_all_non_phi",
    "read_all_phi", 
    "read_all_pii",
    "write_all_non_phi",
    "write_all_phi",
    "write_all_pii"
  ],
  "Patient": [
    "read_self_non_phi",
    "read_self_phi",
    "read_self_pii", 
    "write_self_non_phi",
    "write_self_phi",
    "write_self_pii"
  ]
}
```

### Permission Assignment
Users are assigned to groups through:
- **Automatic Assignment**: Based on user type during registration
- **Manual Assignment**: Through Django admin interface
- **Management Commands**: Bulk user group management
- **Migration Scripts**: Initial group setup and permission assignment

## Integration with Django Auth

### Permission Checking
The system integrates with Django's built-in permission framework:

```python
# Check PHI access permission
if request.user.has_perm('permissions.read_all_phi'):
    # Allow access to all patient data
    
# Check self-access permission  
if request.user.has_perm('permissions.read_self_phi'):
    # Allow access to own data only
```

### Custom Permission Classes
REST framework permission classes implement business logic:

```python
class CanViewPHI(BasePermission):
    def has_permission(self, request, view):
        return (request.user.has_perm('permissions.read_all_phi') 
                or request.user.has_perm('permissions.read_self_phi'))
                
    def has_object_permission(self, request, view, obj):
        if request.user.has_perm('permissions.read_all_phi'):
            return True
        return obj.patient == request.user  # Self-access only
```

## Management Commands

### update_django_admin_access
Manages Django admin access for engineers and superusers:
- Assigns users to Engineer group
- Manages superuser status
- Ensures proper administrative access controls

**Usage:**
```bash
python manage.py update_django_admin_access \
    --engineer_ids 1 2 3 \
    --superuser_ids 4 5
```

### Group Management
Automated group assignment and permission management:
- Initial group creation through migrations
- Permission updates through management commands
- User assignment based on role changes

## Security Features

### Principle of Least Privilege
- Users receive minimum permissions required for their role
- Self-access permissions for patients protect privacy
- Administrative permissions separated from clinical access

### Data Protection
- PHI access strictly controlled and audited
- PII access based on business need
- Engineer access excludes patient health information

### Access Control
- Group-based permissions for role management
- Object-level permissions for data ownership
- Integration with audit logging for compliance

## Configuration

### Permission Setup
Permissions are configured through:
1. **Model Definitions**: PHI and PII permission models
2. **Fixtures**: JSON configuration for group permissions
3. **Migrations**: Automatic group creation and permission assignment
4. **Management Commands**: Ongoing permission management

### Group Assignment
Users are assigned to groups based on:
- **User Type**: Automatic assignment during registration
- **Role Changes**: Updates through user management workflows
- **Administrative Actions**: Manual assignment through admin interface

## Compliance Considerations

### HIPAA Compliance
- Minimum necessary access for PHI
- Role-based access controls for healthcare data
- Audit integration for access tracking
- Patient self-access rights implementation

### Privacy Protection
- Separation of PHI and PII permissions
- Self-access limitations for patient data
- Administrative oversight of permission changes
- Regular permission audits and reviews
