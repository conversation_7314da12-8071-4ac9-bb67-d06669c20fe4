from django.contrib.auth.models import Group
from django.db import models

from .constants import ASSIGNMENT_SCHEME_CHOICES, AssignmentScheme


class AssignableMixin(models.Model):
    class Meta:
        abstract = True

    # DO NOT COPY-PASTE: Prefer TextField over <PERSON><PERSON><PERSON><PERSON>
    assignment_scheme = models.CharField(  # noqa: TID251
        max_length=50,
        blank=False,
        null=False,
        choices=ASSIGNMENT_SCHEME_CHOICES,
        default=AssignmentScheme.ASSIGN_TO_NONE,
    )

    # Once default_assignee is deprecated, owner/assignee to be fetched from AssigneeGroupUser model
    default_assignee_owner_group = models.ForeignKey(
        "user.AssigneeGroup",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        related_name="+",
        verbose_name="Default owner - Assignee Group",
        help_text=(
            "Used for 'Assign to Default Assignee' or as a backup for 'Assign to a role in patient's Care Team' "
            "assignment scheme"
        ),
    )

    # The default user to assign the task to for an assignment scheme
    default_assignee_group = models.ForeignKey(
        Group,
        null=True,
        blank=True,
        related_name="+",
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
        verbose_name="Default owner - Role",
        help_text=(
            "Used for 'Assign to Group - Round Robin' or 'Assign to a role in patient's Care Team' assignment scheme"
        ),
    )
