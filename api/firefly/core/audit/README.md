# Audit

The Audit module provides comprehensive audit logging and compliance tracking for healthcare data access and administrative actions. It ensures regulatory compliance through detailed access logs and request tracking for PHI (Protected Health Information) and administrative operations.

## Business Context

Healthcare applications require strict audit trails for regulatory compliance (HIPAA, SOX, etc.). This module provides:
- PHI access logging for all sensitive patient data interactions
- Django admin action tracking for administrative operations
- Request logging with user attribution and session tracking
- Compliance-ready audit trails for regulatory reporting

## Core Models

### PHIAuditLog
Tracks access to Protected Health Information for compliance:

**Access Tracking:**
- `route` - URL path accessed containing PHI
- `action` - HTTP method (GET, POST, PUT, DELETE)
- `user_id` - User who accessed the PHI
- `created_at` - Timestamp of access

**Audit Features:**
- Extends BaseModelV3 for complete change history
- Automatic logging via middleware for PHI-enabled endpoints
- Read-only admin interface for compliance review
- Integration with permission classes for access control

### DjangoAdminAuditLog
Tracks administrative actions in Django admin:

**Administrative Tracking:**
- `route` - Admin URL accessed
- `action` - Admin action performed (view, add, change, delete)
- `user_id` - Administrator who performed the action
- `created_at` - Timestamp of administrative action

**Security Features:**
- Non-foreign key user_id to prevent data loss
- Indexed fields for efficient audit queries
- Immutable audit records for compliance integrity

## Middleware Components

### PHIAuditLogMiddleware
Automatically logs PHI access for compliance:

**Automatic Detection:**
- Identifies PHI-enabled views through permission classes
- Detects IsOwnerV2 and IsProvider permissions
- Logs successful requests (2xx status codes)
- Captures complete request context

**Implementation:**
```python
class PHIAuditLogMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        if (is_success(response.status_code) 
            and self._has_phi(request.view_func.view_class)):
            create_phi_audit_log(request)
        return response
```

### RequestLoggingMiddleware
Comprehensive request logging with security features:

**Request Tracking:**
- User ID and session tracking
- Authentication method logging
- Tenant context for multi-tenant operations
- Request body logging with sensitive data redaction

**Security Features:**
- Automatic password redaction in request bodies
- Configurable sensitive field redaction
- Complete request/response cycle logging
- Integration with Datadog for monitoring

## Utility Functions

### create_phi_audit_log()
Creates PHI audit log entries:
- Extracts route, action, and user from request
- Creates immutable audit record
- Integrates with BaseModelV3 change tracking

## Admin Interface

### PHIAuditLogAdmin
Read-only interface for compliance review:
- Displays access timestamp, action, route, and user
- Search and filter capabilities for audit investigations
- Complete read-only access to preserve audit integrity
- Integration with BaseModelV3 change history

## Integration Points

### Permission System
- **IsOwnerV2**: Triggers PHI logging for owner-based access
- **IsProvider**: Triggers PHI logging for provider access
- **Permission Classes**: Automatic detection of PHI-enabled endpoints

### BaseModelV3 Integration
- **Change History**: Complete audit trail through pghistory
- **User Attribution**: Automatic user tracking for all changes
- **Compliance**: Database-level audit triggers for data integrity

### Middleware Stack
- **Request Logging**: Comprehensive request/response tracking
- **PHI Detection**: Automatic PHI access identification
- **Security**: Sensitive data redaction and secure logging

## Configuration

The audit system is configured through Django settings and middleware:

**Middleware Configuration:**
```python
MIDDLEWARE = [
    # ... other middleware
    'firefly.core.audit.middleware.RequestLoggingMiddleware',
    'firefly.core.audit.middleware.PHIAuditLogMiddleware',
]
```

**Sensitive Data Redaction:**
- Configurable field names for automatic redaction
- Password fields automatically redacted in request logs
- Extensible pattern matching for additional sensitive fields

## Compliance Features

### HIPAA Compliance
- Complete PHI access logging with user attribution
- Immutable audit records for regulatory review
- Timestamp tracking for access pattern analysis
- Integration with permission system for access control

### Administrative Oversight
- Django admin action tracking for security monitoring
- User session and authentication method logging
- Complete request context for security investigations
- Read-only audit interfaces to preserve data integrity

### Data Retention
- Audit logs preserved through soft delete system
- Historical change tracking via BaseModelV3
- Long-term retention for regulatory requirements
- Efficient querying through indexed fields

## Security Considerations

- **Immutable Records**: Audit logs cannot be modified after creation
- **User Attribution**: All actions tracked to specific users
- **Sensitive Data Protection**: Automatic redaction of passwords and sensitive fields
- **Access Control**: Read-only admin interfaces for audit review
- **Data Integrity**: Database-level constraints and triggers for audit trail preservation
