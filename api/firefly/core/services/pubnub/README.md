# PubNub Integration

PubNub serves as our real-time messaging infrastructure, providing pub/sub capabilities for chat functionality, provider notifications, and real-time communication between members and firefly teams.

## Overview

This service integrates with PubNub to power real-time messaging across our platform. It handles member-provider chat, provider inbox notifications, message persistence, and real-time updates for clinical workflows.

## Business Logic

The PubNub messaging workflow works as follows:

1. **Message Publication**: Messages are published to member-specific channels
2. **Real-time Delivery**: Member apps receive messages immediately via subscription
3. **Provider Routing**: Messages are forwarded to provider inbox channels
4. **Message Persistence**: PubNub functions forward messages to Django API for storage
5. **Metadata Enrichment**: Messages are enhanced with member data and urgency scoring

## Core Components

### Channel Architecture
PubNub uses a structured channel system for message routing:

#### Member Channels
- **Format**: Based on ChatThread UID (one per member)
- **Subscribers**: Member mobile apps, firefly staff
- **Purpose**: Direct member-provider communication
- **Scope**: All messages for a specific member thread

#### Provider Inbox Channels
- **Format**: One shared channel per Tenant
- **Subscribers**: <PERSON> frontend sessions (provider interface)
- **Purpose**: Real-time notifications about new messages
- **Scope**: All member messages for provider awareness

#### API Forward Channels
- **Purpose**: Message persistence to Django backend
- **Function**: PubNub functions forward messages for database storage
- **Integration**: Connects real-time messaging with persistent storage

### PubNub Functions
Server-side functions that process messages automatically:
- **Message Hydration**: Enriches messages with member metadata
- **Provider Forwarding**: Routes messages to provider inbox channels
- **API Integration**: Forwards messages to Django API for persistence
- **Urgency Scoring**: Adds ML-derived urgency analysis

## Message Flow Architecture

The path of a message through our system follows this flow:

### 1. Initial Publication
- **Target**: Member-specific PubNub channel (based on ChatThread UID)
- **Scope**: Each member has a single channel corresponding to their thread
- **Publishers**: Both members and staff can publish to this channel

### 2. Direct Client Subscription
- **Subscribers**: Member mobile apps subscribe directly to their channel
- **Delivery**: Messages received immediately by subscribed clients
- **Real-time**: Sub-100ms delivery for optimal user experience

### 3. PubNub Functions Processing
PubNub functions intercept and process all messages automatically:

#### Member Thread Channel Processing
For messages published to member channels:
- **Message Hydration**: Enriches with member metadata and ML urgency scoring
- **Provider Forwarding**: Re-publishes to shared `provider-inbox` channel
- **API Forwarding**: Sends to `api-forward` channel for persistence
- **Data Enhancement**: Adds context needed by provider clients

#### API Forward Channel Processing
For messages on the `api-forward` channel:
- **Django Integration**: HTTP requests to Django chat API
- **Database Persistence**: Creates ChatMessageV2 records
- **Historical Storage**: Maintains complete message history

### 4. Provider Notification
- **Provider Inbox**: Shared channel for all providers per tenant
- **Lucian Integration**: Frontend subscribes for real-time notifications
- **Message Context**: Includes member metadata for provider awareness

*Note: Backend supports multiple threads per patient, but currently only one thread is used per member.*

## Message Persistence

### Database Integration
PubNub provides real-time messaging, while Django backend handles message persistence:
- **Model**: ChatMessageV2 stores persistent message data
- **Association**: Messages linked to member ChatThread
- **Bulk Loading**: Historical messages loaded by thread for message history
- **Data Integrity**: Ensures messages are preserved beyond real-time delivery

### Persistence Workflow
```
PubNub Message → PubNub Function → Django API → ChatMessageV2 Model
```

#### Message Forwarding Function
- **Function Name**: "message-forwarding"
- **Trigger**: Called for every published message (hook-based)
- **Action**: HTTP request to Django chat API
- **Purpose**: Ensures all messages are persisted to database

#### Local Development
For local testing where PubNub can't reach local servers:
```bash
# Use management command to listen for messages locally
python manage.py pubnub_listener
```
- **Purpose**: Explicitly subscribes to message events from local server
- **Function**: Creates ChatMessageV2 objects when messages received
- **Alternative**: Set up tunnel for PubNub to reach local environment

### Architecture Considerations

#### Current Design Rationale
The indirect persistence approach (PubNub → Function → API) was chosen over direct database writes because:

**PubNub Recommendation**: Direct client-to-database writes discouraged due to:
- Multi-second client latencies
- Server as single point of failure
- Degraded sub-100ms performance for nearby clients

#### Trade-offs
**Current Architecture**:
- ✅ Maintains optimal real-time performance
- ✅ Follows PubNub best practices
- ❌ PubNub becomes single point of failure
- ❌ Additional complexity in message flow

**Alternative (Direct Database)**:
- ✅ Simplified architecture
- ✅ Reduced dependency on PubNub
- ❌ Potential latency impact
- ❌ Risk of message loss if server unavailable

## Message Metadata Enhancement

### Provider UI Requirements
Lucian UI requires member context for messages displayed outside member-specific views:
- **Member Information**: Name, address, program enrollment
- **Clinical Context**: Care team, program status, urgency indicators
- **Navigation Data**: Links back to member records

### Metadata API Integration
```python
# UserMessageMetadataView endpoint
# Exposed to PubNub for message enrichment
GET /api/user/{user_id}/message-metadata/
```

**Workflow**:
1. **PubNub Function**: Requests metadata from Django API
2. **Data Enrichment**: Adds member context to message payload
3. **Provider Delivery**: Enhanced messages sent to provider inbox channel
4. **UI Display**: Lucian displays messages with full member context

## Activity State Communication

### PubNub Signals
Real-time activity indicators using PubNub signals:
- **Provider Presence**: Show which providers are viewing a member's messages
- **Typing Indicators**: Real-time typing status for providers
- **Internal Use**: Currently provider-to-provider only (not member-facing)

### Signal Types
- **Viewing State**: Provider currently viewing member messages
- **Typing State**: Provider actively composing message
- **Presence Updates**: Provider online/offline status

## Management and Operations

### PubNub Admin Portal
- **URL**: [https://admin.pubnub.com/#/login](https://admin.pubnub.com/#/login)
- **Authentication**: Email/password (not SSO)
- **Organization**: Switch to `<EMAIL>` organization view
- **Features**: Usage stats, debugging tools, bug ticket filing

### Message Recovery
The `pull_messages` management command restores chat threads from PubNub history:

#### Usage
```bash
# Restore specific patient messages
python manage.py pull_messages --patient-ids 1427,1721,1404

# Dry run to preview restoration
python manage.py pull_messages --dryrun
```

#### Output Example
```
Will restore 0 messages in thread 1427.default_v1 for patient 1427
Will restore 5 messages in thread 1721.default_v1 for patient 1721
Will restore 22 messages in thread 1404.default_v1 for patient 1404
Will restore 6 messages in thread 1723.default_v1 for patient 1723
```

#### Important Warnings
- **Destructive Operation**: Overwrites existing member thread data
- **No Diff Support**: Does not merge - replaces entire thread
- **Data Loss Risk**: Associated tasks and metadata will be lost
- **Scope Control**: Avoid running for all members - specify user IDs

### Message History Configuration
- **Retention**: Production configured to retain all messages indefinitely
- **Documentation**: [PubNub Chat Message History](https://www.pubnub.com/docs/chat/features/message-history)
- **Access**: Historical messages available via PubNub API

## Technical Limitations and Improvements

### Current Function Limitations

#### 1. API Call Efficiency
- **Issue**: Multiple API calls for message hydration increase latency
- **Impact**: Slower message processing and higher complexity

#### 2. Deployment Challenges
- **Issue**: PubNub Functions CLI tools poorly maintained
- **Impact**: Complex deployment process, authentication timeouts

#### 3. Debugging Constraints
- **Issue**: Limited debugging tools for PubNub Functions
- **Impact**: console.log only viewable in admin portal, no log persistence

## Integration Points

### Django API
- **Message Persistence**: ChatMessageV2 model storage
- **Metadata Enrichment**: UserMessageMetadataView endpoint
- **Local Development**: pubnub_listener management command

### Frontend Applications
- **Patient App**: Direct channel subscription for real-time messages
- **Lucian Provider UI**: Provider inbox channel for notifications
- **Real-time Updates**: Sub-100ms message delivery

### External Services
- **ML Urgency Scoring**: Integration with SageMaker for message analysis
- **Care Team Data**: Member care team information for provider context
- **Program Information**: Member program enrollment and status data