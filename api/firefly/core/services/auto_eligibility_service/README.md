# Auto Eligibility Service

The Auto Eligibility Service provides data persistence and API endpoints for insurance eligibility verification logs. It stores request and response data from Trizetto clearinghouse interactions and provides REST API access to eligibility check information.

## Business Context

This service acts as the data layer for insurance eligibility verification, working alongside the `auto_eligibility` module which contains the business logic and Trizetto integration. When members schedule appointments or update insurance information, the system verifies their coverage status and stores detailed logs of these interactions for auditing, troubleshooting, and member support.

## Core Models

### AutoEligibilityRequestLog
Stores eligibility verification request data sent to Trizetto:

**Patient Information:**
- `patient_id/person_id` - Member being verified
- `patient_insurance_number` - Member's insurance ID
- `patient_first_name/patient_last_name` - Member demographics
- `patient_dob/patient_gender` - Additional demographics
- `patient_address_state` - Member's state of residence
- `patient_is_dependent` - Whether member is a dependent

**Primary Subscriber (for dependents):**
- `primary_first_name/primary_last_name` - Primary subscriber name
- `primary_dob` - Primary subscriber date of birth

**Provider Information:**
- `provider` - Foreign key to Physician model
- `provider_npi` - National Provider Identifier
- `provider_last_name` - Provider name
- `provider_organization_npi` - Organization NPI

**Service Details:**
- `service_start/service_end` - Service date range
- `service_type` - Service type code (default: "30" for primary care)

**Request Metadata:**
- `ticket` - Unique request identifier (UUID)
- `trigger_type` - What triggered the check ("auto" or "lucian")
- `payer_code` - Insurance payer identifier
- `endpoint` - Trizetto API endpoint used
- `request_json` - Structured request parameters
- `raw_request` - Raw XML SOAP request
- `requested_by_user_id` - User who initiated the check

### AutoEligibilityResponseLog
Stores eligibility verification responses from Trizetto:

**Response Metadata:**
- `ticket` - Links to corresponding request
- `retry` - Retry attempt number
- `payer_code/payer_name` - Insurance payer information
- `html_response_code` - HTTP response code
- `response_json` - Parsed response data
- `raw_response` - Raw XML response

**Eligibility Results:**
- `is_eligible` - Whether member has active coverage
- `firefly_is_pcp` - Whether Firefly is designated as PCP
- `pcp_npi/pcp_name` - Primary care provider information
- `rejections` - Array of rejection reasons if verification failed
- `has_error_in_parsing` - Whether response parsing encountered errors

**Coverage Details:**
- `insurance_type` - Type of insurance coverage
- `plan_coverage_description` - Insurance plan description
- `coverage_start/coverage_end` - Coverage date range

**Member References:**
- `patient_id/person_id` - Member being verified

## API Endpoints

### GET /auto_eligibility/time/{patient_id}
Returns information about the latest eligibility check for a patient:

**Response Format:**
```json
{
  "latest_response_time": "2024-01-15T10:30:00.000Z",
  "is_eligible": true,
  "rejections": [],
  "requested_by_user": {
    "id": 123,
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

**Error Responses:**
- `400 Bad Request` - Missing patient_id
- `404 Not Found` - No eligibility checks found for patient

## Business Logic & Side Effects

### AutoEligibilityResponseLog Save Handler
When a response log is saved, the `pre_save_mutation` method automatically:

1. **Parses Response Data**: Extracts structured eligibility information from raw XML
2. **Updates Insurance Information**: Syncs coverage dates and plan details to member's insurance record
3. **Creates Insurance Cases**: Generates cases for Trizetto errors or coverage issues
4. **Handles Plan Matching**: Links responses to existing insurance plans in the system

### Insurance Data Updates
Valid responses automatically update the member's `InsuranceMemberInfo` with:
- Coverage start/end dates
- Plan coverage description
- Insurance plan associations
- PCP attribution status

### Error Handling
The system creates insurance cases when:
- Trizetto returns known error codes indicating service outages
- Response parsing fails due to malformed data
- Coverage verification reveals issues requiring manual review

## Admin Interface

Both models provide read-only admin interfaces for operational support:

**AutoEligibilityRequestLog Admin:**
- Displays request metadata, patient info, and provider details
- Searchable by ticket and payer code
- Filterable by provider NPI
- No add/edit/delete permissions (audit trail preservation)

**AutoEligibilityResponseLog Admin:**
- Shows response status, eligibility results, and PCP information
- Searchable by ticket
- Filterable by payer, eligibility status, and PCP status
- Read-only with disabled bulk delete actions

## Integration Points

### Auto Eligibility Module
- **Business Logic**: The `auto_eligibility` module contains the core verification workflow
- **Trizetto Client**: Handles SOAP API communication with clearinghouse
- **Response Parsing**: Processes XML responses into structured data
- **Workflow Triggers**: Initiates eligibility checks based on business rules

### Insurance Module
- **Member Info Updates**: Syncs eligibility data to insurance records
- **Case Creation**: Generates insurance-related cases for manual review
- **Plan Management**: Links responses to insurance plan catalog

### User Module
- **Person Records**: Associates eligibility data with member profiles
- **Audit Trails**: Tracks who initiated eligibility checks

## Configuration

The service relies on configuration from the auto_eligibility module:
- Trizetto API credentials and endpoints
- Rate limiting settings for API requests
- Feature flags for enabling/disabling verification
- Error code mappings for case creation

## Testing

Test factories are provided for both models:
- `AutoEligibilityRequestLogFactory` - Creates test request logs
- `AutoEligibilityResponseLogFactory` - Creates test response logs

Tests cover:
- API endpoint functionality
- Model save handler behavior
- Admin interface permissions
- Integration with eligibility workflows
