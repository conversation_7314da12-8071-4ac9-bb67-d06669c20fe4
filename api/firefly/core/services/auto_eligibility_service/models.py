import datetime
import logging
from typing import Optional

# DO NOT COPY-PASTE: Prefer model relationship over Array<PERSON>ield
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.db import models
from django.db.models import J<PERSON>NField

from firefly.modules.auto_eligibility.constants import TRIZETTO_ERRORS
from firefly.modules.auto_eligibility.parser import parse_payer_response
from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.firefly_django.save_handlers import SaveHandlersMixin
from firefly.modules.insurance.models import InsurancePayer, InsurancePlan
from firefly.modules.physician.models import Physician

logger = logging.getLogger(__name__)


class AutoEligibilityRequestLog(BaseModelV3):
    """
    Request data for external eligibility checks e.g. Trizetto
    This stores responses for both auto and manual checks
    """

    TRIGGER_LUCIAN = "lucian"
    TRIGGER_AUTO = "auto"

    TRIGGER_CHOICES = ((TRIGGER_LUCIAN, TRIGGER_LUCIAN), (TRIGGER_AUTO, TRIGGER_AUTO))

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    endpoint = models.CharField(max_length=127)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ticket = models.CharField(max_length=127, blank=False, db_index=True)  # noqa: TID251
    retry = models.IntegerField(null=True, blank=True, default=1)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    trigger_type = models.CharField(  # noqa: TID251
        max_length=15,
        db_index=True,
        default=TRIGGER_LUCIAN,
        choices=TRIGGER_CHOICES,
    )

    patient_id = models.IntegerField(db_index=True, null=True, blank=True)
    person_id = models.IntegerField(db_index=True, null=True, blank=True)
    patient_is_dependent = models.BooleanField(default=False)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_insurance_number = models.CharField(max_length=32, blank=False, db_index=True, default="notvalid")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_insurance_name = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_first_name = models.CharField(max_length=127, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_last_name = models.CharField(max_length=127, blank=False, db_index=True, default="notvalid")  # noqa: TID251
    patient_dob = models.DateField(null=True, blank=False)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_gender = models.CharField(max_length=1, null=False, default="F")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    patient_address_state = models.CharField(max_length=2, blank=False, default="MA", db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    primary_first_name = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    primary_last_name = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    primary_dob = models.DateField(blank=True, null=True)

    service_start = models.DateField(default=datetime.date.today)
    service_end = models.DateField(default=datetime.date.today)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    service_type = models.CharField(max_length=4, default="30")  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    payer = models.ForeignKey(InsurancePayer, db_index=True, on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_code = models.CharField(max_length=255, blank=False, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_name = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    provider = models.ForeignKey(Physician, db_index=True, on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_last_name = models.CharField(max_length=127, blank=False, default="notvalid")  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_npi = models.CharField(max_length=11, blank=False, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_organization_npi = models.CharField(max_length=11, blank=False, db_index=True, default="**********")  # noqa: TID251

    request_json = JSONField()
    requested_by_user_id = models.IntegerField(db_index=True, null=True, blank=True)
    raw_request = models.TextField(blank=True)

    class Meta:
        db_table = "auto_eligibility_request_logs"


class AutoEligibilityResponseLog(SaveHandlersMixin, BaseModelV3):
    """
    Store the responses from external eligibility checks
    This stores responses for both auto and manual checks;
    use AutoEligibilityRequestLog.trigger_type to tell the difference
    """

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    ticket = models.CharField(max_length=127, db_index=True)  # noqa: TID251
    retry = models.IntegerField(null=True, blank=True, default=1)

    # These fields can be empty if this was an unsuccessful request
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_code = models.CharField(max_length=255, blank=True, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    payer_name = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    insurance_type = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    plan_coverage_description = models.CharField(max_length=127, blank=True, null=True)  # noqa: TID251
    coverage_start = models.DateField(blank=True, null=True)
    coverage_end = models.DateField(blank=True, null=True)
    patient_id = models.IntegerField(null=True, blank=True)
    person_id = models.IntegerField(null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    rejections = ArrayField(null=True, blank=True, size=255, base_field=models.CharField(max_length=255))  # noqa: TID251

    #
    is_eligible = models.BooleanField(default=False, blank=True, null=True)
    firefly_is_pcp = models.BooleanField(default=False, blank=True, null=True)
    # Store the npi of the PCP from the eligibilty response logs
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pcp_npi = models.CharField(max_length=11, blank=True, null=True)  # noqa: TID251
    pcp_name = models.TextField(blank=True, null=True)
    has_error_in_parsing = models.BooleanField(blank=True, null=True)

    response_json = JSONField()
    html_response_code = models.IntegerField(null=True, blank=True)
    raw_response = models.TextField(blank=True)

    def pre_save_mutation(self, changed):
        from firefly.core.user.models import Person
        from firefly.modules.auto_eligibility.interfaces import ParsedAutoEligibilityResponse
        from firefly.modules.auto_eligibility.utils import _construct_patient_param

        if changed("response_json"):
            person = Person.objects.get(pk=self.person_id)
            patient_params = _construct_patient_param(person)
            parsed_response: Optional[ParsedAutoEligibilityResponse] = parse_payer_response(
                self.payer_code, patient_params, self.response_json
            )
            if parsed_response:
                self.coverage_start = parsed_response["coverage_start"]
                self.coverage_end = parsed_response["coverage_end"]
                self.is_eligible = parsed_response["eligible"]
                self.firefly_is_pcp = parsed_response["firefly_is_pcp"]
                self.rejections = parsed_response["rejections"][self.payer_code]
                self.insurance_type = parsed_response["insurance_type"]
                self.plan_coverage_description = parsed_response["plan_coverage_description"]
                self.pcp_npi = parsed_response["pcp_npi"]
                self.pcp_name = parsed_response["pcp_name"]
                self.has_error_in_parsing = False
            else:
                self.has_error_in_parsing = True

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.core.user.models import Person
        from firefly.modules.auto_eligibility.constants import PLAN_TYPE_ABBR
        from firefly.modules.statemachines.coverage.callbacks import create_new_insurance_case

        person = Person.objects.get(pk=self.person_id)
        is_response_log_valid = True
        if changed("rejections") and self.rejections:
            errors = [r for r in self.rejections if r in TRIZETTO_ERRORS]
            if errors:
                is_response_log_valid = False
                create_new_insurance_case(person)
        if is_response_log_valid and not self.has_error_in_parsing:
            # Only update insurance data if there isnt an indication of a trizetto outage
            insurance_info = person.insurance_info
            if insurance_info:
                update_fields_on_insurance_info = []
                fields_relevant_to_insurance_info = [
                    {"field_on_response_log": "coverage_start", "field_on_insurance_info": "coverage_start"},
                    {"field_on_response_log": "coverage_end", "field_on_insurance_info": "coverage_end"},
                    {
                        "field_on_response_log": "plan_coverage_description",
                        "field_on_insurance_info": "plan_description",
                    },
                    {"field_on_response_log": "pcp_name", "field_on_insurance_info": "pcp_name"},
                    {"field_on_response_log": "firefly_is_pcp", "field_on_insurance_info": "is_firefly_pcp"},
                    {"field_on_response_log": "pcp_npi", "field_on_insurance_info": "pcp_npi"},
                ]
                for field_relevant_to_insurance_info in fields_relevant_to_insurance_info:
                    if changed(field_relevant_to_insurance_info["field_on_response_log"]):
                        setattr(
                            insurance_info,
                            field_relevant_to_insurance_info["field_on_insurance_info"],
                            getattr(self, field_relevant_to_insurance_info["field_on_response_log"]),
                        )
                        update_fields_on_insurance_info.append(
                            field_relevant_to_insurance_info["field_on_insurance_info"]
                        )
                if changed("insurance_type") and self.insurance_type in PLAN_TYPE_ABBR:
                    insurance_info.plan_type = PLAN_TYPE_ABBR[self.insurance_type]
                    update_fields_on_insurance_info.append("plan_type")
                if changed("firefly_is_pcp"):
                    insurance_info.firefly_pcp_at = datetime.datetime.now() if self.firefly_is_pcp else None
                    update_fields_on_insurance_info.append("firefly_pcp_at")
                # Link insurance plan based on plan_coverage_description
                # DO NOT UNLINK when the plan_coverage_description is None
                # Without a change in the member id - if the plan coverage expired
                # Trizetto will not tell us about a plan name. In this scenario it would
                # be incorrect to link the older plan.
                # TODO: Clear out plan coverage description and unlink insurance plan when member id changes
                if (
                    changed("plan_coverage_description")
                    and self.plan_coverage_description
                    and insurance_info.insurance_payer
                ):
                    # Try to find an existing plan with the same name (case-insensitive)
                    insurance_plan_queryset = InsurancePlan.objects.filter(
                        name__iexact=self.plan_coverage_description, insurance_payer=insurance_info.insurance_payer
                    )

                    if not insurance_plan_queryset.exists():
                        logger.info(
                            "Person: %d. No plan exists with name %s linked to payer %s. Creating new plan",
                            person.id,
                            self.plan_coverage_description,
                            insurance_info.insurance_payer.name,
                        )
                        # Create a new plan if one doesn't exist
                        insurance_plan = InsurancePlan.objects.create(
                            name=self.plan_coverage_description, insurance_payer=insurance_info.insurance_payer
                        )
                    else:
                        # Use the existing plan
                        insurance_plan = insurance_plan_queryset.order_by("created_at").first()

                    logger.info(
                        "Person: %d. Linking with plan %d",
                        person.id,
                        insurance_plan.id,
                    )
                    insurance_info.insurance_plan = insurance_plan
                    update_fields_on_insurance_info.append("insurance_plan")
                if update_fields_on_insurance_info:
                    insurance_info.save(update_fields=update_fields_on_insurance_info)

    class Meta:
        db_table = "auto_eligibility_response_logs"
