import logging
from collections import defaultdict
from datetime import datetime

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


class JumpCloudClient(object):
    def __init__(self):
        self.base_url = "https://console.jumpcloud.com/api"

    def get(self, endpoint, params=None):
        url = f"{self.base_url}/{endpoint}"
        headers = self._get_headers()
        r = requests.get(url=url, params=params, headers=headers)
        r.raise_for_status()
        return r.json()

    def post(self, endpoint, data=None):
        url = f"{self.base_url}/{endpoint}"
        headers = self._get_headers()
        r = requests.post(url=url, json=data, headers=headers)
        r.raise_for_status()
        return r.json()

    def get_user_groups(self):
        groups = {group["id"] for group in self._get_data("v2/usergroups")}
        return groups

    def get_users(self, group_id):
        users = {user["to"]["id"] for user in self._get_data(f"v2/usergroups/{group_id}/members")}
        return users

    def get_all_users(self):
        groups = self.get_user_groups()
        users = {u for g in groups for u in self.get_users(g)}
        return users

    def get_gsuite_users(self, limit=100):
        url = f"v2/gsuites/{settings.JUMPCLOUD['GSUITE_ID']}/import/users"
        params = {
            "limit": limit,
        }
        data = self.get(url, params=params)
        users = data["users"]
        while data.get("nextPageToken"):
            params["pageToken"] = data.get("nextPageToken")
            data = self.get(url, params=params)
            users.extend(data["users"])
        return users

    def get_mac_applications(self, filters=None):
        params = {"filter": filters} if filters else None
        apps = self._get_data("v2/systeminsights/apps", params=params)
        return apps

    def get_windows_applications(self, filters=None):
        params = {"filter": filters} if filters else None
        apps = self._get_data("v2/systeminsights/programs", params=params)
        return apps

    def audit_mac_applications(self):
        # exclude_list = []
        data = self.get_mac_applications()
        apps = defaultdict(
            lambda: {
                "last_opened_time": 0.0,
                "count": 0,
                "display_name": "",
                "bundle_name": "",
                "bundle_identifier": "",
                "name": "",
            }
        )
        for d in data:
            name = d["name"]
            apps[name]["last_opened_time"] = max(apps[name]["last_opened_time"], d["last_opened_time"])
            apps[name]["count"] += 1
            apps[name]["display_name"] = d["display_name"]
            apps[name]["bundle_name"] = d["bundle_name"]
            apps[name]["bundle_identifier"] = d["bundle_identifier"]
            apps[name]["name"] = d["name"]
        print("name,last_opened_time,count,display_name,bundle_name,bundle_identifier")
        for a in apps.values():
            print(
                f"{a['name']},{datetime.fromtimestamp(a['last_opened_time'])},{a['count']},{a['display_name']},{a['bundle_name']},{a['bundle_identifier']}"
            )  # noqa
        return apps

    def audit_windows_applications(self):
        # exclude_list = []
        data = self.get_windows_applications()
        apps = defaultdict(lambda: {"name": "", "install_date": "", "publisher": "", "count": 0})
        for d in data:
            name = d["name"]
            apps[name]["name"] = d["name"]
            apps[name]["install_date"] = max(apps[name]["install_date"], d["install_date"])
            apps[name]["publisher"] = max(apps[name]["publisher"], d["publisher"])
            apps[name]["count"] += 1
        print("name,install_date,count,publisher")
        for a in apps.values():
            print(f"{a['name']},{self._format_date(a['install_date'])},{a['count']},{a['publisher']}")  # noqa
        return apps

    def get_users_with_application_installed(self, application_name):
        apps = self.get_mac_applications(filters=[f"bundle_name:eq:{application_name}"])
        apps += self.get_windows_applications(filters=[f"name:eq:{application_name}"])
        for app in apps:
            users = self.get(f"v2/systems/{app['system_id']}/users")
            for user in users:
                user_data = self.get(f"systemusers/{user['id']}")
                print(user_data["email"])

    def _get_data(self, endpoint, params=None, attr=None, offset=100):
        params = params or {}
        params.update(
            {
                "limit": offset,
                "skip": 0,
            }
        )
        results = self.get(endpoint, params=params)
        data = results[attr] if attr else results
        while results[attr] if attr else results:
            params["skip"] += offset
            results = self.get(endpoint, params=params)
            data += results[attr] if attr else results
        return data

    @staticmethod
    def _format_date(dt):
        if not dt:
            return None
        try:
            return datetime.strptime(dt, "%Y-%m-%d")
        except ValueError:
            return datetime.strptime(dt, "%Y%m%d")

    @staticmethod
    def _get_headers():
        return {"x-api-key": settings.JUMPCLOUD["API_KEY"]}
