import logging
from unittest import skip
from unittest.mock import patch

import pytest

from firefly.core.services.jira.attributes import (
    AttributeNotDefined,
    <PERSON><PERSON><PERSON><PERSON>,
    JiraIssueLinkType,
    JiraIssueType,
    JiraProject,
    JiraRequestType,
    JiraServiceDesk,
    JiraStatus,
    get_fields,
    get_issue_link_types,
    get_issue_types,
    get_project,
    get_statuses,
)
from firefly.core.services.jira.client import DuplicateUser, JiraClient, UserNotFound
from firefly.core.tests.test_case import FireflyTestCase

logger = logging.getLogger(__name__)

# Test Data for jira user integration, Note these are re-used in downstream tests in users of this
# client
JIRA_TEST_USERS = [
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/2/user?accountId=************************",  # noqa
        "accountId": "************************",  # pragma: allowlist secret
        "accountType": "atlassian",
        "emailAddress": "<EMAIL>",
        "avatarUrls": {
            "48x48": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "24x24": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "16x16": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "32x32": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
        },
        "displayName": "John Doe",
        "active": True,
        "timeZone": "America/New_York",
        "locale": "en_US",
    }
]
JIRA_TEST_USER_DUPLICATE = [
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/2/user?accountId=************************",  # noqa
        "accountId": "************************",  # pragma: allowlist secret
        "accountType": "atlassian",
        "emailAddress": "<EMAIL>",
        "avatarUrls": {
            "48x48": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "24x24": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "16x16": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "32x32": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
        },
        "displayName": "John Doe",
        "active": True,
        "timeZone": "America/New_York",
        "locale": "en_US",
    },
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/2/user?accountId=************************",  # noqa
        "accountId": "************************",  # pragma: allowlist secret
        "accountType": "atlassian",
        "emailAddress": "<EMAIL>",
        "avatarUrls": {
            "48x48": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "24x24": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "16x16": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
            "32x32": "https://secure.gravatar.com/avatar/1a93b8396c2a28c94e83cf3c13896ab5",
        },
        "displayName": "John Doe",
        "active": True,
        "timeZone": "America/New_York",
        "locale": "en_US",
    },
]
JIRA_TEST_PROJECT = {
    "expand": "description,lead,issueTypes,url,projectKeys,permissions,insight",
    "self": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018",
    "id": "10018",
    "key": "AC",
    "description": "",
    "lead": {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/user?accountId=************************",  # noqa
        "accountId": "************************",  # pragma: allowlist secret
        "avatarUrls": {
            "48x48": "https://secure.gravatar.com/avatar/000a514a1ae232cc3af6f2a3e4a8ed13?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FTC-1.png",  # noqa
            "24x24": "https://secure.gravatar.com/avatar/000a514a1ae232cc3af6f2a3e4a8ed13?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FTC-1.png",  # noqa
            "16x16": "https://secure.gravatar.com/avatar/000a514a1ae232cc3af6f2a3e4a8ed13?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FTC-1.png",  # noqa
            "32x32": "https://secure.gravatar.com/avatar/000a514a1ae232cc3af6f2a3e4a8ed13?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FTC-1.png",  # noqa
        },
        "displayName": "Tanner Costello",
        "active": True,
    },
    "components": [
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10082",
            "id": "10082",
            "name": "1Password",
            "description": "1Password",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10044",
            "id": "10044",
            "name": "Atlassian Admin",
            "description": "Atlassian Admin - Overall Administration of Jira, Opsgenie, StatusPage, Service Desk",  # noqa
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10037",
            "id": "10037",
            "name": "Auth0",
            "description": "Auth0",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10034",
            "id": "10034",
            "name": "AWS",
            "description": "AWS",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10079",
            "id": "10079",
            "name": "Box",
            "description": "Box",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/100461",
            "id": "100461",
            "name": "Datadog",
            "description": "Datadog",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10046",
            "id": "10046",
            "name": "DBT",
            "description": "DBT",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10033",
            "id": "10033",
            "name": "Elation",
            "description": "Elation",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10039",
            "id": "10039",
            "name": "github",
            "description": "github",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10045",
            "id": "10045",
            "name": "Google Workspace",
            "description": "Google Workspace",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10042",
            "id": "10042",
            "name": "Jira",
            "description": "JIra",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10081",
            "id": "10081",
            "name": "JumpCloud",
            "description": "JumpCloud",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10036",
            "id": "10036",
            "name": "Looker",
            "description": "Looker",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10041",
            "id": "10041",
            "name": "Lucian",
            "description": "Lucian",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10043",
            "id": "10043",
            "name": "Opsgenie",
            "description": "Opsgenie",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10038",
            "id": "10038",
            "name": "Practice Suite",
            "description": "Practice Suite",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10083",
            "id": "10083",
            "name": "Slack",
            "description": "Slack",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10035",
            "id": "10035",
            "name": "Snowflake",
            "description": "Snowflake",
            "isAssigneeTypeValid": False,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/component/10084",
            "id": "10084",
            "name": "TalkDesk",
            "description": "TalkDesk",
            "isAssigneeTypeValid": False,
        },
    ],
    "issueTypes": [
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10038",
            "id": "10038",
            "description": "Access Request",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10323?size=medium",  # noqa
            "name": "Access Control: Access Request",
            "subtask": False,
            "avatarId": 10323,
            "hierarchyLevel": 0,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10041",
            "id": "10041",
            "description": "Onboarding Access Requests",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10323?size=medium",  # noqa
            "name": "Access Control: Onboarding Access Request",
            "subtask": True,
            "avatarId": 10323,
            "hierarchyLevel": -1,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10039",
            "id": "10039",
            "description": "Create Staff",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10311?size=medium",  # noqa
            "name": "Access Control: Create Staff",
            "subtask": False,
            "avatarId": 10311,
            "hierarchyLevel": 0,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10040",
            "id": "10040",
            "description": "Onboard Staff",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10310?size=medium",  # noqa
            "name": "Access Control: Onboarding",
            "subtask": False,
            "avatarId": 10310,
            "hierarchyLevel": 0,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10043",
            "id": "10043",
            "description": "Offboarding: Access Removal",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10312?size=medium",  # noqa
            "name": "Access Control: Offboarding Remove Access",
            "subtask": True,
            "avatarId": 10312,
            "hierarchyLevel": -1,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10042",
            "id": "10042",
            "description": "Offboard Staff",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10303?size=medium",  # noqa
            "name": "Access Control: Offboarding",
            "subtask": False,
            "avatarId": 10303,
            "hierarchyLevel": 0,
        },
        {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10044",
            "id": "10044",
            "description": "Remove Access",
            "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10312?size=medium",  # noqa
            "name": "Access Control: Remove Access",
            "subtask": False,
            "avatarId": 10312,
            "hierarchyLevel": 0,
        },
    ],
    "assigneeType": "UNASSIGNED",
    "versions": [],
    "name": "Access Control",
    "roles": {
        "atlassian-addons-project-access": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018/role/10003",  # noqa
        "Service Desk Team": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018/role/10126",  # noqa
        "Developers": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018/role/10100",
        "Service Desk Customers": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018/role/10125",  # noqa
        "Administrators": "https://fireflyhealth.atlassian.net/rest/api/3/project/10018/role/10002",
    },
    "avatarUrls": {
        "48x48": "https://fireflyhealth.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/10424",  # noqa
        "24x24": "https://fireflyhealth.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/10424?size=small",  # noqa
        "16x16": "https://fireflyhealth.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/10424?size=xsmall",  # noqa
        "32x32": "https://fireflyhealth.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/10424?size=medium",  # noqa
    },
    "projectTypeKey": "service_desk",
    "simplified": False,
    "style": "classic",
    "isPrivate": False,
    "properties": {},
}
JIRA_TEST_ISSUE_TYPES = [
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10038",
        "id": "10038",
        "description": "Access Request",
        "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10323?size=medium",  # noqa
        "name": "Access Control: Access Request",
        "untranslatedName": "Access Control: Access Request",
        "subtask": False,
        "avatarId": 10323,
        "hierarchyLevel": 0,
    },
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10040",
        "id": "10040",
        "description": "Remove Access",
        "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10323?size=medium",  # noqa
        "name": "Access Control: Remove Access",
        "untranslatedName": "Access Control: Remove Access",
        "subtask": False,
        "avatarId": 10323,
        "hierarchyLevel": 0,
    },
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/issuetype/10025",
        "id": "10025",
        "description": "A small, distinct piece of work.",
        "iconUrl": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10318?size=medium",  # noqa
        "name": "Task",
        "untranslatedName": "Task",
        "subtask": False,
        "avatarId": 10318,
        "hierarchyLevel": 0,
        "scope": {"type": "PROJECT", "project": {"id": "10014"}},
    },
]
# Test Data for jira field integration, Note these are re-used in downstream tests in users of this
# client
JIRA_TEST_FIELDS = [
    {
        "id": "customfield_10074",
        "key": "customfield_10074",
        "name": "Access Control: AWS Permission",
        "untranslatedName": "Access Control: AWS Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: AWS Permission",
            "Access Control: AWS Permission[Radio Buttons]",
            "cf[10074]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10074,
        },
    },
    {
        "id": "customfield_100791",
        "key": "customfield_100791",
        "name": "Access Control: Datadog Permission",
        "untranslatedName": "Access Control: Datadog Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Datadog Permission",
            "Access Control: Datadog Permission[Checkboxes]",
            "cf[100791]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 100791,
        },
    },
    {
        "id": "customfield_10079",
        "key": "customfield_10079",
        "name": "Access Control: DBT Permission",
        "untranslatedName": "Access Control: DBT Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: DBT Permission",
            "Access Control: DBT Permission[Checkboxes]",
            "cf[10079]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10079,
        },
    },
    {
        "id": "customfield_10104",
        "key": "customfield_10104",
        "name": "Access Control: JumpCloud Permission",
        "untranslatedName": "Access Control: JumpCloud Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: JumpCloud Permission",
            "Access Control: JumpCloud Permission[Radio Buttons]",
            "cf[10104]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10104,
        },
    },
    {
        "id": "customfield_10105",
        "key": "customfield_10105",
        "name": "Access Control: 1Password Permission",
        "untranslatedName": "Access Control: 1Password Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: 1Password Permission",
            "Access Control: 1Password Permission[Radio Buttons]",
            "cf[10105]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10105,
        },
    },
    {
        "id": "customfield_10106",
        "key": "customfield_10106",
        "name": "Access Control: Slack Permission",
        "untranslatedName": "Access Control: Slack Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Slack Permission",
            "Access Control: Slack Permission[Radio Buttons]",
            "cf[10106]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10106,
        },
    },
    {
        "id": "customfield_10107",
        "key": "customfield_10107",
        "name": "Access Control: TalkDesk Permission",
        "untranslatedName": "Access Control: TalkDesk Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: TalkDesk Permission",
            "Access Control: TalkDesk Permission[Radio Buttons]",
            "cf[10107]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10107,
        },
    },
    {
        "id": "customfield_10078",
        "key": "customfield_10078",
        "name": "Access Control: Looker Roles",
        "untranslatedName": "Access Control: Looker Roles",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Looker Roles",
            "Access Control: Looker Roles[Checkboxes]",
            "cf[10078]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10078,
        },
    },
    {
        "id": "customfield_10077",
        "key": "customfield_10077",
        "name": "Access Control: PracticeSuite",
        "untranslatedName": "Access Control: PracticeSuite",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: PracticeSuite",
            "Access Control: PracticeSuite[Radio Buttons]",
            "cf[10077]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10077,
        },
    },
    {
        "id": "customfield_10080",
        "key": "customfield_10080",
        "name": "Access Control: Snowflake",
        "untranslatedName": "Access Control: Snowflake",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Snowflake",
            "Access Control: Snowflake[Checkboxes]",
            "cf[10080]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10080,
        },
    },
    {
        "id": "customfield_10081",
        "key": "customfield_10081",
        "name": "Access Control: Lucian",
        "untranslatedName": "Access Control: Lucian",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Lucian",
            "Access Control: Lucian[Checkboxes]",
            "cf[10081]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10081,
        },
    },
    {
        "id": "customfield_10082",
        "key": "customfield_10082",
        "name": "Access Control: Github Permission",
        "untranslatedName": "Access Control: Github Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Github Permission",
            "Access Control: Github Permission[Checkboxes]",
            "cf[10082]",
        ],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10082,
        },
    },
    {
        "id": "customfield_10084",
        "key": "customfield_10084",
        "name": "Access Control: Elation Permission",
        "untranslatedName": "Access Control: Elation Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Elation Permission",
            "Access Control: Elation Permission[Radio Buttons]",
            "cf[10084]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10084,
        },
    },
    {
        "id": "customfield_10085",
        "key": "customfield_10085",
        "name": "Access Control: Auth0",
        "untranslatedName": "Access Control: Auth0",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["Access Control: Auth0", "Access Control: Auth0[Checkboxes]", "cf[10085]"],
        "schema": {
            "type": "array",
            "items": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes",
            "customId": 10085,
        },
    },
    {
        "id": "customfield_10103",
        "key": "customfield_10103",
        "name": "Access Control: Google Workspace Permission",
        "untranslatedName": "Access Control: Google Workspace Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Google Workspace Permission",
            "Access Control: Google Workspace Permission[Radio Buttons]",
            "cf[10103]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10103,
        },
    },
    {
        "id": "customfield_10101",
        "key": "customfield_10101",
        "name": "Access Control: Box Permission",
        "untranslatedName": "Access Control: Box Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Box Permission",
            "Access Control: Box Permission[Radio Buttons]",
            "cf[10101]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10101,
        },
    },
    {
        "id": "customfield_10076",
        "key": "customfield_10076",
        "name": "Access Control: Jira Permission",
        "untranslatedName": "Access Control: Jira Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Jira Permission",
            "Access Control: Jira Permission[Radio Buttons]",
            "cf[10076]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10076,
        },
    },
    {
        "id": "customfield_10099",
        "key": "customfield_10099",
        "name": "Access Control: Atlassian Permission",
        "untranslatedName": "Access Control: Atlassian Permission",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Atlassian Permission",
            "Access Control: Atlassian Permission[Radio Buttons]",
            "cf[10099]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10099,
        },
    },
    {
        "id": "parent",
        "key": "parent",
        "name": "Parent",
        "custom": False,
        "orderable": False,
        "navigable": True,
        "searchable": False,
        "clauseNames": ["parent"],
    },
    {
        "id": "resolution",
        "key": "resolution",
        "name": "Resolution",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["resolution"],
        "schema": {"type": "resolution", "system": "resolution"},
    },
    {
        "id": "priority",
        "key": "priority",
        "name": "Priority",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["priority"],
        "schema": {"type": "priority", "system": "priority"},
    },
    {
        "id": "labels",
        "key": "labels",
        "name": "Labels",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["labels"],
        "schema": {"type": "array", "items": "string", "system": "labels"},
    },
    {
        "id": "assignee",
        "key": "assignee",
        "name": "Assignee",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["assignee"],
        "schema": {"type": "user", "system": "assignee"},
    },
    {
        "id": "status",
        "key": "status",
        "name": "Status",
        "custom": False,
        "orderable": False,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["status"],
        "schema": {"type": "status", "system": "status"},
    },
    {
        "id": "components",
        "key": "components",
        "name": "Components",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["component"],
        "schema": {"type": "array", "items": "component", "system": "components"},
    },
    {
        "id": "issuekey",
        "key": "issuekey",
        "name": "Key",
        "custom": False,
        "orderable": False,
        "navigable": True,
        "searchable": False,
        "clauseNames": ["id", "issue", "issuekey", "key"],
    },
    {
        "id": "customfield_10054",
        "key": "customfield_10054",
        "name": "Severity",
        "untranslatedName": "Severity",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["cf[10054]", "Severity", "Severity[Dropdown]"],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
            "customId": 10054,
        },
    },
    {
        "id": "customfield_10049",
        "key": "customfield_10049",
        "name": "Impact",
        "untranslatedName": "Impact",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["cf[10049]", "Impact", "Impact[Dropdown]"],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:select",
            "customId": 10049,
        },
    },
    {
        "id": "reporter",
        "key": "reporter",
        "name": "Reporter",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["reporter"],
        "schema": {"type": "user", "system": "reporter"},
    },
    {
        "id": "customfield_10090",
        "key": "customfield_10090",
        "name": "Access Control: Hiring Manager",
        "untranslatedName": "Access Control: Hiring Manager",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Hiring Manager",
            "Access Control: Hiring Manager[User Picker (single user)]",
            "cf[10090]",
        ],
        "schema": {
            "type": "user",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:userpicker",
            "customId": 10090,
        },
    },
    {
        "id": "customfield_10091",
        "key": "customfield_10091",
        "name": "Access Control: Employee or Contractor",
        "untranslatedName": "Access Control: Employee or Contractor",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Employee or Contractor",
            "Access Control: Employee or Contractor[Radio Buttons]",
            "cf[10091]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10091,
        },
    },
    {
        "id": "customfield_10093",
        "key": "customfield_10093",
        "name": "Access Control: Laptop Shipping/Tracking ID",
        "untranslatedName": "Access Control: Laptop Shipping/Tracking ID",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Laptop Shipping/Tracking ID",
            "Access Control: Laptop Shipping/Tracking ID[Short text]",
            "cf[10093]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10093,
        },
    },
    {
        "id": "customfield_10094",
        "key": "customfield_10094",
        "name": "Access Control: Staff Start Date",
        "untranslatedName": "Access Control: Staff Start Date",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff Start Date",
            "Access Control: Staff Start Date[Date]",
            "cf[10094]",
        ],
        "schema": {
            "type": "date",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:datepicker",
            "customId": 10094,
        },
    },
    {
        "id": "customfield_10095",
        "key": "customfield_10095",
        "name": "Access Control: Staff Firefly Email",
        "untranslatedName": "Access Control: Staff Firefly Email",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff Firefly Email",
            "Access Control: Staff Firefly Email[Short text]",
            "cf[10095]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10095,
        },
    },
    {
        "id": "summary",
        "key": "summary",
        "name": "Summary",
        "custom": False,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["summary"],
        "schema": {"type": "string", "system": "summary"},
    },
    {
        "id": "customfield_10086",
        "key": "customfield_10086",
        "name": "Access Control: New Employee Role",
        "untranslatedName": "Access Control: New Employee Role",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: New Employee Role",
            "Access Control: New Employee Role[Radio Buttons]",
            "cf[10086]",
        ],
        "schema": {
            "type": "option",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons",
            "customId": 10086,
        },
    },
    {
        "id": "customfield_10087",
        "key": "customfield_10087",
        "name": "Access Control: Staff First Name",
        "untranslatedName": "Access Control: Staff First Name",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff First Name",
            "Access Control: Staff First Name[Short text]",
            "cf[10087]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10087,
        },
    },
    {
        "id": "customfield_10088",
        "key": "customfield_10088",
        "name": "Access Control: Staff Last Name",
        "untranslatedName": "Access Control: Staff Last Name",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff Last Name",
            "Access Control: Staff Last Name[Short text]",
            "cf[10088]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10088,
        },
    },
    {
        "id": "customfield_10089",
        "key": "customfield_10089",
        "name": "Access Control: Staff personal e-mail",
        "untranslatedName": "Access Control: Staff personal e-mail",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff personal e-mail",
            "Access Control: Staff personal e-mail[Short text]",
            "cf[10089]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10089,
        },
    },
    {
        "id": "customfield_10003",
        "key": "customfield_10003",
        "name": "Approvers",
        "untranslatedName": "Approvers",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["Approvers", "Approvers[User Picker (multiple users)]", "cf[10003]"],
        "schema": {
            "type": "array",
            "items": "user",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker",
            "customId": 10003,
        },
    },
    {
        "id": "customfield_10010",
        "key": "customfield_10010",
        "name": "Request Type",
        "untranslatedName": "Request Type",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": ["cf[10010]", "Request Type"],
        "schema": {
            "type": "sd-customerrequesttype",
            "custom": "com.atlassian.servicedesk:vp-origin",
            "customId": 10010,
        },
    },
]
JIRA_TEST_FIELDS_DUPLICATE = [
    {
        "id": "customfield_10095",
        "key": "customfield_10095",
        "name": "Access Control: Staff Firefly Email",
        "untranslatedName": "Access Control: Staff Firefly Email",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff Firefly Email",
            "Access Control: Staff Firefly Email[Short text]",
            "cf[10095]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10095,
        },
    },
    {
        "id": "customfield_10096",
        "key": "customfield_10096",
        "name": "Access Control: Staff Firefly Email",
        "untranslatedName": "Access Control: Staff Firefly Email",
        "custom": True,
        "orderable": True,
        "navigable": True,
        "searchable": True,
        "clauseNames": [
            "Access Control: Staff Firefly Email",
            "Access Control: Staff Firefly Email[Short text]",
            "cf[10096]",
        ],
        "schema": {
            "type": "string",
            "custom": "com.atlassian.jira.plugin.system.customfieldtypes:textfield",
            "customId": 10096,
        },
        "scope": {},
    },
]
JIRA_TEST_STATUSES = [
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/status/3",
        "description": "This issue is being actively worked on at the moment by the assignee.",
        "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/statuses/inprogress.png",
        "name": "In Progress",
        "untranslatedName": "In Progress",
        "id": "3",
        "statusCategory": {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "yellow",
            "name": "In Progress",
        },
    },
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/status/5",
        "description": "",
        "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/statuses/resolved.png",
        "name": "Completed",
        "untranslatedName": "Completed",
        "id": "5",
        "statusCategory": {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "green",
            "name": "Done",
        },
    },
]
JIRA_TEST_STATUSES_DUPLICATE = [
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/status/3",
        "description": "This issue is being actively worked on at the moment by the assignee.",
        "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/statuses/inprogress.png",
        "name": "In Progress",
        "untranslatedName": "In Progress",
        "id": "3",
        "statusCategory": {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "yellow",
            "name": "In Progress",
        },
    },
    {
        "self": "https://fireflyhealth.atlassian.net/rest/api/3/status/4",
        "description": "This issue is being actively worked on at the moment by the assignee.",
        "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/statuses/inprogress.png",
        "name": "In Progress",
        "untranslatedName": "In Progress",
        "id": "4",
        "statusCategory": {
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "yellow",
            "name": "In Progress",
        },
        "scope": {},
    },
]
# Test Data for jira issue transition integration, Note these are re-used in downstream tests in
# users of this client
JIRA_TEST_ISSUE_TRANSITIONS = {
    "expand": "transitions",
    "transitions": [
        {
            "id": "41",
            "name": "Cancel",
            "to": {
                "self": "https://fireflyhealth.atlassian.net/rest/api/2/status/10035",
                "description": "This was auto-generated by Jira Service Management during workflow import",
                "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/status_generic.gif",
                "name": "Canceled",
                "id": "10035",
                "statusCategory": {
                    "self": "https://fireflyhealth.atlassian.net/rest/api/2/statuscategory/3",
                    "id": 3,
                    "key": "done",
                    "colorName": "green",
                    "name": "Done",
                },
            },
            "hasScreen": False,
            "isGlobal": False,
            "isInitial": False,
            "isAvailable": True,
            "isConditional": False,
            "isLooped": False,
        },
        {
            "id": "21",
            "name": "Complete",
            "to": {
                "self": "https://fireflyhealth.atlassian.net/rest/api/2/status/6",
                "description": "The issue is considered finished, the resolution is correct.",
                "iconUrl": "https://fireflyhealth.atlassian.net/images/icons/statuses/closed.png",
                "name": "Closed",
                "id": "6",
                "statusCategory": {
                    "self": "https://fireflyhealth.atlassian.net/rest/api/2/statuscategory/3",
                    "id": 3,
                    "key": "done",
                    "colorName": "green",
                    "name": "Done",
                },
            },
            "hasScreen": False,
            "isGlobal": False,
            "isInitial": False,
            "isAvailable": True,
            "isConditional": False,
            "isLooped": False,
        },
    ],
}
JIRA_TEST_ISSUE_LINK_TYPES = {
    "issueLinkTypes": [
        {
            "id": "10000",
            "name": "Blocks",
            "inward": "is blocked by",
            "outward": "blocks",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10000",
        },
        {
            "id": "10001",
            "name": "Cloners",
            "inward": "is cloned by",
            "outward": "clones",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10001",
        },
        {
            "id": "10002",
            "name": "Duplicate",
            "inward": "is duplicated by",
            "outward": "duplicates",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10002",
        },
        {
            "id": "10103",
            "name": "Post-Incident Reviews",
            "inward": "is reviewed by",
            "outward": "reviews",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10103",
        },
        {
            "id": "10102",
            "name": "Problem/Incident",
            "inward": "is caused by",
            "outward": "causes",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10102",
        },
        {
            "id": "10003",
            "name": "Relates",
            "inward": "relates to",
            "outward": "relates to",
            "self": "https://fireflyhealth.atlassian.net/rest/api/3/issueLinkType/10003",
        },
    ]
}
JIRA_TEST_SERVICE_DESKS = {
    "size": 3,
    "start": 0,
    "limit": 50,
    "isLastPage": True,
    "_links": {
        "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk",
        "base": "https://fireflyhealth.atlassian.net",
        "context": "",
    },
    "values": [
        {
            "id": "1",
            "projectId": "10018",
            "projectName": "Access Control",
            "projectKey": "AC",
            "_links": {"self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1"},
        },
        {
            "id": "3",
            "projectId": "10022",
            "projectName": "Infrastructure Change Management",
            "projectKey": "INFRA",
            "_links": {"self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/3"},
        },
        {
            "id": "4",
            "projectId": "10024",
            "projectName": "Vendor Onboarding",
            "projectKey": "VO",
            "_links": {"self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/4"},
        },
    ],
}
JIRA_TEST_REQUEST_TYPES = {
    "_expands": ["field"],
    "size": 14,
    "start": 0,
    "limit": 50,
    "isLastPage": True,
    "_links": {
        "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype",
        "base": "https://fireflyhealth.atlassian.net",
        "context": "",
    },
    "values": [
        {
            "_expands": ["field"],
            "id": "151",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/51"  # noqa
            },
            "name": "1Password",
            "description": "Request access to 1Password",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10577",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "1510",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/51"  # noqa
            },
            "name": "Atlassian",
            "description": "Request access to Atlassian",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10577",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "51",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/51"  # noqa
            },
            "name": "Auth0",
            "description": "Request access to Auth0",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10577",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10577?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "45",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/45"  # noqa
            },
            "name": "AWS",
            "description": "Request access to AWS",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10572",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10572?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10572?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10572?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10572?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "147",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/47"  # noqa
            },
            "name": "Box",
            "description": "Request access to Box",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10574",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "1147",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/47"  # noqa
            },
            "name": "Datadog",
            "description": "Request access to Datadog",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10574",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "47",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/47"  # noqa
            },
            "name": "DBT",
            "description": "Request access to DBT",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10574",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10574?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "42",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/42"  # noqa
            },
            "name": "Elation",
            "description": "Request access to Elation",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10570",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10570?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10570?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10570?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10570?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "46",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/46"  # noqa
            },
            "name": "GitHub",
            "description": "Request access to Github",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10573",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10573?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10573?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10573?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10573?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "148",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/48"  # noqa
            },
            "name": "Google Workspace",
            "description": "Request access to Google",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10575",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "48",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/48"  # noqa
            },
            "name": "Jira",
            "description": "Request access to Jira",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10575",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10575?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "41",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/41"  # noqa
            },
            "name": "JumpCloud",
            "description": "Request access to JumpCloud",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10569",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "41",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/41"  # noqa
            },
            "name": "Looker",
            "description": "Request access to Looker",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10569",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10569?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "39",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/39",  # noqa
            },
            "name": "Lucian",
            "description": "Request access to Lucian",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10567",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10567?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10567?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10567?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10567?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "50",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/50"  # noqa
            },
            "name": "New Staff",
            "description": "Request setup and approval for new Firefly Staff (including Employees and Contractors)",  # noqa
            "helpText": "",
            "issueTypeId": "10039",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["12"],
            "icon": {
                "id": "10519",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10519?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10519?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10519?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10519?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "52",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/52"  # noqa
            },
            "name": "Opsgenie",
            "description": "Request access to Opsgenie",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10578",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10578?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10578?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10578?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10578?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "43",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/43"  # noqa
            },
            "name": "PracticeSuite",
            "description": "Request access to PracticeSuite",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10571",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10571?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10571?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10571?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10571?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "140",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/40"  # noqa
            },
            "name": "Slack",
            "description": "Request access to Slack",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10568",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "40",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/40"  # noqa
            },
            "name": "Snowflake",
            "description": "Request access to Snowflake",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10568",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "410",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/40"  # noqa
            },
            "name": "TalkDesk",
            "description": "Request access to TalkDesk",
            "helpText": "",
            "issueTypeId": "10038",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["1"],
            "icon": {
                "id": "10568",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10568?size=medium",  # noqa
                    }
                },
            },
        },
        {
            "_expands": ["field"],
            "id": "53",
            "_links": {
                "self": "https://fireflyhealth.atlassian.net/rest/servicedeskapi/servicedesk/1/requesttype/53"  # noqa
            },
            "name": "Start Onboarding",
            "description": "",
            "helpText": "",
            "issueTypeId": "10040",
            "serviceDeskId": "1",
            "portalId": "1",
            "groupIds": ["12"],
            "icon": {
                "id": "10552",
                "_links": {
                    "iconUrls": {
                        "48x48": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10552?size=large",  # noqa
                        "24x24": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10552?size=small",  # noqa
                        "16x16": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10552?size=xsmall",  # noqa
                        "32x32": "https://fireflyhealth.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10552?size=medium",  # noqa
                    }
                },
            },
        },
    ],
}


class JiraClientUserCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(args, kwargs)
        self.jira_client = JiraClient()

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_user_found(self, mock_get, mock_auth):
        mock_auth.return_value = None
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_USERS
        self.jira_client.find_user_by_email("<EMAIL>")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_no_user_found(self, mock_get, mock_auth):
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = []
        with pytest.raises(UserNotFound):
            self.jira_client.find_user_by_email("<EMAIL>")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_duplicate_user_found(self, mock_get, mock_auth):
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_USER_DUPLICATE
        with pytest.raises(DuplicateUser):
            self.jira_client.find_user_by_email("<EMAIL>")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_wrong_user_found(self, mock_get, mock_auth):
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_USERS
        with pytest.raises(AssertionError):
            self.jira_client.find_user_by_email("<EMAIL>")


class JiraClientProjectCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    @skip("flaky")
    def test_get_project(self, mock_get, mock_auth):
        get_project.cache_clear()
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_PROJECT
        project = JiraProject("AC")
        self.assertEqual(project.get_id(), "10018")
        self.assertEqual(project.get_name(), "AC")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_get_missing_project(self, mock_get, mock_auth):
        get_project.cache_clear()
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_PROJECT
        with pytest.raises(AssertionError):
            project = JiraProject("DE")
            project.get_id()


class JiraClientIssueTypeCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_success_issue_types(self, mock_get, mock_auth):
        get_issue_types.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_TYPES
        issuetype = JiraIssueType("Access Control: Access Request")
        self.assertEqual(issuetype.get_id(), "10038")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_fail_issue_types(self, mock_get, mock_auth):
        get_issue_types.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_TYPES
        with pytest.raises(AttributeNotDefined):
            issuetype = JiraIssueType("Not an IssueType")
            issuetype.get_id()


class JiraClientFieldCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_field(self, mock_get, mock_auth):
        get_fields.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_FIELDS
        field = JiraField("Access Control: Staff Start Date")
        self.assertEqual(field.get_id(), "customfield_10094")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_field_not_defined(self, mock_get, mock_auth):
        get_fields.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = []
        field = JiraField("Access Control: Staff Start Date")
        with pytest.raises(AttributeNotDefined):
            field.get_id()

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_field_duplicate(self, mock_get, mock_auth):
        get_fields.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_FIELDS_DUPLICATE
        field = JiraField("Access Control: Staff Firefly Email")
        self.assertEqual(field.get_id(), "customfield_10095")


class JiraClientStatusCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_status(self, mock_get, mock_auth):
        get_statuses.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_STATUSES
        status = JiraStatus("In Progress")
        self.assertEqual(status.get_id(), "3")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_status_not_defined(self, mock_get, mock_auth):
        get_statuses.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = []
        status = JiraStatus("In Progress")
        with pytest.raises(AttributeNotDefined):
            status.get_id()

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_validate_jira_status_duplicate(self, mock_get, mock_auth):
        get_statuses.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_STATUSES_DUPLICATE
        status = JiraStatus("In Progress")
        self.assertEqual(status.get_id(), "3")


class JiraClientTransitionCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(args, kwargs)
        self.jira_client = JiraClient()

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_valid_transition(self, mock_get, mock_auth):
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_TRANSITIONS
        transition = self.jira_client.lookup_issue_transition(issue_id="FAKE-123", transition_name="Complete")
        self.assertEqual(transition.get("id"), "21")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_invalid_transition(self, mock_get, mock_auth):
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_TRANSITIONS
        with pytest.raises(AssertionError):
            self.jira_client.lookup_issue_transition(issue_id="FAKE-123", transition_name="FAKE TRANSITION")


class JiraClientIssueLinkTypeCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_get_issue_link_types_success(self, mock_get, mock_auth):
        get_issue_link_types.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_LINK_TYPES
        issue_link_type = JiraIssueLinkType("Blocks")
        self.assertEqual(issue_link_type.get_id(), "10000")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_get_issue_link_types_failure(self, mock_get, mock_auth):
        get_issue_link_types.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_ISSUE_LINK_TYPES
        with pytest.raises(AttributeNotDefined):
            issue_link_type = JiraIssueLinkType("Not an IssueLinkType")
            issue_link_type.get_id()


class JiraServiceDeskCase(FireflyTestCase):
    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("requests.get")
    def test_get_service_desks(self, mock_get, mock_auth):
        get_issue_link_types.cache_clear()
        mock_auth.return_value = ""
        mock_get.return_value.status_code.return_value = 200
        mock_get.return_value.json.return_value = JIRA_TEST_SERVICE_DESKS
        service_desk = JiraServiceDesk("AC")
        self.assertEqual(service_desk.get_id(), "1")

    @patch("firefly.core.services.jira.client.JiraClient._get_basic_auth")
    @patch("firefly.core.services.jira.attributes.get_request_types")
    @patch("firefly.core.services.jira.attributes.get_service_desks")
    def test_get_request_types(self, mock_get_service_desks, mock_get_request_types, mock_auth):
        get_issue_link_types.cache_clear()
        mock_auth.return_value = ""
        mock_get_service_desks.return_value = JIRA_TEST_SERVICE_DESKS["values"]
        mock_get_request_types.return_value = JIRA_TEST_REQUEST_TYPES["values"]
        request_type = JiraRequestType("Lucian", JiraServiceDesk("AC"))
        self.assertEqual(request_type.get_id(), "39")
