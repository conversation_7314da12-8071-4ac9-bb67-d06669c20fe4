from django.template.loader import render_to_string

BYPASS_ELATION_SYNC = "BYPASS_ELATION_SYNC"
EMOTICONS_UNICODE_RANGE = render_to_string("emoji_unicodes.txt", {})
SYMOBOLS_AND_PICTOGRAPHS_UNICODE_RANGE = "\U0001f300-\U0001f5ff"
TRANSPORT_AND_MAP_SYMBOLS_UNICODE_RANGE = "\U0001f680-\U0001f6ff"
IOS_FLAGS_SYMBOLS_UNICODE_RANGE = "\U0001f1e0-\U0001f1ff"

SEX_CHOICES = (("Male", "Male"), ("Female", "Female"), ("Unknown", "Unknown"))
WAFFLE_SWITCH_SKIP_ADDING_EMPTY_OBJECTS = "elation.skip_adding_empty_objects"
