from __future__ import annotations

import logging
from enum import Enum
from typing import TYPE_CHECKING, List, Optional

if TYPE_CHECKING:
    from firefly.core.services.zoom.client import RecordingFile, SessionDetail

logger = logging.getLogger(__name__)


class VIDEO_SDK(Enum):
    ZOOM = "zoom"
    TOKBOX = "tokbox"


def is_recording_details_valid(session_detail: SessionDetail):
    log_prefix: str = f"is_recording_details_valid: {str(session_detail.get('session_id'))}"
    recording_files: Optional[List[RecordingFile]] = session_detail.get("recording_files")
    is_valid: bool = False
    if recording_files is not None:
        filtered_recording_files: List[RecordingFile] = list(
            filter(
                lambda recording: (
                    recording is not None
                    and recording.get("file_type") is not None
                    and recording.get("file_type") == "M4A"
                    and recording.get("recording_type") is not None
                    and recording.get("recording_type") == "audio_only"
                ),
                recording_files,
            )
        )
        if len(filtered_recording_files) > 1:
            logger.error("%s: More than one recording found for session", log_prefix)
        elif len(filtered_recording_files) == 0 or filtered_recording_files[0] is None:
            logger.error("%s: No recording found for session", log_prefix)
        elif len(filtered_recording_files) != len(recording_files):
            logger.error("%s: Non audio recording found for session", log_prefix)
        else:
            is_valid = True
    return is_valid
