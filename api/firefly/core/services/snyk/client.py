import logging
from typing import Optional, TypedDict
from urllib.parse import parse_qs, urlparse

import requests
from django.conf import settings
from typing_extensions import NotRequired

logger = logging.getLogger(__name__)


class SnykClient(object):
    def __init__(self):
        self.base_v1_url = "https://api.snyk.io/api/v1"
        self.base_rest_url = "https://api.snyk.io/rest"
        self.organization_id = settings.SNYK["ORG_ID"]

    def get(self, endpoint, params=None, with_org_id=True, use_v1_endpoint=True):
        url = self._get_url(endpoint, with_org_id, use_v1_endpoint)
        headers = self._get_headers()
        r = requests.get(url=url, params=params, headers=headers)
        r.raise_for_status()
        return r.json()

    def post(self, endpoint, data=None, with_org_id=True):
        url = self._get_url(endpoint, with_org_id)
        headers = self._get_headers()
        r = requests.post(url=url, json=data, headers=headers)
        r.raise_for_status()
        return r.json()

    def get_projects(self):
        projects: list = []
        next_page: Optional[str] = None
        prev_page: Optional[str] = None
        page_num: int = 1

        # See https://docs.snyk.io/snyk-api/rest-api/about-the-rest-api#pagination
        class GetProjectRequestParams(TypedDict):
            version: str
            limit: int
            starting_after: NotRequired[str]

        class PaginatedResponseLinks(TypedDict):
            prev: NotRequired[Optional[str]]
            next: NotRequired[Optional[str]]

        class GetProjectResponse(TypedDict):
            data: list
            links: NotRequired[Optional[PaginatedResponseLinks]]

        while (
            next_page is not None
            # either a prev page does not exist or is not the same as the next page
            and ((prev_page is not None and next_page != prev_page) or (prev_page is None))
        ) or (page_num == 1):
            page_num = page_num + 1
            get_projects_params: GetProjectRequestParams = {
                "version": "2023-05-29",
                "limit": 30,
            }
            if next_page:
                get_projects_params["starting_after"] = next_page
            paginated_response: GetProjectResponse = self.get(
                endpoint="projects", params=get_projects_params, use_v1_endpoint=False
            )
            next_page = None
            prev_page = None
            if paginated_response is not None and len(paginated_response["data"]) > 0:
                projects.extend(paginated_response["data"])
                paginated_response_links: Optional[PaginatedResponseLinks] = paginated_response.get("links")
                if paginated_response_links:
                    next_page_link: Optional[str] = paginated_response_links.get("next")
                    if next_page_link:
                        starting_after = parse_qs(urlparse(next_page_link).query).get("starting_after")
                        if starting_after:
                            next_page = starting_after[0]
                    prev_page_link: Optional[str] = paginated_response_links.get("prev")
                    if prev_page_link:
                        ending_before = parse_qs(urlparse(prev_page_link).query).get("ending_before")
                        if ending_before:
                            prev_page = ending_before[0]
        return projects

    def get_issues(self, project_id, filters=None):
        filters = {"filters": filters} or None
        return self.post(f"project/{project_id}/aggregated-issues", data=filters)["issues"]

    def get_issue_ids(self, project_id, filters=None):
        issues = [i["id"] for i in self.get_issues(project_id, filters)]
        return issues

    def create_jira_issue(self, project_id, issue_id, fields=None):
        data = {"fields": fields}
        issue = self.post(f"project/{project_id}/issue/{issue_id}/jira-issue", data=data)
        return issue[issue_id][0]["jiraIssue"]["key"]

    def get_jira_issues(self, project_id):
        jira_issues = self.get(f"project/{project_id}/jira-issues")
        jira_issues = {vulnerability: issues[0]["jiraIssue"]["key"] for vulnerability, issues in jira_issues.items()}
        return jira_issues

    def _get_url(self, endpoint, with_org_id=True, use_v1_endpoint=True):
        if use_v1_endpoint:
            return (
                f"{self.base_v1_url}/org/{self.organization_id}/{endpoint}"
                if with_org_id
                else f"{self.base_v1_url}/{endpoint}"
            )
        else:
            return (
                f"{self.base_rest_url}/orgs/{self.organization_id}/{endpoint}"
                if with_org_id
                else f"{self.base_v1_url}/{endpoint}"
            )

    @staticmethod
    def _get_headers():
        return {"Authorization": f"token {settings.SNYK['API_KEY']}"}
