from unittest import mock

from django.apps import apps
from django.contrib.auth.models import User
from django.db import models
from django.test import RequestFactory
from django.test.testcases import TransactionTestCase
from django_fake_model import models as fake_model
from rest_framework import serializers, viewsets

from ..permissions import CanAccessPHIForUser


class FakeUserModel(fake_model.FakeModel):
    id: models.AutoField = models.AutoField(primary_key=True)


class FakeUserRelationModel(fake_model.FakeModel):
    id: models.AutoField = models.AutoField(primary_key=True)
    user: models.OneToOneField = models.OneToOneField(
        FakeUserModel,
        related_name="+",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )


class FakeModelSerializer(serializers.BaseSerializer):
    def to_representation(self, instance):
        return {"id": instance.id}

    def to_internal_value(self, data):
        return data

    def create(self, validated_data):
        return FakeUserModel(validated_data)


class FakeUserRelationModelSerializer(serializers.BaseSerializer):
    def to_representation(self, instance):
        return {"id": instance.id, "user_id": instance.user_id}


class PermissionsTestCase(TransactionTestCase):
    available_apps = {app_config.name for app_config in apps.get_app_configs()}

    class FakeUserViewSet(viewsets.ModelViewSet):
        model = FakeUserModel
        queryset = FakeUserModel.objects.all()
        lookup_field = "id"
        serializer_class = FakeModelSerializer
        permission_classes = ()

    class FakeUserRelationModelViewSet(viewsets.ModelViewSet):
        model = FakeUserRelationModel
        queryset = FakeUserRelationModel.objects.all()
        lookup_field = "id"
        serializer_class = FakeUserRelationModelSerializer
        permission_classes = (CanAccessPHIForUser,)
        path_to_user_id = "user_id"

    def setUp(self):
        self.request_factory = RequestFactory()
        self.requesting_user = User(id=1)

    @mock.patch("firefly.core.phi_access_control.permissions.requesting_user_has_access")
    @mock.patch("firefly.core.phi_access_control.permissions.request_auth_for_phi_for_user")
    @FakeUserModel.fake_me
    @FakeUserRelationModel.fake_me
    def test_can_access_vip_data(self, mock_request_auth_for_phi_for_user, mock_requesting_user_has_access):
        fake_user_instance = FakeUserModel.objects.create(id=self.requesting_user.id + 1)
        request = self.request_factory.get("/")
        request.user = self.requesting_user
        self.assertIsNotNone(fake_user_instance.id)

        def get_response():
            return self.FakeUserViewSet.as_view({"get": "retrieve"})(request, **{"id": fake_user_instance.id})

        self.assertEqual(get_response().status_code, 200)

        # Add PHI termissions to the View
        self.FakeUserViewSet.permission_classes = (CanAccessPHIForUser,)
        # If the User does not require auth...
        mock_request_auth_for_phi_for_user.cache_clear()
        mock_request_auth_for_phi_for_user.return_value = False
        self.assertEqual(get_response().status_code, 200)
        mock_requesting_user_has_access.assert_not_called()
        # If the User does require auth...
        mock_request_auth_for_phi_for_user.cache_clear()
        mock_request_auth_for_phi_for_user.return_value = True
        # ...and the requesting User has auth...
        mock_requesting_user_has_access.return_value = (True, None)
        self.assertEqual(get_response().status_code, 200)
        mock_requesting_user_has_access.assert_called_once_with(request.user.id, fake_user_instance.id)
        # When the requesting User does not have auth...
        mock_requesting_user_has_access.reset_mock()
        mock_requesting_user_has_access.return_value = (False, None)
        # Permission denied
        self.assertEqual(get_response().status_code, 403)
        mock_requesting_user_has_access.assert_called_once_with(request.user.id, fake_user_instance.id)
        # Creating a resource is not a PHI violation, and is allowed
        mock_requesting_user_has_access.reset_mock()
        post_request = self.request_factory.post("/")
        post_response = self.FakeUserViewSet.as_view({"post": "create"})(post_request)
        self.assertEqual(post_response.status_code, 201)
        mock_requesting_user_has_access.assert_not_called()
        # When the requesting User is the instance User...
        original_id = self.requesting_user.id
        self.requesting_user.id = fake_user_instance.id
        mock_requesting_user_has_access.reset_mock()
        self.assertEqual(get_response().status_code, 200)
        mock_requesting_user_has_access.assert_not_called()
        self.requesting_user.id = original_id

        # Test the View for a parent object with a relation to User
        fake_instance = FakeUserRelationModel.objects.create(user=fake_user_instance)

        def get_response():
            return self.FakeUserRelationModelViewSet.as_view({"get": "retrieve"})(request, **{"id": fake_instance.id})

        # Permission denied
        mock_requesting_user_has_access.reset_mock()
        self.assertEqual(get_response().status_code, 403)
        mock_requesting_user_has_access.assert_called_once_with(request.user.id, fake_user_instance.id)
        # When the requesting User does have auth again...
        mock_requesting_user_has_access.reset_mock()
        mock_requesting_user_has_access.return_value = (True, None)
        self.assertEqual(get_response().status_code, 200)
        mock_requesting_user_has_access.assert_called_once_with(request.user.id, fake_user_instance.id)
