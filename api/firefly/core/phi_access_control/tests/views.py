from datetime import timed<PERSON><PERSON>

from django.utils import timezone as django_timezone

from firefly.core.phi_access_control.utils import request_auth_for_phi_for_user
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.factories import PersonUserFactory

from ..constants import AccessReasons
from ..models import PHIAccessLog


class PHIAccessControlViewSetTestCase(FireflyTestCase):
    def test_create(self):
        response = self.provider_client.post("/phi_access_control/")
        self.assertEqual(response.status_code, 400)
        response = self.provider_client.post(
            "/phi_access_control/",
            data={"user_id": self.patient.id, "requesting_user_id": self.provider.id},
        )
        self.assertEqual(response.status_code, 400)
        self.assertFalse(PHIAccessLog.objects.filter(user_id=self.patient.id).exists())
        request_data = {
            "user_id": self.patient.id,
            "requesting_user_id": self.provider.id,
            "access_reason": AccessReasons.OTHER,
        }
        response = self.provider_client.post("/phi_access_control/", data=request_data)
        log = PHIAccessLog.objects.filter(user_id=self.patient.id, requesting_user_id=self.provider.id).first()
        self.assertEqual(log.access_reason, request_data["access_reason"])
        self.assertEqual(response.data["access"], True)
        self.assertEqual(response.data["expires_at"], log.expires_at)

        # Another request for access should update the same log

        def get_history_count(log):
            return log.pgh_events.filter(id=log.id).count()

        initial_history_count = get_history_count(log)
        request_data["reason"] = AccessReasons.MESSAGE_TRIAGE
        response = self.provider_client.post("/phi_access_control/", data=request_data)
        log.refresh_from_db()
        self.assertEqual(get_history_count(log), initial_history_count + 1)
        self.assertEqual(log.access_reason, request_data["access_reason"])

        # A request for another user is a new log
        new_person = PersonUserFactory()
        new_user = new_person.user
        new_person.request_auth_for_phi = True
        new_person.save()
        request_data["user_id"] = new_user.id
        response = self.provider_client.post("/phi_access_control/", data=request_data)
        new_log = PHIAccessLog.objects.filter(user_id=new_user.id, requesting_user_id=self.provider.id).first()
        self.assertNotEqual(new_log.id, log.id)

    def test_check_access(self):
        self.patient.person.request_auth_for_phi = False
        self.patient.person.save()

        response = self.provider_client.get("/phi_access_control/check_access/")
        self.assertEqual(response.status_code, 400)
        complete_uri = (
            f"/phi_access_control/check_access/?user_id={self.patient.id}&requesting_user_id={self.provider.id}"
        )
        response = self.provider_client.get(
            complete_uri,
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["access"], True)
        # Check with request_auth_for_phi = True
        # Access should be denied until a PHIAccessLog is created
        self.patient.person.request_auth_for_phi = True
        request_auth_for_phi_for_user.cache_clear()
        self.patient.person.save()
        response = self.provider_client.get(
            complete_uri,
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["access"], False)
        access_log = PHIAccessLog.objects.create(
            user_id=self.patient.id,
            requesting_user_id=self.provider.id,
            access_reason=AccessReasons.OTHER,
            expires_at=django_timezone.now() + timedelta(days=-1),
        )
        response = self.provider_client.get(
            complete_uri,
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["access"], False)
        access_log.expires_at = django_timezone.now() + timedelta(days=1)
        access_log.save()
        response = self.provider_client.get(
            complete_uri,
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["access"], True)
