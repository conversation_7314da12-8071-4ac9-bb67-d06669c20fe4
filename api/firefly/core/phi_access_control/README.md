# PHI Access Control

This app manages access control for Protected Health Information (PHI) of VIP patients, providing authorization mechanisms and audit trails for sensitive patient data access.

## Business Logic

The PHI access control workflow works as follows:

1. **VIP Identification**: Patients marked with `Person.request_auth_for_phi = True` are considered VIPs
2. **Access Request**: Providers must request explicit access to view VIP patient data
3. **Access Logging**: All access attempts and grants are logged for audit purposes
4. **Permission Enforcement**: API endpoints are protected with permission classes
5. **PHI Redaction**: Unauthorized access results in redacted data (handled by `redact_phi` app)

## Core Components

### VIP Patient Detection
The `request_auth_for_phi_for_user` function determines whether a patient's PHI requires special authorization. These patients are referred to as "VIPs" in business terminology.

### PHIAccessLog Model
Stores access control information:
- **Requesting User**: Provider user requesting access
- **Target User**: Patient user whose PHI is being accessed
- **Access Status**: Whether access was granted or denied
- **Audit Trail**: Historical record via connected event table

### API Endpoints

#### Check Access
- **Endpoint**: `check_access`
- **Purpose**: Verify if requesting user has access to target user's PHI
- **Usage**: Front-ends check access before displaying sensitive data

#### Grant Access
- **Endpoint**: `create`
- **Purpose**: Grant access to requesting user for target user's PHI
- **Usage**: Used in conjunction with `check_access` for access management

### Permission Classes

#### CanAccessPHIForUser
- **Purpose**: Protects API endpoints from unauthorized PHI access
- **Implementation**: Extra safeguard beyond front-end access checks
- **Usage**: Applied to endpoints that serve sensitive patient data

## Usage Examples

### Checking PHI Access
```python
from firefly.core.phi_access_control.api import check_access

# Check if provider has access to patient PHI
has_access = check_access(requesting_user=provider, target_user=patient)
if has_access:
    # Display full patient information
    pass
else:
    # Display redacted information or request access
    pass
```

### Granting PHI Access
```python
from firefly.core.phi_access_control.models import PHIAccessLog

# Grant access to VIP patient PHI
access_log = PHIAccessLog.objects.create(
    requesting_user=provider,
    target_user=vip_patient,
    access_granted=True
)
```

### API Endpoint Protection
```python
from firefly.core.phi_access_control.permissions import CanAccessPHIForUser

class PatientDetailView(APIView):
    permission_classes = [CanAccessPHIForUser]

    def get(self, request, patient_id):
        # This endpoint is protected - access will be checked automatically
        patient = get_object_or_404(Person, id=patient_id)
        return Response(patient_data)
```

## Integration Points

### Redact PHI App
The `redact_phi` app works in conjunction with PHI access control:
- **Access Control**: Determines if access should be granted
- **Redaction**: Handles actual PHI redaction when access is denied
- **Workflow**: Access control → redaction → response formatting

### Front-end Integration
Front-ends should:
1. Use `check_access` API before displaying sensitive data
2. Use `create` API to request access when needed
3. Handle redacted responses gracefully
4. Provide clear UI for access request workflows

## Audit and Compliance

### Access Logging
- **Complete Audit Trail**: All access attempts logged with timestamps
- **Event History**: Historical record maintained for compliance
- **User Tracking**: Both requesting and target users tracked
- **Access Patterns**: Enables monitoring of unusual access patterns

### Security Features
- **Multi-layer Protection**: API permissions + front-end checks
- **Explicit Access**: No implicit access to VIP patient data
- **Audit Requirements**: Comprehensive logging for regulatory compliance
