from django import forms
from django.contrib.postgres.forms.array import <PERSON><PERSON><PERSON>y<PERSON><PERSON>

from firefly.core.roles.models import Role
from firefly.modules.firefly_django.fireflyadmincommand.models import FireflyAdminCommand
from firefly.modules.physician.models import Physician
from firefly.modules.tenants.models import Tenant


class RoleChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return f"{obj.role_name}"


class WideCharField(forms.CharField):
    def widget_attrs(self, widget):
        attrs = super().widget_attrs(widget)

        attrs["size"] = "40"

        return attrs


class CreateStaffUser(FireflyAdminCommand):
    class form(forms.Form):
        email = WideCharField()
        first_name = WideCharField()
        last_name = WideCharField()
        phone_number = forms.CharField()
        title = forms.CharField(help_text="Patient facing title", required=True)

        test_account = forms.BooleanField(required=False)
        is_external = forms.BooleanField(required=False)
        role = RoleChoiceField(required=True, queryset=Role.objects.all())
        tenant = forms.ModelChoiceField(required=True, queryset=Tenant.objects.all())
        slack_alias_id = forms.CharField(
            help_text="Required if the account will receive Slack notifications. See instructions <a href='https://sites.google.com/firefly.health/tech/operations/account-setup?authuser=0#h.20fxdvt7vkoy'>here</a>",  # noqa
            required=False,
        )
        bio = forms.CharField(
            help_text="Patient-facing biography. Should be found in the <a href='https://docs.google.com/spreadsheets/d/1Ebd4SDp5YkHYhZumhPAcowmdgo8zBVTZa8Sg_aRCSYc/edit#gid=*********'>Intro Content Doc</a>",  # noqa
            required=False,
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["email"]:
            args.append("-email")
            args.append(data["email"])
        if data["first_name"]:
            args.append("-first_name")
            args.append(data["first_name"])
        if data["last_name"]:
            args.append("-last_name")
            args.append(data["last_name"])
        if data["phone_number"]:
            args.append("-phone_number")
            args.append(data["phone_number"])
        if data["title"]:
            args.append("-title")
            args.append(data["title"])
        if data["role"]:
            args.append("-role_id")
            args.append(data["role"].id)
        if data["test_account"]:
            args.append("-test_account")
        if data["is_external"]:
            args.append("-is_external")

        if data["tenant"]:
            args.append("-tenant_id")
            args.append(data["tenant"].id)

        if data["bio"]:
            args.append("-bio")
            args.append(data["bio"])
        if data["slack_alias_id"]:
            args.append("-slack_alias_id")
            args.append(data["slack_alias_id"])

        return args, {
            "user": user,
        }


class PhysicianModelChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return f"{obj.first_name} {obj.last_name}"


class CreateProvider(CreateStaffUser):
    class form(CreateStaffUser.form):
        physician = PhysicianModelChoiceField(
            required=True,
            queryset=Physician.objects.all().order_by("-created_at"),
            help_text="If not present, needs to be synced from Elation",
        )
        states = forms.CharField(
            help_text="Comma-separated state abbreviations for this providers licenses, e.g. 'MA,NH'"
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["physician"]:
            args.append("-physician_id")
            args.append(data["physician"].id)
        if data["states"]:
            args.append("-states")
            args.append(data["states"])

        parent_args, data = super().get_command_arguments(data, user)
        args = args + parent_args
        return args, {
            "user": user,
        }


class DisableUser(FireflyAdminCommand):
    class form(forms.Form):
        id = forms.IntegerField()
        email = forms.CharField()

    def get_command_arguments(self, data, user):
        args = []
        if data["id"]:
            args.append("-id")
            args.append(data["id"])
        if data["email"]:
            args.append("-email")
            args.append(data["email"])
        return args, {
            "user": user,
        }


class EnableUser(FireflyAdminCommand):
    class form(forms.Form):
        id = forms.IntegerField()
        email = forms.CharField()

    def get_command_arguments(self, data, user):
        args = []
        if data["id"]:
            args.append("-id")
            args.append(data["id"])
        if data["email"]:
            args.append("-email")
            args.append(data["email"])
        return args, {
            "user": user,
        }


class BackfillLatitudeLongitudeForPersonAddress(FireflyAdminCommand):
    class form(forms.Form):
        test_accounts_only = forms.BooleanField(
            required=False,
            initial=False,
        )

    def get_command_arguments(self, data, user):
        opts = []
        if data["test_accounts_only"] is True:
            opts.append("--test_accounts_only")

        return opts, {
            "user": user,
        }


class CreateUsersForUnderageDependents(FireflyAdminCommand):
    class form(forms.Form):
        dependent = forms.IntegerField(label="Dependent person id", required=False)
        dryrunoff = forms.BooleanField(label="Write to database", required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["dependent"]:
            args.append("--dependent")
            args.append(data["dependent"])
        if data["dryrunoff"]:
            args.append("--dryrunoff")
            args.append(data["dryrunoff"])
        return args, {"user": user}


class MergePersons(FireflyAdminCommand):
    class form(forms.Form):
        src_person = forms.IntegerField(label="Source person PK (person to be deleted)")
        src_person_email = forms.CharField(label="Source person email", required=False)
        dest_person = forms.IntegerField(label="Dest person PK (person to be updated)")
        dest_person_email = forms.CharField(label="Dest person email", required=False)
        overwrite_coverage = forms.BooleanField(label="Overwrite coverage data", required=False)
        delete_src_person = forms.BooleanField(label="Delete source person after merge", required=False)
        dryrunoff = forms.BooleanField(label="Write to database", required=False)

    def get_command_arguments(self, data, user):
        args = ["--src_person", data["src_person"], "--dest_person", data["dest_person"]]
        if data["src_person_email"]:
            args.append("--src_person_email")
            args.append(data["src_person_email"])
        if data["dest_person_email"]:
            args.append("--dest_person_email")
            args.append(data["dest_person_email"])
        if data["overwrite_coverage"]:
            args.append("--overwrite_coverage")
            args.append(data["overwrite_coverage"])
        if data["delete_src_person"]:
            args.append("--delete_src_person")
            args.append(data["delete_src_person"])
        if data["dryrunoff"]:
            args.append("--dryrunoff")
            args.append(data["dryrunoff"])
        return args, {"user": user}


class AddClinicianToCareTeam(FireflyAdminCommand):
    class form(forms.Form):
        nurse_practioner_id = forms.IntegerField()
        clinician_id = forms.IntegerField()
        dry_run = forms.BooleanField(initial=True, required=False)

    def get_command_arguments(self, data, user):
        args = [
            "-nurse_practioner_id",
            data["nurse_practioner_id"],
            "-clinician_id",
            data["clinician_id"],
        ]
        if data["dry_run"] is False:
            args.append("--dryrunoff")
        return args, {
            "user": user,
        }


class RemoveClinicianFromCareTeam(FireflyAdminCommand):
    class form(forms.Form):
        nurse_practioner_id = forms.IntegerField()
        clinician_id = forms.IntegerField()
        dry_run = forms.BooleanField(initial=True, required=False)

    def get_command_arguments(self, data, user):
        args = [
            "-nurse_practioner_id",
            data["nurse_practioner_id"],
            "-clinician_id",
            data["clinician_id"],
        ]
        if data["dry_run"] is False:
            args.append("--dryrunoff")
        return args, {
            "user": user,
        }


class SyncUserToElation(FireflyAdminCommand):
    class form(forms.Form):
        user_ids = forms.CharField(required=True, help_text="Comma-separated User IDs")

    def get_command_arguments(self, data, user):
        args = [
            "-user_ids",
            data["user_ids"],
        ]
        return args, {
            "user": user,
        }


class BackfillPersonPrimaryPhysicianFromCareTeam(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        user_ids = forms.CharField(required=True, help_text="Comma-separated User IDs")
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        opts = [
            "-user_ids",
            data["user_ids"],
        ]
        return opts, {"user": user}


class BackfillFireflyUsersToZus(FireflyAdminCommand):
    class form(forms.Form):
        person_ids = forms.CharField(help_text="Comma separated list: run on individual users", required=False)
        dry_run = forms.BooleanField(
            initial=True,
            required=False,
        )
        run_all = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Run on all member users irrespective of whether they have an alias map",
        )
        force = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Force sync with Zus even if no changes are detected between the two systems",
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["person_ids"]:
            args.append(["--person_ids", data["person_ids"]])
        if data["dry_run"]:
            args.append("--dryrun")
        if data["run_all"]:
            args.append("--all")
        if data["force"]:
            args.append("--force")
        return args, {
            "user": user,
        }


class BackfillHealthPlanProspectsToZus(FireflyAdminCommand):
    class form(forms.Form):
        person_ids = forms.CharField(help_text="Comma separated list: run on individual users", required=False)
        dry_run = forms.BooleanField(
            initial=True,
            required=False,
        )
        run_all = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Run on all patients irrespective of whether they have an alias map",
        )
        force = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Force sync with Zus even if no changes are detected between the two systems",
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["person_ids"]:
            args.append(["--person_ids", data["person_ids"]])
        if data["dry_run"]:
            args.append("--dryrun")
        if data["run_all"]:
            args.append("--all")
        if data["force"]:
            args.append("--force")
        return args, {
            "user": user,
        }


class FetchZusPatientMedicalHistory(FireflyAdminCommand):
    class form(forms.Form):
        person_ids = forms.CharField(help_text="Comma separated list: run on individual users", required=False)
        dry_run = forms.BooleanField(
            initial=True,
            required=False,
        )
        lower_bound = forms.IntegerField(initial=0, help_text="Lower bound of batch to pull")
        upper_bound = forms.IntegerField(
            initial=0,
            help_text="Upper bound of batch to pull, "
            + "for example a lower bound of 200 and an upper bound of 400 would run the "
            + "job on the 200 patients between the 200th and 400th index of the query",
        )
        from_start_date = forms.DateField(
            required=False,
            label="Only select member whose start date after this. Format: %Y-%m-%d.",
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["person_ids"]:
            args.append(["--person_ids", data["person_ids"]])
        if data["dry_run"]:
            args.append("--dryrun")
        if data["lower_bound"]:
            args.append(["--lower_bound", data["lower_bound"]])
        if data["upper_bound"]:
            args.append(["--upper_bound", data["upper_bound"]])
        if data.get("from_start_date"):
            args += ["--from_start_date", data["from_start_date"]]
        return args, {
            "user": user,
        }


class BackfillFireflyProvidersToZus(FireflyAdminCommand):
    class form(forms.Form):
        provider_ids = forms.CharField(help_text="Comma separated list: run on individual providers", required=False)
        dry_run = forms.BooleanField(
            initial=True,
            required=False,
        )
        run_all = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Run on all providers irrespective of whether they have an alias map",
        )
        force = forms.BooleanField(
            initial=False,
            required=False,
            help_text="Force sync with Zus even if no changes are detected between the two systems",
        )

    def get_command_arguments(self, data, user):
        args = []
        if data["provider_ids"]:
            args.append(["--provider_ids", data["provider_ids"]])
        if data["dry_run"]:
            args.append("--dryrun")
        if data["run_all"]:
            args.append("--all")
        if data["force"]:
            args.append("--force")
        return args, {
            "user": user,
        }


class RemoveTeamMembersFromCareTeam(FireflyAdminCommand):
    class form(forms.Form):
        user_ids = SimpleArrayField(forms.IntegerField())
        person_ids = SimpleArrayField(forms.IntegerField(), required=False)
        dry_run = forms.BooleanField(initial=True, required=False)

    def get_command_arguments(self, data, user):
        args = ["-user_ids", data["user_ids"]]
        if data["person_ids"]:
            args.append("-person_ids")
            args.append(data["person_ids"])
        if data["dry_run"] is False:
            args.append("--dryrunoff")
        return args, {
            "user": user,
        }


class CreateAuth0Account(FireflyAdminCommand):
    class form(forms.Form):
        user_id = forms.IntegerField(required=False)

    def get_command_arguments(self, data, user):
        return ["-user_id", data["user_id"]], {
            "user": user,
        }


class MatchAndMergePersons(FireflyAdminCommand):
    class form(forms.Form):
        dest_person = forms.IntegerField(label="Dest person PK (person to be updated)")
        dest_person_email = forms.CharField(label="Dest person email", required=False)
        delete_src_person = forms.BooleanField(label="Delete source person after merge", required=False)
        employee_identifier = forms.CharField(
            label="Employee Identifier", help_text="Get the employee identifier for user. ex: SID", required=False
        )
        last_ssn = forms.CharField(label="Last SSN", help_text="Get the last 4 digit SSN for user.", required=False)
        member_id = forms.CharField(label="member id", help_text="Get the member id for user.", required=False)
        dryrunoff = forms.BooleanField(label="Write to database", required=False)

    def get_command_arguments(self, data, user):
        args = ["--dest_person", data["dest_person"]]
        if data["member_id"]:
            args.append("--member_id")
            args.append(data["member_id"])
        if data["last_ssn"]:
            args.append("--last_ssn")
            args.append(data["last_ssn"])
        if data["employee_identifier"]:
            args.append("--employee_identifier")
            args.append(data["employee_identifier"])
        if data["dest_person_email"]:
            args.append("--dest_person_email")
            args.append(data["dest_person_email"])
        if data["delete_src_person"]:
            args.append("--delete_src_person")
            args.append(data["delete_src_person"])
        if data["dryrunoff"]:
            args.append("--dryrunoff")
            args.append(data["dryrunoff"])
        return args, {"user": user}


class CleanupTestAccounts(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        with_dry_run_option = True
        email_contains = forms.CharField(required=True)

    def get_command_arguments(self, data, user):
        args = ["-email_contains", data["email_contains"]]
        return args, {"user": user}


class GetAuth0UserData(FireflyAdminCommand):
    class form(forms.Form):
        user_ids = forms.CharField(label="Comma separated user ids", required=True)

    def get_command_arguments(self, data, user):
        args = ["--user_ids", data["user_ids"]]
        return args, {"user": user}


class BackfillEmployeeIdForCoveredMembers(FireflyAdminCommand):
    class form(forms.Form):
        debug = forms.BooleanField(initial=False, required=False)

    def get_command_arguments(self, data, user):
        args = []
        if data["debug"]:
            args += ["--debug", data["debug"]]
        return args, {"user": user}


class BackfillZusPatientIds(FireflyAdminCommand):
    class form(forms.Form):
        pass

    def get_command_arguments(self, data, user):
        return [], {"user": user}


class AddPatientGroupToUsers(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        user_pk = forms.IntegerField(required=False)
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []
        if data["user_pk"]:
            args.append("--user_pk")
            args.append(data["user_pk"])
        return args, {"user": user}


class BackfillPersonRaceEthnicity(FireflyAdminCommand):
    class form(FireflyAdminCommand.form):
        limit = forms.IntegerField(required=False)
        with_dry_run_option = True

    def get_command_arguments(self, data, user):
        args = []

        if data["limit"]:
            args.append("--limit")
            args.append(data["limit"])

        return args, {"user": user}
