from firefly.core.user.utils import disable_user
from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand


class Command(FireflyBaseCommand):
    help = (
        "Disable a user account to prevent them from logging in with Auth0 or from receiving any Firefly communication."
    )

    def add_arguments(self, parser):
        parser.add_argument("-id", dest="id", type=int, required=True)
        parser.add_argument("-email", dest="email", type=str, required=True)

    def run(self, *args, **options):
        disable_user(options["id"], options["email"])
