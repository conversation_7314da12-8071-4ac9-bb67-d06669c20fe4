import logging
from datetime import datetime, timedelta

from firefly.core.user.models import Person, User
from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand

logger = logging.getLogger(__name__)


class Command(FireflyBaseCommand):
    help = "Add temporary dummy users for underage full-benefit dependents"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dependent",
            help="Dependent's person id",
            type=int,
            required=False,
        )
        parser.add_argument(
            "--dryrunoff",
            help="Write to database",
            type=bool,
            required=False,
        )

    def run(self, *args, **options):
        dependent_person_id = options.pop("dependent", None)
        dry_run = not options.pop("dryrunoff", True)
        if dependent_person_id:
            dependents = Person.objects.filter(pk=dependent_person_id)
        else:
            # Get all dependents under 18 years old
            date_18_years_ago = datetime.now() - timedelta(days=365 * 18)
            dependents = Person.objects.filter(
                user__isnull=True,
                member_eligibility__isnull=False,
                employee_eligibility__isnull=True,
                dob__gt=date_18_years_ago,
            )
        for dependent in dependents.iterator():
            try:
                if hasattr(dependent, "user") and dependent.user:
                    logger.error("Error: Dependent %s already has a user", dependent.pk)
                first_name = dependent.first_name
                last_name = dependent.last_name
                phone_number = dependent.phone_number or ""
                email = dependent.email or f"firefly.dependent.{first_name}.{last_name}"
                user = User(
                    phone_number=phone_number,
                    email=email,
                    auth_id=email[:30],
                )
                if dry_run:
                    logger.info(
                        "User created for dependent %s with DOB %s:\nphone_number -> %s\nemail -> %s\n",
                        dependent.pk,
                        dependent.dob,
                        phone_number,
                        email,
                    )
                else:
                    user.save()
                    dependent.user = user
                    dependent.save()
            except Exception:
                logger.exception("Failed to create user for dependent %s", dependent.pk)
