from firefly.core.user.utils import enable_user
from firefly.modules.firefly_django.management.commands.base import FireflyBaseCommand


class Command(FireflyBaseCommand):
    help = (
        "Enable a disabled user account to allow them to log in with Auth0 and start receiving Firefly communications."
    )

    def add_arguments(self, parser):
        parser.add_argument("-id", dest="id", type=int, required=True)
        parser.add_argument("-email", dest="email", type=str, required=True)

    def run(self, *args, **options):
        enable_user(options["id"], options["email"])
