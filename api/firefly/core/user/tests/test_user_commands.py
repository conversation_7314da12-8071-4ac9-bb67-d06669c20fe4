import logging

from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core.management import call_command
from rest_framework.test import APIRequestFactory, force_authenticate

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.api.crud import User<PERSON><PERSON><PERSON>iew
from firefly.core.user.auth.signing import MockKMSTimestampSigner
from firefly.core.user.constants import (
    HEALTH_GUIDE_ROLE,
    MD_ROLE,
    NP_ROLE,
    PATIENT_GROUP_NAME,
    RN_ROLE,
    UserStatus,
)
from firefly.core.user.factories import PersonUserFactory, ProviderDetailFactory
from firefly.core.user.utils import get_default_primary_physician, get_person_status
from firefly.modules.appointment.constants import AppointmentStatus
from firefly.modules.appointment.factories import AppointmentFactory
from firefly.modules.care_teams.utils import set_primary_physician
from firefly.modules.cases.constants import CaseCategoryTitle
from firefly.modules.cases.factories import CaseCategoryFactory, CaseFactory
from firefly.modules.programs.constants import PrimaryCareProgramStatus, ProgramEnrollmentEvents
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, remove_person_from_program, update_program_enrollment
from firefly.modules.statemachines.coverage.constants import CoverageStates
from firefly.modules.tenants.factories import TenantFactory

logger = logging.getLogger(__name__)


class ProviderTenantTestCase(FireflyTestCase):
    def test_provider_tenant_association(self):
        provider = ProviderDetailFactory.create()
        tenant = TenantFactory.create()

        provider.tenant = tenant
        provider.save()
        self.assertEqual(provider.tenant_id, tenant.id)


class BackfillPersonPrimaryPhysicianFromCareTeam(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.provider_1 = ProviderDetailFactory.create()
        self.provider_2 = ProviderDetailFactory.create()
        self.provider_3 = ProviderDetailFactory.create()
        self.provider_4 = ProviderDetailFactory.create()
        self.provider_5 = ProviderDetailFactory.create()

        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_rn, _ = Group.objects.get_or_create(name=RN_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)

        group_md.user_set.add(self.provider_1.user)
        group_np.user_set.add(self.provider_2.user)
        group_hg.user_set.add(self.provider_3.user)
        group_np.user_set.add(self.provider_4.user)
        group_rn.user_set.add(self.provider_5.user)
        print("provider5", self.provider_5.user_id)

        payload = {
            "care_team": {
                "md": self.provider_1.user_id,
                "np": self.provider_2.user_id,
                "rn": self.provider_5.user_id,
                "health_guide": self.provider_3.user_id,
                "behavioral_health": None,
            }
        }
        url = "/user/{}/care-team/v2/"
        set_careteam = self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)

        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        set_primary_physician(self.patient.person, self.provider_4.physician)

    def get_member_by_group(self, care_team_members, group):
        for care_team_member in care_team_members:
            if care_team_member.get("group") == group:
                return care_team_member
        return None

    def test_backfill(self):
        url = "/user/{}/care-team/v2/"
        # Create a new person without care team
        person = PersonUserFactory()
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)
        call_command(
            "backfill_person_primary_physician_from_care_team",
            user=self.provider,
            user_ids=str(person.user.id),
            dry_run_off=True,
        )

        # When there is no NP or MD in care team, primary physician should be default physician
        default_physician = get_default_primary_physician()
        self.assertEqual(
            person.primary_physician.id,
            default_physician,
        )

        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()

        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, NP_ROLE).get("id"),
            self.provider_2.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, RN_ROLE).get("id"),
            self.provider_5.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, HEALTH_GUIDE_ROLE).get("id"),
            self.provider_3.user.id,
        )

        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_4.user.id,
        )
        call_command(
            "backfill_person_primary_physician_from_care_team",
            user=self.provider,
            user_ids=str(self.patient.id),
            dry_run_off=True,
        )

        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()

        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, NP_ROLE).get("id"),
            self.provider_2.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, HEALTH_GUIDE_ROLE).get("id"),
            self.provider_3.user.id,
        )
        # When there is NP in care team, primary physician should be NP
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_2.user.id,
        )

        # Not setting NP in care team, to test update care team workflow
        payload = {
            "care_team": {
                "md": self.provider_1.user_id,
                "rn": self.provider_5.user_id,
                "health_guide": self.provider_3.user_id,
                "behavioral_health": None,
            }
        }
        set_careteam = self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)
        call_command(
            "backfill_person_primary_physician_from_care_team",
            user=self.provider,
            user_ids=str(self.patient.id),
            dry_run_off=True,
        )

        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()
        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertIsNone(self.get_member_by_group(care_team_members, NP_ROLE))
        # When there is no NP in care team, primary physician should be MD
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_1.user.id,
        )

        # Resetting primary physician to a different provider, to test backfill command
        set_primary_physician(self.patient.person, self.provider_4.physician)
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_4.user.id,
        )
        call_command(
            "backfill_person_primary_physician_from_care_team",
            user=self.provider,
            user_ids=str(self.patient.id),
            dry_run_off=True,
        )
        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()

        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertIsNone(self.get_member_by_group(care_team_members, NP_ROLE))
        # When there is no NP in care team, primary physician should be MD
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_1.user.id,
        )

    def test_dryrun(self):
        url = "/user/{}/care-team/v2/"
        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()

        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, NP_ROLE).get("id"),
            self.provider_2.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, HEALTH_GUIDE_ROLE).get("id"),
            self.provider_3.user.id,
        )
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_4.user.id,
        )
        call_command(
            "backfill_person_primary_physician_from_care_team",
            user=self.provider,
            user_ids=str(self.patient.id),
            dry_run_off=False,
        )

        response = self.client.get(url.format(self.patient.id))
        self.assertEqual(response.status_code, 200)
        care_team_members = response.json()

        self.assertEqual(
            self.get_member_by_group(care_team_members, MD_ROLE).get("id"),
            self.provider_1.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, NP_ROLE).get("id"),
            self.provider_2.user.id,
        )
        self.assertEqual(
            self.get_member_by_group(care_team_members, HEALTH_GUIDE_ROLE).get("id"),
            self.provider_3.user.id,
        )
        self.assertEqual(
            self.patient.person.primary_physician.provider.user.id,
            self.provider_4.user.id,
        )


class ConsentFormTestCase(FireflyTestCase):
    def test_consent_form(self):
        # This test case is not provided in the original file or the code block
        # It's assumed to exist as it's called in the original file
        pass


mock_kmst_timestamp_signer = MockKMSTimestampSigner()


class UserListViewTestCase(FireflyTestCase):
    def test_num_queries(self):
        for _ in range(3):
            ProviderDetailFactory(tenant=self.tenant)

        # Normally, we'd use self.provider_client.get(...) to test this, going through the client
        # issues some additional queries unrelated to data fetching. To isolate just the queries we
        # care about, we access the view directly.
        view = UserListView.as_view()
        factory = APIRequestFactory()
        request = factory.get("/user/?type=provider", format="json")
        force_authenticate(request, user=self.provider)

        # Ensure cache is cleared for test isolation.
        ContentType.objects.clear_cache()

        # For N providers
        # 3 calls to fetch permissions of requesting user
        # 1 call to get user data for all providers (has joins to other tables)
        # 1 call to find groups for all providers
        # 1 call to user_assigneegroupuser for all providers
        # 1 call to device_userdevice for all providers
        # 1 call to signup_reasons for all providers
        # 1 call to physicians_practicing_states for all providers
        # 1 call to care pods
        # Total = 10
        with self.assertNumQueries(10):
            response = view(request)
            response.render()

            self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")
            self.assertEqual(response.status_code, 200, f"Response was: {response.getvalue()}")


class PatientStatusTestCase(FireflyTestCase):
    def test_get_person_status(self):
        user = PersonUserFactory.create().user
        user.person.account_verified = True
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        # test No care for discharge onboarding state
        update_program_enrollment(ProgramCodes.PRIMARY_CARE, self.patient.person, ProgramEnrollmentEvents.DISCHARGED)
        enrollment = ProgramEnrollment.objects.get(
            program__uid=ProgramCodes.PRIMARY_CARE, person=self.patient.person, status=PrimaryCareProgramStatus.CHURNED
        )
        enrollment.reason = "discharged"
        enrollment.save()
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NO_CARE)
        # test No care for churned onboarding state
        update_program_enrollment(
            ProgramCodes.PRIMARY_CARE, self.patient.person, ProgramEnrollmentEvents.PROGRAM_UNENROLLED
        )
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NO_CARE)

        # test No care for uncovered insurance
        user.person.coverage = CoverageStates.INCOMPLETE_EXPIRED
        user.person.save()
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NO_CARE)

        user.person.coverage = CoverageStates.COVERED_INSURANCE
        user.person.save()
        # test No care when primary care program not enrolled
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NO_CARE)
        # test Not established status
        # For care only patient
        add_person_to_program(person=user.person, program_uid=ProgramCodes.PRIMARY_CARE)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NOT_ESTABLISHED)
        # For both care + coverage patient
        add_person_to_program(person=user.person, program_uid=ProgramCodes.BENEFIT)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NOT_ESTABLISHED)
        # test Established status
        # For both care + coverage patient
        AppointmentFactory(patient=user, status=AppointmentStatus.CHECKED_OUT.value)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.ESTABLISHED)
        # For care only patient
        remove_person_from_program(person=user.person, program_uid=ProgramCodes.BENEFIT)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.ESTABLISHED)
        # test No care status for Coverage only patient
        remove_person_from_program(person=user.person, program_uid=ProgramCodes.PRIMARY_CARE)
        add_person_to_program(person=user.person, program_uid=ProgramCodes.BENEFIT)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.NO_CARE)
        # test Escalating_Behavior status
        category = CaseCategoryFactory(title=CaseCategoryTitle.ESCALATING_BEHAVIOR)
        CaseFactory(person=user.person, category=category)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.ESCALATING_BEHAVIOR)
        # test fast_icu status
        category = CaseCategoryFactory(title=CaseCategoryTitle.FAST_ICU)
        CaseFactory(person=user.person, category=category)
        status = get_person_status(user)
        self.assertEqual(status, UserStatus.FAST_ICU)


class AddGroupToUsersTestCase(FireflyTestCase):
    def test_patient_group_to_users(self):
        person1 = PersonUserFactory()
        person2 = PersonUserFactory()
        person2.user.phone_number = None
        person2.user.save()
        call_command(
            "add_patient_group_to_users",
            user=self.provider,
            dry_run_off=False,
        )
        self.assertFalse(person1.user.groups.filter(name=PATIENT_GROUP_NAME).exists())
        self.assertFalse(person2.user.groups.filter(name=PATIENT_GROUP_NAME).exists())
        call_command(
            "add_patient_group_to_users",
            user=self.provider,
            dry_run_off=True,
        )
        self.assertTrue(person1.user.groups.filter(name=PATIENT_GROUP_NAME).exists())
        self.assertFalse(person2.user.groups.filter(name=PATIENT_GROUP_NAME).exists())
