import datetime
import logging
from unittest import mock
from unittest.mock import patch

from django.contrib.auth.models import Group
from dramatiq.rate_limits.backends import StubBackend

from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.api.utils import return_astrick_email
from firefly.core.user.constants import MEMBER_GUIDE_COVERAGE_ROLE, DuplicateMatchingThresholds
from firefly.core.user.factories import PersonFactory
from firefly.core.user.matching import find_matches, get_matching_person
from firefly.core.user.models import Person, User
from firefly.core.user.types import MatchedPersonMetaData
from firefly.core.user.utils import clean_member_id
from firefly.modules.eligibility.factories import MemberDataFactory
from firefly.modules.insurance.models import InsuranceMemberInfo
from firefly.modules.tasks.models import TaskCollection, TaskCollectionTask

logger = logging.getLogger(__name__)


@mock.patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number", return_value="1112224444")
class DuplicateMatchingTestCase(FireflyTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        # Import here to avoid circular imports
        from firefly.core.tests.utils import reset_context_to_luci_user

        # Set the context to luci bot user for database operations
        reset_context_to_luci_user()

        # Ensure JPMC contract is configured/cached
        cls.contract
        insurance_info_1 = InsuranceMemberInfo.objects.create(member_id="FF10000001-00")
        cls.existing = Person.objects.create(
            first_name="Peter",
            last_name="Parker",
            dob="1990-01-01",
            phone_number="**********",
            insurance_info=insurance_info_1,
        )
        cls.existing.ssn = "123121234"
        cls.existing.save()
        insurance_info_2 = InsuranceMemberInfo.objects.create(member_id="FF10000002-00")
        cls.existing_with_email = Person.objects.create(
            first_name="Peter",
            last_name="Parker",
            dob="1990-02-02",
            email="<EMAIL>",
            insurance_info=insurance_info_2,
        )
        cls.existing_with_email.ssn = "*********"
        cls.existing_with_email.save()
        # self.parser = FakeDataFeedParser()
        # EmployerGroup.objects.create(location_id="FF101-1")
        task_collection, _ = TaskCollection.objects.get_or_create(title="Duplicate Account in Coverage")
        np_group, _ = Group.objects.get_or_create(name=MEMBER_GUIDE_COVERAGE_ROLE)
        TaskCollectionTask.objects.create(
            title="Check Duplicate Account in Coverage",
            task_collection=task_collection,
            assignee_role=np_group,
            assign_to_patient=False,
        )

    def test_find_matches_with_phone(self, mock_phone_number):
        # Change first name
        matches = find_matches(full_name="Pete Parker", dob="1990-01-01", phone_number="**********")
        self._assert_matched_to_existing_person(matches)
        # Change last name
        matches = find_matches(full_name="Peter Porker", dob="1990-01-01", phone_number="**********")
        self._assert_did_not_match(matches)
        # Change first name more
        matches = find_matches(full_name="Pierre Porker", dob="1990-01-01", phone_number="**********")
        self._assert_did_not_match(matches)
        # Change last name more
        matches = find_matches(full_name="Peter Park", dob="1990-01-01", phone_number="**********")
        self._assert_did_not_match(matches)
        # Change full name
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="**********")
        self._assert_did_not_match(matches)
        # Change name case
        matches = find_matches(full_name="peter parker", dob="1990-01-01", phone_number="**********")
        self._assert_matched_to_existing_person(matches)
        # Change DOB
        matches = find_matches(full_name="Peter Parker", dob="1990-02-01", phone_number="**********")
        self.assertFalse(matches.exists())
        # Change phone number
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", phone_number="1234567891")
        self._assert_did_not_match(matches)
        # Change phone number and first name
        matches = find_matches(full_name="Pete Parker", dob="1990-01-01", phone_number="1234567891")
        self._assert_did_not_match(matches)
        # Change phone number and last name
        matches = find_matches(full_name="Peter Porker", dob="1990-01-01", phone_number="1234567891")
        self._assert_did_not_match(matches)
        # Change phone number and full name
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="1234567891")
        self._assert_did_not_match(matches)

    def test_find_matches_with_phone_and_ssn(self, mock_phone_number):
        # Change first name
        matches = find_matches(full_name="Pete Parker", dob="1990-01-01", phone_number="**********", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change last name
        matches = find_matches(full_name="Peter Porker", dob="1990-01-01", phone_number="**********", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change first name more
        matches = find_matches(full_name="Pierre Porker", dob="1990-01-01", phone_number="**********", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change last name more
        matches = find_matches(full_name="Peter Park", dob="1990-01-01", phone_number="**********", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change full name
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="**********", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change DOB
        matches = find_matches(full_name="Peter Parker", dob="1990-02-01", phone_number="**********", last_ssn="1234")
        self.assertFalse(matches.exists())
        # Change phone number
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", phone_number="1234567891", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change SSN
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", phone_number="1234567891", last_ssn="1235")
        self._assert_did_not_match(matches)
        # Change phone number and first name
        matches = find_matches(full_name="Pete Parker", dob="1990-01-01", phone_number="1234567891", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change phone number and last name
        matches = find_matches(full_name="Peter Porker", dob="1990-01-01", phone_number="1234567891", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change phone number and full name
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="1234567891", last_ssn="1234")
        self._assert_matched_to_existing_person(matches)
        # Change phone number and SSN
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", phone_number="1234567891", last_ssn="1235")
        self._assert_did_not_match(matches)
        # Change full name and SSN
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="**********", last_ssn="1235")
        self._assert_did_not_match(matches)
        # Change phone number, full name and SSN
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", phone_number="1234567891", last_ssn="1235")
        self._assert_did_not_match(matches)

    def test_find_matches_with_phone_and_member_id(self, mock_phone_number):
        # Change first name
        matches = find_matches(
            full_name="Pete Parker",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change last name
        matches = find_matches(
            full_name="Peter Porker",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change first name more
        matches = find_matches(
            full_name="Pierre Porker",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change last name more
        matches = find_matches(
            full_name="Peter Park",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change full name
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change DOB
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-02-01",
            phone_number="**********",
            member_id="FF10000001-00",
        )
        self.assertFalse(matches.exists())
        # Change phone number
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change member ID
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number and first name
        matches = find_matches(
            full_name="Pete Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and last name
        matches = find_matches(
            full_name="Peter Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and full name
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and member ID
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change full name and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number, full name and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)

    def test_find_matches_with_ssn_and_member_id(self, mock_phone_number):
        # Change first name
        matches = find_matches(full_name="Pete Parker", dob="1990-01-01", last_ssn="1234", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches)
        # Change last name
        matches = find_matches(full_name="Peter Porker", dob="1990-01-01", last_ssn="1234", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches)
        # Change first name more
        matches = find_matches(full_name="Pierre Porker", dob="1990-01-01", last_ssn="1234", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches)
        # Change last name more
        matches = find_matches(full_name="Peter Park", dob="1990-01-01", last_ssn="1234", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches)
        # Change full name
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", last_ssn="1234", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches)
        # Change DOB
        matches = find_matches(full_name="Peter Parker", dob="1990-02-01", last_ssn="1234", member_id="FF10000001-00")
        self.assertFalse(matches.exists())
        # Change SSN
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", last_ssn="1235", member_id="FF10000001-00")
        self._assert_did_not_match(matches)
        # Change member ID
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", last_ssn="1234", member_id="FF10000011-00")
        self._assert_did_not_match(matches)
        # Change full name and SSN
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", last_ssn="1235", member_id="FF10000001-00")
        self._assert_did_not_match(matches)
        # Change full name and member ID
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", last_ssn="1234", member_id="FF10000011-00")
        self._assert_did_not_match(matches)
        # Change SSN and member ID
        matches = find_matches(full_name="Peter Parker", dob="1990-01-01", last_ssn="1235", member_id="FF10000011-00")
        self._assert_did_not_match(matches)
        # Change SSN, full name and member ID
        matches = find_matches(full_name="Pete Porker", dob="1990-01-01", last_ssn="1235", member_id="FF10000011-00")
        self._assert_did_not_match(matches)

    def test_find_matches_with_phone_and_ssn_and_member_id(self, mock_phone_number):
        # Change first name
        matches = find_matches(
            full_name="Pete Parker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change last name
        matches = find_matches(
            full_name="Peter Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change first name more
        matches = find_matches(
            full_name="Pierre Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change last name more
        matches = find_matches(
            full_name="Peter Park",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change full name
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change DOB
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-02-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self.assertFalse(matches.exists())
        # Change phone number
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change SSN
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1235",
            member_id="FF10000001-00",
        )
        self._assert_did_not_match(matches)
        # Change member ID
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number and first name
        matches = find_matches(
            full_name="Pete Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and last name
        matches = find_matches(
            full_name="Peter Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and full name
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000001-00",
        )
        self._assert_matched_to_existing_person(matches)
        # Change phone number and member ID
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number and SSN
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1235",
            member_id="FF10000001-00",
        )
        self._assert_did_not_match(matches)
        # Change full name and SSN
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1235",
            member_id="FF10000001-00",
        )
        self._assert_did_not_match(matches)
        # Change full name and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change SSN and member ID
        matches = find_matches(
            full_name="Peter Parker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1235",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change SSN, full name and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1235",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number, full name and SSN
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1235",
            member_id="FF10000001-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number, full name and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)
        # Change phone number, full name, SSN and member ID
        matches = find_matches(
            full_name="Pete Porker",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1235",
            member_id="FF10000011-00",
        )
        self._assert_did_not_match(matches)

    def test_phone_number_regex(self, mock_phone_number):
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Peter",
            last_name="Parker",
            dob="1990-01-01",
            phone_number="(*************",
            exact_match=True,
        )
        self.assertEqual(result["person"], self.existing)

    def test_get_exact_match(self, mock_phone_number):
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Peter",
            last_name="Parker",
            dob="1990-01-01",
            phone_number="**********",
            exact_match=True,
        )
        self.assertEqual(result["person"], self.existing)
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Pete",
            last_name="Parker",
            dob="1990-01-01",
            phone_number="**********",
            exact_match=True,
        )
        self.assertIsNone(result["person"])

    def test_get_match_before_confirmation(self, mock_phone_number):
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Pete",
            last_name="Parker",
            dob="1990-01-01",
            phone_number="**********",
            exact_match=False,
        )
        self.assertEqual(result["person"], self.existing)

    def test_no_match_before_confirmation(self, mock_phone_number):
        # This shouldn't create a new Person because match score is high enough that we need to
        # confirm first with SSN/member ID
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Pete",
            last_name="Porker",
            dob="1990-01-01",
            phone_number="**********",
            exact_match=False,
        )
        self.assertIsNone(result["person"])
        # This should create a new Person because match score is too low
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Norman",
            last_name="Osborne",
            dob="1990-01-01",
            phone_number="1234567891",
            exact_match=False,
        )
        self.assertIsNotNone(result["person"])
        self.assertNotEqual(result["person"], self.existing)

    def test_get_match_with_confirmation(self, mock_phone_number):
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Pete",
            last_name="Porker",
            dob="1990-01-01",
            phone_number="**********",
            last_ssn="1234",
            exact_match=False,
        )
        self.assertEqual(result["person"], self.existing)

    def test_no_match_with_confirmation(self, mock_phone_number):
        # This should create a new Person because match score is low and we've already confirmed SSN
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Norman",
            last_name="Osborne",
            dob="1990-01-01",
            phone_number="1234567891",
            last_ssn="1234",
            exact_match=False,
        )
        self.assertIsNotNone(result["person"])
        self.assertNotEqual(result["person"], self.existing)

    def test_no_match_with_confirmation_and_duplicate_task_creation(self, mock_phone_number):
        # This should create a new Person because match score is low and we've already
        # confirmed SSN and create task for duplicate user
        result: MatchedPersonMetaData = get_matching_person(
            last_ssn="1234",
            exact_match=False,
            first_name="PeterSon",
            last_name="son",
            dob="1990-01-01",
            phone_number="1231233881",
        )
        self.assertTrue(result["create_case"])
        self.assertIsNotNone(result["person"])
        self.assertNotEqual(result["person"], self.existing)
        self.assertIsNotNone(result["duplicate_person"])

    def test_signup_matching_with_email(self, mock_phone_number):
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Pete",
            last_name="Parker",
            dob="1990-02-02",
            email="<EMAIL>",
            exact_match=False,
        )
        self.assertEqual(result["person"], self.existing_with_email)

    def test_signup_not_matching_with_email(self, mock_phone_number):
        # This should create a new Person because match score is too low
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Norman",
            last_name="Osborne",
            dob="1990-02-02",
            email="<EMAIL>",
            exact_match=False,
        )
        self.assertIsNotNone(result["person"])
        self.assertNotEqual(result["person"], self.existing_with_email)

    def test_real_examples_without_alternatives(self, mock_phone_number):
        person, last_ssn = self._create_person(first_name="Jules", last_name="Knight", phone_number=None)
        self._assert_matched_to_real_person(
            person=person,
            phone_number="7982310923",
            last_ssn=last_ssn,
        )
        person, last_ssn = self._create_person(first_name="Ralph", last_name="O'Connell")
        self._assert_matched_to_real_person(person=person, last_name="OConnell", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Annie", last_name="Beth")
        self._assert_matched_to_real_person(person=person, first_name="annie", last_name="beth", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Steven", last_name="Zelda")
        self._assert_matched_to_real_person(person=person, first_name="Steve", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Isabelle", last_name="Beth")
        self._assert_matched_to_real_person(person=person, first_name="Izzy", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Rita", last_name="Knicks Muggle")
        self._assert_matched_to_real_person(person=person, last_name="Muggle", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Christopher", last_name="Jonah")
        self._assert_matched_to_real_person(person=person, first_name="Chris", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Geoffrey", last_name="Genome")
        self._assert_matched_to_real_person(person=person, first_name="Geoff", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Jonathan", last_name="Beth")
        self._assert_matched_to_real_person(person=person, first_name="John", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Cathryn", last_name="Robin-Batman")
        self._assert_matched_to_real_person(person=person, last_name="Robin", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Fran", last_name="Rothberg Beth")
        self._assert_matched_to_real_person(person=person, last_name="Rothberg", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Molly", last_name="Land", phone_number="3427598721")
        self._assert_matched_to_real_person(person=person, phone_number="3427590981", last_ssn=last_ssn)

    def test_real_examples_with_alternatives(self, mock_phone_number):
        person, last_ssn = self._create_person(first_name="Jules", last_name="Knight", phone_number=None)
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(
            person=person,
            phone_number="7982310923",
            last_ssn=last_ssn,
        )
        self._assert_created_new_person(
            person=person,
            phone_number="7982310923",
            last_ssn=last_ssn,
        )
        person, last_ssn = self._create_person(first_name="Ralph", last_name="O'Connell")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, last_name="Connell", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, last_name="Connell", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Annie", last_name="Beth")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="annie", last_name="beth", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="annie", last_name="beth", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Steven", last_name="Zelda")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="Steve", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="Steve", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Isabelle", last_name="Beth")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="Izzy", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="Izzy", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Rita", last_name="Knicks Muggle")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, last_name="Muggle", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, last_name="Muggle", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Christopher", last_name="Jonah")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="Chris", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="Chris", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Geoffrey", last_name="Genome")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="Geoff", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="Geoff", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Jonathan", last_name="Beth")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, first_name="John", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, first_name="John", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Cathryn", last_name="Robin-Batman")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, last_name="Robin", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, last_name="Robin", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Fran", last_name="Rothberg Beth")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, last_name="Rothberg", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, last_name="Rothberg", last_ssn=last_ssn)
        person, last_ssn = self._create_person(first_name="Molly", last_name="Land", phone_number="3427598721")
        self._create_alternatives_for_person(person, last_ssn)
        self._assert_matched_to_real_person(person=person, phone_number="3427590981", last_ssn=last_ssn)
        self._assert_created_new_person(person=person, phone_number="3427590981", last_ssn=last_ssn)

    def test_eligibility_to_signup_with_incorrect_info(self, mock_phone_number):
        # Create new user from eligibility file
        # Sign up again as user with completely incorrect info
        # Should create brand new user
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        signup_person = self._create_person_using_member_id_confirmation(
            data=data,
            first_name="Harrison",
            last_name="Ford",
            phone_number="0001112222",
            member_id="J463256",
        )
        self.assertEqual(signup_person.first_name, "Harrison")
        self.assertEqual(signup_person.last_name, "Ford")
        self.assertNotEqual(eligibility_person, signup_person)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_correct_info(self, mock_mutex, mock_phone_number):
        # Create new user from eligibility file
        # Sign up again as user with exactly correct info
        # Should linked to eligibility_person
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        signup_person = self._create_person_using_member_id_confirmation(
            data, member_id=eligibility_person.insurance_info.member_id
        )
        self.assertEqual(signup_person.first_name, data.first_name)
        self.assertEqual(signup_person.last_name, data.last_name)
        self._assert_duplicate_person(signup_person, signup_person.email)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_almost_correct_info_different_member_id(self, mock_mutex, mock_phone_number):
        # Create new user from eligibility file (with no alternatives)
        # Sign up again as user with almost correct info
        # Go through confirmation screen and add different member ID
        # Should create brand new user
        # while signing up again with same details, duplicated account should be detected
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        signup_person = self._create_person_using_member_id_confirmation(
            data, first_name="Indy", last_name="Jones", member_id=self._get_member_id(data)
        )
        self.assertEqual(signup_person.first_name, "Indy")
        self.assertEqual(signup_person.last_name, "Jones")
        self.assertIsNone(eligibility_person.deleted)
        self._assert_duplicate_person(signup_person, signup_person.email)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_almost_correct_info_correct_member_id(self, mutex_mock, mock_phone_number):
        # Create new user from eligibility file (with no alternatives)
        # Sign up again as user with almost correct info
        # Go through confirmation screen and add correct member ID
        # Should link with existing user
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        signup_person = self._create_person_using_member_id_confirmation(
            data, first_name="Indy", last_name="Jones", member_id=eligibility_person.insurance_info.member_id
        )
        self.assertEqual(signup_person.first_name, "Indy")
        self.assertEqual(signup_person.last_name, "Jones")
        # Try sign up as same person again, duplicate account will be detected.
        self._assert_duplicate_person(signup_person, signup_person.email)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_almost_correct_info_different_ssn_with_alts(
        self, mock_mutex, mock_phone_number
    ):
        # Create new user from eligibility file
        # Create 3 alternative users
        # Sign up again as user with almost correct info
        # Go through confirmation screen and add different SSN
        # Should create brand new user
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        self._create_alternatives_for_person(eligibility_person, data.ssn[-4:])
        self._create_alternatives(
            first_name="Indiana",
            last_name="Jones",
            dob=data.dob,
            phone_number=data.phone,
            last_ssn=data.ssn[-4:],
        )
        signup_person = self._create_person_using_member_id_confirmation(
            data,
            first_name="Indy",
            last_name="Jones",
            member_id=eligibility_person.insurance_info.member_id,
        )
        self.assertEqual(signup_person.first_name, "Indy")
        self.assertEqual(signup_person.last_name, "Jones")
        self.assertIsNone(eligibility_person.deleted)
        self._assert_duplicate_person(signup_person, signup_person.email)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_almost_correct_info_different_member_id_with_alts(
        self, mock_mutex, mock_phone_number
    ):
        # Create new user from eligibility file
        # Create 3 alternative users
        # Sign up again as user with almost correct info
        # Go through confirmation screen and add different member ID
        # Should create brand new user
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        self._create_alternatives_for_person(eligibility_person, data.ssn[-4:])
        self._create_alternatives(
            first_name="Indiana",
            last_name="Jones",
            dob=data.dob,
            phone_number=data.phone,
            last_ssn=data.ssn[-4:],
        )
        eligibility_person = Person.objects.get(email=data.email)
        signup_person = self._create_person_using_member_id_confirmation(
            data, first_name="Indy", last_name="Jones", member_id=self._get_member_id(data)
        )
        self.assertEqual(signup_person.first_name, "Indy")
        self.assertEqual(signup_person.last_name, "Jones")
        self.assertIsNone(eligibility_person.deleted)
        self._assert_duplicate_person(signup_person, signup_person.email)

    @patch("firefly.core.services.dramatiq.utils.get_backend_for_mutex", return_value=StubBackend())
    def test_eligibility_to_signup_with_almost_correct_info_correct_member_id_with_alts(
        self, mock_mutex, mock_phone_number
    ):
        # Create new user from eligibility file
        # Create 3 alternative users
        # Sign up again as user with almost correct info
        # Go through confirmation screen and add correct member ID
        # Should merge with existing user
        data = self._create_person_from_eligibility_file(first_name="Indiana", last_name="Jones")
        eligibility_person = Person.objects.get(email=data.email)
        mock_phone_number.return_value = eligibility_person.phone_number
        self._create_alternatives_for_person(eligibility_person, data.ssn[-4:])
        self._create_alternatives(
            first_name="Indiana",
            last_name="Jones",
            dob=data.dob,
            phone_number=data.phone,
            last_ssn=data.ssn[-4:],
        )
        signup_person = self._create_person_using_member_id_confirmation(
            data,
            first_name="India",
            last_name="Jones",
            dob=eligibility_person.dob,
            member_id=eligibility_person.insurance_info.member_id,
        )
        self.assertEqual(signup_person.first_name, "India")
        self.assertEqual(signup_person.last_name, "Jones")
        self.assertEqual(eligibility_person, signup_person)
        self._assert_duplicate_person(signup_person, signup_person.email)

    def test_signup_to_eligibility_with_incorrect_info(self, mock_phone_number):
        # Create new user from signup
        # Create new user from eligibility file with completely incorrect info
        # Should create brand new user
        signup_data = MemberDataFactory(first_name="Indiana", last_name="Jones")
        signup_person = self._create_person_using_member_id_confirmation(data=signup_data, member_id="J326456")
        eligibility_data = self._create_person_from_eligibility_file(first_name="Harrison", last_name="Ford")
        eligibility_person = Person.objects.get(email=eligibility_data.email)
        self.assertNotEqual(eligibility_person, signup_person)

    def test_signup_to_eligibility_with_almost_correct_info(self, mock_phone_number):
        # Create new user from signup
        # Create new user from eligibility file with almost correct info
        # Should create brand new user
        signup_data = MemberDataFactory(first_name="Indiana", last_name="Jones")
        signup_person = self._create_person_using_member_id_confirmation(data=signup_data, member_id="J2154353")
        eligibility_data = self._create_person_from_eligibility_file(first_name="Indy", last_name="Jones")
        eligibility_person = Person.objects.get(email=eligibility_data.email)
        self.assertNotEqual(eligibility_person, signup_person)

    def _create_person_using_member_id_confirmation(
        self,
        data=None,
        user_dict=None,
        user=None,
        first_name=None,
        last_name=None,
        dob=None,
        phone_number=None,
        member_id=None,
        duplicate=False,
    ):
        user_dict = user_dict or {}
        if user is None:
            user = User.objects.create(email=f"{first_name}.{last_name}@example.com")
        payload = {
            "first_name": first_name or user_dict.get("first_name") or data.first_name,
            "last_name": last_name or user_dict.get("last_name") or data.last_name,
            "preferred_name": "Rose",
            "phone_number": phone_number or user_dict.get("phone_number"),
            "sex": "Male",
            "gender": ["Male"],
            "pronouns": "He/Him/His",
            "dob": dob or user_dict.get("dob") or data.dob,
            "created_from": "app",
            "patient_referral_program": "",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        client = FireflyTestCase.get_member_client(member=user)
        response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(response.status_code, 200)
        if duplicate:
            return response
        else:
            person = Person.objects.get(user=user)
            return person

    def _assert_duplicate_person(self, data, email, first_name=None, last_name=None, phone_number=None):
        obs_email = return_astrick_email(email=email)
        dup_response = self._create_person_using_member_id_confirmation(
            data=data,
            first_name=first_name or data.first_name,
            last_name=last_name or data.last_name,
            member_id=data.insurance_info.member_id,
            phone_number=phone_number,
            duplicate=True,
        )
        self.assertEqual(dup_response.json()["email"], obs_email)

    def _merge_person(self, user, ssn=None, member_id=None):
        client = FireflyTestCase.get_member_client(member=user)
        req = {}
        if ssn:
            req.update({"last_ssn": ssn[-4:] if isinstance(ssn, str) else str(ssn)})
        if member_id:
            req.update({"member_id": member_id})
        response = client.post("/user/me/merge_person/", req, format="json")
        return response

    def _create_person(self, **details):
        person = PersonFactory(**details)
        # Use a more predictable SSN generation to avoid randomness overhead
        base_id = person.id if person.id else 1
        ssn = f"{123456789 + base_id:09d}"
        if details.get("employer", "Firefly Health") == "Firefly Health":
            person.ssn = ssn
            person.save()
        return person, ssn[-4:]

    def _create_alternatives_for_person(self, person, last_ssn):
        first_name = person.first_name
        last_name = person.last_name
        dob = person.dob
        phone_number = person.phone_number
        self._create_alternatives(first_name, last_name, dob, phone_number, last_ssn)

    def _create_alternatives(self, first_name, last_name, dob, phone_number, last_ssn):
        # Pre-generate unique SSNs to avoid while loops
        base_ssn = int(last_ssn) if last_ssn else 1234
        unique_ssns = [str(base_ssn + 1).zfill(4), str(base_ssn + 2).zfill(4), str(base_ssn + 3).zfill(4)]

        # Create person with same info but different last SSN
        alternative_1 = PersonFactory(first_name=first_name, last_name=last_name, dob=dob, phone_number=phone_number)
        alternative_1.ssn = f"12312{unique_ssns[0]}"
        alternative_1.save()

        # Create person with same name but different last SSN
        alternative_2 = PersonFactory(first_name=first_name, last_name=last_name)
        alternative_2.ssn = f"12312{unique_ssns[1]}"
        alternative_2.save()

        # Create person with same phone number and DOB but different last SSN
        alternative_3 = PersonFactory(dob=dob, phone_number=phone_number)
        alternative_3.ssn = f"12312{unique_ssns[2]}"
        alternative_3.save()

        # Create person with different info but same last SSN - use pre-generated values
        alternative_4 = PersonFactory(
            first_name="Alternative" if first_name != "Alternative" else "Different",
            last_name="Person" if last_name != "Person" else "Name",
            dob=datetime.datetime(1985, 6, 15).date()
            if dob != datetime.datetime(1985, 6, 15).date()
            else datetime.datetime(1986, 7, 16).date(),
            phone_number="5551234567" if phone_number != "5551234567" else "5559876543",
        )
        alternative_4.last_ssn = last_ssn
        alternative_4.save()

    def _assert_matched_to_real_person(
        self,
        person,
        last_ssn,
        first_name=None,
        last_name=None,
        dob=None,
        phone_number=None,
    ):
        first_name = first_name or person.first_name
        last_name = last_name or person.last_name
        dob = dob or person.dob
        phone_number = phone_number or person.phone_number
        result: MatchedPersonMetaData = get_matching_person(
            first_name=first_name,
            last_name=last_name,
            dob=dob,
            phone_number=phone_number,
            exact_match=False,
        )
        if not result["person"]:
            result: MatchedPersonMetaData = get_matching_person(
                first_name=first_name,
                last_name=last_name,
                dob=dob,
                phone_number=phone_number,
                last_ssn=last_ssn,
                exact_match=False,
            )
        self.assertEqual(result["person"], person)

    def _assert_created_new_person(
        self,
        person,
        last_ssn,
        first_name=None,
        last_name=None,
        dob=None,
        phone_number=None,
    ):
        first_name = first_name or person.first_name
        last_name = last_name or person.last_name
        dob = dob or person.dob
        phone_number = phone_number or person.phone_number
        # Generate a different SSN without using while loop
        base_ssn = int(last_ssn) if last_ssn else 1234
        ssn = str(base_ssn + 1000).zfill(4)  # Ensure it's different
        result: MatchedPersonMetaData = get_matching_person(
            first_name=first_name,
            last_name=last_name,
            dob=dob,
            phone_number=phone_number,
            exact_match=False,
        )
        if not result["person"]:
            result: MatchedPersonMetaData = get_matching_person(
                first_name=first_name,
                last_name=last_name,
                dob=dob,
                phone_number=phone_number,
                last_ssn=ssn,
                exact_match=False,
            )
        self.assertIsNotNone(result["person"])
        self.assertNotEqual(result["person"], person)

    def _assert_matched_to_person(self, matches, person):
        self.assertTrue(matches.exists())
        matching = matches.first()
        self.assertEqual(matching, person)
        self.assertGreaterEqual(matching.score, DuplicateMatchingThresholds.UPPER)

    def _assert_matched_to_existing_person(self, matches, existing=None):
        self._assert_matched_to_person(matches, self.existing if not existing else existing)

    def _assert_did_not_match(self, matches):
        self.assertTrue(matches.exists())
        matching = matches.first()
        self.assertLessEqual(matching.score, DuplicateMatchingThresholds.UPPER)

    def _get_last_ssn(self, data):
        # Generate a different SSN without while loop
        base_ssn = int(data.ssn[-4:]) if data.ssn else 1234
        return str(base_ssn + 1000).zfill(4)

    def _get_member_id(self, data):
        # Generate a different member ID without while loop
        base_id = (
            int(data.member_key.replace("FF101", "").replace("-00", ""))
            if hasattr(data, "member_key") and data.member_key
            else 10000
        )
        return f"FF101{str(base_id + 1000).zfill(5)}-00"

    def test_check_duplicate_with_exact_match(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        user_dict = {
            "first_name": "Jimmy",
            "last_name": "Carter",
            "preferred_name": "Jim",
            "phone_number": "1112221111",
            "sex": "Male",
            "gender": ["Man"],
            "pronouns": "He/Him/His",
            "dob": "1990-01-01",
            "insurance_member_info": {"state": "MA"},
        }
        signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, user=user, member_id="FF346456-00"
        )
        self.assertEqual(signup_person.first_name, user_dict.get("first_name"))
        self.assertEqual(signup_person.last_name, user_dict.get("last_name"))
        self._assert_duplicate_person(signup_person, signup_person.email)

    def test_check_duplicate_with_first_name_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        user_dict = {
            "first_name": "Jennifer",
            "last_name": "Aniston",
            "preferred_name": "Jeni",
            "phone_number": "1112223333",
            "sex": "Female",
            "gender": ["Female"],
            "dob": "1990-01-02",
            "insurance_member_info": {"state": "MA"},
        }
        signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, user=user, member_id="FF346456-00"
        )
        self.assertEqual(signup_person.first_name, user_dict.get("first_name"))
        self.assertEqual(signup_person.last_name, user_dict.get("last_name"))
        self._assert_duplicate_person(signup_person, signup_person.email, first_name="Jenni")

    def test_check_duplicate_with_last_name_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        user_dict = {
            "first_name": "Rose",
            "last_name": "Geller",
            "preferred_name": "Rose",
            "phone_number": "1112224444",
            "sex": "Male",
            "gender": ["Male"],
            "dob": "1990-01-03",
            "insurance_member_info": {"state": "MA"},
        }
        signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, user=user, member_id="FF346456-00"
        )
        self.assertEqual(signup_person.first_name, user_dict.get("first_name"))
        self.assertEqual(signup_person.last_name, user_dict.get("last_name"))
        self._assert_duplicate_person(signup_person, signup_person.email, last_name="Gelle")

    def test_check_duplicate_with_phone_number_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        mock_phone_number.return_value = "**********"
        user_dict = {
            "first_name": "Chandler",
            "last_name": "Bing",
            "preferred_name": "Chan",
            "phone_number": "**********",
            "sex": "Male",
            "gender": ["Male"],
            "dob": "1990-01-04",
            "insurance_member_info": {"state": "MA"},
        }
        signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, user=user, member_id="FF346456-00"
        )
        self.assertEqual(signup_person.first_name, user_dict.get("first_name"))
        self.assertEqual(signup_person.last_name, user_dict.get("last_name"))
        mock_phone_number.return_value = "**********"
        self._assert_duplicate_person(signup_person, signup_person.email, phone_number="**********")

    def test_check_duplicate_with_dob_altered(self, mock_phone_number):
        """
        when dob is different, new account has to be created
        """
        user = User.objects.create(email="<EMAIL>")
        user_dict = {
            "first_name": "phoebe",
            "last_name": "buffay",
            "preferred_name": "pho",
            "phone_number": "**********",
            "sex": "Female",
            "gender": ["Female"],
            "dob": "1994-01-08",
            "insurance_member_info": {"state": "MA"},
        }
        signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, user=user, member_id="FF346456-00"
        )
        duplicate_signup_person = self._create_person_using_member_id_confirmation(
            user_dict=user_dict, dob="1990-01-08", member_id="FF346452"
        )
        self.assertNotEqual(signup_person, duplicate_signup_person)

    def test_return_astrick_email(self, mock_phone_number):
        email_1 = "<EMAIL>"
        hashed_email_1 = "*@t**t.com"
        self.assertEqual(return_astrick_email(email_1), hashed_email_1)
        email_2 = "<EMAIL>"
        hashed_email_2 = "**@t**t.com"
        self.assertEqual(return_astrick_email(email_2), hashed_email_2)
        email_3 = "<EMAIL>"
        hashed_email_3 = "sa***@t**t.com"
        self.assertEqual(return_astrick_email(email_3), hashed_email_3)

    def test_get_matching_person_with_dob_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        person = Person.objects.create(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="9675342342",
        )
        person.user = user
        person.save()
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Boba",
            last_name="Fett",
            dob="1989-11-11",
            phone_number="9675342342",
            employee_identifier=None,
            exact_match=False,
            allow_with_user=True,
            create_new=False,
        )
        self.assertIsNone(result["person"])

    def test_get_matching_person_with_first_name_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        person = Person.objects.create(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="9675342342",
        )
        person.user = user
        person.email = user.email
        person.save()
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Bob",
            last_name="Fett",
            dob="1988-11-11",
            email=user.email,
            phone_number="9675342342",
            employee_identifier=None,
            exact_match=False,
            allow_with_user=True,
            create_new=False,
        )
        self.assertEqual(result["person"], person)

    def test_get_matching_person_with_last_name_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        person = Person.objects.create(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="9675342342",
        )
        person.user = user
        person.email = user.email
        person.save()
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Boba",
            last_name="Fetttt",
            dob="1988-11-11",
            phone_number="9675342342",
            email=user.email,
            employee_identifier=None,
            exact_match=False,
            allow_with_user=True,
            create_new=False,
        )
        self.assertEqual(result["person"], person)

    def test_get_matching_person_with_phonenumber_altered(self, mock_phone_number):
        user = User.objects.create(email="<EMAIL>")
        person = Person.objects.create(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="9675342342",
        )
        person.user = user
        person.email = user.email
        person.save()
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="9675342344",
            email=user.email,
            employee_identifier=None,
            exact_match=False,
            allow_with_user=True,
            create_new=False,
        )
        self.assertEqual(result["person"], person)

    def test_get_matching_person_without_phonenumber_and_email(self, mock_phone_number):
        # exact matching should happen only when unique identifier(insurance ID, SSN and emp id)
        # is present along with name and DOB
        person = Person.objects.create(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
        )
        person.save()
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number=None,
            email=None,
            employee_identifier=None,
            exact_match=False,
            allow_with_user=False,
            create_new=False,
        )
        self.assertEqual(result["person"], None)

        # exact match but phone number and email ID was given
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Boba",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="123456789",
            email="<EMAIL>",
            employee_identifier=None,
            exact_match=False,
            allow_with_user=False,
            create_new=False,
        )
        self.assertEqual(result["person"], None)

        # slight change in First name but phone number and email ID was given
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Bobo",
            last_name="Fett",
            dob="1988-11-11",
            phone_number="123456789",
            email="<EMAIL>",
            employee_identifier=None,
            exact_match=False,
            allow_with_user=False,
            create_new=False,
        )
        self.assertEqual(result["person"], None)

        # slight change in Last name but phone number and email ID was given
        result: MatchedPersonMetaData = get_matching_person(
            first_name="Bobo",
            last_name="Fetta",
            dob="1988-11-11",
            phone_number="123456789",
            email="<EMAIL>",
            employee_identifier=None,
            exact_match=False,
            allow_with_user=False,
            create_new=False,
        )
        self.assertEqual(result["person"], None)

    def test_match_without_dob(self, mock_phone_number):
        Person.objects.create(email="<EMAIL>", first_name=None, last_name=None, dob=None)
        result = get_matching_person(None, None, None, None, email="<EMAIL>")
        self.assertEqual(result["matched"], False)

    def test_find_matching_person_with_member_id(self, mock_phone_number):
        person = PersonFactory(first_name="Meriadoc", last_name="Brandybuck", dob="1981-01-01")
        self._test_member_id_permutations(person, "ff10000001-00")
        self._test_member_id_permutations(person, "ff10000001")
        self._test_member_id_permutations(person, "FF 1000/0001 00")
        self._test_member_id_permutations(person, "Ff 100-00001")
        self._test_member_id_permutations(person, "ff-10000-001 ")
        self._test_member_id_permutations(person, "  fF*10000001-00")
        self._test_member_id_permutations(person, "ff 10000 001 00")
        self._test_member_id_permutations(person, "Ff-10-00 0001 00")

    def _test_member_id_permutations(self, person, member_id):
        person.insurance_info.member_id = clean_member_id(member_id)
        person.insurance_info.save()
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF10000001-00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF10000001 00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF1000000100")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF10000001")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF 10000001-00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF 10000001 00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF 1000000100")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF 10000001")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-10000001-00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-10000001 00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-1000000100")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-10000001")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-1000 0001-00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-1000 0001 00")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-1000 000100")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="FF-1000 0001")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="f f/10 000001-00 ")
        self._assert_matched_to_existing_person(matches, person)
        matches = find_matches(full_name="Merry Brandybuck", dob="1981-01-01", member_id="  Ff 10-000 0 01-00")
        self._assert_matched_to_existing_person(matches, person)
