import datetime
import logging
import uuid
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest import mock
from unittest.mock import patch

from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core.management import call_command
from django.test import override_settings
from django.utils import timezone

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.feature.testutils import override_switch
from firefly.core.roles.factories import RoleFactory
from firefly.core.tests.client import FireflyTestAPIClient
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.constants import HEALTH_GUIDE_ROLE, MD_ROLE, NP_ROLE, RN_ROLE
from firefly.core.user.elation import (
    WAFFLE_SWITCH_SKIP_INSURANCE_SYNC_FROM_ELATION,
    ElationPersonSync,
)
from firefly.core.user.factories import Patient<PERSON>serFactory, PersonUserFactory, ProviderDetailFactory
from firefly.core.user.models import Person, Provider<PERSON>etail, Provider<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, User
from firefly.core.user.utils import (
    create_patient_pubnub_channel,
    get_default_caregiver_practice_obj,
    get_last_engagement_at,
    is_clinician,
)
from firefly.modules.appointment.models import Appointment
from firefly.modules.attribution.models import Attribution
from firefly.modules.care_teams.factories import CareTeamTemplateFactory
from firefly.modules.cases.constants import INSURANCE_PLAN_NEEDS_REVIEW
from firefly.modules.cases.models import CaseCategory
from firefly.modules.chat_message.models import ChatMessageV2, ChatThread
from firefly.modules.chat_message.utils import get_or_create_default_patient_thread
from firefly.modules.firefly_django.utils import get_lucian_bot_user
from firefly.modules.forms.models import Form, FormSubmission
from firefly.modules.insurance.factories import InsuranceMemberInfoFactory
from firefly.modules.insurance.models import InsuranceMemberInfo, InsurancePayer
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.physician.factories import PhysicianFactory
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER
from firefly.modules.programs.constants import PrimaryCareProgramStatus
from firefly.modules.programs.models import ProgramEnrollment
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program
from firefly.modules.states.models import State
from firefly.modules.tasks.models import Task
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.modules.tenants.factories import TenantFactory
from firefly.settings.base import LUCIAN_USER_TENANT_HEADER_TRANSFORMED

logger = logging.getLogger(__name__)


class UserMetadataTestCase(FireflyTestCase):
    def test_patch_insurance(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        response = self.client.patch("/user/me/insurance/", {"member_id": "test"}, format="json")
        self.assertEqual(response.status_code, 200)
        self.patient.person.insurance_info.refresh_from_db()

        self.assertEqual(self.patient.person.insurance_info.member_id, "test")

    def test_devices(self):
        """Verify that when devices are added - they show up in the /user/me endpoint"""
        device1 = {
            "device_unique_id": "TestDevice1",
            "device_type": "iPhone 6",
            "platform": "iOS",
            "permissions": {"alerts": "off", "notifications": "on"},
            "biometric_capability": "some biometric string",
            "font_scaling": "1.0",
            "color_scheme": "light",
            "device_timezone": "RandomTimeZone",
            "os_version": "2.4",
            "app_version": "0.0.0",
            "ota_provider": "expo",
            "ota_release_version": "0.0.0",
            "device_token": "RandomToken1",
            "device_push_enabled": "True",
        }
        device2 = {
            "device_unique_id": "TestDevice2",
            "device_type": "Android 10",
            "platform": "iOS",
            "permissions": {"alerts": "off", "notifications": "on"},
            "biometric_capability": "some biometric string",
            "font_scaling": "1.0",
            "color_scheme": "light",
            "device_timezone": "RandomTimeZone",
            "os_version": "2.4",
            "app_version": "0.0.0",
            "ota_provider": "expo",
            "ota_release_version": "0.0.0",
            "device_token": "RandomToken2",
            "device_push_enabled": "False",
        }
        response = self.client.post(
            "/device/",
            device1,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        response = self.client.post(
            "/device/",
            device2,
            format="json",
        )
        self.assertEqual(response.status_code, 201)
        response = self.client.get("/user/me/", format="json")
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.json().get("userdevices"))
        self.assertEqual(len(response.json().get("userdevices")), 2)
        self.assertEqual(
            [device1, device2].sort(key=lambda device: device.get("device_unique_id")),
            response.json().get("userdevices").sort(key=lambda device: device.get("device_unique_id")),
        )


class UserInsuranceSourceTypeCase(FireflyTestCase):
    def test_patch_insurance(self):
        add_person_to_program(self.patient.person, ProgramCodes.PRIMARY_CARE)
        firefly_payer, _ = InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        response = self.client.patch("/user/me/insurance/", {"source_type": "private"}, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertNotEqual(self.patient.person.insurance_info.insurance_plan, None)
        self.assertNotEqual(self.patient.person.insurance_info.insurance_payer.name, FIREFLY_PAYER)
        response = self.client.patch(
            "/user/me/insurance/", {"source_type": "private", "insurance_payer_id": firefly_payer.id}, format="json"
        )
        self.patient.person.insurance_info.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.patient.person.insurance_info.insurance_payer.name, FIREFLY_PAYER)
        self.assertIsNotNone(self.patient.person.insurance_info.insurance_plan)

    @patch("firefly.modules.onboarding.statemachine.side_effects.OnboardingGuideNotifier")
    def test_moving_to_signedup_from_churned_onboarding_state(self, mock_notifier):
        self.patient.onboarding_state.to_signedup(actor=self.patient)
        self.client.patch("/user/me/insurance/", {"source_type": "employer"}, format="json")
        self.patient.onboarding_state.refresh_from_db()
        self.assertEqual(self.patient.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.patient.onboarding_state.signup_complete()
        mock_notifier.new_signup.assert_called_with(self.patient)


class UserFormSubmissionCreationTestCase(FireflyTestCase):
    def test_form_submission_creation(self):
        patient_user = User.objects.create(
            phone_number="**********",
            first_name="Test",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()
        insurance_info = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer, member_id="00011", group_number="11234"
        )

        person = Person.objects.create(user=patient_user, insurance_info=insurance_info)
        person.user.save()


class UserUtilsTestCase(FireflyTestCase):
    def test_is_clinician(self):
        md_user = User.objects.create(
            phone_number="**********",
            first_name="Md",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        md_group = Group.objects.get(name=MD_ROLE)
        md_user.groups.add(md_group)

        onboarding_guide_user = User.objects.create(
            phone_number="**********",
            first_name="Onboardingguide",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        onboarding_group = Group.objects.get(name="Provider")
        onboarding_guide_user.groups.add(onboarding_group)

        self.assertTrue(is_clinician(md_user))
        self.assertFalse(is_clinician(onboarding_guide_user))

    @override_settings(ENABLE_PUBNUB_CHANNEL_CREATION=True)
    @patch("firefly.core.user.utils.PubNubClient")
    def test_create_patient_pubnub_channel(self, mock_pubnub_client):
        mock_pubnub_client.return_value.get_channel_for_user.return_value = "foo_channel"
        # get_channel_for_user is called as a side-effect of save()
        # Skip it for now so we can unit test
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_patient_pubnub_channel:
            test_user = User.objects.create(email="<EMAIL>")
            client = FireflyTestCase.get_member_client(member=test_user)
            payload = {
                "first_name": "Test_care_Only",
                "last_name": "user",
                "phone_number": "",
                "preferred_name": "Care",
                "sex": "Male",
                "gender": ["Male"],
                "pronouns": "He/Him/His",
                "dob": "1992-11-22",
                "created_from": "app",
                "patient_referral_program": "",
                "insurance_member_info": {
                    "state": "MA",
                    "source_type": "employer",
                    "insurance_payer_id": self.aetna_payer.id,
                    "member_id": "MA12312312",
                },
                "consent_forms": [self.consent_form.id],
            }
            response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
            self.assertEqual(response.status_code, 200)
            person = Person.objects.get(user=test_user)
            mock_create_patient_pubnub_channel.send.assert_called_once_with(person.pk)
        create_patient_pubnub_channel(person.pk)
        person.refresh_from_db()
        self.assertEqual(person.pubnub_channel, "foo_channel")
        mock_pubnub_client.reset_mock()
        mock_pubnub_client.return_value.get_channel_for_user.return_value = "bar_channel"
        create_patient_pubnub_channel(person.pk)
        person.refresh_from_db()
        self.assertEqual(person.pubnub_channel, "foo_channel")

    @override_settings(ENABLE_PUBNUB_CHANNEL_CREATION=False)
    @patch("firefly.core.user.utils.PubNubClient")
    def test_create_patient_pubnub_channel_with_channel_creation_turned_off(
        self,
        mock_pubnub_client,
    ):
        mock_pubnub_client.return_value.get_channel_for_user.return_value = "foo_channel"
        # get_channel_for_user is called as a side-effect of save()
        # Skip it for now so we can unit test
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_patient_pubnub_channel:
            test_user = User.objects.create(email="<EMAIL>")
            client = FireflyTestCase.get_member_client(member=test_user)
            payload = {
                "first_name": "Test_care_Only",
                "last_name": "user",
                "phone_number": "",
                "preferred_name": "Care",
                "sex": "Male",
                "gender": ["Male"],
                "pronouns": "He/Him/His",
                "dob": "1992-11-22",
                "created_from": "app",
                "patient_referral_program": "",
                "insurance_member_info": {
                    "state": "MA",
                    "source_type": "employer",
                    "insurance_payer_id": self.aetna_payer.id,
                    "member_id": "MA12312312",
                },
                "consent_forms": [self.consent_form.id],
            }
            response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
            self.assertEqual(response.status_code, 200)
            mock_create_patient_pubnub_channel.send.assert_not_called()
            person = Person.objects.get(user=test_user)
        person.refresh_from_db()
        self.assertIsNone(person.pubnub_channel)

    @override_settings(ENABLE_PUBNUB_CHANNEL_CREATION=True)
    @patch("firefly.core.user.utils.PubNubClient")
    def test_create_patient_pubnub_channel_when_user_changes(self, mock_pubnub_client):
        mock_pubnub_client.return_value.get_channel_for_user.return_value = "foo_channel"
        # get_channel_for_user is called as a side-effect of save()
        # Verify that pubnub channel creation is invoked when user is added to person
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_patient_pubnub_channel:
            test_user = User.objects.create(email="<EMAIL>")
            client = FireflyTestCase.get_member_client(member=test_user)
            payload = {
                "first_name": "Test_care_Only",
                "last_name": "user",
                "phone_number": "",
                "preferred_name": "Care",
                "sex": "Male",
                "gender": ["Male"],
                "pronouns": "He/Him/His",
                "dob": "1992-11-22",
                "created_from": "app",
                "patient_referral_program": "",
                "insurance_member_info": {
                    "state": "MA",
                    "source_type": "employer",
                    "insurance_payer_id": self.aetna_payer.id,
                    "member_id": "MA12312312",
                },
                "consent_forms": [self.consent_form.id],
            }
            response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
            self.assertEqual(response.status_code, 200)
            person: Person = Person.objects.get(user=test_user)
            mock_create_patient_pubnub_channel.send.assert_called_once_with(person.pk)
        create_patient_pubnub_channel(person.pk)
        person.refresh_from_db()
        self.assertEqual(person.pubnub_channel, "foo_channel")
        mock_pubnub_client.reset_mock()
        mock_pubnub_client.return_value.get_channel_for_user.return_value = "bar_channel"
        # Verify that signal is invoked
        # when user is changed on a person
        person.user = PatientUserFactory.create()
        person.pubnub_channel = None
        mock_create_patient_pubnub_channel.reset_mock()
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_patient_pubnub_channel:
            person.save()
        mock_create_patient_pubnub_channel.send.assert_called_once_with(person.pk)
        person.refresh_from_db()
        self.assertEqual(person.pubnub_channel, None)
        # Verify that signal is invoked but pubnub channel creation is not invoked
        # when user is removed on a person
        person.user = None
        person.pubnub_channel = None
        mock_create_patient_pubnub_channel.reset_mock()
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_pubnub_channel:
            person.save()
        mock_create_patient_pubnub_channel.send.assert_not_called()
        self.assertEqual(person.pubnub_channel, None)
        # Verify that changing a random attribute on the person - does not invoke the signal
        with patch("firefly.core.user.signals.create_patient_pubnub_channel") as mock_create_pubnub_channel:
            person.first_name = "Test2"
            person.save()
            mock_create_pubnub_channel.send.assert_not_called()

    def test_last_engagement_at(self):
        # Ensure engagement happens after patient is created
        self.patient.onboarding_state.initialized_at = timezone.now() - timedelta(days=10)
        self.patient.onboarding_state.save()
        # Create past chat messages
        thread = ChatThread.objects.create(
            uid=f"{self.patient}.default_v1",
            patient=self.patient,
            name="fireflyhealth",
            tenant=self.tenant,
        )
        # Create past appointments
        appointment_1 = Appointment.objects.create(
            patient=self.patient,
            elation_id=990003,
            physician=self.physician,
            start=(timezone.now() - timedelta(days=1)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason="Clinic",
        )
        Appointment.objects.create(
            patient=self.patient,
            elation_id=990002,
            physician=self.physician,
            start=(timezone.now() - timedelta(days=2)),
            duration="00:15:00",
            time_slot_type="appointment_slot",
            reason="Clinic",
        )
        ChatMessageV2.objects.create(
            uid=uuid.uuid4(),
            thread=thread,
            sender=self.patient,
            sent_at=timezone.now() - timedelta(days=3),
        )
        ChatMessageV2.objects.create(
            uid=uuid.uuid4(),
            thread=thread,
            sender=self.patient,
            sent_at=timezone.now() - timedelta(days=4),
        )
        # Create past tasks
        Task.objects.create(
            title="test task 1",
            patient=self.patient,
            is_complete=True,
            completed_on=datetime.datetime.now() - timedelta(days=5),
        )
        Task.objects.create(
            title="test task 2",
            patient=self.patient,
            is_complete=True,
            completed_on=datetime.datetime.now() - timedelta(days=6),
        )
        # crete task today
        Task.objects.create(
            title="test task 3",
            patient=self.patient,
            is_complete=True,
            completed_on=datetime.datetime.now(),
        )
        # Create past forms
        form = Form.objects.create(uid="test")
        FormSubmission.objects.create(
            form=form,
            user=self.patient,
            started_at=datetime.datetime.now() - timedelta(days=7),
        )
        FormSubmission.objects.create(
            form=form,
            user=self.patient,
            started_at=datetime.datetime.now() - timedelta(days=8),
        )
        last_engagement_at = get_last_engagement_at(self.patient.pk)
        self.assertEqual(last_engagement_at, appointment_1.start)


class UserCareTeamTestCase(FireflyTestCase):
    def test_get_patient_care_team(self):
        patient_user = User.objects.create(
            phone_number="**********",
            first_name="Test",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )

        insurance_payer = InsurancePayer(name="Test Provider", firefly_accepted=1)
        insurance_payer.save()
        insurance_info = InsuranceMemberInfo.objects.create(
            insurance_payer=insurance_payer, member_id="00011", group_number="11234"
        )

        person = Person.objects.create(user=patient_user, insurance_info=insurance_info)
        person.user.save()

        url = "/user/{}/care-team/v2/"
        patient_get_own_careteam = self.client.get(url.format(self.patient.id))
        patient_get_other_careteam = self.client.get(url.format(patient_user.id))
        self.assertEqual(patient_get_own_careteam.status_code, 200)
        self.assertEqual(patient_get_own_careteam.json(), [])
        self.assertEqual(patient_get_other_careteam.status_code, 403)

    def test_set_patient_care_team(self):
        provider_1 = ProviderDetailFactory.create()
        provider_2 = ProviderDetailFactory.create()
        provider_3 = ProviderDetailFactory.create()
        provider_4 = ProviderDetailFactory.create()
        provider_5 = ProviderDetailFactory.create()

        group_md, _ = Group.objects.get_or_create(name=MD_ROLE)
        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)
        group_rn, _ = Group.objects.get_or_create(name=RN_ROLE)

        group_md.user_set.add(provider_1.user)
        group_np.user_set.add(provider_2.user)
        group_hg.user_set.add(provider_3.user)
        group_rn.user_set.add(provider_4.user)
        group_rn.user_set.add(provider_5.user)

        payload = {
            "care_team": {
                "md": provider_1.user_id,
                "np": provider_2.user_id,
                "health_guide": provider_3.user_id,
                "behavioral_health": None,
                "rn": provider_4.user_id,
            }
        }
        url = "/user/{}/care-team/v2/"
        set_careteam = self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)
        self.assertIsNotNone(self.patient.onboarding_state.care_team_selection_at)
        patient_get_own_careteam = self.client.get(url.format(self.patient.id))
        self.assertEqual(patient_get_own_careteam.status_code, 200)
        expected_care_team = [
            {
                "id": provider_1.user_id,
                "group": "role.MD",
            },
            {
                "id": provider_2.user_id,
                "group": "role.NP",
            },
            {
                "id": provider_3.user_id,
                "group": "role.HealthGuide",
            },
            {
                "id": provider_4.user_id,
                "group": "role.RN",
            },
        ]
        care_team_from_response = []
        for care_team_item in patient_get_own_careteam.json():
            care_team_from_response.append(
                {
                    "id": care_team_item["id"],
                    "group": care_team_item["group"],
                },
            )
        self.assertEqual(
            sorted(expected_care_team, key=lambda item: item["id"]),
            sorted(care_team_from_response, key=lambda item: item["id"]),
        )
        self.assertEqual(
            ProviderDetailPatientPersons.all_objects.filter(deleted__isnull=False).count(),
            0,
        )
        another_patient = PersonUserFactory().user
        payload = {
            "care_team": {
                "md": provider_1.user_id,
                "np": provider_2.user_id,
                "health_guide": provider_3.user_id,
                "behavioral_health": None,
                "rn": provider_4.user_id,
            }
        }
        url = "/user/{}/care-team/v2/"
        set_careteam = self.provider_client.patch(url.format(another_patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)
        another_patient.refresh_from_db()
        self.assertIsNotNone(another_patient.onboarding_state.care_team_selection_at)
        self.assertIn(provider_1, list(another_patient.person.care_team.all()))
        self.assertIn(provider_2, list(another_patient.person.care_team.all()))
        self.assertIn(provider_3, list(another_patient.person.care_team.all()))
        # change the care team allocation
        # verify that the providerdetail.person relationship is nuked
        # and that the active rows are returned in the API
        payload = {
            "care_team": {
                "md": provider_1.user_id,
                "np": provider_2.user_id,
                "health_guide": provider_3.user_id,
                "behavioral_health": None,
                "rn": provider_5.user_id,
            }
        }
        url = "/user/{}/care-team/v2/"
        set_careteam = self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        self.assertEqual(set_careteam.status_code, 200)
        self.assertIsNotNone(self.patient.onboarding_state.care_team_selection_at)
        patient_get_own_careteam = self.client.get(url.format(self.patient.id))
        self.assertEqual(patient_get_own_careteam.status_code, 200)
        expected_care_team = [
            {
                "id": provider_1.user_id,
                "group": "role.MD",
            },
            {
                "id": provider_2.user_id,
                "group": "role.NP",
            },
            {
                "id": provider_3.user_id,
                "group": "role.HealthGuide",
            },
            {
                "id": provider_5.user_id,
                "group": "role.RN",
            },
        ]
        care_team_from_response = []
        for care_team_item in patient_get_own_careteam.json():
            care_team_from_response.append(
                {
                    "id": care_team_item["id"],
                    "group": care_team_item["group"],
                },
            )

        self.assertEqual(
            sorted(expected_care_team, key=lambda item: item["id"]),
            sorted(care_team_from_response, key=lambda item: item["id"]),
        )
        self.assertEqual(
            ProviderDetailPatientPersons.all_objects.filter(deleted__isnull=False).count(),
            # Since we do a clear and a set
            len(expected_care_team),
        )
        another_patient_client = FireflyTestAPIClient()
        # Add lucian-user-tenant header for any patient client api call
        # This header required to test tenant permission for APP patient workflow
        another_patient_client.credentials(**{LUCIAN_USER_TENANT_HEADER_TRANSFORMED: FIREFLY_TENANT_KEY})
        another_patient_client.force_authenticate(user=another_patient)
        patient_get_own_careteam = another_patient_client.get(url.format(another_patient.id))
        self.assertEqual(patient_get_own_careteam.status_code, 200)
        expected_care_team = [
            {
                "id": provider_1.user_id,
                "group": "role.MD",
            },
            {
                "id": provider_2.user_id,
                "group": "role.NP",
            },
            {
                "id": provider_3.user_id,
                "group": "role.HealthGuide",
            },
            {
                "id": provider_4.user_id,
                "group": "role.RN",
            },
        ]
        care_team_from_response = []
        for care_team_item in patient_get_own_careteam.json():
            care_team_from_response.append(
                {
                    "id": care_team_item["id"],
                    "group": care_team_item["group"],
                },
            )
        self.assertEqual(
            sorted(expected_care_team, key=lambda item: item["id"]),
            sorted(care_team_from_response, key=lambda item: item["id"]),
        )
        self.assertEqual(
            ProviderDetailPatientPersons.all_objects.filter(deleted__isnull=False).count(),
            4,
        )

    def test_get_group(self):
        # provider with no group won't break get group
        provider_1 = ProviderDetailFactory.create()
        provider_2 = ProviderDetailFactory.create()
        provider_3 = ProviderDetailFactory.create()
        provider_4 = ProviderDetailFactory.create()

        group_np, _ = Group.objects.get_or_create(name=NP_ROLE)
        group_rn, _ = Group.objects.get_or_create(name=RN_ROLE)
        group_hg, _ = Group.objects.get_or_create(name=HEALTH_GUIDE_ROLE)

        group_np.user_set.add(provider_2.user)
        group_rn.user_set.add(provider_4.user)
        group_hg.user_set.add(provider_3.user)

        payload = {
            "care_team": {
                "md": provider_1.user_id,
                "np": provider_2.user_id,
                "rn": provider_4.user_id,
                "health_guide": provider_3.user_id,
                "behavioral_health": None,
            }
        }
        url = "/user/{}/care-team/v2/"
        self.provider_client.patch(url.format(self.patient.id), payload, format="json")
        get_careteam = self.provider_client.get(url.format(self.patient.id))
        self.assertEqual(get_careteam.status_code, 200)

    def test_assigning_care_team_from_template(self):
        provider_1 = ProviderDetailFactory()
        provider_2 = ProviderDetailFactory()
        provider_3 = ProviderDetailFactory()
        care_team_template = CareTeamTemplateFactory.create(
            physicians=(
                provider_1.physician,
                provider_2.physician,
                provider_3.physician,
            )
        )
        state = State.objects.filter(abbreviation__iexact=self.patient.person.insurance_info.state).first()
        care_team_template.primary_physician.practicing_states.add(state)
        care_team_template.physicians.set([provider_1.physician, provider_2.physician])
        response = self.client.post(f"/user/me/care-teams/templates/{care_team_template.id}/")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(len(self.patient.person.care_team.all()), 3)
        care_team_ids = []
        for care_team_member in self.patient.person.care_team.all():
            care_team_ids.append(care_team_member.pk)
        self.assertEqual(
            sorted(care_team_ids),
            sorted(
                [
                    provider_1.pk,
                    provider_2.pk,
                    care_team_template.primary_physician.provider.pk,
                ]
            ),
        )
        self.assertIn(
            care_team_template.primary_physician.provider,
            self.patient.person.care_team.all(),
        )
        self.assertIn(provider_1, self.patient.person.care_team.all())
        self.assertIn(provider_2, self.patient.person.care_team.all())
        self.assertIsNotNone(self.patient.onboarding_state.care_team_selection_at)
        self.patient.onboarding_state.to_signedup()
        response = self.provider_client.get("/user/?type=patient")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            sorted(care_team_ids),
            sorted(response.json()[0]["person"]["care_team"]),
        )
        care_team_template.physicians.set([provider_1.physician])
        response = self.client.post(f"/user/me/care-teams/templates/{care_team_template.id}/")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(len(self.patient.person.care_team.all()), 2)
        care_team_ids = []
        for care_team_member in self.patient.person.care_team.all():
            care_team_ids.append(care_team_member.pk)
        self.assertEqual(
            sorted(care_team_ids),
            sorted([provider_1.pk, care_team_template.primary_physician.provider.pk]),
        )
        response = self.provider_client.get("/user/?type=patient")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()[0]["person"]["care_team"]), 2)
        self.assertEqual(
            sorted(care_team_ids),
            sorted(response.json()[0]["person"]["care_team"]),
        )

    @patch("firefly.core.user.api.care_team.clinicians_in_thread")
    def test_patient_visible_care_team(self, mock_clinicians_in_thread):
        url = "/user/me/care-team/"
        provider = User.objects.create(
            phone_number="**********",
            email="<EMAIL>",
            password="Password1$",  # pragma: allowlist secret
        )
        provider_with_role = ProviderDetail.objects.create(user=provider, tenant=self.tenant)
        provider_with_role.internal_role = RoleFactory()
        provider_with_role.save()
        mock_clinicians_in_thread.return_value = User.objects.filter(id=provider.id)
        response = self.client.get(url, format="json")
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["id"], provider.id)
        self.assertEqual(
            response.json()[0]["internal_role"],
            provider_with_role.internal_role.role_label,
        )
        thread, _ = get_or_create_default_patient_thread(self.patient.id, FIREFLY_TENANT_KEY)
        mock_clinicians_in_thread.assert_called_once_with(thread)
        mock_clinicians_in_thread.reset_mock()

        # Null internal role value
        provider_with_role.internal_role = None
        provider_with_role.save()
        mock_clinicians_in_thread.return_value = User.objects.filter(id=provider.id)
        response = self.client.get(url, format="json")
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["id"], provider.id)
        self.assertIsNone(response.json()[0]["internal_role"], None)
        thread, _ = get_or_create_default_patient_thread(self.patient.id, FIREFLY_TENANT_KEY)
        mock_clinicians_in_thread.assert_called_once_with(thread)


class SignalsTestCase(FireflyTestCase):
    @patch("firefly.core.user.signals.update_auth0_user")
    def test_user_is_active_changed(self, mock_update_auth0_user):
        self.patient.is_active = False
        self.patient.save()
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": True})
        self.patient.is_active = True
        self.patient.save()
        mock_update_auth0_user.assert_called_with(self.patient, {"blocked": False})

    def test_attribution_creation(self):
        person: Person = PersonUserFactory.create()
        person.refresh_from_db()
        self.assertTrue(hasattr(person, "attribution"))
        self.assertIsNotNone(person.attribution)
        person.first_name = str(uuid.uuid4())[:4]
        person.save()
        attribution_queryset = Attribution.objects.filter(person=person)
        self.assertEqual(attribution_queryset.count(), 1)


@mock.patch("firefly.modules.auto_eligibility.utils._log_eligibility_check_request")
@mock.patch("firefly.modules.auto_eligibility.utils.TrizettoClient")
class UserInsuranceTestCase(FireflyTestCase):
    ISNOTELIGIBLE_QUERY_TRIZETTO = {
        "SuccessCode": "Success",
        "ResponseAsXml": """<eligibilityresponse>
        <infosource>
          <transactionid>3f66a054524a0ca4c5e566b7e5d3c1</transactionid>
         <payername>UMR Wausau</payername>
         <payerid>UMR</payerid>
       </infosource>
        <inforeceiver>
        <providername><last>GREENBERG</last></providername><npi>**********</npi></inforeceiver>
        <subscriber><trace_number>**********</trace_number><trace_id>99Trizetto</trace_id><date>
       <datequalifier>Plan</datequalifier>
       <date-of-service>20200624</date-of-service>
       </date><patientname><first>NOBODY</first>
       <last>INPARTICULAR</last></patientname><patientid>U11111111</patientid>
       <date-of-birth>19641213</date-of-birth>
       <rejection><rejectreason>Invalid/Missing Subscriber/Insured ID</rejectreason>
       <followupaction>Please Correct and Resubmit</followupaction></rejection>
       </subscriber>
      </eligibilityresponse>""",
        "ResponseDataType": "Xml",
        "ExtraProcessingInfo": {"Failures": None, "AllMessages": None},
        "ResponseAsRawString": None,
    }
    ISELIGIBLE_QUERY_TRIZETTO = {
        "SuccessCode": "Success",
        "ResponseAsXml": """<eligibilityresponse>
       <subscriber>
             <subscriberaddinfo>
                 <subsupplementalid>Group Number</subsupplementalid>
                 <grouppolicynum>311111</grouppolicynum>
                 <plansponsorname>Fake Company</plansponsorname>
           </subscriberaddinfo>
           <date>
             <datequalifier>Eligibility Begin</datequalifier>
             <date-of-service>20110101</date-of-service>
            </date>
            <date>
              <datequalifier>Plan Begin</datequalifier>
              <date-of-service>20200101</date-of-service>
             </date>
             <date>
                  <datequalifier>Plan End</datequalifier>
                  <date-of-service>20201231</date-of-service>
              </date>
           <patientname><first>TEST</first><last>PATIENT</last>
           <patientaddress>22 Mockigbird Lane</patientaddress>
           <patientcity>Frostbite Falls</patientcity>
           <patientstate>MN</patientstate>
           <patientzip>*********</patientzip>
           </patientname><patientid>55533333</patientid>
          <sex>M</sex>
          <date-of-birth>19451213</date-of-birth>
          <benefit>
            <info>Active Coverage</info>
            <servicetype>Health Benefit Plan Coverage</servicetype>
            <servicetypecode>30</servicetypecode>
            <insurancetype>Preferred Provider Organization (PPO)</insurancetype>
            <plancoveragedescription>CW COMMUNITY CHOICE</plancoveragedescription>
          </benefit>
      </subscriber>
      </eligibilityresponse>""",
        "ExtraProcessingInfo": {"Failures": None, "AllMessages": None},
        "ResponseAsRawString": None,
    }

    def test_patient_insurance_eligibility_for_discharged_patient(self, mock_trizetto_client, mock_request):
        mock_trizetto_client.return_value.get_response.return_value = self.ISNOTELIGIBLE_QUERY_TRIZETTO
        person = PersonUserFactory.create()
        ProgramEnrollment.objects.update_or_create(
            person=person,
            program_id=ProgramCodes.PRIMARY_CARE,
            defaults={
                "period": (datetime.datetime.now(), None),
                "status": PrimaryCareProgramStatus.CHURNED,
                "reason": "Discharged:elected_to_leave",
                "reason_code": ProgramEnrollment.ReasonCode.DISCHARGED.label,
            },
        )

        # check eligibility should not re-enroll patient into primary care
        url = "/user/{}/coverage/check_eligibility/"
        payload = {
            "status": "ineligible",
            "reason": "uninsured",
            "name": "Ineligible (Uninsured)",
        }
        response = self.provider_client.post(url.format(person.user.pk), payload, format="json")
        self.assertEqual(response.status_code, 200)
        enrollment = ProgramEnrollment.objects.get(person=person, period__endswith__isnull=True)
        self.assertEqual(enrollment.status, PrimaryCareProgramStatus.CHURNED)
        self.assertEqual(enrollment.reason_code, ProgramEnrollment.ReasonCode.DISCHARGED.label)

    def test_patient_insurance_eligibility_for_deactivated_patient(self, mock_trizetto_client, mock_request):
        mock_trizetto_client.return_value.get_response.return_value = self.ISNOTELIGIBLE_QUERY_TRIZETTO
        person = PersonUserFactory.create()
        person.user.onboarding_state.status = OnboardingStatus.DEACTIVATED
        person.user.onboarding_state.save()

        # check eligibility should not update onboarding status
        url = "/user/{}/coverage/check_eligibility/"
        payload = {
            "status": "ineligible",
            "reason": "uninsured",
            "name": "Ineligible (Uninsured)",
        }
        response = self.provider_client.post(url.format(person.user.pk), payload, format="json")
        self.assertEqual(response.status_code, 200)
        person.user.onboarding_state.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.DEACTIVATED)

    def test_patient_insurance_eligibility_not_eligible(self, mock_trizetto_client, mock_request):
        mock_trizetto_client.return_value.get_response.return_value = self.ISNOTELIGIBLE_QUERY_TRIZETTO
        person = PersonUserFactory.create()

        url = "/user/{}/coverage/check_eligibility/"
        payload = {
            "status": "ineligible",
            "reason": "uninsured",
            "name": "Ineligible (Uninsured)",
        }
        response = self.provider_client.post(url.format(person.user.pk), payload, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.json()["eligibility"])

    def test_patient_insurance_eligibility_is_eligible(self, mock_trizetto_client, mock_request):
        mock_trizetto_client.return_value.get_response.return_value = self.ISELIGIBLE_QUERY_TRIZETTO

        person = PersonUserFactory.create()

        url = "/user/{}/coverage/check_eligibility/"
        payload = {
            "status": "ineligible",
            "reason": "uninsured",
            "name": "Ineligible (Uninsured)",
        }
        response = self.provider_client.post(url.format(person.user.pk), payload, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.json()["eligibility"])

    def test_patient_insurance_type(self, mock_trizetto_client, mock_request):
        mock_trizetto_client.return_value.get_response.return_value = self.ISELIGIBLE_QUERY_TRIZETTO
        CaseCategory.objects.get_or_create(unique_key=INSURANCE_PLAN_NEEDS_REVIEW, title=INSURANCE_PLAN_NEEDS_REVIEW)
        person = PersonUserFactory.create()

        url = "/user/{}/coverage/check_eligibility/"
        payload = {
            "status": "ineligible",
            "reason": "uninsured",
            "name": "Ineligible (Uninsured)",
        }
        with self.captureOnCommitCallbacks(execute=True):
            response = self.provider_client.post(url.format(person.user.pk), payload, format="json")
        self.assertEqual(response.status_code, 200)
        person.insurance_info.refresh_from_db()
        self.assertEqual(person.insurance_info.plan_type, InsuranceMemberInfo.PLAN_TYPE_PPO)


class ElationPersonSyncTestCase(FireflyTestCase):
    def test_get_elation_friendly_payload(self):
        insurance_info = self.patient.person.insurance_info
        insurance_info.synced_to_elation = False
        # Patient with partial address
        insurance_info.street_address = None
        insurance_info.state = "OH"
        insurance_info.save()
        result = ElationPersonSync().get_elation_friendly_payload(self.patient.person)
        self.assertEqual(result.get("address").get("state"), "OH")
        self.assertEqual(result.get("address").get("address_line1"), "")
        # Complete address
        insurance_info.street_address = "street"
        insurance_info.state = "OH"
        insurance_info.city = "city"
        insurance_info.zipcode = "00000"
        insurance_info.save()
        result = ElationPersonSync().get_elation_friendly_payload(self.patient.person)
        self.assertEqual(result.get("address").get("state"), "OH")

    def test_save_clean_record(self):
        clean_record = {
            "street_address": str(uuid.uuid4())[:4],
            "first_name": str(uuid.uuid4())[:4],
            "member_id": str(uuid.uuid4())[:4],
        }
        original_clean_record = clean_record.copy()
        ElationPersonSync().save_clean_record(elation_id=self.patient.elation_id, clean_record=clean_record)
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.person.first_name, original_clean_record["first_name"])
        self.assertEqual(
            self.patient.person.insurance_info.street_address,
            original_clean_record["street_address"],
        )
        self.assertEqual(
            self.patient.person.insurance_info.member_id,
            original_clean_record["member_id"],
        )

    @override_switch(WAFFLE_SWITCH_SKIP_INSURANCE_SYNC_FROM_ELATION, active=True)
    def test_save_clean_record_skip_insurance_update(self):
        insurance_info = self.patient.person.insurance_info
        original_member_id = str(uuid.uuid4())[:4]
        insurance_info.member_id = original_member_id
        insurance_info.save()

        clean_record = {
            "member_id": original_member_id + "xxx",
        }

        ElationPersonSync().save_clean_record(elation_id=self.patient.elation_id, clean_record=clean_record)
        insurance_info.refresh_from_db()
        self.assertEqual(
            insurance_info.member_id,
            original_member_id,
        )

    @patch(
        "firefly.core.services.elation.sync.mixins.elation_to_object.ElationRecordToObject.get_model_friendly_record"
    )
    def test_save_records(self, mock_get_model_friendly_record):
        self.patient.person.elation_id = 999111
        self.patient.person.save()
        mock_data = {
            "elation_id": self.patient.person.elation_id,
            "first_name": str(uuid.uuid4())[:4],
        }
        mock_get_model_friendly_record.return_value = mock_data.copy()
        saved_instances = ElationPersonSync().save_records(records=[{"data": "test"}])
        self.assertEqual(len(saved_instances), 1)
        self.patient.refresh_from_db()
        self.assertEqual(self.patient.person.first_name, mock_data["first_name"])

    def test_save_relations_records(self):
        person = PersonUserFactory(elation_id=1122331)
        add_person_to_program(person, ProgramCodes.PRIMARY_CARE)

        person_payload = ElationPersonSync().get_elation_friendly_payload(person)
        person_payload["id"] = person.elation_id
        person_payload["caregiver_practice"] = person.caregiver_practice.elation_id
        person_payload["insurances"] = {
            "group_number": person.insurance_info.group_number,
            "member_id": person.insurance_info.member_id,
        }
        saved_instances = ElationPersonSync().save_records(records=[person_payload])
        self.assertEqual(len(saved_instances), 1)
        person.refresh_from_db()

    @patch(
        "firefly.core.services.elation.sync.mixins.elation_to_object.ElationRecordToObject.get_model_friendly_record"
    )
    def test_block_person_creation(self, mock_get_model_friendly_record):
        mock_data = {
            "dob": "2000-01-01",
            "elation_id": 11122112221,
            "first_name": str(uuid.uuid4())[:4],
        }
        mock_get_model_friendly_record.return_value = mock_data.copy()
        saved_instances = ElationPersonSync().save_records(records=[{"data": "test"}])

        self.assertEqual(len(saved_instances), 0)
        self.assertEqual(Person.objects.filter(elation_id=11122112221).count(), 0)

    def test_person_deletion(self):
        person = PersonUserFactory(elation_id=1122331)
        mock_data = {
            "id": person.elation_id,
            "first_name": str(uuid.uuid4())[:4],
            "address": {"zip": "02139"},
            "insurances": {
                "group_number": person.insurance_info.group_number,
                "member_id": person.insurance_info.member_id,
            },
            "caregiver_practice": get_default_caregiver_practice_obj().elation_id,
        }

        class DummyRequest:
            data = {}

        callback_payload = DummyRequest()
        callback_payload.data = {"data": mock_data, "action": "deleted"}
        ElationPersonSync().subscription_callback(callback_payload)
        person.refresh_from_db()
        self.assertIsNone(person.deleted)


class ManagementCommandTestCase(FireflyTestCase):
    @patch("firefly.core.user.utils.create_auth0_user")
    def test_create_provider(self, mock_create_auth0_user):
        physician = PhysicianFactory()
        tenant = TenantFactory()
        role = RoleFactory()
        md_group = Group.objects.get(name=MD_ROLE)
        email = "<EMAIL>"
        phone_number = "**********"
        title = "MadeUpTitle"
        state_MA = State.objects.get(abbreviation="MA")
        state_NH = State.objects.get(abbreviation="NH")
        clinician_group, _ = Group.objects.get_or_create(name="Clinician")
        role.groups.set([md_group, clinician_group])

        call_command(
            "create_provider",
            email=email,
            first_name="Test",
            last_name="Provider",
            phone_number=phone_number,
            physician_id=physician.id,
            role_id=role.pk,
            tenant_id=tenant.pk,
            states=f"{state_MA.abbreviation},{state_NH.abbreviation}",
            user=self.provider,
            title=title,
        )

        created_user: User = User.objects.get(email=email, phone_number=phone_number)
        self.assertEqual(created_user.first_name, physician.first_name)
        self.assertEqual(created_user.last_name, physician.last_name)
        self.assertEqual(created_user.is_staff, False)
        created_provider = ProviderDetail.objects.get(user=created_user)
        self.assertEqual(created_provider.is_external, False)

        self.assertEqual(created_provider.title, title)
        self.assertEqual(created_provider.internal_role, role)
        self.assertEqual(created_provider.physician, physician)
        practicing_states = list(physician.practicing_states.order_by("abbreviation").all())
        self.assertEqual(practicing_states, [state_MA, state_NH])
        groups_for_user = list(created_user.groups.order_by("name").all())
        self.assertEqual(groups_for_user, [clinician_group, md_group])
        mock_create_auth0_user.assert_called_with(created_user)

    @patch("firefly.core.user.utils.create_auth0_user")
    def test_create_staff_user(self, mock_create_auth0_user):
        email = "<EMAIL>"
        phone_number = "**********"
        slack_alias_id = "my_slack_id_4817"
        bio = "Always tries their best"
        tenant = TenantFactory()
        role = RoleFactory()
        title = "MadeUpTitle"
        md_group = Group.objects.get(name=MD_ROLE)
        clinician_group, _ = Group.objects.get_or_create(name="Clinician")
        role.groups.set([md_group, clinician_group])
        call_command(
            "create_staff_user",
            email=email,
            first_name="Test",
            last_name="StaffUser",
            phone_number=phone_number,
            role_id=role.pk,
            tenant_id=tenant.pk,
            user=self.provider,
            slack_alias_id=slack_alias_id,
            bio=bio,
            title=title,
        )

        created_user = User.objects.get(email=email, phone_number=phone_number)
        self.assertEqual(created_user.firstname, "Test")
        self.assertEqual(created_user.lastname, "StaffUser")
        self.assertEqual(created_user.is_staff, False)
        created_provider = ProviderDetail.objects.get(user=created_user)
        self.assertEqual(created_provider.is_external, False)
        self.assertEqual(created_provider.title, title)
        self.assertEqual(created_provider.internal_role, role)
        self.assertEqual(created_provider.bio, bio)
        mock_create_auth0_user.assert_called_with(created_user)
        self.assertTrue(
            AliasMapping.objects.filter(
                object_id=created_user.id,
                content_type=ContentType.objects.get_for_model(User),
                alias_name=AliasName.SLACK,
                alias_id=slack_alias_id,
            ).exists()
        )
        groups_for_user = list(created_user.groups.order_by("name").all())
        self.assertEqual(groups_for_user, [clinician_group, md_group])

    @patch(
        "firefly.core.user.management.commands.backfill_latitude_longitude_for_person_address.Geocoder.geocode_address",
        return_value=(42.364870, -71.178260),
    )
    def test_backfill_latitude_longitude_for_person_address(self, mock_lat_long):
        lucian_user = get_lucian_bot_user()

        # first create the person address and add a reference to a person
        address = InsuranceMemberInfoFactory()
        self.patient.person.insurance_info = address
        self.patient.person.save()

        # call the command to store the lat long for the above address
        with override_settings(APP_ENV="prod"):
            call_command("backfill_latitude_longitude_for_person_address", user=lucian_user)

        mock_lat_long.assert_called_with(
            address.street_address,
            address.street_address_2,
            address.city,
            address.state,
            address.zipcode,
            "backfill_latitude_longitude",
        )
        # get the same address from db and assert the values
        address_with_lat_long = InsuranceMemberInfo.objects.get(id=address.id)
        self.assertNotEqual(address_with_lat_long.latitude, None)
        self.assertNotEqual(address_with_lat_long.longitude, None)

    @patch(
        "firefly.core.user.management.commands.backfill_latitude_longitude_for_person_address.Geocoder.geocode_address",
        return_value=(42.364870, -71.178260),
    )
    def test_backfill_latitude_longitude_for_person_address_in_non_prod_environment(self, mock_lat_long):
        lucian_user = get_lucian_bot_user()

        # first create the person address and add a reference to a person
        address = InsuranceMemberInfoFactory()
        self.patient.person.insurance_info = address
        self.patient.person.save()

        # call the command to store the lat long for the above address
        with override_settings(APP_ENV="staging"):
            call_command("backfill_latitude_longitude_for_person_address", user=lucian_user)

        mock_lat_long.assert_not_called()
        # get the same address from db and assert the values
        address_with_lat_long = InsuranceMemberInfo.objects.get(id=address.id)
        self.assertIsNone(address_with_lat_long.latitude)
        self.assertIsNone(address_with_lat_long.longitude)

    @patch(
        "firefly.core.user.management.commands.backfill_latitude_longitude_for_person_address.Geocoder.geocode_address",
        return_value=(42.364870, -71.178260),
    )
    def test_backfill_latitude_longitude_for_test_account(self, mock_lat_long):
        lucian_user = get_lucian_bot_user()

        person = PersonUserFactory()
        person.user.test_only = True
        person.user.save()

        # first create the person address and add a reference to a person
        address = InsuranceMemberInfoFactory()
        person.insurance_info = address
        person.save()

        # call the command to store the lat long for the above address
        with override_settings(APP_ENV="prod"):
            call_command(
                "backfill_latitude_longitude_for_person_address",
                "--test_accounts_only",
                user=lucian_user,
            )

        mock_lat_long.assert_called_with(
            address.street_address,
            address.street_address_2,
            address.city,
            address.state,
            address.zipcode,
            "backfill_latitude_longitude",
        )
        # get the same address from db and assert the values
        address_with_lat_long = InsuranceMemberInfo.objects.get(id=address.id)
        self.assertNotEqual(address_with_lat_long.latitude, None)
        self.assertNotEqual(address_with_lat_long.longitude, None)

    @patch(
        "firefly.core.user.management.commands.backfill_latitude_longitude_for_person_address.Geocoder.geocode_address",
        return_value=(None, None),
    )
    def test_backfill_latitude_longitude_for_person_address_with_empty_address(self, *args, **kwarg):
        lucian_user = get_lucian_bot_user()

        address = InsuranceMemberInfoFactory()

        # call the command to store the lat long for the above address
        with override_settings(APP_ENV="prod"):
            call_command("backfill_latitude_longitude_for_person_address", user=lucian_user)

        # get the same address from db and check it should not contain lat long
        address_with_lat_long = InsuranceMemberInfo.objects.get(id=address.id)
        self.assertEqual(address_with_lat_long.latitude, None)
        self.assertEqual(address_with_lat_long.longitude, None)

    @patch(
        "firefly.core.user.management.commands.backfill_latitude_longitude_for_person_address.Geocoder.geocode_address",
        return_value=(42.364870, -71.178260),
    )
    def test_backfill_latitude_longitude_for_user_is_null(self, mock_lat_long):
        lucian_user = get_lucian_bot_user()

        person = PersonUserFactory()
        person.user = None
        person.save()
        # first create the person address and add a reference to a person
        address = InsuranceMemberInfoFactory()
        person.insurance_info = address
        person.save()

        # call the command to store the lat long for the above address
        with override_settings(APP_ENV="prod"):
            call_command("backfill_latitude_longitude_for_person_address", user=lucian_user)
        mock_lat_long.assert_called_with(
            address.street_address,
            address.street_address_2,
            address.city,
            address.state,
            address.zipcode,
            "backfill_latitude_longitude",
        )

        # get the same address from db and assert the values
        address_with_lat_long = InsuranceMemberInfo.objects.get(id=address.id)
        self.assertEqual(address_with_lat_long.latitude, Decimal("42.364870"))
        self.assertEqual(address_with_lat_long.longitude, Decimal("-71.178260"))

    @patch("firefly.core.user.management.commands.sync_user_to_elation.elation_update_user")
    def test_sync_user_to_elation(self, mock_elation_update_user):
        person = PersonUserFactory(elation_id=None)
        person_with_elation_id = PersonUserFactory()
        with self.captureOnCommitCallbacks(execute=True):
            call_command(
                "sync_user_to_elation",
                "-user_ids",
                "%s,%s" % (person.user_id, person_with_elation_id.user_id),
                user=self.provider,
            )
        mock_elation_update_user.assert_has_calls(
            [
                mock.call(person_with_elation_id.id),
                mock.call(person.id),
            ]
        )


class ProviderUrlsTestCase(FireflyTestCase):
    def setUp(self, *args, **kwargs):
        super().setUp(args, kwargs)
        self.provider_1 = ProviderDetailFactory()
        self.provider_2 = ProviderDetailFactory()

    def test_patient_get_provider_profile(self):
        response = self.client.get("/user/providers/profiles/")
        self.assertEqual(response.status_code, 200)

    def test_get_provider_profiles_list(self):
        response = self.provider_client.get("/user/providers/profiles/")
        self.assertEqual(response.status_code, 200)
        # Expect 1 profile for Luci bot, 2 for providers created in FireflyTestCase associated with
        # the provider_client, and 2 created  on setup of this test class, 1 for referral group in
        # FireflyTestCase setup.
        self.assertEqual(len(response.json()), 6)

    @patch("firefly.modules.chat_message.utils.PubNubClient")
    def test_get_chat_token(self, mock_client):
        mock_client.return_value.grant_channel_access_token_to_authorized_uuid.return_value = "sample-token"
        response = self.provider_client.post("/user/providers/me/authenticate-chat/v2/")
        response_data = response.json()
        self.assertTrue(isinstance(response_data["token"], str))
        self.assertEqual(response_data["token"], "sample-token")
        self.assertTrue(isinstance(response_data["access_ttl"], int))
        self.assertTrue(isinstance(response_data["uuid"], str))
        self.assertEqual(response_data["uuid"], f"uuid-{self.provider.id}")
