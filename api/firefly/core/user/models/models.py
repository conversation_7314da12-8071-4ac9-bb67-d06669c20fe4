from __future__ import annotations

import logging
import uuid
from datetime import date
from typing import TYPE_CHECKING, List, Optional

import waffle
from django.conf import settings
from django.contrib.auth.base_user import BaseUserManager
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import AbstractUser

# DO NOT COPY-PASTE: Prefer model relationship over <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.postgres.fields import ArrayField  # noqa: TID251
from django.core.exceptions import FieldError
from django.core.validators import RegexValidator
from django.db import models
from django.db.models import Q, QuerySet
from django.db.models.functions import Upper
from django.db.utils import IntegrityError
from django.utils.functional import cached_property
from django_deprecate_fields import deprecate_field
from django_deprecate_fields import deprecate_field as django_deprecate_field

from firefly.core.alias.models import <PERSON>asMapping, <PERSON>asName
from firefly.core.model import BaseModel
from firefly.core.roles.models import Role
from firefly.core.services.auth0.constants import AUTH_AUTH0, AUTH_LEGACY
from firefly.core.startup.utils import get_ssn_salt
from firefly.core.user.constants import (
    DEFAULT_PREFERRED_LANGUAGE,
    ENGINEER_EXCLUDED_MODULES,
    ENGINEER_EXCLUDED_PERMISSIONS,
    RISK_SCORE_CHOICES,
    PatientCreationSources,
)
from firefly.core.utils.exceptions import raise_exception
from firefly.modules.care_teams.utils import (
    get_caregiver_practice,
    get_primary_physician,
    set_caregiver_practice,
    set_primary_physician,
)
from firefly.modules.eligibility.models import EligibilitySubmission
from firefly.modules.eligibility.serializers import clean_ssn_format
from firefly.modules.facts.models import PatientRace, PreferredLanguage
from firefly.modules.firefly_django.models import BaseModelV3, BaseModelV3ManyToManyField
from firefly.modules.firefly_django.save_handlers import PreSaveValidationError, SaveHandlersMixin
from firefly.modules.insurance.models import WAFFLE_CREATE_CASE_FOR_NEW_INSURANCE_PLAN, Employer, InsuranceMemberInfo
from firefly.modules.physician.models import Physician
from firefly.modules.practice.models import Practice
from firefly.modules.statemachines.coverage.mixin import CoverageStateMachineMixin
from firefly.modules.states.models import State
from firefly.modules.tenants.constants import FIREFLY_TENANT_KEY
from firefly.modules.tenants.models import Tenant

if TYPE_CHECKING:
    # These imports are required for typechecking
    from firefly.modules.attribution.models import PayerRosterRecord  # noqa
    from firefly.modules.claims.models import Claim  # noqa
    from firefly.modules.documents.models import PatientSharedFile  # noqa
    from firefly.modules.eligibility.models import (  # noqa
        CoveredMember,
        EligibilityLog,
        EligibilityRecord,
    )
    from firefly.modules.notes.models import Note  # noqa
    from firefly.modules.phone_calls.models import PhoneCall  # noqa
    from firefly.modules.pods.models import PodPeople  # noqa
    from firefly.modules.programs.models import ProgramEnrollment, ProgramInfo  # noqa
    from firefly.modules.quality.models import MeasureReport  # noqa
    from firefly.modules.referral.models import (  # noqa
        PriorAuthorization,
        Referral,
        SearchAvailability,
        Steerage,
        Waiver,
    )
    from firefly.modules.segmentation.models import UserSegmentation  # noqa
    from firefly.modules.statemachines.coverage.models import PatientTransitionLog  # noqa
    from firefly.modules.tasks.models import Task  # noqa


logger = logging.getLogger(__name__)


def deprecate_patient_detail_field(field_instance):
    return django_deprecate_field(
        field_instance,
        return_instead=(lambda: raise_exception(FieldError("PatientDetail is deprecated"))),
    )


class AssigneeGroup(BaseModelV3):
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    name = models.CharField(max_length=500)  # noqa: TID251

    # this flag is set to denote if the group is a provider group
    # this is to be used to filter assignee groups when we need to show only provider groups
    # in case of mutiple users in the group we can set this manually to denote if group is a provider group
    is_provider = models.BooleanField(default=True, null=True, blank=True)
    # A human-readable, understandable key that the code and analytics can use to refer to
    # this assignee group (versus the name, which could be changed for legibility, or the ID, which
    # is not intuitive and varies across the databases).
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unique_key = models.CharField(max_length=200, blank=True, null=True, unique=True)  # noqa: TID251

    def __str__(self) -> str:
        return f"{self.name} ({self.id})"


class UserManager(BaseUserManager):
    use_in_migrations = True

    def _create_user(self, phone_number, email, password, **extra_fields):
        """
        Create and save a user with the given phone_number, email, and password.
        """
        if not phone_number:
            raise ValueError("The given phone_number must be set")
        email = self.normalize_email(email)
        user = self.model(phone_number=phone_number, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, phone_number, email=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(phone_number, email, password, **extra_fields)

    def create_superuser(self, phone_number, email, password, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(phone_number, email, password, **extra_fields)

    def get_by_natural_key(self, username):
        try:
            return self.get(**{self.model.USERNAME_FIELD: username})
        except User.DoesNotExist:
            return self.get(**{"auth_id": username})


class User(SaveHandlersMixin, BaseModelV3, AbstractUser):
    """
    Stores a single user, extends Django's AbstractUser
    """

    AUTH_CHOICES = ((AUTH_AUTH0, AUTH_AUTH0), (AUTH_LEGACY, AUTH_LEGACY))

    created_at = models.DateTimeField(auto_now_add=True, db_index=True)

    phone_regex = RegexValidator(regex=r"^\+?1?\d{9,15}$", message="Phone number format: '+1115559999'.")

    # The phone number field is currently maintained on both the Person and User models and kept in
    # sync between the two. In the future we will update this to be a single Many-to-Many field, but
    # for now. We currently set this value when someone signs up by calling Auth0's API and getting
    # the phone number used to enroll in MFA. A user's phone number may be updated if they change it
    # in the app, a staff member changes it in Lucian, or if they update their MFA enrollment in
    # Auth0 (at which point an Auth0 callback makes a change in our API). Note that when a phone
    # number is changed in the app or in Lucian, the user's MFA enrollment is not changed in Auth0.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_number = models.CharField(validators=[phone_regex], max_length=17, unique=False, null=True, blank=True)  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    unverified_phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True)  # noqa: TID251

    # Auth0 identifier
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    auth_id = models.CharField(max_length=30, unique=True, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    auth_backend = models.CharField(  # noqa: TID251
        max_length=30,
        choices=AUTH_CHOICES,
        default="Legacy",
        null=True,
        blank=True,
    )

    disabled = models.BooleanField(default=False, null=True, blank=True)

    # TODO: mypy rightfully complains that AbstractUser expects this field to be required.
    username = None  # type: ignore

    USERNAME_FIELD = "phone_number"

    REQUIRED_FIELDS = ["email"]

    # TODO: The type annotation and "type: ignore" appears here because AbstractUser expects
    # objects to be a django.contrib.auth.models.UserManager, which is a subclass of
    # django.contrib.auth.base_user.BaseUserManager. Our UserManager is also a subclass of
    # BaseUserManager, not Django's UserManager.
    objects: UserManager = UserManager()  # type: ignore

    # Flag is set to True if User was made for testing purposes.
    test_only = models.BooleanField(default=False, db_index=True)

    updated_at = models.DateTimeField(auto_now=True, db_index=True, null=True)  # type: ignore

    # For staff users, True if User is working in the United States.
    # Certain clients require that access to data is limited based on location.
    is_us_based = models.BooleanField(null=True, blank=True, verbose_name="Is US based")

    # This references the AssigneeGroup that represents assigning something to this individual user
    assignee_group = models.OneToOneField(
        AssigneeGroup,
        related_name="user",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    urgent_visit = django_deprecate_field(
        models.BooleanField(
            help_text="This flag indicates if the patient can book an urgent visit appointment",
            null=True,
            blank=True,
        )
    )

    @property
    def is_patient(self):
        return hasattr(self, "person")

    @property
    def firstname(self):
        if self.is_patient:
            return self.person.first_name
        return self.providerdetail.first_name

    @property
    def lastname(self):
        if self.is_patient:
            return self.person.last_name
        return self.providerdetail.last_name

    @property
    def is_provider(self):
        return hasattr(self, "providerdetail")

    @property
    def account_verified(self):
        if self.is_patient and settings.REQUIRE_PATIENT_VERIFICATION:
            return self.person.account_verified
        return True

    @property
    def account_completed(self):
        if not self.is_patient:
            return True
        return (
            self.person.first_name
            and self.person.last_name
            and (self.person.account_verified or not settings.REQUIRE_PATIENT_VERIFICATION)
        )

    @property
    def elation_id(self):
        if hasattr(self, "person"):
            return self.person.elation_id

    @property
    def elation_url(self):
        if hasattr(self, "person"):
            return self.person.elation_url

    @property
    def zendesk_id(self):
        if hasattr(self, "person"):
            return self.person.zendesk_id

    @cached_property
    def is_engineer_user(self):
        return self.groups.filter(name="Engineer").exists()

    def has_perm(self, perm, obj=None):
        """
        Overrides default `has_perm` method (which configures Django Admin access) to check if user
        is in the Engineer group.
        """
        # Return true if user is an engineer and this permission isn't in excluded list
        if self.is_active and self._has_engineer_perm(perm):
            return True
        return super().has_perm(perm, obj)

    def has_module_perms(self, app_label):
        """
        Overrides default `has_module_perms` method (which configures Django Admin access) to check
        if user is in the Engineer group.
        """
        # Return true if user is an engineer and this permission isn't in excluded list
        if self.is_active and self._has_engineer_module_perms(app_label):
            return True
        return super().has_module_perms(app_label)

    def _has_engineer_perm(self, perm):
        # Check if user is an engineer and if permission is excluded
        if self.is_engineer_user and perm not in ENGINEER_EXCLUDED_PERMISSIONS:
            return True
        return False

    def _has_engineer_module_perms(self, app_label):
        # Check if user is an engineer and if module permissions are excluded
        if self.is_engineer_user and app_label not in ENGINEER_EXCLUDED_MODULES:
            return True
        return False

    class Meta:
        db_table = "auth_user"
        indexes = [
            # User retrieval happens using the exact operator
            # which translates into a comparison based on the
            # output of the upper function
            models.Index(Upper("email"), name="auth_user_upper_email_idx"),
        ]

    def __str__(self) -> str:
        # AbstractUser implements this as `self.get_username()`. Because we set
        # `USERNAME_FIELD` as `phone_number`, which is not unique, it is easy to
        # confuse different users with the same phone number. To avoid this, we
        # override this method to display the primary key to be consistent with
        # the default Model implementation.
        return f"User object ({self.pk})"

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_provider_change

        if hasattr(self, "providerdetail") and self.providerdetail is not None:
            if (
                changed("id")
                or changed("is_active")
                or changed("email")
                or changed("last_name")
                or changed("first_name")
            ):
                logger.info("User %d: Invoking zus update", self.id)
                zus_handle_provider_change.send(user_id=self.id)


class SessionActivity(BaseModelV3):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    last_activity_at = models.DateTimeField(null=True, blank=True)

    class Meta(BaseModelV3.Meta):
        db_table = "auth_user_session_activity"


class PatientAddress(BaseModelV3):
    """A physical address for a patient."""

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    street_address = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    street_address_2 = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    city = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    state = models.CharField(max_length=2, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip = models.CharField(max_length=5, blank=True, null=True)  # noqa: TID251


# DEPRECATED: Do not use
class PatientDetail(BaseModel, CoverageStateMachineMixin):
    """
    Stores additional fields for a patient user
    """

    @property
    def async_elation_updates(self):
        return True

    GENDER_CHOICES = (("Male", "Male"), ("Female", "Female"), ("Unknown", "Unknown"))

    WEB = "web"
    APP = "app"
    CREATED_FROM_CHOICES = ((WEB, "web"), (APP, "app"))

    patient_id = models.UUIDField(null=True, default=uuid.uuid4)

    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)

    elation_id = deprecate_patient_detail_field(models.BigIntegerField(unique=True, blank=True, null=True))

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    hint_id = deprecate_patient_detail_field(models.CharField(unique=True, blank=True, null=True, max_length=16))  # noqa: TID251

    # Zendesk ID is not unique because email addresses are not unique, so we may
    # have more than one user with the same email address (and therefore zendesk ID)
    zendesk_id = deprecate_patient_detail_field(models.BigIntegerField(blank=True, null=True))

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    preferred_name = deprecate_patient_detail_field(models.CharField(max_length=255, blank=True, default=""))  # noqa: TID251

    dob = deprecate_patient_detail_field(models.DateField(blank=True, null=True))

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    zip = deprecate_patient_detail_field(models.CharField(max_length=5, blank=True, null=True))  # noqa: TID251

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    constant_contact_id = deprecate_patient_detail_field(models.CharField(blank=True, null=True, max_length=200))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    appsflyer_id = deprecate_patient_detail_field(models.CharField(blank=True, null=True, max_length=200))  # noqa: TID251

    sex = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=7, choices=GENDER_CHOICES, default="Unknown", blank=True)  # noqa: TID251
    )
    gender = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, blank=True, null=True), null=True, blank=True)  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pronouns = deprecate_patient_detail_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251

    primary_physician = deprecate_patient_detail_field(
        models.ForeignKey(
            Physician,
            related_name="primary_patients",
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    caregiver_practice = deprecate_patient_detail_field(
        models.ForeignKey(
            Practice,
            related_name="patients",
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    insurance_info = deprecate_patient_detail_field(
        models.OneToOneField(
            InsuranceMemberInfo,
            related_name="patient",
            blank=True,
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    address = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.OneToOneField(PatientAddress, related_name="patient", blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251
    )

    eligibility_submission = deprecate_patient_detail_field(
        models.OneToOneField(
            EligibilitySubmission,
            related_name="patient",
            blank=True,
            null=True,
            # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
            on_delete=models.SET_NULL,  # noqa: TID251
        )
    )

    accepted_terms = deprecate_patient_detail_field(models.BooleanField(default=False, db_index=True))

    device_token = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True)  # noqa: TID251
    )  # Migrated to UserDevice
    device_push_enabled = deprecate_patient_detail_field(
        models.BooleanField(null=True, blank=True)
    )  # Migrated to UserDevice

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pubnub_channel = deprecate_patient_detail_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251

    account_verified = deprecate_patient_detail_field(models.BooleanField(default=False, db_index=True))

    forgot_password_verified_at = deprecate_patient_detail_field(models.DateTimeField(null=True, blank=True))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    forgot_password_code = deprecate_patient_detail_field(models.CharField(max_length=15, null=True, blank=True))  # noqa: TID251

    created_from = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=3, choices=CREATED_FROM_CHOICES, default=APP, blank=True, null=True)  # noqa: TID251
    )

    # Track how user heard about us; the _extra field is for freeform answers.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    heard_about_from = deprecate_patient_detail_field(models.CharField(max_length=63, blank=True, null=True))  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    heard_about_from_extra = deprecate_patient_detail_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251

    is_PEPM = deprecate_patient_detail_field(models.BooleanField(default=False))
    is_PEPM_RTW = deprecate_patient_detail_field(models.BooleanField(default=False))
    is_screening_only = deprecate_patient_detail_field(models.BooleanField(default=False))

    send_daily_covid_screen = deprecate_patient_detail_field(models.BooleanField(default=False))
    has_bp_cuff = deprecate_patient_detail_field(models.BooleanField(blank=True, null=True))
    # TODO: medical_equipment_data
    # Explore using a new Model once we know more about what we will be tracking
    # Store an array of: freeform device name, selectable device type
    medical_equipment_data = deprecate_patient_detail_field(models.JSONField(default=list, null=True, blank=True))
    risk_score = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=15, choices=RISK_SCORE_CHOICES, null=True, blank=True)  # noqa: TID251
    )

    employer = deprecate_patient_detail_field(
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        models.ForeignKey(to=Employer, null=True, blank=True, on_delete=models.SET_NULL)  # noqa: TID251
    )

    class Meta:
        db_table = "auth_user_patient_details"

    def save(self, *args, **kwargs):
        super(PatientDetail, self).save(*args, **kwargs)


class Person(SaveHandlersMixin, BaseModelV3, CoverageStateMachineMixin):
    # DO NOT CHANGE THIS. This controls whether person records can be deleted based on actions in Elation
    # We want lucian to be the source of truth for all user accounts. Deleting accounts in Lucian should still
    # delete the downstream elation chart if one exists.
    disallow_deletion_from_elation = True
    user = models.OneToOneField(User, related_name="person", on_delete=models.CASCADE, null=True, blank=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    email = models.CharField(max_length=255, unique=True, null=True, blank=True)  # noqa: TID251
    # The phone number field is currently maintained on both the Person and User models and kept in
    # sync between the two. In the future we will update this to be a single Many-to-Many field, but
    # for now. We currently set this value when someone signs up by calling Auth0's API and getting
    # the phone number used to enroll in MFA. A user's phone number may be updated if they change it
    # in the app, a staff member changes it in Lucian, or if they update their MFA enrollment in
    # Auth0 (at which point an Auth0 callback makes a change in our API). Note that when a phone
    # number is changed in the app or in Lucian, the user's MFA enrollment is not changed in Auth0.
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_number = models.CharField(max_length=17, null=True, blank=True)  # noqa: TID251
    dob = models.DateField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    hashed_full_ssn = models.CharField(max_length=100, null=True, blank=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    hashed_last_ssn = models.CharField(max_length=100, null=True, blank=True)  # noqa: TID251
    # Sex assigned at birth
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    sex = models.CharField(max_length=7, default="Unknown", blank=True, null=True)  # noqa: TID251
    gender = ArrayField(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(max_length=255, blank=True, null=True),  # noqa: TID251
        null=True,
        blank=True,
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pronouns = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    first_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    preferred_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    races = django_deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        ArrayField(models.CharField(max_length=255, blank=True, null=True), null=True, blank=True),  # noqa: TID251
        return_instead=(
            lambda: raise_exception(FieldError("Person.races is deprecated, use races_and_ethnicities instead"))
        ),
    )
    races_and_ethnicities = BaseModelV3ManyToManyField(
        PatientRace,
        through="PersonRaceEthnicity",
        related_name="+",
    )
    preferred_language = models.ForeignKey(
        PreferredLanguage,
        related_name="people",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    preferred_language_other = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    CREATED_FROM_CHOICES = [
        (PatientCreationSources.WEB, "Web"),
        (PatientCreationSources.APP, "App"),
        (PatientCreationSources.LUCIAN_ADD_PERSON, "Lucian - Add Person"),
    ]
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    created_from = models.CharField(max_length=3, blank=True, null=True, choices=CREATED_FROM_CHOICES)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    heard_about_from = models.CharField(max_length=63, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    heard_about_from_extra = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    insurance_info = models.OneToOneField(
        InsuranceMemberInfo,
        related_name="person",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    address = models.ForeignKey(PatientAddress, related_name="person", blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    employer = models.ForeignKey(Employer, related_name="person", blank=True, null=True, on_delete=models.SET_NULL)  # noqa: TID251
    # TODO: User can opt to enter an employer name that isn't listed in the app-- for now, store this here
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    employer_other = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    # TODO: Remove these fields after disabling dual-write and migrate to alias fields
    elation_id = models.BigIntegerField(unique=True, blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pubnub_channel = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # TODO: medical_equipment_data
    # Explore using a new Model once we know more about what we will be tracking
    # Store an array of: freeform device name, selectable device type
    medical_equipment_data = models.JSONField(default=list, null=True, blank=True)
    account_verified = models.BooleanField(default=False, db_index=True, null=True, blank=True)
    request_auth_for_phi = models.BooleanField(default=False)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    deleted_reason = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    provider_note = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    provider_note_last_updated_at = models.DateTimeField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    phone_verify_status = models.CharField(max_length=60, blank=True, null=True)  # noqa: TID251

    tenants = BaseModelV3ManyToManyField(
        Tenant,
        related_name="people",
        through="PersonTenants",
    )

    # Flag is set to enable notifications for appointments (currently used for missed only).
    # TODO: update default to True after running a backfill to change all existing values to True.
    enable_appt_notifications = models.BooleanField(default=True, blank=True, null=True)
    avatar = models.ImageField(null=True, blank=True, upload_to="person-avatars/")
    avatar_large = models.ImageField(null=True, blank=True, upload_to="person-avatars-large/")

    # Last successful api call to Zus ZAP
    refreshed_zus_patient_history_at = deprecate_field(models.DateTimeField(blank=True, null=True, default=None))
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    refreshed_zus_patient_history_job_id = deprecate_field(models.CharField(max_length=255, blank=True, null=True))  # noqa: TID251

    # define reverse accessors
    payer_roster_records: QuerySet["PayerRosterRecord"]
    provider_detail_patient_persons: QuerySet["ProviderDetailPatientPersons"]
    person_tenants: QuerySet["PersonTenants"]
    pod_people: QuerySet["PodPeople"]
    claims: QuerySet["Claim"]
    patientsharedfile_set: QuerySet["PatientSharedFile"]
    employee_eligibility: QuerySet["EligibilityRecord"]
    eligibilitylog_set: QuerySet["EligibilityLog"]
    covered_by: QuerySet["CoveredMember"]
    notes: QuerySet["Note"]
    phoneCall: QuerySet["PhoneCall"]
    program_info: QuerySet["ProgramInfo"]
    program_enrollments: QuerySet["ProgramEnrollment"]
    measurereport_set: QuerySet["MeasureReport"]
    search_availability: QuerySet["SearchAvailability"]
    priorauthorization_set: QuerySet["PriorAuthorization"]
    waiver_set: QuerySet["Waiver"]
    referral: QuerySet["Referral"]
    steerages: QuerySet["Steerage"]
    patienttransitionlog_set: QuerySet["PatientTransitionLog"]
    tasks: QuerySet["Task"]
    user_segmentation: QuerySet["UserSegmentation"]

    @property
    def ssn(self):
        return self.hashed_full_ssn

    @ssn.setter
    def ssn(self, value):
        ssn_salt = get_ssn_salt()
        # Expect inbound ssn to be either 4 or 9 digits. Only save SSN if salt is available.
        if value and ssn_salt:
            clean_ssn = clean_ssn_format(value)
            if len(clean_ssn) == 9:
                last_ssn = clean_ssn[-4:]
                # Save both full length and last 4 digits
                self.hashed_full_ssn = make_password(clean_ssn)
                self.hashed_last_ssn = make_password(last_ssn, salt=ssn_salt)
            elif len(clean_ssn) == 4:
                self.hashed_last_ssn = make_password(clean_ssn, salt=ssn_salt)

    @property
    def last_ssn(self):
        return self.hashed_last_ssn

    @last_ssn.setter
    def last_ssn(self, value):
        ssn_salt = get_ssn_salt()
        # Only save SSN if salt is available
        if value and len(value) == 4 and ssn_salt:
            clean_ssn = clean_ssn_format(value)
            self.hashed_last_ssn = make_password(clean_ssn, salt=ssn_salt)

    @property
    def elation_url(self):
        if self.elation_id:
            return f"{settings.ELATION['BASE_URL']}/patient/{self.elation_id}/"
        return None

    @property
    def async_elation_updates(self):
        return True

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def first_name_used(self):
        return self.preferred_name if self.preferred_name else self.first_name

    # For ElationPersonSync relationship
    # Elation requires a primary physician to create an account
    @property
    def primary_physician(self):
        return get_primary_physician(self)

    @primary_physician.setter
    def primary_physician(self, value):
        set_primary_physician(self, value)

    @property
    def caregiver_practice(self):
        return get_caregiver_practice(self)

    @caregiver_practice.setter
    def caregiver_practice(self, value):
        set_caregiver_practice(self, value)

    @property
    def risk_score(self):
        from firefly.modules.programs.primary_care.utils import get_risk_score

        return get_risk_score(self)

    @property
    def age(self) -> Optional[int]:
        if self.dob:
            today = date.today()
            return today.year - self.dob.year - ((today.month, today.day) < (self.dob.month, self.dob.day))
        return None

    @property
    def employee_identifier(self):
        return AliasMapping.get_alias_id_for_object(obj=self, alias_name=AliasName.EMPLOYEE_IDENTIFIER, pk=self.pk)

    @employee_identifier.setter
    def employee_identifier(self, value: Optional[str]):
        """
        This is dummy method to support getter property.
        The actual setter logic is in set_employee_identifier method.
        The match.first() in get_matching_person method, calls the fetchall()
        in django query, which inturn calls __iter__ method in ModelIterable
        which again calls setattr. The should not happen for employee_identifier field
        hence moving the setter to uitls.
        https://github.com/django/django/blob/c487634c106888effbba680e02269e489ab911a6/django/db/models/query.py#L128
        """
        pass

    @property
    def primary_subscriber_person(self):
        if (
            hasattr(self.insurance_info, "is_primary_subscriber") and not self.insurance_info.is_primary_subscriber
        ) and hasattr(self, "covered_members"):
            return self.covered_members.primary_subscriber_person
        return None

    @property
    def has_non_english_preferred_language(self):
        """
        Do we know that the Person has a preference for a language other than English?
        Used to make service exceptions, e.g. for scheduling Appointment interpreters
        or controlling access to Appointment types
        """
        if self.preferred_language and self.preferred_language.name != DEFAULT_PREFERRED_LANGUAGE:
            return True

        return False

    class Meta(BaseModelV3.Meta):
        db_table = "user_person"
        verbose_name_plural = "people"

    def pre_save_validation(self, changed):
        # Member accounts cannot be simultaneously used as Firefly Health staff
        # accounts. Prevent a Person from being linked to a User with a
        # ProviderDetail. A corresponding check exists on the ProviderDetail
        # model.
        if changed("user") and self.user and hasattr(self.user, "providerdetail"):
            raise PreSaveValidationError("Members accounts cannot also be providers")

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.elation.subscribers import (
            elation_update_user,
        )
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_person_change

        if (
            changed("id")
            or changed("dob")
            or changed("sex")
            or changed("first_name")
            or changed("last_name")
            or changed("phone_number")
            or changed("elation_id")
            or changed("deleted")
        ):
            logger.info("Person %d: Invoking elation update", self.id)
            elation_update_user.send(person_id=self.id)
        if (
            changed("id")
            or changed("email")
            or changed("first_name")
            or changed("last_name")
            or changed("phone_number")
            or changed("deleted")
        ):
            logger.info("Person %d: Invoking zus update", self.id)
            zus_handle_person_change.send(person_id=self.id)
        if changed("preferred_language"):
            from firefly.core.user.signals import create_case_for_preferred_language

            create_case_for_preferred_language(self)

    def save(self, *args, **kwargs):
        super(Person, self).save(*args, **kwargs)
        # Set default value for Pubnub key on creation
        # setting default tenant as firefly. For now hardcoded key
        if self.tenants.count() == 0:
            firefly_tenant, _ = Tenant.objects.get_or_create(key=FIREFLY_TENANT_KEY)
            try:
                self.tenants.add(firefly_tenant.id)
            except IntegrityError:
                logger.info("person %s has already linked to the tenant %s", self.id, firefly_tenant.id)

        if waffle.switch_is_active(WAFFLE_CREATE_CASE_FOR_NEW_INSURANCE_PLAN):
            if (
                self.insurance_info
                and self.insurance_info.insurance_plan
                and not self.insurance_info.insurance_plan.networks.exists()
            ):
                from firefly.modules.insurance.utils import create_case_for_new_insurance_plan

                # create a case for attaching an insurance plan to a member without netoworks
                create_case_for_new_insurance_plan(self.insurance_info.insurance_plan, self)


def get_default_state():
    default_state, _ = State.objects.get_or_create(
        abbreviation="MA", defaults={"name": "Massachusetts", "can_service": True}
    )
    return [default_state]


class ProviderDetail(SaveHandlersMixin, BaseModelV3):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)

    avatar = models.ImageField(null=True, blank=True, upload_to="provider-avatars/")
    avatar_large = models.ImageField(null=True, blank=True, upload_to="provider-avatars-large/")

    bio = models.TextField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    title = models.CharField(max_length=255, blank=True, help_text="Professional Designation e.g. NP, MD, MSc")  # noqa: TID251
    role = django_deprecate_field(
        # DO NOT COPY-PASTE: Prefer TextField over CharField
        models.CharField(  # noqa: TID251
            max_length=255,
            blank=True,
            help_text="Patient-facing e.g. Physician, Nurse Practitioner.",
        ),
        return_instead=(
            lambda: raise_exception(
                FieldError("role field in providerdetails is deprecated, use internal role instead")
            )
        ),
    )
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    pronouns = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    quote = models.TextField(blank=True, null=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    education = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    languages = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    testimonial = models.TextField(blank=True, null=True)
    is_external = models.BooleanField(default=False)
    is_taking_new_patients = models.BooleanField(default=True)
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    dea_number = models.CharField(max_length=255, null=True, blank=True)  # noqa: TID251

    patient_persons = BaseModelV3ManyToManyField(
        Person,
        related_name="care_team",
        blank=True,
        through="ProviderDetailPatientPersons",
    )

    physician = models.OneToOneField(
        Physician,
        related_name="provider",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    tenant = models.ForeignKey(
        Tenant,
        related_name="provider_details",
        on_delete=models.CASCADE,
    )

    internal_role = models.ForeignKey(
        Role,
        related_name="provider_internal_role_details",
        blank=True,
        null=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    first_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    last_name = models.CharField(max_length=255, blank=True, null=True)  # noqa: TID251

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def pre_save_validation(self, changed):
        # Firefly Health staff accounts cannot be simultaneously used as member
        # accounts. Prevent a User linked to a Person from also being linked to
        # a ProviderDetail. A corresponding check exists on the Person model.
        if changed("user") and hasattr(self.user, "person"):
            raise PreSaveValidationError("Provider account cannot also be members")

    def post_save_side_effect(self, changed, get_old_value_for_changed_field):
        from firefly.modules.change_data_capture.subscribers.zus.subscribers import zus_handle_provider_change

        if get_old_value_for_changed_field("user") is not None and (changed("last_name") or changed("first_name")):
            logger.info("User %d: Invoking zus update from provider data change", self.user.id)
            zus_handle_provider_change.send(user_id=self.user.id)

    class Meta(BaseModelV3.Meta):
        db_table = "auth_user_provider_details"


class ProviderDetailPatientPersons(BaseModelV3):
    providerdetail = models.ForeignKey(
        ProviderDetail,
        related_name="provider_detail_patient_persons",
        on_delete=models.CASCADE,
    )
    person = models.ForeignKey(
        Person,
        related_name="provider_detail_patient_persons",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "auth_user_provider_details_patient_persons"
        verbose_name_plural: str = "Provider Detail Patient Persons"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["providerdetail", "person"],
                condition=Q(deleted=None),
                name="auth_user_provider_details_patient_persons_uniq",
            )
        ]


# join table to track audit history for AssigneeGroup
class AssigneeGroupUser(BaseModelV3):
    group = models.ForeignKey(
        AssigneeGroup,
        related_name="assignee_groups",
        on_delete=models.CASCADE,
    )
    user = models.ForeignKey(
        User,
        related_name="assignee_groups",
        on_delete=models.CASCADE,
    )


class PersonTenants(BaseModelV3):
    tenant = models.ForeignKey(
        Tenant,
        related_name="person_tenants",
        on_delete=models.CASCADE,
    )
    person = models.ForeignKey(
        Person,
        related_name="person_tenants",
        on_delete=models.CASCADE,
    )

    class Meta:
        db_table: str = "user_person_tenants"
        unique_together: List[str] = []
        constraints = [
            models.UniqueConstraint(
                fields=["tenant", "person"], condition=Q(deleted=None), name="user_person_tenants_uniq"
            )
        ]


# This model captures the person preferences related to navigation
class PersonPreference(BaseModelV3):
    preference = models.TextField(null=True, blank=True)
    person = models.OneToOneField(
        Person,
        null=False,
        blank=False,
        related_name="person_preference",
        on_delete=models.CASCADE,
    )


class PersonRaceEthnicity(BaseModelV3):
    """
    Many-to-many join table between Person and PatientRace, representing the
    races and ethnicities with which the person identifies.
    """

    person = models.ForeignKey(
        Person,
        on_delete=models.CASCADE,
        related_name="+",
    )
    race_or_ethnicity = models.ForeignKey(
        PatientRace,
        on_delete=models.CASCADE,
        # Related name is required in order to filter out deleted entries when
        # using `Person.races_and_ethnicities`.
        related_name="person_races_and_ethnicities",
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["person", "race_or_ethnicity"],
                condition=Q(deleted=None),
                name="person_race_ethnicity_person_race_or_ethnicity_uniq",
            )
        ]
