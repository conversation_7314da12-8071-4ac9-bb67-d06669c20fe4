import logging
from typing import Optional

from django.conf import settings
from django.contrib.auth.models import Group
from django.db import transaction
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers

from firefly.core.alias.models import AliasMapping, AliasName
from firefly.core.redact_phi.serializers import RedactPHIListSerializer
from firefly.core.roles.models import Role
from firefly.core.user.models.models import AssigneeGroup, AssigneeGroupUser, PersonPreference
from firefly.core.user.utils import get_person_status
from firefly.modules.care_teams.models import CareTeamTemplate, CareTeamTemplatePhysicians
from firefly.modules.device.serializers import UserDeviceSerializer
from firefly.modules.facts.models import PatientRace, PreferredLanguage
from firefly.modules.firefly_django.serializers import (
    BaseSerializerV3,
    ReadWriteSerializerMethodField,
)
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import FormSubmission
from firefly.modules.insurance.models import Employer
from firefly.modules.insurance.serializers import (
    EmployerSerializer,
    InsuranceMemberInfoWithNetworkSerializer,
)
from firefly.modules.onboarding.serializers import OnboardingSignupSerializer
from firefly.modules.physician.models import Physician
from firefly.modules.pods.models import PodProviders
from firefly.modules.pods.serializers import PodSerializer
from firefly.modules.programs.primary_care.serializers import PrimaryCareProgramInfoSerializer
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import program_info_for_person_program
from firefly.modules.statemachines.coverage.serializers import CoverageSerializer
from firefly.modules.states.models import State
from firefly.modules.states.serializers import StateSerializer

from ..constants import CARE_TEAM_GROUPS, PATIENT_GROUP_NAME
from ..models import PatientAddress, Person, ProviderDetail, User

logger = logging.getLogger(__name__)

CASE_INSENSITIVE_KEY_MAP = {"is_pepm": "is_PEPM"}
SERVICEABLE_PROGRAMS = {ProgramCodes.PRIMARY_CARE, ProgramCodes.URGENT_CARE, ProgramCodes.BENEFIT}


def remap_problematic_keys(validated_data):
    for key in list(validated_data):
        mapped_key = CASE_INSENSITIVE_KEY_MAP.get(key.lower(), None)
        if mapped_key is not None:
            validated_data[mapped_key] = validated_data.pop(key)
    return validated_data


class GroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Group
        read_only_fields = ("id",)
        fields = ("name",)


class PublicProviderDetailWithGroupsSerializer(BaseSerializerV3):
    groups = GroupSerializer(many=True, read_only=True, source="user.groups")

    class Meta(BaseSerializerV3.Meta):
        model = ProviderDetail
        read_only_fields = BaseSerializerV3.Meta.read_only_fields
        fields = [
            "pk",
            "bio",
            "title",
            "avatar",
            "avatar_large",
            "pronouns",
            "groups",
            "is_taking_new_patients",
        ]


class PhysicianSerializerWithGroups(BaseSerializerV3):
    # title is included for backwards compatibility
    title = serializers.CharField(source="provider.title", read_only=True)
    provider = PublicProviderDetailWithGroupsSerializer(read_only=True)
    practicing_states = StateSerializer(many=True, read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = Physician
        fields = BaseSerializerV3.Meta.read_only_fields + [
            "first_name",
            "last_name",
            "specialty",
            "practice",
            "npi",
            "title",
            "provider",
            "practicing_states",
            "elation_user_id",
        ]


class CareTeamTemplatePhysicianSerializer(BaseSerializerV3):
    physician = PhysicianSerializerWithGroups()

    class Meta(BaseSerializerV3.Meta):
        model = CareTeamTemplatePhysicians
        fields = BaseSerializerV3.Meta.read_only_fields + ["physician"]


class CareTeamTemplateSerializer(BaseSerializerV3):
    primary_physician = PhysicianSerializerWithGroups()
    physicians = PhysicianSerializerWithGroups(many=True)
    licensed_in_patient_state = serializers.SerializerMethodField()
    license_pending_in_patient_state = serializers.SerializerMethodField()
    primary_physician_panel_size = serializers.IntegerField()

    @extend_schema_field(serializers.BooleanField())
    def get_licensed_in_patient_state(self, instance):
        # Queryset annotations add this attribute
        if hasattr(instance, "licensed_in_patient_state"):
            return instance.licensed_in_patient_state
        else:
            return False

    @extend_schema_field(serializers.BooleanField())
    def get_license_pending_in_patient_state(self, instance):
        # Queryset annotations add this attribute
        if hasattr(instance, "license_pending_in_patient_state"):
            return instance.license_pending_in_patient_state
        else:
            return False

    class Meta(BaseSerializerV3.Meta):
        model = CareTeamTemplate
        fields = BaseSerializerV3.Meta.read_only_fields + [
            "primary_physician",
            "physicians",
            "licensed_in_patient_state",
            "license_pending_in_patient_state",
            "primary_physician_panel_size",
        ]


class PhysicianPracticingStatesSerializer(BaseSerializerV3):
    practicing_state_ids: serializers.PrimaryKeyRelatedField = serializers.PrimaryKeyRelatedField(
        source="practicing_states", many=True, queryset=State.objects.all()
    )
    practicing_states = StateSerializer(many=True, read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = Physician
        fields = BaseSerializerV3.Meta.read_only_fields + [
            "practicing_states",
            "practicing_state_ids",
        ]


class PublicProviderDetailSerializer(BaseSerializerV3):
    internal_role = serializers.SerializerMethodField()

    class Meta(BaseSerializerV3.Meta):
        model = ProviderDetail
        read_only_fields = BaseSerializerV3.Meta.read_only_fields
        fields = ["pk", "bio", "title", "avatar", "avatar_large", "pronouns", "internal_role"]

    def get_internal_role(self, instance) -> Optional[str]:
        if hasattr(instance, "internal_role") and instance.internal_role:
            return instance.internal_role.role_label
        else:
            return None


class PhysicianSerializer(BaseSerializerV3):
    # title is included for backwards compatibility
    title = serializers.CharField(source="provider.title", read_only=True)
    provider = PublicProviderDetailSerializer(read_only=True)
    practicing_states = StateSerializer(many=True, read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = Physician
        fields = BaseSerializerV3.Meta.read_only_fields + [
            "first_name",
            "last_name",
            "specialty",
            "practice",
            "npi",
            "title",
            "provider",
            "practicing_states",
            "elation_user_id",
            "calendar_url",
        ]


class PodProvidersSerializer(BaseSerializerV3):
    pod = PodSerializer(read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = PodProviders
        fields = BaseSerializerV3.Meta.read_only_fields + ["pod"]


class CareTeamSerializer(BaseSerializerV3):
    group = serializers.SerializerMethodField()
    physician_name_with_title = serializers.SerializerMethodField()

    class Meta(BaseSerializerV3.Meta):
        model = User
        fields = BaseSerializerV3.Meta.fields + ["first_name", "last_name", "group", "physician_name_with_title"]

    def get_group(self, instance):
        instance_user_groups = instance.groups.values_list("name", flat=True)
        return next(iter([x for x in instance_user_groups if x in CARE_TEAM_GROUPS]), None)

    def get_physician_name_with_title(self, instance):
        from firefly.bff.app.utils import get_appointment_physician_name

        return get_appointment_physician_name(instance.providerdetail.physician)


class PatientAddressSerializer(BaseSerializerV3):
    class Meta(BaseSerializerV3.Meta):
        model = PatientAddress
        read_only_fields = BaseSerializerV3.Meta.read_only_fields
        fields = [
            "street_address",
            "street_address_2",
            "city",
            "state",
            "zip",
        ]


class AssigneeGroupSerializer(BaseSerializerV3):
    assignee_group_users = serializers.SerializerMethodField()
    is_group = serializers.SerializerMethodField()

    class Meta(BaseSerializerV3.Meta):
        model = AssigneeGroup
        read_only_fields = BaseSerializerV3.Meta.read_only_fields
        fields = ["name", "is_provider", "assignee_group_users", "is_group"]

    def get_assignee_group_users(self, obj):
        return [assignee_group_user.user_id for assignee_group_user in obj.assignee_groups.all()]

    def get_is_group(self, obj) -> bool:
        try:
            group = obj.user
        except User.DoesNotExist:
            group = None
        return False if group is not None else True


class AssigneeGroupUserSerializer(BaseSerializerV3):
    class Meta(BaseSerializerV3.Meta):
        model = AssigneeGroupUser
        read_only_fields = BaseSerializerV3.Meta.read_only_fields
        fields = ["group", "user"]


class PersonDemographicsSerializer(BaseSerializerV3):
    """A limited set of person fields used for patient search."""

    class Meta(BaseSerializerV3.Meta):
        model = Person
        fields = [
            "id",
            "first_name",
            "last_name",
            "phone_number",
            "dob",
            "sex",
            "request_auth_for_phi",
            "user_id",
        ]


class LimitedPersonReadSerializer(BaseSerializerV3):
    """A limited set of person fields returned in list views."""

    coverage = CoverageSerializer(required=False, read_only=True)
    state = serializers.CharField(source="insurance_member_info.state", read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = Person
        fields = [
            "first_name",
            "last_name",
            "coverage",
            "state",
            "elation_id",
            "elation_url",
            "request_auth_for_phi",
        ]


class UserCreateSerializer(BaseSerializerV3):
    """A limited set of user fields available to set for public user creation."""

    class Meta(BaseSerializerV3.Meta):
        model = User
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + ["is_patient"]
        fields = BaseSerializerV3.Meta.fields + [
            "is_patient",
            "first_name",
            "last_name",
            "phone_number",
        ]


class ProviderDetailSerializer(BaseSerializerV3):
    practicing_states = StateSerializer(source="physician.practicing_states", many=True, read_only=True)
    tenant_key = serializers.CharField(read_only=True, source="tenant.key")

    internal_role: "serializers.SlugRelatedField[Role]" = serializers.SlugRelatedField(
        many=False, read_only=True, slug_field="role_name"
    )
    pod_providers = PodProvidersSerializer(many=True, read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = ProviderDetail
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + [
            "created_at",
            "internal_role",
            "pod_providers",
            "practicing_states",
            "tenant_key",
            "updated_at",
            "updated_by",
        ]
        fields = read_only_fields + [
            "avatar",
            "avatar_large",
            "bio",
            "first_name",
            "is_external",
            "is_taking_new_patients",
            "last_name",
            "pronouns",
            "physician_id",
            "tenant",
            "title",
        ]


class LimitedProviderViewSerializer(BaseSerializerV3):
    title = serializers.CharField(source="providerdetail.title", read_only=True)
    bio = serializers.CharField(source="providerdetail.bio", read_only=True)
    avatar = serializers.ImageField(source="providerdetail.avatar", read_only=True)
    avatar_large = serializers.ImageField(source="providerdetail.avatar_large", read_only=True)
    internal_role = serializers.SerializerMethodField()
    # NOTE (Franklin): Include physician_id in this serializer
    # in case we need to map physician ids to providers in the frontend
    physician_id = serializers.IntegerField(source="providerdetail.physician.id", read_only=True)
    # Provider profile fields
    pronouns = serializers.CharField(source="providerdetail.pronouns", read_only=True)
    quote = serializers.CharField(source="providerdetail.quote", read_only=True)
    education = serializers.CharField(source="providerdetail.education", read_only=True)
    languages = serializers.CharField(source="providerdetail.languages", read_only=True)
    testimonial = serializers.CharField(source="providerdetail.testimonial", read_only=True)
    # Should default to None for non-MD providers.
    care_team_template = CareTeamTemplateSerializer(
        source="providerdetail.physician.care_team_templates_as_primary",
        read_only=True,
        allow_null=True,
    )
    first_name = serializers.CharField(source="providerdetail.first_name", read_only=True)
    last_name = serializers.CharField(source="providerdetail.last_name", read_only=True)

    class Meta(BaseSerializerV3.Meta):
        model = User
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + [
            "first_name",
            "last_name",
            "title",
            "internal_role",
            "bio",
            "avatar",
            "avatar_large",
            "pronouns",
            "quote",
            "education",
            "languages",
            "testimonial",
            "care_team_template",
            "is_active",
        ]
        fields = BaseSerializerV3.Meta.fields + [
            "first_name",
            "last_name",
            "title",
            "internal_role",
            "bio",
            "avatar",
            "avatar_large",
            "physician_id",
            "pronouns",
            "quote",
            "education",
            "languages",
            "testimonial",
            "care_team_template",
            "is_active",
        ]

    def get_internal_role(self, instance) -> Optional[str]:
        if (
            hasattr(instance, "providerdetail")
            and instance.providerdetail
            and hasattr(instance.providerdetail, "internal_role")
            and instance.providerdetail.internal_role
        ):
            return instance.providerdetail.internal_role.role_label
        else:
            return None


class LimitedUserSerializer(RedactPHIListSerializer, UserCreateSerializer):
    """A limited set of user fields returned in list views."""

    person = LimitedPersonReadSerializer(required=False)

    class Meta(UserCreateSerializer.Meta):
        fields = UserCreateSerializer.Meta.fields + [
            "email",
            "test_only",
            "is_active",
            "person",
        ]
        redact_phi_test_path = ["person", "request_auth_for_phi"]

        redact_phi_field_paths = [["first_name"], ["last_name"], ["email"], ["phone_number"]]


class UserDemographicsSerializer(serializers.Serializer):
    """A set of read-only user fields used for patient search."""

    def to_representation(self, user: "User"):
        if not hasattr(user, "person"):
            return {}
        return {
            "id": user.id,
            "first_name": user.person.first_name,
            "last_name": user.person.last_name,
            "phone_number": user.person.phone_number,
            "dob": user.person.dob,
            "sex": user.person.sex,
            "request_auth_for_phi": user.person.request_auth_for_phi,
        }


class PersonPreferredLanguageSerializer(BaseSerializerV3):
    class Meta(BaseSerializerV3.Meta):
        model = PreferredLanguage
        fields = ["name", "label"]


class PersonSerializer(BaseSerializerV3):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    address = PatientAddressSerializer(required=False, read_only=True)
    chat_channel = serializers.SerializerMethodField()
    coverage = CoverageSerializer(required=False, read_only=True)
    ehr_id = serializers.SerializerMethodField()
    ehr_url = serializers.SerializerMethodField()
    employer: serializers.PrimaryKeyRelatedField = serializers.PrimaryKeyRelatedField(
        allow_null=True,
        required=False,
        queryset=Employer.objects.all(),
    )
    employer_info = EmployerSerializer(
        allow_null=True,
        required=False,
        source="employer",
    )
    member_identifier_type = serializers.SerializerMethodField(read_only=True, required=False)
    insurance_info = InsuranceMemberInfoWithNetworkSerializer(read_only=True, required=False)
    insurance_form_id = serializers.SerializerMethodField(read_only=True, required=False, allow_null=True)
    primary_care_program_info = ReadWriteSerializerMethodField(required=False, allow_null=True)
    preferred_language_info = PersonPreferredLanguageSerializer(
        required=False, read_only=True, source="preferred_language"
    )

    class PatientRaceSerializer(serializers.ModelSerializer):
        """
        Very limited serializer mainly for nested writes.
        """

        id = serializers.IntegerField()

        class Meta:
            model = PatientRace
            fields = ["id"]

    races_and_ethnicities = PatientRaceSerializer(many=True, required=False, allow_null=True)

    class Meta(BaseSerializerV3.Meta):
        model = Person
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + [
            "address",
            "care_team",
            "chat_channel",
            "coverage",
            "ehr_id",
            "ehr_url",
            "elation_id",
            "elation_url",
            "employee_identifier",
            "insurance_info",
            "insurance_form_id",
            "phone_number",
            "preferred_language_info",
            "pubnub_channel",
            "request_auth_for_phi",
            "member_identifier_type",
        ]
        fields = read_only_fields + [
            "account_verified",
            "created_from",
            "dob",
            "employer",
            "employer_other",
            "employer_info",
            "first_name",
            "gender",
            "heard_about_from",
            "heard_about_from_extra",
            "last_name",
            "medical_equipment_data",
            "preferred_name",
            "primary_care_program_info",
            "pronouns",
            "sex",
            "provider_note",
            "provider_note_last_updated_at",
            "avatar",
            "avatar_large",
            "races_and_ethnicities",
            "preferred_language",
            "preferred_language_other",
        ]

    # DO NOT COPY-PASTE - transaction.atomic considered harmful
    @transaction.atomic  # noqa: TID251
    def create(self, validated_data):
        validated_data = remap_problematic_keys(validated_data)
        insurance_info_data = validated_data.pop("insurance_info", None)
        insurance_info = self.fields["insurance_info"].create(insurance_info_data) if insurance_info_data else None
        address_data = validated_data.pop("address", None)
        address = self.fields["address"].create(address_data) if address_data else None
        employer_data = validated_data.pop("employer", None)
        employer = self.fields["employer"].create(employer_data) if employer_data else None
        person = Person.objects.create(
            insurance_info=insurance_info, address=address, employer=employer, **validated_data
        )
        tenant_id = self.context.get("tenant_id")
        if tenant_id:
            person.tenants.set([tenant_id])
        return person

    def update(self, instance, validated_data):
        validated_data = remap_problematic_keys(validated_data)
        primary_care_data = validated_data.pop("primary_care_program_info", None)
        # TODO: medical_equipment_data
        # Do basic validation here while this is a JSON field and not its own model
        if "medical_equipment_data" in validated_data:
            val = validated_data["medical_equipment_data"]
            if not isinstance(val, list):
                raise serializers.ValidationError({"medical_equipment_data": f"Expected list, got f{type(val)}"})
            for item in val:
                if item.get("name") is None or item.get("type") is None:
                    raise serializers.ValidationError(
                        {"medical_equipment_data": "Expected a 'name' and 'type' value for each item in the list"}
                    )
        if primary_care_data:
            self._update_primary_care_program_info(primary_care_data)
        races_and_ethnicities = validated_data.pop("races_and_ethnicities", None)
        if races_and_ethnicities:
            instance.races_and_ethnicities.set(
                PatientRace.objects.filter(
                    id__in=[race_or_ethnicity["id"] for race_or_ethnicity in races_and_ethnicities]
                )
            )

        return super().update(instance, validated_data)

    def get_chat_channel(self, data):
        if self.instance:
            return AliasMapping.get_alias_id_for_object(
                obj=self.instance, alias_name=AliasName.PUBNUB_CHANNEL, pk=self.instance.pk
            )

    def get_ehr_id(self, data):
        if self.instance:
            return self._get_ehr_id()

    def get_ehr_url(self, data):
        ehr_id = self._get_ehr_id()
        if ehr_id:
            return f"{settings.ELATION['BASE_URL']}/patient/{ehr_id}/"

    def get_insurance_form_id(self, data) -> Optional[int]:
        if hasattr(data, "user") and data.user is not None:
            form_submissions = (
                FormSubmission.objects.filter(user_id=data.user.id)
                .filter(form__uid=FormUID.INSURANCE_FORM)
                .order_by("-created_at")
            )
            if form_submissions:
                logger.debug("insurance form submission id is %s", form_submissions[0].id)
                return form_submissions[0].id
        logger.debug(
            "insurance form submission id is none",
        )
        return None

    def get_primary_care_program_info(self, data):
        primary_care_program_info = program_info_for_person_program(person=data, program_uid=ProgramCodes.PRIMARY_CARE)
        return PrimaryCareProgramInfoSerializer(primary_care_program_info).data

    def _get_ehr_id(self):
        if self.instance:
            return AliasMapping.get_alias_id_for_object(
                obj=self.instance, alias_name=AliasName.ELATION_ID, pk=self.instance.pk
            )
        return None

    def get_member_identifier_type(self, data) -> Optional[str]:
        if data and hasattr(data, "attribution") and data.attribution and data.attribution.contract:
            return data.attribution.contract.member_identifier_type
        logger.debug(
            "Failed to get member contract",
        )
        return None

    def _update_primary_care_program_info(self, primary_care_data):
        # Pass in the request context into child serializer
        primary_care_program_info = program_info_for_person_program(self.instance, ProgramCodes.PRIMARY_CARE)
        if primary_care_program_info:
            primary_care_serializer = PrimaryCareProgramInfoSerializer(
                primary_care_program_info,
                data=primary_care_data,
                context={"request": self.context.get("request")},
            )
            primary_care_serializer.is_valid(raise_exception=True)
            primary_care_serializer.save()


class PersonCRUDSerializer(PersonSerializer):
    """
    This serializer contains email and user in addition to PersonSerializer
    and is used for CRUD operation on Person
    """

    address = PatientAddressSerializer(required=False)
    insurance_info = InsuranceMemberInfoWithNetworkSerializer(required=False)

    class Meta(PersonSerializer.Meta):
        fields = PersonSerializer.Meta.fields + ["email", "user"]


class UserSerializer(BaseSerializerV3):
    """
    Used by Lucian for the "me" object, provider list views, etc.
    """

    provider_fields = ProviderDetailSerializer(source="providerdetail", required=False)
    groups = GroupSerializer(many=True, read_only=True)
    onboarding_status = serializers.CharField(source="onboarding_state.status", read_only=True)
    onboarding_state = OnboardingSignupSerializer(required=False, read_only=True)
    userdevices = UserDeviceSerializer(many=True, read_only=True)
    person = PersonSerializer(required=False)
    programs = serializers.SerializerMethodField(required=False)
    assignee_group = AssigneeGroupSerializer(required=False)
    user_status = serializers.SerializerMethodField()

    class Meta(BaseSerializerV3.Meta):
        model = User
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + [
            "auth_id",
            "groups",
            "is_active",
            "is_patient",
            "is_provider",
            "onboarding_state",
            "onboarding_status",
            "phone_number",
            "programs",
            "test_only",
            "userdevices",
            "user_status",
        ]
        fields = (
            BaseSerializerV3.Meta.fields
            + read_only_fields
            + [
                "assignee_group",
                "first_name",
                "last_name",
                "email",
                "person",
                "provider_fields",
            ]
        )
        extra_kwargs = {"password": {"write_only": True}}

    def get_user_status(self, obj):
        if self.instance:
            return get_person_status(self.instance)

    def update(self, instance, validated_data):
        provider_fields_data = validated_data.pop("providerdetail", None)
        person_fields_data = validated_data.pop("person", None)
        validated_data.pop("password", None)
        if person_fields_data:
            if hasattr(instance, "person"):
                if "preferred_language" in person_fields_data:
                    person_fields_data["preferred_language"] = getattr(
                        person_fields_data["preferred_language"], "id", None
                    )
                if "insurance_info" in person_fields_data:
                    person_fields_data["insurance_info"] = getattr(person_fields_data["insurance_info"], "id", None)
                if "employer" in person_fields_data:
                    person_fields_data["employer"] = getattr(
                        person_fields_data["employer"],
                        "id",
                        self.initial_data["person"]["employer"],
                    )
                if "first_name" in validated_data:
                    person_fields_data["first_name"] = validated_data["first_name"]
                if "last_name" in validated_data:
                    person_fields_data["last_name"] = validated_data["last_name"]
                if "email" in validated_data:
                    person_fields_data["email"] = validated_data["email"]
                if "phone_number" in validated_data:
                    person_fields_data["phone_number"] = validated_data["phone_number"]

                person_serialized = PersonCRUDSerializer(
                    instance.person,
                    data=person_fields_data,
                    partial=True,
                    context={"request": self.context["request"]},
                )

                person_serialized.is_valid(raise_exception=True)
                person_serialized.save()
            else:
                Person.objects.create(user=instance, **person_fields_data)
                patient_group = Group.objects.get(name=PATIENT_GROUP_NAME)
                instance.groups.add(patient_group)
        if provider_fields_data:
            if hasattr(instance, "providerdetail"):
                provider_serialized = ProviderDetailSerializer(
                    instance.providerdetail,
                    data=provider_fields_data,
                    partial=True,
                    context={"request": self.context["request"]},
                )
                provider_serialized.is_valid(raise_exception=True)
                provider_serialized.save()
            else:
                ProviderDetail.objects.create(user=instance, **provider_fields_data)
                provider_group = Group.objects.get(name="Provider")
                instance.groups.add(provider_group)
        return super(UserSerializer, self).update(instance, validated_data)

    @extend_schema_field(serializers.ListField(child=serializers.CharField()))
    def get_programs(self, obj):
        try:
            return obj.person.program_info.filter(program__uid__in=SERVICEABLE_PROGRAMS).values_list(
                "program__uid", flat=True
            )
        except Exception:
            return []


# TODO: Remove ProviderCreateSerializer becauase an admin is used to creater provider and Staff.
# the api is no longer user expect the test case
class ProviderCreateSerializer(UserSerializer):
    class Meta(UserSerializer.Meta):
        model = User
        read_only_fields = BaseSerializerV3.Meta.read_only_fields + [
            "auth_id",
            "groups",
            "is_active",
            "is_patient",
            "is_provider",
            "onboarding_state",
            "onboarding_status",
            "person",
            "programs",
            "test_only",
            "userdevices",
            "user_status",
        ]
        fields = (
            BaseSerializerV3.Meta.fields
            + read_only_fields
            + [
                "assignee_group",
                "email",
                "first_name",
                "last_name",
                "phone_number",
                "provider_fields",
            ]
        )
        extra_kwargs = {"password": {"write_only": True}}

    # DO NOT COPY-PASTE - transaction.atomic considered harmful
    @transaction.atomic  # noqa: TID251
    def create(self, validated_data):
        provider_fields_data = validated_data.pop("providerdetail", None)

        # TODO (Kunal): Once we fully deprecate name fields on User, we should change how the /provider endpoint is
        # called appropriately
        provider_fields_data["first_name"] = validated_data["first_name"]
        provider_fields_data["last_name"] = validated_data["last_name"]
        # TODO (Kunal): Remove first_name and last_name from user creation
        user = super().create(validated_data)
        provider_fields_data.update({"user": user})
        if provider_fields_data:
            ProviderDetail.objects.create(**provider_fields_data)
        provider_group = Group.objects.get(name="Provider")
        user.groups.add(provider_group)
        return user


class ProviderProfileSerializer(PublicProviderDetailSerializer):
    # Should default to None for non-MD providers
    care_team_template = CareTeamTemplateSerializer(
        source="physician.care_team_templates_as_primary",
        read_only=True,
        allow_null=True,
    )

    class Meta(PublicProviderDetailSerializer.Meta):
        model = ProviderDetail
        fields = PublicProviderDetailSerializer.Meta.fields + [
            "first_name",
            "last_name",
            "quote",
            "education",
            "languages",
            "testimonial",
            "care_team_template",
            "physician_id",
        ]


class ChatProviderSerializer(LimitedProviderViewSerializer):
    in_care_team = serializers.SerializerMethodField()
    last_sent_at = serializers.SerializerMethodField()

    class Meta(LimitedProviderViewSerializer.Meta):
        model = User
        fields = LimitedProviderViewSerializer.Meta.fields + [
            "in_care_team",
            "last_sent_at",
        ]

    def get_in_care_team(self, obj):
        try:
            return obj.in_care_team
        except Exception:
            return None

    def get_last_sent_at(self, obj):
        try:
            return obj.last_sent_at
        except Exception:
            return None


class PersonPreferenceSerializer(BaseSerializerV3):
    class Meta(BaseSerializerV3.Meta):
        model = PersonPreference
        fields = BaseSerializerV3.Meta.fields + ["person", "preference"]
