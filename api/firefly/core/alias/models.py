import logging
from typing import List, Optional, cast

from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Q

from firefly.modules.firefly_django.models import BaseModelV3
from firefly.modules.tenants.models import Tenant

logger = logging.getLogger(__name__)


def get_content_type(obj):
    return ContentType.objects.get_for_model(obj)


class AliasName:
    BCBS = "BCBS"
    BCBS_PREFIX = "BCBSPrefix"
    CAPITAL_RX = "CapitalRx"
    ELATION_ID = "ElationId"
    FLUME = "Flume"
    GOOGLE = "Google"
    # TODO: this should not be persisted (NT-243), drop these records
    PUBNUB_CHANNEL = "PubNubChannel"
    RIBBON = "Ribbon"
    RIBBON_PROCEDURE = "RibbonProcedure"
    SLACK = "Slack"
    TEST = "Test"
    TALKDESK = "Talkdesk"
    TALON = "Talon"
    ZINGLE = "Zingle"
    ZUS = "Zus"
    ZUS_PATIENT = "ZusPatient"
    ZUS_SERVICE_REQUEST = "ZusServiceRequest"
    OPSGENIE = "OpsGenie"
    BCBS_SYNTHETIC_ID = "BCBSSyntheticID"
    EMPLOYEE_IDENTIFIER = "EmployeeIdentifier"
    ELATION_REFERRAL_ORDER = "ElationReferralOrder"
    ELATION_REFERRAL_LETTER = "ElationReferralLetter"
    BRAZE_CAMPAIGN_ID = "BrazeCampaignId"
    # Generated by the device of the member. Corresponds to the id generated by the device for a calendar entry
    CALENDAR_EVENT_ID = "CalendarEventId"

    @classmethod
    def has_multiple_aliases(cls, alias: str) -> bool:
        return alias in {cls.BCBS_PREFIX, cls.RIBBON_PROCEDURE}

    @classmethod
    def requires_tenant(cls, alias: str) -> bool:
        return alias in {cls.ZUS, cls.ZUS_PATIENT}


class AliasMapping(BaseModelV3):
    @staticmethod
    def get_alias_ids(
        content_type: ContentType,
        obj_ids: list,
        alias_name: Optional[str] = None,
        tenant_obj: Optional[Tenant] = None,
    ):
        if alias_name and AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requires a tenant.", alias_name)
        if alias_name and not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        all_alias_ids = AliasMapping.objects.filter(
            content_type=content_type, object_id__in=obj_ids, alias_name=alias_name
        ).order_by("alias_id")
        if tenant_obj:
            all_alias_ids = all_alias_ids.filter(tenant=tenant_obj)
        else:
            all_alias_ids = all_alias_ids.filter(tenant__isnull=True)
        return list(all_alias_ids.values_list("alias_id", flat=True))

    @staticmethod
    def get_all_alias_ids_for_object(
        obj: models.Model, alias_name: str, pk=None, tenant_obj: Optional[Tenant] = None
    ) -> List[str]:
        if AliasName.has_multiple_aliases(alias_name) is False:
            raise Exception("Use get_alias_id_for_object for objects with one allowed aliases")
        if AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requres a tenant.", alias_name)
        if not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        all_alias_ids = AliasMapping.objects.filter(
            content_type=get_content_type(obj),
            object_id=(pk or obj.pk),
            alias_name=alias_name,
        )
        if tenant_obj:
            all_alias_ids = all_alias_ids.filter(tenant=tenant_obj)
        else:
            all_alias_ids = all_alias_ids.filter(tenant__isnull=True)
        return list(all_alias_ids.values_list("alias_id", flat=True))

    @staticmethod
    def get_alias_id_for_object(
        obj: models.Model, alias_name: str, pk=None, tenant_obj: Optional[Tenant] = None
    ) -> Optional[str]:
        if AliasName.has_multiple_aliases(alias_name):
            raise Exception("Use get_all_alias_ids_for_object for objects with multiple aliases")
        if AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requres a tenant.", alias_name)
        if not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        all_alias_ids_queryset = AliasMapping.objects.filter(
            content_type=get_content_type(obj),
            object_id=(pk or obj.pk),
            alias_name=alias_name,
        )
        if tenant_obj:
            all_alias_ids_queryset = all_alias_ids_queryset.filter(tenant=tenant_obj)
        else:
            all_alias_ids_queryset = all_alias_ids_queryset.filter(tenant__isnull=True)
        all_alias_ids: List[str] = cast(List[str], list(all_alias_ids_queryset.values_list("alias_id", flat=True)))
        if all_alias_ids and len(all_alias_ids) > 0:
            return all_alias_ids[0]
        else:
            return None

    @staticmethod
    def set_mapping(
        content_type: ContentType,
        obj_id: int,
        alias_name: str,
        alias_id: str,
        tenant_obj: Optional[Tenant] = None,
    ) -> Optional["AliasMapping"]:
        if AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requres a tenant.", alias_name)
        if not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        allow_multiple_aliases = AliasName.has_multiple_aliases(alias_name)
        # Look for an existing mapping for the alias_id, if its a different object, delete it
        mapping: Optional["AliasMapping"] = None
        try:
            if tenant_obj:
                mapping = AliasMapping.all_objects.get(
                    content_type=content_type,
                    alias_name=alias_name,
                    alias_id=alias_id,
                    tenant=tenant_obj,
                )
            else:
                mapping = AliasMapping.all_objects.get(
                    content_type=content_type,
                    alias_name=alias_name,
                    alias_id=alias_id,
                    tenant__isnull=True,
                )
        except AliasMapping.DoesNotExist:
            pass
        if mapping is not None:
            if mapping.object_id != obj_id:
                logger.info(
                    "Deleting alias mapping for %s-%s for %s ...was %s mapped to %s",
                    content_type,
                    obj_id,
                    alias_name,
                    mapping.alias_id,
                    mapping.object_id,
                )
                mapping.delete()
            elif mapping.deleted is not None:
                mapping.undelete()

        # If multiple mappings are not allowed
        # Look for existing mapping for the object
        # if its missing create it, if different,update it
        if allow_multiple_aliases is False:
            try:
                if tenant_obj:
                    mapping = AliasMapping.all_objects.get(
                        content_type=content_type,
                        object_id=obj_id,
                        alias_name=alias_name,
                        tenant=tenant_obj,
                    )
                else:
                    mapping = AliasMapping.all_objects.get(
                        content_type=content_type,
                        object_id=obj_id,
                        alias_name=alias_name,
                        tenant__isnull=True,
                    )
                if mapping is not None and mapping.deleted is not None:
                    mapping.undelete()
            except AliasMapping.DoesNotExist:
                logger.info(
                    "Creating new alias mapping for %s-%s for %s...to %s",
                    content_type,
                    obj_id,
                    alias_name,
                    alias_id,
                )
                if tenant_obj:
                    mapping, _ = AliasMapping.objects.update_or_create(
                        content_type=content_type,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        tenant=tenant_obj,
                        defaults={"object_id": obj_id},
                    )
                else:
                    mapping, _ = AliasMapping.objects.update_or_create(
                        content_type=content_type,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        defaults={"object_id": obj_id},
                    )

            assert mapping is not None
            if mapping.alias_id != alias_id:
                logger.info(
                    "Updating existing alias mapping for %s-%s for %s...to %s from %s",
                    content_type,
                    obj_id,
                    alias_name,
                    alias_id,
                    mapping.alias_id,
                )
                mapping.alias_id = alias_id
                mapping.save()

        else:
            try:
                if tenant_obj:
                    mapping = AliasMapping.all_objects.get(
                        content_type=content_type,
                        object_id=obj_id,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        tenant=tenant_obj,
                    )
                else:
                    mapping = AliasMapping.all_objects.get(
                        content_type=content_type,
                        object_id=obj_id,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        tenant__isnull=True,
                    )
                if mapping is not None and mapping.deleted is not None:
                    mapping.undelete()
            except AliasMapping.DoesNotExist:
                logger.info(
                    "Creating alias mapping for %s-%s for %s...to %s",
                    content_type,
                    obj_id,
                    alias_name,
                    alias_id,
                )
                if tenant_obj:
                    mapping, _ = AliasMapping.objects.update_or_create(
                        content_type=content_type,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        tenant=tenant_obj,
                        defaults={"object_id": obj_id},
                    )
                else:
                    mapping, _ = AliasMapping.objects.update_or_create(
                        content_type=content_type,
                        alias_name=alias_name,
                        alias_id=alias_id,
                        defaults={"object_id": obj_id},
                    )
        return mapping

    @staticmethod
    def set_mapping_by_object(
        obj: models.Model,
        alias_name: str,
        alias_id: str,
        pk=None,
        tenant_obj: Optional[Tenant] = None,
    ) -> Optional["AliasMapping"]:
        return AliasMapping.set_mapping(
            obj_id=(pk or obj.pk),
            content_type=get_content_type(obj),
            alias_name=alias_name,
            alias_id=alias_id,
            tenant_obj=tenant_obj,
        )

    @staticmethod
    def get_alias_mapping_for_alias(
        alias_name: str,
        alias_id: str,
        content_type: ContentType,
        tenant_obj: Optional[Tenant] = None,
    ) -> "AliasMapping":
        if AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requres a tenant.", alias_name)
        if not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        if tenant_obj:
            return AliasMapping.objects.get(
                alias_id=alias_id,
                alias_name=alias_name,
                content_type=content_type,
                tenant=tenant_obj,
            )
        else:
            return AliasMapping.objects.get(
                alias_id=alias_id,
                alias_name=alias_name,
                content_type=content_type,
                tenant__isnull=True,
            )

    @staticmethod
    def delete_mapping(
        content_type: ContentType,
        obj_id: int,
        alias_name: Optional[str] = None,
        tenant_obj: Optional[Tenant] = None,
    ):
        if alias_name and AliasName.requires_tenant(alias_name) and not tenant_obj:
            raise Exception("The alias name %s requres a tenant.", alias_name)
        if alias_name and not AliasName.requires_tenant(alias_name) and tenant_obj:
            raise Exception("The alias name %s does not support tenancy.", alias_name)
        if tenant_obj:
            AliasMapping.objects.filter(
                content_type=content_type,
                object_id=obj_id,
                alias_name=alias_name,
                tenant=tenant_obj,
            ).delete()
        else:
            AliasMapping.objects.filter(
                content_type=content_type,
                object_id=obj_id,
                alias_name=alias_name,
            ).delete()

    @staticmethod
    def delete_mapping_for_object(
        obj: models.Model, alias_name: Optional[str] = None, tenant_obj: Optional[Tenant] = None
    ):
        AliasMapping.delete_mapping(
            content_type=get_content_type(obj),
            obj_id=obj.pk,
            alias_name=alias_name,
            tenant_obj=tenant_obj,
        )

    ALIAS_NAME_CHOICES = (
        (AliasName.BCBS, "BCBS"),
        (AliasName.BCBS_PREFIX, "BCBSPrefix"),
        (AliasName.ELATION_ID, "ElationId"),
        (AliasName.CAPITAL_RX, "CapitalRx"),
        (AliasName.FLUME, "Flume"),
        (AliasName.GOOGLE, "Google"),
        (AliasName.PUBNUB_CHANNEL, "PubNubChannel"),
        (AliasName.RIBBON, "Ribbon"),
        (AliasName.RIBBON_PROCEDURE, "RibbonProcedure"),
        (AliasName.SLACK, "Slack"),
        (AliasName.TEST, "Test"),
        (AliasName.TALKDESK, "Talkdesk"),
        (AliasName.TALON, "TALON"),
        (AliasName.ZINGLE, "Zingle"),
        (AliasName.ZUS, "Zus"),
        (AliasName.ZUS_PATIENT, "ZusPatient"),
        (AliasName.ZUS_SERVICE_REQUEST, "ZusServiceRequest"),
        (AliasName.EMPLOYEE_IDENTIFIER, "EmployeeIdentifier"),
        (AliasName.ELATION_REFERRAL_ORDER, "ElationReferralOrder"),
        (AliasName.BRAZE_CAMPAIGN_ID, "BrazeCampaignId"),
        (AliasName.ELATION_REFERRAL_LETTER, "ElationReferralLetter"),
        # appointment add to calendar event id, used for schedule, cancel or reschedule appointment event
        (AliasName.CALENDAR_EVENT_ID, "CalendarEventId"),
    )

    # Firefly entity
    content_type: models.Field = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id: models.Field = models.PositiveIntegerField()
    content_object = GenericForeignKey("content_type", "object_id")

    # DO NOT COPY-PASTE: Prefer TextField over CharField
    alias_name: models.Field = models.CharField(max_length=50, choices=ALIAS_NAME_CHOICES, db_index=True)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer TextField over CharField
    alias_id: models.Field = models.CharField(max_length=255)  # noqa: TID251
    # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
    tenant: models.Field = models.ForeignKey(Tenant, on_delete=models.SET_NULL, blank=True, null=True)  # noqa: TID251

    class Meta(BaseModelV3.Meta):
        db_table = "alias_mapping"
        unique_together = (("content_type", "alias_name", "object_id", "alias_id", "tenant"),)
        constraints = [
            models.UniqueConstraint(
                fields=["content_type", "alias_name", "alias_id", "tenant"],
                condition=Q(
                    alias_name__in=(AliasName.RIBBON_PROCEDURE,),
                    _negated=True,
                ),
                name="only_one_record_per_foreign_key_unless_exempt",
            )
        ]
