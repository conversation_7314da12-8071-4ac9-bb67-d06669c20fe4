# Roles

The Roles module provides clinical role management for healthcare providers within the Firefly care delivery system. It defines provider roles, manages care team assignments, and enables role-based appointment scheduling and task assignment.

## Business Context

Healthcare delivery requires clear role definitions for different types of providers to ensure appropriate care coordination and appointment scheduling. This module provides:
- Clinical role definitions for different provider types (physicians, nurse practitioners, health guides)
- Care team role assignments for coordinated patient care
- Appointment type restrictions based on provider qualifications
- Task assignment routing based on clinical roles

## Core Models

### Role
Defines clinical roles for healthcare providers:

**Role Definition:**
- `role_name` - Standardized role identifier (from ROLE_NAME_CHOICES)
- `role_label` - Display label for applications and CRM systems
- `groups` - Associated Django permission groups for access control

**Model Features:**
- Extends BaseModelV3 for complete audit trail
- Unique role names for analytics and system integration
- Many-to-many relationship with Django Groups through RoleGroups

### RoleGroups
Junction table linking roles to Django permission groups:

**Association Management:**
- `role` - Foreign key to Role model
- `group` - Foreign key to Django Group model
- Unique constraint preventing duplicate role-group associations

## Role Definitions

### Clinical Provider Roles

**Physician (MD):**
- Primary care physicians and specialists
- Full prescribing authority and clinical decision-making
- Can provide Annual Wellness Visits and comprehensive care

**Nurse Practitioner (NP):**
- Advanced practice nurses with prescribing authority
- Preferred as primary care providers in care team assignments
- Can provide Annual Wellness Visits and focused care

**Registered Nurse (RN):**
- Licensed nurses providing clinical support
- Care coordination and patient education
- Limited to specific appointment types and clinical tasks

### Care Coordination Roles

**Health Guide (HG):**
- Non-clinical care coordinators and health coaches
- Health Guide Consult appointments and member support
- Care plan coordination and wellness coaching

**Behavioral Health Specialist (BH):**
- Mental health and behavioral health providers
- Specialized Behavioral Health appointments
- Integrated behavioral health services

**Member Guide:**
- Member experience and onboarding support
- Non-clinical member assistance and navigation
- Coverage and benefits guidance

**Care Coordinator:**
- Clinical care coordination and case management
- Cross-functional care team communication
- Care plan implementation and monitoring

## Role Integration

### Care Team Assignment

**Primary Physician Selection:**
```python
# NP Priority Logic
primary_provider = (
    ProviderDetail.objects.filter(pk__in=providers, user__groups__name=NP_ROLE)
    .exclude(pk__in=providers, user__groups__name=MD_ROLE)
    .first()
)

# MD Fallback
if not primary_provider:
    primary_provider = ProviderDetail.objects.filter(
        pk__in=providers, user__groups__name=MD_ROLE
    ).first()
```

**Care Team Visibility:**
- Roles displayed to members in priority order: Physician, NP, Health Guide
- Role-based care team member presentation in patient applications
- Provider role filtering for care team templates

### Appointment Scheduling

**Role-Based Appointment Types:**
```python
APPOINTMENT_TYPE_TO_ROLE = {
    "Behavioral Health": [ROLE_VALUES.BH],
    "Health Guide Consult": [ROLE_VALUES.HG],
    "Annual Wellness Visit Established": [ROLE_VALUES.NP, ROLE_VALUES.PHYSICIAN],
    "Annual Wellness Visit New": [ROLE_VALUES.NP, ROLE_VALUES.PHYSICIAN],
}
```

**Provider Prioritization:**
- NP-preferred scheduling for primary care appointments
- Role-based provider sorting in appointment availability
- Care team role validation for appointment booking

### Task Assignment

**Role-Based Task Routing:**
- Tasks assigned to specific roles within patient care teams
- Care team role groups for automated task distribution
- Role-based round-robin assignment for operational tasks

## Admin Interface

### RolesAdmin
Comprehensive role management interface:
- Search by role name and ID
- Inline editing of role-group associations
- Complete audit trail through BaseModelV3AdminMixin
- Role label management for application display

### Role Management Features
- Role creation and modification tracking
- Permission group association management
- Role usage analytics and reporting

## Integration Points

### User and Provider Models
- **ProviderDetail**: Links providers to clinical roles through internal_role field
- **User Groups**: Integration with Django permission system
- **Care Team**: Role-based provider assignment and care coordination

### Appointment System
- **Appointment Types**: Role restrictions for appointment booking
- **Provider Scheduling**: Role-based availability and prioritization
- **Care Team Booking**: Role validation for care team appointments

### Task Management
- **Task Assignment**: Role-based task routing within care teams
- **Care Team Groups**: Automated assignment based on clinical roles
- **Workflow Routing**: Role-specific task distribution

## Role Constants

### Available Roles
```python
class ROLE_VALUES:
    PHYSICIAN = "Physician"
    NP = "Nurse Practitioner"
    RN = "Registered Nurse"
    HG = "Health Guide"
    BH = "Behavioral Health Specialist"
    MEMBER_GUIDE = "Member Guide"
    CARE_COORDINATOR = "Care Coordinator"
    OTHERS = "Others"
```

### Care Team Display
- **Visible Roles**: Physician, NP, Health Guide (in priority order)
- **Role Abbreviations**: MD, NP, RN, HG, BH for UI display
- **State Licensing**: MD, NP, RN, BH roles require state licensing validation

## Business Logic

### Primary Care Provider Assignment
1. **NP Preference**: System prefers Nurse Practitioners as primary care providers
2. **MD Fallback**: Medical Doctors assigned if no NP available in care team
3. **License Validation**: Primary physician must be licensed in patient's state
4. **Program Integration**: Primary physician assignment updates primary care program

### Appointment Eligibility
1. **Role Validation**: Appointment types restricted to qualified provider roles
2. **Care Team Filtering**: Appointments limited to patient's assigned care team
3. **Provider Prioritization**: Role-based sorting of available appointment slots

### Task Distribution
1. **Care Team Routing**: Tasks assigned to specific roles within patient care teams
2. **Round Robin**: Operational tasks distributed among role group members
3. **Escalation**: Task reassignment based on role hierarchy and availability

## Configuration

Role configuration is managed through:
- **Constants**: Standardized role definitions in ROLE_VALUES
- **Database**: Role instances with labels and group associations
- **Migrations**: Initial role setup and permission group linking
- **Admin Interface**: Ongoing role management and group associations

## Testing

Test factories are provided for role testing:
- `RoleFactory` - Creates test role instances
- Role-based provider factories for care team testing
- Appointment scheduling tests with role validation
- Task assignment tests with role-based routing
