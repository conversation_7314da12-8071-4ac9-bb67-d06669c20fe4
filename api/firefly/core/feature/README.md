# Feature

The Feature module provides comprehensive feature flag management and A/B testing capabilities for controlled feature rollouts. Built on django-waffle, it extends functionality with ownership tracking, phase management, and enhanced user/group targeting for healthcare application requirements.

## Business Context

Healthcare applications require careful feature rollouts to ensure patient safety and regulatory compliance. This module provides:
- Controlled feature rollouts with gradual user exposure
- A/B testing capabilities for product experimentation
- User and group-based feature targeting
- Phase tracking for feature lifecycle management
- Integration with healthcare-specific user roles and permissions

## Core Models

### Flag
User-based feature flags with advanced targeting and phase tracking:

**Targeting Options:**
- `everyone` - Enable/disable for all users (highest priority)
- `superusers` - Enable for superusers only
- `staff` - Enable for staff users
- `users` - Specific user targeting through FlagUser junction table
- `groups` - Group-based targeting through FlagGroup junction table
- `percent` - Percentage-based rollout for gradual deployment

**Phase Management:**
- `phase` - Current lifecycle phase (General Availability, Internal, No Availability, Unknown)
- `phase_last_changed_at` - Timestamp tracking phase transitions
- Automatic phase calculation based on flag configuration

**Ownership and Governance:**
- `owner` - Team or person responsible for the feature flag
- Audit trail through BaseModelV3 integration
- Change tracking for compliance and debugging

### Switch
Simple boolean switches for system-wide feature control:
- Global on/off switches without user targeting
- Used for system maintenance and emergency feature disabling
- Simpler alternative to flags for basic feature control

### Sample
Percentage-based sampling for A/B testing:
- Random user sampling based on percentage
- Consistent user experience across sessions
- Statistical testing and experimentation support

## Feature Flag Lifecycle

### Phase Calculation
Automatic phase determination based on flag configuration:

```python
def calculate_phase(self):
    if self.everyone is True:
        return PHASE_GENERAL_AVAILABILITY
    elif self.everyone is False:
        return PHASE_NO_AVAILABILITY
    elif self.superusers is True or self.staff is True:
        return PHASE_INTERNAL_AVAILABILITY
    return PHASE_UNKNOWN
```

**Phase Definitions:**
- **General Availability**: Feature enabled for all users
- **No Availability**: Feature disabled for all users
- **Internal Availability**: Feature enabled for internal users only
- **Unknown**: Phase cannot be determined from current configuration

### Phase Tracking
Automatic tracking of phase transitions:
- `phase_last_changed_at` updated when phase changes
- Enables feature lifecycle analytics and compliance reporting
- Supports rollback and deployment timeline analysis

## User and Group Targeting

### FlagUser Junction Table
Individual user targeting for precise feature control:
- Direct user assignment to feature flags
- Overrides percentage-based rollouts
- Useful for beta testing and specific user enablement

### FlagGroup Junction Table
Group-based feature targeting:
- Enables role-based feature access (clinicians, patients, staff)
- Integrates with Django permission groups
- Supports healthcare-specific role targeting

### Targeting Priority
Feature flag evaluation follows priority order:
1. **Individual User Assignment** (highest priority)
2. **Group Membership**
3. **Superuser/Staff Status**
4. **Everyone Setting**
5. **Percentage Rollout** (lowest priority)

## API Integration

### REST API Endpoints
Feature flag access through REST API:

**Member Endpoints:**
- `FlagMemberViewSet` - Patient-facing feature flag access
- `SwitchMemberViewSet` - Patient-facing switch access
- Public access with modification restrictions

**Provider Endpoints:**
- `FlagProviderViewSet` - Provider-facing feature flag access
- `SwitchProviderViewSet` - Provider-facing switch access
- Provider authentication required

### Query Parameters
Flexible feature flag querying:
```python
# Get specific flags by name
GET /api/flags/?name=feature1,feature2,feature3

# Get all flags
GET /api/flags/
```

## Django-Waffle Integration

### Enhanced Request Handling
Fixes django-waffle bug with Django Rest Framework:

```python
def is_active(self, request, read_only=False):
    # Workaround for DRF Request wrapper issue
    if isinstance(request, RestFrameworkRequest):
        return super().is_active(request._request, read_only)
    return super().is_active(request, read_only)
```

### Utility Functions
Simplified feature flag checking:

```python
def is_flag_active_for_user(user: "User", flag_name: str) -> bool:
    try:
        flag = get_waffle_flag_model().get(flag_name)
        return flag.is_active_for_user(user)
    except Exception:
        return False  # Safe default when flag doesn't exist
```

## Custom Managers

### Safe Delete Integration
Feature flags integrate with soft delete system:
- `FlagManager` / `FlagAllManager` - Standard and all-objects managers
- `SwitchManager` / `SwitchAllManager` - Switch-specific managers
- `SampleManager` / `SampleAllManager` - Sample-specific managers
- Bulk update capabilities for efficient flag management

## Testing Support

### Test Factories
Comprehensive test factories for feature flag testing:
- `FlagFactory` - Creates test feature flags
- `SwitchFactory` - Creates test switches
- Support for various flag configurations and targeting scenarios

### Test Utilities
Helper functions for testing feature flag behavior:
- Flag activation testing across different user types
- Group-based targeting validation
- Percentage rollout testing with deterministic results

## Usage Patterns

### Healthcare-Specific Targeting
```python
# Enable feature for clinicians only
flag = Flag.objects.create(
    name="advanced_clinical_tools",
    owner="Clinical Team"
)
clinician_group = Group.objects.get(name="Clinician")
FlagGroup.objects.create(flag=flag, group=clinician_group)
```

### Gradual Rollout
```python
# Start with internal users, gradually expand
flag = Flag.objects.create(
    name="new_patient_portal",
    owner="Product Team",
    staff=True,  # Internal availability phase
    percent=0    # No external users initially
)

# Later: expand to 10% of users
flag.percent = 10
flag.save()

# Finally: general availability
flag.everyone = True
flag.save()  # Automatically updates phase to General Availability
```

### Emergency Disable
```python
# Quickly disable problematic feature
flag = Flag.objects.get(name="problematic_feature")
flag.everyone = False
flag.save()  # Automatically updates phase to No Availability
```

## Integration Points

### Permission System
- Integration with Django Groups for role-based targeting
- Healthcare-specific role targeting (clinicians, patients, staff)
- Permission-based feature access control

### User Management
- User-specific feature flag targeting
- Integration with user roles and care team assignments
- Support for provider and patient contexts

### Audit and Compliance
- Complete change tracking through BaseModelV3
- Phase transition logging for compliance reporting
- Owner assignment for feature flag governance

## Configuration

Feature flags are configured through:
- **Django Admin**: Web interface for flag management
- **API Endpoints**: Programmatic flag configuration
- **Management Commands**: Bulk flag operations
- **Database Migrations**: Initial flag setup and configuration

## Best Practices

### Ownership and Governance
- Assign clear ownership to teams or individuals
- Use descriptive flag names with consistent naming conventions
- Track phase transitions for feature lifecycle management

### Targeting Strategy
- Start with internal users for new features
- Use percentage rollouts for gradual deployment
- Leverage group targeting for role-based features

### Cleanup and Maintenance
- Remove flags after features reach general availability
- Monitor flag usage and performance impact
- Regular review of flag inventory and ownership
