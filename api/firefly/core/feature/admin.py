import itertools

from django.contrib import admin
from safedelete.admin import highlight_deleted
from waffle.admin import FlagAdmin as WaffleFlagAdmin
from waffle.admin import SampleAdmin as WaffleSampleAdmin
from waffle.admin import SwitchAdmin as WaffleSwitchAdmin

from firefly.core.feature.models import Flag, Sample, Switch
from firefly.modules.firefly_django.admin import BaseModelV3AdminMixin


def highlight_deleted_name(list_display):
    """
    Replace the "name" field of the model with a version that is highlighted
    with a strikethrough when the object is deleted.
    """
    return tuple(field if field != "name" else highlight_deleted for field in list_display)


@admin.register(Flag)
class FlagAdmin(WaffleFlagAdmin, BaseModelV3AdminMixin):  # type: ignore
    # waffle defines list_display and list_filter as a tuple - that needs exactly the same
    # number of elements when overridden
    list_display = tuple(
        itertools.chain(
            highlight_deleted_name(WaffleFlagAdmin.list_display),
            [
                "owner",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            WaffleFlagAdmin.list_filter,
            BaseModelV3AdminMixin.list_filter,
            [
                "owner",
            ],
        )
    )

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        if db_field.name == "groups":
            # Terrible hack, but BaseModelAdmin.formfield_for_manytomany first
            # checks whether the "through" model is auto-created by Django or
            # not, and short-circuits if not. However, the rest of the function
            # works as expected for this field. We're not storing any additional
            # information on the FlagGroup model that can't be handled by
            # rendering the usual input on this form. This is a shameless
            # copy-paste partial reimplementation of the rest of that function.
            kwargs["queryset"] = self.get_field_queryset(kwargs.get("using"), db_field, request)
            form_field = db_field.formfield(**kwargs)
            form_field.help_text = (
                f"{form_field.help_text} Hold down “Control”, or “Command” on a Mac, to select more than one."
            )
            return form_field

        return super().formfield_for_manytomany(db_field, request, **kwargs)


@admin.register(Switch)
class SwitchAdmin(WaffleSwitchAdmin, BaseModelV3AdminMixin):  # type: ignore
    list_display = tuple(
        itertools.chain(
            highlight_deleted_name(WaffleSwitchAdmin.list_display),
            [
                "owner",
            ],
        )
    )
    list_filter = tuple(
        itertools.chain(
            WaffleSwitchAdmin.list_filter,
            BaseModelV3AdminMixin.list_filter,
            [
                "owner",
            ],
        )
    )


@admin.register(Sample)
class SampleAdmin(WaffleSampleAdmin, BaseModelV3AdminMixin):  # type: ignore
    list_display = tuple(
        itertools.chain(
            highlight_deleted_name(WaffleSampleAdmin.list_display),
            BaseModelV3AdminMixin.list_display,
            [
                "owner",
            ],
        )
    )
    list_display = tuple(
        itertools.chain(
            WaffleSampleAdmin.list_filter,
            BaseModelV3AdminMixin.list_filter,
            [
                "owner",
            ],
        )
    )
