from typing import TypeVar

from django.conf import settings
from django.db import models

from firefly.modules.firefly_django.context import warn_if_not_in_pghistory_context_with_user

T_model_co = TypeVar("T_model_co", bound="BaseModel", covariant=True)


class ActiveManager(models.Manager[T_model_co]):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


class DeletedManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=True)


class AllManager(models.Manager):
    """Exposes the default Django manager, which returns all objects in the database.
    We can't use models.Manager directly below as it's an abstract base class.
    """

    pass


# TODO: Deprecate and delete after `PatientDetail` has been nuked
class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="+",
        null=True,
        blank=True,
        # DO NOT COPY-PASTE: Prefer CASCADE, PROTECT, or RESTRICT
        on_delete=models.SET_NULL,  # noqa: TID251
    )
    is_deleted = models.BooleanField(default=False, db_index=True)

    objects = ActiveManager()

    deleted_objects = DeletedManager()

    # Includes both soft deleted and active objects; all objects in the database.
    all_objects = AllManager()

    _elation_update = False

    @property
    def elation_update(self):
        return self._elation_update

    @elation_update.setter
    def elation_update(self, value):
        self._elation_update = value

    def delete(self):
        self.is_deleted = True
        self.save()

    def save(self, *args, **kwargs):
        # Prior to BaseModelV3 upgrade, detect whether this is being saved
        # without setting `pghistory.context(user=...)`.
        warn_if_not_in_pghistory_context_with_user(
            message=f"Attempting to save {type(self).__name__} without setting a pghistory.context user"
        )

        super().save(*args, **kwargs)

    class Meta:
        abstract = True
