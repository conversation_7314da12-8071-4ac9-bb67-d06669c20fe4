"""
Firefly URL Configuration.

The `patterns` list routes URLs to views. For more information please see:
https://docs.djangoproject.com/en/2.0/topics/http/urls/

URLs that are not imported from a Firefly Django app should use the legacy `django_path` or
`re_path` to ensure compatibility. All Firefly Django app URLs should be imported as
`path(<route>, include(<app.urls>))` using the new `path` import.
"""

import typing

from django.conf import settings
from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.urls import include, re_path
from django.urls import path as django_path
from django.urls.resolvers import URLPattern, URLResolver
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView

from firefly.core.status import status_view
from firefly.core.urls.url_patterns import path
from firefly.modules.tenants.middleware import skip_tenant_permission

# There is an open issue in static file type being list[URLPattern] and other list being [URLResolver]
# as work around, defining a custom type. Ref: https://github.com/typeddjango/django-stubs/issues/550
URL = typing.Union[URLPattern, URLResolver]
URLList = typing.List[URL]

patterns = [
    path("accumulators/", include("firefly.modules.accumulators.urls")),
    path("announcements/", include("firefly.modules.announcements.urls")),
    path("appointment/", include("firefly.modules.appointment.urls")),
    path("auto_eligibility/", include("firefly.core.services.auto_eligibility_service.urls")),
    path("bff/", include("firefly.bff.urls")),
    path("care-plan/", include("firefly.modules.care_plan.urls")),
    # TODO: Switch to using path when permissions bug is fixed.
    re_path(r"^providers/(?P<id>(\d+|me))/cases/", include("firefly.modules.cases.urls.provider")),
    path("pods/", include("firefly.modules.pods.urls")),
    path("chat/", include("firefly.modules.chat_message.urls")),
    path("claims/", include("firefly.modules.claims.urls")),
    path("clinic-hours/", include("firefly.modules.clinic_hours.urls")),
    path("code-systems/", include("firefly.modules.code_systems.urls")),
    path("content/", include("firefly.modules.content.urls")),
    path("csp-report/", include("firefly.modules.csp_report.urls")),
    re_path(
        r"^providers/(?P<id>(\d+|me))/communications/",
        include("firefly.modules.communications.urls"),
    ),
    path("data-events/", include("firefly.modules.data_events.urls")),
    path("device/", include("firefly.modules.device.urls")),
    path("documents/", include("firefly.modules.documents.urls")),
    path("eligibility/", include("firefly.modules.eligibility.urls")),
    path("escalated-message/", include("firefly.modules.escalated_message.urls")),
    path("events/", include("firefly.modules.events.urls")),
    path("experiments/", include("firefly.modules.experiments.urls")),
    path("facts/", include("firefly.modules.facts.urls")),
    path("feature/", include("firefly.core.feature.urls")),
    path("features/", include("firefly.modules.features.urls")),
    path("form/", include("firefly.modules.forms.urls")),
    path("forms_v2/", include("firefly.modules.forms.v2.urls")),
    # Healthcheck views
    re_path(r"^healthcheck/?$", status_view),  # For built-in healthchecks.
    path("health-plan/", include("firefly.modules.health_plan.urls")),
    path("talkdesk/", include("firefly.modules.talkdesk.urls")),
    path("insurance/", include("firefly.modules.insurance.urls")),
    path("log/", include("firefly.modules.client_log.urls")),
    path("macros/", include("firefly.modules.macros.urls")),
    path("mailers/", include("firefly.modules.mailers.urls")),
    path("network/", include("firefly.modules.network.urls")),
    path("notes/", include("firefly.modules.notes.urls")),
    path("notifications/", include("firefly.modules.notifications.urls")),
    re_path(r"^providers/(?P<id>(\d+|me))/phone-calls/", include("firefly.modules.phone_calls.urls")),
    path("physician/", include("firefly.modules.physician.urls")),
    path("onboarding/", include("firefly.modules.onboarding.urls")),
    path("phi_access_control/", include("firefly.core.phi_access_control.urls")),
    path("practice-suite/", include("firefly.modules.billing.urls")),
    path("patient-referral-programs/", include("firefly.modules.patient_referral_programs.urls")),
    path("programs/", include("firefly.modules.programs.urls")),
    path(
        "programs/depression-anxiety/",
        include("firefly.modules.programs.depression_and_anxiety.urls"),
    ),
    path(
        "programs/hypertension/",
        include("firefly.modules.programs.hypertension.urls"),
    ),
    path(
        "programs/diabetes/",
        include("firefly.modules.programs.diabetes.urls"),
    ),
    path(
        "programs/adhd-add/",
        include("firefly.modules.programs.adhd_add.urls"),
    ),
    path("programs/benefit/", include("firefly.modules.programs.benefit.urls")),
    path("programs/primary-care/", include("firefly.modules.programs.primary_care.urls")),
    path("programs/urgent-care/", include("firefly.modules.programs.urgent_care.urls")),
    path("risk-capture/", include("firefly.modules.risk_capture.urls")),
    path("referral/", include("firefly.modules.referral.urls")),
    path("schedule/", include("firefly.modules.schedule.urls")),
    path("settings/", include("firefly.modules.settings.urls")),
    path("states/", include("firefly.modules.states.urls")),
    django_path("status/", status_view),  # For UptimeRobot.
    path("task/", include("firefly.modules.tasks.urls")),
    re_path("tenants/", include("firefly.modules.tenants.urls.patient")),
    path("user/", include("firefly.core.user.urls.user")),
    re_path(r"^providers/(?P<id>(\d+|me))/user/", include("firefly.core.user.urls.person")),
    path("visit-ratings/", include("firefly.modules.visit_ratings.urls")),
    re_path(r"^providers/(?P<id>(\d+|me))/work_units/", include("firefly.modules.work_units.urls")),
    path("worklists/", include("firefly.modules.worklists.urls")),
    path("education-chassis/", include("firefly.modules.education_chassis.urls")),
    path("bulletin-board/", include("firefly.modules.bulletin_board.urls")),
    path("user-segmentation/", include("firefly.modules.segmentation.urls")),
    path("email/", include("firefly.modules.email.urls")),
]

if settings.IS_TESTING:
    # Add firefly django urls during testing to test behaviour of
    # the models created to verify behaviour of base model implementation
    patterns += [
        path("firefly_django/", include("firefly.modules.firefly_django.urls")),
    ]
urlpatterns: URLList = []
if settings.ENABLE_ADMIN:
    urlpatterns += [
        django_path("admin/", admin.site.urls),
        django_path("oidc/", include("mozilla_django_oidc.urls")),
    ]

# Flatten patterns (which is a list of lists) into urlpatterns
urlpatterns += [pattern for app in patterns for pattern in (app if isinstance(app, (tuple, list)) else (app,))]

# OpenAPI views
api_schema_view = skip_tenant_permission()(SpectacularAPIView.as_view())
api_ui_view = skip_tenant_permission()(SpectacularSwaggerView.as_view(url_name="schema"))
api_docs_view = skip_tenant_permission()(SpectacularRedocView.as_view(url_name="schema"))

urlpatterns += [
    re_path(r"^schema/$", api_schema_view, name="schema"),
    re_path(r"^schema/ui/$", api_ui_view, name="swagger-ui"),
    re_path(r"^docs/$", api_docs_view, name="redoc"),
]

urlpatterns += staticfiles_urlpatterns()
