# See README notes
# and https://pre-commit.com
default_stages: ['push']
repos:

  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.3.0
    hooks:
      - id: detect-secrets
        args:
          [
            "--baseline",
            ".secrets.baseline",
            "--exclude-files",
            "api/docker-compose.yaml|api/.*?/migrations/.*?\\.py$|.circleci/workflows.yml|CHANGELOG.md|app/.env.development|app/.env.staging|app/.env.production|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/Dev.xcscheme|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/DevDebug.xcscheme|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/Production.xcscheme|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/ProductionDebug.xcscheme|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/Staging.xcscheme|app/ios/FireflyHealth.xcodeproj/xcshareddata/xcschemes/StagingDebug.xcscheme|app/android/app/src/development/google-services.json|app/android/app/src/prod/google-services.json|app/android/app/src/staging/google-services.json|packages/api/mocks/manifest.ts",
          ]
  - repo: local
    hooks:
      - id: ruff-check
        # Linting, not formatting
        # Expect this to be a part of editor on-save configuration
        # but allow opting-in as part of pre-commit
        name: ruff-check
        entry: ruff check --show-fixes --fix --exit-non-zero-on-fix
        language: system
        types: [python]
        stages: [commit]
        args: []
        verbose: true
      - id: ruff-format
        # Matches the formatting we expect editor on-save to be doing
        # `format` can't make fixes *and* exit with a non-zero status like `check` can
        name: ruff-format
        entry: ruff format --diff
        language: system
        types: [python]
        stages: [commit]
        args: []
        verbose: true
      - id: openapi-yaml
        name: openapi-yaml
        language: system
        entry: make -C api openapi.yaml
        always_run: true
        pass_filenames: false
