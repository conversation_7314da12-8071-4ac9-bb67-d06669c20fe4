// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-dockerfile
{
	"name": "Existing Dockerfile",
	"build": {
		// Sets the run context to one level up instead of the .devcontainer folder.
		"context": "..",
		// Update the 'dockerFile' property if you aren't using the standard 'Dockerfile' filename.
		"dockerfile": "../Dockerfile"
	},
	"features": {
		// Need git - so that vscode features that rely on git and the source control tab work
		// when vscode is running the container
		"ghcr.io/devcontainers/features/git:1": {
			"ppa": true,
			"version": "latest"
		},
		"ghcr.io/stuartleeks/dev-container-features/shell-history:0": {},
		// to enable the `make` command - so that we can run helpers in the `Makefile`
		"ghcr.io/jungaretti/features/make:1": {},
		// since github auth works via ssh keys
		"ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {
			"packages": "openssh-client, sudo"
		}
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// TODO: Need to enable a code extension host
	// "forwardPorts": [],
	// Uncomment the next line to run commands after the container is created.
	// "postCreateCommand": "cat /etc/os-release",
	// Configure tool-specific properties.
	// Installs the python, intellicode, ruff, mypy and gitlens
	// extenstions in vscode
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"eamodio.gitlens",
				"VisualStudioExptTeam.vscodeintellicode",
				"charliermarsh.ruff",
				"matangover.mypy"
			]
		}
	},
	// Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "devcontainer"
}