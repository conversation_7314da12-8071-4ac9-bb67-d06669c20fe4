[mypy]
mypy_path = $MYPY_CONFIG_FILE_DIR/stubs
python_version = 3.11
# We're gradually adding types to formerly untyped modules, so ignore missing annotations for now.
disable_error_code = annotation-unchecked
# TODO: Narrow down to only specific imports.
ignore_missing_imports = True
plugins =
  mypy_django_plugin.main,
  mypy_drf_plugin.main
warn_redundant_casts = True
warn_unused_ignores = True

[mypy.plugins.django-stubs]
django_settings_module = firefly.settings.typechecking
