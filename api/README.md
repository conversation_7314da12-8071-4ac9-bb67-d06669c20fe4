# API


A RESTful API powering the Firefly mobile app and CRM.
Built with Django, Python 3.x

## Overview

The API service is the core backend for Firefly Health's digital platform, providing:

- Authentication and authorization
- Patient and provider data management
- Form submission and processing
- Appointment scheduling and management
- Integration with third-party services
- PDF generation and document management
- Asynchronous task processing

## Getting Started

Development on this repo can be accomplished in a remote environment or from your local machine (and assumes you are using a Mac). If this is your first time building this module, you should start here. There are a few pre-reqs to getting started. You will need:

- Git Access
  - Git command line installed (also consider [Git Desktop](https://desktop.github.com/))
  - A GitHub user with access to this organization (if you are reading this, you have access; otherwise your username will need to be added to the [list of members in the firefly-infra repo](https://github.com/fireflyhealth/firefly-infra/blob/main/github/members.tf)).
  - An SSH key for authentication to clone and push to repos. Note that the key you use for GitHub will also be used to access the remote development environment.
    - [Generate the keypair](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)
    - [Add the public key to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account)
- Infrastructure Access (AWS/EKS):
  - Make sure you have [access to the necessary AWS accounts based on role](https://github.com/fireflyhealth/firefly-infra/blob/main/aws/sso/groups.tf).
  - Install the AWS Command Line Interface from Managed Software Center.
  - Install `lucible` from the [firefly-infra repo](https://github.com/fireflyhealth/firefly-infra)
  - Run `lucible login` to verify connectivity
- An admin user in the [DEV environment](https://lucian-admin.dev.i.firefly.health/admin/)
  - You will need to ask your onboarding buddy or another engineer to use the [Create Staff User admin command](https://lucian-admin.dev.i.firefly.health/admin/fireflyadmincommand/fireflyadmincommand/run/createstaffuser)
  - For email, it is recommended to use `<EMAIL>` for your staff account, and to differentiate other test patient users you create with your Firefly address by adding a suffix like `<EMAIL>`.

If you are missing ANY of these things, PLEASE SEE YOUR ONBOARDING BUDDY!

## Remote Development

- Install the AWS Session Manager plugin from Managed Software Center.
- Run `lucible devenv list` to see the available instances. Pick one closest to you for minimal latency.
- Run `lucible devenv ssh [name]`.
  - When this step is run - please take note of any warnings at the top of the output. Warnings usually appear if there is an issue with ssh tunneling. Port 8084 on the local Mac is connected to a port on the remote machine (similarly other ports for other services). If there is a conflict with port allocation - it usually indicates that the port is already in use. Here are common reasons for the tunneling to fail.
    - Port 5432: The postgres service might be running locally. Use `brew services list` to verify that the postgres service is not running. To stop the service, use  `brew services stop postgresql@13` (replace the version with the version string of the running service)
- For most folks, this should just work. However, if you use multiple SSH keys to authenticate to different services, you may need to update your `~/.ssh/config` to specify the key. It should be an SSH key that you use to connect to GitHub, found at [https://github.com/settings/keys](https://github.com/settings/keys). If you recently added the public key to GitHub, it may not yet have synced to the remote environment; ask an AWS admin to log in to the corp account and apply the `FireflyHealth-ApplyAnsiblePlaybooks` association in AWS Systems Manager State Manager.
- On the remote server, copy the contents of `~/.ssh/id_rsa.pub` and add this as an Authentication Key (and optionally as a Signing Key if you sign your commits) to GitHub at [https://github.com/settings/ssh/new](https://github.com/settings/ssh/new).
Now you can `<NAME_EMAIL>:fireflyhealth/lucian.git`, `cd lucian/api`, and continue with the steps below in the [Set up your development environment](#set-up-your-development-environment) section. You should be able to reach your API server on your local machine via SSH tunnel.
- To set up your preferred IDE, see the [IDE Support](#ide-support) section below.

## Local Development

Skip this section if you are using the remote development environment.

For local development, you will need:

- [Docker Desktop](https://www.docker.com/products/docker-desktop) installed on your machine
  - Check `Use Docker Compose V2` in Docker preferences -> General

Most of the subsequent steps can be accomplished with the `Makefile` in the repository's `api/` folder. It contains most of the recipes you'll need to get started. At times, it will prompt you to install other dependencies before running the recipe again.

## Set up your development environment

This is divided into two phases:

- Starting docker containers. This will ensure that all APIs are up/running and unblock development.
- Setting up development environment. This phase ensures that dependencies are available for IDEs to support features like auto-complete.

### Starting docker containers

Our Makefile automates the setup and build of your local environment. To get started, you should be able to run this command to completely rebuild your environment (including setting up your database state). You are encouraged to do this regularly to pickup specific configuration changes to your local environtment.

```
make rebuild-local-environment
```

After this command is complete (should take several minutes for all the steps), all instances should be up and running. Running `make tail-web` should show the logs of the api-container (this command will continue to tail logs until you cancel it).

Note that it does take a while to startup - since it builds all the containers, sets up secrets and sets up the database. However you should see logs stating that all migrations are upto date, followed by installation of the debugger and then the actual server startup. All of this should complete in under 5 minutes after `make tail-web` is run.

Succesful installation should show logs that look like

```
web_1       | Django version 3.2.2, using settings 'firefly.settings.local'
web_1       | Development server is running at http://0.0.0.0:8000/
web_1       | Using the Werkzeug debugger (http://werkzeug.pocoo.org/)
web_1       | Quit the server with CONTROL-C.
web_1       |  * Debugger is active!
web_1       |  * Debugger PIN: 234-366-702
```

Now you're ready to log into your local Django admin console for the first time. Because you cloned the DEV system, your account and credentials will be the same for your local system.

Via your favorite browser, login to [http://localhost:8084/admin/](http://localhost:8084/admin/).

You should be able to make any code changes now - and the server should restart automatically to reflect the new changes.

### Setting up development environment

This step attempts to install dependencies in the development environment to allow for features such as auto-complete and typechecking in the IDE.

Pre-requisites:

To set up a [Python Virtual-Environment](https://uoa-eresearch.github.io/eresearch-cookbook/recipe/2014/11/26/python-virtual-env/), install the necessary requirements, and install Git [pre-commit](https://pre-commit.com/) hooks to run lint and autoformatting before every push, run:

```
make git-hooks
```

At this point the docker based setup and the local setup required to support features in the IDE should be all set-up.

## Repository Structure

The API codebase is organized as follows:

```
api/
├── ci/                  # CI/CD configuration and scripts
├── docker/              # Docker configuration files
├── firefly/             # Main Django application
│   ├── core/            # Core functionality shared across modules
│   ├── modules/         # Feature-specific modules
│   │   ├── documents/   # Document management
│   │   ├── eligibility/ # Insurance eligibility
│   │   ├── forms/       # Form submission and processing
│   │   ├── network/     # Provider network directory
│   │   └── ...
│   └── settings/        # Environment-specific settings
├── pdf-export/          # Node.js service for PDF generation
└── tests/               # Test suite
```

The codebase follows a modular architecture, with each module containing its own models, views, serializers, and business logic. This organization helps maintain separation of concerns and makes the codebase more maintainable.

## API Documentation

The API is documented using the [OpenAPI specification](https://swagger.io/specification/) (formerly known as Swagger). This documentation is automatically generated from code annotations and is available at:

- Local: http://localhost:8084/api/schema/swagger-ui/
- Dev: https://lucian-api.dev.i.firefly.health/api/schema/swagger-ui/
- Staging: https://lucian-api.staging.i.firefly.health/api/schema/swagger-ui/
- Production: https://lucian-api.i.firefly.health/api/schema/swagger-ui/

We use [drf-spectacular](https://drf-spectacular.readthedocs.io/) to generate the OpenAPI schema from Django REST Framework views. To document your API endpoints:

1. Use the `@extend_schema` decorator on your views to provide additional information
2. Use serializers to define request and response schemas
3. Add docstrings to your views and serializers

The OpenAPI schema is also used to generate the TypeScript client in the `@fireflyhealth/api` package.

The remainder of the README adds documentation around more helper functions that were added in. Although reading through them is recommended, the remainder of the doc does not contain any required step to be able to contribute to the API.


# Helpful recipes

## Installing new packages

To install new packages first add the new package to the `pyproject.toml` file.

Then for non-docker development run:

```
make local-install-package
```

For Docker development run:

```
make docker-install-package
```

## (Re)Create your local configuration

Most of the environment-specific configuration that controls your api install will live in the [settings folder](https://github.com/fireflyhealth/lucian/tree/main/api/firefly/settings) in a file called `local.py`. This is an override\* of `base.py` in the same folder. There are also vendor specific keys configuration files that will need to be setup for your server to start. The following command will automatically set these up for you. However, if you need to make a change to these are part of your development efforts, you will likely need to incorporate these changes into `local.py` persist these appropriately for all developers going forward.

```
make create-local-config
```

## (Re)Create your containers

We use Docker Desktop to host local environments. It frequently leaves behind old or stale artifacts that can invalidate your testing. To do this, you will need [Docker Desktop] (https://www.docker.com/products/docker-desktop). This will need to be running (this can be verified by clicking the docker icon in your system tray; it should say `Docker Desktop is running`) run:

To delete your existing docker artifacts run (note this will destroy all data volumes, containers, images)

```
make docker-clean
```

To rebuild your docker containers

```
make docker-build
```

To rebuild and start your containers

```
make up
```

## Rebuilding your local database

The default `make rebuild-local-environment` command will automatically restore your database from our DEV environment. If you need to repeat this step by itself it can be done with `make clone-db`. This may be necessary if you end up deleting docker volumes with some of the commands above.

If `make db-clone` fails due to an error like "could not translate host name", it may be your DNS server blocking access to `.in` domains. On your computer, go to System Preferences > Network > Preferences > Advanced > DNS and add `*******` (Google) to the list of DNS servers.

## Rebuilding test boot strap file

The `api/test_bootstrap.sqlc` contains a dump of the database with migrations run till the date of the generation of the file. Tests can then use a database created using this image so that they do not spend time in creating the state of the database.

However, this file gets stale as we add more migrations to our codebase. To regenerate:

- Create and push a branch
- In circle ci: trigger a manual build for the branch with the `build-bootstrap` param set
  - In Circle CI: Click Trigger Pipeline
  - In the popup window that appears - click `Add parameters`
  - Change the `Parameter type` to `boolean`
  - Set the name to `build-bootstrap` and the value to `true`
  - Click `Trigger Pipeline`
- At the end of the run of this workflow, navigate to the `build-bootstrap-api` job and check for the new bootstrap file under the artifacts tab.
- Download this file, save it at `api/test_bootstrap.sqlc` and create a PR for review.

## Code Formatting, Linters and Checks

We use a collection of formatters and linters to help enforce code style and best-practices:

1) [flake8](https://flake8.pycqa.org/en/latest/)
2) [ruff](https://github.com/charliermarsh/ruff)

General guidelines:

1) Formatting / quickly fix-able linting: on save in editor, should take milliseconds
2) Linting: Highlight lines in editor, should take less than a few seconds. If your editor does not support this, opt-in to pre-commit hooks. The `make git-hooks` command by default does not install these, so run `make git-hooks-opt-in-pre-commit` to do this
3) Other checks that take <10s and would otherwise be expensive to wait for in CI: should run as part of git pre-push hooks

Most of the engineering team uses [VS Code](https://code.visualstudio.com/) for daily development. All of these capabilities are available as plugins which automatically lint files upon save and the configuration for these plugins is checked into git - meaning they will automatically activate when you start using VS Code.

1) [ruff](https://marketplace.visualstudio.com/items?itemName=charliermarsh.ruff)

## Running Django management commands in Docker

1. Make sure Docker is running
2. `docker compose exec -T web python manage.py <name_of_command>`

## How to run tests

We use `pytest` as a test runner. To run all tests:

```
make test
```

Here's an example of how to run an individual test file:

```
make test test_path=path/to/file
```

or even an individual class or method:

```
make test test_path=path/to/file::Class::method
```

A few tips for using `pytest`:

- To select individual tests to run vs. running the entire suite or all the tests in a file, see the [pytest documentation](https://docs.pytest.org/en/latest/usage.html#specifying-tests-selecting-tests)
- To improve startup times by re-using the test DB between runs, use the `--reuse-db` flag ([docs](https://pytest-django.readthedocs.io/en/latest/database.html#reuse-db-reuse-the-testing-database-between-test-runs))
- To hide the test output about deprecation warnings, use the `--disable-pytest-warnings` flag. Explicit logs e.g. from `logger.info()` will still be visible.
- You can configure pytest to always use certain options using the `pytest.ini` file, instead of using command-line arguments.

## Linting Migrations

Migrations can optionally be [linted](https://github.com/3YOURMIND/django-migration-linter) from the command line with the `lint` flag:

```
python manage.py makemigrations --lint
```

## Type Checking

We use [mypy](http://www.mypy-lang.org/) for type checking, including the [django-stubs](https://github.com/typeddjango/django-stubs) plugin to support some of Django's more dynamic features. To run the type checker locally, run:

```
make typecheck
```

Typing is currently optional, but encouraged as a way to detect incorrect code early in the development cycle. Typing in Python may be slightly different from typing in other languages that you may have used before. See [Common issues and solutions](https://mypy.readthedocs.io/en/stable/common_issues.html), especially if you encounter something surprising.

In the (hopefully rare) event that you discover a false positive, write a comment describing the issue above the problematic line (perhaps with a TODO to fix upstream), and add a `# type: ignore` annotation to silence it.

## Admin Commands

Many of our [Django admin commands](https://docs.djangoproject.com/en/3.2/howto/custom-management-commands/) can be run in the Admin UI.

We also have some custom behavior around admin commands which come from the `FireflyBaseCommand` base class. All admin commands should inherit from this base class.

Any `FireflyBaseCommand` instance needs to know the auditable User who is running the command. When using `django-admincommand` to run the command from the UI, the currently-authenticated User is passed as an argument to the instance's `get_command_arguments()` function. This User should be included in the dict returned by that function.

If you need to run a command from the shell, make sure you are running the command on the `web` Docker container and identify the User using the `--user-phone-number` flag

```
docker compose exec web ./manage.py admin_command_name --user-phone-number **********
```

## Creating Provider Users

Creating a Provider User will give you an account you can use to log in to the CRM tool.

1. Log into the Django admin at [http://localhost:8084/admin/](http://localhost:8084/admin/).
2. Create a new user (you can choose any phone number).
3. Edit that user and add it to the provider group.
4. Create a new provider detail linked to that user. You will need to select at least one patient (we should fix this.)
5. You should now be able to log into Lucian w/ this user.

## Syncing Elation Data locally

In the event that you want to manually pull Elation data into your local database, you can run the following script. This will not create the entire database and is best used after a successful `make db-clone`. You can then use the Lucian Dev provider account (credentials in 1Password). This will require successfully configuring elation in `local.py` by pulling credentials from `dev.py`.

```
    $ docker compose exec web setup/bootstrap_data.sh
```

## Pull in Elation Data

If the instance is new or has not been listening to Elation updates, then make sure to pull in physician, practice, and appointment data from Elation. Be aware that the appointment pull step will print errors if for any Elation patient you do not have represented in your DB.

1. Pull in Physicians:

```
   docker compose exec web python manage.py pull_physicians
```

2. Pull in Practices:

```
   docker compose exec web python manage.py pull_practices
```

3. Pull in Appointments:

```
   docker compose exec web python manage.py pull_appointments
```

## Listening to updates from Elation

Currently this app leverages Elation's Event Subscription API to receive updates via webhooks. To test and develop with webhooks on a local machine you can forward TCP connections from the dev server's port 8080 to your local. This requires ssh access to the dev server - ************.

1. Set `ELATION['CALLBACK_BASE_URL']` to `'http://************:8080'` in local.py

2. Setup TCP forwarding from remote port 8080 to local port 8084 with ssh:

   ssh -R 0.0.0.0:8080:localhost:8084 {SSH_USER}@************

## Connect to S3

This app stores images in an S3 bucket. Access, secret, and session tokens are required for an authorized S3 connection from your local environment. This requires ssh access to the dev server - ************.

When you want to upload data to S3, for example to backfill data into a table, you'll end up wanting to upload it first to `firefly-dev`, and then eventually also to `firefly-staging` and `firefly-prod`. Note that for the latter two especially, you need to utilize the right permissions even when creating the folder itself for your data. The options that you'll want to choose are:

- Server-side encryption: Specify an encryption key
- Encryption settings: Override default encryption bucket settings
- Encryption key type: Amazon S3 key (SSE-S3)

## Local Settings You May Want to Modify

If you want to turn off the token authentication add the following to local.py (recommend not adding this for initial set up):

```
    REST_FRAMEWORK = {
        'DEFAULT_PERMISSION_CLASSES': [
            'rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly'
        ]
    }

```

If you want to send emails to stdout (this is helpful for testing email alerts locally) add the following to local.py:

```
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

## AWS / EKS

We deploy the API to Amazon Web Services (AWS), specifically running on Amazon Elastic Kubernetes Service (EKS). Each environment lives in a separate AWS account. Resources are managed using infrastructure-as-code stored in the [firefly-infra](https://github.com/fireflyhealth/firefly-infra) repository.

To deploy, follow the [instructions on the wiki](https://sites.google.com/firefly.health/tech/operations/how-changes-get-into-production).

## Running commands in EKS environments

It is possible to run commands against various environments via `lucible ssh`. For example, to run a Django management command against dev:

```
lucible ssh dev -- python manage.py show_urls
```

## Async and Scheduled Tasks

We use [Dramatiq](https://dramatiq.io) and [django_dramatiq](https://github.com/Bogdanp/django_dramatiq) to manage async tasks. We use [APScheduler](https://apscheduler.readthedocs.io/en/latest/) for dynamic scheduling of one-off and repeated tasks. RabbitMQ is used as the message broker.

- If a new async task is added without specifying the queue name, [the default queue is used](https://dramatiq.io/reference.html#actors-messages). The default queue has 2 dedicated workers attached to it.

- If a new async task is added with the queue name, [a new queue is created](https://dramatiq.io/reference.html#actors-messages). The newly created queue has no workers attached to it. Update the procfile to configure the queue_name to attach workers.

Workers defined in Procfile:

- common_worker: Processes just the default, prior-authorization, fetch_latitude_longitude, eligibility-check, and send_referral_outreach queues
- network_directory_worker: Processes the directory backfill queues

The local docker-compose environment automatically starts a local RabbitMQ cluster and a few dramatiq workers.

In order to stop running dramatiq because you may not be using them for development:

```
docker compose stop dramatiq
```

The [instructions](https://github.com/Bogdanp/django_dramatiq#declaring-tasks) in the django_dramatiq README give an example of how to add new tasks; check out the `tasks.py` files in each app folder for examples.

To view logs from async jobs, you'll need to watch the logs with:

```
docker compose logs --tail 100 -f dramatiq
```

You can view job queues via our message broker RabbitMQ. A local dashboard is available at http://localhost:15672/#/queues (credentials are guest:guest).

### PDF Export Service

The API includes a Node.js-based PDF export service (`pdf-export`) that generates PDF versions of form submissions. This service:

- Runs as a separate Docker container defined in `docker-compose.yaml`
- Uses Puppeteer to render PDFs from form submissions
- Supports both direct rendering from the forms-ui React app and custom EJS templates
- Is accessed via Dramatiq tasks from the main API

For more details, see the [pdf-export README](pdf-export/README.md).

## Adding CronJobs

Cronjobs are added in the `helm/values.yaml` file. To add a new cronjob that will be monitored in production create a new item under `jobs` with a `name`, `command`, and `schedule`:
```
- name: some-new-cronjob
  command: python /code/manage.py some_new_command
  schedule: "10 6 * * *"
```

To stop this job from being monitored in Cronitor add `include: "false"` to the new job.

## Troubleshooting

#### My commit/push hooks are not working / I'm getting an error

This is most likely either due to using an incorrect Python version, or not using the Poetry environment. First check which environment you are using by running `poetry env list`.

```
 % poetry env list
api-l5f4Yav4-py3.12 (Activated)
```
Above you can see that the current environment is running Python 3.12, which is not supported. In this case tell Poetry to use Python 3.11 and rebuild the local environment:
```
poetry env use python3.11
poetry install
```
Once this completes use `poetry shell` to drop into the new environment and run `poetry run pre-commit install --hook-type pre-push` and `poetry run pre-commit install --hook-type pre-commit`. At this point your environment should be fixed.

Make sure to always use `poetry shell` to activate the environment if you want the hooks to run everytime.


#### I'm seeing `Elation API authentication failed with (invalid_client)`.

Check the Elation configurations from `firefly/settings/local.py`.

#### `botocore.exceptions.NoCredentialsError: Unable to locate credentials` error when logging in to Lucian

This is probably the backend trying to connect to S3 in order to fetch the provider's avatar images. To get around this, try setting `avatar` and `avatar_large` on the `PatientDetail` object to be `None`.

#### `botocore.exceptions.MetadataRetrievalError: Error retrieving metadata: Received error when attempting to retrieve ECS metadata`

AWS credentials setup via lucible expire periodically. To fix this, run:

```shell
lucible login
docker-compose restart ecs-local-endpoints
docker-compose restart web
docker-compose restart dramatiq
```

#### Linters not running on commits (causing failures in CI)

When development is done on the crm repo - running `yarn` in the crm repo will overwrite the git hooks that run pre-commit checks in the api repo.

To reactivate this, run the following commands in the API repo:

```shell
git config --unset-all core.hooksPath
make git-hooks
```


## Elation Troubleshooting

We currently sync records to Elation as pre/post-save signals. Sometimes the Elation updates will fail, possibly due to:

- Duplicate full name
- Duplicate phone number
- Transient Elation system errors

If the Elation update fails, the Django save update will also fail and throw a 500.

We store all incoming Elation webhooks in the `ElationSyncLog` model. This can be useful for troubleshooting updates from Elation.

## Connecting to Postgres

```
docker compose exec db psql -U docker -d firefly_docker
```

### Adding Fake Patients

Add fake patients in Lucian
```
docker compose exec -T web python manage.py create_fake_patients 20
```

Warning - make sure that desired integrations are turned off via Waffle Switches - otherwise these new patients could trigger texts (to fake phone numbers), or emails, or ZenDesk/ActiveCampaign records.

### Adding Fake Messages

Fill in fake messages in Lucian between patient and providers
```
docker compose exec -T web python manage.py create_fake_messages 20 --patient_ids 123,456 --provider_ids 789,012 --user-phone-number **********
```

### Adding Fake Message Tasks

Fill in fake messages tasks in Lucian between patient and providers

Currently the task titles are all names due to text being too long and all messages are due the following day of when the script is run
```
docker compose exec -T web python manage.py create_fake_message_tasks 20 --patient_ids 123,456 --provider_ids 789,012 --user-phone-number **********
```

## Audit Logging

We use pghistory that uses PostGres triggers and takes a snapshot of the model when changes are made and attaches the User making the change.

Any model that inherits from `BaseModelV3` has pghistory included.
This can be seen in Django Admin in the `history` section and in Looker tables with the Model name + `Event`.

## Caching

We use [django-cachalot](https://django-cachalot.readthedocs.io/) to cache infrequently changing data. Data is cached in local memcache - this avoids a round trip to redis. Consequently cache invalidation does not happen instantaneously - caches are purged once every hour. This is controlled by the `CACHALOT_TIMEOUT` setting in `base.py`.

When all the tables in a query are included in the cache: the results of the query are stored in the cache. Fact tables / data that changes infrequently are good candidates to be added to the cache .

### Disabling cache

Set the `CACHALOT_ENABLED` setting to false in `base.py` to disable caching.

### Adding a new table to cache

Update the `CACHALOT_ONLY_CACHABLE_TABLES` entry in `base.py` to include any additional tables that needs to be cached. The names included in the list should reflect the database table name.

## Secrets

> :warning: **Secrets management is in a transitional state.** Combined secrets and configuration in `firefly-sec` is deprecated in favor of managing secrets in AWS Secrets Manager and configuration in the monorepo. Ask in #-tech-ops if you are unsure how to proceed.

Secrets are managed in [AWS Secrets Manager](https://aws.amazon.com/secrets-manager/). At runtime, Lucian retrieves those secrets using the AWS SDK:

```python
from firefly.core.services.aws_secrets_manager.utils import get_aws_secret

secret = get_aws_secret("my-secret")
```

Most of the time, you will want to retrieve the secret when defining [Django settings](https://docs.djangoproject.com/en/3.2/topics/settings/), like so:

```python
# settings/{local,dev,staging,prod}.py


MY_SETTING = {
  "CONFIGURATION": "something not sensitive",
  "SECRET": get_aws_secret("my-secret"),
}
```

AWS Secrets Manager is managed by Terraform. To add or update a secret, see [the README in firefly-infra](https://github.com/fireflyhealth/firefly-infra/blob/main/README.md#secrets-management).

# IDE Support

## VSCode

VSCode is supported for both remote and local development environments.

There are two flavors of getting VSCode up and running with the remote environment. The first approach involves connecting VSCode to the remote machine over SSH. The second approach involves connecting VSCode to the remote machine over SSH and then running VSCode inside a dev container.

While the second approach has more steps for the initial setup - these pay off fairly quickly especially since dependencies and extensions do not need to be installed locally / on a per user basis.

#### VSCode over Remote Environment

- On Mac: Start VSCode. Install the [Remote SSH plugin](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh).
- In VSCode: Use command+shift+P to run the command Remote-SSH: Connect to Host and select the instance to connect to.
- Assuming the git clone step was already completed above, Use the Folder icon on the top left to select the folder you want to be able to run VSCode on.

Downsides:
- Plugins will need to be installed manually
- Autocomplete and source code for libraries will not be available out of the box - folks will need to run make python-deps to install libraries per user

#### VSCode in a container over Remote Environment

- Complete the steps required to connect to the remote environment.
- On the initial connect, VSCode will prompt to open the dev container since a configuration already exists. Either use the prompt or use command+shift+P to select `DevContainers: Reopen in container`.
- The first run will take some time (~10 mins) as it creates the environment; installs extensions and dependencies.
- One additional step required here is to set up SSH authentication. Since the container (that was built out of the Dockerfile) does not have any knowledge of our SSH keys, it will not be able to authenticate with GitHub. [VSCode already solves for this](https://code.visualstudio.com/remote/advancedcontainers/sharing-git-credentials) - and forwards the request to the local machine to find an appropriate identity. To setup the identity:
  - Run `ssh-add ~/.ssh/id_ed25519` on your local machine. (Replace `id_ed25519` with the key used for GitHub)
  - The following snippet can be added to `~/.bash_rc` or `~/.zsh_rc` to automatically add this on every login:

```
if [ -z "$SSH_AUTH_SOCK" ] ; then
  eval `ssh-agent -s`
  ssh-add ~/.ssh/id_ed25519
fi
```

- Once the setup is complete - VSCode will have access to all dependencies / source code for auto complete to work and will have extensions pre-installed.

#### FAQ

##### How do I change my preferred shell on the remote environment?

The default profile in use is bash. Zsh is the other installed shell. To switch over to zsh, run:

```
export SHELL=`which zsh`
[ -z "$ZSH_VERSION" ] && exec "$SHELL" -l
```

Chsh will not work since users do not have root permissions.

Note that this should not be added to ~/.profile - since vscode will try to run this while connecting to the remote environment.

##### Why are so many AWS login windows open?

If you leave your vscode connected to the remote environment for a very long time and the lucible creds expire, vscode will no longer be able to connect to the remote environment. VSCode will periodically attempts to reconnect - and you will have .. oh.. So many browser windows open - each trying to get into AWS.

##### Why does my admin page load without any css?

Recollect static contents and restart server:

```
docker compose exec web ./manage.py collectstatic
docker compose restart web
```

##### **************: Permission denied (publickey).

```
**************: Permission denied (publickey).
fatal: Could not read from remote repository.
Please make sure you have the correct access rights
and the repository exists.
```

Verify that your SSH keys have really been added to GitHub.

On your local Mac - verify that ssh-add -l lists that the key has been added.

If not run `ssh-add ~/.ssh/id_ed25519` or its equivalent.
